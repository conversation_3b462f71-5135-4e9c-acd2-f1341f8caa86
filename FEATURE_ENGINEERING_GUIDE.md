# 特征工程模块详细文档

## 1. 模块概述

特征工程模块是量化交易系统的核心组件之一，负责从原始金融数据中提取和生成有价值的特征，为后续的模型训练提供输入。本模块支持多种技术指标的计算，包括移动平均线、相对强弱指标、MACD、布林带等，同时支持特征归一化和特征选择。

## 2. 模块结构

特征工程模块由以下几个主要组件组成：

- `FeatureEngineer`: 基础特征工程类，负责特征的生成和处理
- `EnhancedFeatureEngineer`: 增强版特征工程类，提供更多高级特征和优化
- `FeatureEngineeringAdapter`: 特征工程适配器，用于转换不同格式的特征配置
- `OptimizedFeatureEngineering`: 优化版特征工程类，提供性能优化

## 3. 配置格式

特征工程模块支持两种配置格式：标准格式和扁平格式。

### 3.1 标准格式

标准格式是一个嵌套的字典结构，每个特征类别都有自己的配置子字典。

```python
{
    'price_features': {'use': True},
    'volume': {'use': True, 'periods': [5, 10, 20]},
    'sma': {'use': True, 'periods': [5, 10, 20, 60]},
    'ema': {'use': True, 'periods': [5, 10, 20]},
    'rsi': {'use': True, 'periods': [6, 14, 21]},
    'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9},
    'bbands': {'use': True, 'period': 20, 'std': 2.0},
    'normalization': {'use': True, 'method': 'minmax'}
}
```

### 3.2 扁平格式

扁平格式是一个扁平的字典结构，所有配置都在顶层。

```python
{
    'use_price': True,
    'use_volume': True,
    'use_technical': True,
    'sma_periods': [5, 10, 20, 30, 60],
    'ema_periods': [5, 10, 20, 30, 60],
    'rsi_periods': [14],
    'macd_params': {'fast': 12, 'slow': 26, 'signal': 9},
    'bb_params': {'window': 20, 'num_std': 2},
    'atr_periods': [14],
    'normalization': 'zscore'
}
```

## 4. 支持的特征

### 4.1 价格特征

价格特征是基于原始价格数据计算的特征，包括：

- 涨跌幅：当日收盘价相对前一日收盘价的变化百分比
- 对数收益率：当日收盘价相对前一日收盘价的对数变化
- 价格动量：当日收盘价相对N日前收盘价的变化百分比
- 价格加速度：价格动量的变化率

配置示例：

```python
'price_features': {'use': True}
```

### 4.2 成交量特征

成交量特征是基于原始成交量数据计算的特征，包括：

- 成交量变化率：当日成交量相对前一日成交量的变化百分比
- 成交量移动平均：N日成交量的移动平均
- 相对成交量：当日成交量相对N日成交量移动平均的比值

配置示例：

```python
'volume': {'use': True, 'periods': [5, 10, 20]}
```

### 4.3 移动平均线

移动平均线是一种常用的技术指标，用于平滑价格数据，减少噪声。支持的移动平均线类型包括：

- 简单移动平均线 (SMA)：N日收盘价的算术平均
- 指数移动平均线 (EMA)：赋予近期价格更高权重的移动平均
- 加权移动平均线 (WMA)：根据时间赋予不同权重的移动平均
- 考夫曼自适应移动平均线 (KAMA)：根据价格波动调整权重的移动平均

配置示例：

```python
'sma': {'use': True, 'periods': [5, 10, 20, 60]},
'ema': {'use': True, 'periods': [5, 10, 20]},
'wma': {'use': True, 'periods': [10, 20]},
'kama': {'use': True, 'periods': [10, 20]}
```

### 4.4 动量指标

动量指标用于衡量价格变动的速度和强度，支持的动量指标包括：

- 相对强弱指标 (RSI)：衡量价格上涨的强度相对于价格下跌的强度
- 随机指标 (Stochastic)：衡量当前价格相对于一段时间内价格范围的位置
- 商品通道指数 (CCI)：衡量价格偏离移动平均的程度
- 平均方向指数 (ADX)：衡量价格趋势的强度
- MACD：移动平均收敛散度，用于识别价格趋势的变化
- 动量 (Momentum)：当前价格相对于N日前价格的差值
- 变动率 (ROC)：当前价格相对于N日前价格的变化百分比

配置示例：

```python
'rsi': {'use': True, 'periods': [6, 14, 21]},
'stoch': {'use': True, 'k_period': 14, 'd_period': 3, 'slowing': 3},
'cci': {'use': True, 'periods': [14, 20]},
'adx': {'use': True, 'period': 14},
'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9},
'mom': {'use': True, 'periods': [10, 20]},
'roc': {'use': True, 'periods': [10, 20]}
```

### 4.5 波动率指标

波动率指标用于衡量价格波动的幅度，支持的波动率指标包括：

- 布林带 (Bollinger Bands)：基于价格标准差的上下轨道
- 平均真实波幅 (ATR)：衡量价格波动的平均幅度
- 历史波动率 (Historical Volatility)：基于对数收益率的标准差

配置示例：

```python
'bbands': {'use': True, 'period': 20, 'std': 2.0},
'atr': {'use': True, 'periods': [7, 14, 21]},
'volatility': {'use': True, 'periods': [10, 20, 30]}
```

### 4.6 统计特征

统计特征是基于滚动窗口计算的统计量，包括：

- 滚动均值：N日价格的均值
- 滚动标准差：N日价格的标准差
- 滚动偏度：N日价格的偏度，衡量分布的不对称性
- 滚动峰度：N日价格的峰度，衡量分布的尖峭程度
- 滚动最大值：N日价格的最大值
- 滚动最小值：N日价格的最小值
- 滚动分位数：N日价格的分位数

配置示例：

```python
'rolling_stats': {'use': True, 'windows': [5, 10, 20, 60]}
```

### 4.7 高级特征

高级特征是基于多种数据源或复杂计算的特征，包括：

- 价格形态特征：识别常见的价格形态，如头肩顶、双顶、三角形等
- 市场微观结构特征：基于高频数据的特征，如买卖价差、市场深度等
- 时间特征：基于日期和时间的特征，如星期几、月份、季度等
- 跨资产特征：基于多个资产的相关性和协整性的特征

配置示例：

```python
'advanced_features': {'use': True},
'market_microstructure': {'use': True},
'time_features': {'use': True},
'cross_asset': {'use': True}
```

### 4.8 特征归一化

特征归一化用于将特征缩放到相同的范围，支持的归一化方法包括：

- 最小-最大归一化 (MinMax)：将特征缩放到[0, 1]范围
- 标准化 (Standardization)：将特征转换为均值为0，标准差为1的分布
- 稳健归一化 (Robust)：基于分位数的归一化，对异常值不敏感
- Yeo-Johnson变换：一种幂变换，用于使数据更接近正态分布

配置示例：

```python
'normalization': {'use': True, 'method': 'minmax'}
```

### 4.9 特征选择

特征选择用于从众多特征中选择最有价值的特征，支持的特征选择方法包括：

- 过滤法 (Filter)：基于特征与目标变量的相关性选择特征
- 包装法 (Wrapper)：基于模型性能选择特征
- 嵌入法 (Embedded)：在模型训练过程中选择特征
- 组合法 (Combined)：结合多种方法选择特征

配置示例：

```python
'feature_selection': {'use': True, 'method': 'mutual_info', 'top_n': 30}
```

## 5. 使用示例

### 5.1 基本使用

```python
from quant_project.core_logic.feature_engineer import FeatureEngineer

# 创建特征工程配置
feature_config = {
    'price_features': {'use': True},
    'volume': {'use': True, 'periods': [5, 10, 20]},
    'sma': {'use': True, 'periods': [5, 10, 20, 60]},
    'ema': {'use': True, 'periods': [5, 10, 20]},
    'rsi': {'use': True, 'periods': [6, 14, 21]},
    'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9},
    'bbands': {'use': True, 'period': 20, 'std': 2.0},
    'normalization': {'use': True, 'method': 'minmax'}
}

# 创建特征工程实例
feature_engineer = FeatureEngineer(feature_config)

# 生成特征
processed_data = feature_engineer.generate_features(data)
```

### 5.2 使用适配器

```python
from quant_project.core_logic.feature_engineer import FeatureEngineer
from quant_project.core_logic.feature_engineering_adapter import FeatureEngineeringAdapter

# 创建扁平格式配置
flat_config = {
    'use_price': True,
    'use_volume': True,
    'use_technical': True,
    'sma_periods': [5, 10, 20],
    'rsi_periods': [14],
    'normalization': 'zscore'
}

# 使用适配器转换配置格式
adapter = FeatureEngineeringAdapter()
standard_config = adapter.adapt_config(flat_config)

# 创建特征工程实例
feature_engineer = FeatureEngineer(standard_config)

# 生成特征
processed_data = feature_engineer.generate_features(data)
```

### 5.3 使用增强版特征工程

```python
from quant_project.core_logic.enhanced_feature_engineer import EnhancedFeatureEngineer

# 创建特征工程配置
feature_config = {
    'price_features': {'use': True},
    'volume': {'use': True, 'periods': [5, 10, 20]},
    'sma': {'use': True, 'periods': [5, 10, 20, 60]},
    'ema': {'use': True, 'periods': [5, 10, 20]},
    'rsi': {'use': True, 'periods': [6, 14, 21]},
    'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9},
    'bbands': {'use': True, 'period': 20, 'std': 2.0},
    'advanced_features': {'use': True},
    'normalization': {'use': True, 'method': 'robust'}
}

# 创建增强版特征工程实例
feature_engineer = EnhancedFeatureEngineer(
    feature_config=feature_config,
    avoid_lookahead=True,
    check_stationarity=True,
    feature_selection_method='combined',
    normalization_method='robust',
    rolling_window_type='expanding',
    max_features=50
)

# 生成特征
processed_data = feature_engineer.generate_features(data)
```
