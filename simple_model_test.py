"""
简单模型保存和加载测试
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger('model_test')

print("脚本开始执行")
logger.info("脚本开始执行")

# 导入核心逻辑模块
try:
    print("尝试导入模块...")
    # 添加项目路径
    sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), "quant_project"))
    print(f"Python路径: {sys.path}")

    # 尝试不同的导入路径
    try:
        from quant_project.core_logic.drl_agent import DRLAgent
        print("从quant_project导入成功")
    except ImportError as e:
        print(f"从quant_project导入失败: {e}")
        try:
            from core_logic.drl_agent import DRLAgent
            print("从core_logic导入成功")
        except ImportError as e:
            print(f"从core_logic导入失败: {e}")
            # 最后尝试直接导入
            try:
                sys.path.append("quant_project")
                from core_logic.drl_agent import DRLAgent
                print("从quant_project/core_logic导入成功")
            except ImportError as e:
                print(f"所有导入尝试都失败: {e}")
                sys.exit(1)
except Exception as e:
    print(f"导入过程中发生错误: {e}")
    sys.exit(1)

# 检查saved_models目录
if not os.path.exists("saved_models"):
    os.makedirs("saved_models")
    print("创建saved_models目录")

# 列出现有模型
model_files = [f for f in os.listdir("saved_models") if f.endswith(".zip")]
print(f"现有模型文件: {model_files}")

# 检查是否有最佳模型
best_models = [f for f in model_files if 'best_' in f]
print(f"最佳模型文件: {best_models}")

print("脚本执行完毕")
