#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试优化版数据处理和特征工程模块
"""

import unittest
import pandas as pd
import numpy as np
import logging
import time
import os
import sys
from datetime import datetime, timedelta

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('test_optimized')

# 导入优化模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from core_logic.optimized_data_handler import OptimizedDataHandler
from core_logic.optimized_feature_engineering import OptimizedFeatureEngineering

class TestOptimizedModules(unittest.TestCase):
    """测试优化版数据处理和特征工程模块"""
    
    @classmethod
    def setUpClass(cls):
        """测试前的准备工作"""
        logger.info("准备测试数据...")
        
        # 创建测试数据目录
        os.makedirs('test_data_cache', exist_ok=True)
        
        # 创建模拟股票数据
        cls.create_mock_stock_data()
    
    @classmethod
    def tearDownClass(cls):
        """测试后的清理工作"""
        logger.info("清理测试数据...")
        
        # 删除测试数据目录
        import shutil
        if os.path.exists('test_data_cache'):
            shutil.rmtree('test_data_cache')
    
    @classmethod
    def create_mock_stock_data(cls):
        """创建模拟股票数据"""
        # 创建日期范围
        start_date = datetime(2020, 1, 1)
        end_date = datetime(2020, 12, 31)
        date_range = pd.date_range(start=start_date, end=end_date, freq='B')  # 工作日
        
        # 创建模拟价格数据
        np.random.seed(42)  # 设置随机种子，确保结果可重现
        n = len(date_range)
        
        # 生成随机价格
        close_price = 100 + np.cumsum(np.random.normal(0, 1, n)) / 10
        open_price = close_price + np.random.normal(0, 1, n)
        high_price = np.maximum(close_price, open_price) + np.abs(np.random.normal(0, 1, n))
        low_price = np.minimum(close_price, open_price) - np.abs(np.random.normal(0, 1, n))
        
        # 生成随机成交量
        volume = np.abs(np.random.normal(1000000, 500000, n))
        
        # 创建DataFrame
        cls.stock_data = pd.DataFrame({
            '日期': date_range,
            '开盘': open_price,
            '最高': high_price,
            '最低': low_price,
            '收盘': close_price,
            '成交量': volume
        })
        
        # 设置日期索引
        cls.stock_data.set_index('日期', inplace=True)
        
        # 保存到CSV文件
        cls.stock_data.to_csv('test_data_cache/mock_stock_data.csv')
        
        logger.info(f"创建了模拟股票数据，共 {len(cls.stock_data)} 行")
    
    def test_optimized_data_handler(self):
        """测试优化版数据处理器"""
        logger.info("测试优化版数据处理器...")
        
        # 创建优化版数据处理器
        data_handler = OptimizedDataHandler(cache_dir='test_data_cache')
        
        # 测试数据清洗
        start_time = time.time()
        cleaned_data = data_handler._clean_data(self.stock_data)
        logger.info(f"数据清洗耗时: {time.time() - start_time:.4f}秒")
        
        # 测试并行数据清洗
        start_time = time.time()
        parallel_cleaned_data = data_handler._parallel_clean_data(self.stock_data)
        logger.info(f"并行数据清洗耗时: {time.time() - start_time:.4f}秒")
        
        # 检查清洗结果是否一致
        pd.testing.assert_frame_equal(cleaned_data, parallel_cleaned_data)
        
        # 测试异常值检测
        start_time = time.time()
        for col in ['开盘', '最高', '最低', '收盘', '成交量']:
            fixed_col = data_handler._detect_and_fix_outliers(self.stock_data, col)
            self.assertEqual(len(fixed_col), len(self.stock_data))
        logger.info(f"异常值检测耗时: {time.time() - start_time:.4f}秒")
        
        # 测试时间序列对齐
        start_time = time.time()
        aligned_data = data_handler._align_time_series(
            self.stock_data, 
            start_date='2020-01-01', 
            end_date='2020-12-31', 
            freq='D'
        )
        logger.info(f"时间序列对齐耗时: {time.time() - start_time:.4f}秒")
        
        # 测试缓存机制
        # 首次获取数据（无缓存）
        start_time = time.time()
        data1 = data_handler.get_stock_data(
            stock_code='sh000001',
            start_date='2020-01-01',
            end_date='2020-12-31',
            frequency='日线',
            use_cache=True
        )
        first_time = time.time() - start_time
        logger.info(f"首次获取数据耗时: {first_time:.4f}秒")
        
        # 再次获取数据（有缓存）
        start_time = time.time()
        data2 = data_handler.get_stock_data(
            stock_code='sh000001',
            start_date='2020-01-01',
            end_date='2020-12-31',
            frequency='日线',
            use_cache=True
        )
        second_time = time.time() - start_time
        logger.info(f"再次获取数据耗时: {second_time:.4f}秒")
        
        # 检查缓存是否有效
        self.assertLess(second_time, first_time)
        
        # 清除缓存
        data_handler.clear_cache()
        
        logger.info("优化版数据处理器测试完成")
    
    def test_optimized_feature_engineering(self):
        """测试优化版特征工程"""
        logger.info("测试优化版特征工程...")
        
        # 创建优化版特征工程器
        feature_eng = OptimizedFeatureEngineering()
        
        # 测试基本特征计算
        start_time = time.time()
        basic_features = feature_eng.calculate_features(self.stock_data, 'basic')
        logger.info(f"基本特征计算耗时: {time.time() - start_time:.4f}秒")
        
        # 测试技术指标计算
        start_time = time.time()
        technical_features = feature_eng.calculate_features(self.stock_data, 'technical')
        logger.info(f"技术指标计算耗时: {time.time() - start_time:.4f}秒")
        
        # 测试并行特征计算
        feature_eng.parallel_processing = True
        start_time = time.time()
        parallel_features = feature_eng.calculate_features(self.stock_data, 'all')
        parallel_time = time.time() - start_time
        logger.info(f"并行特征计算耗时: {parallel_time:.4f}秒")
        
        # 测试串行特征计算
        feature_eng.parallel_processing = False
        start_time = time.time()
        serial_features = feature_eng.calculate_features(self.stock_data, 'all')
        serial_time = time.time() - start_time
        logger.info(f"串行特征计算耗时: {serial_time:.4f}秒")
        
        # 检查并行计算是否比串行计算快
        self.assertLessEqual(parallel_time, serial_time * 1.2)  # 允许一定的误差
        
        # 测试缓存机制
        feature_eng.use_cache = True
        feature_eng.parallel_processing = True
        
        # 首次计算特征（无缓存）
        start_time = time.time()
        features1 = feature_eng.calculate_features(self.stock_data, 'all')
        first_time = time.time() - start_time
        logger.info(f"首次计算特征耗时: {first_time:.4f}秒")
        
        # 再次计算特征（有缓存）
        start_time = time.time()
        features2 = feature_eng.calculate_features(self.stock_data, 'all')
        second_time = time.time() - start_time
        logger.info(f"再次计算特征耗时: {second_time:.4f}秒")
        
        # 检查缓存是否有效
        self.assertLess(second_time, first_time)
        
        # 测试收益率计算
        start_time = time.time()
        returns_data = feature_eng.calculate_returns(self.stock_data)
        logger.info(f"收益率计算耗时: {time.time() - start_time:.4f}秒")
        
        # 测试波动率计算
        start_time = time.time()
        volatility_data = feature_eng.calculate_volatility(self.stock_data)
        logger.info(f"波动率计算耗时: {time.time() - start_time:.4f}秒")
        
        # 测试价格特征计算
        start_time = time.time()
        price_features = feature_eng.calculate_price_features(self.stock_data)
        logger.info(f"价格特征计算耗时: {time.time() - start_time:.4f}秒")
        
        # 测试成交量特征计算
        start_time = time.time()
        volume_features = feature_eng.calculate_volume_features(self.stock_data)
        logger.info(f"成交量特征计算耗时: {time.time() - start_time:.4f}秒")
        
        # 测试所有特征计算
        start_time = time.time()
        all_features = feature_eng.calculate_all_features(self.stock_data)
        logger.info(f"所有特征计算耗时: {time.time() - start_time:.4f}秒")
        
        # 测试特征标准化
        start_time = time.time()
        normalized_features = feature_eng.normalize_features(all_features)
        logger.info(f"特征标准化耗时: {time.time() - start_time:.4f}秒")
        
        # 清除缓存
        feature_eng.clear_cache()
        
        logger.info("优化版特征工程测试完成")
    
    def test_end_to_end_performance(self):
        """测试端到端性能"""
        logger.info("测试端到端性能...")
        
        # 创建优化版数据处理器和特征工程器
        data_handler = OptimizedDataHandler(cache_dir='test_data_cache')
        feature_eng = OptimizedFeatureEngineering()
        
        # 测试端到端性能（并行处理）
        data_handler.parallel_processing = True
        feature_eng.parallel_processing = True
        
        start_time = time.time()
        
        # 获取数据
        data = data_handler.get_stock_data(
            stock_code='sh000001',
            start_date='2020-01-01',
            end_date='2020-12-31',
            frequency='日线',
            use_cache=True
        )
        
        # 计算特征
        features = feature_eng.calculate_all_features(data)
        
        parallel_time = time.time() - start_time
        logger.info(f"并行处理端到端耗时: {parallel_time:.4f}秒")
        
        # 测试端到端性能（串行处理）
        data_handler.parallel_processing = False
        feature_eng.parallel_processing = False
        
        # 清除缓存
        data_handler.clear_cache()
        feature_eng.clear_cache()
        
        start_time = time.time()
        
        # 获取数据
        data = data_handler.get_stock_data(
            stock_code='sh000001',
            start_date='2020-01-01',
            end_date='2020-12-31',
            frequency='日线',
            use_cache=True
        )
        
        # 计算特征
        features = feature_eng.calculate_all_features(data)
        
        serial_time = time.time() - start_time
        logger.info(f"串行处理端到端耗时: {serial_time:.4f}秒")
        
        # 检查并行处理是否比串行处理快
        self.assertLessEqual(parallel_time, serial_time)
        
        logger.info("端到端性能测试完成")

if __name__ == '__main__':
    unittest.main()
