import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader, TensorDataset
import numpy as np
import pandas as pd
from typing import Dict, List, Union, Tuple, Callable, Optional, Any
import os
import json
import logging
import matplotlib.pyplot as plt
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import time
from tqdm import tqdm

# Configure logger
logger = logging.getLogger(__name__)

class ModelTrainer:
    """
    Class for training and evaluating PyTorch models for quantitative trading.
    Provides utilities for training, validation, checkpoint management,
    and model evaluation.
    """
    
    def __init__(self, 
                model: nn.Module, 
                optimizer: optim.Optimizer = None,
                loss_fn: Callable = None,
                lr_scheduler: Any = None,
                device: str = None,
                config: Dict = None):
        """
        Initialize the ModelTrainer.
        
        Args:
            model: PyTorch model to train
            optimizer: PyTorch optimizer (if None, Adam with lr=0.001 will be used)
            loss_fn: Loss function (if None, MSELoss will be used)
            lr_scheduler: Learning rate scheduler
            device: Device to use ('cuda', 'cpu', or None for auto-detection)
            config: Configuration dictionary with training parameters
        """
        self.config = config or {}
        
        # Set device
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
            
        self.model = model.to(self.device)
        
        # Set default optimizer if none provided
        if optimizer is None:
            self.optimizer = optim.Adam(self.model.parameters(), 
                                      lr=self.config.get('learning_rate', 0.001))
        else:
            self.optimizer = optimizer
            
        # Set default loss function if none provided
        if loss_fn is None:
            self.loss_fn = nn.MSELoss()
        else:
            self.loss_fn = loss_fn
            
        self.lr_scheduler = lr_scheduler
        
        # Training history
        self.history = {
            'train_loss': [],
            'val_loss': [],
            'metrics': {}
        }
        
        # Best model state
        self.best_val_loss = float('inf')
        self.best_model_state = None
        
    def train_step(self, x: torch.Tensor, y: torch.Tensor) -> float:
        """
        Perform a single training step.
        
        Args:
            x: Input features
            y: Target values
            
        Returns:
            Loss value
        """
        self.model.train()
        self.optimizer.zero_grad()
        
        # Forward pass
        outputs = self.model(x)
        loss = self.loss_fn(outputs, y)
        
        # Backward pass and optimize
        loss.backward()
        
        # Gradient clipping if configured
        if 'clip_grad_norm' in self.config:
            torch.nn.utils.clip_grad_norm_(
                self.model.parameters(), 
                self.config['clip_grad_norm']
            )
            
        self.optimizer.step()
        
        return loss.item()
    
    def validate_step(self, x: torch.Tensor, y: torch.Tensor) -> Dict[str, float]:
        """
        Perform a validation step.
        
        Args:
            x: Input features
            y: Target values
            
        Returns:
            Dictionary with validation metrics
        """
        self.model.eval()
        with torch.no_grad():
            outputs = self.model(x)
            loss = self.loss_fn(outputs, y)
            
            # Calculate additional metrics
            metrics = {
                'loss': loss.item()
            }
            
            # Add other metrics if needed
            # Move tensors to CPU for numpy conversion
            y_np = y.cpu().numpy()
            outputs_np = outputs.cpu().numpy()
            
            metrics['mse'] = mean_squared_error(y_np, outputs_np)
            metrics['mae'] = mean_absolute_error(y_np, outputs_np)
            
            try:
                metrics['r2'] = r2_score(y_np, outputs_np)
            except:
                metrics['r2'] = 0.0
                
        return metrics
    
    def fit(self, 
           train_loader: DataLoader, 
           val_loader: DataLoader = None,
           epochs: int = 10,
           callbacks: List[Callable] = None,
           verbose: bool = True) -> Dict[str, List]:
        """
        Train the model.
        
        Args:
            train_loader: DataLoader for training data
            val_loader: DataLoader for validation data (optional)
            epochs: Number of epochs to train
            callbacks: List of callback functions
            verbose: Whether to print progress
            
        Returns:
            Dictionary with training history
        """
        callbacks = callbacks or []
        
        for epoch in range(epochs):
            start_time = time.time()
            epoch_loss = 0.0
            
            # Training loop
            if verbose:
                train_iterator = tqdm(train_loader, desc=f"Epoch {epoch+1}/{epochs}")
            else:
                train_iterator = train_loader
                
            for batch_idx, (data, target) in enumerate(train_iterator):
                data, target = data.to(self.device), target.to(self.device)
                
                # Handle potential NaN values
                if torch.isnan(data).any() or torch.isnan(target).any():
                    logger.warning(f"Batch {batch_idx} contains NaN values, skipping")
                    continue
                    
                loss = self.train_step(data, target)
                epoch_loss += loss
                
                # Update progress bar if verbose
                if verbose:
                    train_iterator.set_postfix({'loss': loss})
                    
            # Calculate average training loss
            avg_train_loss = epoch_loss / len(train_loader)
            self.history['train_loss'].append(avg_train_loss)
            
            # Validation step
            if val_loader is not None:
                val_metrics = self.evaluate(val_loader, verbose=False)
                val_loss = val_metrics['loss']
                self.history['val_loss'].append(val_loss)
                
                # Save best model
                if val_loss < self.best_val_loss:
                    self.best_val_loss = val_loss
                    self.best_model_state = {
                        'model_state_dict': self.model.state_dict(),
                        'optimizer_state_dict': self.optimizer.state_dict(),
                        'epoch': epoch,
                        'val_loss': val_loss
                    }
                    
                # Update learning rate scheduler if provided
                if self.lr_scheduler is not None:
                    if isinstance(self.lr_scheduler, torch.optim.lr_scheduler.ReduceLROnPlateau):
                        self.lr_scheduler.step(val_loss)
                    else:
                        self.lr_scheduler.step()
                        
                # Print epoch summary
                if verbose:
                    elapsed_time = time.time() - start_time
                    print(f"Epoch {epoch+1}/{epochs} completed in {elapsed_time:.2f}s - "
                          f"train_loss: {avg_train_loss:.6f} - val_loss: {val_loss:.6f}")
                    
                    # Print additional metrics
                    for key, value in val_metrics.items():
                        if key != 'loss':
                            print(f"val_{key}: {value:.6f}")
            else:
                # No validation data provided
                if verbose:
                    elapsed_time = time.time() - start_time
                    print(f"Epoch {epoch+1}/{epochs} completed in {elapsed_time:.2f}s - "
                          f"train_loss: {avg_train_loss:.6f}")
                    
            # Execute callbacks
            for callback in callbacks:
                callback(self, epoch)
                
        return self.history
    
    def evaluate(self, data_loader: DataLoader, verbose: bool = True) -> Dict[str, float]:
        """
        Evaluate the model on a dataset.
        
        Args:
            data_loader: DataLoader for evaluation data
            verbose: Whether to print evaluation results
            
        Returns:
            Dictionary with evaluation metrics
        """
        self.model.eval()
        total_loss = 0.0
        all_targets = []
        all_outputs = []
        
        with torch.no_grad():
            for data, target in data_loader:
                data, target = data.to(self.device), target.to(self.device)
                
                # Handle potential NaN values
                if torch.isnan(data).any() or torch.isnan(target).any():
                    continue
                    
                outputs = self.model(data)
                loss = self.loss_fn(outputs, target).item()
                total_loss += loss
                
                # Store predictions and targets for additional metrics
                all_targets.append(target.cpu().numpy())
                all_outputs.append(outputs.cpu().numpy())
                
        # Calculate average loss
        avg_loss = total_loss / len(data_loader)
        
        # Concatenate all batches
        y_true = np.concatenate(all_targets)
        y_pred = np.concatenate(all_outputs)
        
        # Calculate additional metrics
        metrics = {
            'loss': avg_loss,
            'mse': mean_squared_error(y_true, y_pred),
            'mae': mean_absolute_error(y_true, y_pred)
        }
        
        try:
            metrics['r2'] = r2_score(y_true, y_pred)
        except:
            metrics['r2'] = 0.0
            
        if verbose:
            print("Evaluation metrics:")
            for key, value in metrics.items():
                print(f"  {key}: {value:.6f}")
                
        return metrics
    
    def predict(self, data_loader: DataLoader) -> np.ndarray:
        """
        Make predictions using the trained model.
        
        Args:
            data_loader: DataLoader for prediction data
            
        Returns:
            NumPy array with predictions
        """
        self.model.eval()
        predictions = []
        
        with torch.no_grad():
            for data in data_loader:
                # Handle both (data) and (data, _) formats
                if isinstance(data, (list, tuple)):
                    data = data[0]
                    
                data = data.to(self.device)
                outputs = self.model(data)
                predictions.append(outputs.cpu().numpy())
                
        return np.concatenate(predictions)
    
    def save_checkpoint(self, filepath: str, save_optimizer: bool = True) -> None:
        """
        Save model checkpoint to file.
        
        Args:
            filepath: Path to save the checkpoint
            save_optimizer: Whether to save optimizer state
        """
        checkpoint = {
            'model_state_dict': self.model.state_dict(),
            'config': self.config
        }
        
        if save_optimizer:
            checkpoint['optimizer_state_dict'] = self.optimizer.state_dict()
            
        if self.lr_scheduler is not None:
            checkpoint['lr_scheduler_state_dict'] = self.lr_scheduler.state_dict()
            
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        torch.save(checkpoint, filepath)
        logger.info(f"Model checkpoint saved to {filepath}")
        
    def load_checkpoint(self, filepath: str, load_optimizer: bool = True) -> None:
        """
        Load model checkpoint from file.
        
        Args:
            filepath: Path to the checkpoint file
            load_optimizer: Whether to load optimizer state
        """
        if not os.path.exists(filepath):
            logger.error(f"Checkpoint file {filepath} not found")
            return
            
        checkpoint = torch.load(filepath, map_location=self.device)
        
        self.model.load_state_dict(checkpoint['model_state_dict'])
        
        if 'config' in checkpoint:
            self.config.update(checkpoint['config'])
            
        if load_optimizer and 'optimizer_state_dict' in checkpoint:
            self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            
        if self.lr_scheduler is not None and 'lr_scheduler_state_dict' in checkpoint:
            self.lr_scheduler.load_state_dict(checkpoint['lr_scheduler_state_dict'])
            
        logger.info(f"Model checkpoint loaded from {filepath}")
        
    def save_best_model(self, filepath: str) -> None:
        """
        Save the best model (based on validation loss) to file.
        
        Args:
            filepath: Path to save the best model
        """
        if self.best_model_state is None:
            logger.warning("No best model state found, saving current model instead")
            self.save_checkpoint(filepath)
            return
            
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        torch.save(self.best_model_state, filepath)
        logger.info(f"Best model saved to {filepath}")
        
    def load_best_model(self) -> None:
        """
        Load the best model state into the current model.
        """
        if self.best_model_state is None:
            logger.warning("No best model state found")
            return
            
        self.model.load_state_dict(self.best_model_state['model_state_dict'])
        self.optimizer.load_state_dict(self.best_model_state['optimizer_state_dict'])
        logger.info(f"Loaded best model from epoch {self.best_model_state['epoch'] + 1} "
                   f"with validation loss {self.best_model_state['val_loss']:.6f}")
        
    def plot_learning_curves(self, figsize: Tuple[int, int] = (10, 6)) -> None:
        """
        Plot learning curves for training and validation loss.
        
        Args:
            figsize: Figure size (width, height)
        """
        plt.figure(figsize=figsize)
        
        # Plot training loss
        plt.plot(self.history['train_loss'], label='Training Loss')
        
        # Plot validation loss if available
        if 'val_loss' in self.history and self.history['val_loss']:
            plt.plot(self.history['val_loss'], label='Validation Loss')
            
        plt.title('Learning Curves')
        plt.xlabel('Epoch')
        plt.ylabel('Loss')
        plt.legend()
        plt.grid(True)
        plt.show()

# Neural network model creation functions

def create_mlp(input_dim: int, 
              output_dim: int = 1, 
              hidden_dims: List[int] = [64, 32],
              activation: nn.Module = nn.ReLU(),
              dropout_rate: float = 0.2,
              batch_norm: bool = True) -> nn.Module:
    """
    Create a multilayer perceptron model.
    
    Args:
        input_dim: Number of input features
        output_dim: Number of output features
        hidden_dims: List of hidden layer dimensions
        activation: Activation function
        dropout_rate: Dropout rate
        batch_norm: Whether to use batch normalization
        
    Returns:
        PyTorch MLP model
    """
    layers = []
    dims = [input_dim] + hidden_dims
    
    for i in range(len(dims) - 1):
        # Add linear layer
        layers.append(nn.Linear(dims[i], dims[i+1]))
        
        # Add batch normalization if enabled
        if batch_norm:
            layers.append(nn.BatchNorm1d(dims[i+1]))
            
        # Add activation
        layers.append(activation)
        
        # Add dropout
        if dropout_rate > 0:
            layers.append(nn.Dropout(dropout_rate))
            
    # Add output layer
    layers.append(nn.Linear(dims[-1], output_dim))
    
    return nn.Sequential(*layers)

def create_lstm_model(input_dim: int,
                    hidden_dim: int = 64,
                    num_layers: int = 2,
                    output_dim: int = 1,
                    dropout_rate: float = 0.2,
                    bidirectional: bool = False) -> nn.Module:
    """
    Create an LSTM model for sequence data.
    
    Args:
        input_dim: Number of input features
        hidden_dim: Hidden dimension size
        num_layers: Number of LSTM layers
        output_dim: Number of output features
        dropout_rate: Dropout rate
        bidirectional: Whether to use bidirectional LSTM
        
    Returns:
        PyTorch LSTM model
    """
    class LSTMModel(nn.Module):
        def __init__(self):
            super(LSTMModel, self).__init__()
            self.lstm = nn.LSTM(
                input_size=input_dim,
                hidden_size=hidden_dim,
                num_layers=num_layers,
                batch_first=True,
                dropout=dropout_rate if num_layers > 1 else 0,
                bidirectional=bidirectional
            )
            
            # Adjust output size if bidirectional
            lstm_output_dim = hidden_dim * 2 if bidirectional else hidden_dim
            
            self.fc = nn.Linear(lstm_output_dim, output_dim)
            
        def forward(self, x):
            # x shape: (batch_size, seq_len, input_dim)
            lstm_out, _ = self.lstm(x)
            
            # Use the output from the last time step
            out = self.fc(lstm_out[:, -1, :])
            return out
            
    return LSTMModel()

def create_cnn_1d(input_dim: int,
                seq_length: int,
                output_dim: int = 1,
                num_filters: List[int] = [64, 128, 256],
                kernel_sizes: List[int] = [3, 3, 3],
                activation: nn.Module = nn.ReLU(),
                dropout_rate: float = 0.2,
                pool_sizes: List[int] = [2, 2, 2]) -> nn.Module:
    """
    Create a 1D CNN model for sequence data.
    
    Args:
        input_dim: Number of input features
        seq_length: Length of input sequence
        output_dim: Number of output features
        num_filters: List of filter sizes for conv layers
        kernel_sizes: List of kernel sizes for conv layers
        activation: Activation function
        dropout_rate: Dropout rate
        pool_sizes: List of pooling sizes
        
    Returns:
        PyTorch 1D CNN model
    """
    class CNN1D(nn.Module):
        def __init__(self):
            super(CNN1D, self).__init__()
            
            self.conv_layers = nn.ModuleList()
            in_channels = input_dim
            
            current_length = seq_length
            for i, (n_filters, k_size, p_size) in enumerate(zip(num_filters, kernel_sizes, pool_sizes)):
                # Add convolutional layer
                self.conv_layers.append(nn.Conv1d(in_channels, n_filters, kernel_size=k_size, padding=k_size//2))
                self.conv_layers.append(nn.BatchNorm1d(n_filters))
                self.conv_layers.append(activation)
                
                # Add pooling layer
                self.conv_layers.append(nn.MaxPool1d(p_size))
                
                # Update variables for next layer
                in_channels = n_filters
                current_length = current_length // p_size
                
            # Calculate flattened size
            self.flatten_size = current_length * num_filters[-1]
            
            # Fully connected layers
            self.fc = nn.Sequential(
                nn.Linear(self.flatten_size, 128),
                nn.BatchNorm1d(128),
                activation,
                nn.Dropout(dropout_rate),
                nn.Linear(128, output_dim)
            )
            
        def forward(self, x):
            # x shape: (batch_size, seq_length, input_dim)
            # Reshape for 1D convolution: (batch_size, input_dim, seq_length)
            x = x.permute(0, 2, 1)
            
            # Apply conv layers
            for layer in self.conv_layers:
                x = layer(x)
                
            # Flatten
            x = x.view(x.size(0), -1)
            
            # Apply fully connected layers
            x = self.fc(x)
            return x
            
    return CNN1D()

def create_time_series_dataset(features: np.ndarray,
                             targets: np.ndarray,
                             seq_length: int,
                             batch_size: int = 32,
                             shuffle: bool = True) -> DataLoader:
    """
    Create a DataLoader for time series data.
    
    Args:
        features: Feature matrix of shape (n_samples, n_features)
        targets: Target values of shape (n_samples,) or (n_samples, n_targets)
        seq_length: Length of sequences to create
        batch_size: Batch size
        shuffle: Whether to shuffle the data
        
    Returns:
        PyTorch DataLoader for time series data
    """
    # Check if targets is 1D and convert to 2D if needed
    if len(targets.shape) == 1:
        targets = targets.reshape(-1, 1)
        
    n_samples = features.shape[0]
    n_features = features.shape[1]
    
    # Create sequences
    X_sequences = []
    y_sequences = []
    
    for i in range(n_samples - seq_length):
        X_sequences.append(features[i:i+seq_length])
        y_sequences.append(targets[i+seq_length])
        
    X = np.array(X_sequences)
    y = np.array(y_sequences)
    
    # Convert to PyTorch tensors
    X_tensor = torch.FloatTensor(X)
    y_tensor = torch.FloatTensor(y)
    
    # Create TensorDataset
    dataset = TensorDataset(X_tensor, y_tensor)
    
    # Create DataLoader
    return DataLoader(dataset, batch_size=batch_size, shuffle=shuffle)

def prepare_data_for_training(features: pd.DataFrame,
                            targets: pd.Series,
                            train_ratio: float = 0.7,
                            val_ratio: float = 0.15,
                            test_ratio: float = 0.15,
                            batch_size: int = 32,
                            seed: int = 42) -> Tuple[DataLoader, DataLoader, DataLoader]:
    """
    Prepare data for training, validation, and testing.
    
    Args:
        features: Feature DataFrame
        targets: Target Series
        train_ratio: Ratio of training data
        val_ratio: Ratio of validation data
        test_ratio: Ratio of test data
        batch_size: Batch size
        seed: Random seed
        
    Returns:
        Tuple of (train_loader, val_loader, test_loader)
    """
    assert abs(train_ratio + val_ratio + test_ratio - 1.0) < 1e-6, "Ratios must sum to 1"
    
    # Convert to numpy arrays
    X = features.values
    y = targets.values
    
    # Set random seed for reproducibility
    np.random.seed(seed)
    
    # Get data dimensions
    n_samples = X.shape[0]
    
    # Create indices for train, validation, and test sets
    indices = np.random.permutation(n_samples)
    train_size = int(n_samples * train_ratio)
    val_size = int(n_samples * val_ratio)
    
    train_indices = indices[:train_size]
    val_indices = indices[train_size:train_size+val_size]
    test_indices = indices[train_size+val_size:]
    
    # Split the data
    X_train, y_train = X[train_indices], y[train_indices]
    X_val, y_val = X[val_indices], y[val_indices]
    X_test, y_test = X[test_indices], y[test_indices]
    
    # Convert to PyTorch tensors
    X_train_tensor = torch.FloatTensor(X_train)
    y_train_tensor = torch.FloatTensor(y_train).view(-1, 1)
    
    X_val_tensor = torch.FloatTensor(X_val)
    y_val_tensor = torch.FloatTensor(y_val).view(-1, 1)
    
    X_test_tensor = torch.FloatTensor(X_test)
    y_test_tensor = torch.FloatTensor(y_test).view(-1, 1)
    
    # Create TensorDatasets
    train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
    val_dataset = TensorDataset(X_val_tensor, y_val_tensor)
    test_dataset = TensorDataset(X_test_tensor, y_test_tensor)
    
    # Create DataLoaders
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size)
    test_loader = DataLoader(test_dataset, batch_size=batch_size)
    
    return train_loader, val_loader, test_loader 