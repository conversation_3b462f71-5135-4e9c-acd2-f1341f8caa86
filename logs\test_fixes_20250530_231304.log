2025-05-30 23:13:04,184 - test_fixes - INFO - ==================================================
2025-05-30 23:13:04,185 - test_fixes - INFO - 开始测试修复后的功能
2025-05-30 23:13:04,185 - test_fixes - INFO - ==================================================
2025-05-30 23:13:04,185 - test_fixes - INFO - 测试GPU检测功能
2025-05-30 23:13:04,186 - gpu_installer - INFO - 检测系统GPU...
2025-05-30 23:13:04,296 - gpu_installer - INFO - 检测到NVIDIA GPU
2025-05-30 23:13:04,297 - gpu_installer - WARNING - 检测GPU时出错: argument of type 'NoneType' is not iterable
2025-05-30 23:13:04,297 - gpu_installer - WARNING - 检测CUDA时出错: [WinError 2] 系统找不到指定的文件。
2025-05-30 23:13:06,166 - gpu_installer - WARNING - TensorFlow GPU检测失败
2025-05-30 23:13:06,166 - test_fixes - INFO - GPU检测结果: {'has_gpu': True, 'gpu_count': 1, 'gpu_names': [], 'cuda_version': None, 'pytorch_gpu': True, 'tensorflow_gpu': False}
2025-05-30 23:13:06,166 - test_fixes - INFO - 测试特征工程适配器
2025-05-30 23:13:09,840 - drl_trading - INFO - 检测到扁平格式配置，转换为标准格式
2025-05-30 23:13:09,840 - test_fixes - INFO - 扁平格式配置: {'use_price': True, 'use_volume': True, 'use_technical': True, 'sma_periods': [5, 10, 20, 30, 60], 'ema_periods': [5, 10, 20, 30, 60], 'rsi_periods': [14], 'macd_params': {'fast': 12, 'slow': 26, 'signal': 9}, 'bb_params': {'window': 20, 'num_std': 2}, 'atr_periods': [14], 'normalization': 'zscore'}
2025-05-30 23:13:09,840 - test_fixes - INFO - 转换后的标准格式配置: {'price_features': {'use': True}, 'volume': {'use': True}, 'sma': {'use': True, 'periods': [5, 10, 20, 30, 60]}, 'ema': {'use': True, 'periods': [5, 10, 20, 30, 60]}, 'rsi': {'use': True, 'periods': [14]}, 'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9}, 'bbands': {'use': True, 'period': 20, 'std': 2}, 'atr': {'use': True, 'periods': [14]}, 'normalization': {'use': True, 'method': 'zscore'}}
2025-05-30 23:13:09,840 - drl_trading - INFO - 检测到标准格式配置，进行验证和填充默认值
2025-05-30 23:13:09,841 - test_fixes - INFO - 标准格式配置: {'price_features': {'use': True}, 'volume': {'use': True, 'periods': [5, 10, 20]}, 'sma': {'use': True, 'periods': [5, 10, 20, 60]}, 'ema': {'use': True, 'periods': [5, 10, 20]}, 'rsi': {'use': True, 'periods': [6, 14, 21]}, 'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9}, 'bbands': {'use': True, 'period': 20, 'std': 2.0}, 'normalization': {'use': True, 'method': 'minmax'}}
2025-05-30 23:13:09,841 - test_fixes - INFO - 适配后的配置: {'price_features': {'use': True}, 'volume': {'use': True, 'periods': [5, 10, 20]}, 'sma': {'use': True, 'periods': [5, 10, 20, 60]}, 'ema': {'use': True, 'periods': [5, 10, 20]}, 'rsi': {'use': True, 'periods': [6, 14, 21]}, 'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9}, 'bbands': {'use': True, 'period': 20, 'std': 2.0}, 'atr': {'use': True, 'periods': [14]}, 'normalization': {'use': True, 'method': 'minmax'}}
2025-05-30 23:13:09,841 - test_fixes - INFO - 测试性能分析器参数处理
2025-05-30 23:13:09,844 - test_fixes - INFO - 测试直接传入DataFrame
2025-05-30 23:13:09,846 - drl_trading - INFO - 交易记录中没有profit列，尝试从交易记录计算
2025-05-30 23:13:09,847 - test_fixes - ERROR - 测试性能分析器参数处理时出错: 'shares'
2025-05-30 23:13:09,884 - test_fixes - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pandas\core\indexes\base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'shares'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\cursor\量化\test_fixes.py", line 165, in test_performance_analyzer
    metrics_df = analyzer.analyze(trades_df, portfolio_values)
  File "C:\cursor\量化\quant_project\core_logic\performance_analyzer.py", line 87, in analyze
    trade_stats = self.calculate_trade_statistics(trades_df)
  File "C:\cursor\量化\quant_project\core_logic\performance_analyzer.py", line 370, in calculate_trade_statistics
    shares = buy_trade['shares']  # 假设买入和卖出的股数相同
             ~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pandas\core\series.py", line 1121, in __getitem__
    return self._get_value(key)
           ~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pandas\core\series.py", line 1237, in _get_value
    loc = self.index.get_loc(label)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'shares'

2025-05-30 23:13:09,884 - test_fixes - INFO - === 测试数据获取功能 ===
2025-05-30 23:13:09,907 - test_fixes - INFO - 测试数据获取: {'stock_code': 'sh000001', 'start_date': '2020-01-01', 'end_date': '2020-12-31', 'frequency': 'D', 'data_source': '指数'}
2025-05-30 23:13:09,908 - test_fixes - ERROR - 数据获取功能测试失败: DataHandlerAdapter.get_stock_data() got an unexpected keyword argument 'data_source'
2025-05-30 23:13:09,908 - test_fixes - INFO - === 测试因子挖掘功能 ===
2025-05-30 23:13:09,917 - test_fixes - ERROR - 因子挖掘功能测试失败: DataHandlerAdapter.get_stock_data() got an unexpected keyword argument 'data_source'
2025-05-30 23:13:09,917 - test_fixes - INFO - === 测试DRL智能体功能 ===
2025-05-30 23:13:09,917 - test_fixes - ERROR - DRL智能体功能测试失败: PPOAgent.__init__() missing 2 required positional arguments: 'env_config' and 'agent_config'
2025-05-30 23:13:09,917 - test_fixes - INFO - ==================================================
2025-05-30 23:13:09,918 - test_fixes - INFO - 测试结果:
2025-05-30 23:13:09,918 - test_fixes - INFO - 1. GPU检测功能: 成功
2025-05-30 23:13:09,918 - test_fixes - INFO - 2. 特征工程适配器: 失败
2025-05-30 23:13:09,918 - test_fixes - INFO -    错误: 未知错误
2025-05-30 23:13:09,918 - test_fixes - INFO - 3. 性能分析器参数处理: 失败
2025-05-30 23:13:09,918 - test_fixes - INFO -    错误: 'shares'
2025-05-30 23:13:09,918 - test_fixes - INFO - 4. 数据获取功能: 失败
2025-05-30 23:13:09,918 - test_fixes - INFO -    错误: False
2025-05-30 23:13:09,919 - test_fixes - INFO - 5. 因子挖掘功能: 失败
2025-05-30 23:13:09,919 - test_fixes - INFO -    错误: False
2025-05-30 23:13:09,919 - test_fixes - INFO - 6. DRL智能体功能: 失败
2025-05-30 23:13:09,919 - test_fixes - INFO -    错误: False
2025-05-30 23:13:09,919 - test_fixes - INFO - ==================================================
2025-05-30 23:13:09,919 - test_fixes - INFO - 修复测试总结: 部分失败
