"""
回测模块
提供回测引擎、性能指标计算、回测结果可视化等功能
"""

try:
    from .backtest_engine import BacktestEngine
except ImportError:
    BacktestEngine = None

try:
    from .performance_metrics import PerformanceMetrics
except ImportError:
    PerformanceMetrics = None

try:
    from .visualization import BacktestVisualizer
except ImportError:
    BacktestVisualizer = None

try:
    from .optimization import StrategyOptimizer
except ImportError:
    StrategyOptimizer = None

try:
    from .report_generator import ReportGenerator
except ImportError:
    ReportGenerator = None

# 向后兼容导出
__all__ = [
    'BacktestEngine',
    'PerformanceMetrics',
    'BacktestVisualizer',
    'StrategyOptimizer',
    'ReportGenerator'
]