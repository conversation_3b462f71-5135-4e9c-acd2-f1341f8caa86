"""
因子生成器模块
负责自动生成各类交易因子
"""

import logging
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from scipy import stats

class FactorGenerator:
    """
    因子生成器
    负责自动生成各类交易因子
    """
    
    def __init__(self, data_handler, base_features=None):
        """
        初始化因子生成器
        
        参数:
            data_handler: 数据处理器
            base_features: 基础特征列表，默认为OHLCV
        """
        self.data_handler = data_handler
        self.base_features = base_features or ['open', 'high', 'low', 'close', 'volume']
        self.logger = logging.getLogger('drl_trading')
        
    def generate_technical_factors(self, df):
        """
        生成技术指标族，尝试不同参数组合
        
        参数:
            df (pandas.DataFrame): 原始行情数据
            
        返回:
            dict: 生成的技术指标字典
        """
        self.logger.info("生成技术指标因子...")
        self.logger.info(f"输入数据形状: {df.shape}, 列名: {list(df.columns)}")
        factors = {}
        
        # 确保输入数据有所需的列（支持中文列名和英文列名）
        required_cols = {
            'open': ['open', '开盘', 'Open', '开盘价', 'OPEN'],
            'high': ['high', '最高', 'High', '最高价', 'HIGH'],
            'low': ['low', '最低', 'Low', '最低价', 'LOW'], 
            'close': ['close', '收盘', 'Close', '收盘价', 'CLOSE'],
            'volume': ['volume', '成交量', 'Volume', '成交量', 'VOLUME', 'vol', 'Vol', 'VOL']
        }
        
        # 创建列名映射
        col_mapping = {}
        for std_name, variants in required_cols.items():
            for variant in variants:
                if variant in df.columns:
                    col_mapping[std_name] = variant
                    self.logger.info(f"找到列 '{variant}' 映射到标准名称 '{std_name}'")
                    break
        
        # 检查是否找到所有必要的列
        missing_cols = [col for col in required_cols.keys() if col not in col_mapping]
        if missing_cols:
            self.logger.warning(f"输入数据缺少必要的列: {', '.join(missing_cols)}")
            self.logger.info(f"尝试使用数值列作为替代，可用列: {[col for col in df.columns if pd.api.types.is_numeric_dtype(df[col])]}")
            
            # 尝试使用位置索引匹配列
            numeric_cols = [col for col in df.columns if pd.api.types.is_numeric_dtype(df[col])]
            if len(numeric_cols) >= 5:
                self.logger.info(f"找到 {len(numeric_cols)} 个数值列，尝试使用位置索引匹配OHLCV")
                # 假设前5列是OHLCV
                standard_names = ['open', 'high', 'low', 'close', 'volume']
                for i, std_name in enumerate(standard_names):
                    if i < len(numeric_cols) and std_name in missing_cols:
                        col_mapping[std_name] = numeric_cols[i]
                        self.logger.info(f"使用列 '{numeric_cols[i]}' 作为 '{std_name}'")
            
            # 如果还有缺失的列，尝试创建一些默认值
            still_missing = [col for col in required_cols.keys() if col not in col_mapping]
            if still_missing:
                self.logger.warning(f"仍然缺少列: {still_missing}, 尝试创建默认值")
                
                # 检查是否至少有一个价格列可用
                price_cols = [col for col in df.columns if any(price_word in col.lower() for price_word in ['价', '价格', 'price', 'close', 'open', 'high', 'low'])]
                
                if price_cols:
                    self.logger.info(f"找到可能的价格列: {price_cols}, 使用第一个作为基准")
                    price_col = price_cols[0]
                    
                    if 'close' in still_missing:
                        col_mapping['close'] = price_col
                        self.logger.info(f"使用 '{price_col}' 作为收盘价")
                
                if 'close' in col_mapping:  # 如果至少有收盘价
                    close_col = col_mapping['close']
                    # 如果缺少开盘价，使用收盘价
                    if 'open' in still_missing:
                        df['open'] = df[close_col]
                        col_mapping['open'] = 'open'
                        self.logger.info("创建开盘价列（使用收盘价）")
                    # 如果缺少最高价，使用收盘价的1.005倍
                    if 'high' in still_missing:
                        df['high'] = df[close_col] * 1.005
                        col_mapping['high'] = 'high'
                        self.logger.info("创建最高价列（收盘价的1.005倍）")
                    # 如果缺少最低价，使用收盘价的0.995倍
                    if 'low' in still_missing:
                        df['low'] = df[close_col] * 0.995
                        col_mapping['low'] = 'low'
                        self.logger.info("创建最低价列（收盘价的0.995倍）")
                    # 如果缺少成交量，创建一个随机成交量
                    if 'volume' in still_missing:
                        df['volume'] = np.random.randint(1000, 10000, size=len(df))
                        col_mapping['volume'] = 'volume'
                        self.logger.info("创建成交量列（随机数）")
        
        # 如果无法获取必要的列，返回空字典
        if not col_mapping:
            self.logger.error("无法获取必要的列，无法生成技术指标")
            self.logger.error(f"需要的列: {list(required_cols.keys())}, 可用列: {list(df.columns)}")
            return factors
        
        self.logger.info(f"最终列映射: {col_mapping}")
        
        # 确保索引是日期类型
        if not isinstance(df.index, pd.DatetimeIndex):
            self.logger.warning("数据索引不是日期类型，尝试转换")
            try:
                # 检查是否有日期列
                date_cols = [col for col in df.columns if '日期' in col or 'date' in col.lower() or 'time' in col.lower()]
                if date_cols:
                    self.logger.info(f"找到日期列: {date_cols[0]}, 尝试设置为索引")
                    df = df.set_index(date_cols[0])
                    try:
                        df.index = pd.to_datetime(df.index)
                        self.logger.info("成功转换索引为日期类型")
                    except Exception as e:
                        self.logger.error(f"转换索引为日期类型失败: {str(e)}")
                else:
                    # 使用行号作为日期索引
                    self.logger.warning("没有找到日期列，使用行号作为日期索引")
                    # 创建一个从今天往前的日期序列
                    today = datetime.now()
                    dates = pd.date_range(end=today, periods=len(df))
                    df.index = dates
                    self.logger.info("使用模拟日期作为索引")
            except Exception as e:
                self.logger.error(f"设置日期索引失败: {str(e)}")
        
        try:
            # 基本价格特征
            close = df[col_mapping['close']]
            open_price = df[col_mapping['open']]
            high = df[col_mapping['high']]
            low = df[col_mapping['low']]
            volume = df[col_mapping['volume']]
            
            # ---------- 基础价格因子 ----------
            # 当日价格变动
            factors['daily_return'] = close.pct_change()
            factors['high_low_ratio'] = high / low
            factors['close_open_ratio'] = close / open_price
            factors['amplitude'] = (high - low) / open_price  # 振幅
            
            # ---------- 移动平均线族 ----------
            for period in [5, 10, 20, 30, 60, 120]:
                # 简单移动平均线
                factors[f'sma_{period}'] = close.rolling(window=period).mean()
                # 指数移动平均线
                factors[f'ema_{period}'] = close.ewm(span=period, adjust=False).mean()
                # 成交量加权移动平均线
                factors[f'vwap_{period}'] = (close * volume).rolling(window=period).sum() / volume.rolling(window=period).sum()
                # 加权移动平均线
                weights = np.arange(1, period + 1)
                factors[f'wma_{period}'] = close.rolling(window=period).apply(lambda x: np.sum(weights * x) / weights.sum(), raw=True)
                
                # 价格与均线关系
                factors[f'close_sma{period}_ratio'] = close / factors[f'sma_{period}']
                factors[f'close_ema{period}_ratio'] = close / factors[f'ema_{period}']
                
                # 均线趋势
                factors[f'sma{period}_slope'] = factors[f'sma_{period}'].diff(3) / 3  # 3日均线斜率
                factors[f'ema{period}_slope'] = factors[f'ema_{period}'].diff(3) / 3  # 3日均线斜率
            
            # ---------- 价格筛选自适应移动平均 ----------
            # 实现Kaufman自适应移动平均(KAMA)
            def kama(series, er_period=10, fast_period=2, slow_period=30):
                er = abs(series.diff(er_period)) / (abs(series.diff()).rolling(er_period).sum())
                fast_alpha = 2 / (fast_period + 1)
                slow_alpha = 2 / (slow_period + 1)
                sc = (er * (fast_alpha - slow_alpha) + slow_alpha) ** 2
                kama_series = pd.Series(0.0, index=series.index)
                kama_series.iloc[er_period] = series.iloc[er_period]
                
                for i in range(er_period + 1, len(series)):
                    kama_series.iloc[i] = kama_series.iloc[i-1] + sc.iloc[i] * (series.iloc[i] - kama_series.iloc[i-1])
                
                return kama_series
                
            for er_period in [10, 20]:
                factors[f'kama_{er_period}'] = kama(close, er_period=er_period)
                factors[f'close_kama{er_period}_ratio'] = close / factors[f'kama_{er_period}']
                
            # ---------- RSI指标族 ----------
            def calculate_rsi(prices, period=14):
                delta = prices.diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
                
                rs = gain / loss
                rsi = 100 - (100 / (1 + rs))
                return rsi
                
            for period in [6, 14, 21, 28]:
                factors[f'rsi_{period}'] = calculate_rsi(close, period=period)
                
            # ---------- 布林带指标族 ----------
            for period in [20, 30]:
                for std in [1.0, 1.5, 2.0]:
                    middle = close.rolling(window=period).mean()
                    rolling_std = close.rolling(window=period).std()
                    
                    upper = middle + std * rolling_std
                    lower = middle - std * rolling_std
                    
                    suffix = f"_{period}_{int(std*10)}"
                    factors[f'bb_middle{suffix}'] = middle
                    factors[f'bb_upper{suffix}'] = upper
                    factors[f'bb_lower{suffix}'] = lower
                    factors[f'bb_width{suffix}'] = (upper - lower) / middle
                    factors[f'bb_position{suffix}'] = (close - lower) / (upper - lower)
                    
            # ---------- MACD指标族 ----------
            for fast_period, slow_period, signal_period in [(12, 26, 9), (8, 17, 9), (5, 34, 5)]:
                ema_fast = close.ewm(span=fast_period, adjust=False).mean()
                ema_slow = close.ewm(span=slow_period, adjust=False).mean()
                macd = ema_fast - ema_slow
                signal = macd.ewm(span=signal_period, adjust=False).mean()
                histogram = macd - signal
                
                suffix = f"_{fast_period}_{slow_period}_{signal_period}"
                factors[f'macd{suffix}'] = macd
                factors[f'macd_signal{suffix}'] = signal
                factors[f'macd_hist{suffix}'] = histogram
                
            # ---------- KDJ指标 ----------
            def calculate_kdj(high_series, low_series, close_series, n=9, m1=3, m2=3):
                lowest_low = low_series.rolling(window=n).min()
                highest_high = high_series.rolling(window=n).max()
                rsv = ((close_series - lowest_low) / (highest_high - lowest_low)) * 100
                
                k = pd.Series(np.zeros(len(rsv)), index=rsv.index)
                d = pd.Series(np.zeros(len(rsv)), index=rsv.index)
                j = pd.Series(np.zeros(len(rsv)), index=rsv.index)
                
                for i in range(n, len(rsv)):
                    if i == n:
                        k[i] = 50
                        d[i] = 50
                    else:
                        k[i] = (m1 * rsv[i] + (n - m1) * k[i-1]) / n
                        d[i] = (m2 * k[i] + (n - m2) * d[i-1]) / n
                    j[i] = 3 * k[i] - 2 * d[i]
                
                return k, d, j
                
            for n in [9, 14, 20]:
                for m1, m2 in [(3, 3), (2, 2)]:
                    k, d, j = calculate_kdj(high, low, close, n=n, m1=m1, m2=m2)
                    suffix = f"_{n}_{m1}_{m2}"
                    factors[f'k{suffix}'] = k
                    factors[f'd{suffix}'] = d
                    factors[f'j{suffix}'] = j
            
            # ---------- CCI指标族 ----------
            def calculate_cci(high_series, low_series, close_series, n=14):
                tp = (high_series + low_series + close_series) / 3
                ma = tp.rolling(window=n).mean()
                md = tp.rolling(window=n).apply(lambda x: np.abs(x - x.mean()).mean())
                cci = (tp - ma) / (0.015 * md)
                return cci
                
            for period in [14, 20, 30]:
                factors[f'cci_{period}'] = calculate_cci(high, low, close, n=period)
                
            # ---------- ATR指标族 ----------
            def calculate_atr(high_series, low_series, close_series, n=14):
                tr1 = high_series - low_series
                tr2 = abs(high_series - close_series.shift(1))
                tr3 = abs(low_series - close_series.shift(1))
                
                tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
                atr = tr.rolling(window=n).mean()
                return atr, tr
                
            for period in [7, 14, 21]:
                atr, tr = calculate_atr(high, low, close, n=period)
                factors[f'atr_{period}'] = atr
                factors[f'tr_{period}'] = tr
                factors[f'atr_percent_{period}'] = atr / close * 100
            
            # ---------- 抛物线转向系统（SAR）----------
            def calculate_psar(high, low, close, af_start=0.02, af_step=0.02, af_max=0.2):
                bull = True  # 初始为上升趋势
                af = af_start  # 加速因子初始值
                ep = low[0]  # 极点初始值为第一个低点
                sar = high[0]  # SAR初始值为第一个高点
                sar_series = pd.Series(index=high.index)
                
                for i in range(1, len(high)):
                    if bull:
                        # 上升趋势
                        sar = sar + af * (ep - sar)
                        sar = min(sar, low[i-1], low[i-2] if i >= 2 else low[i-1])
                        
                        if high[i] > ep:
                            ep = high[i]
                            af = min(af + af_step, af_max)
                            
                        if low[i] < sar:
                            bull = False
                            af = af_start
                            sar = ep
                            ep = low[i]
                    else:
                        # 下降趋势
                        sar = sar - af * (sar - ep)
                        sar = max(sar, high[i-1], high[i-2] if i >= 2 else high[i-1])
                        
                        if low[i] < ep:
                            ep = low[i]
                            af = min(af + af_step, af_max)
                            
                        if high[i] > sar:
                            bull = True
                            af = af_start
                            sar = ep
                            ep = high[i]
                    
                    sar_series[i] = sar
                
                return sar_series
            
            factors['psar'] = calculate_psar(high, low, close)
            factors['psar_ratio'] = close / factors['psar']
            
            # ---------- 母子线（蜡烛图形态）----------
            factors['upper_shadow'] = high - np.maximum(open_price, close)
            factors['lower_shadow'] = np.minimum(open_price, close) - low
            factors['body_size'] = abs(close - open_price)
            factors['is_bullish'] = (close > open_price).astype(int)
            factors['is_bearish'] = (close < open_price).astype(int)
            
            # 计算蜡烛图形态指标
            body_avg = factors['body_size'].rolling(window=14).mean()
            factors['body_ratio'] = factors['body_size'] / body_avg
            factors['upper_shadow_ratio'] = factors['upper_shadow'] / factors['body_size']
            factors['lower_shadow_ratio'] = factors['lower_shadow'] / factors['body_size']
            
            # ---------- 波动率指标 ----------
            # 历史波动率
            for period in [10, 20, 30]:
                log_return = np.log(close / close.shift(1))
                factors[f'volatility_{period}'] = log_return.rolling(window=period).std() * np.sqrt(252)
            
            # ---------- 量价关系指标 ----------
            factors['volume_price_corr_10'] = close.rolling(10).corr(volume)
            factors['volume_price_corr_20'] = close.rolling(20).corr(volume)
            
            # 量能比率
            for period in [5, 10, 20]:
                factors[f'volume_ratio_{period}'] = volume / volume.rolling(window=period).mean()
            
            # ---------- 累积量能指标 ----------
            # 计算OBV
            obv = pd.Series(0, index=close.index)
            for i in range(1, len(close)):
                if close[i] > close[i-1]:
                    obv[i] = obv[i-1] + volume[i]
                elif close[i] < close[i-1]:
                    obv[i] = obv[i-1] - volume[i]
                else:
                    obv[i] = obv[i-1]
            
            factors['obv'] = obv
            factors['obv_ma_10'] = obv.rolling(window=10).mean()
            factors['obv_ma_20'] = obv.rolling(window=20).mean()
            
            # ---------- 价格动量指标族 ----------
            for period in [10, 20, 30, 60]:
                # 价格动量
                factors[f'momentum_{period}'] = close / close.shift(period) - 1
                # 变化率
                factors[f'roc_{period}'] = (close - close.shift(period)) / close.shift(period) * 100
            
            # ---------- 威廉指标 ----------
            def williams_r(high, low, close, period=14):
                highest_high = high.rolling(window=period).max()
                lowest_low = low.rolling(window=period).min()
                wr = -100 * (highest_high - close) / (highest_high - lowest_low)
                return wr
                
            for period in [14, 20]:
                factors[f'williams_r_{period}'] = williams_r(high, low, close, period=period)
            
            # ---------- 随机指标 ----------
            for period in [14, 21]:
                for k_period in [3, 5]:
                    highest_high = high.rolling(window=period).max()
                    lowest_low = low.rolling(window=period).min()
                    k_raw = 100 * (close - lowest_low) / (highest_high - lowest_low)
                    k = k_raw.rolling(window=k_period).mean()
                    d = k.rolling(window=3).mean()
                    
                    factors[f'stoch_k_{period}_{k_period}'] = k
                    factors[f'stoch_d_{period}_{k_period}'] = d
            
            # ---------- 相对强弱指数变化率 ----------
            for period in [6, 14]:
                rsi = factors[f'rsi_{period}']
                factors[f'rsi_change_{period}'] = rsi - rsi.shift(1)
                factors[f'rsi_ma_{period}_5'] = rsi.rolling(window=5).mean()
            
            # ---------- 市场微观结构特征 ----------
            factors['price_range'] = high - low
            factors['price_range_ma_5'] = factors['price_range'].rolling(window=5).mean()
            factors['price_range_ma_10'] = factors['price_range'].rolling(window=10).mean()
            
            # ---------- 特征工程元指标 ----------
            # 价格与移动平均距离的标准差（衡量价格波动性）
            for period in [10, 20]:
                rolling_mean = close.rolling(window=period).mean()
                rolling_std = close.rolling(window=period).std()
                factors[f'z_score_{period}'] = (close - rolling_mean) / rolling_std
            
            # 返回所有计算的技术指标
            self.logger.info(f"成功生成 {len(factors)} 个技术指标因子")
            return factors
            
        except Exception as e:
            self.logger.error(f"生成技术指标时出错: {str(e)}")
            import traceback
            self.logger.error(traceback.format_exc())
            return factors

    def generate_cross_factors(self, base_factors):
        """
        生成交叉因子，结合不同的基础因子
        
        参数:
            base_factors (dict): 基础因子字典
            
        返回:
            dict: 交叉因子字典
        """
        cross_factors = {}
        
        # 选择一些主要的基础因子进行交叉
        primary_factors = {
            'sma_5': base_factors.get('sma_5'),
            'sma_20': base_factors.get('sma_20'),
            'ema_12': base_factors.get('ema_12', base_factors.get('ema_10')),
            'rsi_14': base_factors.get('rsi_14'),
            'volatility_20': base_factors.get('volatility_20'),
            'macd_12_26_9': base_factors.get('macd_12_26_9')
        }
        
        # 过滤掉None值
        primary_factors = {k: v for k, v in primary_factors.items() if v is not None}
        
        if len(primary_factors) < 2:
            self.logger.warning("基础因子不足，无法生成交叉因子")
            return cross_factors
            
        # 生成因子比值
        factor_keys = list(primary_factors.keys())
        for i in range(len(factor_keys)):
            for j in range(i+1, len(factor_keys)):
                key_i = factor_keys[i]
                key_j = factor_keys[j]
                
                # 因子比值
                cross_name = f"{key_i}_to_{key_j}"
                cross_factors[cross_name] = primary_factors[key_i] / primary_factors[key_j]
                
                # 因子差值
                diff_name = f"{key_i}_minus_{key_j}"
                cross_factors[diff_name] = primary_factors[key_i] - primary_factors[key_j]
        
        self.logger.info(f"成功生成 {len(cross_factors)} 个交叉因子")
        return cross_factors
        
    def generate_time_factors(self, base_factors, time_shifts=[1, 2, 3, 5, 10]):
        """
        生成时间序列因子，包括滞后、差分和滚动统计
        
        参数:
            base_factors (dict): 基础因子字典
            time_shifts (list): 时间平移参数列表
            
        返回:
            dict: 时间序列因子字典
        """
        time_factors = {}
        
        # 选择一部分重要的基础因子生成时间序列因子
        important_factors = ['daily_return', 'rsi_14', 'sma_20', 'volume_ratio_5', 'macd_12_26_9']
        
        for factor_name in important_factors:
            if factor_name not in base_factors:
                continue
                
            factor = base_factors[factor_name]
            
            # 滞后因子
            for shift in time_shifts:
                time_factors[f"{factor_name}_lag_{shift}"] = factor.shift(shift)
            
            # 差分因子
            for shift in [1, 3, 5]:
                time_factors[f"{factor_name}_diff_{shift}"] = factor.diff(shift)
            
            # 滚动统计
            for window in [3, 5, 10]:
                time_factors[f"{factor_name}_rolling_mean_{window}"] = factor.rolling(window=window).mean()
                time_factors[f"{factor_name}_rolling_std_{window}"] = factor.rolling(window=window).std()
                time_factors[f"{factor_name}_rolling_min_{window}"] = factor.rolling(window=window).min()
                time_factors[f"{factor_name}_rolling_max_{window}"] = factor.rolling(window=window).max()
        
        self.logger.info(f"成功生成 {len(time_factors)} 个时间序列因子")
        return time_factors 