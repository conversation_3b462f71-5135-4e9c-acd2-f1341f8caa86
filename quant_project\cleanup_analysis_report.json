{"timestamp": "1748621132.2079751", "project_root": "C:\\cursor\\量化\\quant_project", "analysis_results": {"duplicate_files": [["C:\\cursor\\量化\\quant_project\\main_app.py", "C:\\cursor\\量化\\quant_project\\fixed_main_app.py"], ["C:\\cursor\\量化\\quant_project\\main_app.py", "C:\\cursor\\量化\\quant_project\\main_app_with_factor_mining.py"]], "test_files": ["C:\\cursor\\量化\\quant_project\\test_auto_factor_mining.py", "C:\\cursor\\量化\\quant_project\\test_auto_training_flow.py", "C:\\cursor\\量化\\quant_project\\test_backtesting_bug.py", "C:\\cursor\\量化\\quant_project\\test_callback_fix.py", "C:\\cursor\\量化\\quant_project\\test_comprehensive_fixes.py", "C:\\cursor\\量化\\quant_project\\test_data_acquisition.py", "C:\\cursor\\量化\\quant_project\\test_data_acquisition_bug.py", "C:\\cursor\\量化\\quant_project\\test_data_handling.py", "C:\\cursor\\量化\\quant_project\\test_date_handling.py", "C:\\cursor\\量化\\quant_project\\test_factor_mining_page.py", "C:\\cursor\\量化\\quant_project\\test_feature_engineering.py", "C:\\cursor\\量化\\quant_project\\test_fixes.py", "C:\\cursor\\量化\\quant_project\\test_hpo.py", "C:\\cursor\\量化\\quant_project\\test_import.py", "C:\\cursor\\量化\\quant_project\\test_observation_fix.py", "C:\\cursor\\量化\\quant_project\\test_observation_shape.py", "C:\\cursor\\量化\\quant_project\\test_profit_fix.py", "C:\\cursor\\量化\\quant_project\\test_streamlit.py", "C:\\cursor\\量化\\quant_project\\test_trading_env.py", "C:\\cursor\\量化\\quant_project\\test_ui.py", "C:\\cursor\\量化\\quant_project\\tests\\test_config_loading.py", "C:\\cursor\\量化\\quant_project\\tests\\test_data_extraction.py", "C:\\cursor\\量化\\quant_project\\tests\\test_drl_agent.py", "C:\\cursor\\量化\\quant_project\\tests\\test_ensemble_learning.py", "C:\\cursor\\量化\\quant_project\\tests\\test_ensemble_ui_logic.py", "C:\\cursor\\量化\\quant_project\\tests\\test_extended_data_extraction.py", "C:\\cursor\\量化\\quant_project\\tests\\test_feature_engineering.py", "C:\\cursor\\量化\\quant_project\\tests\\test_gpu_detection.py", "C:\\cursor\\量化\\quant_project\\tests\\test_optimized_modules.py", "C:\\cursor\\量化\\quant_project\\tests\\test_performance_analyzer.py", "C:\\cursor\\量化\\quant_project\\tests\\test_trading_environment_constraints.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\aiohttp\\test_utils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\patsy\\test_build.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\patsy\\test_highlevel.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\patsy\\test_regressions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\patsy\\test_splines_bs_data.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\patsy\\test_splines_crs_data.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\patsy\\test_state.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tests\\test_func.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\~orch\\_dynamo\\test_case.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\~orch\\_dynamo\\test_minifier_common.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\~orch\\_inductor\\test_case.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\~orch\\_inductor\\test_operators.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\~orch\\testing\\_internal\\distributed\\_shard\\test_common.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\~orch\\fx\\passes\\tests\\test_pass_manager.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\torch\\_dynamo\\test_case.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\torch\\_dynamo\\test_minifier_common.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\torch\\_inductor\\test_case.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\torch\\_inductor\\test_operators.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\torch\\testing\\_internal\\distributed\\_shard\\test_common.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\torch\\fx\\passes\\tests\\test_pass_manager.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\vector\\tests\\test_coordsysrect.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\vector\\tests\\test_dyadic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\vector\\tests\\test_field_functions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\vector\\tests\\test_functions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\vector\\tests\\test_implicitregion.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\vector\\tests\\test_integrals.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\vector\\tests\\test_operators.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\vector\\tests\\test_parametricregion.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\vector\\tests\\test_printing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\vector\\tests\\test_vector.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\utilities\\tests\\test_autowrap.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\utilities\\tests\\test_codegen.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\utilities\\tests\\test_codegen_julia.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\utilities\\tests\\test_codegen_octave.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\utilities\\tests\\test_codegen_rust.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\utilities\\tests\\test_decorator.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\utilities\\tests\\test_deprecated.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\utilities\\tests\\test_enumerative.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\utilities\\tests\\test_exceptions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\utilities\\tests\\test_iterables.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\utilities\\tests\\test_lambdify.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\utilities\\tests\\test_matchpy_connector.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\utilities\\tests\\test_mathml.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\utilities\\tests\\test_misc.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\utilities\\tests\\test_pickling.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\utilities\\tests\\test_source.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\utilities\\tests\\test_timeutils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\utilities\\tests\\test_wester.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\utilities\\tests\\test_xxe.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\utilities\\_compilation\\tests\\test_compilation.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\unify\\tests\\test_rewrite.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\unify\\tests\\test_sympy.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\unify\\tests\\test_unify.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\testing\\tests\\test_code_quality.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\testing\\tests\\test_deprecated.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\testing\\tests\\test_module_imports.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\testing\\tests\\test_pytest.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\testing\\tests\\test_runtests_pytest.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\tensor\\tests\\test_functions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\tensor\\tests\\test_indexed.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\tensor\\tests\\test_index_methods.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\tensor\\tests\\test_printing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\tensor\\tests\\test_tensor.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\tensor\\tests\\test_tensor_element.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\tensor\\tests\\test_tensor_operators.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\tensor\\array\\tests\\test_arrayop.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\tensor\\array\\tests\\test_array_comprehension.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\tensor\\array\\tests\\test_array_derivatives.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\tensor\\array\\tests\\test_immutable_ndim_array.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\tensor\\array\\tests\\test_mutable_ndim_array.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\tensor\\array\\tests\\test_ndim_array.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\tensor\\array\\tests\\test_ndim_array_conversions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\tensor\\array\\expressions\\tests\\test_arrayexpr_derivatives.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\tensor\\array\\expressions\\tests\\test_array_expressions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\tensor\\array\\expressions\\tests\\test_as_explicit.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\tensor\\array\\expressions\\tests\\test_convert_array_to_indexed.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\tensor\\array\\expressions\\tests\\test_convert_array_to_matrix.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\tensor\\array\\expressions\\tests\\test_convert_indexed_to_array.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\tensor\\array\\expressions\\tests\\test_convert_matrix_to_array.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\tensor\\array\\expressions\\tests\\test_deprecated_conv_modules.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\strategies\\tests\\test_core.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\strategies\\tests\\test_rl.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\strategies\\tests\\test_tools.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\strategies\\tests\\test_traverse.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\strategies\\tests\\test_tree.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\strategies\\branch\\tests\\test_core.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\strategies\\branch\\tests\\test_tools.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\strategies\\branch\\tests\\test_traverse.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\stats\\tests\\test_compound_rv.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\stats\\tests\\test_continuous_rv.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\stats\\tests\\test_discrete_rv.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\stats\\tests\\test_error_prop.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\stats\\tests\\test_finite_rv.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\stats\\tests\\test_joint_rv.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\stats\\tests\\test_matrix_distributions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\stats\\tests\\test_mix.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\stats\\tests\\test_random_matrix.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\stats\\tests\\test_rv.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\stats\\tests\\test_stochastic_process.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\stats\\tests\\test_symbolic_multivariate.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\stats\\tests\\test_symbolic_probability.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\stats\\sampling\\tests\\test_sample_continuous_rv.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\stats\\sampling\\tests\\test_sample_discrete_rv.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\stats\\sampling\\tests\\test_sample_finite_rv.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\solvers\\tests\\test_constantsimp.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\solvers\\tests\\test_decompogen.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\solvers\\tests\\test_inequalities.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\solvers\\tests\\test_numeric.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\solvers\\tests\\test_pde.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\solvers\\tests\\test_polysys.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\solvers\\tests\\test_recurr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\solvers\\tests\\test_simplex.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\solvers\\tests\\test_solvers.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\solvers\\tests\\test_solveset.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\solvers\\ode\\tests\\test_lie_group.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\solvers\\ode\\tests\\test_ode.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\solvers\\ode\\tests\\test_riccati.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\solvers\\ode\\tests\\test_single.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\solvers\\ode\\tests\\test_subscheck.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\solvers\\ode\\tests\\test_systems.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\solvers\\diophantine\\tests\\test_diophantine.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\simplify\\tests\\test_combsimp.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\simplify\\tests\\test_cse.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\simplify\\tests\\test_cse_diff.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\simplify\\tests\\test_epathtools.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\simplify\\tests\\test_fu.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\simplify\\tests\\test_function.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\simplify\\tests\\test_gammasimp.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\simplify\\tests\\test_hyperexpand.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\simplify\\tests\\test_powsimp.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\simplify\\tests\\test_radsimp.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\simplify\\tests\\test_ratsimp.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\simplify\\tests\\test_rewrite.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\simplify\\tests\\test_simplify.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\simplify\\tests\\test_sqrtdenest.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\simplify\\tests\\test_trigsimp.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\sets\\tests\\test_conditionset.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\sets\\tests\\test_contains.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\sets\\tests\\test_fancysets.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\sets\\tests\\test_ordinals.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\sets\\tests\\test_powerset.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\sets\\tests\\test_setexpr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\sets\\tests\\test_sets.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\series\\tests\\test_approximants.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\series\\tests\\test_aseries.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\series\\tests\\test_demidovich.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\series\\tests\\test_formal.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\series\\tests\\test_fourier.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\series\\tests\\test_gruntz.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\series\\tests\\test_kauers.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\series\\tests\\test_limits.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\series\\tests\\test_limitseq.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\series\\tests\\test_lseries.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\series\\tests\\test_nseries.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\series\\tests\\test_order.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\series\\tests\\test_residues.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\series\\tests\\test_sequences.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\series\\tests\\test_series.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\sandbox\\tests\\test_indexed_integrals.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_aesaracode.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_c.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_codeprinter.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_conventions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_cupy.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_cxx.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_dot.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_fortran.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_glsl.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_gtk.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_jax.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_jscode.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_julia.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_lambdarepr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_latex.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_llvmjit.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_maple.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_mathematica.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_mathml.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_numpy.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_octave.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_precedence.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_preview.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_pycode.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_python.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_rcode.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_repr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_rust.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_smtlib.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_str.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_tableform.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_tensorflow.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_theanocode.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_torch.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\tests\\test_tree.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\printing\\pretty\\tests\\test_pretty.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_appellseqs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_constructor.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_densearith.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_densebasic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_densetools.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_dispersion.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_distributedmodules.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_euclidtools.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_factortools.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_fields.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_galoistools.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_groebnertools.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_heuristicgcd.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_hypothesis.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_injections.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_modulargcd.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_monomials.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_multivariate_resultants.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_orderings.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_orthopolys.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_partfrac.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_polyclasses.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_polyfuncs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_polymatrix.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_polyoptions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_polyroots.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_polytools.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_polyutils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_puiseux.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_pythonrational.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_rationaltools.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_rings.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_ring_series.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_rootisolation.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_rootoftools.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_solvers.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_specialpolys.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_sqfreetools.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\tests\\test_subresultants_qq_zz.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\numberfields\\tests\\test_basis.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\numberfields\\tests\\test_galoisgroups.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\numberfields\\tests\\test_minpoly.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\numberfields\\tests\\test_modules.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\numberfields\\tests\\test_numbers.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\numberfields\\tests\\test_primes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\numberfields\\tests\\test_subfield.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\numberfields\\tests\\test_utilities.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\matrices\\tests\\test_ddm.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\matrices\\tests\\test_dense.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\matrices\\tests\\test_domainmatrix.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\matrices\\tests\\test_domainscalar.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\matrices\\tests\\test_eigen.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\matrices\\tests\\test_fflu.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\matrices\\tests\\test_inverse.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\matrices\\tests\\test_linsolve.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\matrices\\tests\\test_lll.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\matrices\\tests\\test_normalforms.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\matrices\\tests\\test_nullspace.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\matrices\\tests\\test_rref.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\matrices\\tests\\test_sdm.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\matrices\\tests\\test_xxm.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\domains\\tests\\test_domains.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\domains\\tests\\test_polynomialring.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\domains\\tests\\test_quotientring.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\agca\\tests\\test_extensions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\agca\\tests\\test_homomorphisms.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\agca\\tests\\test_ideals.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\polys\\agca\\tests\\test_modules.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\plotting\\tests\\test_experimental_lambdify.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\plotting\\tests\\test_plot.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\plotting\\tests\\test_plot_implicit.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\plotting\\tests\\test_series.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\plotting\\tests\\test_textplot.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\plotting\\tests\\test_utils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\plotting\\pygletplot\\tests\\test_plotting.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\plotting\\intervalmath\\tests\\test_intervalmath.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\plotting\\intervalmath\\tests\\test_interval_functions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\plotting\\intervalmath\\tests\\test_interval_membership.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\tests\\test_clebsch_gordan.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\tests\\test_hydrogen.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\tests\\test_paulialgebra.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\tests\\test_physics_matrices.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\tests\\test_pring.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\tests\\test_qho_1d.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\tests\\test_secondquant.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\tests\\test_sho.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\vector\\tests\\test_dyadic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\vector\\tests\\test_fieldfunctions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\vector\\tests\\test_frame.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\vector\\tests\\test_functions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\vector\\tests\\test_output.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\vector\\tests\\test_point.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\vector\\tests\\test_printing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\vector\\tests\\test_vector.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\units\\tests\\test_dimensions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\units\\tests\\test_dimensionsystem.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\units\\tests\\test_prefixes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\units\\tests\\test_quantities.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\units\\tests\\test_unitsystem.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\units\\tests\\test_unit_system_cgs_gauss.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\units\\tests\\test_util.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_anticommutator.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_boson.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_cartesian.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_cg.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_circuitplot.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_circuitutils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_commutator.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_constants.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_dagger.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_density.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_fermion.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_gate.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_grover.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_hilbert.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_identitysearch.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_innerproduct.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_kind.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_matrixutils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_operator.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_operatorordering.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_operatorset.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_pauli.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_piab.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_printing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_qapply.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_qasm.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_qexpr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_qft.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_qubit.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_represent.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_sho1d.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_shor.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_spin.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_state.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_tensorproduct.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_trace.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\quantum\\tests\\test_transforms.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\optics\\tests\\test_gaussopt.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\optics\\tests\\test_medium.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\optics\\tests\\test_polarization.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\optics\\tests\\test_utils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\optics\\tests\\test_waves.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\mechanics\\tests\\test_actuator.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\mechanics\\tests\\test_body.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\mechanics\\tests\\test_functions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\mechanics\\tests\\test_inertia.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\mechanics\\tests\\test_joint.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\mechanics\\tests\\test_jointsmethod.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\mechanics\\tests\\test_kane.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\mechanics\\tests\\test_kane2.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\mechanics\\tests\\test_kane3.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\mechanics\\tests\\test_kane4.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\mechanics\\tests\\test_kane5.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\mechanics\\tests\\test_lagrange.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\mechanics\\tests\\test_lagrange2.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\mechanics\\tests\\test_linearity_of_velocity_constraints.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\mechanics\\tests\\test_linearize.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\mechanics\\tests\\test_loads.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\mechanics\\tests\\test_method.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\mechanics\\tests\\test_models.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\mechanics\\tests\\test_particle.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\mechanics\\tests\\test_pathway.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\mechanics\\tests\\test_rigidbody.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\mechanics\\tests\\test_system.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\mechanics\\tests\\test_system_class.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\mechanics\\tests\\test_wrapping_geometry.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\hep\\tests\\test_gamma_matrices.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\control\\tests\\test_control_plots.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\control\\tests\\test_lti.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\continuum_mechanics\\tests\\test_arch.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\continuum_mechanics\\tests\\test_beam.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\continuum_mechanics\\tests\\test_cable.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\continuum_mechanics\\tests\\test_truss.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\biomechanics\\tests\\test_activation.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\biomechanics\\tests\\test_curve.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\biomechanics\\tests\\test_mixin.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\physics\\biomechanics\\tests\\test_musculotendon.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\parsing\\tests\\test_ast_parser.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\parsing\\tests\\test_autolev.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\parsing\\tests\\test_custom_latex.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\parsing\\tests\\test_c_parser.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\parsing\\tests\\test_fortran_parser.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\parsing\\tests\\test_implicit_multiplication_application.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\parsing\\tests\\test_latex.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\parsing\\tests\\test_latex_deps.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\parsing\\tests\\test_latex_lark.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\parsing\\tests\\test_mathematica.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\parsing\\tests\\test_maxima.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\parsing\\tests\\test_sympy_parser.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\parsing\\tests\\test_sym_expr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\ntheory\\tests\\test_bbp_pi.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\ntheory\\tests\\test_continued_fraction.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\ntheory\\tests\\test_digits.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\ntheory\\tests\\test_ecm.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\ntheory\\tests\\test_egyptian_fraction.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\ntheory\\tests\\test_elliptic_curve.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\ntheory\\tests\\test_factor_.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\ntheory\\tests\\test_generate.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\ntheory\\tests\\test_hypothesis.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\ntheory\\tests\\test_modular.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\ntheory\\tests\\test_multinomial.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\ntheory\\tests\\test_partitions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\ntheory\\tests\\test_primetest.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\ntheory\\tests\\test_qs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\ntheory\\tests\\test_residue.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\multipledispatch\\tests\\test_conflict.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\multipledispatch\\tests\\test_core.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\multipledispatch\\tests\\test_dispatcher.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\tests\\test_commonmatrix.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\tests\\test_decompositions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\tests\\test_determinant.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\tests\\test_domains.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\tests\\test_eigen.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\tests\\test_graph.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\tests\\test_immutable.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\tests\\test_interactions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\tests\\test_matrices.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\tests\\test_matrixbase.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\tests\\test_normalforms.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\tests\\test_reductions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\tests\\test_repmatrix.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\tests\\test_solvers.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\tests\\test_sparse.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\tests\\test_sparsetools.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\tests\\test_subspaces.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\expressions\\tests\\test_adjoint.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\expressions\\tests\\test_applyfunc.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\expressions\\tests\\test_blockmatrix.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\expressions\\tests\\test_companion.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\expressions\\tests\\test_derivatives.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\expressions\\tests\\test_determinant.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\expressions\\tests\\test_diagonal.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\expressions\\tests\\test_dotproduct.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\expressions\\tests\\test_factorizations.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\expressions\\tests\\test_fourier.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\expressions\\tests\\test_funcmatrix.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\expressions\\tests\\test_hadamard.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\expressions\\tests\\test_indexing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\expressions\\tests\\test_inverse.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\expressions\\tests\\test_kronecker.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\expressions\\tests\\test_matadd.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\expressions\\tests\\test_matexpr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\expressions\\tests\\test_matmul.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\expressions\\tests\\test_matpow.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\expressions\\tests\\test_permutation.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\expressions\\tests\\test_sets.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\expressions\\tests\\test_slice.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\expressions\\tests\\test_special.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\expressions\\tests\\test_trace.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\matrices\\expressions\\tests\\test_transpose.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\logic\\tests\\test_boolalg.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\logic\\tests\\test_dimacs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\logic\\tests\\test_inference.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\logic\\tests\\test_lra_theory.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\liealgebras\\tests\\test_cartan_matrix.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\liealgebras\\tests\\test_cartan_type.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\liealgebras\\tests\\test_dynkin_diagram.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\liealgebras\\tests\\test_root_system.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\liealgebras\\tests\\test_type_A.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\liealgebras\\tests\\test_type_B.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\liealgebras\\tests\\test_type_C.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\liealgebras\\tests\\test_type_D.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\liealgebras\\tests\\test_type_E.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\liealgebras\\tests\\test_type_F.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\liealgebras\\tests\\test_type_G.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\liealgebras\\tests\\test_weyl_group.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\interactive\\tests\\test_interactive.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\interactive\\tests\\test_ipython.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\integrals\\tests\\test_deltafunctions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\integrals\\tests\\test_failing_integrals.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\integrals\\tests\\test_heurisch.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\integrals\\tests\\test_integrals.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\integrals\\tests\\test_intpoly.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\integrals\\tests\\test_laplace.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\integrals\\tests\\test_lineintegrals.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\integrals\\tests\\test_manual.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\integrals\\tests\\test_meijerint.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\integrals\\tests\\test_prde.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\integrals\\tests\\test_quadrature.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\integrals\\tests\\test_rationaltools.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\integrals\\tests\\test_rde.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\integrals\\tests\\test_risch.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\integrals\\tests\\test_singularityfunctions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\integrals\\tests\\test_transforms.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\integrals\\tests\\test_trigonometry.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\holonomic\\tests\\test_holonomic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\holonomic\\tests\\test_recurrence.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\geometry\\tests\\test_curve.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\geometry\\tests\\test_ellipse.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\geometry\\tests\\test_entity.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\geometry\\tests\\test_geometrysets.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\geometry\\tests\\test_line.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\geometry\\tests\\test_parabola.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\geometry\\tests\\test_plane.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\geometry\\tests\\test_point.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\geometry\\tests\\test_polygon.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\geometry\\tests\\test_util.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\functions\\special\\tests\\test_bessel.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\functions\\special\\tests\\test_beta_functions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\functions\\special\\tests\\test_bsplines.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\functions\\special\\tests\\test_delta_functions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\functions\\special\\tests\\test_elliptic_integrals.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\functions\\special\\tests\\test_error_functions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\functions\\special\\tests\\test_gamma_functions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\functions\\special\\tests\\test_hyper.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\functions\\special\\tests\\test_mathieu.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\functions\\special\\tests\\test_singularity_functions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\functions\\special\\tests\\test_spec_polynomials.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\functions\\special\\tests\\test_spherical_harmonics.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\functions\\special\\tests\\test_tensor_functions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\functions\\special\\tests\\test_zeta_functions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\functions\\elementary\\tests\\test_complexes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\functions\\elementary\\tests\\test_exponential.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\functions\\elementary\\tests\\test_hyperbolic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\functions\\elementary\\tests\\test_integers.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\functions\\elementary\\tests\\test_interface.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\functions\\elementary\\tests\\test_miscellaneous.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\functions\\elementary\\tests\\test_piecewise.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\functions\\elementary\\tests\\test_trigonometric.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\functions\\combinatorial\\tests\\test_comb_factorials.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\functions\\combinatorial\\tests\\test_comb_numbers.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\external\\tests\\test_autowrap.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\external\\tests\\test_codegen.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\external\\tests\\test_gmpy.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\external\\tests\\test_importtools.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\external\\tests\\test_ntheory.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\external\\tests\\test_numpy.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\external\\tests\\test_pythonmpq.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\external\\tests\\test_scipy.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\discrete\\tests\\test_convolutions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\discrete\\tests\\test_recurrences.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\discrete\\tests\\test_transforms.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\diffgeom\\tests\\test_class_structure.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\diffgeom\\tests\\test_diffgeom.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\diffgeom\\tests\\test_function_diffgeom_book.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\diffgeom\\tests\\test_hyperbolic_space.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\crypto\\tests\\test_crypto.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_args.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_arit.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_assumptions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_basic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_cache.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_compatibility.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_complex.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_constructor_postprocessor.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_containers.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_count_ops.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_diff.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_equal.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_eval.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_evalf.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_expand.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_expr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_exprtools.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_facts.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_function.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_kind.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_logic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_match.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_multidimensional.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_noncommutative.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_numbers.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_operations.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_parameters.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_power.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_priority.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_random.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_relational.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_rules.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_singleton.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_sorting.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_subs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_symbol.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_sympify.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_traversal.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_truediv.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\core\\tests\\test_var.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\concrete\\tests\\test_delta.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\concrete\\tests\\test_gosper.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\concrete\\tests\\test_guess.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\concrete\\tests\\test_products.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\concrete\\tests\\test_sums_products.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\combinatorics\\tests\\test_coset_table.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\combinatorics\\tests\\test_fp_groups.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\combinatorics\\tests\\test_free_groups.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\combinatorics\\tests\\test_galois.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\combinatorics\\tests\\test_generators.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\combinatorics\\tests\\test_graycode.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\combinatorics\\tests\\test_group_constructs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\combinatorics\\tests\\test_group_numbers.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\combinatorics\\tests\\test_homomorphisms.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\combinatorics\\tests\\test_named_groups.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\combinatorics\\tests\\test_partitions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\combinatorics\\tests\\test_pc_groups.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\combinatorics\\tests\\test_permutations.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\combinatorics\\tests\\test_perm_groups.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\combinatorics\\tests\\test_polyhedron.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\combinatorics\\tests\\test_prufer.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\combinatorics\\tests\\test_rewriting.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\combinatorics\\tests\\test_schur_number.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\combinatorics\\tests\\test_subsets.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\combinatorics\\tests\\test_tensor_can.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\combinatorics\\tests\\test_testutil.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\combinatorics\\tests\\test_util.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\codegen\\tests\\test_abstract_nodes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\codegen\\tests\\test_algorithms.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\codegen\\tests\\test_applications.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\codegen\\tests\\test_approximations.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\codegen\\tests\\test_ast.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\codegen\\tests\\test_cfunctions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\codegen\\tests\\test_cnodes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\codegen\\tests\\test_cxxnodes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\codegen\\tests\\test_fnodes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\codegen\\tests\\test_matrix_nodes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\codegen\\tests\\test_numpy_nodes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\codegen\\tests\\test_pynodes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\codegen\\tests\\test_pyutils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\codegen\\tests\\test_rewriting.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\codegen\\tests\\test_scipy_nodes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\categories\\tests\\test_baseclasses.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\categories\\tests\\test_drawing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\calculus\\tests\\test_accumulationbounds.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\calculus\\tests\\test_euler.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\calculus\\tests\\test_finite_diff.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\calculus\\tests\\test_singularities.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\calculus\\tests\\test_util.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\assumptions\\tests\\test_assumptions_2.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\assumptions\\tests\\test_context.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\assumptions\\tests\\test_matrices.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\assumptions\\tests\\test_query.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\assumptions\\tests\\test_refine.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\assumptions\\tests\\test_rel_queries.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\assumptions\\tests\\test_satask.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\assumptions\\tests\\test_sathandlers.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\assumptions\\tests\\test_wrapper.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sympy\\algebras\\tests\\test_quaternion.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tests\\test_package.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tests\\test_x13.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\tests\\test_adfuller_lag.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\tests\\test_ar.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\tests\\test_arima_process.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\tests\\test_bds.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\tests\\test_deterministic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\tests\\test_exponential_smoothing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\tests\\test_seasonal.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\tests\\test_stattools.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\tests\\test_tsa_tools.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\tests\\test_x13.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\vector_ar\\tests\\test_coint.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\vector_ar\\tests\\test_svar.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\vector_ar\\tests\\test_var.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\vector_ar\\tests\\test_var_jmulti.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\vector_ar\\tests\\test_vecm.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\stl\\tests\\test_mstl.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\stl\\tests\\test_stl.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_cfa_simulation_smoothing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_cfa_tvpvar.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_chandrasekhar.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_collapsed.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_concentrated.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_conserve_memory.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_decompose.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_dynamic_factor.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_dynamic_factor_mq.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_dynamic_factor_mq_frbny_nowcast.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_dynamic_factor_mq_monte_carlo.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_exact_diffuse_filtering.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_exponential_smoothing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_fixed_params.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_forecasting.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_impulse_responses.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_initialization.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_kalman.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_mlemodel.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_models.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_multivariate_switch_univariate.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_news.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_options.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_pickle.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_prediction.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_representation.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_sarimax.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_save.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_simulate.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_simulation_smoothing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_smoothing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_structural.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_tools.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_univariate.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_var.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_varmax.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\statespace\\tests\\test_weights.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\regime_switching\\tests\\test_markov_autoregression.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\regime_switching\\tests\\test_markov_regression.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\regime_switching\\tests\\test_markov_switching.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\interp\\tests\\test_denton.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\innovations\\tests\\test_arma_innovations.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\innovations\\tests\\test_cython_arma_innovations_fast.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\holtwinters\\tests\\test_holtwinters.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\forecasting\\tests\\test_stl.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\forecasting\\tests\\test_theta.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\filters\\tests\\test_filters.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\base\\tests\\test_base.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\base\\tests\\test_datetools.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\base\\tests\\test_prediction.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\base\\tests\\test_tsa_indexes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\arima\\tests\\test_model.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\arima\\tests\\test_params.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\arima\\tests\\test_specification.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\arima\\tests\\test_tools.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\arima\\estimators\\tests\\test_burg.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\arima\\estimators\\tests\\test_durbin_levinson.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\arima\\estimators\\tests\\test_gls.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\arima\\estimators\\tests\\test_hannan_rissanen.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\arima\\estimators\\tests\\test_innovations.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\arima\\estimators\\tests\\test_statespace.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\arima\\estimators\\tests\\test_yule_walker.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tsa\\ardl\\tests\\test_ardl.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\treatment\\tests\\test_teffects.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tools\\tests\\test_catadd.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tools\\tests\\test_data.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tools\\tests\\test_decorators.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tools\\tests\\test_docstring.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tools\\tests\\test_eval_measures.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tools\\tests\\test_grouputils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tools\\tests\\test_linalg.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tools\\tests\\test_numdiff.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tools\\tests\\test_parallel.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tools\\tests\\test_rootfinding.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tools\\tests\\test_sequences.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tools\\tests\\test_testing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tools\\tests\\test_tools.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tools\\tests\\test_transform_model.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tools\\tests\\test_web.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\tools\\validation\\tests\\test_validation.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_anova.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_anova_rm.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_base.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_contingency_tables.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_contrast.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_correlation.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_corrpsd.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_deltacov.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_descriptivestats.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_diagnostic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_diagnostic_other.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_dist_dependant_measures.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_effectsize.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_gof.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_groups_sw.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_influence.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_inter_rater.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_knockoff.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_lilliefors.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_mediation.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_meta.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_moment_helpers.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_multi.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_multivariate.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_nonparametric.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_oaxaca.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_oneway.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_outliers_influence.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_pairwise.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_panel_robustcov.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_power.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_proportion.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_qsturng.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_rates_poisson.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_regularized_covariance.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_robust_compare.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_sandwich.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_statstools.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_tabledist.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_tost.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\tests\\test_weightstats.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\stats\\libqsturng\\tests\\test_qsturng.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\sandbox\\tests\\test_gam.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\sandbox\\tests\\test_pca.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\sandbox\\tests\\test_predict_functional.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\sandbox\\stats\\tests\\test_multicomp.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\sandbox\\stats\\tests\\test_runs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\sandbox\\regression\\tests\\test_gmm.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\sandbox\\regression\\tests\\test_gmm_poisson.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\sandbox\\panel\\tests\\test_random_panel.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\sandbox\\nonparametric\\tests\\test_kernel_extras.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\sandbox\\nonparametric\\tests\\test_smoothers.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\sandbox\\distributions\\tests\\test_extras.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\sandbox\\distributions\\tests\\test_gof_new.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\sandbox\\distributions\\tests\\test_multivariate.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\sandbox\\distributions\\tests\\test_norm_expan.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\sandbox\\distributions\\tests\\test_transf.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\robust\\tests\\test_mquantiles.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\robust\\tests\\test_norms.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\robust\\tests\\test_rlm.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\robust\\tests\\test_scale.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\regression\\tests\\test_cov.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\regression\\tests\\test_dimred.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\regression\\tests\\test_glsar_gretl.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\regression\\tests\\test_glsar_stata.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\regression\\tests\\test_lme.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\regression\\tests\\test_predict.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\regression\\tests\\test_processreg.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\regression\\tests\\test_quantile_regression.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\regression\\tests\\test_recursive_ls.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\regression\\tests\\test_regression.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\regression\\tests\\test_robustcov.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\regression\\tests\\test_rolling.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\regression\\tests\\test_theil.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\regression\\tests\\test_tools.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\othermod\\tests\\test_beta.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\nonparametric\\tests\\test_asymmetric.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\nonparametric\\tests\\test_bandwidths.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\nonparametric\\tests\\test_kde.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\nonparametric\\tests\\test_kernels.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\nonparametric\\tests\\test_kernel_density.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\nonparametric\\tests\\test_kernel_regression.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\nonparametric\\tests\\test_lowess.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\multivariate\\tests\\test_cancorr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\multivariate\\tests\\test_factor.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\multivariate\\tests\\test_manova.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\multivariate\\tests\\test_ml_factor.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\multivariate\\tests\\test_multivariate_ols.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\multivariate\\tests\\test_pca.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\multivariate\\factor_rotation\\tests\\test_rotation.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\miscmodels\\tests\\test_generic_mle.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\miscmodels\\tests\\test_ordinal_model.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\miscmodels\\tests\\test_poisson.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\miscmodels\\tests\\test_tmodel.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\iolib\\tests\\test_pickle.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\iolib\\tests\\test_summary.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\iolib\\tests\\test_summary2.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\iolib\\tests\\test_summary_old.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\iolib\\tests\\test_table.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\iolib\\tests\\test_table_econpy.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\imputation\\tests\\test_bayes_mi.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\imputation\\tests\\test_mice.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\imputation\\tests\\test_ros.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\graphics\\tests\\test_agreement.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\graphics\\tests\\test_boxplots.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\graphics\\tests\\test_correlation.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\graphics\\tests\\test_dotplot.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\graphics\\tests\\test_factorplots.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\graphics\\tests\\test_functional.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\graphics\\tests\\test_gofplots.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\graphics\\tests\\test_mosaicplot.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\graphics\\tests\\test_regressionplots.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\graphics\\tests\\test_tsaplots.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\genmod\\tests\\test_bayes_mixed_glm.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\genmod\\tests\\test_constrained.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\genmod\\tests\\test_gee.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\genmod\\tests\\test_gee_glm.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\genmod\\tests\\test_glm.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\genmod\\tests\\test_glm_weights.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\genmod\\tests\\test_qif.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\genmod\\tests\\test_score_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\genmod\\families\\tests\\test_family.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\genmod\\families\\tests\\test_link.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\gam\\tests\\test_gam.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\gam\\tests\\test_penalized.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\gam\\tests\\test_smooth_basis.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\formula\\tests\\test_formula.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\emplike\\tests\\test_aft.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\emplike\\tests\\test_anova.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\emplike\\tests\\test_descriptive.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\emplike\\tests\\test_origin.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\emplike\\tests\\test_regression.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\duration\\tests\\test_phreg.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\duration\\tests\\test_survfunc.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\distributions\\tests\\test_bernstein.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\distributions\\tests\\test_discrete.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\distributions\\tests\\test_ecdf.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\distributions\\tests\\test_edgeworth.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\distributions\\tests\\test_mixture.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\distributions\\tests\\test_tools.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\discrete\\tests\\test_conditional.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\discrete\\tests\\test_constrained.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\discrete\\tests\\test_count_model.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\discrete\\tests\\test_diagnostic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\discrete\\tests\\test_discrete.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\discrete\\tests\\test_margins.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\discrete\\tests\\test_predict.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\discrete\\tests\\test_sandwich_cov.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\discrete\\tests\\test_truncated_model.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\datasets\\tests\\test_data.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\datasets\\tests\\test_utils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\compat\\tests\\test_itercompat.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\compat\\tests\\test_pandas.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\compat\\tests\\test_scipy_compat.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\base\\tests\\test_data.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\base\\tests\\test_distributed_estimation.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\base\\tests\\test_generic_methods.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\base\\tests\\test_optimize.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\base\\tests\\test_penalized.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\base\\tests\\test_penalties.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\base\\tests\\test_predict.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\base\\tests\\test_screening.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\base\\tests\\test_shrink_pickle.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\base\\tests\\test_transform.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sqlalchemy\\testing\\suite\\test_cte.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sqlalchemy\\testing\\suite\\test_ddl.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sqlalchemy\\testing\\suite\\test_deprecations.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sqlalchemy\\testing\\suite\\test_dialect.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sqlalchemy\\testing\\suite\\test_insert.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sqlalchemy\\testing\\suite\\test_reflection.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sqlalchemy\\testing\\suite\\test_results.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sqlalchemy\\testing\\suite\\test_rowcount.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sqlalchemy\\testing\\suite\\test_select.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sqlalchemy\\testing\\suite\\test_sequence.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sqlalchemy\\testing\\suite\\test_types.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sqlalchemy\\testing\\suite\\test_unicode_ddl.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sqlalchemy\\testing\\suite\\test_update_delete.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\smmap\\test\\test_buf.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\smmap\\test\\test_mman.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\smmap\\test\\test_tutorial.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\smmap\\test\\test_util.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\tests\\test_base.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\tests\\test_build.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\tests\\test_calibration.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\tests\\test_check_build.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\tests\\test_common.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\tests\\test_config.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\tests\\test_discriminant_analysis.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\tests\\test_docstrings.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\tests\\test_docstring_parameters.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\tests\\test_dummy.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\tests\\test_init.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\tests\\test_isotonic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\tests\\test_kernel_approximation.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\tests\\test_kernel_ridge.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\tests\\test_metadata_routing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\tests\\test_metaestimators.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\tests\\test_metaestimators_metadata_routing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\tests\\test_min_dependencies_readme.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\tests\\test_multiclass.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\tests\\test_multioutput.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\tests\\test_naive_bayes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\tests\\test_pipeline.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\tests\\test_public_functions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\tests\\test_random_projection.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\_loss\\tests\\test_link.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\_loss\\tests\\test_loss.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_arpack.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_arrayfuncs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_array_api.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_bunch.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_chunking.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_class_weight.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_cython_blas.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_deprecation.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_encode.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_estimator_checks.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_estimator_html_repr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_extmath.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_fast_dict.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_fixes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_graph.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_indexing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_mask.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_metaestimators.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_missing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_mocking.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_multiclass.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_murmurhash.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_optimize.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_parallel.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_param_validation.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_plotting.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_pprint.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_random.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_response.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_seq_dataset.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_set_output.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_shortest_path.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_show_versions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_sparsefuncs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_stats.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_tags.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_testing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_typedefs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_unique.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_user_interface.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_utils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_validation.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\utils\\tests\\test_weight_vector.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\tree\\tests\\test_export.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\tree\\tests\\test_monotonic_tree.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\tree\\tests\\test_reingold_tilford.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\tree\\tests\\test_tree.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\svm\\tests\\test_bounds.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\svm\\tests\\test_sparse.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\svm\\tests\\test_svm.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\semi_supervised\\tests\\test_label_propagation.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\semi_supervised\\tests\\test_self_training.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\preprocessing\\tests\\test_common.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\preprocessing\\tests\\test_data.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\preprocessing\\tests\\test_discretization.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\preprocessing\\tests\\test_encoders.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\preprocessing\\tests\\test_function_transformer.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\preprocessing\\tests\\test_label.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\preprocessing\\tests\\test_polynomial.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\preprocessing\\tests\\test_target_encoder.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\neural_network\\tests\\test_base.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\neural_network\\tests\\test_mlp.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\neural_network\\tests\\test_rbm.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\neural_network\\tests\\test_stochastic_optimizers.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\neighbors\\tests\\test_ball_tree.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\neighbors\\tests\\test_graph.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\neighbors\\tests\\test_kde.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\neighbors\\tests\\test_kd_tree.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\neighbors\\tests\\test_lof.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\neighbors\\tests\\test_nca.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\neighbors\\tests\\test_nearest_centroid.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\neighbors\\tests\\test_neighbors.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\neighbors\\tests\\test_neighbors_pipeline.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\neighbors\\tests\\test_neighbors_tree.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\neighbors\\tests\\test_quad_tree.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\model_selection\\tests\\test_classification_threshold.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\model_selection\\tests\\test_plot.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\model_selection\\tests\\test_search.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\model_selection\\tests\\test_split.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\model_selection\\tests\\test_successive_halving.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\model_selection\\tests\\test_validation.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\mixture\\tests\\test_bayesian_mixture.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\mixture\\tests\\test_gaussian_mixture.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\mixture\\tests\\test_mixture.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\metrics\\tests\\test_classification.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\metrics\\tests\\test_common.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\metrics\\tests\\test_dist_metrics.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\metrics\\tests\\test_pairwise.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\metrics\\tests\\test_pairwise_distances_reduction.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\metrics\\tests\\test_ranking.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\metrics\\tests\\test_regression.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\metrics\\tests\\test_score_objects.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\metrics\\_plot\\tests\\test_common_curve_display.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\metrics\\_plot\\tests\\test_confusion_matrix_display.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\metrics\\_plot\\tests\\test_det_curve_display.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\metrics\\_plot\\tests\\test_precision_recall_display.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\metrics\\_plot\\tests\\test_predict_error_display.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\metrics\\_plot\\tests\\test_roc_curve_display.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\metrics\\cluster\\tests\\test_bicluster.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\metrics\\cluster\\tests\\test_common.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\metrics\\cluster\\tests\\test_supervised.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\metrics\\cluster\\tests\\test_unsupervised.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\manifold\\tests\\test_isomap.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\manifold\\tests\\test_locally_linear.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\manifold\\tests\\test_mds.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\manifold\\tests\\test_spectral_embedding.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\manifold\\tests\\test_t_sne.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_base.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_bayes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_common.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_coordinate_descent.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_huber.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_least_angle.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_linear_loss.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_logistic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_omp.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_passive_aggressive.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_perceptron.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_quantile.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_ransac.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_ridge.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_sag.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_sgd.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_sparse_coordinate_descent.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\linear_model\\tests\\test_theil_sen.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\linear_model\\_glm\\tests\\test_glm.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\inspection\\tests\\test_partial_dependence.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\inspection\\tests\\test_pd_utils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\inspection\\tests\\test_permutation_importance.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\inspection\\_plot\\tests\\test_boundary_decision_display.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\inspection\\_plot\\tests\\test_plot_partial_dependence.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\impute\\tests\\test_base.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\impute\\tests\\test_common.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\impute\\tests\\test_impute.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\impute\\tests\\test_knn.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\gaussian_process\\tests\\test_gpc.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\gaussian_process\\tests\\test_gpr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\gaussian_process\\tests\\test_kernels.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\frozen\\tests\\test_frozen.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\feature_selection\\tests\\test_base.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\feature_selection\\tests\\test_chi2.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\feature_selection\\tests\\test_feature_select.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\feature_selection\\tests\\test_from_model.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\feature_selection\\tests\\test_mutual_info.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\feature_selection\\tests\\test_rfe.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\feature_selection\\tests\\test_sequential.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\feature_selection\\tests\\test_variance_threshold.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\feature_extraction\\tests\\test_dict_vectorizer.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\feature_extraction\\tests\\test_feature_hasher.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\feature_extraction\\tests\\test_image.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\feature_extraction\\tests\\test_text.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\experimental\\tests\\test_enable_hist_gradient_boosting.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\experimental\\tests\\test_enable_iterative_imputer.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\experimental\\tests\\test_enable_successive_halving.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\ensemble\\tests\\test_bagging.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\ensemble\\tests\\test_base.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\ensemble\\tests\\test_common.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\ensemble\\tests\\test_forest.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\ensemble\\tests\\test_gradient_boosting.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\ensemble\\tests\\test_iforest.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\ensemble\\tests\\test_stacking.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\ensemble\\tests\\test_voting.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\ensemble\\tests\\test_weight_boosting.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\tests\\test_binning.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\tests\\test_bitset.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\tests\\test_compare_lightgbm.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\tests\\test_gradient_boosting.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\tests\\test_grower.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\tests\\test_histogram.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\tests\\test_monotonic_constraints.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\tests\\test_predictor.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\tests\\test_splitting.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\ensemble\\_hist_gradient_boosting\\tests\\test_warm_start.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\decomposition\\tests\\test_dict_learning.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\decomposition\\tests\\test_factor_analysis.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\decomposition\\tests\\test_fastica.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\decomposition\\tests\\test_incremental_pca.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\decomposition\\tests\\test_kernel_pca.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\decomposition\\tests\\test_nmf.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\decomposition\\tests\\test_online_lda.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\decomposition\\tests\\test_pca.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\decomposition\\tests\\test_sparse_pca.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\decomposition\\tests\\test_truncated_svd.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\datasets\\tests\\test_20news.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\datasets\\tests\\test_arff_parser.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\datasets\\tests\\test_base.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\datasets\\tests\\test_california_housing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\datasets\\tests\\test_common.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\datasets\\tests\\test_covtype.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\datasets\\tests\\test_kddcup99.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\datasets\\tests\\test_lfw.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\datasets\\tests\\test_olivetti_faces.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\datasets\\tests\\test_openml.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\datasets\\tests\\test_rcv1.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\datasets\\tests\\test_samples_generator.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\datasets\\tests\\test_svmlight_format.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\cross_decomposition\\tests\\test_pls.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\covariance\\tests\\test_covariance.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\covariance\\tests\\test_elliptic_envelope.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\covariance\\tests\\test_graphical_lasso.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\covariance\\tests\\test_robust_covariance.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\compose\\tests\\test_column_transformer.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\compose\\tests\\test_target.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\cluster\\tests\\test_affinity_propagation.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\cluster\\tests\\test_bicluster.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\cluster\\tests\\test_birch.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\cluster\\tests\\test_bisect_k_means.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\cluster\\tests\\test_dbscan.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\cluster\\tests\\test_feature_agglomeration.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\cluster\\tests\\test_hdbscan.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\cluster\\tests\\test_hierarchical.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\cluster\\tests\\test_k_means.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\cluster\\tests\\test_mean_shift.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\cluster\\tests\\test_optics.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\cluster\\tests\\test_spectral.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\cluster\\_hdbscan\\tests\\test_reachibility.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_archive_util.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_bdist_deprecations.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_bdist_egg.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_bdist_wheel.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_build.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_build_clib.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_build_ext.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_build_meta.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_build_py.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_config_discovery.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_core_metadata.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_depends.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_develop.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_dist.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_distutils_adoption.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_dist_info.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_editable_install.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_egg_info.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_extern.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_find_packages.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_find_py_modules.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_glob.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_install_scripts.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_logging.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_manifest.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_namespaces.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_scripts.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_sdist.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_setopt.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_setuptools.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_shutil_wrapper.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_unicode_utils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_virtualenv.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_warnings.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_wheel.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\test_windows_wrappers.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_archive_util.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_bdist.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_bdist_dumb.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_bdist_rpm.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_build.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_build_clib.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_build_ext.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_build_py.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_build_scripts.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_check.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_clean.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_cmd.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_config_cmd.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_core.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_dir_util.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_dist.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_extension.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_filelist.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_file_util.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_install.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_install_data.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_install_headers.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_install_lib.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_install_scripts.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_log.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_modified.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_sdist.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_spawn.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_sysconfig.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_text_file.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_util.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_version.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\tests\\test_versionpredicate.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\tests\\test_base.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\tests\\test_cygwin.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\tests\\test_mingw.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\tests\\test_msvc.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\_distutils\\compilers\\C\\tests\\test_unix.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\config\\test_apply_pyprojecttoml.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\config\\test_expand.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\config\\test_pyprojecttoml.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\config\\test_pyprojecttoml_dynamic_deps.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\config\\test_setupcfg.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\integration\\test_pbr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\setuptools\\tests\\integration\\test_pip_install_sdist.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\_lib\\tests\\test_array_api.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\_lib\\tests\\test_bunch.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\_lib\\tests\\test_ccallback.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\_lib\\tests\\test_config.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\_lib\\tests\\test_deprecation.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\_lib\\tests\\test_doccer.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\_lib\\tests\\test_import_cycles.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\_lib\\tests\\test_public_api.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\_lib\\tests\\test_scipy_version.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\_lib\\tests\\test_tmpdirs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\_lib\\tests\\test_warnings.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\_lib\\tests\\test__gcutils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\_lib\\tests\\test__pep440.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\_lib\\tests\\test__testutils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\_lib\\tests\\test__threadsafety.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\_lib\\tests\\test__util.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_axis_nan_policy.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_binned_statistic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_censored_data.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_contingency.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_continuous.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_continuous_basic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_continuous_fit_censored.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_correlation.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_crosstab.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_discrete_basic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_discrete_distns.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_distributions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_entropy.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_fast_gen_inversion.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_fit.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_hypotests.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_kdeoth.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_mgc.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_morestats.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_mstats_basic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_mstats_extras.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_multicomp.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_multivariate.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_odds_ratio.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_qmc.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_rank.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_relative_risk.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_resampling.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_sampling.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_sensitivity_analysis.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_stats.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_survival.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_tukeylambda_stats.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\tests\\test_variation.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_basic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_bdtr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_boost_ufuncs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_boxcox.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_cdflib.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_cdft_asymptotic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_cephes_intp_cast.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_cosine_distr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_cython_special.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_data.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_dd.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_digamma.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_ellip_harm.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_erfinv.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_exponential_integrals.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_extending.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_faddeeva.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_gamma.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_gammainc.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_hyp2f1.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_hypergeometric.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_iv_ratio.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_kolmogorov.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_lambertw.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_legendre.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_loggamma.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_logit.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_logsumexp.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_log_softmax.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_mpmath.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_nan_inputs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_ndtr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_ndtri_exp.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_orthogonal.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_orthogonal_eval.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_owens_t.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_pcf.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_pdtr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_powm1.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_precompute_expn_asy.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_precompute_gammainc.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_precompute_utils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_round.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_sf_error.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_sici.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_specfun.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_spence.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_spfun_stats.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_spherical_bessel.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_sph_harm.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_support_alternative_backends.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_trig.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_ufunc_signatures.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_wrightomega.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_wright_bessel.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_xsf_cuda.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\special\\tests\\test_zeta.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\spatial\\tests\\test_distance.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\spatial\\tests\\test_hausdorff.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\spatial\\tests\\test_kdtree.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\spatial\\tests\\test_qhull.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\spatial\\tests\\test_slerp.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\spatial\\tests\\test_spherical_voronoi.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\spatial\\tests\\test__plotutils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\spatial\\tests\\test__procrustes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\spatial\\transform\\tests\\test_rotation.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\spatial\\transform\\tests\\test_rotation_groups.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\spatial\\transform\\tests\\test_rotation_spline.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\tests\\test_arithmetic1d.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\tests\\test_array_api.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\tests\\test_base.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\tests\\test_common1d.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\tests\\test_construct.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\tests\\test_coo.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\tests\\test_csc.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\tests\\test_csr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\tests\\test_dok.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\tests\\test_extract.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\tests\\test_indexing1d.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\tests\\test_matrix_io.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\tests\\test_minmax1d.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\tests\\test_sparsetools.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\tests\\test_spfuncs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\tests\\test_sputils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\linalg\\tests\\test_expm_multiply.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\linalg\\tests\\test_interface.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\linalg\\tests\\test_matfuncs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\linalg\\tests\\test_norm.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\linalg\\tests\\test_onenormest.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\linalg\\tests\\test_propack.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\linalg\\tests\\test_pydata_sparse.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\linalg\\tests\\test_special_sparse_arrays.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\tests\\test_gcrotmk.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\tests\\test_iterative.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\tests\\test_lgmres.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\tests\\test_lsmr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\tests\\test_lsqr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\tests\\test_minres.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\linalg\\_isolve\\tests\\test_utils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\tests\\test_svds.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\lobpcg\\tests\\test_lobpcg.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\linalg\\_eigen\\arpack\\tests\\test_arpack.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\linalg\\_dsolve\\tests\\test_linsolve.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\csgraph\\tests\\test_connected_components.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\csgraph\\tests\\test_conversions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\csgraph\\tests\\test_flow.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\csgraph\\tests\\test_graph_laplacian.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\csgraph\\tests\\test_matching.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\csgraph\\tests\\test_pydata_sparse.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\csgraph\\tests\\test_reordering.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\csgraph\\tests\\test_shortest_path.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\csgraph\\tests\\test_spanning_tree.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\sparse\\csgraph\\tests\\test_traversal.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\signal\\tests\\test_array_tools.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\signal\\tests\\test_bsplines.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\signal\\tests\\test_cont2discrete.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\signal\\tests\\test_czt.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\signal\\tests\\test_dltisys.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\signal\\tests\\test_filter_design.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\signal\\tests\\test_fir_filter_design.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\signal\\tests\\test_ltisys.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\signal\\tests\\test_max_len_seq.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\signal\\tests\\test_peak_finding.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\signal\\tests\\test_result_type.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\signal\\tests\\test_savitzky_golay.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\signal\\tests\\test_short_time_fft.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\signal\\tests\\test_signaltools.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\signal\\tests\\test_spectral.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\signal\\tests\\test_splines.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\signal\\tests\\test_upfirdn.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\signal\\tests\\test_waveforms.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\signal\\tests\\test_wavelets.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\signal\\tests\\test_windows.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_bracket.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_chandrupatla.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_cobyla.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_cobyqa.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_constraints.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_constraint_conversion.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_cython_optimize.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_differentiable_functions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_direct.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_extending.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_hessian_update_strategy.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_isotonic_regression.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_lbfgsb_hessinv.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_lbfgsb_setulb.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_least_squares.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_linear_assignment.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_linesearch.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_linprog.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_lsq_common.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_lsq_linear.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_milp.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_minimize_constrained.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_minpack.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_nnls.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_nonlin.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_optimize.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_quadratic_assignment.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_regression.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_slsqp.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_tnc.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_trustregion.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_trustregion_exact.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_trustregion_krylov.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test_zeros.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test__basinhopping.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test__differential_evolution.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test__dual_annealing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test__linprog_clean_inputs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test__numdiff.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test__remove_redundancy.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test__root.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test__shgo.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\tests\\test__spectral.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\tests\\test_canonical_constraint.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\tests\\test_nested_minimize.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\tests\\test_projections.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\tests\\test_qp_subproblem.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\optimize\\_trustregion_constr\\tests\\test_report.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\odr\\tests\\test_odr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\ndimage\\tests\\test_c_api.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\ndimage\\tests\\test_datatypes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\ndimage\\tests\\test_filters.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\ndimage\\tests\\test_fourier.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\ndimage\\tests\\test_interpolation.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\ndimage\\tests\\test_measurements.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\ndimage\\tests\\test_morphology.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\ndimage\\tests\\test_ni_support.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\ndimage\\tests\\test_splines.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\linalg\\tests\\test_basic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\linalg\\tests\\test_blas.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\linalg\\tests\\test_cythonized_array_utils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\linalg\\tests\\test_cython_blas.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\linalg\\tests\\test_cython_lapack.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\linalg\\tests\\test_decomp.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\linalg\\tests\\test_decomp_cholesky.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\linalg\\tests\\test_decomp_cossin.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\linalg\\tests\\test_decomp_ldl.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\linalg\\tests\\test_decomp_lu.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\linalg\\tests\\test_decomp_polar.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\linalg\\tests\\test_decomp_update.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\linalg\\tests\\test_extending.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\linalg\\tests\\test_fblas.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\linalg\\tests\\test_interpolative.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\linalg\\tests\\test_lapack.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\linalg\\tests\\test_matfuncs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\linalg\\tests\\test_matmul_toeplitz.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\linalg\\tests\\test_procrustes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\linalg\\tests\\test_sketches.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\linalg\\tests\\test_solvers.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\linalg\\tests\\test_solve_toeplitz.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\linalg\\tests\\test_special_matrices.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\io\\tests\\test_fortran.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\io\\tests\\test_idl.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\io\\tests\\test_mmio.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\io\\tests\\test_netcdf.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\io\\tests\\test_paths.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\io\\tests\\test_wavfile.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\io\\_harwell_boeing\\tests\\test_fortran_format.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\io\\_harwell_boeing\\tests\\test_hb.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\io\\matlab\\tests\\test_byteordercodes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\io\\matlab\\tests\\test_mio.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\io\\matlab\\tests\\test_mio5_utils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\io\\matlab\\tests\\test_miobase.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\io\\matlab\\tests\\test_mio_funcs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\io\\matlab\\tests\\test_mio_utils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\io\\matlab\\tests\\test_pathological.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\io\\matlab\\tests\\test_streams.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\io\\arff\\tests\\test_arffread.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\interpolate\\tests\\test_bary_rational.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\interpolate\\tests\\test_bsplines.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\interpolate\\tests\\test_fitpack.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\interpolate\\tests\\test_fitpack2.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\interpolate\\tests\\test_gil.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\interpolate\\tests\\test_interpnd.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\interpolate\\tests\\test_interpolate.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\interpolate\\tests\\test_ndgriddata.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\interpolate\\tests\\test_pade.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\interpolate\\tests\\test_polyint.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\interpolate\\tests\\test_rbf.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\interpolate\\tests\\test_rbfinterp.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\interpolate\\tests\\test_rgi.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\integrate\\tests\\test_banded_ode_solvers.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\integrate\\tests\\test_bvp.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\integrate\\tests\\test_cubature.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\integrate\\tests\\test_integrate.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\integrate\\tests\\test_odeint_jac.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\integrate\\tests\\test_quadpack.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\integrate\\tests\\test_quadrature.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\integrate\\tests\\test_tanhsinh.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\integrate\\tests\\test__quad_vec.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\integrate\\_ivp\\tests\\test_ivp.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\integrate\\_ivp\\tests\\test_rk.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\fftpack\\tests\\test_basic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\fftpack\\tests\\test_helper.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\fftpack\\tests\\test_import.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\fftpack\\tests\\test_pseudo_diffs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\fftpack\\tests\\test_real_transforms.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\fft\\tests\\test_backend.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\fft\\tests\\test_basic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\fft\\tests\\test_fftlog.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\fft\\tests\\test_helper.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\fft\\tests\\test_multithreading.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\fft\\tests\\test_real_transforms.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\fft\\_pocketfft\\tests\\test_basic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\fft\\_pocketfft\\tests\\test_real_transforms.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\differentiate\\tests\\test_differentiate.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\datasets\\tests\\test_data.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\constants\\tests\\test_codata.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\constants\\tests\\test_constants.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\cluster\\tests\\test_disjoint_set.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\cluster\\tests\\test_hierarchy.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\cluster\\tests\\test_vq.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\referencing\\tests\\test_core.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\referencing\\tests\\test_exceptions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\referencing\\tests\\test_jsonschema.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\referencing\\tests\\test_referencing_suite.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\referencing\\tests\\test_retrieval.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_acero.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_adhoc_memory_leak.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_array.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_builder.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_cffi.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_compute.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_convert_builtin.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_cpp_internals.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_csv.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_cuda.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_cuda_numba_interop.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_cython.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_dataset.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_dataset_encryption.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_deprecations.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_device.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_dlpack.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_exec_plan.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_extension_type.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_feather.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_flight.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_flight_async.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_fs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_gandiva.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_gdb.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_io.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_ipc.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_json.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_jvm.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_memory.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_misc.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_orc.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_pandas.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_scalars.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_schema.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_sparse_tensor.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_strategies.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_substrait.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_table.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_tensor.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_types.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_udf.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_util.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\test_without_numpy.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\interchange\\test_conversion.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\interchange\\test_interchange_spec.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\parquet\\test_basic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\parquet\\test_compliant_nested_type.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\parquet\\test_dataset.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\parquet\\test_data_types.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\parquet\\test_datetime.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\parquet\\test_encryption.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\parquet\\test_metadata.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\parquet\\test_pandas.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\parquet\\test_parquet_file.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pyarrow\\tests\\parquet\\test_parquet_writer.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\psutil\\tests\\test_aix.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\psutil\\tests\\test_bsd.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\psutil\\tests\\test_connections.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\psutil\\tests\\test_contracts.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\psutil\\tests\\test_linux.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\psutil\\tests\\test_memleaks.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\psutil\\tests\\test_misc.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\psutil\\tests\\test_osx.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\psutil\\tests\\test_posix.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\psutil\\tests\\test_process.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\psutil\\tests\\test_process_all.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\psutil\\tests\\test_scripts.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\psutil\\tests\\test_sunos.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\psutil\\tests\\test_system.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\psutil\\tests\\test_testutils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\psutil\\tests\\test_unicode.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\psutil\\tests\\test_windows.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\plotly\\matplotlylib\\mplexporter\\tests\\test_basic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\plotly\\matplotlylib\\mplexporter\\tests\\test_utils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pkg_resources\\tests\\test_find_distributions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pkg_resources\\tests\\test_integration_zope_interface.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pkg_resources\\tests\\test_markers.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pkg_resources\\tests\\test_pkg_resources.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pkg_resources\\tests\\test_resources.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pkg_resources\\tests\\test_working_set.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\test_aggregation.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\test_algos.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\test_common.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\test_downstream.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\test_errors.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\test_expressions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\test_flags.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\test_multilevel.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\test_nanops.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\test_optional_dependency.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\test_register_accessor.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\test_sorting.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\test_take.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\api\\test_api.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\api\\test_types.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\apply\\test_frame_apply.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\apply\\test_frame_apply_relabeling.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\apply\\test_frame_transform.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\apply\\test_invalid_arg.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\apply\\test_numba.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\apply\\test_series_apply.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\apply\\test_series_apply_relabeling.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\apply\\test_series_transform.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\apply\\test_str.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arithmetic\\test_array_ops.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arithmetic\\test_categorical.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arithmetic\\test_datetime64.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arithmetic\\test_interval.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arithmetic\\test_numeric.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arithmetic\\test_object.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arithmetic\\test_period.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arithmetic\\test_timedelta64.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\test_array.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\test_datetimelike.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\test_datetimes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\test_ndarray_backed.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\test_period.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\test_timedeltas.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\base\\test_constructors.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\base\\test_conversion.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\base\\test_fillna.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\base\\test_misc.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\base\\test_transpose.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\base\\test_unique.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\base\\test_value_counts.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\computation\\test_compat.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\computation\\test_eval.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\config\\test_config.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\config\\test_localization.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\construction\\test_extract_array.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_array.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_astype.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_chained_assignment_deprecation.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_clip.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_constructors.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_core_functionalities.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_functions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_indexing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_internals.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_interp_fillna.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_methods.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_replace.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_setitem.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\copy_view\\test_util.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\dtypes\\test_common.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\dtypes\\test_concat.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\dtypes\\test_dtypes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\dtypes\\test_generic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\dtypes\\test_inference.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\dtypes\\test_missing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\extension\\test_arrow.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\extension\\test_categorical.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\extension\\test_common.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\extension\\test_datetime.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\extension\\test_extension.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\extension\\test_interval.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\extension\\test_masked.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\extension\\test_numpy.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\extension\\test_period.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\extension\\test_sparse.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\extension\\test_string.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\test_alter_axes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\test_api.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\test_arithmetic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\test_arrow_interface.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\test_block_internals.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\test_constructors.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\test_cumulative.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\test_iteration.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\test_logical_ops.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\test_nonunique_indexes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\test_npfuncs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\test_query_eval.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\test_reductions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\test_repr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\test_stack_unstack.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\test_subclass.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\test_ufunc.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\test_unary.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\test_validate.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\generic\\test_duplicate_labels.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\generic\\test_finalize.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\generic\\test_frame.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\generic\\test_generic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\generic\\test_label_or_level_utils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\generic\\test_series.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\generic\\test_to_xarray.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\test_all_methods.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\test_api.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\test_apply.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\test_apply_mutate.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\test_bin_groupby.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\test_categorical.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\test_counting.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\test_cumulative.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\test_filters.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\test_groupby.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\test_groupby_dropna.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\test_groupby_subclass.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\test_grouping.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\test_indexing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\test_index_as_string.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\test_libgroupby.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\test_missing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\test_numba.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\test_numeric_only.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\test_pipe.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\test_raises.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\test_reductions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\test_timegrouper.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\test_any_index.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\test_base.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\test_common.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\test_datetimelike.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\test_engines.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\test_frozen.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\test_indexing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\test_index_new.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\test_numpy_compat.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\test_old_base.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\test_setops.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\test_subclass.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexing\\test_at.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexing\\test_categorical.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexing\\test_chaining_and_caching.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexing\\test_check_indexer.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexing\\test_coercion.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexing\\test_datetime.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexing\\test_floats.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexing\\test_iat.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexing\\test_iloc.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexing\\test_indexers.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexing\\test_indexing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexing\\test_loc.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexing\\test_na_indexing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexing\\test_partial.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexing\\test_scalar.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\interchange\\test_impl.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\interchange\\test_spec_conformance.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\interchange\\test_utils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\internals\\test_api.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\internals\\test_internals.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\internals\\test_managers.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\test_clipboard.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\test_common.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\test_compression.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\test_feather.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\test_fsspec.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\test_gbq.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\test_gcs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\test_html.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\test_http_headers.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\test_orc.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\test_parquet.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\test_pickle.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\test_s3.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\test_spss.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\test_sql.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\test_stata.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\libs\\test_hashtable.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\libs\\test_join.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\libs\\test_lib.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\libs\\test_libalgos.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\plotting\\test_backend.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\plotting\\test_boxplot_method.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\plotting\\test_common.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\plotting\\test_converter.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\plotting\\test_datetimelike.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\plotting\\test_groupby.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\plotting\\test_hist_method.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\plotting\\test_misc.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\plotting\\test_series.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\plotting\\test_style.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\reductions\\test_reductions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\reductions\\test_stat_reductions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\resample\\test_base.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\resample\\test_datetime_index.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\resample\\test_period_index.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\resample\\test_resampler_grouper.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\resample\\test_resample_api.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\resample\\test_timedelta.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\resample\\test_time_grouper.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\reshape\\test_crosstab.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\reshape\\test_cut.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\reshape\\test_from_dummies.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\reshape\\test_get_dummies.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\reshape\\test_melt.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\reshape\\test_pivot.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\reshape\\test_pivot_multilevel.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\reshape\\test_qcut.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\reshape\\test_union_categoricals.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\reshape\\test_util.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\scalar\\test_nat.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\scalar\\test_na_scalar.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\test_api.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\test_arithmetic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\test_constructors.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\test_cumulative.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\test_formats.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\test_iteration.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\test_logical_ops.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\test_missing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\test_npfuncs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\test_reductions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\test_subclass.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\test_ufunc.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\test_unary.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\test_validate.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\strings\\test_api.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\strings\\test_case_justify.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\strings\\test_cat.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\strings\\test_extract.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\strings\\test_find_replace.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\strings\\test_get_dummies.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\strings\\test_split_partition.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\strings\\test_strings.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\strings\\test_string_array.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tools\\test_to_datetime.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tools\\test_to_numeric.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tools\\test_to_time.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tools\\test_to_timedelta.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_api.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_array_to_datetime.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_ccalendar.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_conversion.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_fields.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_libfrequencies.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_liboffsets.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_npy_units.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_np_datetime.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_parse_iso8601.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_parsing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_period.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_resolution.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_strptime.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_timedeltas.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_timezones.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_to_offset.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tslibs\\test_tzconversion.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_almost_equal.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_attr_equal.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_categorical_equal.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_extension_array_equal.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_frame_equal.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_index_equal.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_interval_array_equal.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_numpy_array_equal.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_produces_warning.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\util\\test_assert_series_equal.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\util\\test_deprecate.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\util\\test_deprecate_kwarg.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\util\\test_deprecate_nonkeyword_arguments.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\util\\test_doc.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\util\\test_hashing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\util\\test_numba.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\util\\test_rewrite_warning.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\util\\test_shares_memory.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\util\\test_show_versions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\util\\test_util.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\util\\test_validate_args.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\util\\test_validate_args_and_kwargs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\util\\test_validate_inclusive.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\util\\test_validate_kwargs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\window\\test_api.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\window\\test_apply.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\window\\test_base_indexer.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\window\\test_cython_aggregations.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\window\\test_dtypes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\window\\test_ewm.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\window\\test_expanding.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\window\\test_groupby.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\window\\test_numba.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\window\\test_online.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\window\\test_pairwise.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\window\\test_rolling.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\window\\test_rolling_functions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\window\\test_rolling_quantile.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\window\\test_rolling_skew_kurt.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\window\\test_timeseries_window.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\window\\test_win_type.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\window\\moments\\test_moments_consistency_ewm.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\window\\moments\\test_moments_consistency_expanding.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\window\\moments\\test_moments_consistency_rolling.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tseries\\frequencies\\test_frequencies.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tseries\\frequencies\\test_freq_code.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tseries\\frequencies\\test_inference.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tseries\\holiday\\test_calendar.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tseries\\holiday\\test_federal.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tseries\\holiday\\test_holiday.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tseries\\holiday\\test_observance.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_business_day.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_business_hour.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_business_month.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_business_quarter.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_business_year.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_common.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_custom_business_day.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_custom_business_hour.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_custom_business_month.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_dst.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_easter.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_fiscal.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_index.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_month.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_offsets.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_offsets_properties.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_quarter.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_ticks.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_week.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\tseries\\offsets\\test_year.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\accessors\\test_cat_accessor.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\accessors\\test_dt_accessor.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\accessors\\test_list_accessor.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\accessors\\test_sparse_accessor.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\accessors\\test_struct_accessor.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\accessors\\test_str_accessor.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_datetime.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_delitem.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_get.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_getitem.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_indexing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_mask.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_setitem.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_set_value.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_take.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_where.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\indexing\\test_xs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_add_prefix_suffix.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_align.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_argsort.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_asof.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_astype.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_autocorr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_between.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_case_when.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_clip.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_combine.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_combine_first.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_compare.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_convert_dtypes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_copy.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_count.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_cov_corr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_describe.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_diff.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_drop.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_dropna.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_drop_duplicates.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_dtypes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_duplicated.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_equals.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_explode.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_fillna.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_get_numeric_data.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_head_tail.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_infer_objects.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_info.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_interpolate.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_isin.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_isna.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_is_monotonic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_is_unique.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_item.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_map.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_matmul.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_nlargest.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_nunique.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_pct_change.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_pop.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_quantile.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_rank.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_reindex.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_reindex_like.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_rename.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_rename_axis.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_repeat.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_replace.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_reset_index.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_round.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_searchsorted.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_set_name.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_size.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_sort_index.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_sort_values.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_tolist.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_to_csv.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_to_dict.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_to_frame.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_to_numpy.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_truncate.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_tz_localize.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_unique.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_unstack.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_update.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_values.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_value_counts.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\series\\methods\\test_view.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\scalar\\interval\\test_arithmetic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\scalar\\interval\\test_constructors.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\scalar\\interval\\test_contains.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\scalar\\interval\\test_formats.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\scalar\\interval\\test_interval.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\scalar\\interval\\test_overlaps.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\scalar\\period\\test_arithmetic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\scalar\\period\\test_asfreq.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\scalar\\period\\test_period.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\test_arithmetic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\test_constructors.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\test_formats.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\test_timedelta.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\test_arithmetic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\test_comparisons.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\test_constructors.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\test_formats.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\test_timestamp.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\test_timezones.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_as_unit.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_normalize.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_replace.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_round.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_timestamp_method.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_to_julian_date.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_to_pydatetime.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_tz_convert.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\scalar\\timestamp\\methods\\test_tz_localize.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\methods\\test_as_unit.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\scalar\\timedelta\\methods\\test_round.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_append.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_append_common.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_categorical.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_concat.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_dataframe.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_datetimes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_empty.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_index.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_invalid.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_series.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\reshape\\concat\\test_sort.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_join.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_merge.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_merge_asof.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_merge_cross.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_merge_index_as_string.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_merge_ordered.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\reshape\\merge\\test_multi.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\plotting\\frame\\test_frame.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\plotting\\frame\\test_frame_color.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\plotting\\frame\\test_frame_groupby.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\plotting\\frame\\test_frame_legend.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\plotting\\frame\\test_frame_subplots.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\plotting\\frame\\test_hist_box_by.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\excel\\test_odf.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\excel\\test_odswriter.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\excel\\test_openpyxl.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\excel\\test_readers.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\excel\\test_style.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\excel\\test_writers.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\excel\\test_xlrd.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\excel\\test_xlsxwriter.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_console.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_css.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_eng_formatting.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_format.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_ipython_compat.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_printing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_to_csv.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_to_excel.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_to_html.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_to_latex.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_to_markdown.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\formats\\test_to_string.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\json\\test_compression.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\json\\test_deprecated_kwargs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\json\\test_json_table_schema.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\json\\test_json_table_schema_ext_dtype.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\json\\test_normalize.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\json\\test_pandas.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\json\\test_readlines.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\json\\test_ujson.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_comment.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_compression.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_concatenate_chunks.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_converters.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_c_parser_only.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_dialect.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_encoding.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_header.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_index_col.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_mangle_dupes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_multi_thread.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_na_values.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_network.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_parse_dates.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_python_parser_only.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_quoting.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_read_fwf.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_skiprows.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_textreader.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_unsupported.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\test_upcast.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_append.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_categorical.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_compat.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_complex.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_errors.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_file_handling.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_keys.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_put.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_pytables_missing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_read.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_retain_attributes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_round_trip.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_select.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_store.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_subclass.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_timezones.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\pytables\\test_time_series.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\sas\\test_byteswap.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\sas\\test_sas.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\sas\\test_sas7bdat.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\sas\\test_xport.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\xml\\test_to_xml.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\xml\\test_xml.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\xml\\test_xml_dtypes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_chunksize.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_common_basic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_data_list.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_decimal.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_file_buffer_url.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_float.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_index.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_inf.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_ints.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_iterator.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_read_errors.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\common\\test_verbose.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\dtypes\\test_categorical.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\dtypes\\test_dtypes_basic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\dtypes\\test_empty.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\usecols\\test_parse_dates.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\usecols\\test_strings.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\parser\\usecols\\test_usecols_basic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_bar.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_exceptions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_format.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_highlight.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_html.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_matplotlib.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_non_unique.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_style.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_tooltip.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_to_latex.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\io\\formats\\style\\test_to_string.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexing\\interval\\test_interval.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexing\\interval\\test_interval_new.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_chaining_and_caching.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_datetime.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_getitem.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_iloc.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_indexing_slow.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_loc.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_multiindex.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_partial.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_setitem.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_slice.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexing\\multiindex\\test_sorted.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_constructors.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_formats.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_indexing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_pickle.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_reshape.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_setops.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\base_class\\test_where.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_append.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_astype.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_category.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_constructors.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_equals.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_fillna.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_formats.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_indexing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_map.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_reindex.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\categorical\\test_setops.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_drop_duplicates.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_equals.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_indexing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_is_monotonic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_nat.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_sort_values.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimelike_\\test_value_counts.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_arithmetic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_constructors.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_datetime.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_date_range.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_formats.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_freq_attr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_indexing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_iter.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_join.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_npfuncs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_ops.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_partial_slicing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_pickle.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_reindex.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_scalar_compat.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_setops.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\test_timezones.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_astype.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_constructors.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_equals.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_formats.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_indexing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_interval.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_interval_range.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_interval_tree.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_join.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_pickle.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\interval\\test_setops.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_analytics.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_astype.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_compat.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_constructors.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_conversion.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_copy.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_drop.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_duplicates.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_equivalence.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_formats.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_get_level_values.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_get_set.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_indexing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_integrity.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_isin.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_join.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_lexsort.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_missing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_monotonic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_names.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_partial_indexing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_pickle.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_reindex.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_reshape.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_setops.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_sorting.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\multi\\test_take.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\numeric\\test_astype.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\numeric\\test_indexing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\numeric\\test_join.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\numeric\\test_numeric.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\numeric\\test_setops.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\object\\test_astype.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\object\\test_indexing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_constructors.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_formats.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_freq_attr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_indexing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_join.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_monotonic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_partial_slicing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_period.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_period_range.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_pickle.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_resolution.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_scalar_compat.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_searchsorted.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_setops.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\test_tools.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\ranges\\test_constructors.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\ranges\\test_indexing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\ranges\\test_join.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\ranges\\test_range.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\ranges\\test_setops.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_arithmetic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_constructors.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_delete.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_formats.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_freq_attr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_indexing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_join.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_ops.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_pickle.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_scalar_compat.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_searchsorted.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_setops.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_timedelta.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\test_timedelta_range.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\test_astype.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\test_factorize.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\test_fillna.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\test_insert.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\test_repeat.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\timedeltas\\methods\\test_shift.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_asfreq.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_astype.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_factorize.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_fillna.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_insert.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_is_full.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_repeat.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_shift.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\period\\methods\\test_to_timestamp.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_asof.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_astype.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_delete.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_factorize.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_fillna.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_insert.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_isocalendar.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_map.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_normalize.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_repeat.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_resolution.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_round.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_shift.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_snap.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_to_frame.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_to_julian_date.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_to_period.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_to_pydatetime.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_to_series.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_tz_convert.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_tz_localize.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexes\\datetimes\\methods\\test_unique.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\aggregate\\test_aggregate.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\aggregate\\test_cython.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\aggregate\\test_numba.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\aggregate\\test_other.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_corrwith.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_describe.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_groupby_shift_diff.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_is_monotonic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_nlargest_nsmallest.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_nth.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_quantile.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_rank.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_sample.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_size.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_skew.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\methods\\test_value_counts.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\transform\\test_numba.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\groupby\\transform\\test_transform.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\constructors\\test_from_dict.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\constructors\\test_from_records.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_coercion.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_delitem.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_get.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_getitem.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_get_value.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_indexing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_insert.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_mask.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_setitem.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_set_value.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_take.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_where.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\indexing\\test_xs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_add_prefix_suffix.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_align.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_asfreq.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_asof.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_assign.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_astype.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_at_time.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_between_time.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_clip.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_combine.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_combine_first.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_compare.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_convert_dtypes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_copy.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_count.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_cov_corr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_describe.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_diff.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_dot.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_drop.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_droplevel.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_dropna.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_drop_duplicates.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_dtypes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_duplicated.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_equals.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_explode.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_fillna.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_filter.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_first_and_last.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_first_valid_index.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_get_numeric_data.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_head_tail.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_infer_objects.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_info.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_interpolate.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_isetitem.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_isin.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_is_homogeneous_dtype.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_iterrows.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_join.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_map.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_matmul.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_nlargest.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_pct_change.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_pipe.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_pop.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_quantile.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_rank.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_reindex.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_reindex_like.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_rename.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_rename_axis.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_reorder_levels.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_replace.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_reset_index.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_round.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_sample.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_select_dtypes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_set_axis.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_set_index.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_shift.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_size.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_sort_index.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_sort_values.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_swapaxes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_swaplevel.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_csv.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_dict.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_dict_of_blocks.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_numpy.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_period.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_records.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_to_timestamp.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_transpose.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_truncate.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_tz_convert.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_tz_localize.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_update.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_values.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\frame\\methods\\test_value_counts.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\extension\\array_with_attr\\test_array_with_attr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\extension\\decimal\\test_decimal.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\extension\\json\\test_json.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\extension\\list\\test_list.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_can_hold_element.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_construct_from_scalar.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_construct_ndarray.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_construct_object_arr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_dict_compat.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_downcast.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_find_common_type.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_infer_datetimelike.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_infer_dtype.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_maybe_box_native.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\dtypes\\cast\\test_promote.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\copy_view\\index\\test_datetimeindex.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\copy_view\\index\\test_index.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\copy_view\\index\\test_periodindex.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\copy_view\\index\\test_timedeltaindex.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_arithmetic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_astype.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_comparison.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_construction.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_function.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_indexing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_logical.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_ops.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_reduction.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\boolean\\test_repr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_algos.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_analytics.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_api.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_astype.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_constructors.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_dtypes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_indexing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_map.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_missing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_operators.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_replace.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_repr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_sorting.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_subclass.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_take.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\categorical\\test_warnings.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\datetimes\\test_constructors.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\datetimes\\test_cumulative.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\datetimes\\test_reductions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_arithmetic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_astype.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_comparison.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_concat.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_construction.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_contains.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_function.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_repr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\floating\\test_to_numpy.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_arithmetic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_comparison.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_concat.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_construction.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_dtypes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_function.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_indexing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_reduction.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\integer\\test_repr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\interval\\test_astype.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\interval\\test_formats.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\interval\\test_interval.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\interval\\test_interval_pyarrow.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\interval\\test_overlaps.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\masked\\test_arithmetic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\masked\\test_arrow_compat.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\masked\\test_function.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\masked\\test_indexing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\numpy_\\test_indexing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\numpy_\\test_numpy.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\period\\test_arrow_compat.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\period\\test_astype.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\period\\test_constructors.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\period\\test_reductions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_accessor.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_arithmetics.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_array.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_astype.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_combine_concat.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_constructors.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_dtype.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_indexing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_libsparse.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_reductions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\sparse\\test_unary.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\string_\\test_string.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\string_\\test_string_arrow.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\timedeltas\\test_constructors.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\timedeltas\\test_cumulative.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\arrays\\timedeltas\\test_reductions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\tests\\test_configtool.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\tests\\test_ctypeslib.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\tests\\test_lazyloading.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\tests\\test_matlib.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\tests\\test_numpy_config.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\tests\\test_numpy_version.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\tests\\test_public_api.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\tests\\test_reloading.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\tests\\test_scripts.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\tests\\test_warnings.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\tests\\test__all__.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_pyinstaller\\tests\\test_pyinstaller.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_abc.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_api.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_argparse.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_arraymethod.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_arrayobject.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_arrayprint.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_array_api_info.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_array_coercion.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_array_interface.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_casting_floatingpoint_errors.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_casting_unittests.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_conversion_utils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_cpu_dispatcher.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_cpu_features.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_custom_dtypes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_cython.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_datetime.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_defchararray.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_deprecations.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_dlpack.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_dtype.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_einsum.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_errstate.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_extint128.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_function_base.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_getlimits.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_half.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_hashtable.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_indexerrors.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_indexing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_item_selection.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_limited_api.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_longdouble.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_machar.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_memmap.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_mem_overlap.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_mem_policy.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_multiarray.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_multithreading.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_nditer.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_nep50_promotions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_numeric.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_numerictypes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_overrides.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_print.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_protocols.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_records.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_regression.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_scalarbuffer.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_scalarinherit.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_scalarmath.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_scalarprint.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_scalar_ctors.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_scalar_methods.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_shape_base.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_simd.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_simd_module.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_stringdtype.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_strings.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_ufunc.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_umath.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_umath_accuracy.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_umath_complex.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test_unicode.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\_core\\tests\\test__exceptions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\typing\\tests\\test_isfile.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\typing\\tests\\test_runtime.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\typing\\tests\\test_typing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\testing\\tests\\test_utils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\random\\tests\\test_direct.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\random\\tests\\test_extending.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\random\\tests\\test_generator_mt19937.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\random\\tests\\test_generator_mt19937_regressions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\random\\tests\\test_random.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\random\\tests\\test_randomstate.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\random\\tests\\test_randomstate_regression.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\random\\tests\\test_regression.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\random\\tests\\test_seed_sequence.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\random\\tests\\test_smoke.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\polynomial\\tests\\test_chebyshev.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\polynomial\\tests\\test_classes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\polynomial\\tests\\test_hermite.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\polynomial\\tests\\test_hermite_e.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\polynomial\\tests\\test_laguerre.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\polynomial\\tests\\test_legendre.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\polynomial\\tests\\test_polynomial.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\polynomial\\tests\\test_polyutils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\polynomial\\tests\\test_printing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\polynomial\\tests\\test_symbol.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\matrixlib\\tests\\test_defmatrix.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\matrixlib\\tests\\test_interaction.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\matrixlib\\tests\\test_masked_matrix.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\matrixlib\\tests\\test_matrix_linalg.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\matrixlib\\tests\\test_multiarray.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\matrixlib\\tests\\test_numeric.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\matrixlib\\tests\\test_regression.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\ma\\tests\\test_arrayobject.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\ma\\tests\\test_core.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\ma\\tests\\test_deprecations.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\ma\\tests\\test_extras.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\ma\\tests\\test_mrecords.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\ma\\tests\\test_old_ma.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\ma\\tests\\test_regression.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\ma\\tests\\test_subclassing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\linalg\\tests\\test_deprecations.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\linalg\\tests\\test_linalg.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\linalg\\tests\\test_regression.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\lib\\tests\\test_arraypad.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\lib\\tests\\test_arraysetops.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\lib\\tests\\test_arrayterator.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\lib\\tests\\test_array_utils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\lib\\tests\\test_format.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\lib\\tests\\test_function_base.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\lib\\tests\\test_histograms.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\lib\\tests\\test_index_tricks.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\lib\\tests\\test_io.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\lib\\tests\\test_loadtxt.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\lib\\tests\\test_mixins.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\lib\\tests\\test_nanfunctions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\lib\\tests\\test_packbits.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\lib\\tests\\test_polynomial.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\lib\\tests\\test_recfunctions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\lib\\tests\\test_regression.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\lib\\tests\\test_shape_base.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\lib\\tests\\test_stride_tricks.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\lib\\tests\\test_twodim_base.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\lib\\tests\\test_type_check.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\lib\\tests\\test_ufunclike.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\lib\\tests\\test_utils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\lib\\tests\\test__datasource.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\lib\\tests\\test__iotools.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\lib\\tests\\test__version.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\fft\\tests\\test_helper.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\fft\\tests\\test_pocketfft.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\f2py\\tests\\test_abstract_interface.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\f2py\\tests\\test_array_from_pyobj.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\f2py\\tests\\test_assumed_shape.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\f2py\\tests\\test_block_docstring.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\f2py\\tests\\test_callback.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\f2py\\tests\\test_character.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\f2py\\tests\\test_common.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\f2py\\tests\\test_crackfortran.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\f2py\\tests\\test_data.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\f2py\\tests\\test_docs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\f2py\\tests\\test_f2cmap.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\f2py\\tests\\test_f2py2e.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\f2py\\tests\\test_isoc.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\f2py\\tests\\test_kind.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\f2py\\tests\\test_mixed.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\f2py\\tests\\test_modules.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\f2py\\tests\\test_parameter.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\f2py\\tests\\test_pyf_src.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\f2py\\tests\\test_quoted_character.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\f2py\\tests\\test_regression.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\f2py\\tests\\test_return_character.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\f2py\\tests\\test_return_complex.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\f2py\\tests\\test_return_integer.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\f2py\\tests\\test_return_logical.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\f2py\\tests\\test_return_real.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\f2py\\tests\\test_routines.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\f2py\\tests\\test_semicolon_split.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\f2py\\tests\\test_size.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\f2py\\tests\\test_string.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\f2py\\tests\\test_symbolic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\numpy\\f2py\\tests\\test_value_attrspec.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\tests\\test_all_random_functions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\tests\\test_convert.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\tests\\test_convert_numpy.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\tests\\test_convert_pandas.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\tests\\test_convert_scipy.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\tests\\test_exceptions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\tests\\test_import.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\tests\\test_lazy_imports.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\tests\\test_relabel.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\utils\\tests\\test_backends.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\utils\\tests\\test_config.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\utils\\tests\\test_decorators.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\utils\\tests\\test_heaps.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\utils\\tests\\test_mapped_queue.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\utils\\tests\\test_misc.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\utils\\tests\\test_random_sequence.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\utils\\tests\\test_rcm.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\utils\\tests\\test_unionfind.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\utils\\tests\\test__init.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\readwrite\\tests\\test_adjlist.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\readwrite\\tests\\test_edgelist.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\readwrite\\tests\\test_gexf.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\readwrite\\tests\\test_gml.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\readwrite\\tests\\test_graph6.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\readwrite\\tests\\test_graphml.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\readwrite\\tests\\test_leda.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\readwrite\\tests\\test_p2g.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\readwrite\\tests\\test_pajek.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\readwrite\\tests\\test_sparse6.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\readwrite\\tests\\test_text.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\readwrite\\json_graph\\tests\\test_adjacency.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\readwrite\\json_graph\\tests\\test_cytoscape.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\readwrite\\json_graph\\tests\\test_node_link.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\readwrite\\json_graph\\tests\\test_tree.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\linalg\\tests\\test_algebraic_connectivity.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\linalg\\tests\\test_attrmatrix.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\linalg\\tests\\test_bethehessian.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\linalg\\tests\\test_graphmatrix.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\linalg\\tests\\test_laplacian.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\linalg\\tests\\test_modularity.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\linalg\\tests\\test_spectrum.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\generators\\tests\\test_atlas.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\generators\\tests\\test_classic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\generators\\tests\\test_cographs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\generators\\tests\\test_community.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\generators\\tests\\test_degree_seq.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\generators\\tests\\test_directed.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\generators\\tests\\test_duplication.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\generators\\tests\\test_ego.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\generators\\tests\\test_expanders.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\generators\\tests\\test_geometric.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\generators\\tests\\test_harary_graph.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\generators\\tests\\test_internet_as_graphs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\generators\\tests\\test_intersection.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\generators\\tests\\test_interval_graph.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\generators\\tests\\test_joint_degree_seq.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\generators\\tests\\test_lattice.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\generators\\tests\\test_line.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\generators\\tests\\test_mycielski.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\generators\\tests\\test_nonisomorphic_trees.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\generators\\tests\\test_random_clustered.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\generators\\tests\\test_random_graphs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\generators\\tests\\test_small.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\generators\\tests\\test_spectral_graph_forge.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\generators\\tests\\test_stochastic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\generators\\tests\\test_sudoku.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\generators\\tests\\test_time_series.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\generators\\tests\\test_trees.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\generators\\tests\\test_triads.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\drawing\\tests\\test_agraph.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\drawing\\tests\\test_latex.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\drawing\\tests\\test_layout.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\drawing\\tests\\test_pydot.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\drawing\\tests\\test_pylab.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\classes\\tests\\test_coreviews.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\classes\\tests\\test_digraph.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\classes\\tests\\test_digraph_historical.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\classes\\tests\\test_filters.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\classes\\tests\\test_function.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\classes\\tests\\test_graph.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\classes\\tests\\test_graphviews.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\classes\\tests\\test_graph_historical.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\classes\\tests\\test_multidigraph.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\classes\\tests\\test_multigraph.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\classes\\tests\\test_reportviews.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\classes\\tests\\test_special.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\classes\\tests\\test_subgraphviews.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_asteroidal.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_boundary.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_bridges.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_broadcasting.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_chains.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_chordal.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_clique.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_cluster.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_communicability.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_core.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_covering.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_cuts.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_cycles.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_dag.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_distance_measures.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_distance_regular.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_dominance.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_dominating.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_d_separation.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_efficiency.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_euler.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_graphical.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_graph_hashing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_hierarchy.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_hybrid.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_isolate.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_link_prediction.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_lowest_common_ancestors.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_matching.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_max_weight_clique.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_mis.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_moral.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_node_classification.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_non_randomness.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_planarity.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_planar_drawing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_polynomials.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_reciprocity.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_regular.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_richclub.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_similarity.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_simple_paths.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_smallworld.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_smetric.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_sparsifiers.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_structuralholes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_summarization.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_swap.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_threshold.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_time_dependent.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_tournament.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_triads.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_vitality.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_voronoi.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_walks.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tests\\test_wiener.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tree\\tests\\test_branchings.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tree\\tests\\test_coding.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tree\\tests\\test_decomposition.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tree\\tests\\test_mst.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tree\\tests\\test_operations.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\tree\\tests\\test_recognition.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\traversal\\tests\\test_beamsearch.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\traversal\\tests\\test_bfs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\traversal\\tests\\test_dfs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\traversal\\tests\\test_edgebfs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\traversal\\tests\\test_edgedfs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\shortest_paths\\tests\\test_astar.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\shortest_paths\\tests\\test_dense.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\shortest_paths\\tests\\test_dense_numpy.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\shortest_paths\\tests\\test_generic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\shortest_paths\\tests\\test_unweighted.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\shortest_paths\\tests\\test_weighted.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\operators\\tests\\test_all.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\operators\\tests\\test_binary.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\operators\\tests\\test_product.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\operators\\tests\\test_unary.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\minors\\tests\\test_contraction.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\link_analysis\\tests\\test_hits.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\link_analysis\\tests\\test_pagerank.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\isomorphism\\tests\\test_ismags.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\isomorphism\\tests\\test_isomorphism.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\isomorphism\\tests\\test_isomorphvf2.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\isomorphism\\tests\\test_match_helpers.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\isomorphism\\tests\\test_temporalisomorphvf2.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\isomorphism\\tests\\test_tree_isomorphism.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\isomorphism\\tests\\test_vf2pp.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\isomorphism\\tests\\test_vf2pp_helpers.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\isomorphism\\tests\\test_vf2userfunc.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\flow\\tests\\test_gomory_hu.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\flow\\tests\\test_maxflow.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\flow\\tests\\test_maxflow_large_graph.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\flow\\tests\\test_mincost.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\flow\\tests\\test_networksimplex.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\connectivity\\tests\\test_connectivity.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\connectivity\\tests\\test_cuts.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\connectivity\\tests\\test_disjoint_paths.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\connectivity\\tests\\test_edge_augmentation.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\connectivity\\tests\\test_edge_kcomponents.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\connectivity\\tests\\test_kcomponents.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\connectivity\\tests\\test_kcutsets.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\connectivity\\tests\\test_stoer_wagner.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\components\\tests\\test_attracting.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\components\\tests\\test_biconnected.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\components\\tests\\test_connected.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\components\\tests\\test_semiconnected.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\components\\tests\\test_strongly_connected.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\components\\tests\\test_weakly_connected.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\community\\tests\\test_asyn_fluid.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\community\\tests\\test_centrality.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\community\\tests\\test_divisive.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\community\\tests\\test_kclique.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\community\\tests\\test_kernighan_lin.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\community\\tests\\test_label_propagation.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\community\\tests\\test_louvain.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\community\\tests\\test_lukes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\community\\tests\\test_modularity_max.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\community\\tests\\test_quality.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\community\\tests\\test_utils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\coloring\\tests\\test_coloring.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\centrality\\tests\\test_betweenness_centrality.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\centrality\\tests\\test_betweenness_centrality_subset.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\centrality\\tests\\test_closeness_centrality.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\centrality\\tests\\test_current_flow_betweenness_centrality.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\centrality\\tests\\test_current_flow_betweenness_centrality_subset.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\centrality\\tests\\test_current_flow_closeness.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\centrality\\tests\\test_degree_centrality.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\centrality\\tests\\test_dispersion.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\centrality\\tests\\test_eigenvector_centrality.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\centrality\\tests\\test_group.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\centrality\\tests\\test_harmonic_centrality.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\centrality\\tests\\test_katz_centrality.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\centrality\\tests\\test_laplacian_centrality.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\centrality\\tests\\test_load_centrality.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\centrality\\tests\\test_percolation_centrality.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\centrality\\tests\\test_reaching.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\centrality\\tests\\test_second_order_centrality.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\centrality\\tests\\test_subgraph.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\centrality\\tests\\test_trophic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\centrality\\tests\\test_voterank.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\bipartite\\tests\\test_basic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\bipartite\\tests\\test_centrality.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\bipartite\\tests\\test_cluster.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\bipartite\\tests\\test_covering.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\bipartite\\tests\\test_edgelist.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\bipartite\\tests\\test_extendability.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\bipartite\\tests\\test_generators.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\bipartite\\tests\\test_matching.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\bipartite\\tests\\test_matrix.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\bipartite\\tests\\test_project.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\bipartite\\tests\\test_redundancy.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\bipartite\\tests\\test_spectral_bipartivity.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\assortativity\\tests\\test_connectivity.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\assortativity\\tests\\test_correlation.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\assortativity\\tests\\test_mixing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\assortativity\\tests\\test_neighbor_degree.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\assortativity\\tests\\test_pairs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\approximation\\tests\\test_approx_clust_coeff.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\approximation\\tests\\test_clique.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\approximation\\tests\\test_connectivity.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\approximation\\tests\\test_distance_measures.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\approximation\\tests\\test_dominating_set.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\approximation\\tests\\test_kcomponents.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\approximation\\tests\\test_matching.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\approximation\\tests\\test_maxcut.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\approximation\\tests\\test_ramsey.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\approximation\\tests\\test_steinertree.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\approximation\\tests\\test_traveling_salesman.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\approximation\\tests\\test_treewidth.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\approximation\\tests\\test_vertex_cover.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpmath\\tests\\test_basic_ops.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpmath\\tests\\test_bitwise.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpmath\\tests\\test_calculus.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpmath\\tests\\test_compatibility.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpmath\\tests\\test_convert.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpmath\\tests\\test_diff.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpmath\\tests\\test_division.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpmath\\tests\\test_eigen.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpmath\\tests\\test_eigen_symmetric.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpmath\\tests\\test_elliptic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpmath\\tests\\test_fp.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpmath\\tests\\test_functions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpmath\\tests\\test_functions2.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpmath\\tests\\test_gammazeta.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpmath\\tests\\test_hp.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpmath\\tests\\test_identify.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpmath\\tests\\test_interval.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpmath\\tests\\test_levin.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpmath\\tests\\test_linalg.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpmath\\tests\\test_matrices.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpmath\\tests\\test_mpmath.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpmath\\tests\\test_ode.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpmath\\tests\\test_pickle.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpmath\\tests\\test_power.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpmath\\tests\\test_quad.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpmath\\tests\\test_rootfinding.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpmath\\tests\\test_special.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpmath\\tests\\test_str.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpmath\\tests\\test_summation.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpmath\\tests\\test_trig.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpmath\\tests\\test_visualization.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\tests\\test_art3d.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\tests\\test_axes3d.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpl_toolkits\\mplot3d\\tests\\test_legend3d.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpl_toolkits\\axisartist\\tests\\test_angle_helper.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpl_toolkits\\axisartist\\tests\\test_axislines.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpl_toolkits\\axisartist\\tests\\test_axis_artist.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpl_toolkits\\axisartist\\tests\\test_floating_axes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpl_toolkits\\axisartist\\tests\\test_grid_finder.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpl_toolkits\\axisartist\\tests\\test_grid_helper_curvelinear.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\mpl_toolkits\\axes_grid1\\tests\\test_axes_grid1.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_afm.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_agg.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_agg_filter.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_animation.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_api.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_arrow_patches.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_artist.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_axes.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_axis.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_backends_interactive.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_backend_bases.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_backend_cairo.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_backend_gtk3.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_backend_inline.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_backend_macosx.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_backend_nbagg.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_backend_pdf.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_backend_pgf.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_backend_ps.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_backend_qt.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_backend_registry.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_backend_svg.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_backend_template.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_backend_tk.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_backend_tools.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_backend_webagg.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_basic.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_bbox_tight.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_bezier.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_category.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_cbook.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_collections.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_colorbar.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_colors.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_compare_images.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_constrainedlayout.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_container.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_contour.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_cycles.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_dates.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_datetime.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_determinism.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_doc.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_dviread.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_figure.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_fontconfig_pattern.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_font_manager.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_ft2font.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_getattr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_gridspec.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_image.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_legend.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_lines.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_marker.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_mathtext.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_matplotlib.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_mlab.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_multivariate_colormaps.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_offsetbox.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_patches.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_path.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_patheffects.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_pickle.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_png.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_polar.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_preprocess_data.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_pyplot.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_quiver.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_rcparams.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_sankey.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_scale.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_simplification.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_skew.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_sphinxext.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_spines.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_streamplot.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_style.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_subplots.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_table.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_testing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_texmanager.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_text.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_textpath.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_ticker.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_tightlayout.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_transforms.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_triangulation.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_type1font.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_units.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_usetex.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\matplotlib\\tests\\test_widgets.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\jsonschema_specifications\\tests\\test_jsonschema_specifications.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\jsonschema\\tests\\test_cli.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\jsonschema\\tests\\test_deprecations.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\jsonschema\\tests\\test_exceptions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\jsonschema\\tests\\test_format.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\jsonschema\\tests\\test_jsonschema_test_suite.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\jsonschema\\tests\\test_types.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\jsonschema\\tests\\test_utils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\jsonschema\\tests\\test_validators.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\joblib\\test\\test_backports.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\joblib\\test\\test_cloudpickle_wrapper.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\joblib\\test\\test_config.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\joblib\\test\\test_dask.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\joblib\\test\\test_disk.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\joblib\\test\\test_func_inspect.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\joblib\\test\\test_func_inspect_special_encoding.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\joblib\\test\\test_hashing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\joblib\\test\\test_init.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\joblib\\test\\test_logger.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\joblib\\test\\test_memmapping.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\joblib\\test\\test_memory.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\joblib\\test\\test_memory_async.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\joblib\\test\\test_missing_multiprocessing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\joblib\\test\\test_module.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\joblib\\test\\test_numpy_pickle.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\joblib\\test\\test_numpy_pickle_compat.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\joblib\\test\\test_numpy_pickle_utils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\joblib\\test\\test_parallel.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\joblib\\test\\test_store_backends.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\joblib\\test\\test_testing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\joblib\\test\\test_utils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\greenlet\\tests\\test_contextvars.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\greenlet\\tests\\test_cpp.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\greenlet\\tests\\test_extension_interface.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\greenlet\\tests\\test_gc.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\greenlet\\tests\\test_generator.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\greenlet\\tests\\test_generator_nested.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\greenlet\\tests\\test_greenlet.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\greenlet\\tests\\test_greenlet_trash.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\greenlet\\tests\\test_leaks.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\greenlet\\tests\\test_stack_saved.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\greenlet\\tests\\test_throw.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\greenlet\\tests\\test_tracing.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\greenlet\\tests\\test_version.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\greenlet\\tests\\test_weakref.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\gitdb\\test\\test_base.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\gitdb\\test\\test_example.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\gitdb\\test\\test_pack.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\gitdb\\test\\test_stream.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\gitdb\\test\\test_util.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\bs4\\tests\\test_builder.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\bs4\\tests\\test_builder_registry.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\bs4\\tests\\test_css.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\bs4\\tests\\test_dammit.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\bs4\\tests\\test_element.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\bs4\\tests\\test_filter.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\bs4\\tests\\test_formatter.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\bs4\\tests\\test_fuzz.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\bs4\\tests\\test_html5lib.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\bs4\\tests\\test_htmlparser.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\bs4\\tests\\test_lxml.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\bs4\\tests\\test_navigablestring.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\bs4\\tests\\test_pageelement.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\bs4\\tests\\test_soup.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\bs4\\tests\\test_tag.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\bs4\\tests\\test_tree.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\arch\\tests\\test_compat.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\arch\\tests\\test_data.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\arch\\tests\\test_examples.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\arch\\tests\\test_tester.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\arch\\tests\\bootstrap\\test_block_length.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\arch\\tests\\bootstrap\\test_bootstrap.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\arch\\tests\\bootstrap\\test_multiple_comparison.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\arch\\tests\\covariance\\test_covariance.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\arch\\tests\\unitroot\\test_dynamic_ols.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\arch\\tests\\unitroot\\test_engle_granger.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\arch\\tests\\unitroot\\test_fmols_ccr.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\arch\\tests\\unitroot\\test_phillips_ouliaris.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\arch\\tests\\unitroot\\test_unitroot.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\arch\\tests\\univariate\\test_arch_in_mean.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\arch\\tests\\univariate\\test_base.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\arch\\tests\\univariate\\test_distribution.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\arch\\tests\\univariate\\test_forecast.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\arch\\tests\\univariate\\test_mean.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\arch\\tests\\univariate\\test_moment.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\arch\\tests\\univariate\\test_recursions.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\arch\\tests\\univariate\\test_rescale.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\arch\\tests\\univariate\\test_variance_forecasting.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\arch\\tests\\univariate\\test_volatility.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\arch\\tests\\utility\\test_array.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\arch\\tests\\utility\\test_cov.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\arch\\tests\\utility\\test_io.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\arch\\tests\\utility\\test_timeseries.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\arch\\tests\\utility\\test_utility.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\alembic\\testing\\suite\\test_autogen_comments.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\alembic\\testing\\suite\\test_autogen_computed.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\alembic\\testing\\suite\\test_autogen_diffs.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\alembic\\testing\\suite\\test_autogen_fks.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\alembic\\testing\\suite\\test_autogen_identity.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\alembic\\testing\\suite\\test_environment.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\alembic\\testing\\suite\\test_op.py", "C:\\cursor\\量化\\quant_project\\api_test.py", "C:\\cursor\\量化\\quant_project\\comprehensive_end_to_end_test.py", "C:\\cursor\\量化\\quant_project\\tests\\comprehensive_ui_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\~orch\\utils\\benchmark\\examples\\spectral_ops_fuzz_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\~orch\\testing\\_internal\\distributed\\ddp_under_dist_autograd_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\~orch\\testing\\_internal\\distributed\\distributed_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\~orch\\testing\\_internal\\distributed\\rpc\\dist_autograd_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\~orch\\testing\\_internal\\distributed\\rpc\\dist_optimizer_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\~orch\\testing\\_internal\\distributed\\rpc\\faulty_agent_rpc_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\~orch\\testing\\_internal\\distributed\\rpc\\rpc_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\~orch\\testing\\_internal\\distributed\\rpc\\examples\\parameter_server_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\~orch\\testing\\_internal\\distributed\\rpc\\examples\\reinforcement_learning_rpc_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\~orch\\testing\\_internal\\distributed\\rpc\\jit\\dist_autograd_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\~orch\\testing\\_internal\\distributed\\rpc\\jit\\rpc_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\~orch\\testing\\_internal\\distributed\\nn\\api\\remote_module_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\asyncio_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\auth_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\autoreload_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\circlerefs_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\concurrent_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\curl_httpclient_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\escape_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\gen_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\http1connection_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\httpclient_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\httpserver_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\httputil_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\import_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\ioloop_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\iostream_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\locale_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\locks_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\log_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\netutil_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\options_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\process_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\queues_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\routing_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\simple_httpclient_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\tcpclient_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\tcpserver_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\template_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\testing_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\twisted_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\util_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\websocket_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\web_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\tornado\\test\\wsgi_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\torch\\utils\\benchmark\\examples\\spectral_ops_fuzz_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\torch\\testing\\_internal\\distributed\\ddp_under_dist_autograd_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\torch\\testing\\_internal\\distributed\\distributed_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\torch\\testing\\_internal\\distributed\\rpc\\dist_autograd_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\torch\\testing\\_internal\\distributed\\rpc\\dist_optimizer_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\torch\\testing\\_internal\\distributed\\rpc\\faulty_agent_rpc_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\torch\\testing\\_internal\\distributed\\rpc\\rpc_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\torch\\testing\\_internal\\distributed\\rpc\\examples\\parameter_server_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\torch\\testing\\_internal\\distributed\\rpc\\examples\\reinforcement_learning_rpc_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\torch\\testing\\_internal\\distributed\\rpc\\jit\\dist_autograd_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\torch\\testing\\_internal\\distributed\\rpc\\jit\\rpc_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\torch\\testing\\_internal\\distributed\\nn\\api\\remote_module_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\streamlit\\testing\\v1\\app_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\genmod\\tests\\test_score_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\_bws_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\scipy\\stats\\_page_trend_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\networkx\\algorithms\\assortativity\\tests\\base_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\colorama\\tests\\ansitowin32_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\colorama\\tests\\ansi_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\colorama\\tests\\initialise_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\colorama\\tests\\isatty_test.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\colorama\\tests\\winterm_test.py", "C:\\cursor\\量化\\quant_project\\debug_app.py", "C:\\cursor\\量化\\quant_project\\debug_main.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\~orch\\_dynamo\\debug_utils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\~orch\\_prims\\debug_prims.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\~orch\\_inductor\\codegen\\debug_utils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\~orch\\distributed\\elastic\\timer\\debug_info_logging.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\torch\\_dynamo\\debug_utils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\torch\\_prims\\debug_prims.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\torch\\_inductor\\codegen\\debug_utils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\torch\\distributed\\elastic\\timer\\debug_info_logging.py", "C:\\cursor\\量化\\quant_project\\check_imports.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\~orch\\testing\\_internal\\check_kernel_launches.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\torch\\testing\\_internal\\check_kernel_launches.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\sandbox\\distributions\\tests\\check_moments.py", "C:\\cursor\\量化\\quant_project\\verify_factor_mining.py"], "redundant_modules": [["C:\\cursor\\量化\\quant_project\\core_logic\\optimized_data_handler.py", "C:\\cursor\\量化\\quant_project\\core_logic\\data_handler.py"], ["C:\\cursor\\量化\\quant_project\\core_logic\\optimized_feature_engineering.py", "C:\\cursor\\量化\\quant_project\\core_logic\\feature_engineer.py"], ["C:\\cursor\\量化\\quant_project\\core_logic\\enhanced_drl_agent.py", "C:\\cursor\\量化\\quant_project\\core_logic\\drl_agent.py"], ["C:\\cursor\\量化\\quant_project\\core_logic\\enhanced_feature_engineer.py", "C:\\cursor\\量化\\quant_project\\core_logic\\feature_engineer.py"], ["C:\\cursor\\量化\\quant_project\\core_logic\\enhanced_trading_environment.py", "C:\\cursor\\量化\\quant_project\\core_logic\\trading_environment.py"], ["C:\\cursor\\量化\\quant_project\\core_logic\\feature_engineering_adapter.py", "C:\\cursor\\量化\\quant_project\\core_logic\\feature_engineer.py"], ["C:\\cursor\\量化\\quant_project\\core_logic\\optimized_data_handler_adapter.py", "C:\\cursor\\量化\\quant_project\\core_logic\\data_handler.py"]], "unused_files": ["C:\\cursor\\量化\\quant_project\\api_test.py", "C:\\cursor\\量化\\quant_project\\debug_app.py", "C:\\cursor\\量化\\quant_project\\debug_main.py", "C:\\cursor\\量化\\quant_project\\setup_test_env.py", "C:\\cursor\\量化\\quant_project\\simple_run.py", "C:\\cursor\\量化\\quant_project\\start_system.py", "C:\\cursor\\量化\\quant_project\\run_project.py", "C:\\cursor\\量化\\quant_project\\run_tests.py", "C:\\cursor\\量化\\quant_project\\run_all_tests.py", "C:\\cursor\\量化\\quant_project\\install_gpu_support.py", "C:\\cursor\\量化\\quant_project\\fix_asyncio_torch.py"]}, "cleanup_recommendations": [{"category": "重复文件清理", "action": "remove_duplicates", "files": [["C:\\cursor\\量化\\quant_project\\main_app.py", "C:\\cursor\\量化\\quant_project\\fixed_main_app.py"], ["C:\\cursor\\量化\\quant_project\\main_app.py", "C:\\cursor\\量化\\quant_project\\main_app_with_factor_mining.py"]], "description": "删除重复的文件，保留最新或最完整的版本"}, {"category": "临时测试文件清理", "action": "remove_temp_tests", "files": ["C:\\cursor\\量化\\quant_project\\test_backtesting_bug.py", "C:\\cursor\\量化\\quant_project\\test_comprehensive_fixes.py", "C:\\cursor\\量化\\quant_project\\test_data_acquisition_bug.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\sklearn\\tests\\test_check_build.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\pandas\\tests\\indexing\\test_check_indexer.py", "C:\\cursor\\量化\\quant_project\\debug_app.py", "C:\\cursor\\量化\\quant_project\\debug_main.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\~orch\\_dynamo\\debug_utils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\~orch\\_prims\\debug_prims.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\~orch\\_inductor\\codegen\\debug_utils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\~orch\\distributed\\elastic\\timer\\debug_info_logging.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\torch\\_dynamo\\debug_utils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\torch\\_prims\\debug_prims.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\torch\\_inductor\\codegen\\debug_utils.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\torch\\distributed\\elastic\\timer\\debug_info_logging.py", "C:\\cursor\\量化\\quant_project\\check_imports.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\~orch\\testing\\_internal\\check_kernel_launches.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\torch\\testing\\_internal\\check_kernel_launches.py", "C:\\cursor\\量化\\quant_project\\test_env\\Lib\\site-packages\\statsmodels\\sandbox\\distributions\\tests\\check_moments.py", "C:\\cursor\\量化\\quant_project\\verify_factor_mining.py"], "description": "删除调试过程中创建的临时测试文件"}, {"category": "冗余模块整合", "action": "consolidate_modules", "files": [["C:\\cursor\\量化\\quant_project\\core_logic\\optimized_data_handler.py", "C:\\cursor\\量化\\quant_project\\core_logic\\data_handler.py"], ["C:\\cursor\\量化\\quant_project\\core_logic\\optimized_feature_engineering.py", "C:\\cursor\\量化\\quant_project\\core_logic\\feature_engineer.py"], ["C:\\cursor\\量化\\quant_project\\core_logic\\enhanced_drl_agent.py", "C:\\cursor\\量化\\quant_project\\core_logic\\drl_agent.py"], ["C:\\cursor\\量化\\quant_project\\core_logic\\enhanced_feature_engineer.py", "C:\\cursor\\量化\\quant_project\\core_logic\\feature_engineer.py"], ["C:\\cursor\\量化\\quant_project\\core_logic\\enhanced_trading_environment.py", "C:\\cursor\\量化\\quant_project\\core_logic\\trading_environment.py"], ["C:\\cursor\\量化\\quant_project\\core_logic\\feature_engineering_adapter.py", "C:\\cursor\\量化\\quant_project\\core_logic\\feature_engineer.py"], ["C:\\cursor\\量化\\quant_project\\core_logic\\optimized_data_handler_adapter.py", "C:\\cursor\\量化\\quant_project\\core_logic\\data_handler.py"]], "description": "保留最优版本，删除冗余模块"}, {"category": "未使用文件清理", "action": "remove_unused", "files": ["C:\\cursor\\量化\\quant_project\\api_test.py", "C:\\cursor\\量化\\quant_project\\debug_app.py", "C:\\cursor\\量化\\quant_project\\debug_main.py", "C:\\cursor\\量化\\quant_project\\setup_test_env.py", "C:\\cursor\\量化\\quant_project\\simple_run.py", "C:\\cursor\\量化\\quant_project\\start_system.py", "C:\\cursor\\量化\\quant_project\\run_project.py", "C:\\cursor\\量化\\quant_project\\run_tests.py", "C:\\cursor\\量化\\quant_project\\run_all_tests.py", "C:\\cursor\\量化\\quant_project\\install_gpu_support.py", "C:\\cursor\\量化\\quant_project\\fix_asyncio_torch.py"], "description": "删除不再使用的脚本和工具文件"}, {"category": "缓存和日志清理", "action": "clean_cache", "files": ["C:\\cursor\\量化\\quant_project\\data_cache", "C:\\cursor\\量化\\quant_project\\logs", "C:\\cursor\\量化\\quant_project\\__pycache__"], "description": "清理缓存文件和旧日志（保留最近的）"}]}