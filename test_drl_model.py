#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
DRL模型训练与评估测试脚本
测试模型训练稳定性、模型保存与加载、算法对比
"""

import os
import sys
import pandas as pd
import numpy as np
import logging
import traceback
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import gymnasium as gym
import random
import torch

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'logs/drl_model_test_{datetime.now().strftime("%Y-%m-%d")}.log')
    ]
)

# 确保logs目录存在
os.makedirs('logs', exist_ok=True)

# 添加项目路径到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入相关模块
from quant_trading.data.data_handler import DataHandler
from quant_trading.features.feature_engineer import FeatureEngineer
from quant_trading.trading.trading_environment import TradingEnvironment
from quant_trading.agents.drl_agent import DRLAgent, MetricsCallback
from quant_trading.utils.common import load_config, detect_gpu

def test_model_training_stability():
    """测试模型训练稳定性"""
    print("\n===== 测试模型训练稳定性 =====")

    # 检查GPU
    gpu_info = detect_gpu()
    if gpu_info['available']:
        print(f"使用GPU: {gpu_info['devices']}")
    else:
        print("未检测到GPU，将使用CPU进行训练")

    # 获取测试数据
    handler = DataHandler(cache_dir='data_cache')
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=180)).strftime('%Y-%m-%d')
    stock = 'sh000001'

    try:
        # 获取原始数据
        data = handler.get_stock_data(stock, start_date, end_date)

        if data is None or data.empty:
            print("获取测试数据失败，无法进行模型训练测试")
            return

        # 加载配置
        env_config = load_config("quant_project/configs/env_config.yaml")
        agent_config = load_config("quant_project/configs/drl_agent_config.yaml")

        # 生成特征
        feature_engineer = FeatureEngineer(env_config.get('feature_config', {}))
        processed_data = feature_engineer.generate_features(data)

        # 数据分割
        split_config = env_config.get('data_split', {'train_ratio': 0.7, 'val_ratio': 0.15, 'test_ratio': 0.15})
        train_size = int(len(processed_data) * split_config['train_ratio'])
        val_size = int(len(processed_data) * split_config['val_ratio'])

        train_data = processed_data.iloc[:train_size]
        val_data = processed_data.iloc[train_size:train_size+val_size]
        test_data = processed_data.iloc[train_size+val_size:]

        print(f"数据分割: 训练集={len(train_data)}, 验证集={len(val_data)}, 测试集={len(test_data)}")

        # 创建环境配置
        env_params = {
            'df_processed_data': train_data,
            'initial_capital': env_config.get('initial_capital', 100000),
            'commission_rate': env_config.get('commission_rate', 0.0003),
            'min_hold_days': env_config.get('min_hold_days', 3),
            'allow_short': env_config.get('allow_short', False),
            'max_position': env_config.get('max_position', 1.0),
            'window_size': min(20, len(train_data) - 1),  # 确保窗口大小不超过数据长度
            'reward_config': env_config.get('reward_config', {})
        }

        # 使用不同的随机种子进行多次训练
        seeds = [42, 123, 456]
        total_timesteps = 5000  # 使用较小的步数进行测试

        rewards_history = []

        for seed in seeds:
            print(f"\n使用随机种子 {seed} 进行训练...")

            # 设置随机种子
            random.seed(seed)
            np.random.seed(seed)
            torch.manual_seed(seed)

            # 创建DRL智能体
            agent_config['algorithm'] = 'PPO'  # 使用PPO算法
            drl_agent = DRLAgent(env_params, agent_config)

            # 创建回调
            metrics_callback = MetricsCallback()

            # 训练模型
            print(f"开始训练: 算法=PPO, 步数={total_timesteps}")
            training_stats = drl_agent.train(
                total_timesteps=total_timesteps,
                callback_list=[metrics_callback],
                progress_bar=True
            )

            # 收集奖励历史
            if hasattr(metrics_callback, 'rewards') and metrics_callback.rewards:
                rewards_history.append(metrics_callback.rewards)

                # 计算平均奖励
                avg_reward = sum(metrics_callback.rewards) / max(1, len(metrics_callback.rewards))
                print(f"平均奖励: {avg_reward:.4f}")

                # 计算奖励标准差
                std_reward = np.std(metrics_callback.rewards)
                print(f"奖励标准差: {std_reward:.4f}")

        # 绘制不同种子的奖励曲线
        if rewards_history:
            plt.figure(figsize=(10, 6))
            for i, rewards in enumerate(rewards_history):
                plt.plot(rewards, label=f'Seed {seeds[i]}')

            plt.title("不同随机种子的训练奖励")
            plt.xlabel("步数")
            plt.ylabel("奖励")
            plt.legend()
            plt.grid(True)

            plots_dir = "plots"
            if not os.path.exists(plots_dir):
                os.makedirs(plots_dir)

            plt.savefig(os.path.join(plots_dir, f"training_stability_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"))
            print(f"训练稳定性可视化已保存到 plots/training_stability_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")

    except Exception as e:
        print(f"测试模型训练稳定性时出错: {str(e)}")
        print(traceback.format_exc())

def test_model_save_load():
    """测试模型保存与加载"""
    print("\n===== 测试模型保存与加载 =====")

    # 获取测试数据
    handler = DataHandler(cache_dir='data_cache')
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=120)).strftime('%Y-%m-%d')
    stock = 'sh000001'

    try:
        # 获取原始数据
        data = handler.get_stock_data(stock, start_date, end_date)

        if data is None or data.empty:
            print("获取测试数据失败，无法进行模型保存与加载测试")
            return

        # 加载配置
        env_config = load_config("quant_project/configs/env_config.yaml")
        agent_config = load_config("quant_project/configs/drl_agent_config.yaml")

        # 生成特征
        feature_engineer = FeatureEngineer(env_config.get('feature_config', {}))
        processed_data = feature_engineer.generate_features(data)

        # 创建环境配置
        env_params = {
            'df_processed_data': processed_data,
            'initial_capital': env_config.get('initial_capital', 100000),
            'commission_rate': env_config.get('commission_rate', 0.0003),
            'min_hold_days': env_config.get('min_hold_days', 3),
            'allow_short': env_config.get('allow_short', False),
            'max_position': env_config.get('max_position', 1.0),
            'window_size': min(20, len(processed_data) - 1),  # 确保窗口大小不超过数据长度
            'reward_config': env_config.get('reward_config', {})
        }

        # 创建DRL智能体
        agent_config['algorithm'] = 'PPO'  # 使用PPO算法
        drl_agent = DRLAgent(env_params, agent_config)

        # 训练模型（使用较小的步数）
        print("训练简单模型...")
        drl_agent.train(total_timesteps=2000, progress_bar=True)

        # 保存模型
        print("保存模型...")
        model_path = drl_agent.save_model(stock_code=stock)
        print(f"模型已保存到: {model_path}")

        # 加载模型
        print("加载模型...")
        loaded_agent = DRLAgent.load_model(model_path)

        if loaded_agent:
            print("模型加载成功")

            # 测试加载的模型进行预测
            print("使用加载的模型进行预测...")

            # 创建测试环境
            test_env_params = env_params.copy()
            test_env = TradingEnvironment(**test_env_params)

            # 重置环境
            observation, info = test_env.reset()

            # 使用原始模型预测
            original_action = drl_agent.predict_action(observation)

            # 使用加载的模型预测
            loaded_action = loaded_agent.predict_action(observation)

            print(f"原始模型预测动作: {original_action}")
            print(f"加载的模型预测动作: {loaded_action}")

            if original_action == loaded_action:
                print("验证通过: 加载的模型与原始模型预测结果一致")
            else:
                print("警告: 加载的模型与原始模型预测结果不一致")
        else:
            print("模型加载失败")

    except Exception as e:
        print(f"测试模型保存与加载时出错: {str(e)}")
        print(traceback.format_exc())

def test_algorithm_comparison():
    """测试算法对比"""
    print("\n===== 测试算法对比 =====")

    # 获取测试数据
    handler = DataHandler(cache_dir='data_cache')
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=180)).strftime('%Y-%m-%d')
    stock = 'sh000001'

    try:
        # 获取原始数据
        data = handler.get_stock_data(stock, start_date, end_date)

        if data is None or data.empty:
            print("获取测试数据失败，无法进行算法对比测试")
            return

        # 加载配置
        env_config = load_config("quant_project/configs/env_config.yaml")
        agent_config = load_config("quant_project/configs/drl_agent_config.yaml")

        # 生成特征
        feature_engineer = FeatureEngineer(env_config.get('feature_config', {}))
        processed_data = feature_engineer.generate_features(data)

        # 数据分割
        split_config = env_config.get('data_split', {'train_ratio': 0.7, 'val_ratio': 0.15, 'test_ratio': 0.15})
        train_size = int(len(processed_data) * split_config['train_ratio'])
        val_size = int(len(processed_data) * split_config['val_ratio'])

        train_data = processed_data.iloc[:train_size]
        val_data = processed_data.iloc[train_size:train_size+val_size]
        test_data = processed_data.iloc[train_size+val_size:]

        # 创建环境配置
        env_params = {
            'df_processed_data': train_data,
            'initial_capital': env_config.get('initial_capital', 100000),
            'commission_rate': env_config.get('commission_rate', 0.0003),
            'min_hold_days': env_config.get('min_hold_days', 3),
            'allow_short': env_config.get('allow_short', False),
            'max_position': env_config.get('max_position', 1.0),
            'window_size': min(20, len(train_data) - 1),  # 确保窗口大小不超过数据长度
            'reward_config': env_config.get('reward_config', {})
        }

        # 测试不同算法
        algorithms = ['PPO', 'A2C', 'DQN']
        total_timesteps = 5000  # 使用较小的步数进行测试

        algorithm_rewards = {}

        for algorithm in algorithms:
            print(f"\n测试算法: {algorithm}")

            # 创建DRL智能体
            agent_config['algorithm'] = algorithm
            drl_agent = DRLAgent(env_params, agent_config)

            # 创建回调
            metrics_callback = MetricsCallback()

            # 训练模型
            print(f"开始训练: 算法={algorithm}, 步数={total_timesteps}")
            training_stats = drl_agent.train(
                total_timesteps=total_timesteps,
                callback_list=[metrics_callback],
                progress_bar=True
            )

            # 收集奖励历史
            if hasattr(metrics_callback, 'rewards') and metrics_callback.rewards:
                algorithm_rewards[algorithm] = metrics_callback.rewards

                # 计算平均奖励
                avg_reward = sum(metrics_callback.rewards) / max(1, len(metrics_callback.rewards))
                print(f"平均奖励: {avg_reward:.4f}")

            # 在测试集上评估
            print(f"在测试集上评估 {algorithm}...")

            # 创建测试环境 - 使用与训练相同的环境参数，只是替换数据
            # 注意：我们不能直接使用测试数据，因为模型期望特定维度的观测空间
            # 所以我们只评估训练环境的性能
            print(f"使用训练环境评估 {algorithm} 性能...")

            # 重置训练环境
            observation, info = drl_agent.env.reset()
            done = False
            total_reward = 0

            # 执行几个回合的评估
            for _ in range(3):  # 执行3个回合
                observation, info = drl_agent.env.reset()
                episode_reward = 0
                done = False

                while not done:
                    action = drl_agent.predict_action(observation)
                    observation, reward, terminated, truncated, info = drl_agent.env.step(action)
                    episode_reward += reward
                    done = terminated or truncated

                total_reward += episode_reward
                print(f"回合奖励: {episode_reward:.4f}")

            # 计算平均奖励
            avg_reward = total_reward / 3

            print(f"平均回合奖励: {avg_reward:.4f}")
            print(f"最终组合价值: {info['portfolio_value']:.2f}")

            # 保存模型
            model_path = drl_agent.save_model(stock_code=f"{stock}_{algorithm}")
            print(f"{algorithm} 模型已保存到: {model_path}")

        # 绘制不同算法的奖励曲线
        if algorithm_rewards:
            plt.figure(figsize=(10, 6))
            for algorithm, rewards in algorithm_rewards.items():
                plt.plot(rewards, label=algorithm)

            plt.title("不同算法的训练奖励")
            plt.xlabel("步数")
            plt.ylabel("奖励")
            plt.legend()
            plt.grid(True)

            plots_dir = "plots"
            if not os.path.exists(plots_dir):
                os.makedirs(plots_dir)

            plt.savefig(os.path.join(plots_dir, f"algorithm_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"))
            print(f"算法对比可视化已保存到 plots/algorithm_comparison_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")

    except Exception as e:
        print(f"测试算法对比时出错: {str(e)}")
        print(traceback.format_exc())

def run_all_tests():
    """运行所有DRL模型训练与评估测试"""
    try:
        test_model_training_stability()
        test_model_save_load()
        test_algorithm_comparison()
        print("\n===== 所有DRL模型训练与评估测试完成 =====")
        return True
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        print(traceback.format_exc())
        return False

if __name__ == '__main__':
    run_all_tests()
