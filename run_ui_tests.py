#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
UI测试运行器
运行所有UI测试并生成测试报告
"""

import os
import sys
import time
import logging
import subprocess
import pandas as pd
import numpy as np
from datetime import datetime
import traceback
import json
import importlib

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join('logs', 'ui_test_runner.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('ui_test_runner')

def run_test_script(script_path):
    """
    运行测试脚本
    
    参数:
        script_path (str): 测试脚本路径
    
    返回:
        dict: 测试结果
    """
    logger.info(f"运行测试脚本: {script_path}")
    
    try:
        # 获取脚本名称（不含扩展名）
        script_name = os.path.splitext(os.path.basename(script_path))[0]
        
        # 导入脚本模块
        module = importlib.import_module(script_name)
        
        # 运行测试
        if hasattr(module, 'run_all_tests'):
            start_time = time.time()
            results = module.run_all_tests()
            end_time = time.time()
            
            execution_time = end_time - start_time
            logger.info(f"测试脚本 {script_name} 运行完成，耗时: {execution_time:.2f} 秒")
            
            return {
                'script': script_name,
                'success': True,
                'results': results,
                'execution_time': execution_time
            }
        else:
            logger.error(f"测试脚本 {script_name} 没有 run_all_tests 函数")
            return {
                'script': script_name,
                'success': False,
                'error': "脚本没有 run_all_tests 函数"
            }
    
    except Exception as e:
        logger.error(f"运行测试脚本 {script_path} 时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            'script': os.path.basename(script_path),
            'success': False,
            'error': str(e)
        }

def generate_html_report(results, output_path):
    """
    生成HTML测试报告
    
    参数:
        results (list): 测试结果列表
        output_path (str): 输出路径
    """
    logger.info(f"生成HTML测试报告: {output_path}")
    
    # 创建HTML报告
    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>UI测试报告</title>
        <style>
            body {{
                font-family: Arial, sans-serif;
                margin: 20px;
                line-height: 1.6;
            }}
            h1, h2, h3 {{
                color: #333;
            }}
            table {{
                border-collapse: collapse;
                width: 100%;
                margin-bottom: 20px;
            }}
            th, td {{
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
            }}
            th {{
                background-color: #f2f2f2;
            }}
            tr:nth-child(even) {{
                background-color: #f9f9f9;
            }}
            .success {{
                color: green;
                font-weight: bold;
            }}
            .failure {{
                color: red;
                font-weight: bold;
            }}
            .warning {{
                color: orange;
                font-weight: bold;
            }}
            .details {{
                margin-left: 20px;
                margin-bottom: 20px;
            }}
        </style>
    </head>
    <body>
        <h1>UI测试报告</h1>
        <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        
        <h2>测试摘要</h2>
        <table>
            <tr>
                <th>测试脚本</th>
                <th>状态</th>
                <th>执行时间 (秒)</th>
                <th>详情</th>
            </tr>
    """
    
    # 添加测试结果
    for result in results:
        script = result.get('script', '')
        success = result.get('success', False)
        execution_time = result.get('execution_time', 0)
        error = result.get('error', '')
        
        status_class = 'success' if success else 'failure'
        status_text = '成功' if success else '失败'
        
        html += f"""
            <tr>
                <td>{script}</td>
                <td class="{status_class}">{status_text}</td>
                <td>{execution_time:.2f}</td>
                <td>{error}</td>
            </tr>
        """
    
    html += """
        </table>
        
        <h2>详细测试结果</h2>
    """
    
    # 添加详细测试结果
    for result in results:
        script = result.get('script', '')
        success = result.get('success', False)
        test_results = result.get('results', {})
        
        html += f"""
        <h3>脚本: {script}</h3>
        """
        
        if success and test_results:
            html += """
            <div class="details">
                <table>
                    <tr>
                        <th>测试模块</th>
                        <th>结果</th>
                    </tr>
            """
            
            for module, module_result in test_results.items():
                if isinstance(module_result, dict):
                    # 复杂结果
                    success_rate = module_result.get('success_rate', 0)
                    if 'success' in module_result:
                        module_success = module_result['success']
                        status_class = 'success' if module_success else 'failure'
                        status_text = '成功' if module_success else '失败'
                    else:
                        status_class = 'success' if success_rate > 0.8 else ('warning' if success_rate > 0.5 else 'failure')
                        status_text = f"{success_rate * 100:.2f}% 成功"
                    
                    html += f"""
                    <tr>
                        <td>{module}</td>
                        <td class="{status_class}">{status_text}</td>
                    </tr>
                    """
                else:
                    # 简单结果
                    status_class = 'success' if module_result else 'failure'
                    status_text = '成功' if module_result else '失败'
                    
                    html += f"""
                    <tr>
                        <td>{module}</td>
                        <td class="{status_class}">{status_text}</td>
                    </tr>
                    """
            
            html += """
                </table>
            </div>
            """
        else:
            html += f"""
            <div class="details">
                <p class="failure">测试未成功完成或没有返回结果</p>
            </div>
            """
    
    html += """
        <h2>测试结论</h2>
    """
    
    # 计算总体成功率
    success_count = sum(1 for result in results if result.get('success', False))
    total_count = len(results)
    success_rate = success_count / total_count if total_count > 0 else 0
    
    if success_rate == 1:
        html += """
        <p class="success">所有测试都成功通过！</p>
        """
    elif success_rate >= 0.8:
        html += """
        <p class="warning">大部分测试通过，但有少量失败。请查看详细结果。</p>
        """
    else:
        html += """
        <p class="failure">测试通过率较低，需要修复问题。请查看详细结果。</p>
        """
    
    html += """
    </body>
    </html>
    """
    
    # 写入HTML文件
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html)
    
    logger.info(f"HTML测试报告已生成: {output_path}")

def run_all_tests():
    """运行所有UI测试"""
    logger.info("开始运行所有UI测试")
    
    # 确保测试目录存在
    os.makedirs('test_results', exist_ok=True)
    os.makedirs('logs', exist_ok=True)
    
    # 定义测试脚本
    test_scripts = [
        'test_ui.py',
        'test_ui_advanced.py',
        'test_ui_session.py'
    ]
    
    # 运行测试脚本
    results = []
    for script in test_scripts:
        result = run_test_script(script)
        results.append(result)
    
    # 生成测试报告
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    report_path = os.path.join('test_results', f'ui_test_report_{timestamp}.html')
    generate_html_report(results, report_path)
    
    # 保存原始测试结果
    results_path = os.path.join('test_results', f'ui_test_results_{timestamp}.json')
    with open(results_path, 'w') as f:
        json.dump(results, f, indent=4, default=str)
    
    logger.info(f"所有UI测试完成，测试报告已生成: {report_path}")
    
    return results

if __name__ == "__main__":
    run_all_tests()
