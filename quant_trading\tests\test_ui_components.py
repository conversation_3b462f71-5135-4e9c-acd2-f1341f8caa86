#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
UI组件测试脚本
测试量化交易系统的UI组件
"""

import os
import sys
import time
import logging
import subprocess
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import traceback
import json
import matplotlib.pyplot as plt

# 设置日志
os.makedirs('logs', exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join('logs', 'ui_components_test.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('ui_components_test')

def test_app_startup():
    """测试应用启动"""
    logger.info("测试应用启动")
    
    try:
        # 查找主应用文件
        app_files = ['main_app.py', 'quant_project/main_app.py']
        app_file = None
        
        for file in app_files:
            if os.path.exists(file):
                app_file = file
                break
        
        if app_file is None:
            logger.error("找不到主应用文件")
            return {
                'success': False,
                'error': '找不到主应用文件'
            }
        
        # 使用subprocess启动应用，不阻塞当前进程
        process = subprocess.Popen(
            [sys.executable, "-m", "streamlit", "run", app_file],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待几秒钟让应用启动
        time.sleep(5)
        
        # 检查进程是否仍在运行
        if process.poll() is None:
            logger.info("应用成功启动")
            
            # 终止进程
            process.terminate()
            process.wait(timeout=5)
            
            return {
                'success': True,
                'app_file': app_file
            }
        else:
            # 获取错误输出
            stdout, stderr = process.communicate()
            logger.error(f"应用启动失败: {stderr}")
            return {
                'success': False,
                'error': stderr,
                'app_file': app_file
            }
    except Exception as e:
        logger.error(f"测试应用启动时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            'success': False,
            'error': str(e)
        }

def test_ui_components():
    """测试UI组件"""
    logger.info("测试UI组件")
    
    try:
        # 查找UI组件目录
        ui_dirs = ['ui_components', 'quant_project/ui_components']
        ui_dir = None
        
        for directory in ui_dirs:
            if os.path.exists(directory) and os.path.isdir(directory):
                ui_dir = directory
                break
        
        if ui_dir is None:
            logger.warning("找不到UI组件目录，尝试在主应用文件中查找UI组件")
            
            # 查找主应用文件
            app_files = ['main_app.py', 'quant_project/main_app.py']
            app_file = None
            
            for file in app_files:
                if os.path.exists(file):
                    app_file = file
                    break
            
            if app_file is None:
                logger.error("找不到主应用文件")
                return {
                    'success': False,
                    'error': '找不到主应用文件和UI组件目录'
                }
            
            # 分析主应用文件中的UI组件
            with open(app_file, 'r', encoding='utf-8') as f:
                app_content = f.read()
            
            # 检查是否使用了Streamlit
            if 'import streamlit as st' in app_content:
                logger.info("主应用使用了Streamlit")
                
                # 检查页面数量
                pages = []
                if "st.sidebar.selectbox" in app_content:
                    logger.info("检测到侧边栏页面选择器")
                    
                    # 尝试提取页面名称
                    import re
                    page_match = re.search(r'st\.sidebar\.selectbox\([^,]+,\s*\[([^\]]+)\]', app_content)
                    if page_match:
                        page_str = page_match.group(1)
                        # 提取引号中的内容
                        page_names = re.findall(r'[\'"]([^\'"]+)[\'"]', page_str)
                        pages = page_names
                        logger.info(f"检测到页面: {pages}")
                
                return {
                    'success': True,
                    'ui_framework': 'Streamlit',
                    'app_file': app_file,
                    'pages': pages
                }
            else:
                logger.warning("主应用未使用Streamlit或无法识别UI框架")
                return {
                    'success': False,
                    'error': '无法识别UI框架'
                }
        
        # 如果找到UI组件目录，分析组件
        ui_files = [f for f in os.listdir(ui_dir) if f.endswith('.py')]
        
        if not ui_files:
            logger.warning(f"UI组件目录 {ui_dir} 中没有Python文件")
            return {
                'success': False,
                'error': f"UI组件目录 {ui_dir} 中没有Python文件"
            }
        
        components = []
        
        for ui_file in ui_files:
            file_path = os.path.join(ui_dir, ui_file)
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否使用了Streamlit
            if 'import streamlit as st' in content:
                # 尝试提取组件名称和功能
                import re
                
                # 查找函数定义
                functions = re.findall(r'def\s+([a-zA-Z0-9_]+)\s*\(', content)
                
                components.append({
                    'file': ui_file,
                    'framework': 'Streamlit',
                    'functions': functions
                })
        
        return {
            'success': True,
            'ui_dir': ui_dir,
            'components': components
        }
    
    except Exception as e:
        logger.error(f"测试UI组件时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            'success': False,
            'error': str(e)
        }

def test_ui_backend_interaction():
    """测试UI与后端的交互"""
    logger.info("测试UI与后端的交互")
    
    try:
        # 查找主应用文件
        app_files = ['main_app.py', 'quant_project/main_app.py']
        app_file = None
        
        for file in app_files:
            if os.path.exists(file):
                app_file = file
                break
        
        if app_file is None:
            logger.error("找不到主应用文件")
            return {
                'success': False,
                'error': '找不到主应用文件'
            }
        
        # 分析主应用文件中的后端交互
        with open(app_file, 'r', encoding='utf-8') as f:
            app_content = f.read()
        
        # 检查后端模块导入
        backend_imports = []
        
        import re
        
        # 查找从core_logic导入的模块
        core_logic_imports = re.findall(r'from\s+(?:quant_project\.)?core_logic\.([a-zA-Z0-9_]+)\s+import', app_content)
        backend_imports.extend(core_logic_imports)
        
        # 查找直接导入的core_logic模块
        direct_imports = re.findall(r'import\s+(?:quant_project\.)?core_logic\.([a-zA-Z0-9_]+)', app_content)
        backend_imports.extend(direct_imports)
        
        # 检查是否有表单提交
        has_forms = 'st.form' in app_content
        
        # 检查是否有按钮
        has_buttons = 'st.button' in app_content
        
        # 检查是否有文件上传
        has_file_upload = 'st.file_uploader' in app_content
        
        # 检查是否有进度条
        has_progress_bar = 'st.progress' in app_content
        
        # 检查是否有图表
        has_charts = any(chart_func in app_content for chart_func in ['st.line_chart', 'st.bar_chart', 'st.pyplot'])
        
        # 检查是否有会话状态
        has_session_state = 'st.session_state' in app_content
        
        # 检查是否有缓存
        has_cache = '@st.cache' in app_content or '@st.cache_data' in app_content
        
        # 检查是否有停止按钮
        has_stop_button = 'stop' in app_content.lower() and 'button' in app_content.lower()
        
        # 检查是否有日志清除选项
        has_log_clear = 'clear' in app_content.lower() and 'log' in app_content.lower()
        
        return {
            'success': True,
            'app_file': app_file,
            'backend_imports': backend_imports,
            'ui_features': {
                'has_forms': has_forms,
                'has_buttons': has_buttons,
                'has_file_upload': has_file_upload,
                'has_progress_bar': has_progress_bar,
                'has_charts': has_charts,
                'has_session_state': has_session_state,
                'has_cache': has_cache,
                'has_stop_button': has_stop_button,
                'has_log_clear': has_log_clear
            }
        }
    
    except Exception as e:
        logger.error(f"测试UI与后端的交互时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            'success': False,
            'error': str(e)
        }

def run_all_tests():
    """运行所有UI组件测试"""
    logger.info("开始运行UI组件测试")
    
    # 测试应用启动
    startup_results = test_app_startup()
    logger.info(f"应用启动测试结果: {startup_results}")
    
    # 测试UI组件
    components_results = test_ui_components()
    logger.info(f"UI组件测试结果: {components_results}")
    
    # 测试UI与后端的交互
    interaction_results = test_ui_backend_interaction()
    logger.info(f"UI与后端交互测试结果: {interaction_results}")
    
    # 汇总测试结果
    results = {
        'startup': startup_results,
        'components': components_results,
        'interaction': interaction_results
    }
    
    # 保存测试结果
    os.makedirs('test_results', exist_ok=True)
    
    # 将结果转换为可序列化的格式
    serializable_results = {}
    for category, result in results.items():
        if isinstance(result, dict):
            serializable_result = {}
            for k, v in result.items():
                if isinstance(v, (list, dict, bool, int, float, str)) or v is None:
                    serializable_result[k] = v
                else:
                    serializable_result[k] = str(v)
            serializable_results[category] = serializable_result
        else:
            serializable_results[category] = str(result)
    
    with open('test_results/ui_components_test.json', 'w', encoding='utf-8') as f:
        json.dump(serializable_results, f, ensure_ascii=False, indent=4)
    
    logger.info("UI组件测试完成，结果已保存")
    
    return results

if __name__ == "__main__":
    run_all_tests()
