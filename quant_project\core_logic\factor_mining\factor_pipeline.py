"""
因子流水线模块
负责自动化因子挖掘和应用的流程
"""

import logging
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import time
import json
import os

from quant_project.core_logic.factor_mining.factor_generator import FactorGenerator
from quant_project.core_logic.factor_mining.factor_evaluator import FactorEvaluator
from quant_project.core_logic.factor_mining.factor_selector import FactorSelector

class AutoFactorPipeline:
    """
    自动因子挖掘流水线
    自动化执行因子生成、评估和选择的流程
    """
    
    def __init__(self, data_handler):
        """
        初始化自动因子挖掘流水线
        
        参数:
            data_handler: 数据处理器
        """
        self.data_handler = data_handler
        self.logger = logging.getLogger('drl_trading')
        self.config = self._get_default_config()
        self.generator = FactorGenerator(data_handler)
        self.selector = FactorSelector()
        
    def _get_default_config(self):
        """获取默认配置"""
        return {
            # 因子生成配置
            'generate_technical_factors': True,  # 技术指标因子
            'generate_cross_factors': True,      # 交叉因子
            'generate_time_factors': True,       # 时间序列因子
            
            # 因子评估配置
            'min_ic_abs': 0.05,                # 最小IC绝对值
            'evaluation_periods': [1, 3, 5, 10, 20],  # 评估周期
            
            # 因子筛选配置
            'corr_threshold': 0.7,             # 相关性阈值
            'multi_period_min': 3,             # 最少有效周期
            'multi_period_ratio': 0.6,         # 最少有效周期比例
            'top_n_factors': 20,               # 选择的因子数量
            'use_composite_score': True,       # 使用综合得分
            
            # 特殊配置
            'enable_parallel': False,           # 是否启用并行计算
            'verbose': True,                    # 详细日志
            'plot_results': True,               # 绘制结果
            'save_results': True,               # 保存结果
            'result_dir': 'factor_results',     # 结果保存目录
        }
    
    def configure(self, **kwargs):
        """
        更新配置参数
        
        参数:
            **kwargs: 配置参数
            
        返回:
            self: 支持链式调用
        """
        for key, value in kwargs.items():
            if key in self.config:
                self.config[key] = value
                self.logger.debug(f"更新配置: {key} = {value}")
            else:
                self.logger.warning(f"未知配置参数: {key}")
                
        return self
    
    def run_with_data(self, data, min_ic_abs=None, corr_threshold=None, top_n_factors=None,
                     evaluation_periods=None, progress_callback=None):
        """
        使用提供的数据运行因子挖掘流水线
        
        参数:
            data (pandas.DataFrame): 输入数据
            min_ic_abs (float): 最小IC绝对值
            corr_threshold (float): 相关性阈值
            top_n_factors (int): 选择的因子数量
            evaluation_periods (list): 评估周期
            progress_callback (callable): 进度回调函数，接收三个参数：阶段名称，进度(0-1)，消息
            
        返回:
            dict: 挖掘结果
        """
        self.logger.info("开始运行自动因子挖掘流水线...")
        start_time = time.time()
        
        # 定义进度报告函数
        def report_progress(stage, progress, message):
            if progress_callback and callable(progress_callback):
                try:
                    progress_callback(stage, progress, message)
                except Exception as e:
                    self.logger.warning(f"调用进度回调函数出错: {str(e)}")
            self.logger.info(f"{stage} - {progress:.0%} - {message}")
        
        # 使用传入的参数覆盖配置
        if min_ic_abs is not None:
            self.config['min_ic_abs'] = min_ic_abs
        if corr_threshold is not None:
            self.config['corr_threshold'] = corr_threshold
        if top_n_factors is not None:
            self.config['top_n_factors'] = top_n_factors
        if evaluation_periods is not None:
            self.config['evaluation_periods'] = evaluation_periods
        
        # 初始化进度
        report_progress("初始化", 0.0, "开始因子挖掘流程")
        
        # 1. 生成因子
        report_progress("因子生成", 0.1, "开始生成因子")
        start_generate = time.time()
        
        all_factors = {}
        
        # 1.1 生成技术指标因子
        if self.config['generate_technical_factors']:
            report_progress("因子生成", 0.15, "生成技术指标因子")
            technical_factors = self.generator.generate_technical_factors(data)
            all_factors.update(technical_factors)
            report_progress("因子生成", 0.2, f"生成了 {len(technical_factors)} 个技术指标因子")
        
        # 1.2 生成交叉因子
        if self.config['generate_cross_factors'] and all_factors:
            report_progress("因子生成", 0.25, "生成交叉因子")
            cross_factors = self.generator.generate_cross_factors(all_factors)
            all_factors.update(cross_factors)
            report_progress("因子生成", 0.3, f"生成了 {len(cross_factors)} 个交叉因子")
        
        # 1.3 生成时间序列因子
        if self.config['generate_time_factors'] and all_factors:
            report_progress("因子生成", 0.35, "生成时间序列因子")
            time_factors = self.generator.generate_time_factors(all_factors)
            all_factors.update(time_factors)
            report_progress("因子生成", 0.4, f"生成了 {len(time_factors)} 个时间序列因子")
        
        generate_time = time.time() - start_generate
        report_progress("因子生成", 0.4, f"因子生成完成，共 {len(all_factors)} 个因子，耗时 {generate_time:.2f} 秒")
        
        if not all_factors:
            report_progress("失败", 1.0, "没有生成任何因子，流水线终止")
            return {
                'status': 'failed',
                'message': '没有生成任何因子',
                'time_elapsed': time.time() - start_time
            }
        
        # 2. 评估因子
        report_progress("因子评估", 0.45, "开始评估因子")
        start_evaluate = time.time()
        
        # 创建评估器
        evaluator = FactorEvaluator(data)
        
        # 评估所有因子
        report_progress("因子评估", 0.5, f"评估 {len(all_factors)} 个因子")
        evaluation_results = evaluator.evaluate_factors(all_factors, self.config['min_ic_abs'])
        
        evaluate_time = time.time() - start_evaluate
        report_progress("因子评估", 0.6, f"因子评估完成，共评估了 {len(evaluation_results)} 个因子，耗时 {evaluate_time:.2f} 秒")
        
        if not evaluation_results:
            report_progress("失败", 1.0, "没有因子通过评估，流水线终止")
            return {
                'status': 'failed',
                'message': '没有因子通过评估',
                'time_elapsed': time.time() - start_time,
                'all_factors': all_factors
            }
        
        # 3. 筛选因子
        report_progress("因子筛选", 0.65, "开始筛选因子")
        start_select = time.time()
        
        # 3.1 多周期过滤
        report_progress("因子筛选", 0.7, "多周期过滤")
        filtered_factors = self.selector.multi_period_filter(
            all_factors, 
            evaluation_results, 
            min_periods=self.config['multi_period_min'], 
            min_periods_ratio=self.config['multi_period_ratio']
        )
        
        # 3.2 相关性过滤
        report_progress("因子筛选", 0.75, "相关性过滤")
        filtered_factors = self.selector.remove_highly_correlated(
            filtered_factors, 
            threshold=self.config['corr_threshold'], 
            method='keep_highest_ic'
        )
        
        # 3.3 选择最终因子
        report_progress("因子筛选", 0.8, "选择最终因子")
        best_factors = self.selector.select_best_factors(
            filtered_factors, 
            evaluation_results, 
            top_n=self.config['top_n_factors'],
            use_composite_score=self.config['use_composite_score']
        )
        
        select_time = time.time() - start_select
        report_progress("因子筛选", 0.85, f"因子筛选完成，选择了 {len(best_factors)} 个最佳因子，耗时 {select_time:.2f} 秒")
        
        if not best_factors:
            report_progress("失败", 1.0, "筛选后没有剩余因子，流水线终止")
            return {
                'status': 'failed',
                'message': '筛选后没有剩余因子',
                'time_elapsed': time.time() - start_time,
                'all_factors': all_factors,
                'evaluation_results': evaluation_results
            }
        
        # 4. 绘制结果
        if self.config['plot_results']:
            report_progress("结果处理", 0.9, "绘制结果")
            
            # 绘制前置条件检查
            if not hasattr(evaluator, 'plot_factor_evaluation'):
                self.logger.warning("评估器没有绘图方法，跳过绘图")
            else:
                try:
                    # 绘制因子评估结果
                    fig = evaluator.plot_factor_evaluation(
                        evaluation_results, 
                        top_n=min(self.config['top_n_factors'], len(best_factors))
                    )
                    
                    # 保存图形
                    if self.config['save_results'] and fig is not None:
                        result_dir = self.config['result_dir']
                        os.makedirs(result_dir, exist_ok=True)
                        
                        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                        fig_path = os.path.join(result_dir, f'factor_evaluation_{timestamp}.png')
                        fig.savefig(fig_path, dpi=300, bbox_inches='tight')
                        report_progress("结果处理", 0.95, f"保存评估图表到 {fig_path}")
                except Exception as e:
                    self.logger.warning(f"绘制结果时出错: {str(e)}")
        
        # 5. 保存结果
        report_progress("完成", 1.0, "因子挖掘成功完成")
        result = {
            'status': 'success',
            'message': '因子挖掘成功完成',
            'time_elapsed': time.time() - start_time,
            'best_factors': best_factors,
            'evaluation_results': {k: evaluation_results[k] for k in best_factors if k in evaluation_results},
            'config': self.config,
            'factor_summary': {
                'total_generated': len(all_factors),
                'passed_evaluation': len(evaluation_results),
                'final_selected': len(best_factors)
            }
        }
        
        if self.config['save_results']:
            self.logger.info("保存因子挖掘结果...")
            
            result_dir = self.config['result_dir']
            os.makedirs(result_dir, exist_ok=True)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # 保存因子数据
            factor_data = pd.DataFrame(best_factors)
            factor_data_path = os.path.join(result_dir, f'best_factors_{timestamp}.csv')
            factor_data.to_csv(factor_data_path)
            self.logger.info(f"保存因子数据到 {factor_data_path}")
            
            # 保存评估结果摘要
            eval_summary = []
            for factor_name in best_factors:
                if factor_name in evaluation_results:
                    eval_data = evaluation_results[factor_name]
                    eval_summary.append({
                        'factor_name': factor_name,
                        'best_ic': eval_data.get('best_ic', 0),
                        'max_abs_ic': eval_data.get('max_abs_ic', 0),
                        'best_period': eval_data.get('best_period', 0),
                        'composite_score': eval_data.get('composite_score', 0)
                    })
            
            eval_summary_df = pd.DataFrame(eval_summary)
            eval_summary_path = os.path.join(result_dir, f'factor_evaluation_{timestamp}.csv')
            eval_summary_df.to_csv(eval_summary_path, index=False)
            self.logger.info(f"保存评估摘要到 {eval_summary_path}")
        
        self.logger.info(f"自动因子挖掘流水线完成，共耗时 {time.time() - start_time:.2f} 秒")
        return result
    
    def run(self, stock_code, start_date, end_date, 
            min_ic_abs=None, corr_threshold=None, top_n_factors=None,
            evaluation_periods=None, progress_callback=None):
        """
        运行因子挖掘流水线
        
        参数:
            stock_code (str): 股票代码
            start_date (str): 开始日期，格式YYYY-MM-DD
            end_date (str): 结束日期，格式YYYY-MM-DD
            min_ic_abs (float): 最小IC绝对值
            corr_threshold (float): 相关性阈值
            top_n_factors (int): 选择的因子数量
            evaluation_periods (list): 评估周期
            progress_callback (callable): 进度回调函数，接收三个参数：阶段名称，进度(0-1)，消息
            
        返回:
            dict: 挖掘结果
        """
        self.logger.info(f"运行自动因子挖掘 - 股票: {stock_code}, 时间: {start_date} 至 {end_date}")
        
        # 使用数据处理器获取数据
        try:
            data = self.data_handler.get_stock_data(
                stock_code=stock_code,
                start_date=start_date,
                end_date=end_date,
                adjust='qfq'  # 前复权
            )
            
            if data is None or data.empty:
                self.logger.error(f"无法获取股票数据: {stock_code}")
                return {
                    'status': 'failed',
                    'message': f'无法获取股票数据: {stock_code}'
                }
                
            self.logger.info(f"成功获取数据，形状: {data.shape}")
            
        except Exception as e:
            self.logger.error(f"获取股票数据时出错: {str(e)}")
            return {
                'status': 'failed',
                'message': f'获取股票数据时出错: {str(e)}'
            }
        
        # 运行挖掘流水线
        result = self.run_with_data(
            data=data,
            min_ic_abs=min_ic_abs,
            corr_threshold=corr_threshold,
            top_n_factors=top_n_factors,
            evaluation_periods=evaluation_periods,
            progress_callback=progress_callback
        )
        
        # 添加股票代码和时间范围信息
        result['stock_code'] = stock_code
        result['date_range'] = {
            'start_date': start_date,
            'end_date': end_date
        }
        
        return result

class AdaptiveFactorSystem:
    """
    自适应因子系统
    动态更新并应用最佳交易因子
    """
    
    def __init__(self, data_handler, model_trainer=None):
        """
        初始化自适应因子系统
        
        参数:
            data_handler: 数据处理器
            model_trainer: 模型训练器
        """
        self.data_handler = data_handler
        self.model_trainer = model_trainer
        self.factor_pipeline = AutoFactorPipeline(data_handler)
        self.logger = logging.getLogger('drl_trading')
    
    def update_factors(self, stock_code, lookback_days=365, min_ic_abs=0.05, top_n=20):
        """
        更新因子集合
        
        参数:
            stock_code (str): 股票代码
            lookback_days (int): 回溯天数
            min_ic_abs (float): 最小IC绝对值
            top_n (int): 选择的顶级因子数量
            
        返回:
            dict: 更新结果
        """
        self.logger.info(f"更新因子 - 股票: {stock_code}, 回溯天数: {lookback_days}")
        
        # 计算日期范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=lookback_days)
        
        # 格式化日期
        start_date_str = start_date.strftime('%Y-%m-%d')
        end_date_str = end_date.strftime('%Y-%m-%d')
        
        # 运行因子挖掘
        result = self.factor_pipeline.run(
            stock_code=stock_code,
            start_date=start_date_str,
            end_date=end_date_str,
            min_ic_abs=min_ic_abs,
            top_n_factors=top_n
        )
        
        if result['status'] != 'success':
            self.logger.error(f"因子更新失败: {result['message']}")
            return result
            
        self.logger.info(f"因子更新成功，获得 {len(result['best_factors'])} 个因子")
        
        return result
    
    def train_with_best_factors(self, stock_code, env_config, agent_config, lookback_days=365):
        """
        使用最佳因子训练模型
        
        参数:
            stock_code (str): 股票代码
            env_config (dict): 环境配置
            agent_config (dict): 智能体配置
            lookback_days (int): 回溯天数
            
        返回:
            dict: 训练结果
        """
        self.logger.info(f"使用最佳因子训练模型 - 股票: {stock_code}")
        
        if self.model_trainer is None:
            self.logger.error("模型训练器未设置")
            return {
                'status': 'failed',
                'message': '模型训练器未设置'
            }
            
        # 更新因子
        factor_result = self.update_factors(stock_code, lookback_days)
        
        if factor_result['status'] != 'success':
            return factor_result
            
        best_factors = factor_result['best_factors']
        
        # 获取模型训练数据
        try:
            # 计算日期范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=lookback_days)
            
            # 格式化日期
            start_date_str = start_date.strftime('%Y-%m-%d')
            end_date_str = end_date.strftime('%Y-%m-%d')
            
            # 获取原始数据
            data = self.data_handler.get_stock_data(
                stock_code=stock_code,
                start_date=start_date_str,
                end_date=end_date_str
            )
            
            # 添加因子作为特征
            for factor_name, factor_value in best_factors.items():
                # 确保共享相同的索引
                if not isinstance(factor_value.index, pd.DatetimeIndex):
                    self.logger.warning(f"因子 {factor_name} 索引不是日期类型，尝试转换")
                    factor_value.index = pd.to_datetime(factor_value.index)
                    
                # 重采样到共同日期
                common_index = sorted(set(data.index).intersection(set(factor_value.index)))
                
                if not common_index:
                    self.logger.warning(f"因子 {factor_name} 与数据没有共同的日期")
                    continue
                    
                # 添加因子到数据
                short_name = factor_name.replace(" ", "_").replace("-", "_")[:30]  # 短名称
                data[f'factor_{short_name}'] = factor_value
                
            # 更新环境配置
            env_config['df_processed_data'] = data
            
        except Exception as e:
            self.logger.error(f"准备训练数据时出错: {str(e)}")
            return {
                'status': 'failed',
                'message': f'准备训练数据时出错: {str(e)}'
            }
            
        # 训练模型
        try:
            self.logger.info("开始训练模型...")
            model, train_result = self.model_trainer.train_model(env_config, agent_config)
            
            if model is None:
                self.logger.error("模型训练失败")
                return {
                    'status': 'failed',
                    'message': '模型训练失败',
                    'factor_result': factor_result
                }
                
            self.logger.info("模型训练成功")
            
            # 保存模型
            model_path = self.model_trainer.save_model(
                model, 
                stock_code=stock_code,
                factors=list(best_factors.keys())
            )
            
            # 返回结果
            return {
                'status': 'success',
                'message': '使用最佳因子训练模型成功',
                'model_path': model_path,
                'factors': list(best_factors.keys()),
                'factor_result': factor_result,
                'train_result': train_result
            }
            
        except Exception as e:
            self.logger.error(f"训练模型时出错: {str(e)}")
            return {
                'status': 'failed',
                'message': f'训练模型时出错: {str(e)}',
                'factor_result': factor_result
            }
    
    def apply_factors_to_model(self, model, best_factors):
        """
        将因子应用到现有模型
        
        参数:
            model: 模型对象
            best_factors (dict): 最佳因子字典
            
        返回:
            dict: 应用结果
        """
        self.logger.info(f"将因子应用到模型...")
        
        if model is None:
            self.logger.error("模型为空")
            return {
                'status': 'failed',
                'message': '模型为空'
            }
            
        try:
            # 记录原模型的信息
            orig_obs_dim = None
            if hasattr(model, 'policy') and hasattr(model.policy, 'observation_space'):
                orig_obs_dim = model.policy.observation_space.shape[0]
                
            # 检查模型类型
            is_mlp_model = False
            if hasattr(model, 'policy') and hasattr(model.policy, 'features_extractor'):
                # 检查MLP模型结构
                if 'mlp' in str(model.policy.features_extractor).lower():
                    is_mlp_model = True
                    
            if not is_mlp_model:
                self.logger.warning("模型不是MLP类型，因子适配可能不适用")
                
            # 获取模型信息
            model_info = {
                'orig_obs_dim': orig_obs_dim,
                'is_mlp_model': is_mlp_model,
                'model_type': str(type(model)),
                'policy_type': str(type(model.policy)) if hasattr(model, 'policy') else None
            }
            
            # 记录应用因子
            if not hasattr(model, 'applied_factors'):
                model.applied_factors = []
                
            # 添加应用的因子
            model.applied_factors.extend(list(best_factors.keys()))
            
            # 记录当前的训练状态
            training_stats = None
            if hasattr(model, 'logger') and hasattr(model.logger, 'get_stats'):
                training_stats = model.logger.get_stats()
                
            self.logger.info(f"因子已应用到模型，因子数量: {len(best_factors)}")
            
            return {
                'status': 'success',
                'message': '因子已应用到模型',
                'model_info': model_info,
                'applied_factors': model.applied_factors,
                'training_stats': training_stats
            }
            
        except Exception as e:
            self.logger.error(f"应用因子到模型时出错: {str(e)}")
            return {
                'status': 'failed',
                'message': f'应用因子到模型时出错: {str(e)}'
            }
    
    def export_factors(self, best_factors, file_path):
        """
        导出因子到文件
        
        参数:
            best_factors (dict): 最佳因子字典
            file_path (str): 文件路径
            
        返回:
            dict: 导出结果
        """
        self.logger.info(f"导出因子到文件: {file_path}")
        
        try:
            # 创建目录
            os.makedirs(os.path.dirname(os.path.abspath(file_path)), exist_ok=True)
            
            # 准备因子元数据
            factor_meta = {
                'factor_count': len(best_factors),
                'export_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'factor_names': list(best_factors.keys())
            }
            
            # 将因子数据转换为DataFrame
            factor_data = pd.DataFrame(best_factors)
            
            # 保存因子数据
            factor_data.to_csv(file_path)
            
            # 保存元数据
            meta_path = file_path.replace('.csv', '_meta.json')
            with open(meta_path, 'w') as f:
                json.dump(factor_meta, f, indent=4)
                
            self.logger.info(f"因子导出成功: {len(best_factors)} 个因子")
            
            return {
                'status': 'success',
                'message': '因子导出成功',
                'file_path': file_path,
                'meta_path': meta_path,
                'factor_count': len(best_factors)
            }
            
        except Exception as e:
            self.logger.error(f"导出因子时出错: {str(e)}")
            return {
                'status': 'failed',
                'message': f'导出因子时出错: {str(e)}'
            }
    
    def import_factors(self, file_path):
        """
        从文件导入因子
        
        参数:
            file_path (str): 文件路径
            
        返回:
            dict: 导入结果
        """
        self.logger.info(f"从文件导入因子: {file_path}")
        
        try:
            # 检查文件是否存在
            if not os.path.exists(file_path):
                self.logger.error(f"因子文件不存在: {file_path}")
                return {
                    'status': 'failed',
                    'message': f'因子文件不存在: {file_path}'
                }
                
            # 检查文件类型
            if not file_path.endswith('.csv'):
                self.logger.error(f"不支持的文件格式，需要CSV文件: {file_path}")
                return {
                    'status': 'failed',
                    'message': f'不支持的文件格式，需要CSV文件: {file_path}'
                }
                
            # 加载因子数据
            factor_data = pd.read_csv(file_path, index_col=0)
            
            # 将索引转换为日期类型
            factor_data.index = pd.to_datetime(factor_data.index)
            
            # 转换为因子字典
            factor_dict = {column: factor_data[column] for column in factor_data.columns}
            
            # 尝试加载元数据
            meta_path = file_path.replace('.csv', '_meta.json')
            meta_data = None
            
            if os.path.exists(meta_path):
                try:
                    with open(meta_path, 'r') as f:
                        meta_data = json.load(f)
                except Exception as e:
                    self.logger.warning(f"无法加载元数据: {str(e)}")
            
            self.logger.info(f"成功导入 {len(factor_dict)} 个因子")
            
            return {
                'status': 'success',
                'message': '因子导入成功',
                'factors': factor_dict,
                'meta_data': meta_data,
                'factor_count': len(factor_dict)
            }
            
        except Exception as e:
            self.logger.error(f"导入因子时出错: {str(e)}")
            return {
                'status': 'failed',
                'message': f'导入因子时出错: {str(e)}'
            } 