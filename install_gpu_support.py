#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
GPU支持检测与安装脚本
检测系统是否支持GPU，并提供安装GPU支持的功能
"""

import os
import sys
import logging
import subprocess
import platform
import re
import json
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('gpu_support')

def detect_gpu():
    """
    检测系统GPU支持情况
    
    返回:
        dict: GPU支持信息
    """
    logger.info("检测GPU支持...")
    
    result = {
        'available': False,
        'devices': [],
        'cuda_version': None,
        'driver_version': None,
        'pytorch_gpu': False,
        'tensorflow_gpu': False,
    }
    
    # 检测操作系统
    system = platform.system()
    logger.info(f"操作系统: {system}")
    
    # 针对不同操作系统的检测方法
    if system == 'Windows':
        result = _detect_gpu_windows(result)
    elif system == 'Linux':
        result = _detect_gpu_linux(result)
    elif system == 'Darwin':  # macOS
        result = _detect_gpu_macos(result)
    else:
        logger.warning(f"不支持的操作系统: {system}")
    
    # 检测PyTorch是否支持GPU
    result = _check_pytorch_gpu(result)
    
    # 检测TensorFlow是否支持GPU
    result = _check_tensorflow_gpu(result)
    
    # 打印检测结果
    logger.info(f"GPU可用: {result['available']}")
    logger.info(f"设备列表: {result['devices']}")
    logger.info(f"CUDA版本: {result['cuda_version']}")
    logger.info(f"驱动版本: {result['driver_version']}")
    logger.info(f"PyTorch GPU支持: {result['pytorch_gpu']}")
    logger.info(f"TensorFlow GPU支持: {result['tensorflow_gpu']}")
    
    return result

def _detect_gpu_windows(result):
    """在Windows系统上检测GPU"""
    logger.info("在Windows系统上检测GPU...")
    
    try:
        # 使用nvidia-smi命令检测NVIDIA GPU
        nvidia_smi_output = subprocess.check_output(
            ['nvidia-smi', '--query-gpu=name,memory.total,memory.free,driver_version,compute_cap', '--format=csv,noheader'],
            universal_newlines=True,
            stderr=subprocess.DEVNULL
        )
        
        # 解析输出
        for line in nvidia_smi_output.strip().split('\n'):
            parts = [part.strip() for part in line.split(',')]
            if len(parts) >= 5:
                name = parts[0]
                memory_total = parts[1]
                memory_free = parts[2]
                driver_version = parts[3]
                compute_capability = parts[4]
                
                result['available'] = True
                result['driver_version'] = driver_version
                result['devices'].append({
                    'name': name,
                    'memory_total': memory_total,
                    'memory_free': memory_free,
                    'compute_capability': compute_capability
                })
        
        # 检测CUDA版本
        try:
            nvcc_output = subprocess.check_output(['nvcc', '--version'], universal_newlines=True, stderr=subprocess.DEVNULL)
            cuda_version_match = re.search(r'release (\d+\.\d+)', nvcc_output)
            if cuda_version_match:
                result['cuda_version'] = cuda_version_match.group(1)
        except (subprocess.SubprocessError, FileNotFoundError):
            # 尝试从环境变量获取CUDA版本
            cuda_path = os.environ.get('CUDA_PATH')
            if cuda_path:
                cuda_version_match = re.search(r'v(\d+\.\d+)', cuda_path)
                if cuda_version_match:
                    result['cuda_version'] = cuda_version_match.group(1)
    
    except (subprocess.SubprocessError, FileNotFoundError):
        logger.info("未检测到NVIDIA GPU或nvidia-smi不可用")
    
    # 如果没有检测到NVIDIA GPU，尝试检测AMD GPU
    if not result['available']:
        try:
            # 使用PowerShell获取GPU信息
            powershell_cmd = "Get-CimInstance -ClassName Win32_VideoController | ConvertTo-Json"
            powershell_output = subprocess.check_output(['powershell', '-Command', powershell_cmd], universal_newlines=True)
            
            # 解析PowerShell输出
            gpu_info = json.loads(powershell_output)
            
            # 处理单个GPU或多个GPU的情况
            if isinstance(gpu_info, list):
                gpu_list = gpu_info
            else:
                gpu_list = [gpu_info]
            
            for gpu in gpu_list:
                name = gpu.get('Name', 'Unknown GPU')
                if 'AMD' in name or 'Radeon' in name:
                    result['available'] = True
                    result['devices'].append({
                        'name': name,
                        'type': 'AMD',
                        'memory_total': gpu.get('AdapterRAM', 'Unknown'),
                        'driver_version': gpu.get('DriverVersion', 'Unknown')
                    })
                    if not result['driver_version']:
                        result['driver_version'] = gpu.get('DriverVersion', 'Unknown')
        
        except (subprocess.SubprocessError, json.JSONDecodeError) as e:
            logger.info(f"检测AMD GPU时出错: {str(e)}")
    
    return result

def _detect_gpu_linux(result):
    """在Linux系统上检测GPU"""
    logger.info("在Linux系统上检测GPU...")
    
    try:
        # 使用nvidia-smi命令检测NVIDIA GPU
        nvidia_smi_output = subprocess.check_output(
            ['nvidia-smi', '--query-gpu=name,memory.total,memory.free,driver_version,compute_cap', '--format=csv,noheader'],
            universal_newlines=True,
            stderr=subprocess.DEVNULL
        )
        
        # 解析输出
        for line in nvidia_smi_output.strip().split('\n'):
            parts = [part.strip() for part in line.split(',')]
            if len(parts) >= 5:
                name = parts[0]
                memory_total = parts[1]
                memory_free = parts[2]
                driver_version = parts[3]
                compute_capability = parts[4]
                
                result['available'] = True
                result['driver_version'] = driver_version
                result['devices'].append({
                    'name': name,
                    'memory_total': memory_total,
                    'memory_free': memory_free,
                    'compute_capability': compute_capability
                })
        
        # 检测CUDA版本
        try:
            nvcc_output = subprocess.check_output(['nvcc', '--version'], universal_newlines=True, stderr=subprocess.DEVNULL)
            cuda_version_match = re.search(r'release (\d+\.\d+)', nvcc_output)
            if cuda_version_match:
                result['cuda_version'] = cuda_version_match.group(1)
        except (subprocess.SubprocessError, FileNotFoundError):
            # 尝试使用ldconfig查找CUDA库
            try:
                ldconfig_output = subprocess.check_output(['ldconfig', '-p'], universal_newlines=True)
                cuda_lib_match = re.search(r'libcudart.so.(\d+\.\d+)', ldconfig_output)
                if cuda_lib_match:
                    result['cuda_version'] = cuda_lib_match.group(1)
            except (subprocess.SubprocessError, FileNotFoundError):
                pass
    
    except (subprocess.SubprocessError, FileNotFoundError):
        logger.info("未检测到NVIDIA GPU或nvidia-smi不可用")
    
    # 如果没有检测到NVIDIA GPU，尝试检测AMD GPU
    if not result['available']:
        try:
            # 使用lspci命令检测AMD GPU
            lspci_output = subprocess.check_output(['lspci', '-v'], universal_newlines=True)
            
            # 检查是否有AMD GPU
            if re.search(r'VGA.*AMD|AMD.*\[Radeon', lspci_output):
                # 提取AMD GPU信息
                amd_gpus = re.findall(r'VGA.*AMD.*\n(?:.*\n)*?(?=^\S)', lspci_output + '\n\n', re.MULTILINE)
                
                for gpu_info in amd_gpus:
                    name_match = re.search(r'VGA.*: (AMD.*)', gpu_info)
                    name = name_match.group(1) if name_match else 'AMD GPU'
                    
                    result['available'] = True
                    result['devices'].append({
                        'name': name,
                        'type': 'AMD'
                    })
        
        except (subprocess.SubprocessError, FileNotFoundError) as e:
            logger.info(f"检测AMD GPU时出错: {str(e)}")
    
    return result

def _detect_gpu_macos(result):
    """在macOS系统上检测GPU"""
    logger.info("在macOS系统上检测GPU...")
    
    try:
        # 使用system_profiler命令获取GPU信息
        profiler_output = subprocess.check_output(
            ['system_profiler', 'SPDisplaysDataType', '-json'],
            universal_newlines=True
        )
        
        # 解析JSON输出
        gpu_info = json.loads(profiler_output)
        
        if 'SPDisplaysDataType' in gpu_info:
            for gpu in gpu_info['SPDisplaysDataType']:
                name = gpu.get('sppci_model', 'Unknown GPU')
                gpu_type = 'Integrated' if 'Intel' in name else 'Dedicated'
                
                # 对于Apple Silicon，检查Metal支持
                if 'Apple' in name:
                    result['available'] = True
                    gpu_type = 'Apple Silicon'
                
                # 收集内存信息
                memory = gpu.get('spdisplays_vram', 'Unknown')
                
                result['devices'].append({
                    'name': name,
                    'type': gpu_type,
                    'memory': memory,
                })
                
                # 如果找到独立显卡，标记为可用
                if gpu_type == 'Dedicated' or gpu_type == 'Apple Silicon':
                    result['available'] = True
    
    except (subprocess.SubprocessError, json.JSONDecodeError) as e:
        logger.info(f"检测macOS GPU时出错: {str(e)}")
    
    return result

def _check_pytorch_gpu(result):
    """检测PyTorch是否支持GPU"""
    logger.info("检测PyTorch GPU支持...")
    
    try:
        import torch
        result['pytorch_gpu'] = torch.cuda.is_available()
        
        if result['pytorch_gpu']:
            # 获取PyTorch能够访问的GPU数量
            gpu_count = torch.cuda.device_count()
            logger.info(f"PyTorch可访问的GPU数量: {gpu_count}")
            
            # 获取每个GPU的名称
            for i in range(gpu_count):
                logger.info(f"PyTorch GPU {i}: {torch.cuda.get_device_name(i)}")
            
            # 如果之前没有检测到CUDA版本，从PyTorch获取
            if not result['cuda_version']:
                result['cuda_version'] = torch.version.cuda
            
            # 如果没有检测到GPU可用，但PyTorch认为可用，则更新状态
            if not result['available']:
                result['available'] = True
    
    except ImportError:
        logger.info("未安装PyTorch或导入错误")
    except Exception as e:
        logger.info(f"检测PyTorch GPU支持时出错: {str(e)}")
    
    return result

def _check_tensorflow_gpu(result):
    """检测TensorFlow是否支持GPU"""
    logger.info("检测TensorFlow GPU支持...")
    
    try:
        import tensorflow as tf
        gpus = tf.config.list_physical_devices('GPU')
        result['tensorflow_gpu'] = len(gpus) > 0
        
        if result['tensorflow_gpu']:
            logger.info(f"TensorFlow检测到的GPU: {gpus}")
            
            # 如果没有检测到GPU可用，但TensorFlow认为可用，则更新状态
            if not result['available']:
                result['available'] = True
    
    except ImportError:
        logger.info("未安装TensorFlow或导入错误")
    except Exception as e:
        logger.info(f"检测TensorFlow GPU支持时出错: {str(e)}")
    
    return result

def install_gpu_support():
    """
    安装GPU支持
    
    返回:
        bool: 安装是否成功
    """
    logger.info("开始安装GPU支持...")
    
    # 检测GPU
    gpu_info = detect_gpu()
    
    if not gpu_info['available']:
        logger.warning("未检测到支持的GPU，无法安装GPU支持")
        return False
    
    # 根据检测到的GPU类型安装相应的支持
    if any('NVIDIA' in device.get('name', '') for device in gpu_info['devices']):
        return _install_nvidia_support(gpu_info)
    elif any('AMD' in device.get('name', '') or 'Radeon' in device.get('name', '') for device in gpu_info['devices']):
        return _install_amd_support(gpu_info)
    elif any('Apple' in device.get('name', '') for device in gpu_info['devices']):
        return _install_apple_silicon_support(gpu_info)
    else:
        logger.warning(f"未知GPU类型: {gpu_info['devices']}")
        return False

def _install_nvidia_support(gpu_info):
    """安装NVIDIA GPU支持"""
    logger.info("安装NVIDIA GPU支持...")
    
    success = True
    
    # 检查CUDA版本
    cuda_version = gpu_info.get('cuda_version')
    if not cuda_version:
        logger.warning("未检测到CUDA版本，无法确定兼容的PyTorch/TensorFlow版本")
        cuda_version = "11.8"  # 默认使用较新的CUDA版本
    
    # 安装PyTorch GPU版本
    try:
        if float(cuda_version) >= 11.0:
            logger.info(f"为CUDA {cuda_version}安装PyTorch...")
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install',
                'torch', 'torchvision', 'torchaudio',
                '--index-url', 'https://download.pytorch.org/whl/cu118'
            ])
        elif float(cuda_version) >= 10.2:
            logger.info(f"为CUDA {cuda_version}安装PyTorch...")
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install',
                'torch', 'torchvision', 'torchaudio',
                '--index-url', 'https://download.pytorch.org/whl/cu102'
            ])
        else:
            logger.warning(f"CUDA版本 {cuda_version} 可能不受最新PyTorch支持")
            success = False
    except subprocess.SubprocessError as e:
        logger.error(f"安装PyTorch GPU版本时出错: {str(e)}")
        success = False
    
    # 安装TensorFlow GPU版本
    try:
        logger.info("安装TensorFlow GPU版本...")
        subprocess.check_call([
            sys.executable, '-m', 'pip', 'install', 'tensorflow'
        ])
    except subprocess.SubprocessError as e:
        logger.error(f"安装TensorFlow GPU版本时出错: {str(e)}")
        success = False
    
    # 安装其他GPU加速库
    try:
        logger.info("安装其他GPU加速库...")
        subprocess.check_call([
            sys.executable, '-m', 'pip', 'install', 'cupy-cuda11x', 'numba'
        ])
    except subprocess.SubprocessError as e:
        logger.error(f"安装其他GPU加速库时出错: {str(e)}")
        # 这不是关键依赖，不影响整体成功状态
    
    return success

def _install_amd_support(gpu_info):
    """安装AMD GPU支持"""
    logger.info("安装AMD GPU支持...")
    logger.warning("AMD GPU支持可能有限，某些功能可能不可用")
    
    success = True
    
    # 安装PyTorch ROCm版本（仅在Linux上支持）
    if platform.system() == 'Linux':
        try:
            logger.info("安装PyTorch ROCm版本...")
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install',
                'torch', 'torchvision', 'torchaudio',
                '--index-url', 'https://download.pytorch.org/whl/rocm5.4.2'
            ])
        except subprocess.SubprocessError as e:
            logger.error(f"安装PyTorch ROCm版本时出错: {str(e)}")
            success = False
    else:
        logger.warning("AMD GPU上的PyTorch加速仅在Linux上受支持")
        success = False
    
    # 安装TensorFlow
    try:
        logger.info("安装TensorFlow...")
        subprocess.check_call([
            sys.executable, '-m', 'pip', 'install', 'tensorflow'
        ])
        logger.warning("TensorFlow在AMD GPU上可能不支持硬件加速")
    except subprocess.SubprocessError as e:
        logger.error(f"安装TensorFlow时出错: {str(e)}")
        success = False
    
    return success

def _install_apple_silicon_support(gpu_info):
    """安装Apple Silicon支持"""
    logger.info("安装Apple Silicon GPU支持...")
    
    if platform.system() != 'Darwin':
        logger.error("Apple Silicon支持仅适用于macOS")
        return False
    
    success = True
    
    # 安装PyTorch MPS版本
    try:
        logger.info("安装PyTorch MPS版本...")
        subprocess.check_call([
            sys.executable, '-m', 'pip', 'install',
            'torch', 'torchvision', 'torchaudio',
            '--index-url', 'https://download.pytorch.org/whl/cpu'
        ])
    except subprocess.SubprocessError as e:
        logger.error(f"安装PyTorch MPS版本时出错: {str(e)}")
        success = False
    
    # 安装TensorFlow Metal版本
    try:
        logger.info("安装TensorFlow Metal版本...")
        subprocess.check_call([
            sys.executable, '-m', 'pip', 'install', 'tensorflow-macos', 'tensorflow-metal'
        ])
    except subprocess.SubprocessError as e:
        logger.error(f"安装TensorFlow Metal版本时出错: {str(e)}")
        success = False
    
    return success

def main():
    """主函数"""
    # 检测GPU
    gpu_info = detect_gpu()
    
    # 显示检测结果
    print("\nGPU检测结果:")
    print(f"可用: {'是' if gpu_info['available'] else '否'}")
    print(f"CUDA版本: {gpu_info['cuda_version'] or '未检测到'}")
    print(f"驱动版本: {gpu_info['driver_version'] or '未检测到'}")
    print(f"PyTorch GPU支持: {'是' if gpu_info['pytorch_gpu'] else '否'}")
    print(f"TensorFlow GPU支持: {'是' if gpu_info['tensorflow_gpu'] else '否'}")
    
    print("\n检测到的GPU设备:")
    for i, device in enumerate(gpu_info['devices']):
        print(f"  设备 {i+1}: {device.get('name', '未知名称')}")
        for key, value in device.items():
            if key != 'name':
                print(f"    {key}: {value}")
    
    # 询问是否安装GPU支持
    if gpu_info['available'] and not (gpu_info['pytorch_gpu'] and gpu_info['tensorflow_gpu']):
        print("\n检测到GPU，但部分深度学习框架未启用GPU支持。")
        choice = input("是否要安装GPU支持? (y/n): ").strip().lower()
        
        if choice == 'y':
            success = install_gpu_support()
            if success:
                print("\nGPU支持安装成功！")
            else:
                print("\nGPU支持安装部分失败，请检查日志获取详细信息。")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
