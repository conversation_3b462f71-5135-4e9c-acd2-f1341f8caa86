"""
鲁棒模块测试脚本
测试新开发的市场分析、风险管理、验证和评估等模块
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime
import akshare as ak
import warnings

# 忽略特定警告
warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", category=RuntimeWarning)

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/robust_modules_test.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger('robust_modules_test')

# 导入自定义模块
from quant_project.core_logic.market_analysis import MarketConditionDetector, MarketCondition
from quant_project.core_logic.risk_management import RiskManager, StopLossType, PositionSizingMethod
from quant_project.core_logic.validation import TimeSeriesCV, CVMethod, OverfittingDetector, MarketConditionCV
from quant_project.core_logic.evaluation import ModelEvaluator
from quant_project.core_logic.data_handling import EnhancedDataValidator
from quant_project.core_logic.trading_env import RobustTradingEnvironment

def get_test_data():
    """获取测试数据"""
    logger.info("获取测试数据")
    
    # 尝试从缓存加载数据
    cache_file = 'data_cache/test_data.csv'
    if os.path.exists(cache_file):
        logger.info(f"从缓存加载数据: {cache_file}")
        df = pd.read_csv(cache_file, index_col=0, parse_dates=True)
        return df
    
    # 如果缓存不存在，从akshare获取数据
    try:
        # 获取上证指数历史数据
        logger.info("从akshare获取上证指数历史数据")
        df = ak.stock_zh_index_daily(symbol="sh000001")
        
        # 重命名列
        df.rename(columns={
            'date': '日期',
            'open': '开盘',
            'high': '最高',
            'low': '最低',
            'close': '收盘',
            'volume': '成交量'
        }, inplace=True)
        
        # 设置日期为索引
        df['日期'] = pd.to_datetime(df['日期'])
        df.set_index('日期', inplace=True)
        
        # 确保数据按日期排序
        df.sort_index(inplace=True)
        
        # 保存到缓存
        os.makedirs(os.path.dirname(cache_file), exist_ok=True)
        df.to_csv(cache_file)
        
        return df
    except Exception as e:
        logger.error(f"获取数据失败: {str(e)}")
        # 创建模拟数据
        logger.info("创建模拟数据")
        dates = pd.date_range(start='2020-01-01', end='2022-12-31', freq='B')
        n = len(dates)
        
        # 创建模拟价格
        price = 3000
        prices = [price]
        for _ in range(1, n):
            change = np.random.normal(0, 1) * 30
            price += change
            prices.append(price)
        
        # 创建模拟成交量
        volumes = np.random.randint(10000, 1000000, size=n)
        
        # 创建数据框
        df = pd.DataFrame({
            '开盘': prices,
            '最高': [p * (1 + np.random.uniform(0, 0.02)) for p in prices],
            '最低': [p * (1 - np.random.uniform(0, 0.02)) for p in prices],
            '收盘': [p * (1 + np.random.normal(0, 0.01)) for p in prices],
            '成交量': volumes
        }, index=dates)
        
        # 保存到缓存
        os.makedirs(os.path.dirname(cache_file), exist_ok=True)
        df.to_csv(cache_file)
        
        return df

def test_market_condition_detector():
    """测试市场状态检测器"""
    logger.info("测试市场状态检测器")
    
    # 获取测试数据
    df = get_test_data()
    
    # 创建市场状态检测器
    detector = MarketConditionDetector(logger=logger)
    
    # 检测整个数据集的市场状态
    result = detector.detect_market_condition(df)
    
    # 输出结果
    logger.info(f"市场状态: {result['primary_condition'].name}")
    logger.info(f"市场状态指标: {result['metrics']}")
    
    # 检测不同时间段的市场状态
    window_size = 60
    market_conditions = []
    
    for i in range(window_size, len(df), window_size):
        end_idx = min(i + window_size, len(df))
        window_data = df.iloc[i-window_size:end_idx]
        
        result = detector.detect_market_condition(window_data)
        market_conditions.append({
            'start_date': window_data.index[0],
            'end_date': window_data.index[-1],
            'condition': result['primary_condition'].name,
            'metrics': result['metrics']
        })
    
    # 输出不同时间段的市场状态
    logger.info(f"检测到 {len(market_conditions)} 个时间段的市场状态")
    for i, mc in enumerate(market_conditions[:5]):
        logger.info(f"时间段 {i+1}: {mc['start_date']} 至 {mc['end_date']}, 状态: {mc['condition']}")
    
    # 统计各种市场状态的数量
    condition_counts = {}
    for mc in market_conditions:
        condition = mc['condition']
        condition_counts[condition] = condition_counts.get(condition, 0) + 1
    
    logger.info(f"市场状态统计: {condition_counts}")
    
    return True

def test_risk_manager():
    """测试风险管理器"""
    logger.info("测试风险管理器")
    
    # 获取测试数据
    df = get_test_data()
    
    # 创建市场状态检测器
    detector = MarketConditionDetector(logger=logger)
    
    # 创建风险管理器
    stop_loss_config = {
        'type': StopLossType.VOLATILITY,
        'threshold': 0.05,  # 5%止损
        'atr_multiplier': 3.0,
        'trailing_percent': 0.02,
        'max_loss': 0.1,
        'time_stop': 10
    }
    
    position_sizing_config = {
        'method': PositionSizingMethod.VOLATILITY,
        'max_position': 1.0,
        'risk_per_trade': 0.01,
        'target_volatility': 0.15,
        'kelly_fraction': 0.5
    }
    
    risk_manager = RiskManager(
        stop_loss_config=stop_loss_config,
        position_sizing_config=position_sizing_config,
        market_condition_detector=detector,
        logger=logger
    )
    
    # 模拟交易过程
    initial_capital = 100000
    current_capital = initial_capital
    position = 0
    entry_price = 0
    trades = []
    
    # 计算波动率
    returns = df['收盘'].pct_change().dropna()
    volatility = returns.std() * np.sqrt(252)  # 年化波动率
    
    # 模拟交易
    for i in range(60, len(df) - 1):
        # 获取当前数据
        current_data = df.iloc[:i+1]
        current_price = current_data['收盘'].iloc[-1]
        
        # 更新市场状态
        risk_manager.update_market_condition(current_data)
        
        # 如果没有持仓，考虑买入
        if position == 0:
            # 计算仓位大小
            position_size = risk_manager.calculate_position_size(
                price=current_price,
                volatility=volatility,
                capital=current_capital
            )
            
            # 买入
            position = position_size
            entry_price = current_price
            cost = position * current_price
            current_capital -= cost
            
            trades.append({
                'date': current_data.index[-1],
                'action': 'buy',
                'price': current_price,
                'position': position,
                'cost': cost,
                'capital': current_capital
            })
            
            logger.info(f"买入: 日期={current_data.index[-1]}, 价格={current_price}, 仓位={position}, 成本={cost}")
        
        # 如果有持仓，检查是否需要卖出
        else:
            # 检查止损
            stop_loss_triggered = risk_manager.check_stop_loss(
                current_price=current_price,
                position=position,
                entry_price=entry_price
            )
            
            # 检查止盈
            take_profit_triggered = risk_manager.check_take_profit(
                current_price=current_price,
                position=position,
                entry_price=entry_price
            )
            
            # 如果触发止损或止盈，卖出
            if stop_loss_triggered or take_profit_triggered:
                revenue = position * current_price
                current_capital += revenue
                
                trades.append({
                    'date': current_data.index[-1],
                    'action': 'sell',
                    'price': current_price,
                    'position': position,
                    'revenue': revenue,
                    'capital': current_capital,
                    'profit': revenue - (position * entry_price),
                    'trigger': 'stop_loss' if stop_loss_triggered else 'take_profit'
                })
                
                logger.info(f"卖出: 日期={current_data.index[-1]}, 价格={current_price}, 仓位={position}, 收入={revenue}, 触发={'止损' if stop_loss_triggered else '止盈'}")
                
                # 重置持仓
                position = 0
                entry_price = 0
    
    # 统计交易结果
    if trades:
        buy_trades = [t for t in trades if t['action'] == 'buy']
        sell_trades = [t for t in trades if t['action'] == 'sell']
        
        total_trades = len(sell_trades)
        profitable_trades = len([t for t in sell_trades if t.get('profit', 0) > 0])
        win_rate = profitable_trades / total_trades if total_trades > 0 else 0
        
        total_profit = sum([t.get('profit', 0) for t in sell_trades])
        profit_rate = total_profit / initial_capital
        
        logger.info(f"交易统计: 总交易={total_trades}, 盈利交易={profitable_trades}, 胜率={win_rate:.2%}")
        logger.info(f"盈亏统计: 总盈亏={total_profit:.2f}, 盈亏率={profit_rate:.2%}")
        
        # 统计止损和止盈触发情况
        stop_loss_count = len([t for t in sell_trades if t.get('trigger') == 'stop_loss'])
        take_profit_count = len([t for t in sell_trades if t.get('trigger') == 'take_profit'])
        
        logger.info(f"触发统计: 止损={stop_loss_count}, 止盈={take_profit_count}")
    
    return True

def test_validation():
    """测试验证模块"""
    logger.info("测试验证模块")
    
    # 获取测试数据
    df = get_test_data()
    
    # 创建特征
    df['收益率'] = df['收盘'].pct_change()
    df['波动率'] = df['收益率'].rolling(window=20).std() * np.sqrt(252)
    df['SMA5'] = df['收盘'].rolling(window=5).mean()
    df['SMA20'] = df['收盘'].rolling(window=20).mean()
    df['SMA60'] = df['收盘'].rolling(window=60).mean()
    df['RSI'] = 100 - (100 / (1 + (df['收益率'].rolling(window=14).apply(lambda x: sum(x[x > 0]) / sum(abs(x[x < 0])) if sum(abs(x[x < 0])) != 0 else 9999))))
    
    # 删除缺失值
    df.dropna(inplace=True)
    
    # 创建目标变量（下一天的收益率）
    df['目标'] = df['收益率'].shift(-1)
    df.dropna(inplace=True)
    
    # 准备特征和目标
    X = df[['收益率', '波动率', 'SMA5', 'SMA20', 'SMA60', 'RSI', '收盘']]
    y = df['目标']
    
    # 测试时间序列交叉验证
    logger.info("测试时间序列交叉验证")
    
    # 创建交叉验证器
    cv = TimeSeriesCV(
        method=CVMethod.ROLLING_WINDOW,
        n_splits=5,
        train_size=None,
        test_size=None,
        gap=0,
        embargo=0,
        logger=logger
    )
    
    # 获取分割
    splits = list(cv.split(X))
    logger.info(f"交叉验证分割数量: {len(splits)}")
    
    for i, (train_index, test_index) in enumerate(splits):
        logger.info(f"分割 {i+1}: 训练集大小={len(train_index)}, 测试集大小={len(test_index)}")
    
    # 测试市场状态交叉验证
    logger.info("测试市场状态交叉验证")
    
    # 创建市场状态检测器
    detector = MarketConditionDetector(logger=logger)
    
    # 创建市场状态交叉验证器
    market_cv = MarketConditionCV(
        market_detector=detector,
        cv_method=CVMethod.ROLLING_WINDOW,
        n_splits=5,
        train_size=None,
        test_size=None,
        gap=0,
        embargo=0,
        window_size=60,
        logger=logger
    )
    
    # 获取市场状态分布
    market_distribution = market_cv.get_market_condition_distribution(X)
    logger.info(f"市场状态分布: {market_distribution}")
    
    # 获取市场状态时间线
    market_timeline = market_cv.get_market_condition_timeline(X)
    logger.info(f"市场状态时间线长度: {len(market_timeline)}")
    
    # 测试过拟合检测器
    logger.info("测试过拟合检测器")
    
    # 创建过拟合检测器
    overfitting_detector = OverfittingDetector(logger=logger)
    
    # 模拟训练集和测试集评分
    train_scores = [0.6, 0.65, 0.7, 0.75, 0.8]
    test_scores = [0.55, 0.58, 0.6, 0.61, 0.62]
    
    # 检测过拟合
    is_overfitting, overfitting_degree, details = overfitting_detector.detect_overfitting(
        train_scores=train_scores,
        test_scores=test_scores,
        threshold=0.1
    )
    
    logger.info(f"过拟合检测结果: 是否过拟合={is_overfitting}, 过拟合程度={overfitting_degree:.4f}")
    logger.info(f"过拟合详情: {details}")
    
    # 获取正则化建议
    suggestions = overfitting_detector.suggest_regularization(overfitting_degree)
    logger.info(f"正则化建议: {suggestions}")
    
    return True

def test_enhanced_data_validator():
    """测试增强数据验证器"""
    logger.info("测试增强数据验证器")
    
    # 获取测试数据
    df = get_test_data()
    
    # 创建增强数据验证器
    validator = EnhancedDataValidator(logger=logger)
    
    # 验证数据
    is_valid, error_msg = validator.validate_data(df)
    logger.info(f"数据验证结果: 是否有效={is_valid}, 错误信息={error_msg}")
    
    # 检查数据质量
    quality_score = validator.check_data_quality(df)
    logger.info(f"数据质量评分: {quality_score}")
    
    return True

def test_robust_trading_environment():
    """测试鲁棒交易环境"""
    logger.info("测试鲁棒交易环境")
    
    # 获取测试数据
    df = get_test_data()
    
    # 创建市场状态检测器
    detector = MarketConditionDetector(logger=logger)
    
    # 创建风险管理器
    risk_manager = RiskManager(
        stop_loss_config={
            'type': StopLossType.VOLATILITY,
            'threshold': 0.05
        },
        position_sizing_config={
            'method': PositionSizingMethod.VOLATILITY,
            'max_position': 1.0
        },
        market_condition_detector=detector,
        logger=logger
    )
    
    # 创建鲁棒交易环境
    env = RobustTradingEnvironment(
        df_processed_data=df,
        initial_capital=100000,
        commission_rate=0.0003,
        min_hold_days=3,
        allow_short=False,
        max_position=1.0,
        window_size=20,
        market_condition_detector=detector,
        risk_manager=risk_manager,
        adaptive_reward=True,
        market_regime_aware=True
    )
    
    # 重置环境
    observation, info = env.reset()
    logger.info(f"环境重置，观测形状: {observation.shape}")
    
    # 执行随机动作
    total_steps = 100
    total_reward = 0
    
    for _ in range(total_steps):
        action = np.random.choice([0, 1, 2])  # 0=保持, 1=买入, 2=卖出
        observation, reward, terminated, truncated, info = env.step(action)
        total_reward += reward
        
        if terminated or truncated:
            break
    
    logger.info(f"执行 {total_steps} 步，总奖励: {total_reward}")
    logger.info(f"最终信息: {info}")
    
    return True

def main():
    """主函数"""
    logger.info("开始测试鲁棒模块")
    
    # 创建测试结果目录
    os.makedirs('test_reports', exist_ok=True)
    
    # 测试市场状态检测器
    try:
        test_market_condition_detector()
        logger.info("市场状态检测器测试通过")
    except Exception as e:
        logger.error(f"市场状态检测器测试失败: {str(e)}")
    
    # 测试风险管理器
    try:
        test_risk_manager()
        logger.info("风险管理器测试通过")
    except Exception as e:
        logger.error(f"风险管理器测试失败: {str(e)}")
    
    # 测试验证模块
    try:
        test_validation()
        logger.info("验证模块测试通过")
    except Exception as e:
        logger.error(f"验证模块测试失败: {str(e)}")
    
    # 测试增强数据验证器
    try:
        test_enhanced_data_validator()
        logger.info("增强数据验证器测试通过")
    except Exception as e:
        logger.error(f"增强数据验证器测试失败: {str(e)}")
    
    # 测试鲁棒交易环境
    try:
        test_robust_trading_environment()
        logger.info("鲁棒交易环境测试通过")
    except Exception as e:
        logger.error(f"鲁棒交易环境测试失败: {str(e)}")
    
    logger.info("鲁棒模块测试完成")

if __name__ == "__main__":
    main()
