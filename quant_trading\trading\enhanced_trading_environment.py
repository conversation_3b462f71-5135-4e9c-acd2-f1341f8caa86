"""
增强交易环境模块
实现高级交易环境，包括更灵活的动作空间、更丰富的观测空间和更复杂的奖励函数
符合顶尖量化基金的最佳实践
"""

import logging
import numpy as np
import pandas as pd
import gymnasium as gym
from gymnasium import spaces
from typing import Dict, List, Tuple, Optional, Union

class EnhancedTradingEnvironment(gym.Env):
    """
    增强交易环境类
    实现高级交易环境，包括更灵活的动作空间、更丰富的观测空间和更复杂的奖励函数
    """

    metadata = {'render_modes': ['human']}

    def __init__(
        self,
        df_processed_data: pd.DataFrame,
        initial_capital: float = 100000.0,
        commission_rate: float = 0.0003,
        min_hold_days: int = 3,
        allow_short: bool = False,
        max_position: float = 1.0,
        reward_config: Optional[Dict] = None,
        window_size: int = 20,
        action_type: str = 'discrete',
        n_discrete_actions: int = 3,
        render_mode: Optional[str] = None,
        slippage_model: str = 'percentage',  # 'percentage', 'fixed', 'none'
        slippage_value: float = 0.001,  # 0.1% 滑点
        enable_stop_loss: bool = True,
        stop_loss_threshold: float = 0.05,  # 5% 止损
        enable_take_profit: bool = True,
        take_profit_threshold: float = 0.1,  # 10% 止盈
        dynamic_risk_management: bool = True,
        volatility_scaling: bool = True,
        compatibility_mode: bool = False  # 是否启用兼容模式（与TradingEnvironment兼容）
    ):
        """
        初始化交易环境

        参数:
            df_processed_data (pandas.DataFrame): 处理后的行情数据，必须包含OHLCV列
            initial_capital (float): 初始资金
            commission_rate (float): 交易手续费率
            min_hold_days (int): 最小持有天数
            allow_short (bool): 是否允许做空
            max_position (float): 最大仓位比例
            reward_config (dict, optional): 奖励函数配置
            window_size (int): 观测窗口大小
            action_type (str): 动作类型，'discrete'或'continuous'
            n_discrete_actions (int): 离散动作数量（仅当action_type='discrete'时有效）
            render_mode (str, optional): 渲染模式
            slippage_model (str): 滑点模型，'percentage'(百分比), 'fixed'(固定值), 'none'(无滑点)
            slippage_value (float): 滑点值，对于百分比模型表示价格的百分比，对于固定模型表示固定金额
            enable_stop_loss (bool): 是否启用止损
            stop_loss_threshold (float): 止损阈值，表示亏损比例
            enable_take_profit (bool): 是否启用止盈
            take_profit_threshold (float): 止盈阈值，表示盈利比例
            dynamic_risk_management (bool): 是否启用动态风险管理
            volatility_scaling (bool): 是否根据波动率调整仓位
            compatibility_mode (bool): 是否启用兼容模式，使环境返回与TradingEnvironment相同格式的观测
        """
        super(EnhancedTradingEnvironment, self).__init__()

        self.logger = logging.getLogger('drl_trading')

        # 数据相关
        self.df = df_processed_data.copy()
        self.window_size = window_size

        # 交易相关
        self.initial_capital = initial_capital
        self.commission_rate = commission_rate
        self.min_hold_days = min_hold_days
        self.allow_short = allow_short
        self.max_position = max_position

        # 奖励相关
        self.reward_config = reward_config or {
            'return_weight': 1.0,
            'volatility_weight': -1.0,
            'drawdown_weight': -1.0,
            'holding_penalty': -0.0001,
            'turnover_penalty': -0.001,
            'monthly_return_target': 0.10,  # 10%
            'monthly_drawdown_limit': 0.04,  # 4%
            'monthly_sharpe_target': 1.5,   # 1.5
            'target_achievement_weight': 2.0,
            'progressive_reward_factor': 1.0
        }

        # 动作相关
        self.action_type = action_type
        self.n_discrete_actions = n_discrete_actions

        # 渲染相关
        self.render_mode = render_mode

        # 市场模拟相关
        self.slippage_model = slippage_model
        self.slippage_value = slippage_value

        # 风险管理相关
        self.enable_stop_loss = enable_stop_loss
        self.stop_loss_threshold = stop_loss_threshold
        self.enable_take_profit = enable_take_profit
        self.take_profit_threshold = take_profit_threshold
        self.dynamic_risk_management = dynamic_risk_management
        self.volatility_scaling = volatility_scaling

        # 兼容模式设置
        self.compatibility_mode = compatibility_mode
        if self.compatibility_mode:
            self.logger.info("增强型交易环境启用兼容模式，将返回扁平观测以兼容基础模型")

        # 交易记录相关
        self.entry_price = 0.0  # 入场价格，用于计算止盈止损
        self.position_direction = 0  # 1表示多头，-1表示空头，0表示无持仓

        # 定义动作空间
        if self.action_type == 'discrete':
            # 离散动作空间
            self.action_space = spaces.Discrete(n_discrete_actions)
        else:
            # 连续动作空间
            self.action_space = spaces.Box(
                low=-1.0 if self.allow_short else 0.0,
                high=1.0,
                shape=(1,),
                dtype=np.float32
            )

        # 定义观测空间
        # 观测空间包括：
        # 1. 市场数据特征（window_size x n_features）
        # 2. 账户状态（持仓、持仓天数、现金比例等）
        n_features = len(self.df.columns)

        if self.compatibility_mode:
            # 在兼容模式下，使用与TradingEnvironment相同的Box空间
            # 计算扁平化后的观测空间大小
            flat_market_data_size = self.window_size * n_features
            account_state_size = 5  # [position, holding_days, cash_ratio, portfolio_value, unrealized_pnl]
            total_obs_size = flat_market_data_size + account_state_size

            self.observation_space = spaces.Box(
                low=-10.0,
                high=10.0,
                shape=(total_obs_size,),
                dtype=np.float32
            )
            self.logger.info(f"兼容模式：使用Box观测空间，形状为 {(total_obs_size,)}")
        else:
            # 在标准模式下，使用Dict空间
            self.observation_space = spaces.Dict({
                'market_data': spaces.Box(
                    low=-np.inf,
                    high=np.inf,
                    shape=(self.window_size, n_features),
                    dtype=np.float32
                ),
                'account_state': spaces.Box(
                    low=-np.inf,
                    high=np.inf,
                    shape=(5,),  # [position, holding_days, cash_ratio, portfolio_value, unrealized_pnl]
                    dtype=np.float32
                )
            })

        # 初始化环境状态
        self.reset()

    def reset(self, seed=None, options=None):
        """
        重置环境

        参数:
            seed (int, optional): 随机种子
            options (dict, optional): 重置选项

        返回:
            tuple: (observation, info)
        """
        super().reset(seed=seed)

        # 设置当前时间步
        self.current_step = self.window_size

        # 初始化账户状态
        self.portfolio_value = self.initial_capital
        self.cash = self.initial_capital
        self.position = 0.0
        self.position_value = 0.0
        self.holding_days = 0
        self.last_action = 0

        # 初始化风险管理相关状态
        self.entry_price = 0.0
        self.position_direction = 0
        self.stop_loss_triggered = False
        self.take_profit_triggered = False

        # 初始化波动率缩放因子
        self.volatility_scaling_factor = 1.0

        # 初始化动态风险管理参数
        self.dynamic_stop_loss_threshold = self.stop_loss_threshold
        self.dynamic_take_profit_threshold = self.take_profit_threshold

        # 初始化交易历史
        self.trades = []
        self.portfolio_values = [self.portfolio_value]
        self.positions = [self.position]
        self.returns = [0.0]
        self.actions = [0]
        self.stop_loss_events = []
        self.take_profit_events = []

        # 获取初始观测
        observation = self._get_observation()

        # 获取初始信息
        info = self._get_info()

        return observation, info

    def step(self, action):
        """
        执行一步交易

        参数:
            action: 交易动作

        返回:
            tuple: (observation, reward, terminated, truncated, info)
        """
        # 保存上一步的投资组合价值
        prev_portfolio_value = self.portfolio_value

        # 获取当前价格
        current_price = self.df.iloc[self.current_step]['收盘']

        # 重置止盈止损触发标志
        self.stop_loss_triggered = False
        self.take_profit_triggered = False

        # 如果启用了动态风险管理，更新风险管理参数
        if self.dynamic_risk_management:
            self._update_risk_parameters()

        # 如果启用了波动率缩放，计算波动率缩放因子
        if self.volatility_scaling:
            self._calculate_volatility_scaling()

        # 检查是否触发止盈止损
        stop_action = self._check_stop_loss_take_profit(current_price)

        # 如果触发了止盈止损，使用止盈止损动作替代原始动作
        if stop_action is not None:
            self.logger.info(f"触发{'止损' if self.stop_loss_triggered else '止盈'}，原始动作 {action} 被替换为 {stop_action}")
            action = stop_action

        # 执行交易（包含滑点模拟）
        self._execute_trade(action, current_price)

        # 更新持仓天数
        if self.position != 0:
            self.holding_days += 1
        else:
            self.holding_days = 0

        # 更新当前时间步
        self.current_step += 1

        # 计算新的投资组合价值
        new_price = self.df.iloc[self.current_step]['收盘']
        self.position_value = self.position * new_price
        self.portfolio_value = self.cash + self.position_value

        # 记录历史
        self.portfolio_values.append(self.portfolio_value)
        self.positions.append(self.position)
        self.returns.append((self.portfolio_value / prev_portfolio_value) - 1)
        self.actions.append(action)

        # 如果触发了止盈止损，记录事件
        if self.stop_loss_triggered:
            self.stop_loss_events.append({
                'step': self.current_step,
                'date': self.df.index[self.current_step],
                'price': current_price,
                'position': self.position,
                'threshold': self.dynamic_stop_loss_threshold
            })

        if self.take_profit_triggered:
            self.take_profit_events.append({
                'step': self.current_step,
                'date': self.df.index[self.current_step],
                'price': current_price,
                'position': self.position,
                'threshold': self.dynamic_take_profit_threshold
            })

        # 获取观测
        observation = self._get_observation()

        # 计算奖励
        reward = self._calculate_reward()

        # 检查是否结束
        terminated = self.current_step >= len(self.df) - 1
        truncated = False

        # 获取信息
        info = self._get_info()

        # 渲染
        if self.render_mode == 'human':
            self.render()

        return observation, reward, terminated, truncated, info

    def _update_risk_parameters(self):
        """
        更新动态风险管理参数

        根据市场状况和投资组合表现动态调整止盈止损阈值
        """
        # 计算最近的波动率
        if len(self.returns) >= 10:
            recent_volatility = np.std(self.returns[-10:]) * np.sqrt(252)  # 年化波动率

            # 根据波动率调整止损阈值（波动率越高，止损越严格）
            base_stop_loss = self.stop_loss_threshold
            volatility_factor = min(3.0, max(0.5, recent_volatility / 0.2))  # 假设0.2是正常波动率
            self.dynamic_stop_loss_threshold = base_stop_loss / volatility_factor

            # 根据波动率调整止盈阈值（波动率越高，止盈越宽松）
            base_take_profit = self.take_profit_threshold
            self.dynamic_take_profit_threshold = base_take_profit * volatility_factor

            # 限制阈值范围
            self.dynamic_stop_loss_threshold = max(0.01, min(0.1, self.dynamic_stop_loss_threshold))
            self.dynamic_take_profit_threshold = max(0.05, min(0.3, self.dynamic_take_profit_threshold))

    def _calculate_volatility_scaling(self):
        """
        计算波动率缩放因子

        根据市场波动率调整仓位大小，波动率高时减少仓位，波动率低时增加仓位
        """
        # 计算最近的波动率
        if len(self.returns) >= 20:
            recent_volatility = np.std(self.returns[-20:]) * np.sqrt(252)  # 年化波动率
            target_volatility = 0.15  # 目标年化波动率

            # 计算缩放因子
            if recent_volatility > 0:
                self.volatility_scaling_factor = target_volatility / recent_volatility

                # 限制缩放因子范围
                self.volatility_scaling_factor = max(0.2, min(2.0, self.volatility_scaling_factor))
            else:
                self.volatility_scaling_factor = 1.0
        else:
            self.volatility_scaling_factor = 1.0

    def _check_stop_loss_take_profit(self, current_price):
        """
        检查是否触发止盈止损

        参数:
            current_price (float): 当前价格

        返回:
            action: 如果触发止盈止损，返回相应的动作；否则返回None
        """
        # 如果没有持仓，不需要检查止盈止损
        if self.position == 0 or self.entry_price == 0:
            return None

        # 计算当前盈亏比例
        if self.position_direction > 0:  # 多头
            profit_ratio = (current_price - self.entry_price) / self.entry_price
        else:  # 空头
            profit_ratio = (self.entry_price - current_price) / self.entry_price

        # 检查是否触发止损
        if self.enable_stop_loss and profit_ratio < -self.dynamic_stop_loss_threshold:
            self.stop_loss_triggered = True
            # 返回平仓动作
            return 0 if self.action_type == 'discrete' else np.array([0.0])

        # 检查是否触发止盈
        if self.enable_take_profit and profit_ratio > self.dynamic_take_profit_threshold:
            self.take_profit_triggered = True
            # 返回平仓动作
            return 0 if self.action_type == 'discrete' else np.array([0.0])

        return None

    def _execute_trade(self, action, price):
        """
        执行交易

        参数:
            action: 交易动作
            price (float): 交易价格
        """
        # 解析动作
        target_position = self._action_to_position(action)

        # 如果启用了波动率缩放，调整目标仓位
        if self.volatility_scaling and not (self.stop_loss_triggered or self.take_profit_triggered):
            target_position *= self.volatility_scaling_factor

        # 如果持仓天数小于最小持有天数，且当前有持仓，则不允许减仓
        if self.holding_days < self.min_hold_days and self.position > 0 and target_position < self.position:
            target_position = self.position

        # 如果持仓天数小于最小持有天数，且当前有空仓，则不允许减空仓
        if self.holding_days < self.min_hold_days and self.position < 0 and target_position > self.position:
            target_position = self.position

        # 计算交易数量
        trade_size = target_position - self.position

        # 如果交易数量不为0，执行交易
        if abs(trade_size) > 1e-6:
            # 应用滑点模型计算实际交易价格
            actual_price = self._apply_slippage(price, trade_size > 0)

            # 计算交易金额
            trade_value = abs(trade_size * actual_price)

            # 计算手续费
            commission = trade_value * self.commission_rate

            # 更新现金和持仓
            self.cash -= (trade_size * actual_price + commission)
            self.position = target_position

            # 更新入场价格和持仓方向（用于止盈止损计算）
            if (self.position > 0 and self.position_direction <= 0) or (self.position < 0 and self.position_direction >= 0):
                # 仓位方向发生变化，更新入场价格
                self.entry_price = actual_price
                self.position_direction = 1 if self.position > 0 else -1
            elif self.position == 0:
                # 平仓，重置入场价格和持仓方向
                self.entry_price = 0.0
                self.position_direction = 0

            # 记录交易，使用与TradingEnvironment一致的格式
            if trade_size > 0:  # 买入
                self.trades.append({
                    'step': self.current_step,
                    'date': self.df.index[self.current_step],
                    'action': 'buy',
                    'price': actual_price,
                    'shares': trade_size,
                    'cost': trade_value,
                    'commission': commission,
                    'slippage': actual_price - price if price > 0 else 0
                })
            else:  # 卖出
                self.trades.append({
                    'step': self.current_step,
                    'date': self.df.index[self.current_step],
                    'action': 'sell',
                    'price': actual_price,
                    'shares': abs(trade_size),
                    'revenue': trade_value,
                    'commission': commission,
                    'slippage': price - actual_price if price > 0 else 0
                })

    def _apply_slippage(self, price, is_buy):
        """
        应用滑点模型计算实际交易价格

        参数:
            price (float): 基准价格
            is_buy (bool): 是否为买入交易

        返回:
            float: 应用滑点后的实际交易价格
        """
        if self.slippage_model == 'none' or price <= 0:
            return price

        if self.slippage_model == 'percentage':
            # 百分比滑点模型
            slippage_factor = 1 + (self.slippage_value if is_buy else -self.slippage_value)
            return price * slippage_factor

        elif self.slippage_model == 'fixed':
            # 固定金额滑点模型
            return price + (self.slippage_value if is_buy else -self.slippage_value)

        return price

    def _action_to_position(self, action):
        """
        将动作转换为目标仓位

        参数:
            action: 交易动作

        返回:
            float: 目标仓位
        """
        if self.action_type == 'discrete':
            # 离散动作
            if self.n_discrete_actions == 3:
                # 0: 空仓, 1: 满仓, 2: 空仓
                if action == 0:  # 空仓
                    return 0.0
                elif action == 1:  # 满仓
                    return self.max_position
                else:  # 空仓
                    return -self.max_position if self.allow_short else 0.0
            else:
                # 将离散动作映射到[-max_position, max_position]范围
                action_range = 2 * self.max_position if self.allow_short else self.max_position
                position_step = action_range / (self.n_discrete_actions - 1)
                target_position = -self.max_position if self.allow_short else 0.0
                target_position += action * position_step
                return target_position
        else:
            # 连续动作
            # 将[-1, 1]范围的动作映射到[-max_position, max_position]范围
            if self.allow_short:
                return action[0] * self.max_position
            else:
                return max(0.0, action[0]) * self.max_position

    def _get_observation(self):
        """
        获取观测

        返回:
            dict 或 numpy.ndarray: 观测（根据compatibility_mode决定返回类型）
        """
        # 获取市场数据
        market_data = self.df.iloc[self.current_step - self.window_size + 1:self.current_step + 1].values

        # 获取账户状态
        account_state = np.array([
            self.position,  # 当前持仓
            self.holding_days,  # 持仓天数
            self.cash / self.portfolio_value,  # 现金比例
            self.portfolio_value / self.initial_capital,  # 投资组合价值相对于初始资金
            (self.position_value - self.position * self.df.iloc[self.current_step - 1]['收盘']) / self.portfolio_value if self.position != 0 else 0.0  # 未实现盈亏
        ], dtype=np.float32)

        # 检查是否需要兼容模式（与TradingEnvironment兼容）
        if hasattr(self, 'compatibility_mode') and self.compatibility_mode:
            # 将字典观测转换为扁平数组，与TradingEnvironment兼容
            # 1. 展平市场数据
            flat_market_data = market_data.flatten()

            # 2. 组合观测向量
            observation = np.concatenate([flat_market_data, account_state])

            # 3. 确保观测值在合理范围内
            observation = np.clip(observation, -10.0, 10.0)

            self.logger.debug(f"使用兼容模式，返回扁平观测，形状: {observation.shape}")
            return observation.astype(np.float32)
        else:
            # 返回字典观测
            return {
                'market_data': market_data.astype(np.float32),
                'account_state': account_state
            }

    def _calculate_reward(self):
        """
        计算奖励

        直接将目标纳入奖励函数：
        - 月度收益>10%
        - 月度最大回撤<4%
        - 月度夏普>1.5

        实现多阶段奖励：短期奖励（每次交易）与长期奖励（月度绩效）相结合
        加入风险调整因子：不仅奖励高收益，更奖励风险调整后的收益

        返回:
            float: 奖励
        """
        # 获取奖励权重
        return_weight = self.reward_config.get('return_weight', 1.0)
        volatility_weight = self.reward_config.get('volatility_weight', -1.0)
        drawdown_weight = self.reward_config.get('drawdown_weight', -1.0)
        holding_penalty = self.reward_config.get('holding_penalty', -0.0001)
        turnover_penalty = self.reward_config.get('turnover_penalty', -0.001)

        # 新增目标相关权重
        monthly_return_target = self.reward_config.get('monthly_return_target', 0.10)  # 10%
        monthly_drawdown_limit = self.reward_config.get('monthly_drawdown_limit', 0.04)  # 4%
        monthly_sharpe_target = self.reward_config.get('monthly_sharpe_target', 1.5)  # 1.5

        target_achievement_weight = self.reward_config.get('target_achievement_weight', 2.0)
        progressive_reward_factor = self.reward_config.get('progressive_reward_factor', 1.0)

        # 计算短期收益率奖励（每次交易）
        return_reward = self.returns[-1] * return_weight

        # 计算波动率惩罚
        if len(self.returns) > 1:
            volatility = np.std(self.returns[-20:]) if len(self.returns) >= 20 else np.std(self.returns)
            volatility_penalty = volatility * volatility_weight
        else:
            volatility_penalty = 0.0

        # 计算回撤惩罚
        if len(self.portfolio_values) > 1:
            peak = max(self.portfolio_values[:-1])
            drawdown = (self.portfolio_values[-1] - peak) / peak
            drawdown_penalty = drawdown * drawdown_weight if drawdown < 0 else 0.0
        else:
            drawdown_penalty = 0.0

        # 计算持仓惩罚
        holding_reward = abs(self.position) * holding_penalty

        # 计算换手率惩罚
        if len(self.actions) > 1 and self.actions[-2] != self.actions[-1]:
            turnover_reward = turnover_penalty
        else:
            turnover_reward = 0.0

        # 计算月度指标（如果有足够的数据）
        monthly_target_reward = 0.0

        # 假设一个月约有21个交易日
        if len(self.portfolio_values) >= 21:
            # 计算月度收益率
            monthly_return = (self.portfolio_values[-1] / self.portfolio_values[-min(21, len(self.portfolio_values))]) - 1

            # 计算月度最大回撤
            monthly_values = self.portfolio_values[-min(21, len(self.portfolio_values)):]
            monthly_peak = max(monthly_values)
            monthly_drawdown = (self.portfolio_values[-1] - monthly_peak) / monthly_peak

            # 计算月度夏普比率
            if len(self.returns) >= 21:
                monthly_returns = self.returns[-min(21, len(self.returns)):]
                monthly_return_mean = np.mean(monthly_returns) * 21  # 年化
                monthly_return_std = np.std(monthly_returns) * np.sqrt(21)  # 年化
                monthly_sharpe = monthly_return_mean / monthly_return_std if monthly_return_std > 0 else 0
            else:
                monthly_sharpe = 0

            # 计算目标达成奖励
            # 1. 月度收益目标
            if monthly_return >= monthly_return_target:
                # 超额完成目标给予额外奖励（渐进式奖励）
                excess_return = monthly_return - monthly_return_target
                monthly_target_reward += target_achievement_weight * (1.0 + excess_return * progressive_reward_factor)
            else:
                # 部分完成目标给予部分奖励
                completion_ratio = max(0, monthly_return / monthly_return_target)
                monthly_target_reward += target_achievement_weight * completion_ratio

            # 2. 月度最大回撤限制
            if monthly_drawdown > monthly_drawdown_limit:
                # 超过回撤限制给予惩罚
                excess_drawdown = monthly_drawdown - monthly_drawdown_limit
                monthly_target_reward -= target_achievement_weight * (1.0 + excess_drawdown * progressive_reward_factor * 5)
            else:
                # 控制在回撤限制内给予奖励
                control_ratio = (monthly_drawdown_limit - max(0, monthly_drawdown)) / monthly_drawdown_limit
                monthly_target_reward += target_achievement_weight * control_ratio

            # 3. 月度夏普比率目标
            if monthly_sharpe >= monthly_sharpe_target:
                # 超额完成目标给予额外奖励
                excess_sharpe = monthly_sharpe - monthly_sharpe_target
                monthly_target_reward += target_achievement_weight * (1.0 + excess_sharpe * progressive_reward_factor)
            else:
                # 部分完成目标给予部分奖励
                completion_ratio = max(0, monthly_sharpe / monthly_sharpe_target)
                monthly_target_reward += target_achievement_weight * completion_ratio

        # 计算总奖励（短期+长期）
        reward = return_reward + volatility_penalty + drawdown_penalty + holding_reward + turnover_reward + monthly_target_reward

        return reward

    def _get_info(self):
        """
        获取信息

        返回:
            dict: 信息
        """
        # 计算基本信息
        info = {
            'step': self.current_step,
            'portfolio_value': self.portfolio_value,
            'position': self.position,
            'cash': self.cash,
            'holding_days': self.holding_days,
            'return': self.returns[-1] if len(self.returns) > 0 else 0.0,
            'entry_price': self.entry_price,
            'position_direction': self.position_direction,
            'volatility_scaling_factor': self.volatility_scaling_factor
        }

        # 添加风险管理信息
        if self.dynamic_risk_management:
            info.update({
                'dynamic_stop_loss_threshold': self.dynamic_stop_loss_threshold,
                'dynamic_take_profit_threshold': self.dynamic_take_profit_threshold,
                'stop_loss_triggered': self.stop_loss_triggered,
                'take_profit_triggered': self.take_profit_triggered
            })

        # 添加性能指标（如果有足够的数据）
        if len(self.portfolio_values) >= 21:
            # 计算月度收益率
            monthly_return = (self.portfolio_values[-1] / self.portfolio_values[-min(21, len(self.portfolio_values))]) - 1

            # 计算月度最大回撤
            monthly_values = self.portfolio_values[-min(21, len(self.portfolio_values)):]
            monthly_peak = max(monthly_values)
            monthly_drawdown = (self.portfolio_values[-1] - monthly_peak) / monthly_peak

            # 计算月度夏普比率
            if len(self.returns) >= 21:
                monthly_returns = self.returns[-min(21, len(self.returns)):]
                monthly_return_mean = np.mean(monthly_returns) * 21  # 年化
                monthly_return_std = np.std(monthly_returns) * np.sqrt(21)  # 年化
                monthly_sharpe = monthly_return_mean / monthly_return_std if monthly_return_std > 0 else 0
            else:
                monthly_sharpe = 0

            # 添加月度指标
            info.update({
                'monthly_return': monthly_return,
                'monthly_drawdown': monthly_drawdown,
                'monthly_sharpe': monthly_sharpe,
                'target_monthly_return': self.reward_config.get('monthly_return_target', 0.10),
                'target_monthly_drawdown': self.reward_config.get('monthly_drawdown_limit', 0.04),
                'target_monthly_sharpe': self.reward_config.get('monthly_sharpe_target', 1.5)
            })

        return info

    def render(self):
        """
        渲染环境
        """
        if self.render_mode == 'human':
            print(f"Step: {self.current_step}, "
                  f"Price: {self.df.iloc[self.current_step]['收盘']:.2f}, "
                  f"Position: {self.position:.2f}, "
                  f"Portfolio Value: {self.portfolio_value:.2f}, "
                  f"Return: {self.returns[-1]*100:.2f}%")

    def close(self):
        """
        关闭环境
        """
        pass

    def get_trades_history(self):
        """
        获取交易历史

        返回:
            list: 交易记录列表
        """
        return self.trades

    def get_portfolio_history(self):
        """
        获取组合价值历史

        返回:
            pandas.Series: 组合价值序列
        """
        return pd.Series(
            self.portfolio_values,
            index=self.df.index[self.window_size:self.window_size + len(self.portfolio_values)]
        )
