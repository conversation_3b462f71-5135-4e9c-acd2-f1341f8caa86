# Quantitative Trading Framework

A comprehensive framework for quantitative trading research, model development, and strategy backtesting.

## Overview

This framework provides a complete toolkit for quantitative trading, including:

- **Data Processing**: Cleaning, feature engineering, and normalization tools
- **Model Development**: Neural network models for price prediction and pattern recognition
- **Backtesting**: Robust backtesting engine with performance metrics
- **Reinforcement Learning**: Trading agents with RL-based decision making
- **Automated Training**: Tools for unattended model training and monitoring

## Project Structure

```
quant_trading/
│
├── data_processors.py       # Data processing utilities
├── model_utils.py           # Model training and evaluation utilities
├── backtest_utils.py        # Backtesting engine and metrics
├── trading_env.py           # RL trading environment
├── drl_agent.py             # Deep RL agent implementation
├── auto_trainer.py          # Automated training utilities
├── example_usage.py         # Examples of using the framework
├── requirements.txt         # Project dependencies
└── README.md                # This file
```

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd quant_trading
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Install TA-Lib:
   - For Windows: Download and install from [TA-Lib Windows Binaries](http://prdownloads.sourceforge.net/ta-lib/ta-lib-0.4.0-msvc.zip)
   - For macOS: `brew install ta-lib`
   - For Linux: `apt-get install ta-lib`

## Features

### Data Processing (`data_processors.py`)

- Feature engineering for financial time series
- Technical indicator calculation
- Outlier handling
- Time-based feature generation
- Missing value imputation
- Normalization and scaling

### Model Training (`model_utils.py`)

- PyTorch model creation helpers
- Training and evaluation utilities
- Learning rate optimization
- Checkpoint management
- Performance visualization

### Backtesting (`backtest_utils.py`)

- Strategy backtesting engine
- Performance metrics calculation
- Trading strategy implementations
- Visualization tools

### Reinforcement Learning (`trading_env.py`, `drl_agent.py`)

- OpenAI Gym-compatible trading environment
- PPO agent implementation
- Custom reward functions
- Environment wrappers for state/action normalization

### Automated Training (`auto_trainer.py`)

- Unattended training management
- Error recovery
- Resource monitoring
- Email notifications
- Training scheduling

## Example Usage

See `example_usage.py` for detailed examples of how to use each component of the framework.

Basic workflow:

```python
# Data Processing
from data_processors import DataProcessor
processor = DataProcessor()
processed_data = processor.normalize_features(data)

# Model Training
from model_utils import ModelTrainer, create_mlp
model = create_mlp(input_dim=10, output_dim=1)
trainer = ModelTrainer(model)
trainer.fit(train_loader, val_loader, epochs=50)

# Backtesting
from backtest_utils import BacktestEngine
engine = BacktestEngine(price_data=data)
results = engine.run_backtest(strategy=my_strategy)
metrics = engine.calculate_performance_metrics()
engine.plot_performance()

# Reinforcement Learning
from trading_env import TradingEnvironment
from drl_agent import PPOAgent, PPOTrainer
env = TradingEnvironment(df=data, features=features)
agent = PPOAgent(state_dim=state_dim, action_dim=action_dim)
trainer = PPOTrainer(env=env, agent=agent)
trainer.train()
```

## Requirements

- Python 3.8+
- PyTorch 1.9+
- NumPy, Pandas, Matplotlib
- Scikit-learn
- Gym
- TA-Lib
- See `requirements.txt` for full list

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- PyTorch team for the deep learning framework
- OpenAI team for the Gym reinforcement learning interface
- TA-Lib developers for technical analysis indicators
