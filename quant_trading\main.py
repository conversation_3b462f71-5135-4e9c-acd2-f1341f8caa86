"""
DRL量化交易系统 - 主应用程序
该文件实现了基于Streamlit的用户界面，用于DRL量化交易系统的交互和可视化。
"""

import os
import sys
import logging
import shutil
import subprocess

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(current_dir)
sys.path.append(parent_dir)

# 应用兼容性补丁 (修复 Python 3.13 + PyTorch + Streamlit 兼容性问题)
try:
    from fix_asyncio_torch import apply_all_patches
    apply_all_patches()
    print("已应用 Python 3.13 + PyTorch + Streamlit 兼容性补丁")
except Exception as e:
    print(f"应用兼容性补丁时出错: {str(e)}")

import streamlit as st
import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import seaborn as sns
from stable_baselines3.common.callbacks import BaseCallback

# 配置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'Arial Unicode MS', 'DejaVu Sans']  # 优先使用的字体列表
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

print(f"Current directory: {current_dir}")
print(f"Python path: {sys.path}")

def check_dependencies():
    """检查并安装必要的依赖"""
    try:
        import psutil
    except ImportError:
        print("正在安装psutil...")
        import subprocess
        subprocess.check_call([sys.executable, "-m", "pip", "install", "psutil"])
        import psutil

    try:
        import optuna
    except ImportError:
        print("正在安装optuna...")
        import subprocess
        subprocess.check_call([sys.executable, "-m", "pip", "install", "optuna"])
        import optuna

    # 检查GPU相关依赖
    try:
        import torch
        if torch.cuda.is_available():
            print(f"PyTorch已启用GPU支持: {torch.cuda.get_device_name(0)}")
        else:
            print("PyTorch未检测到GPU")
    except ImportError:
        print("未安装PyTorch")
    except Exception as e:
        print(f"检查PyTorch GPU支持时出错: {str(e)}")

    # 检查matplotlib中文字体支持
    try:
        import matplotlib.font_manager as fm
        fonts = [f.name for f in fm.fontManager.ttflist]
        chinese_fonts = [f for f in fonts if '黑体' in f or '宋体' in f or 'SimHei' in f or 'SimSun' in f or 'Microsoft YaHei' in f]
        if chinese_fonts:
            print(f"找到支持中文的字体: {chinese_fonts}")
        else:
            print("未找到支持中文的字体，可能会影响图表中文显示")
    except Exception as e:
        print(f"检查中文字体支持时出错: {str(e)}")

# 检查依赖
check_dependencies()

# 检查文件是否存在
data_handler_path = os.path.join(current_dir, "core_logic", "data_handler.py")
print(f"data_handler.py 文件是否存在: {os.path.exists(data_handler_path)}")

# 导入核心逻辑模块
try:
    # 尝试导入优化版数据处理模块
    try:
        from quant_trading.data.optimized_data_handler import OptimizedDataHandler
        from quant_trading.data.optimized_data_handler_adapter import OptimizedDataHandlerAdapter
        print("成功导入优化版数据处理模块")
        # 使用适配器保持向后兼容性
        DataHandler = OptimizedDataHandlerAdapter
    except ImportError as odh_e:
        print(f"导入优化版数据处理模块失败: {str(odh_e)}，尝试导入重构版模块")
        try:
            from core_logic.data_handling import DataHandler
            from quant_trading.data.adapter import DataHandlerAdapter
            print("成功导入重构后的数据处理模块")
            # 使用适配器保持向后兼容性
            DataHandler = DataHandlerAdapter
        except ImportError as dh_e:
            print(f"导入重构后的数据处理模块失败: {str(dh_e)}，将使用原始模块")
            from quant_trading.data.data_handler import DataHandler

    # 尝试导入优化版特征工程模块
    try:
        from quant_trading.features.optimized_feature_engineering import OptimizedFeatureEngineering
        from quant_trading.features.optimized_feature_engineering_adapter import OptimizedFeatureEngineeringAdapter
        print("成功导入优化版特征工程模块")
        # 使用适配器保持向后兼容性
        FeatureEngineer = OptimizedFeatureEngineeringAdapter
    except ImportError as ofe_e:
        print(f"导入优化版特征工程模块失败: {str(ofe_e)}，尝试导入重构版模块")
        try:
            from quant_trading.features.feature_engineering import FeatureEngineer, EnhancedFeatureEngineer
            from quant_trading.features.feature_engineering.adapter import FeatureEngineerAdapter
            print("成功导入重构后的特征工程模块")
            # 使用适配器保持向后兼容性
            FeatureEngineer = FeatureEngineerAdapter
        except ImportError as fe:
            print(f"导入重构后的特征工程模块失败: {str(fe)}，将使用原始模块")
            from quant_trading.features.feature_engineer import FeatureEngineer

    # 导入重构后的交易环境模块
    try:
        from core_logic.trading_env import TradingEnvironment
        from quant_trading.trading.adapter import TradingEnvironmentAdapter
        print("成功导入重构后的交易环境模块")
        # 使用适配器保持向后兼容性
        TradingEnvironment = TradingEnvironmentAdapter
    except ImportError as te:
        print(f"导入重构后的交易环境模块失败: {str(te)}，将使用原始模块")
        from quant_trading.trading.trading_environment import TradingEnvironment

    from quant_trading.agents.drl_agent import DRLAgent
    from quant_trading.evaluation.performance_analyzer import PerformanceAnalyzer
    from quant_project.core_logic.utils import setup_logger, is_gpu_available, get_gpu_info, setup_gpu_environment, diagnose_gpu_issues
    from gymnasium import spaces
    print("成功导入所有模块")
except ImportError as e:
    print(f"导入失败: {str(e)}")

# 设置日志
try:
    logger = setup_logger(log_file='logs/app.log')
except NameError:
    # 如果setup_logger未定义，创建一个简单的日志记录器
    print("setup_logger未定义，创建基本日志记录器")

    # 确保日志目录存在
    os.makedirs('logs', exist_ok=True)

    # 创建日志记录器
    logger = logging.getLogger('drl_trading')
    logger.setLevel(logging.INFO)

    # 创建文件处理器
    file_handler = logging.FileHandler('logs/app.log')
    file_handler.setLevel(logging.INFO)

    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)

    # 创建格式化器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # 添加处理器到日志记录器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

# 设置页面配置
st.set_page_config(
    page_title="DRL量化交易系统",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 添加自定义CSS样式，美化页面
st.markdown("""
<style>
    /* 主标题样式 */
    .main-header {
        color: #1E88E5;
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        text-align: center;
        padding: 1rem;
        border-bottom: 2px solid #f0f2f6;
    }

    /* 页面分隔线 */
    .section-divider {
        margin: 2rem 0;
        height: 3px;
        background: linear-gradient(90deg, rgba(242,242,242,1) 0%, rgba(30,136,229,0.5) 50%, rgba(242,242,242,1) 100%);
        border: none;
    }

    /* 卡片样式 */
    .info-card {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
    }

    /* 按钮样式 */
    .stButton>button {
        border-radius: 5px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    /* 提示框样式 */
    .info-box {
        padding: 10px 15px;
        border-radius: 5px;
        margin-bottom: 15px;
        background-color: #e3f2fd;
        border-left: 5px solid #1E88E5;
    }

    /* 表格样式 */
    .dataframe {
        border-collapse: collapse;
        width: 100%;
    }
    .dataframe th {
        background-color: #f0f2f6;
        font-weight: 600;
    }
    .dataframe td, .dataframe th {
        padding: 8px;
        border: 1px solid #ddd;
    }
    .dataframe tr:nth-child(even) {
        background-color: #f9f9f9;
    }

    /* 指标卡片样式 */
    .metric-card {
        background-color: white;
        border-radius: 8px;
        padding: 15px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        text-align: center;
        margin-bottom: 15px;
    }
    .metric-value {
        font-size: 1.8rem;
        font-weight: 700;
        color: #1E88E5;
    }
    .metric-label {
        font-size: 0.9rem;
        color: #666;
    }
</style>
""", unsafe_allow_html=True)

# 自动配置GPU环境（禁用自动安装）
gpu_setup_result = setup_gpu_environment(auto_install=False)
logger.info(f"GPU环境配置结果: {gpu_setup_result['message']}")

# 如果尝试了自动安装，记录结果
if gpu_setup_result.get('auto_install_attempted', False):
    if gpu_setup_result.get('auto_install_result', {}).get('success', False):
        logger.info("GPU支持已自动安装成功")
    else:
        logger.warning("GPU支持自动安装失败，请手动安装")

# 获取GPU信息
gpu_info = get_gpu_info()
if gpu_info['available']:
    logger.info(f"检测到 {gpu_info['count']} 个GPU设备，使用 {gpu_info['framework']} 框架")
else:
    logger.warning("未检测到可用的GPU设备，将使用CPU进行计算")

# 初始化会话状态
if 'data_loaded' not in st.session_state:
    st.session_state.data_loaded = False
if 'data' not in st.session_state:
    st.session_state.data = None
if 'processed_data' not in st.session_state:
    st.session_state.processed_data = None
if 'env_config' not in st.session_state:
    st.session_state.env_config = {
        'initial_capital': 100000,
        'commission_rate': 0.0003,
        'min_hold_days': 3
    }
if 'agent_config' not in st.session_state:
    st.session_state.agent_config = {
        'algorithm': 'PPO',
        'policy_network': 'MlpPolicy',
        'learning_rate': 0.0003,
        'gamma': 0.99
    }
if 'model_loaded' not in st.session_state:
    st.session_state.model_loaded = False
if 'model' not in st.session_state:
    st.session_state.model = None
if 'training_metrics' not in st.session_state:
    st.session_state.training_metrics = {
        'rewards': [],
        'losses': [],
        'steps': [],
        'episodes': []
    }
if 'current_training_models' not in st.session_state:
    # 初始化为空列表
    st.session_state.current_training_models = []

    # 检查saved_models目录是否存在
    models_dir = "saved_models"
    if os.path.exists(models_dir):
        # 获取所有模型文件
        model_files = [os.path.join(models_dir, f) for f in os.listdir(models_dir) if f.endswith(".zip")]

        # 优先添加BEST模型
        best_models = [f for f in model_files if "BEST" in f]
        for model_path in best_models:
            if os.path.exists(model_path) and model_path not in st.session_state.current_training_models:
                st.session_state.current_training_models.append(model_path)
                logger.info(f"启动时添加最佳模型到当前训练模型列表: {os.path.basename(model_path)}")

                # 检查是否有对应的集成目录
                ensemble_dir = model_path.replace('.zip', '_ensemble')
                if os.path.exists(ensemble_dir) and os.path.isdir(ensemble_dir) and ensemble_dir not in st.session_state.current_training_models:
                    st.session_state.current_training_models.append(ensemble_dir)
                    logger.info(f"启动时添加最佳模型的集成目录到当前训练模型列表: {os.path.basename(ensemble_dir)}")

        # 如果没有BEST模型，添加最新的模型
        if not best_models:
            # 按修改时间排序，最新的在前
            model_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
            # 添加最新的模型（最多3个）
            for model_path in model_files[:3]:
                if os.path.exists(model_path) and model_path not in st.session_state.current_training_models:
                    st.session_state.current_training_models.append(model_path)
                    logger.info(f"启动时添加最新模型到当前训练模型列表: {os.path.basename(model_path)}")

                    # 检查是否有对应的集成目录
                    ensemble_dir = model_path.replace('.zip', '_ensemble')
                    if os.path.exists(ensemble_dir) and os.path.isdir(ensemble_dir) and ensemble_dir not in st.session_state.current_training_models:
                        st.session_state.current_training_models.append(ensemble_dir)
                        logger.info(f"启动时添加最新模型的集成目录到当前训练模型列表: {os.path.basename(ensemble_dir)}")

    # 创建集成目录映射
    st.session_state.ensemble_dirs_map = {}
    for path in st.session_state.current_training_models:
        if os.path.isdir(path) and path.endswith('_ensemble'):
            model_path = path.replace('_ensemble', '.zip')
            if os.path.exists(model_path):
                model_name = os.path.basename(model_path)
                st.session_state.ensemble_dirs_map[model_name] = path
                logger.info(f"启动时创建集成目录映射: {model_name} -> {path}")
# 确保训练状态变量存在并正确初始化
if 'training_in_progress' not in st.session_state:
    st.session_state.training_in_progress = False
    logger.info("初始化training_in_progress=False")
if 'stop_training' not in st.session_state:
    st.session_state.stop_training = False
    logger.info("初始化stop_training=False")

# 移除了对training_just_started标志的处理，简化训练流程

# 侧边栏导航
st.sidebar.title("DRL量化交易系统")
page = st.sidebar.radio(
    "导航",
    ["数据中心与环境配置", "DRL智能体训练", "策略性能评估", "实况信号决策", "日志控制台", "策略总结与项目报告", "GPU状态与诊断"]
)

# 显示GPU状态信息
with st.sidebar.expander("GPU状态", expanded=False):
    if gpu_info['available']:
        st.success(f"✅ GPU可用: 检测到 {gpu_info['count']} 个设备")
        st.info(f"框架: {gpu_info['framework']}")

        # 显示每个GPU的信息
        for i, device in enumerate(gpu_info['devices']):
            st.write(f"**GPU {i+1}**: {device}")

            # 显示显存信息（如果有）
            if i < len(gpu_info['memory']):
                memory = gpu_info['memory'][i]
                if isinstance(memory['total'], (int, float)):
                    st.write(f"显存: {memory['total']}GB 总计, {memory.get('free', 'Unknown')}GB 可用")
    else:
        st.warning("⚠️ GPU不可用，将使用CPU进行计算")

        # 显示诊断按钮
        if st.button("诊断GPU问题"):
            with st.spinner("正在诊断GPU问题..."):
                diagnosis = diagnose_gpu_issues()
                if diagnosis['issues_detected']:
                    st.error("检测到以下问题:")
                    for problem in diagnosis['problems']:
                        st.write(f"- {problem}")

                    st.info("建议解决方案:")
                    for suggestion in diagnosis['suggestions']:
                        st.write(f"- {suggestion}")
                else:
                    st.info("未检测到明显的GPU问题，但GPU仍不可用。请检查您的硬件是否支持GPU加速。")

# 数据中心与环境配置页面
if page == "数据中心与环境配置":
    # 使用自定义样式的标题
    st.markdown('<div class="main-header">数据中心与环境配置</div>', unsafe_allow_html=True)

    # 添加用户指南
    with st.expander("📚 用户指南", expanded=True):
        st.markdown("""
        ## 数据中心与环境配置指南

        本页面用于获取金融数据并配置交易环境参数，是使用系统的第一步。

        ### 使用步骤：

        1. **数据获取**
           - 输入金融产品代码（支持股票和指数，例如：`sh000001`为上证指数，`index_000300`为沪深300指数）
           - 选择日期范围和数据频率（日线、周线等）
           - 点击"获取数据"按钮，系统会从AkShare获取历史数据

        2. **特征工程配置**
           - 选择需要的技术指标（如移动平均线、RSI、MACD等）
           - 调整各指标的参数（如周期长度）
           - 点击"生成特征"按钮应用设置并计算特征
           - 可以查看特征相关性和重要性分析，帮助选择有效特征

        3. **交易环境参数配置**
           - 设置初始资金（用于模拟交易的起始资金）
           - 设置手续费率（影响交易成本计算）
           - 设置最小持仓天数（防止过度交易）
           - 点击"保存环境配置"按钮保存设置

        ### 重要提示：
        - 系统仅支持股票和指数数据，期货和加密货币数据提取功能已被禁用
        - 建议使用至少一年的历史数据以获得足够的训练样本
        - 数据质量对模型训练至关重要，请确保获取的数据完整且有代表性
        - 交易环境参数会直接影响模型的学习行为和回测结果，请根据实际情况设置
        """)

        # 添加分隔线
        st.markdown("---")

    # 添加分隔线
    st.markdown('<hr class="section-divider">', unsafe_allow_html=True)

    # 数据获取部分
    st.header("数据获取")
    col1, col2 = st.columns(2)

    with col1:
        stock_code = st.text_input("金融产品代码", "sh000001")
        start_date = st.date_input("开始日期", datetime.now() - timedelta(days=365))
        end_date = st.date_input("结束日期", datetime.now())

    with col2:
        data_frequency = st.selectbox("数据频率", ["日线", "周线", "月线", "小时线", "分钟线"], 0)

        with st.expander("金融产品代码格式说明", expanded=True):
            st.markdown("""
            ### 支持的金融产品代码格式

            #### 股票（推荐使用）
            - 上海证券交易所: `sh` + 6位数字，例如 `sh600000`、`sh601398`
            - 深圳证券交易所: `sz` + 6位数字，例如 `sz000001`、`sz002230`

            #### 指数（推荐使用）
            格式: `index_` + 指数代码

            **上证指数系列**
            - `index_000001`: 上证综指
            - `index_000016`: 上证50指数
            - `index_000300`: 沪深300指数
            - `index_000905`: 中证500指数

            **深证指数系列**
            - `index_399001`: 深证成指
            - `index_399006`: 创业板指数
            - `index_399673`: 创业板50指数

            > **注意**：请严格按照上述格式输入代码，系统将根据代码前缀自动识别数据类型。

            > **重要提示**：期货和加密货币数据提取功能已被禁用，请使用股票或指数数据。
            """)

    if st.button("获取数据"):
        try:
            with st.spinner("正在获取数据..."):
                data_handler = DataHandler()
                data = data_handler.get_stock_data(
                    stock_code=stock_code,
                    start_date=start_date.strftime("%Y-%m-%d"),
                    end_date=end_date.strftime("%Y-%m-%d"),
                    frequency=data_frequency
                )
                st.session_state.data = data
                st.session_state.data_loaded = True
                logger.info(f"成功获取股票 {stock_code} 的数据，共 {len(data)} 条记录")
                st.success(f"成功获取数据，共 {len(data)} 条记录")

                # 自动进行特征工程
                with st.spinner("正在自动生成特征..."):
                    # 使用增强特征工程器，自动计算最优参数
                    try:
                        # 尝试使用重构后的增强特征工程器
                        feature_engineer = FeatureEngineer(use_enhanced=True)
                        logger.info("使用重构后的增强特征工程器自动生成特征")
                    except Exception as e:
                        logger.warning(f"使用重构后的增强特征工程器失败: {str(e)}")
                        try:
                            # 尝试使用原始增强特征工程器
                            from quant_trading.features.enhanced_feature_engineer import EnhancedFeatureEngineer
                            feature_engineer = EnhancedFeatureEngineer()
                            logger.info("使用原始增强特征工程器自动生成特征")
                        except ImportError:
                            # 如果增强特征工程器不可用，使用标准特征工程器
                            logger.info("增强特征工程器不可用，使用标准特征工程器")
                            feature_engineer = FeatureEngineer()

                    # 生成特征
                    processed_data = feature_engineer.generate_features(data)

                    # 删除NaN值
                    processed_data = processed_data.dropna()

                    # 保存到会话状态
                    st.session_state.processed_data = processed_data
                    st.session_state.feature_config = feature_engineer.feature_config

                    logger.info(f"自动特征生成完成，处理后数据形状: {processed_data.shape}")
                    st.success(f"自动特征生成完成，处理后数据形状: {processed_data.shape}")
        except Exception as e:
            error_msg = str(e)
            logger.error(f"获取数据失败: {error_msg}")

            # 检查是否是PyMiniRacer错误
            if "snapshot_blob.bin" in error_msg or "py_mini_racer" in error_msg:
                error_msg = "获取数据时出现PyMiniRacer错误。这可能是由于缺少必要的库或文件导致的。\n\n"
                error_msg += "建议解决方案:\n"
                error_msg += "1. 尝试重新安装akshare库: pip install --upgrade akshare\n"
                error_msg += "2. 确保您的系统安装了最新的Visual C++ Redistributable\n"
                error_msg += "3. 尝试使用不同的股票代码，如 'sh000001'（上证指数）"
                st.error(error_msg)
            else:
                st.error(f"获取数据失败: {error_msg}")

    # 数据概览
    if st.session_state.data_loaded and st.session_state.data is not None:
        st.header("数据概览")
        st.dataframe(st.session_state.data.head())

        st.subheader("数据统计")
        st.write(f"数据形状: {st.session_state.data.shape}")
        st.write(f"列名: {', '.join(st.session_state.data.columns.tolist())}")

        # 数据可视化
        st.subheader("数据可视化")
        fig, ax = plt.subplots(figsize=(10, 6))
        ax.plot(st.session_state.data.index, st.session_state.data['收盘'])
        ax.set_title(f"{stock_code} 收盘价")
        ax.set_xlabel("日期")
        ax.set_ylabel("价格")
        st.pyplot(fig)

    # 添加分隔线
    st.markdown('<hr class="section-divider">', unsafe_allow_html=True)

    # 特征工程配置
    st.header("特征工程配置")
    if st.session_state.data_loaded:
        st.subheader("选择技术指标")

        col1, col2, col3 = st.columns(3)

        with col1:
            use_sma = st.checkbox("简单移动平均线 (SMA)", True)
            sma_periods = st.multiselect("SMA周期", [5, 10, 20, 30, 60, 120], [5, 20, 60])

            use_ema = st.checkbox("指数移动平均线 (EMA)", True)
            ema_periods = st.multiselect("EMA周期", [5, 10, 20, 30, 60, 120], [5, 20])

        with col2:
            use_rsi = st.checkbox("相对强弱指标 (RSI)", True)
            rsi_period = st.slider("RSI周期", 5, 30, 14)

            use_macd = st.checkbox("MACD", True)
            macd_fast = st.slider("MACD快线", 5, 20, 12)
            macd_slow = st.slider("MACD慢线", 15, 40, 26)
            macd_signal = st.slider("MACD信号线", 5, 15, 9)

        with col3:
            use_bbands = st.checkbox("布林带", True)
            bbands_period = st.slider("布林带周期", 5, 30, 20)
            bbands_std = st.slider("布林带标准差", 1.0, 3.0, 2.0, 0.1)

            use_atr = st.checkbox("真实波动幅度 (ATR)", True)
            atr_period = st.slider("ATR周期", 5, 30, 14)

        # 添加高级特征工程选项
        st.subheader("高级特征工程")
        col1, col2, col3 = st.columns(3)

        with col1:
            use_alpha_factors = st.checkbox("Alpha因子", True)
            use_market_microstructure = st.checkbox("市场微观结构特征", True)

        with col2:
            use_time_features = st.checkbox("时间特征", True)
            use_adaptive_features = st.checkbox("自适应特征", True)

        with col3:
            use_sentiment_indicators = st.checkbox("市场情绪指标", True)
            feature_selection_method = st.selectbox("特征选择方法", ["filter", "wrapper", "embedded", "combined"], 3)

        if st.button("生成特征"):
            try:
                with st.spinner("正在生成特征..."):
                    feature_config = {
                        'sma': {'use': use_sma, 'periods': sma_periods},
                        'ema': {'use': use_ema, 'periods': ema_periods},
                        'rsi': {'use': use_rsi, 'period': rsi_period},
                        'macd': {'use': use_macd, 'fast': macd_fast, 'slow': macd_slow, 'signal': macd_signal},
                        'bbands': {'use': use_bbands, 'period': bbands_period, 'std': bbands_std},
                        'atr': {'use': use_atr, 'period': atr_period},
                        'alpha_factors': {'use': use_alpha_factors},
                        'market_microstructure': {'use': use_market_microstructure},
                        'time_features': {'use': use_time_features},
                        'adaptive_features': {'use': use_adaptive_features},
                        'sentiment_indicators': {'use': use_sentiment_indicators},
                        'feature_selection': {'use': True, 'method': feature_selection_method, 'n_features': 40, 'target_days': 5},
                        'normalization': {'method': 'robust'}
                    }

                    feature_engineer = FeatureEngineer(feature_config)
                    processed_data = feature_engineer.generate_features(st.session_state.data)

                    # 删除NaN值
                    processed_data = processed_data.dropna()

                    st.session_state.processed_data = processed_data
                    st.session_state.feature_config = feature_config

                    logger.info(f"成功生成特征，处理后数据形状: {processed_data.shape}")
                    st.success(f"成功生成特征，处理后数据形状: {processed_data.shape}")
            except Exception as e:
                logger.error(f"特征生成失败: {str(e)}")
                st.error(f"特征生成失败: {str(e)}")

        if st.session_state.processed_data is not None:
            st.subheader("处理后的数据预览")
            st.dataframe(st.session_state.processed_data.head())
    else:
        st.info("请先获取数据")

    # 添加分隔线
    st.markdown('<hr class="section-divider">', unsafe_allow_html=True)

    # 交易环境参数配置
    st.header("交易环境参数配置")

    # 基本参数
    st.subheader("基本参数")
    col1, col2 = st.columns(2)

    with col1:
        initial_capital = st.number_input("初始资金", 10000, 10000000, 100000, 10000)
        commission_rate = st.number_input("手续费率 (单边)", 0.0, 0.01, 0.0003, 0.0001, format="%.5f")

    with col2:
        min_hold_days = st.number_input("最小持仓天数", 1, 10, 3, 1)
        st.info("所有交易将在收盘价执行，且严格遵守最小持仓天数规则")

    # 市场模拟参数
    st.subheader("市场模拟参数")
    col1, col2 = st.columns(2)

    with col1:
        slippage_model = st.selectbox("滑点模型", ["none", "percentage", "fixed"], 1)
        slippage_value = st.number_input("滑点值", 0.0, 0.01, 0.001, 0.0001, format="%.5f")
        st.info("百分比模型表示价格的百分比，固定模型表示固定金额")

    with col2:
        allow_short = st.checkbox("允许做空", False)
        max_position = st.slider("最大仓位比例", 0.1, 1.0, 1.0, 0.1)

    # 风险管理参数
    st.subheader("风险管理参数")
    col1, col2, col3 = st.columns(3)

    with col1:
        enable_stop_loss = st.checkbox("启用止损", True)
        stop_loss_threshold = st.slider("止损阈值", 0.01, 0.1, 0.05, 0.01, format="%.2f")

    with col2:
        enable_take_profit = st.checkbox("启用止盈", True)
        take_profit_threshold = st.slider("止盈阈值", 0.05, 0.3, 0.1, 0.01, format="%.2f")

    with col3:
        dynamic_risk_management = st.checkbox("动态风险管理", True)
        volatility_scaling = st.checkbox("波动率缩放", True)
        st.info("动态风险管理会根据市场状况自动调整止盈止损阈值")

    if st.button("保存环境配置"):
        st.session_state.env_config = {
            'initial_capital': initial_capital,
            'commission_rate': commission_rate,
            'min_hold_days': min_hold_days,
            'allow_short': allow_short,
            'max_position': max_position,
            'slippage_model': slippage_model,
            'slippage_value': slippage_value,
            'enable_stop_loss': enable_stop_loss,
            'stop_loss_threshold': stop_loss_threshold,
            'enable_take_profit': enable_take_profit,
            'take_profit_threshold': take_profit_threshold,
            'dynamic_risk_management': dynamic_risk_management,
            'volatility_scaling': volatility_scaling,
            'reward_config': {
                'return_weight': 1.0,
                'volatility_weight': -1.0,
                'drawdown_weight': -1.0,
                'holding_penalty': -0.0001,
                'turnover_penalty': -0.001,
                'monthly_return_target': 0.10,
                'monthly_drawdown_limit': 0.04,
                'monthly_sharpe_target': 1.5,
                'target_achievement_weight': 2.0,
                'progressive_reward_factor': 1.0
            }
        }
        logger.info(f"保存环境配置: {st.session_state.env_config}")
        st.success("环境配置已保存")

# DRL智能体训练页面
elif page == "DRL智能体训练":
    # 使用自定义样式的标题
    st.markdown('<div class="main-header">DRL智能体训练</div>', unsafe_allow_html=True)

    # 添加用户指南
    with st.expander("📚 用户指南", expanded=True):
        st.markdown("""
        ## DRL智能体训练指南

        本页面用于配置和训练深度强化学习(DRL)智能体，是系统的核心功能。

        ### 使用步骤：

        1. **算法与参数配置**
           - 选择DRL算法（PPO、A2C、DQN、SAC、TD3或DDPG）
           - 设置学习率和折扣因子等基本参数
           - 根据所选算法配置特定参数（如PPO的裁剪参数、DQN的缓冲区大小等）
           - 设置网络结构（隐藏层数量、节点数和激活函数）
           - 选择是否使用特征提取器（CNN或Attention）

        2. **高级训练选项**
           - **集成学习**：可选择单一算法集成或多算法集成
             - 单一算法集成：使用同一算法训练多个模型，每个模型使用不同的随机种子
             - 多算法集成：使用多种不同算法训练模型，结合各算法优势
             - 投票方法：majority（多数投票）、weighted（加权投票）或average（平均）
           - **课程学习**：从简单环境开始，逐步增加难度，帮助智能体更好地学习
           - **早停**：当模型性能不再提升时自动停止训练，避免过拟合

        3. **训练控制**
           - 设置训练总步数（建议至少100,000步以上获得稳定效果）
           - 选择是否使用GPU加速训练（如有GPU可用）
           - 配置GPU优化选项（混合精度训练、内存增长等）
           - 点击"开始训练"按钮启动训练过程
           - 训练过程中可以随时点击"停止训练"按钮安全中断训练

        4. **超参数优化（可选）**
           - 启用超参数优化可以自动搜索最佳参数组合
           - 设置试验次数、评估方式和目标指标
           - 配置参数搜索范围（学习率、折扣因子等）
           - 点击"开始超参数优化"按钮启动优化过程

        ### 重要提示：
        - PPO算法通常是最稳定的选择，适合大多数交易场景
        - 启用集成学习可以显著提高模型稳定性和性能
        - 训练完成后，系统会自动保存最佳模型并删除非最佳模型
        - 训练过程中可以观察奖励曲线判断训练效果
        - 如果使用GPU，训练速度会显著提升（通常5-10倍）
        - 当启用多算法集成学习时，主页面上的'DRL算法'选择将被禁用
        """)

        # 添加分隔线
        st.markdown("---")

    # 检查是否已加载数据和生成特征
    if not st.session_state.data_loaded or st.session_state.processed_data is None:
        st.warning("请先在'数据中心与环境配置'页面加载数据并生成特征")
        st.stop()

    # 添加分隔线
    st.markdown('<hr class="section-divider">', unsafe_allow_html=True)

    # 算法与参数配置区
    st.header("算法与参数配置")

    col1, col2 = st.columns(2)

    with col1:
        # 检查是否已经选择了集成学习和多算法集成
        ensemble_selected = 'agent_config' in st.session_state and st.session_state.agent_config.get('ensemble', {}).get('use', False)
        multi_algorithm_selected = 'agent_config' in st.session_state and st.session_state.agent_config.get('ensemble', {}).get('use_multi_algorithm', False)

        # 如果选择了集成学习，显示提示信息
        if ensemble_selected:
            if multi_algorithm_selected:
                st.warning("⚠️ 您已启用多算法集成学习，将使用集成学习中选择的多个算法进行训练，而非单一算法。")
                # 保存到会话状态，以便在页面刷新时保持选择
                st.session_state.use_ensemble = True
                st.session_state.use_multi_algorithm = True
            else:
                st.info("您已选择使用集成学习，将使用多个模型进行训练和预测")
                st.session_state.use_ensemble = True
                st.session_state.use_multi_algorithm = False

        # 如果启用了多算法集成学习，禁用主算法选择
        algorithm = st.selectbox(
            "DRL算法",
            ["PPO", "A2C", "SAC", "TD3", "DDPG", "DQN"],
            0,
            disabled=multi_algorithm_selected,
            help="当启用多算法集成学习时，此选项将被禁用，系统将使用您在集成学习中选择的算法。"
        )
        learning_rate = st.number_input("学习率", 0.00001, 0.01, 0.0003, 0.00001, format="%.5f")
        gamma = st.number_input("折扣因子", 0.8, 0.999, 0.99, 0.001, format="%.3f")

    with col2:
        # 检查是否启用了多算法集成学习
        multi_algorithm_enabled = st.session_state.get('use_multi_algorithm', False)

        # 如果启用了多算法集成学习，显示提示信息
        if multi_algorithm_enabled:
            st.info("您已启用多算法集成学习，每个算法将使用其默认参数配置。")
            # 设置默认参数值，以便后续代码可以使用
            if algorithm == "PPO":
                n_steps = 2048
                batch_size = 64
                n_epochs = 10
                clip_range = 0.2
                use_sde = True
                sde_sample_freq = 4
            elif algorithm == "A2C":
                n_steps = 5
                ent_coef = 0.01
                use_sde = False
            elif algorithm == "SAC":
                buffer_size = 100000
                learning_starts = 1000
                batch_size = 256
                tau = 0.005
                ent_coef = "auto"
                target_entropy = "auto"
            elif algorithm == "TD3":
                buffer_size = 100000
                learning_starts = 1000
                batch_size = 256
                tau = 0.005
                policy_delay = 2
                target_policy_noise = 0.2
            elif algorithm == "DDPG":
                buffer_size = 100000
                learning_starts = 1000
                batch_size = 256
                tau = 0.005
            elif algorithm == "DQN":
                buffer_size = 10000
                learning_starts = 1000
                target_update_interval = 1000
                exploration_fraction = 0.3
                exploration_final_eps = 0.05
        else:
            # 根据所选算法显示特定参数
            if algorithm == "PPO":
                n_steps = st.number_input("每次更新前收集的步数", 64, 4096, 2048, 64)
                batch_size = st.number_input("每次优化的小批量大小", 16, 1024, 64, 16)
                n_epochs = st.number_input("每次更新时的优化轮数", 1, 30, 10, 1)
                clip_range = st.number_input("PPO裁剪参数", 0.1, 0.5, 0.2, 0.01, format="%.2f")
                use_sde = st.checkbox("使用随机探索", True)
                sde_sample_freq = st.number_input("SDE采样频率", 1, 20, 4, 1)
            elif algorithm == "A2C":
                n_steps = st.number_input("每次更新前收集的步数", 1, 100, 5, 1)
                ent_coef = st.number_input("熵系数", 0.0, 0.5, 0.01, 0.01, format="%.2f")
                use_sde = st.checkbox("使用随机探索", False)
            elif algorithm == "SAC":
                buffer_size = st.number_input("经验回放缓冲区大小", 1000, 1000000, 100000, 1000)
                learning_starts = st.number_input("开始学习前收集的步数", 100, 10000, 1000, 100)
                batch_size = st.number_input("每次优化的小批量大小", 16, 1024, 256, 16)
                tau = st.number_input("软更新系数", 0.001, 0.1, 0.005, 0.001, format="%.4f")
                ent_coef = st.selectbox("熵系数", ["auto", "fixed"], 0)
                target_entropy = st.selectbox("目标熵", ["auto", "fixed"], 0)
            elif algorithm == "TD3":
                buffer_size = st.number_input("经验回放缓冲区大小", 1000, 1000000, 100000, 1000)
                learning_starts = st.number_input("开始学习前收集的步数", 100, 10000, 1000, 100)
                batch_size = st.number_input("每次优化的小批量大小", 16, 1024, 256, 16)
                tau = st.number_input("软更新系数", 0.001, 0.1, 0.005, 0.001, format="%.4f")
                policy_delay = st.number_input("策略延迟", 1, 10, 2, 1)
                target_policy_noise = st.number_input("目标策略噪声", 0.0, 0.5, 0.2, 0.01, format="%.2f")
            elif algorithm == "DDPG":
                buffer_size = st.number_input("经验回放缓冲区大小", 1000, 1000000, 100000, 1000)
                learning_starts = st.number_input("开始学习前收集的步数", 100, 10000, 1000, 100)
                batch_size = st.number_input("每次优化的小批量大小", 16, 1024, 256, 16)
                tau = st.number_input("软更新系数", 0.001, 0.1, 0.005, 0.001, format="%.4f")
            elif algorithm == "DQN":
                buffer_size = st.number_input("经验回放缓冲区大小", 1000, 100000, 10000, 1000)
                learning_starts = st.number_input("开始学习前收集的步数", 100, 10000, 1000, 100)
                target_update_interval = st.number_input("目标网络更新间隔", 100, 10000, 1000, 100)
                exploration_fraction = st.number_input("探索率衰减比例", 0.1, 0.9, 0.3, 0.1, format="%.1f")
                exploration_final_eps = st.number_input("最终探索率", 0.01, 0.2, 0.05, 0.01, format="%.2f")

    # 网络结构配置
    st.subheader("网络结构")
    col1, col2, col3 = st.columns(3)

    with col1:
        network_arch = st.selectbox("网络架构", ["2层标准", "3层深度", "4层超深度"], 1)
        if network_arch == "2层标准":
            hidden_layers = [64, 64]
        elif network_arch == "3层深度":
            hidden_layers = [256, 128, 64]
        else:  # 4层超深度
            hidden_layers = [512, 256, 128, 64]

        hidden_layer_1 = st.number_input("第一隐藏层节点数", 16, 512, hidden_layers[0], 16)

    with col2:
        if len(hidden_layers) >= 2:
            hidden_layer_2 = st.number_input("第二隐藏层节点数", 16, 256, hidden_layers[1], 16)
        if len(hidden_layers) >= 3:
            hidden_layer_3 = st.number_input("第三隐藏层节点数", 16, 128, hidden_layers[2], 16)
        if len(hidden_layers) >= 4:
            hidden_layer_4 = st.number_input("第四隐藏层节点数", 16, 64, hidden_layers[3], 8)

    with col3:
        activation_fn = st.selectbox("激活函数", ["ReLU", "Tanh", "ELU"], 0)
        use_features_extractor = st.checkbox("使用特征提取器", True)
        if use_features_extractor:
            features_extractor_type = st.selectbox("特征提取器类型", ["CNN", "Attention"], 1)
            features_dim = st.number_input("特征维度", 32, 256, 128, 16)

    # 添加分隔线
    st.markdown('<hr class="section-divider">', unsafe_allow_html=True)

    # 初始化agent_config，避免未定义错误
    # 根据当前选择的算法创建初始配置
    if algorithm == "PPO":
        agent_config = {
            'algorithm': algorithm,
            'policy_network': 'MlpPolicy',
            'learning_rate': learning_rate,
            'gamma': gamma,
            'n_steps': n_steps,
            'batch_size': batch_size,
            'n_epochs': n_epochs,
            'clip_range': clip_range,
            'use_gpu': gpu_info['available'],
            'net_arch': {
                'pi': [hidden_layer_1, hidden_layer_2],
                'vf': [hidden_layer_1, hidden_layer_2]
            },
            'activation_fn': 'relu' if activation_fn == 'ReLU' else 'tanh'
        }
    elif algorithm == "A2C":
        agent_config = {
            'algorithm': algorithm,
            'policy_network': 'MlpPolicy',
            'learning_rate': learning_rate,
            'gamma': gamma,
            'n_steps': n_steps,
            'ent_coef': ent_coef,
            'use_gpu': gpu_info['available'],
            'net_arch': {
                'pi': [hidden_layer_1, hidden_layer_2],
                'vf': [hidden_layer_1, hidden_layer_2]
            },
            'activation_fn': 'relu' if activation_fn == 'ReLU' else 'tanh'
        }
    elif algorithm == "DQN":
        agent_config = {
            'algorithm': algorithm,
            'policy_network': 'MlpPolicy',
            'learning_rate': learning_rate,
            'gamma': gamma,
            'buffer_size': buffer_size,
            'learning_starts': learning_starts,
            'target_update_interval': target_update_interval,
            'use_gpu': gpu_info['available'],
            'net_arch': [hidden_layer_1, hidden_layer_2],
            'activation_fn': 'relu' if activation_fn == 'ReLU' else 'tanh'
        }
    else:
        # 默认配置，避免未定义错误
        agent_config = {
            'algorithm': algorithm,
            'policy_network': 'MlpPolicy',
            'learning_rate': learning_rate,
            'gamma': gamma,
            'use_gpu': gpu_info['available']
        }

    # 高级训练选项
    st.subheader("高级训练选项")
    col1, col2 = st.columns(2)

    with col1:
        use_curriculum_learning = st.checkbox("使用课程学习", True)
        if use_curriculum_learning:
            st.info("课程学习将从简单环境开始，逐步增加难度，帮助智能体更好地学习")
            curriculum_stages = st.number_input("课程阶段数", 2, 5, 3, 1)

            # 将课程学习配置添加到agent_config
            agent_config['curriculum_learning'] = {
                'use': True,
                'stages': curriculum_stages
            }

        normalize_advantage = st.checkbox("优势归一化", True)
        target_kl = st.number_input("KL散度目标", 0.001, 0.1, 0.01, 0.001, format="%.3f")

        # 将优势归一化和KL散度目标添加到agent_config
        if algorithm == "PPO":
            agent_config['normalize_advantage'] = normalize_advantage
            agent_config['target_kl'] = target_kl

    with col2:
        use_ensemble = st.checkbox("使用集成学习", True)
        if use_ensemble:
            st.info("集成学习将训练多个模型并组合它们的预测，提高稳定性和性能。当选择集成学习时，将使用所选算法训练多个模型，并在预测时组合它们的结果。")

            # 添加更详细的集成学习说明
            with st.expander("集成学习详细说明"):
                st.markdown("""
                ### 集成学习实现说明

                本项目中的集成学习支持两种模式：

                #### 1. 单一算法集成学习
                - **同一算法多次训练**：使用相同的算法（如PPO、A2C等）训练多个模型实例，每个模型使用不同的随机种子初始化
                - 适合于当您已经确定了最佳算法，希望通过多次训练减少随机性影响的情况

                #### 2. 多算法集成学习
                - **不同算法组合**：使用多种不同的算法（PPO、A2C、DQN等）训练模型，每种算法都有其独特的优势
                - 适合于不确定哪种算法最适合当前问题，或希望结合多种算法优势的情况
                - 通常能获得比单一算法更稳健的性能，因为不同算法在不同市场环境下可能表现各异

                #### 模型组合方式
                在预测阶段，将所有模型的预测结果按照选定的投票方法组合成最终决策：
                - **majority（多数投票）**：采用多数模型预测的动作
                - **weighted（加权投票）**：根据模型性能给予不同权重，性能更好的模型权重更高
                - **average（平均）**：对所有模型的预测结果取平均值

                #### 集成学习的优势
                - 减少过拟合风险
                - 提高模型稳定性
                - 降低单个模型的方差
                - 通常能获得比单个模型更好的性能
                - 多算法集成可以结合不同算法的优势，应对不同市场环境

                > **注意**：集成学习功能已完全实现。系统将训练多个模型并在预测时组合它们的结果。在回测时，系统会自动启用兼容模式以确保环境观测空间与模型期望的输入格式匹配。
                """)

            ensemble_size = st.number_input("集成模型数量", 2, 5, 3, 1)
            voting_method = st.selectbox("投票方法", ["majority", "weighted", "average"], 1)

            # 多算法集成学习选项
            use_multi_algorithm = st.checkbox("使用多算法集成学习", False,
                                           help="启用后可以选择多种算法进行集成学习，而不是仅使用单一算法")

            # 更新会话状态
            st.session_state.use_multi_algorithm = use_multi_algorithm

            # 如果启用了多算法集成学习，显示提示信息
            if use_multi_algorithm:
                st.warning("⚠️ 启用多算法集成学习后，主页面上的'DRL算法'选择将被禁用，系统将使用下方选择的多个算法进行训练。")

                # 可用的算法列表
                available_algorithms = ["PPO", "A2C", "DQN", "SAC", "TD3", "DDPG"]

                # 默认选择的算法
                default_selections = ["PPO", "A2C", "DQN"]

                # 多选框，让用户选择要集成的算法
                selected_algorithms = st.multiselect(
                    "选择要集成的算法",
                    options=available_algorithms,
                    default=default_selections,
                    help="选择2-5种算法进行集成学习，系统将训练这些算法的模型并组合它们的预测结果"
                )

                # 确保至少选择了2种算法
                if len(selected_algorithms) < 2:
                    st.warning("请至少选择2种算法进行集成学习")
                    # 如果选择不足，使用默认选择
                    if len(selected_algorithms) == 0:
                        selected_algorithms = default_selections
                        st.info(f"已自动选择默认算法: {', '.join(selected_algorithms)}")

                # 确保算法数量不超过模型数量
                if len(selected_algorithms) > ensemble_size:
                    st.warning(f"选择的算法数量({len(selected_algorithms)})超过了集成模型数量({ensemble_size})，将只使用前{ensemble_size}个算法")
                    selected_algorithms = selected_algorithms[:ensemble_size]

                st.success(f"将使用您选择的算法({', '.join(selected_algorithms)})训练 {ensemble_size} 个模型，并使用 {voting_method} 方法组合它们的预测结果。")
            else:
                # 单算法集成学习，让用户选择使用哪个算法
                ensemble_algorithm = st.selectbox(
                    "选择集成学习使用的算法",
                    ["PPO", "A2C", "DQN", "SAC", "TD3", "DDPG"],
                    index=["PPO", "A2C", "DQN", "SAC", "TD3", "DDPG"].index(algorithm) if algorithm in ["PPO", "A2C", "DQN", "SAC", "TD3", "DDPG"] else 0,
                    help="选择用于集成学习的单一算法，将训练多个该算法的模型实例"
                )
                st.info(f"将使用 {ensemble_algorithm} 算法训练 {ensemble_size} 个模型，并使用 {voting_method} 方法组合它们的预测结果。")

            # 将集成学习配置添加到agent_config
            if use_multi_algorithm:
                # 如果使用多算法集成学习，传递用户选择的算法列表
                agent_config['ensemble'] = {
                    'use': True,
                    'n_models': ensemble_size,
                    'voting_method': voting_method,
                    'algorithm': algorithm,  # 记录主算法（用于兼容性）
                    'use_multi_algorithm': True,  # 是否使用多算法集成学习
                    'selected_algorithms': selected_algorithms  # 用户选择的算法列表
                }
            else:
                # 如果使用单一算法集成学习，使用用户在集成学习部分选择的算法
                agent_config['ensemble'] = {
                    'use': True,
                    'n_models': ensemble_size,
                    'voting_method': voting_method,
                    'algorithm': ensemble_algorithm,  # 使用用户在集成学习部分选择的算法
                    'use_multi_algorithm': False,  # 是否使用多算法集成学习
                    'selected_algorithms': [ensemble_algorithm]  # 单算法集成学习也记录选择的算法
                }

            # 保存到会话状态，以便在页面刷新时保持选择
            st.session_state.agent_config = agent_config
        else:
            # 如果不使用集成学习，确保agent_config中的ensemble配置被正确设置为禁用
            agent_config['ensemble'] = {
                'use': False
            }

            # 更新会话状态
            st.session_state.agent_config = agent_config
            # 确保会话状态中的use_ensemble标志也被更新
            st.session_state.use_ensemble = False

        early_stopping = st.checkbox("使用早停", True)
        if early_stopping:
            patience = st.number_input("早停耐心值", 5, 50, 10, 5)

            # 将早停配置添加到agent_config
            agent_config['early_stopping'] = {
                'use': True,
                'enabled': True,
                'patience': patience,
                'reward_threshold': 0.0  # 默认值，可以根据需要调整
            }

    # 训练控制区
    st.header("训练控制")
    col1, col2 = st.columns(2)

    with col1:
        total_timesteps = st.number_input("训练总步数", 10000, 1000000, 100000, 10000)

    with col2:
        # 检查是否有GPU可用
        gpu_available = gpu_info['available']
        use_gpu = st.checkbox("使用GPU进行训练", value=gpu_available, disabled=not gpu_available)

        if gpu_available:
            # 显示GPU信息
            gpu_framework = gpu_info['framework']
            gpu_count = gpu_info['count']
            st.success(f"✅ 检测到 {gpu_count} 个GPU设备，使用 {gpu_framework} 框架")

            # 如果有多个GPU，允许选择使用哪个
            if gpu_count > 1:
                selected_gpu = st.selectbox(
                    "选择GPU设备",
                    options=list(range(gpu_count)),
                    format_func=lambda x: f"GPU {x+1}: {gpu_info['devices'][x]}"
                )
                st.session_state.selected_gpu = selected_gpu

            # 显示GPU优化选项
            st.subheader("GPU优化选项")
            use_mixed_precision = st.checkbox("使用混合精度训练", value=True,
                                            help="混合精度训练可以加速训练过程并减少内存使用，但可能略微影响精度")
            memory_growth = st.checkbox("启用内存增长", value=True,
                                      help="允许GPU内存根据需要增长，而不是一次性分配所有内存")
            st.session_state.gpu_options = {
                'use_mixed_precision': use_mixed_precision,
                'memory_growth': memory_growth
            }
        else:
            st.warning("⚠️ 未检测到可用的GPU，将使用CPU进行训练")
            st.info("训练速度可能较慢。如需加速，请配置GPU环境。")

            # 显示诊断链接
            if st.button("诊断GPU问题"):
                with st.spinner("正在诊断GPU问题..."):
                    diagnosis = diagnose_gpu_issues()
                    if diagnosis['issues_detected']:
                        st.error("检测到以下问题:")
                        for problem in diagnosis['problems']:
                            st.write(f"- {problem}")

                        st.info("建议解决方案:")
                        for suggestion in diagnosis['suggestions']:
                            st.write(f"- {suggestion}")
                    else:
                        st.info("未检测到明显的GPU问题。请检查您的硬件是否支持GPU加速。")

    # 更新智能体配置中的GPU设置
    agent_config['use_gpu'] = use_gpu

    # 构建环境配置
    env_config = {
        'df_processed_data': st.session_state.processed_data,
        'initial_capital': st.session_state.env_config['initial_capital'],
        'commission_rate': st.session_state.env_config['commission_rate'],
        'min_hold_days': st.session_state.env_config['min_hold_days'],
        'window_size': 20
    }

    # 训练控制区域
    st.subheader("训练控制")

    # 超参数优化选项
    use_hpo_in_training = st.checkbox("在训练中进行超参数优化",
                                    value=False,
                                    help="启用后，训练过程会先进行超参数优化，然后使用最佳参数进行完整训练")

    # 定义默认的超参数优化配置
    hpo_config = {
        'use': use_hpo_in_training,
        'n_trials': 20,
        'n_startup_trials': 5,
        'n_evaluations': 2,
        'n_timesteps': 10000,
        'optimization_method': 'tpe',
        'target_metric': 'reward',
        'param_space': {
            'learning_rate': {'min': 1e-5, 'max': 1e-3},
            'gamma': {'min': 0.9, 'max': 0.9999}
        },
        'pruning': {
            'use': True,
            'method': 'median',
            'patience': 5
        }
    }

    # 如果在超参数优化部分有更详细的配置，会在后面覆盖这些默认值

    # 训练控制按钮
    col1, col2 = st.columns(2)
    with col1:
        start_training = st.button("开始训练", disabled=st.session_state.training_in_progress)
    with col2:
        stop_training = st.button("停止训练",
                                disabled=not st.session_state.training_in_progress,
                                help="点击此按钮可以安全地停止当前训练过程，系统会保存当前最佳模型")

    # 如果点击了停止训练按钮
    if stop_training:
        st.session_state.stop_training = True
        st.warning("正在停止训练，请稍候...")
        # 设置训练状态为False，以便重新启用开始训练按钮
        st.session_state.training_in_progress = False
        logger.info("停止训练按钮被点击，设置training_in_progress=False")
        st.rerun()

    # 开始训练按钮
    if start_training:
        try:
            # 配置GPU环境（如果可用）
            if use_gpu and gpu_info['available']:
                with st.spinner("正在配置GPU环境..."):
                    # 获取GPU选项
                    gpu_options = getattr(st.session_state, 'gpu_options', {
                        'use_mixed_precision': True,
                        'memory_growth': True
                    })

                    # 选择GPU设备（如果有多个）
                    gpu_config_message = ""
                    if gpu_info['count'] > 1:
                        selected_gpu = getattr(st.session_state, 'selected_gpu', 0)

                        # 设置环境变量以选择特定GPU
                        if gpu_info['framework'] == 'PyTorch':
                            import os
                            os.environ['CUDA_VISIBLE_DEVICES'] = str(selected_gpu)
                            gpu_config_message += f"已选择GPU {selected_gpu+1}: {gpu_info['devices'][selected_gpu]}\n"
                        elif gpu_info['framework'] == 'TensorFlow':
                            try:
                                import tensorflow as tf
                                tf.config.set_visible_devices(tf.config.list_physical_devices('GPU')[selected_gpu], 'GPU')
                                gpu_config_message += f"已选择GPU {selected_gpu+1}: {gpu_info['devices'][selected_gpu]}\n"
                            except ImportError:
                                st.warning("TensorFlow未安装，将使用PyTorch进行训练")
                                gpu_info['framework'] = 'PyTorch'
                                import os
                                os.environ['CUDA_VISIBLE_DEVICES'] = str(selected_gpu)
                                gpu_config_message += f"已选择GPU {selected_gpu+1}: {gpu_info['devices'][selected_gpu]}\n"

                    # 配置混合精度训练
                    if gpu_options.get('use_mixed_precision', True):
                        if gpu_info['framework'] == 'PyTorch':
                            import torch
                            if hasattr(torch.cuda, 'amp'):
                                # PyTorch已支持自动混合精度
                                gpu_config_message += "已启用PyTorch混合精度训练\n"
                        elif gpu_info['framework'] == 'TensorFlow':
                            try:
                                import tensorflow as tf
                                policy = tf.keras.mixed_precision.Policy('mixed_float16')
                                tf.keras.mixed_precision.set_global_policy(policy)
                                gpu_config_message += "已启用TensorFlow混合精度训练\n"
                            except ImportError:
                                st.warning("TensorFlow未安装，将使用PyTorch进行训练")
                                gpu_info['framework'] = 'PyTorch'

                    # 配置内存增长
                    if gpu_options.get('memory_growth', True):
                        if gpu_info['framework'] == 'TensorFlow':
                            try:
                                import tensorflow as tf
                                for gpu in tf.config.list_physical_devices('GPU'):
                                    tf.config.experimental.set_memory_growth(gpu, True)
                                gpu_config_message += "已启用TensorFlow内存增长\n"
                            except ImportError:
                                st.warning("TensorFlow未安装，将使用PyTorch进行训练")
                                gpu_info['framework'] = 'PyTorch'
                                # PyTorch默认就是按需分配内存的
                                gpu_config_message += "PyTorch默认使用内存增长模式\n"
                        elif gpu_info['framework'] == 'PyTorch':
                            # PyTorch默认就是按需分配内存的
                            gpu_config_message += "PyTorch默认使用内存增长模式\n"

                    # 清理GPU缓存
                    if gpu_info['framework'] == 'PyTorch':
                        import torch
                        torch.cuda.empty_cache()
                        gpu_config_message += "已清理PyTorch GPU缓存\n"

                    st.info(f"GPU配置成功:\n{gpu_config_message}")

                    # 更新agent_config以使用GPU
                    agent_config['use_gpu'] = True
                    if gpu_info['framework'] == 'PyTorch':
                        agent_config['device'] = 'cuda'
                    elif gpu_info['framework'] == 'TensorFlow':
                        agent_config['device'] = 'GPU'
            else:
                # 使用CPU
                agent_config['use_gpu'] = False
                agent_config['device'] = 'cpu'
                if use_gpu:
                    st.warning("未检测到可用的GPU，将使用CPU进行训练")

            with st.spinner("正在初始化DRL智能体..."):
                # 检查是否使用集成学习或超参数优化，如果是则使用EnhancedDRLAgent
                use_enhanced_agent = (agent_config.get('ensemble', {}).get('use', False) or
                                     (use_hpo_in_training and hpo_config['use']))

                if use_enhanced_agent:
                    # 导入增强型DRL智能体
                    from quant_trading.agents.enhanced_drl_agent import EnhancedDRLAgent

                    # 创建增强型DRL智能体，传递超参数优化配置
                    if use_hpo_in_training and hpo_config['use']:
                        drl_agent = EnhancedDRLAgent(env_config, agent_config, hpo_config)
                    else:
                        drl_agent = EnhancedDRLAgent(env_config, agent_config)

                    logger.info("使用增强型DRL智能体，支持集成学习和高级超参数优化")
                else:
                    # 创建标准DRL智能体
                    drl_agent = DRLAgent(env_config, agent_config)
                    logger.info("使用标准DRL智能体")

                # 创建进度条
                progress_bar = st.progress(0)

                # 创建指标展示区
                metrics_container = st.container()

                # 创建图表展示区
                chart_container = st.container()
                with chart_container:
                    rewards_chart = st.empty()
                    episodes_chart = st.empty()

                # 创建自定义回调
                class StreamlitCallback(BaseCallback):
                    def __init__(self, verbose=0):
                        super(StreamlitCallback, self).__init__(verbose)
                        self.rewards = []
                        self.episode_rewards = []
                        self.episode_lengths = []
                        self.steps = []
                        self.episodes = 0
                        self.start_time = time.time()
                        self.last_update_time = time.time()
                        self.update_interval = 0.1  # 更新UI的最小时间间隔（秒）
                        self.last_progress = 0.0

                    def _on_training_start(self):
                        """训练开始时的回调"""
                        logger.info(f"开始训练，总步数: {total_timesteps}")
                        # 确保进度条从0开始
                        progress_bar.progress(0.0)

                    def _on_step(self):
                        try:
                            # 计算当前进度
                            current_progress = min(self.num_timesteps / max(1, total_timesteps), 1.0)
                            current_time = time.time()
                            time_since_update = current_time - self.last_update_time

                            # 只有当进度变化明显或经过足够时间才更新UI，减少UI更新频率
                            if (current_progress - self.last_progress > 0.01 or
                                time_since_update > self.update_interval or
                                current_progress >= 1.0):

                                # 更新进度条
                                progress_bar.progress(current_progress)
                                self.last_progress = current_progress
                                self.last_update_time = current_time

                                # 记录步数（只记录关键点，避免数组过大）
                                if len(self.steps) == 0 or self.num_timesteps - self.steps[-1] >= total_timesteps / 100:
                                    self.steps.append(self.num_timesteps)

                                # 更新回合数
                                if hasattr(self.model, 'ep_info_buffer') and len(self.model.ep_info_buffer) > 0:
                                    self.episodes = len(self.model.ep_info_buffer)
                                    # 记录最新回合的奖励
                                    if len(self.episode_rewards) < self.episodes:
                                        latest_reward = self.model.ep_info_buffer[-1]["r"]
                                        self.episode_rewards.append(latest_reward)
                                        self.episode_lengths.append(self.model.ep_info_buffer[-1]["l"])

                                # 更新指标
                                with metrics_container:
                                    elapsed_time = current_time - self.start_time
                                    # 避免除以零和处理极小值
                                    if current_progress > 0.001:
                                        estimated_time = elapsed_time / current_progress
                                        remaining_time = max(0, estimated_time - elapsed_time)
                                    else:
                                        remaining_time = 0

                                    col1, col2, col3, col4 = st.columns(4)
                                    with col1:
                                        st.metric("已完成步数", f"{self.num_timesteps}/{total_timesteps}")
                                    with col2:
                                        st.metric("已完成回合数", f"{self.episodes}")
                                    with col3:
                                        st.metric("预估剩余时间", f"{timedelta(seconds=int(remaining_time))}")
                                    with col4:
                                        steps_per_second = self.num_timesteps / max(0.001, elapsed_time)
                                        st.metric("训练速度", f"{steps_per_second:.1f} 步/秒")

                                # 更新奖励图表（如果有足够的数据）
                                if len(self.episode_rewards) > 1:
                                    rewards_df = pd.DataFrame({
                                        "回合": range(1, len(self.episode_rewards) + 1),
                                        "奖励": self.episode_rewards
                                    })
                                    rewards_chart.line_chart(rewards_df.set_index("回合"))
                        except Exception as e:
                            # 捕获并记录任何UI更新过程中的错误，但不中断训练
                            logger.error(f"更新训练UI时发生错误: {str(e)}")

                        # 始终返回True以继续训练
                        return True

                    def _on_training_end(self):
                        """训练结束时的回调"""
                        # 确保进度条显示100%完成
                        progress_bar.progress(1.0)
                        logger.info(f"训练结束，总步数: {self.num_timesteps}, 总回合数: {self.episodes}")

                        # 最终更新回合数和奖励图表
                        if hasattr(self.model, 'ep_info_buffer') and len(self.model.ep_info_buffer) > 0:
                            self.episodes = len(self.model.ep_info_buffer)

                            # 更新奖励图表
                            if len(self.episode_rewards) > 0:
                                rewards_df = pd.DataFrame({
                                    "回合": range(1, len(self.episode_rewards) + 1),
                                    "奖励": self.episode_rewards
                                })
                                rewards_chart.line_chart(rewards_df.set_index("回合"))

                        return True

                # 创建回调实例
                callback = StreamlitCallback()

                # 添加额外的回调函数，如果需要的话
                callbacks = [callback]

                # 如果启用了早停策略，添加早停回调
                if agent_config.get('early_stopping', {}).get('enabled', False):
                    from stable_baselines3.common.callbacks import EvalCallback, StopTrainingOnRewardThreshold

                    # 创建评估环境
                    eval_env = TradingEnvironment(**env_config)

                    # 创建早停回调
                    reward_threshold = agent_config.get('early_stopping', {}).get('reward_threshold', 0)
                    stop_train_callback = StopTrainingOnRewardThreshold(reward_threshold=reward_threshold, verbose=1)
                    eval_callback = EvalCallback(
                        eval_env,
                        callback_on_new_best=stop_train_callback,
                        eval_freq=max(total_timesteps // 20, 1000),  # 评估频率
                        n_eval_episodes=5,
                        verbose=1
                    )
                    callbacks.append(eval_callback)
                    st.info(f"已启用早停策略，奖励阈值: {reward_threshold}")

                # 开始训练
                device_info = 'GPU' if use_gpu and gpu_info['available'] else 'CPU'
                st.info(f"开始训练DRL智能体，使用{device_info}，请耐心等待...")
                logger.info(f"开始训练DRL智能体，使用{device_info}，总步数: {total_timesteps}")

                # 设置训练进行中标志
                if not st.session_state.training_in_progress:
                    st.session_state.training_in_progress = True
                    st.session_state.stop_training = False
                    logger.info("开始训练，设置training_in_progress=True")

                    # 直接开始训练，不使用rerun机制
                    # 这样可以避免Streamlit重新渲染导致的训练流程中断问题
                    logger.info("开始训练流程，设置training_in_progress=True")

                # 记录训练开始时间
                train_start_time = time.time()

                # 创建停止训练回调
                class StopTrainingCallback(BaseCallback):
                    def __init__(self, verbose=0):
                        super(StopTrainingCallback, self).__init__(verbose)
                        # 确保训练状态标志在初始化时被设置
                        st.session_state.training_in_progress = True
                        st.session_state.stop_training = False
                        logger.info("训练开始，设置training_in_progress=True, stop_training=False")

                    def _on_step(self):
                        # 检查是否应该停止训练
                        if 'stop_training' in st.session_state and st.session_state.stop_training:
                            logger.info("收到停止训练信号，正在停止训练...")
                            # 确保训练状态被重置，以便可以重新开始训练
                            st.session_state.training_in_progress = False
                            logger.info("停止训练，设置training_in_progress=False")
                            return False  # 返回False会停止训练
                        return True  # 继续训练

                    def _on_training_end(self):
                        # 训练结束时（包括正常结束和被停止）清除训练状态
                        logger.info("训练结束，清除训练状态")
                        # 确保会话状态存在
                        if 'training_in_progress' in st.session_state:
                            st.session_state.training_in_progress = False
                            logger.info("设置training_in_progress=False")
                        # 重置停止训练标志
                        if 'stop_training' in st.session_state:
                            st.session_state.stop_training = False
                            logger.info("设置stop_training=False")

                # 添加停止训练回调
                callbacks.append(StopTrainingCallback())

                # 检查是否使用集成学习
                if agent_config.get('ensemble', {}).get('use', False):
                    # 检查是否使用多算法集成学习
                    use_multi_algorithm = agent_config.get('ensemble', {}).get('use_multi_algorithm', False)
                    if use_multi_algorithm:
                        selected_algorithms = agent_config.get('ensemble', {}).get('selected_algorithms', [])
                        st.info(f"检测到多算法集成学习配置（{agent_config['ensemble']['n_models']} 个模型，{agent_config['ensemble']['voting_method']} 投票方法）。将使用选定的算法 {', '.join(selected_algorithms)} 训练多个模型并组合它们的预测结果。")
                    else:
                        st.info(f"检测到单算法集成学习配置（{agent_config['ensemble']['n_models']} 个模型，{agent_config['ensemble']['voting_method']} 投票方法）。将使用 {agent_config['algorithm']} 算法训练多个模型并组合它们的预测结果。")
                    logger.info(f"检测到集成学习配置，模型数量: {agent_config['ensemble']['n_models']}, 投票方法: {agent_config['ensemble']['voting_method']}")

                # 检查是否需要先进行超参数优化
                if use_hpo_in_training and hpo_config['use']:
                    st.info("正在进行超参数优化，完成后将使用最佳参数进行完整训练...")

                    # 创建进度条
                    hpo_progress_bar = st.progress(0)

                    # 创建指标展示区
                    hpo_metrics_container = st.container()
                    with hpo_metrics_container:
                        st.subheader("超参数优化进度")
                        trial_info = st.empty()
                        best_params_display = st.empty()

                    # 创建图表展示区
                    hpo_chart_container = st.container()
                    with hpo_chart_container:
                        params_chart = st.empty()
                        rewards_chart = st.empty()

                    # 定义回调函数来更新UI
                    class OptimizationCallback:
                        def __init__(self, n_trials):
                            self.n_trials = max(1, n_trials)  # 确保分母不为零
                            self.current_trial = 0
                            self.best_params = None
                            self.best_value = float('-inf')
                            self.trial_values = []
                            self.param_history = {}
                            self.start_time = time.time()
                            self.last_update_time = time.time()
                            self.update_interval = 0.2  # 更新UI的最小时间间隔（秒）
                            self.last_progress = 0.0

                            # 初始化进度条
                            hpo_progress_bar.progress(0.0)
                            logger.info(f"开始超参数优化，计划试验次数: {n_trials}")

                        def __call__(self, study, trial):
                            try:
                                self.current_trial += 1
                                current_time = time.time()

                                # 计算当前进度
                                progress = min(self.current_trial / self.n_trials, 1.0)
                                time_since_update = current_time - self.last_update_time

                                # 只有当进度变化明显或经过足够时间才更新UI
                                if (progress - self.last_progress > 0.01 or
                                    time_since_update > self.update_interval or
                                    progress >= 1.0 or self.current_trial == 1):

                                    # 更新进度条
                                    hpo_progress_bar.progress(progress)
                                    self.last_progress = progress
                                    self.last_update_time = current_time

                                    # 计算预估剩余时间
                                    elapsed_time = current_time - self.start_time
                                    if progress > 0.001:
                                        estimated_total_time = elapsed_time / progress
                                        remaining_time = max(0, estimated_total_time - elapsed_time)
                                        time_per_trial = elapsed_time / self.current_trial
                                    else:
                                        remaining_time = 0
                                        time_per_trial = 0

                                    # 更新当前试验信息
                                    trial_info.markdown(f"""
                                    **当前进度**: 试验 {self.current_trial}/{self.n_trials} ({progress:.1%})
                                    **当前试验奖励**: {trial.value:.4f}
                                    **预估剩余时间**: {timedelta(seconds=int(remaining_time))}
                                    **平均每次试验时间**: {timedelta(seconds=int(time_per_trial))}
                                    """)

                                # 记录参数和奖励
                                self.trial_values.append(trial.value)

                                # 记录参数历史
                                for param_name, param_value in trial.params.items():
                                    if param_name not in self.param_history:
                                        self.param_history[param_name] = []
                                    self.param_history[param_name].append(param_value)

                                # 更新最佳参数
                                if trial.value > self.best_value:
                                    self.best_value = trial.value
                                    self.best_params = trial.params

                                    # 显示最佳参数
                                    best_params_display.markdown(f"""
                                    ### 当前最佳参数 (奖励: {self.best_value:.4f})
                                    ```
                                    {self.best_params}
                                    ```
                                    """)

                                    # 记录到日志
                                    logger.info(f"发现更好的参数，试验 {self.current_trial}/{self.n_trials}, 奖励: {self.best_value:.4f}")
                                    logger.info(f"最佳参数: {self.best_params}")

                            except Exception as e:
                                # 捕获并记录任何UI更新过程中的错误，但不中断优化过程
                                logger.error(f"更新优化UI时发生错误: {str(e)}")

                            # 更新奖励图表
                            rewards_df = pd.DataFrame({
                                "试验": range(1, len(self.trial_values) + 1),
                                "奖励": self.trial_values
                            })
                            rewards_chart.line_chart(rewards_df.set_index("试验"))

                            # 更新参数图表（选择最重要的几个参数）
                            if len(self.trial_values) > 5:
                                key_params = ['learning_rate', 'gamma']
                                if algorithm == "PPO":
                                    key_params.extend(['n_steps', 'clip_range'])
                                elif algorithm == "A2C":
                                    key_params.extend(['n_steps', 'ent_coef'])
                                elif algorithm == "DQN":
                                    key_params.extend(['buffer_size', 'target_update_interval'])

                                # 只显示存在的参数
                                key_params = [p for p in key_params if p in self.param_history]

                                if key_params:
                                    param_df = pd.DataFrame({
                                        "试验": range(1, len(self.trial_values) + 1)
                                    })

                                    for param in key_params[:3]:  # 最多显示3个参数
                                        if len(self.param_history[param]) == len(self.trial_values):
                                            param_df[param] = self.param_history[param]

                                    params_chart.line_chart(param_df.set_index("试验"))

                    # 开始超参数优化
                    callback = OptimizationCallback(hpo_config['n_trials'])
                    best_params = drl_agent.optimize_hyperparameters(
                        n_trials=hpo_config['n_trials'],
                        n_startup_trials=hpo_config['n_startup_trials'],
                        n_evaluations=hpo_config['n_evaluations'],
                        n_timesteps=hpo_config['n_timesteps'],
                        callback=callback
                    )

                    # 优化完成后显示结果
                    st.success("超参数优化完成！将使用最佳参数进行完整训练")
                    st.json(best_params)

                    # 保存最佳参数到会话状态
                    st.session_state.best_params = best_params

                # 记录训练配置，确保UI设置正确传递到后端
                logger.info("训练配置详情:")
                logger.info(f"算法: {algorithm}")
                logger.info(f"总时间步数: {total_timesteps}")
                logger.info(f"环境配置: {env_config}")

                # 详细记录智能体配置
                logger.info("智能体配置详情:")
                for key, value in agent_config.items():
                    logger.info(f"  {key}: {value}")

                # 详细记录超参数优化配置
                if use_hpo_in_training and hpo_config['use']:
                    logger.info("超参数优化配置详情:")
                    for key, value in hpo_config.items():
                        if key != 'param_space':  # 参数空间可能很大，单独记录
                            logger.info(f"  {key}: {value}")

                    logger.info("超参数搜索空间:")
                    for param, space in hpo_config.get('param_space', {}).items():
                        logger.info(f"  {param}: {space}")

                # 详细记录集成学习配置
                if agent_config.get('ensemble', {}).get('use', False):
                    logger.info("集成学习配置详情:")
                    for key, value in agent_config.get('ensemble', {}).items():
                        logger.info(f"  {key}: {value}")

                # 执行训练
                training_stats = drl_agent.train(
                    total_timesteps=total_timesteps,
                    callback_list=callbacks,
                    progress_bar=False  # 使用我们自己的进度条，而不是SB3的进度条
                )

                # 清除训练进行中标志
                # 这里再次确保训练状态被正确清除（虽然回调也会处理，但为了安全起见）
                if 'training_in_progress' in st.session_state:
                    st.session_state.training_in_progress = False
                    logger.info("训练完成，设置training_in_progress=False")
                if 'stop_training' in st.session_state:
                    st.session_state.stop_training = False
                    logger.info("训练完成，设置stop_training=False")

                # 强制重新渲染页面，确保训练按钮状态正确
                st.rerun()

                # 记录训练总时间
                train_duration = time.time() - train_start_time
                logger.info(f"训练完成，总耗时: {timedelta(seconds=int(train_duration))}")

                # 训练完成后保存模型，传递更多信息
                # 获取股票代码（如果有）
                stock_code = None
                if 'data' in st.session_state and st.session_state.data is not None:
                    if hasattr(st.session_state.data, 'name'):
                        stock_code = st.session_state.data.name

                # 收集性能指标
                performance_metrics = {}

                # 检查回调类型并获取相应的性能指标
                if isinstance(callback, OptimizationCallback):
                    # 如果是超参数优化回调，使用最佳值作为性能指标
                    if hasattr(callback, 'best_value') and callback.best_value != float('-inf'):
                        performance_metrics['return'] = callback.best_value
                        logger.info(f"使用超参数优化的最佳值作为性能指标: {callback.best_value}")
                elif hasattr(callback, 'episode_rewards') and callback.episode_rewards:
                    # 如果是训练回调，计算平均奖励
                    avg_reward = sum(callback.episode_rewards) / max(1, len(callback.episode_rewards))
                    performance_metrics['return'] = avg_reward
                    logger.info(f"使用训练回调的平均奖励作为性能指标: {avg_reward}")
                else:
                    # 如果无法获取性能指标，使用默认值
                    performance_metrics['return'] = 0.0
                    logger.warning("无法获取性能指标，使用默认值 0.0")

                # 检查是否已经在训练过程中保存了模型（集成学习的情况）
                if hasattr(training_stats, 'get') and training_stats.get('model_path'):
                    # 集成学习训练已经保存了模型
                    model_path = training_stats.get('model_path')
                    ensemble_dir = training_stats.get('ensemble_dir')
                    logger.info(f"使用集成学习训练中已保存的模型: {model_path}")
                    logger.info(f"集成学习目录: {ensemble_dir}")
                else:
                    # 常规训练，保存模型，并清理本次训练中的非最佳模型
                    model_path = drl_agent.save_model(
                        stock_code=stock_code,
                        performance_metrics=performance_metrics,
                        clean_old_models=True  # 清理本次训练中的非最佳模型，只保留最佳模型
                    )

                # 显示训练结果
                st.success(f"训练完成！模型已保存到: {model_path}")
                st.json(training_stats)

                # 保存训练指标到会话状态
                if isinstance(callback, OptimizationCallback):
                    # 如果是超参数优化回调，保存优化指标
                    st.session_state.training_metrics = {
                        'rewards': callback.trial_values,
                        'best_value': callback.best_value if hasattr(callback, 'best_value') else None,
                        'best_params': callback.best_params if hasattr(callback, 'best_params') else None,
                        'n_trials': callback.current_trial
                    }
                    logger.info("保存超参数优化指标到会话状态")
                elif hasattr(callback, 'episode_rewards'):
                    # 如果是训练回调，保存训练指标
                    st.session_state.training_metrics = {
                        'rewards': callback.episode_rewards,
                        'lengths': callback.episode_lengths if hasattr(callback, 'episode_lengths') else [],
                        'steps': callback.steps if hasattr(callback, 'steps') else []
                    }
                    logger.info("保存训练指标到会话状态")
                else:
                    # 如果无法获取指标，使用空值
                    st.session_state.training_metrics = {
                        'rewards': [],
                        'lengths': [],
                        'steps': []
                    }
                    logger.warning("无法获取训练指标，使用空值")

                # 保存模型到会话状态
                st.session_state.model = drl_agent
                st.session_state.model_loaded = True
                st.session_state.model_path = model_path

                # 记录当前训练的模型路径
                if model_path not in st.session_state.current_training_models:
                    st.session_state.current_training_models.append(model_path)

                # 检查是否是集成学习模型，如果是，也记录集成目录
                ensemble_dir = model_path.replace('.zip', '_ensemble')
                if os.path.exists(ensemble_dir) and os.path.isdir(ensemble_dir):
                    if ensemble_dir not in st.session_state.current_training_models:
                        st.session_state.current_training_models.append(ensemble_dir)
                        logger.info(f"添加集成学习目录到当前训练模型列表: {ensemble_dir}")

                # 确保当前训练的模型被添加到会话状态
                logger.info(f"确保当前训练的模型被正确添加到会话状态: {model_path}")

                # 从模型路径中提取时间戳
                model_filename = os.path.basename(model_path)
                # 尝试从文件名中提取时间戳（格式通常为YYYYMMDD_HHMMSS）
                import re
                timestamp_match = re.search(r'(\d{8}_\d{6})', model_filename)

                # 查找所有相关模型并添加到会话状态
                models_dir = "saved_models"
                if os.path.exists(models_dir):
                    # 获取所有模型文件
                    all_model_files = [f for f in os.listdir(models_dir) if f.endswith(".zip")]

                    # 如果能提取到时间戳，查找相关模型
                    if timestamp_match:
                        timestamp = timestamp_match.group(1)
                        logger.info(f"从模型文件名中提取到时间戳: {timestamp}")

                        # 查找所有包含当前时间戳的模型（包括普通模型和最佳模型）
                        related_models = [f for f in all_model_files if timestamp in f]
                        logger.info(f"找到相关模型: {related_models}")

                        # 添加所有相关模型到会话状态
                        for related_model in related_models:
                            related_model_path = os.path.join(models_dir, related_model)
                            if os.path.exists(related_model_path) and related_model_path not in st.session_state.current_training_models:
                                st.session_state.current_training_models.append(related_model_path)
                                logger.info(f"添加相关模型到当前训练模型列表: {related_model}")

                                # 检查是否有对应的集成目录
                                related_ensemble_dir = related_model_path.replace('.zip', '_ensemble')
                                if os.path.exists(related_ensemble_dir) and os.path.isdir(related_ensemble_dir) and related_ensemble_dir not in st.session_state.current_training_models:
                                    st.session_state.current_training_models.append(related_ensemble_dir)
                                    logger.info(f"添加相关模型的集成目录到当前训练模型列表: {related_ensemble_dir}")
                    else:
                        # 如果无法提取时间戳，添加当前模型和最新的BEST模型
                        logger.warning(f"无法从模型文件名中提取时间戳: {model_filename}")

                        # 确保当前模型被添加
                        if model_path not in st.session_state.current_training_models:
                            st.session_state.current_training_models.append(model_path)
                            logger.info(f"添加当前模型到当前训练模型列表: {model_filename}")

                        # 检查是否有对应的集成目录
                        current_ensemble_dir = model_path.replace('.zip', '_ensemble')
                        if os.path.exists(current_ensemble_dir) and os.path.isdir(current_ensemble_dir) and current_ensemble_dir not in st.session_state.current_training_models:
                            st.session_state.current_training_models.append(current_ensemble_dir)
                            logger.info(f"添加当前模型的集成目录到当前训练模型列表: {current_ensemble_dir}")

                        # 查找所有BEST模型
                        best_model_files = [f for f in all_model_files if "BEST" in f]

                        # 添加最新的BEST模型
                        if best_model_files:
                            # 按修改时间排序
                            best_model_files.sort(key=lambda x: os.path.getmtime(os.path.join(models_dir, x)), reverse=True)
                            newest_best_model = best_model_files[0]
                            best_model_path = os.path.join(models_dir, newest_best_model)

                            if os.path.exists(best_model_path) and best_model_path not in st.session_state.current_training_models:
                                st.session_state.current_training_models.append(best_model_path)
                                logger.info(f"添加最新的最佳模型到当前训练模型列表: {newest_best_model}")

                                # 检查是否有对应的集成目录
                                best_ensemble_dir = best_model_path.replace('.zip', '_ensemble')
                                if os.path.exists(best_ensemble_dir) and os.path.isdir(best_ensemble_dir) and best_ensemble_dir not in st.session_state.current_training_models:
                                    st.session_state.current_training_models.append(best_ensemble_dir)
                                    logger.info(f"添加最新的最佳模型的集成目录到当前训练模型列表: {best_ensemble_dir}")

                    # 创建或更新集成目录映射
                    if 'ensemble_dirs_map' not in st.session_state:
                        st.session_state.ensemble_dirs_map = {}

                    # 更新所有模型的集成目录映射
                    for model_file in all_model_files:
                        ensemble_dir = os.path.join(models_dir, model_file.replace('.zip', '_ensemble'))
                        if os.path.exists(ensemble_dir) and os.path.isdir(ensemble_dir):
                            st.session_state.ensemble_dirs_map[model_file] = ensemble_dir
                            logger.info(f"更新集成目录映射: {model_file} -> {ensemble_dir}")

                    # 特别处理集成学习训练的情况
                    if hasattr(training_stats, 'get') and training_stats.get('ensemble_dir'):
                        model_filename = os.path.basename(model_path)
                        ensemble_dir = training_stats.get('ensemble_dir')
                        if os.path.exists(ensemble_dir) and os.path.isdir(ensemble_dir):
                            st.session_state.ensemble_dirs_map[model_filename] = ensemble_dir
                            logger.info(f"添加集成学习训练模型到映射: {model_filename} -> {ensemble_dir}")

                # 打印当前训练模型列表，确认所有模型都被正确添加
                logger.info(f"当前训练模型列表: {[os.path.basename(p) for p in st.session_state.current_training_models]}")

        except Exception as e:
            st.error(f"训练过程中发生错误: {str(e)}")
            logger.error(f"训练过程中发生错误: {str(e)}", exc_info=True)
            # 确保在发生异常时也清除训练状态
            if 'training_in_progress' in st.session_state:
                st.session_state.training_in_progress = False
                logger.info("训练过程中发生异常，设置training_in_progress=False")
            if 'stop_training' in st.session_state:
                st.session_state.stop_training = False
                logger.info("训练过程中发生异常，设置stop_training=False")

            # 强制重新渲染页面，确保训练按钮状态正确
            st.rerun()

    # 超参数优化区域
    st.header("超参数优化 (HPO)")

    with st.expander("超参数优化配置", expanded=True):
        st.markdown("""
        ### 超参数优化

        超参数优化使用Optuna库自动搜索最佳的模型参数组合，以提高模型性能。
        """)

        col1, col2, col3 = st.columns(3)

        with col1:
            use_hpo = st.checkbox("启用超参数优化", True)
            n_trials = st.number_input("试验次数", 10, 200, 100, 5,
                                     help="执行的超参数组合总数，数量越多，找到最优参数的可能性越大，但耗时也越长")
            n_startup_trials = st.number_input("启动试验次数", 5, 50, 20, 1,
                                            help="随机采样的初始试验次数，用于收集足够的数据来指导后续的参数搜索")

        with col2:
            n_evaluations = st.number_input("每次试验的评估次数", 1, 10, 3, 1,
                                          help="每组参数评估的次数，取平均值作为该组参数的性能指标")
            n_timesteps = st.number_input("每次评估的时间步数", 5000, 100000, 50000, 5000,
                                        help="每次评估训练的步数，较小的步数可以加快优化过程，但可能不够准确")
            optimization_method = st.selectbox("优化方法", ["bayesian", "random", "grid"], 0,
                                            help="贝叶斯优化最高效，随机搜索更简单，网格搜索最全面但最慢")

        with col3:
            target_metric = st.selectbox("目标指标", ["return", "sharpe_ratio", "sortino_ratio", "calmar_ratio"], 1,
                                      help="选择优化的目标指标，夏普比率平衡收益和风险")
            use_pruning = st.checkbox("启用剪枝", True,
                                   help="提前终止表现不佳的试验，节省计算资源")
            if use_pruning:
                pruning_method = st.selectbox("剪枝方法", ["median", "percentile"], 0)
                pruning_patience = st.number_input("剪枝耐心值", 2, 10, 5, 1)

        # 根据算法显示相应的参数范围设置
        st.subheader("参数搜索范围")

        # 通用参数
        col1, col2 = st.columns(2)
        with col1:
            lr_min = st.number_input("学习率最小值", 1e-6, 1e-3, 1e-5, format="%.6f")
            lr_max = st.number_input("学习率最大值", 1e-5, 1e-2, 1e-3, format="%.5f")

        with col2:
            gamma_min = st.number_input("折扣因子最小值", 0.8, 0.99, 0.9, 0.01, format="%.2f")
            gamma_max = st.number_input("折扣因子最大值", 0.9, 1.0, 0.9999, 0.0001, format="%.4f")

        # 算法特定参数
        if algorithm == "PPO":
            col1, col2, col3 = st.columns(3)
            with col1:
                n_steps_min = st.number_input("步数最小值", 32, 1024, 64)
                n_steps_max = st.number_input("步数最大值", 128, 4096, 2048)

            with col2:
                batch_size_min = st.number_input("批量大小最小值", 16, 128, 32)
                batch_size_max = st.number_input("批量大小最大值", 64, 1024, 256)

            with col3:
                n_epochs_min = st.number_input("优化轮数最小值", 1, 10, 5)
                n_epochs_max = st.number_input("优化轮数最大值", 5, 30, 20)

            col1, col2 = st.columns(2)
            with col1:
                clip_range_min = st.number_input("裁剪参数最小值", 0.1, 0.3, 0.1, 0.05, format="%.2f")
                clip_range_max = st.number_input("裁剪参数最大值", 0.1, 0.5, 0.3, 0.05, format="%.2f")

            with col2:
                ent_coef_min = st.number_input("熵系数最小值", 0.0, 0.05, 0.0, 0.01, format="%.2f")
                ent_coef_max = st.number_input("熵系数最大值", 0.0, 0.5, 0.1, 0.01, format="%.2f")

        elif algorithm == "A2C":
            col1, col2 = st.columns(2)
            with col1:
                n_steps_min = st.number_input("步数最小值", 1, 20, 5)
                n_steps_max = st.number_input("步数最大值", 5, 100, 20)

            with col2:
                ent_coef_min = st.number_input("熵系数最小值", 0.0, 0.05, 0.0, 0.01, format="%.2f")
                ent_coef_max = st.number_input("熵系数最大值", 0.0, 0.5, 0.1, 0.01, format="%.2f")

        elif algorithm == "DQN":
            col1, col2 = st.columns(2)
            with col1:
                buffer_size_min = st.number_input("缓冲区大小最小值", 1000, 50000, 5000, 1000)
                buffer_size_max = st.number_input("缓冲区大小最大值", 10000, 200000, 100000, 10000)

            with col2:
                learning_starts_min = st.number_input("学习起始步数最小值", 100, 5000, 1000, 100)
                learning_starts_max = st.number_input("学习起始步数最大值", 1000, 20000, 10000, 1000)

            col1, col2 = st.columns(2)
            with col1:
                target_update_min = st.number_input("目标网络更新间隔最小值", 100, 2000, 500, 100)
                target_update_max = st.number_input("目标网络更新间隔最大值", 500, 10000, 5000, 500)

        # 更新超参数优化配置
        param_space = {
            'learning_rate': {'min': lr_min, 'max': lr_max},
            'gamma': {'min': gamma_min, 'max': gamma_max}
        }

        if algorithm == "PPO":
            param_space.update({
                'n_steps': {'min': n_steps_min, 'max': n_steps_max},
                'batch_size': {'min': batch_size_min, 'max': batch_size_max},
                'n_epochs': {'min': n_epochs_min, 'max': n_epochs_max},
                'clip_range': {'min': clip_range_min, 'max': clip_range_max},
                'ent_coef': {'min': ent_coef_min, 'max': ent_coef_max}
            })
        elif algorithm == "A2C":
            param_space.update({
                'n_steps': {'min': n_steps_min, 'max': n_steps_max},
                'ent_coef': {'min': ent_coef_min, 'max': ent_coef_max}
            })
        elif algorithm == "DQN":
            param_space.update({
                'buffer_size': {'min': buffer_size_min, 'max': buffer_size_max},
                'learning_starts': {'min': learning_starts_min, 'max': learning_starts_max},
                'target_update_interval': {'min': target_update_min, 'max': target_update_max}
            })

        # 更新全局的hpo_config，而不是重新定义
        hpo_config.update({
            'use': use_hpo,
            'n_trials': n_trials,
            'n_startup_trials': n_startup_trials,
            'n_evaluations': n_evaluations,
            'n_timesteps': n_timesteps,
            'optimization_method': optimization_method,
            'target_metric': target_metric,
            'param_space': param_space,
            'pruning': {
                'use': use_pruning,
                'method': pruning_method if use_pruning else 'none',
                'patience': pruning_patience if use_pruning else 0
            }
        })

        if use_hpo:
            if st.button("开始超参数优化"):
                try:
                    with st.spinner("正在进行超参数优化，这可能需要较长时间..."):
                        # 创建进度条
                        progress_bar = st.progress(0)

                        # 创建指标展示区
                        metrics_container = st.container()
                        with metrics_container:
                            st.subheader("优化进度")
                            trial_info = st.empty()
                            best_params_display = st.empty()

                        # 创建图表展示区
                        chart_container = st.container()
                        with chart_container:
                            params_chart = st.empty()
                            rewards_chart = st.empty()

                        # 创建DRL智能体
                        from quant_trading.agents.enhanced_drl_agent import EnhancedDRLAgent

                        # 配置GPU环境（如果可用）
                        if use_gpu and gpu_info['available']:
                            # 设置GPU选项
                            gpu_options = st.session_state.gpu_options

                            # 如果有多个GPU，选择指定的GPU
                            if gpu_info['count'] > 1 and hasattr(st.session_state, 'selected_gpu'):
                                os.environ["CUDA_VISIBLE_DEVICES"] = str(st.session_state.selected_gpu)

                            # 配置GPU
                            try:
                                if gpu_info['framework'] == 'TensorFlow':
                                    try:
                                        import tensorflow as tf
                                        if gpu_options.get('memory_growth', True):
                                            gpus = tf.config.experimental.list_physical_devices('GPU')
                                            if gpus:
                                                for gpu in gpus:
                                                    tf.config.experimental.set_memory_growth(gpu, True)

                                        if gpu_options.get('use_mixed_precision', True):
                                            policy = tf.keras.mixed_precision.Policy('mixed_float16')
                                            tf.keras.mixed_precision.set_global_policy(policy)
                                    except ImportError:
                                        st.warning("TensorFlow未安装，将使用PyTorch进行训练")
                                        gpu_info['framework'] = 'PyTorch'

                                # 配置PyTorch
                                elif gpu_info['framework'] == 'PyTorch':
                                    import torch
                                    if gpu_options.get('use_mixed_precision', True):
                                        torch.backends.cudnn.benchmark = True
                                        if hasattr(torch.cuda, 'amp'):
                                            st.info("启用PyTorch自动混合精度")

                                gpu_config_message = f"GPU配置: 使用{'混合精度' if gpu_options.get('use_mixed_precision', True) else '全精度'}, "
                                gpu_config_message += f"内存增长{'启用' if gpu_options.get('memory_growth', True) else '禁用'}"

                                st.info(f"GPU配置成功:\n{gpu_config_message}")
                            except Exception as e:
                                st.error(f"GPU配置失败: {str(e)}")
                                logger.error(f"GPU配置失败: {str(e)}")

                            # 更新agent_config以使用GPU
                            agent_config['use_gpu'] = True
                            if gpu_info['framework'] == 'PyTorch':
                                agent_config['device'] = 'cuda'
                            elif gpu_info['framework'] == 'TensorFlow':
                                agent_config['device'] = 'GPU'
                        else:
                            # 使用CPU
                            agent_config['use_gpu'] = False
                            agent_config['device'] = 'cpu'
                            if use_gpu:
                                st.warning("未检测到可用的GPU，将使用CPU进行训练")

                        # 创建增强型DRL智能体
                        drl_agent = EnhancedDRLAgent(env_config, agent_config, hpo_config)

                        # 定义回调函数来更新UI
                        class OptimizationCallback:
                            def __init__(self, n_trials):
                                self.n_trials = max(1, n_trials)  # 确保分母不为零
                                self.current_trial = 0
                                self.best_params = None
                                self.best_value = float('-inf')
                                self.trial_values = []
                                self.param_history = {}
                                self.start_time = time.time()
                                self.last_update_time = time.time()
                                self.update_interval = 0.2  # 更新UI的最小时间间隔（秒）
                                self.last_progress = 0.0

                                # 添加这些属性以兼容训练后的代码
                                self.episode_rewards = []
                                self.episode_lengths = []
                                self.steps = []

                                # 初始化进度条
                                progress_bar.progress(0.0)
                                logger.info(f"开始超参数优化，计划试验次数: {n_trials}")

                            def __call__(self, study, trial):
                                try:
                                    self.current_trial += 1
                                    current_time = time.time()

                                    # 计算当前进度
                                    progress = min(self.current_trial / self.n_trials, 1.0)
                                    time_since_update = current_time - self.last_update_time

                                    # 只有当进度变化明显或经过足够时间才更新UI
                                    if (progress - self.last_progress > 0.01 or
                                        time_since_update > self.update_interval or
                                        progress >= 1.0 or self.current_trial == 1):

                                        # 更新进度条
                                        progress_bar.progress(progress)
                                        self.last_progress = progress
                                        self.last_update_time = current_time

                                        # 计算预估剩余时间
                                        elapsed_time = current_time - self.start_time
                                        if progress > 0.001:
                                            estimated_total_time = elapsed_time / progress
                                            remaining_time = max(0, estimated_total_time - elapsed_time)
                                            time_per_trial = elapsed_time / self.current_trial
                                        else:
                                            remaining_time = 0
                                            time_per_trial = 0

                                        # 更新当前试验信息
                                        trial_info.markdown(f"""
                                        **当前进度**: 试验 {self.current_trial}/{self.n_trials} ({progress:.1%})
                                        **当前试验奖励**: {trial.value:.4f}
                                        **预估剩余时间**: {datetime.timedelta(seconds=int(remaining_time))}
                                        **平均每次试验时间**: {datetime.timedelta(seconds=int(time_per_trial))}
                                        """)

                                    # 记录参数和奖励
                                    self.trial_values.append(trial.value)

                                    # 记录参数历史（限制历史记录大小，避免内存占用过大）
                                    for param_name, param_value in trial.params.items():
                                        if param_name not in self.param_history:
                                            self.param_history[param_name] = []
                                        self.param_history[param_name].append(param_value)

                                    # 更新最佳参数
                                    if trial.value > self.best_value:
                                        self.best_value = trial.value
                                        self.best_params = trial.params

                                        # 显示最佳参数
                                        best_params_display.markdown(f"""
                                        ### 当前最佳参数 (奖励: {self.best_value:.4f})
                                        ```
                                        {self.best_params}
                                        ```
                                        """)

                                        # 记录到日志
                                        logger.info(f"发现更好的参数，试验 {self.current_trial}/{self.n_trials}, 奖励: {self.best_value:.4f}")
                                        logger.info(f"最佳参数: {self.best_params}")

                                except Exception as e:
                                    # 捕获并记录任何UI更新过程中的错误，但不中断优化过程
                                    logger.error(f"更新优化UI时发生错误: {str(e)}")

                                # 更新奖励图表
                                rewards_df = pd.DataFrame({
                                    "试验": range(1, len(self.trial_values) + 1),
                                    "奖励": self.trial_values
                                })
                                rewards_chart.line_chart(rewards_df.set_index("试验"))

                                # 更新参数图表（选择最重要的几个参数）
                                if len(self.trial_values) > 5:
                                    key_params = ['learning_rate', 'gamma']
                                    if algorithm == "PPO":
                                        key_params.extend(['n_steps', 'clip_range'])
                                    elif algorithm == "A2C":
                                        key_params.extend(['n_steps', 'ent_coef'])
                                    elif algorithm == "DQN":
                                        key_params.extend(['buffer_size', 'target_update_interval'])

                                    # 只显示存在的参数
                                    key_params = [p for p in key_params if p in self.param_history]

                                    if key_params:
                                        param_df = pd.DataFrame({
                                            "试验": range(1, len(self.trial_values) + 1)
                                        })

                                        for param in key_params[:3]:  # 最多显示3个参数
                                            if len(self.param_history[param]) == len(self.trial_values):
                                                param_df[param] = self.param_history[param]

                                        params_chart.line_chart(param_df.set_index("试验"))

                        # 开始超参数优化
                        callback = OptimizationCallback(n_trials)
                        best_params = drl_agent.optimize_hyperparameters(
                            n_trials=n_trials,
                            n_startup_trials=n_startup_trials,
                            n_evaluations=n_evaluations,
                            n_timesteps=n_timesteps,
                            callback=callback
                        )

                        # 优化完成后显示结果
                        st.success("超参数优化完成！")
                        st.json(best_params)

                        # 保存最佳参数到会话状态
                        st.session_state.best_params = best_params

                        # 提示用户使用最佳参数进行训练
                        st.info("您可以使用上面的最佳参数配置模型，然后点击'开始训练'按钮进行完整训练。")

                except Exception as e:
                    st.error(f"超参数优化过程中发生错误: {str(e)}")
                    logger.error(f"超参数优化过程中发生错误: {str(e)}", exc_info=True)

elif page == "策略性能评估":
    # 使用自定义样式的标题
    st.markdown('<div class="main-header">策略性能评估</div>', unsafe_allow_html=True)

    # 添加用户指南
    with st.expander("📚 用户指南", expanded=True):
        st.markdown("""
        ## 策略性能评估指南

        本页面用于评估训练好的DRL模型在历史数据上的表现，帮助您了解策略的优势和风险。

        ### 使用步骤：

        1. **模型选择与加载**
           - 从下拉列表中选择要评估的模型（🌟标记的是最佳模型）
           - 系统会自动识别当前训练会话的模型，优先显示最佳模型
           - 点击"加载模型"按钮加载所选模型
           - 如果是集成学习模型，系统会自动加载所有子模型

        2. **测试环境配置**
           - 选择测试数据的日期范围（建议使用未参与训练的数据进行真实评估）
           - 设置初始资金、手续费率、最小持仓天数等参数
           - 配置滑点模型（百分比或固定金额）以模拟真实交易环境
           - 设置风险管理参数（止损、止盈阈值等）
           - 点击"开始回测"按钮运行回测

        3. **性能指标分析**
           - **收益指标**：总收益率、年化收益率、月度收益率等
           - **风险指标**：最大回撤、波动率、下行风险等
           - **风险调整收益**：夏普比率、索提诺比率、卡尔玛比率等
           - **交易统计**：胜率、盈亏比、平均持仓时间、交易次数等
           - **月度/季度/年度表现**：不同时间段的收益分布

        4. **可视化结果**
           - **净值曲线图**：显示策略和基准的净值变化对比
           - **月度收益热图**：直观展示每月收益情况，识别季节性模式
           - **价格与交易点图**：标记买入和卖出点位，分析交易时机
           - **回撤图**：展示策略的回撤情况和持续时间
           - **持仓分析**：展示持仓时间分布和仓位变化

        ### 重要提示：
        - 回测结果仅代表历史表现，不保证未来收益
        - 关注风险调整后的收益指标（如夏普比率），而不仅是总收益率
        - 检查策略在不同市场环境（牛市、熊市、震荡市）下的表现稳定性
        - 分析交易频率和持仓时间是否符合预期和交易成本假设
        - 系统会自动检测并使用与训练模型兼容的环境配置，确保回测结果有效
        - 对于集成学习模型，系统会使用所有子模型进行预测，并按照设定的投票方法组合结果
        """)

        # 添加分隔线
        st.markdown("---")

    # 添加分隔线
    st.markdown('<hr class="section-divider">', unsafe_allow_html=True)

    # 模型选择与加载区
    st.header("模型选择与加载")

    # 检查saved_models目录是否存在
    if not os.path.exists("saved_models"):
        os.makedirs("saved_models")

    # 获取保存的模型列表
    model_files = [f for f in os.listdir("saved_models") if f.endswith(".zip")]

    # 检查是否有当前训练的模型
    if 'current_training_models' in st.session_state and st.session_state.current_training_models:
        # 只显示当前训练的模型（只保留.zip文件，但记录集成目录的关联）
        current_model_files = []
        ensemble_dirs_map = {}  # 用于记录模型文件与其集成目录的映射

        # 确保current_training_models中的路径都存在
        valid_paths = []
        for path in st.session_state.current_training_models:
            if os.path.exists(path):
                valid_paths.append(path)
            else:
                logger.warning(f"模型路径不存在，将从列表中移除: {path}")

        # 更新会话状态中的列表
        st.session_state.current_training_models = valid_paths

        # 如果列表为空，尝试重新加载模型
        if not valid_paths:
            logger.warning("当前训练模型列表为空，尝试重新加载模型")
            # 检查saved_models目录是否存在
            models_dir = "saved_models"
            if os.path.exists(models_dir):
                # 获取所有模型文件
                model_files_paths = [os.path.join(models_dir, f) for f in os.listdir(models_dir) if f.endswith(".zip")]

                # 优先添加BEST模型
                best_models_paths = [f for f in model_files_paths if "BEST" in f]
                for model_path in best_models_paths:
                    if os.path.exists(model_path) and model_path not in st.session_state.current_training_models:
                        st.session_state.current_training_models.append(model_path)
                        logger.info(f"重新加载最佳模型到当前训练模型列表: {os.path.basename(model_path)}")

                        # 检查是否有对应的集成目录
                        ensemble_dir = model_path.replace('.zip', '_ensemble')
                        if os.path.exists(ensemble_dir) and os.path.isdir(ensemble_dir) and ensemble_dir not in st.session_state.current_training_models:
                            st.session_state.current_training_models.append(ensemble_dir)
                            logger.info(f"重新加载最佳模型的集成目录到当前训练模型列表: {os.path.basename(ensemble_dir)}")

                # 如果没有BEST模型，添加最新的模型
                if not best_models_paths:
                    # 按修改时间排序，最新的在前
                    model_files_paths.sort(key=lambda x: os.path.getmtime(x), reverse=True)
                    # 添加最新的模型（最多3个）
                    for model_path in model_files_paths[:3]:
                        if os.path.exists(model_path) and model_path not in st.session_state.current_training_models:
                            st.session_state.current_training_models.append(model_path)
                            logger.info(f"重新加载最新模型到当前训练模型列表: {os.path.basename(model_path)}")

                            # 检查是否有对应的集成目录
                            ensemble_dir = model_path.replace('.zip', '_ensemble')
                            if os.path.exists(ensemble_dir) and os.path.isdir(ensemble_dir) and ensemble_dir not in st.session_state.current_training_models:
                                st.session_state.current_training_models.append(ensemble_dir)
                                logger.info(f"重新加载最新模型的集成目录到当前训练模型列表: {os.path.basename(ensemble_dir)}")

        # 处理模型文件和集成目录
        for path in st.session_state.current_training_models:
            if os.path.exists(path):
                if path.endswith('.zip'):
                    # 如果是模型文件，直接添加
                    current_model_files.append(os.path.basename(path))
                elif os.path.isdir(path) and path.endswith('_ensemble'):
                    # 如果是集成目录，找到对应的模型文件
                    model_path = path.replace('_ensemble', '.zip')
                    if os.path.exists(model_path):
                        model_name = os.path.basename(model_path)
                        # 记录这个模型有集成目录
                        ensemble_dirs_map[model_name] = path
                        # 确保模型文件在列表中
                        if model_name not in current_model_files:
                            current_model_files.append(model_name)
                            logger.info(f"通过集成目录添加模型: {model_name}")

        # 分类模型文件
        best_models = [f for f in current_model_files if 'BEST' in f]
        regular_models = [f for f in current_model_files if 'BEST' not in f]

        # 按时间排序（最新的在前）
        regular_models.sort(reverse=True)

        # 组合模型列表，最佳模型在前
        sorted_models = best_models + regular_models

        # 保存集成目录映射到会话状态，以便后续使用
        st.session_state.ensemble_dirs_map = ensemble_dirs_map
        logger.info(f"集成目录映射: {ensemble_dirs_map}")

        if not sorted_models:
            # 如果没有找到当前训练的模型，使用所有可用模型
            logger.warning("没有找到当前训练的模型，将显示所有可用模型")
            # 分类模型文件
            best_models = [f for f in model_files if 'BEST' in f]
            regular_models = [f for f in model_files if 'BEST' not in f]

            # 按时间排序（最新的在前）
            regular_models.sort(reverse=True)

            # 组合模型列表，最佳模型在前
            sorted_models = best_models + regular_models

            if not sorted_models:
                st.warning("没有找到保存的模型。请先在'DRL智能体训练'页面训练并保存模型。")
                st.stop()

            st.warning("未检测到当前训练会话的模型。显示所有可用模型，但使用非当前训练的模型可能导致兼容性问题。")
        else:
            st.info("显示当前可用的模型，优先显示最佳模型。")
    else:
        # 分类模型文件
        best_models = [f for f in model_files if 'BEST' in f]
        regular_models = [f for f in model_files if 'BEST' not in f]

        # 按时间排序（最新的在前）
        regular_models.sort(reverse=True)

        # 组合模型列表，最佳模型在前
        sorted_models = best_models + regular_models

        if not sorted_models:
            st.warning("没有找到保存的模型。请先在'DRL智能体训练'页面训练并保存模型。")
            st.stop()

        st.warning("未检测到当前训练会话的模型。显示所有可用模型，但使用非当前训练的模型可能导致兼容性问题。")

    # 从列表中选择模型
    selected_model = st.selectbox(
        "选择模型",
        sorted_models,
        format_func=lambda x: f"🌟 {x}" if 'BEST' in x else x
    )
    model_path = os.path.join("saved_models", selected_model)

    # 显示模型信息
    is_best_model = 'BEST' in selected_model
    if is_best_model:
        st.info("您选择了训练过程中表现最佳的模型。这个模型在验证集上的表现优于训练结束时保存的模型。")

    # 检查是否有对应的VecNormalize参数文件
    vec_normalize_path = model_path.replace('.zip', '_vecnormalize.pkl')
    has_vec_normalize = os.path.exists(vec_normalize_path)
    if has_vec_normalize:
        st.info("检测到环境归一化参数文件，加载模型时将自动使用。")

    # 检查是否是集成学习模型
    ensemble_dir = model_path.replace('.zip', '_ensemble')
    # 优先使用会话状态中的映射来检查是否有集成目录
    if 'ensemble_dirs_map' in st.session_state and selected_model in st.session_state.ensemble_dirs_map:
        ensemble_dir = st.session_state.ensemble_dirs_map[selected_model]
        is_ensemble = True
        logger.info(f"从会话状态映射中找到集成目录: {ensemble_dir}")
    else:
        is_ensemble = os.path.exists(ensemble_dir) and os.path.isdir(ensemble_dir)

    if is_ensemble:
        st.info("检测到集成学习模型，将加载集成模型进行回测。")

    # 加载模型按钮
    if st.button("加载模型"):
        try:
            with st.spinner("正在加载模型..."):
                # 准备环境配置（如果有数据）
                eval_env_config = None
                if st.session_state.processed_data is not None:
                    eval_env_config = {
                        'df_processed_data': st.session_state.processed_data,
                        'initial_capital': st.session_state.env_config.get('initial_capital', 100000),
                        'commission_rate': st.session_state.env_config.get('commission_rate', 0.0003),
                        'min_hold_days': st.session_state.env_config.get('min_hold_days', 3),
                        'window_size': 20
                    }

                # 检查是否是集成学习模型
                ensemble_dir = model_path.replace('.zip', '_ensemble')
                # 优先使用会话状态中的映射来检查是否有集成目录
                if 'ensemble_dirs_map' in st.session_state and os.path.basename(model_path) in st.session_state.ensemble_dirs_map:
                    ensemble_dir = st.session_state.ensemble_dirs_map[os.path.basename(model_path)]
                    is_ensemble = True
                    logger.info(f"从会话状态映射中找到集成目录: {ensemble_dir}")
                else:
                    is_ensemble = os.path.exists(ensemble_dir) and os.path.isdir(ensemble_dir)

                # 加载模型
                try:
                    # 尝试使用增强型DRL智能体加载模型（支持集成学习）
                    from quant_trading.agents.enhanced_drl_agent import EnhancedDRLAgent

                    # 创建基本的智能体配置
                    agent_config = {
                        'algorithm': 'PPO',  # 默认算法，会根据模型文件名自动检测
                        'use_gpu': gpu_info['available']
                    }

                    # 从模型文件名中尝试检测算法
                    model_filename = os.path.basename(model_path)
                    for algo in ['PPO', 'A2C', 'DQN', 'SAC', 'TD3', 'DDPG']:
                        if algo in model_filename:
                            agent_config['algorithm'] = algo
                            break

                    # 加载模型
                    drl_agent = EnhancedDRLAgent(eval_env_config, agent_config)
                    drl_agent.load_model(model_path)
                    logger.info(f"使用增强型DRL智能体加载模型: {model_path}")

                    if is_ensemble:
                        logger.info(f"检测到集成学习目录: {ensemble_dir}")
                except Exception as e:
                    logger.warning(f"使用增强型DRL智能体加载模型失败: {str(e)}，将使用标准DRL智能体")
                    # 回退到标准DRL智能体
                    drl_agent = DRLAgent.load_model(model_path, eval_env_config)

                st.session_state.model = drl_agent
                st.session_state.model_loaded = True
                st.session_state.model_path = model_path

                # 显示成功消息
                if is_best_model:
                    st.success(f"🌟 最佳模型已成功加载: {selected_model}")
                else:
                    st.success(f"模型已成功加载: {selected_model}")
        except Exception as e:
            error_msg = str(e)
            logger.error(f"加载模型失败: {error_msg}", exc_info=True)

            # 提供更详细的错误信息
            if "incompatible architecture" in error_msg.lower():
                st.error(f"模型架构与当前环境不兼容。这可能是由于特征数量或环境配置变化导致的。\n\n原始错误: {error_msg}")
                st.info("建议: 尝试使用与训练时相同的特征配置，或者重新训练模型。")
            elif "cuda" in error_msg.lower():
                st.error(f"GPU相关错误。尝试在CPU上加载模型。\n\n原始错误: {error_msg}")
                st.info("建议: 禁用GPU选项，或者检查GPU驱动是否正确安装。")
            elif "vecnormalize" in error_msg.lower():
                st.error(f"VecNormalize参数加载失败。可能缺少归一化参数文件或参数不兼容。\n\n原始错误: {error_msg}")
                st.info("建议: 确保模型和对应的归一化参数文件在同一目录下。")
            elif "file not found" in error_msg.lower():
                st.error(f"模型文件不存在: {model_path}")
                st.info("建议: 检查模型文件是否被移动或删除。")
            else:
                st.error(f"加载模型失败: {error_msg}")

            st.stop()

    # 添加分隔线
    st.markdown('<hr class="section-divider">', unsafe_allow_html=True)

    # 测试环境配置区
    st.header("测试环境配置")

    # 检查是否已加载数据
    if not st.session_state.data_loaded:
        st.warning("请先在'数据中心与环境配置'页面加载数据")
        st.stop()

    # 数据分割
    st.subheader("数据分割")

    # 获取数据的日期范围
    if st.session_state.processed_data is not None:
        data = st.session_state.processed_data
        start_date = data.index[0]
        end_date = data.index[-1]

        # 选择测试数据的日期范围
        col1, col2 = st.columns(2)
        with col1:
            test_start_date = st.date_input("测试开始日期",
                                          value=pd.to_datetime(start_date) + pd.Timedelta(days=int((end_date - start_date).days * 0.7)),
                                          min_value=pd.to_datetime(start_date),
                                          max_value=pd.to_datetime(end_date))
        with col2:
            test_end_date = st.date_input("测试结束日期",
                                        value=pd.to_datetime(end_date),
                                        min_value=pd.to_datetime(test_start_date),
                                        max_value=pd.to_datetime(end_date))

        # 分割数据
        # 确保时区一致性
        start_idx = pd.to_datetime(test_start_date).tz_localize(None)
        end_idx = pd.to_datetime(test_end_date).tz_localize(None)

        # 确保数据索引也是无时区的
        if data.index.tz is not None:
            data = data.tz_localize(None)

        test_data = data.loc[start_idx:end_idx]

        if len(test_data) < 30:
            st.warning("测试数据太少，请选择更长的时间范围")
            st.stop()

        st.info(f"测试数据: {len(test_data)} 条记录，从 {test_start_date} 到 {test_end_date}")
    else:
        st.warning("请先在'数据中心与环境配置'页面生成特征")
        st.stop()

    # 环境参数配置
    st.subheader("环境参数")

    # 基本参数
    st.markdown("#### 基本参数")
    col1, col2 = st.columns(2)

    with col1:
        initial_capital = st.number_input("测试初始资金", 10000, 10000000, st.session_state.env_config.get('initial_capital', 100000), 10000)
        commission_rate = st.number_input("测试手续费率", 0.0, 0.01, st.session_state.env_config.get('commission_rate', 0.0003), 0.0001, format="%.5f")

    with col2:
        min_hold_days = st.number_input("测试最小持仓天数", 1, 10, st.session_state.env_config.get('min_hold_days', 3), 1)
        window_size = st.number_input("观测窗口大小", 5, 50, 20, 1)

    # 市场模拟参数
    st.markdown("#### 市场模拟参数")
    col1, col2 = st.columns(2)

    with col1:
        slippage_model = st.selectbox("滑点模型", ["none", "percentage", "fixed"],
                                    index=["none", "percentage", "fixed"].index(st.session_state.env_config.get('slippage_model', 'percentage')))
        slippage_value = st.number_input("滑点值", 0.0, 0.01, st.session_state.env_config.get('slippage_value', 0.001), 0.0001, format="%.5f")

    with col2:
        allow_short = st.checkbox("允许做空", st.session_state.env_config.get('allow_short', False))
        max_position = st.slider("最大仓位比例", 0.1, 1.0, st.session_state.env_config.get('max_position', 1.0), 0.1)

    # 风险管理参数
    st.markdown("#### 风险管理参数")
    col1, col2, col3 = st.columns(3)

    with col1:
        enable_stop_loss = st.checkbox("启用止损", st.session_state.env_config.get('enable_stop_loss', True))
        stop_loss_threshold = st.slider("止损阈值", 0.01, 0.1, st.session_state.env_config.get('stop_loss_threshold', 0.05), 0.01, format="%.2f")

    with col2:
        enable_take_profit = st.checkbox("启用止盈", st.session_state.env_config.get('enable_take_profit', True))
        take_profit_threshold = st.slider("止盈阈值", 0.05, 0.3, st.session_state.env_config.get('take_profit_threshold', 0.1), 0.01, format="%.2f")

    with col3:
        dynamic_risk_management = st.checkbox("动态风险管理", st.session_state.env_config.get('dynamic_risk_management', True))
        volatility_scaling = st.checkbox("波动率缩放", st.session_state.env_config.get('volatility_scaling', True))

    # 添加分隔线
    st.markdown('<hr class="section-divider">', unsafe_allow_html=True)

    # 评估/回测控制区
    st.header("执行评估/回测")

    if st.button("执行评估/回测"):
        if not st.session_state.model_loaded:
            st.error("请先加载模型")
            st.stop()

        try:
            with st.spinner("正在执行回测..."):
                # 获取模型的观测空间维度
                if hasattr(st.session_state.model.model, 'observation_space'):
                    model_obs_dim = st.session_state.model.model.observation_space.shape[0]
                    logger.info(f"模型期望的观测空间维度: {model_obs_dim}")
                else:
                    model_obs_dim = 34  # 使用错误信息中提到的期望维度
                    logger.warning(f"无法获取模型的观测空间维度，使用默认值: {model_obs_dim}")

                # 计算特征数量
                feature_columns = [col for col in test_data.columns if col not in ['开盘', '最高', '最低', '收盘', '成交量', '成交额']]
                n_features = len(feature_columns)

                # 计算需要的特征数量
                # 观测空间维度 = window_size * n_features + 4 (账户状态)
                required_features = (model_obs_dim - 4) // window_size
                if required_features <= 0:
                    required_features = 1  # 确保至少有一个特征

                logger.info(f"当前特征数量: {n_features}, 需要的特征数量: {required_features}")

                # 如果特征数量不匹配，调整特征
                if n_features != required_features:
                    # 尝试使用优化特征工程适配器重新生成特征
                    try:
                        from quant_trading.features.optimized_feature_engineering_adapter import OptimizedFeatureEngineeringAdapter

                        # 使用原始数据重新生成特征，并指定目标特征数量
                        feature_engineer = OptimizedFeatureEngineeringAdapter(st.session_state.feature_config)
                        test_data = feature_engineer.generate_features(st.session_state.test_data, apply_selection=False, target_feature_count=required_features)

                        logger.info(f"使用OptimizedFeatureEngineeringAdapter调整特征数量为: {required_features}")
                    except Exception as fe:
                        logger.warning(f"使用OptimizedFeatureEngineeringAdapter调整特征失败: {str(fe)}")

                        # 手动调整特征数量
                        adjusted_test_data = test_data[['开盘', '最高', '最低', '收盘', '成交量', '成交额']].copy() if '成交额' in test_data.columns else test_data[['开盘', '最高', '最低', '收盘', '成交量']].copy()

                        # 如果当前特征数量大于需要的特征数量，只保留需要的特征
                        if n_features > required_features:
                            logger.info(f"特征数量过多，从 {n_features} 减少到 {required_features}")
                            for i in range(required_features):
                                if i < len(feature_columns):
                                    adjusted_test_data[feature_columns[i]] = test_data[feature_columns[i]]
                                else:
                                    # 如果需要更多特征但没有足够的列，添加零列
                                    adjusted_test_data[f'feature_{i}'] = 0.0
                        # 如果当前特征数量小于需要的特征数量，添加零列
                        elif n_features < required_features:
                            logger.info(f"特征数量不足，从 {n_features} 增加到 {required_features}")
                            for i in range(n_features):
                                adjusted_test_data[feature_columns[i]] = test_data[feature_columns[i]]
                            for i in range(n_features, required_features):
                                adjusted_test_data[f'dummy_feature_{i}'] = 0.0

                        test_data = adjusted_test_data

                # 创建测试环境
                test_env_config = {
                    'df_processed_data': test_data,
                    'initial_capital': initial_capital,
                    'commission_rate': commission_rate,
                    'min_hold_days': min_hold_days,
                    'window_size': window_size,
                    'allow_short': allow_short,
                    'max_position': max_position,
                    'slippage_model': slippage_model,
                    'slippage_value': slippage_value,
                    'enable_stop_loss': enable_stop_loss,
                    'stop_loss_threshold': stop_loss_threshold,
                    'enable_take_profit': enable_take_profit,
                    'take_profit_threshold': take_profit_threshold,
                    'dynamic_risk_management': dynamic_risk_management,
                    'volatility_scaling': volatility_scaling
                }

                # 检查是否应该使用增强型交易环境
                try:
                    # 尝试导入增强型交易环境
                    from quant_trading.trading.enhanced_trading_environment import EnhancedTradingEnvironment

                    # 添加必要的参数，无论是否有slippage_model
                    if 'action_type' not in test_env_config:
                        test_env_config['action_type'] = 'discrete'
                    if 'n_discrete_actions' not in test_env_config:
                        test_env_config['n_discrete_actions'] = 3

                    # 如果没有滑点模型参数，添加默认值
                    if 'slippage_model' not in test_env_config:
                        test_env_config['slippage_model'] = 'percentage'
                        test_env_config['slippage_value'] = 0.001

                    # 检查是否使用了集成学习
                    if 'ensemble' in agent_config and agent_config['ensemble'].get('use', False):
                        # 启用兼容模式，使增强型环境返回与基础环境相同格式的观测
                        test_env_config['compatibility_mode'] = True
                        logger.info("检测到集成学习配置，启用增强型交易环境的兼容模式")

                    test_env = EnhancedTradingEnvironment(**test_env_config)
                    logger.info("使用增强型交易环境进行回测")
                except Exception as e:
                    logger.warning(f"无法使用增强型交易环境: {str(e)}，回退到基础交易环境")
                    # 移除增强型环境特有的参数
                    for param in ['slippage_model', 'slippage_value', 'enable_stop_loss',
                                 'stop_loss_threshold', 'enable_take_profit', 'take_profit_threshold',
                                 'dynamic_risk_management', 'volatility_scaling', 'compatibility_mode',
                                 'action_type', 'n_discrete_actions']:
                        if param in test_env_config:
                            test_env_config.pop(param)
                    # 尝试使用适配器类以确保兼容性
                    try:
                        from quant_trading.trading.adapter import TradingEnvironmentAdapter
                        test_env = TradingEnvironmentAdapter(**test_env_config)
                        logger.info("使用TradingEnvironmentAdapter进行回测")
                    except ImportError:
                        # 如果适配器不可用，使用标准交易环境
                        test_env = TradingEnvironment(**test_env_config)
                        logger.info("使用标准TradingEnvironment进行回测")

                # 记录回测配置，确保UI设置正确传递到后端
                logger.info("回测配置详情:")
                logger.info(f"测试数据范围: {test_start_date} 到 {test_end_date}, 共 {len(test_data)} 条记录")
                logger.info(f"初始资金: {initial_capital}")
                logger.info(f"手续费率: {commission_rate}")
                logger.info(f"滑点模型: {slippage_model}, 滑点值: {slippage_value}")
                logger.info(f"风险管理参数: 止损={enable_stop_loss}({stop_loss_threshold}), 止盈={enable_take_profit}({take_profit_threshold})")
                logger.info(f"完整回测环境配置: {test_env_config}")

                # 检查环境的观察空间维度是否与模型期望的维度匹配
                env_obs_dim = test_env.observation_space.shape[0]
                logger.info(f"环境观察空间维度: {env_obs_dim}")

                # 如果环境的观察空间维度与模型期望的维度不匹配，则调整环境的观察空间
                if hasattr(st.session_state.model.model, 'observation_space'):
                    model_obs_dim = st.session_state.model.model.observation_space.shape[0]
                    logger.info(f"模型期望的观察空间维度: {model_obs_dim}")

                    if env_obs_dim != model_obs_dim:
                        logger.warning(f"环境观察空间维度 ({env_obs_dim}) 与模型期望的维度 ({model_obs_dim}) 不匹配")

                        # 创建一个观察空间包装器，将环境的观察空间调整为模型期望的维度
                        import numpy as np

                        # 获取原始的observation_space类型
                        original_observation_space = test_env.observation_space

                        # 根据原始observation_space的类型创建新的observation_space
                        try:
                            # 尝试使用gymnasium
                            try:
                                import gymnasium as gym
                                test_env.observation_space = gym.spaces.Box(
                                    low=-np.inf,
                                    high=np.inf,
                                    shape=(model_obs_dim,),
                                    dtype=np.float32
                                )
                                logger.info("使用gymnasium创建新的观察空间")
                            except ImportError:
                                # 如果无法导入gymnasium，尝试使用gym
                                try:
                                    import gym
                                    test_env.observation_space = gym.spaces.Box(
                                        low=-np.inf,
                                        high=np.inf,
                                        shape=(model_obs_dim,),
                                        dtype=np.float32
                                    )
                                    logger.info("使用gym创建新的观察空间")
                                except ImportError:
                                    logger.warning("无法导入gymnasium或gym，尝试使用其他方法调整观察空间维度")
                                    raise
                        except Exception as e:
                            logger.warning(f"无法使用gymnasium或gym创建新的观察空间: {str(e)}")
                            # 直接修改原始observation_space的shape属性
                            if hasattr(original_observation_space, 'shape'):
                                # 使用与原始observation_space相同的类型创建新的observation_space
                                try:
                                    test_env.observation_space = type(original_observation_space)(
                                        low=-np.inf,
                                        high=np.inf,
                                        shape=(model_obs_dim,),
                                        dtype=np.float32
                                    )
                                    logger.info("使用原始observation_space类型创建新的观察空间")
                                except Exception as e:
                                    logger.warning(f"无法创建新的observation_space: {str(e)}")
                                    # 最后的尝试：直接修改shape属性
                                    original_observation_space.shape = (model_obs_dim,)
                                    test_env.observation_space = original_observation_space
                                    logger.info("直接修改原始observation_space的shape属性")

                        # 保存原始的reset和step方法
                        original_reset = test_env.reset
                        original_step = test_env.step

                        # 重写reset方法，调整观察空间维度
                        def wrapped_reset(self, **kwargs):
                            try:
                                # 尝试调用原始reset方法
                                result = original_reset(**kwargs)

                                # 处理不同的返回格式
                                if isinstance(result, tuple) and len(result) == 2:
                                    # 新版API: (obs, info)
                                    obs, info = result
                                else:
                                    # 旧版API: obs
                                    obs = result
                                    info = {}

                                # 确保obs是numpy数组
                                if not isinstance(obs, np.ndarray):
                                    obs = np.array(obs, dtype=np.float32)

                                # 调整观察空间维度
                                if len(obs) < model_obs_dim:
                                    # 如果观察空间维度小于模型期望的维度，则填充零
                                    padding = np.zeros(model_obs_dim - len(obs), dtype=np.float32)
                                    obs = np.concatenate([obs, padding])
                                    logger.info(f"观察空间维度从 {len(obs) - len(padding)} 填充到 {model_obs_dim}")
                                elif len(obs) > model_obs_dim:
                                    # 如果观察空间维度大于模型期望的维度，则截断
                                    obs = obs[:model_obs_dim]
                                    logger.info(f"观察空间维度从 {len(obs) + (len(obs) - model_obs_dim)} 截断到 {model_obs_dim}")

                                return (obs, info) if isinstance(result, tuple) else obs

                            except Exception as e:
                                logger.error(f"wrapped_reset方法出错: {str(e)}", exc_info=True)
                                # 如果出错，尝试返回一个合适的默认值
                                default_obs = np.zeros(model_obs_dim, dtype=np.float32)
                                return (default_obs, {})

                        # 重写step方法，调整观察空间维度
                        def wrapped_step(self, action):
                            try:
                                # 尝试调用原始step方法
                                result = original_step(action)

                                # 处理不同的返回格式
                                if isinstance(result, tuple):
                                    if len(result) == 5:
                                        # 新版API: (obs, reward, terminated, truncated, info)
                                        next_obs, reward, terminated, truncated, info = result
                                        done = terminated or truncated
                                    elif len(result) == 4:
                                        # 旧版API: (obs, reward, done, info)
                                        next_obs, reward, done, info = result
                                        terminated = done
                                        truncated = False
                                    else:
                                        raise ValueError(f"Unexpected step result format: {result}")
                                else:
                                    raise ValueError(f"Step result is not a tuple: {result}")

                                # 确保next_obs是numpy数组
                                if not isinstance(next_obs, np.ndarray):
                                    next_obs = np.array(next_obs, dtype=np.float32)

                                # 调整观察空间维度
                                if len(next_obs) < model_obs_dim:
                                    # 如果观察空间维度小于模型期望的维度，则填充零
                                    padding = np.zeros(model_obs_dim - len(next_obs), dtype=np.float32)
                                    next_obs = np.concatenate([next_obs, padding])
                                    logger.info(f"观察空间维度从 {len(next_obs) - len(padding)} 填充到 {model_obs_dim}")
                                elif len(next_obs) > model_obs_dim:
                                    # 如果观察空间维度大于模型期望的维度，则截断
                                    next_obs = next_obs[:model_obs_dim]
                                    logger.info(f"观察空间维度从 {len(next_obs) + (len(next_obs) - model_obs_dim)} 截断到 {model_obs_dim}")

                                # 返回与原始格式相同的结果
                                if len(result) == 5:
                                    return next_obs, reward, terminated, truncated, info
                                else:
                                    return next_obs, reward, done, info

                            except Exception as e:
                                logger.error(f"wrapped_step方法出错: {str(e)}", exc_info=True)
                                # 如果出错，尝试返回一个合适的默认值
                                default_obs = np.zeros(model_obs_dim, dtype=np.float32)
                                return default_obs, 0.0, True, True, {}

                        # 将包装方法绑定到环境对象
                        import types
                        test_env.reset = types.MethodType(wrapped_reset, test_env)
                        test_env.step = types.MethodType(wrapped_step, test_env)

                        logger.info(f"已调整环境观察空间维度为: {model_obs_dim}")

                # 执行回测
                observation, info = test_env.reset()
                done = False

                while not done:
                    try:
                        # 确保observation是numpy数组
                        if not isinstance(observation, np.ndarray):
                            observation = np.array(observation, dtype=np.float32)

                        # 检查observation的维度是否与模型期望的维度匹配
                        if hasattr(st.session_state.model.model, 'observation_space'):
                            model_obs_dim = st.session_state.model.model.observation_space.shape[0]
                            if len(observation) != model_obs_dim:
                                logger.warning(f"观察空间维度不匹配: 当前 {len(observation)}, 期望 {model_obs_dim}")
                                # 调整observation的维度
                                if len(observation) < model_obs_dim:
                                    # 如果observation的维度小于模型期望的维度，则填充零
                                    padding = np.zeros(model_obs_dim - len(observation), dtype=np.float32)
                                    observation = np.concatenate([observation, padding])
                                    logger.info(f"观察空间维度从 {len(observation) - len(padding)} 填充到 {model_obs_dim}")
                                else:
                                    # 如果observation的维度大于模型期望的维度，则截断
                                    observation = observation[:model_obs_dim]
                                    logger.info(f"观察空间维度从 {len(observation) + (len(observation) - model_obs_dim)} 截断到 {model_obs_dim}")

                        # 预测动作
                        action = st.session_state.model.predict_action(observation, deterministic=True)

                        # 执行动作
                        observation, reward, terminated, truncated, info = test_env.step(action)
                        done = terminated or truncated
                    except Exception as e:
                        logger.error(f"回测过程中发生错误: {str(e)}", exc_info=True)
                        st.error(f"回测过程中发生错误: {str(e)}")
                        break

                # 获取交易记录和组合价值历史
                trades = test_env.get_trades_history()
                portfolio_values = test_env.get_portfolio_history()

                # 获取基准数据（买入并持有）
                benchmark_values = None
                if len(test_data) > 0:
                    # 简单的买入并持有策略
                    initial_price = test_data['收盘'].iloc[0]
                    final_price = test_data['收盘'].iloc[-1]
                    benchmark_return = final_price / initial_price
                    benchmark_values = pd.Series(
                        [initial_capital * (1 + (test_data['收盘'].iloc[i] / initial_price - 1)) for i in range(len(test_data))],
                        index=test_data.index
                    )

                # 计算性能指标
                performance_analyzer = PerformanceAnalyzer()
                metrics = performance_analyzer.analyze(trades, portfolio_values, benchmark_values)

                # 保存回测结果到会话状态
                st.session_state.backtest_results = {
                    'trades': trades,
                    'portfolio_values': portfolio_values,
                    'benchmark_values': benchmark_values,
                    'metrics': metrics
                }

                st.success("回测完成！")

        except Exception as e:
            st.error(f"回测过程中发生错误: {str(e)}")
            logger.error(f"回测过程中发生错误: {str(e)}", exc_info=True)

    # 性能指标展示区
    if 'backtest_results' in st.session_state and st.session_state.backtest_results:
        # 添加分隔线
        st.markdown('<hr class="section-divider">', unsafe_allow_html=True)

        st.header("性能指标")

        metrics = st.session_state.backtest_results['metrics']

        # 创建性能指标表格
        col1, col2, col3 = st.columns(3)

        with col1:
            st.subheader("收益指标")
            st.metric("总收益率", f"{metrics['total_return']:.2%}")
            st.metric("年化收益率", f"{metrics['annualized_return']:.2%}")
            if 'benchmark_total_return' in metrics:
                st.metric("基准总收益率", f"{metrics['benchmark_total_return']:.2%}")
                st.metric("超额收益", f"{metrics['total_return'] - metrics['benchmark_total_return']:.2%}")

        with col2:
            st.subheader("风险指标")
            st.metric("最大回撤", f"{metrics['max_drawdown']:.2%}")
            st.metric("最大回撤持续天数", f"{metrics['max_drawdown_duration']}")
            if 'benchmark_max_drawdown' in metrics:
                st.metric("基准最大回撤", f"{metrics['benchmark_max_drawdown']:.2%}")

        with col3:
            st.subheader("风险调整收益")
            st.metric("夏普比率", f"{metrics['sharpe_ratio']:.2f}")
            st.metric("索提诺比率", f"{metrics['sortino_ratio']:.2f}")
            st.metric("卡玛比率", f"{metrics['calmar_ratio']:.2f}")
            if 'information_ratio' in metrics:
                st.metric("信息比率", f"{metrics['information_ratio']:.2f}")

        # 交易统计
        if 'total_trades' in metrics:
            st.subheader("交易统计")
            col1, col2, col3, col4 = st.columns(4)

            with col1:
                st.metric("总交易次数", f"{metrics['total_trades']}")
                st.metric("平均持仓天数", f"{metrics['avg_holding_period']:.1f}")

            with col2:
                st.metric("胜率", f"{metrics['win_rate']:.2%}")
                st.metric("盈亏比", f"{metrics.get('profit_loss_ratio', 0):.2f}")

            with col3:
                st.metric("平均盈利", f"{metrics['avg_profit']:.2f}")
                st.metric("平均亏损", f"{metrics['avg_loss']:.2f}")

            with col4:
                st.metric("最大单笔盈利", f"{metrics.get('max_profit', 0):.2f}")
                st.metric("最大单笔亏损", f"{metrics.get('max_loss', 0):.2f}")

        # 风险管理统计
        if 'stop_loss_events' in metrics or 'take_profit_events' in metrics:
            st.subheader("风险管理统计")
            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric("止损触发次数", f"{metrics.get('stop_loss_count', 0)}")
                if 'stop_loss_count' in metrics and metrics['stop_loss_count'] > 0:
                    st.metric("止损平均亏损", f"{metrics.get('avg_stop_loss', 0):.2%}")

            with col2:
                st.metric("止盈触发次数", f"{metrics.get('take_profit_count', 0)}")
                if 'take_profit_count' in metrics and metrics['take_profit_count'] > 0:
                    st.metric("止盈平均收益", f"{metrics.get('avg_take_profit', 0):.2%}")

            with col3:
                st.metric("风险管理贡献", f"{metrics.get('risk_management_contribution', 0):.2%}")
                st.metric("波动率缩放平均因子", f"{metrics.get('avg_volatility_scaling', 1.0):.2f}")

        # 添加分隔线
        st.markdown('<hr class="section-divider">', unsafe_allow_html=True)

        # 可视化结果区
        st.header("可视化结果")

        # 净值曲线图
        st.subheader("净值曲线")
        portfolio_values = st.session_state.backtest_results['portfolio_values']
        benchmark_values = st.session_state.backtest_results['benchmark_values']

        fig = plt.figure(figsize=(12, 6))
        plt.plot(portfolio_values.index, portfolio_values, label="策略")
        if benchmark_values is not None:
            plt.plot(benchmark_values.index, benchmark_values, label="基准", alpha=0.7)
        plt.title("净值曲线")
        plt.xlabel("日期")
        plt.ylabel("净值")
        plt.legend()
        plt.grid(True)
        st.pyplot(fig)

        # 月度收益热图
        if 'monthly_returns' in metrics and len(metrics['monthly_returns']) > 0:
            st.subheader("月度收益热图")

            try:
                # 将月度收益转换为热图格式
                monthly_returns = metrics['monthly_returns'].copy()  # 创建副本避免修改原始数据

                # 确保索引是日期类型
                if not isinstance(monthly_returns.index, pd.DatetimeIndex):
                    monthly_returns.index = pd.to_datetime(monthly_returns.index)

                # 创建年月索引
                monthly_returns.index = pd.MultiIndex.from_arrays([
                    monthly_returns.index.year,
                    monthly_returns.index.month
                ], names=['year', 'month'])

                # 将Series转换为DataFrame并透视
                monthly_returns_pivot = monthly_returns.reset_index()
                monthly_returns_pivot = monthly_returns_pivot.pivot(index='year', columns='month', values=0)

                # 设置月份名称
                month_names = {
                    1: '一月', 2: '二月', 3: '三月', 4: '四月',
                    5: '五月', 6: '六月', 7: '七月', 8: '八月',
                    9: '九月', 10: '十月', 11: '十一月', 12: '十二月'
                }
                monthly_returns_pivot.columns = [month_names.get(m, str(m)) for m in monthly_returns_pivot.columns]

                fig, ax = plt.subplots(figsize=(12, 6))
                sns.heatmap(monthly_returns_pivot, annot=True, fmt=".2%", cmap="RdYlGn", center=0, ax=ax)
                plt.title("月度收益热图")
                st.pyplot(fig)
            except Exception as e:
                st.warning(f"生成月度收益热图时出错: {str(e)}")
                logger.error(f"生成月度收益热图时出错: {str(e)}", exc_info=True)

                # 显示简化版月度收益表格
                st.write("月度收益表格:")
                monthly_returns_df = pd.DataFrame(metrics['monthly_returns'])
                monthly_returns_df.columns = ['收益率']
                monthly_returns_df['收益率'] = monthly_returns_df['收益率'].map('{:.2%}'.format)
                st.dataframe(monthly_returns_df)

        # 价格图与交易点标记
        st.subheader("价格与交易点")
        trades = st.session_state.backtest_results['trades']

        fig, ax = plt.subplots(figsize=(12, 6))
        ax.plot(test_data.index, test_data['收盘'], label="收盘价")

        # 标记买入点和卖出点
        buy_dates = [trade['date'] for trade in trades if trade['action'] == 'buy']
        buy_prices = [trade['price'] for trade in trades if trade['action'] == 'buy']

        sell_dates = [trade['date'] for trade in trades if trade['action'] == 'sell']
        sell_prices = [trade['price'] for trade in trades if trade['action'] == 'sell']

        ax.scatter(buy_dates, buy_prices, color='green', marker='^', s=100, label="买入")
        ax.scatter(sell_dates, sell_prices, color='red', marker='v', s=100, label="卖出")

        plt.title("价格与交易点")
        plt.xlabel("日期")
        plt.ylabel("价格")
        plt.legend()
        plt.grid(True)
        st.pyplot(fig)

        # 回撤图
        st.subheader("回撤图")

        # 计算回撤序列
        portfolio_values_arr = portfolio_values.values
        peak = np.maximum.accumulate(portfolio_values_arr)
        drawdown = (portfolio_values_arr - peak) / peak

        fig, ax = plt.subplots(figsize=(12, 6))
        ax.fill_between(portfolio_values.index, drawdown, 0, color='red', alpha=0.3)
        ax.plot(portfolio_values.index, drawdown, color='red', label="回撤")
        plt.title("回撤图")
        plt.xlabel("日期")
        plt.ylabel("回撤")
        plt.legend()
        plt.grid(True)
        st.pyplot(fig)

        # 交易记录表格
        st.subheader("交易记录")
        if trades:
            trades_df = pd.DataFrame(trades)
            st.dataframe(trades_df)

elif page == "实况信号决策":
    # 使用自定义样式的标题
    st.markdown('<div class="main-header">实况信号决策</div>', unsafe_allow_html=True)

    # 添加用户指南
    with st.expander("📚 用户指南", expanded=True):
        st.markdown("""
        ## 实况信号决策指南

        本页面用于获取最新市场数据并生成实时交易信号，帮助您做出投资决策。

        ### 使用步骤：

        1. **模型选择与加载**
           - 从下拉列表中选择要使用的模型（🌟标记的是最佳模型）
           - 系统会自动识别当前训练会话的模型，优先显示最佳模型
           - 点击"加载模型"按钮加载所选模型
           - 如果是集成学习模型，系统会自动加载所有子模型

        2. **最新市场数据获取**
           - 输入金融产品代码（支持股票和指数，例如：`sh000001`为上证指数，`index_000300`为沪深300指数）
           - 设置数据日期范围（默认获取最近90天数据，确保数据量足够）
           - 勾选"使用最新交易日"获取最新市场数据
           - 点击"获取最新数据"按钮下载并处理数据
           - 系统会自动使用与训练时相同的特征工程配置处理数据

        3. **交易信号生成**
           - 设置环境参数（初始资金、手续费率、最小持仓天数等）
           - 点击"生成交易信号"按钮获取当前的交易建议
           - 系统会自动调整特征维度以匹配模型要求
           - 查看信号详情（买入、卖出或持仓）和当前价格

        4. **信号分析与可视化**
           - 查看价格走势图和交易信号标记
           - 分析模型的决策依据
           - 查看历史信号记录和表现

        ### 重要提示：
        - 系统生成的交易信号仅供参考，不构成投资建议
        - 实际交易时应结合自身风险承受能力和市场判断
        - 定期更新数据以获取最新的市场信息
        - 不同模型可能会产生不同的交易信号，建议比较分析
        - 系统会自动缓存特征数据以提高性能，减少重复计算
        - 如果数据获取或特征生成速度较慢，请耐心等待，这是为了确保信号准确性
        - 期货和加密货币数据提取功能已被禁用，请使用股票或指数数据
        """)

        # 添加分隔线
        st.markdown("---")

    # 模型选择与加载区
    st.header("模型选择与加载")

    # 检查saved_models目录是否存在
    if not os.path.exists("saved_models"):
        os.makedirs("saved_models")

    # 获取保存的模型列表
    model_files = [f for f in os.listdir("saved_models") if f.endswith(".zip")]

    # 检查是否有当前训练的模型
    if 'current_training_models' in st.session_state and st.session_state.current_training_models:
        # 只显示当前训练的模型（只保留.zip文件，但记录集成目录的关联）
        current_model_files = []
        ensemble_dirs_map = {}  # 用于记录模型文件与其集成目录的映射

        # 确保current_training_models中的路径都存在
        valid_paths = []
        for path in st.session_state.current_training_models:
            if os.path.exists(path):
                valid_paths.append(path)
            else:
                logger.warning(f"模型路径不存在，将从列表中移除: {path}")

        # 更新会话状态中的列表
        st.session_state.current_training_models = valid_paths

        # 如果列表为空，尝试重新加载模型
        if not valid_paths:
            logger.warning("当前训练模型列表为空，尝试重新加载模型")
            # 检查saved_models目录是否存在
            models_dir = "saved_models"
            if os.path.exists(models_dir):
                # 获取所有模型文件
                model_files_paths = [os.path.join(models_dir, f) for f in os.listdir(models_dir) if f.endswith(".zip")]

                # 优先添加BEST模型
                best_models_paths = [f for f in model_files_paths if "BEST" in f]
                for model_path in best_models_paths:
                    if os.path.exists(model_path) and model_path not in st.session_state.current_training_models:
                        st.session_state.current_training_models.append(model_path)
                        logger.info(f"重新加载最佳模型到当前训练模型列表: {os.path.basename(model_path)}")

                        # 检查是否有对应的集成目录
                        ensemble_dir = model_path.replace('.zip', '_ensemble')
                        if os.path.exists(ensemble_dir) and os.path.isdir(ensemble_dir) and ensemble_dir not in st.session_state.current_training_models:
                            st.session_state.current_training_models.append(ensemble_dir)
                            logger.info(f"重新加载最佳模型的集成目录到当前训练模型列表: {os.path.basename(ensemble_dir)}")

                # 如果没有BEST模型，添加最新的模型
                if not best_models_paths:
                    # 按修改时间排序，最新的在前
                    model_files_paths.sort(key=lambda x: os.path.getmtime(x), reverse=True)
                    # 添加最新的模型（最多3个）
                    for model_path in model_files_paths[:3]:
                        if os.path.exists(model_path) and model_path not in st.session_state.current_training_models:
                            st.session_state.current_training_models.append(model_path)
                            logger.info(f"重新加载最新模型到当前训练模型列表: {os.path.basename(model_path)}")

                            # 检查是否有对应的集成目录
                            ensemble_dir = model_path.replace('.zip', '_ensemble')
                            if os.path.exists(ensemble_dir) and os.path.isdir(ensemble_dir) and ensemble_dir not in st.session_state.current_training_models:
                                st.session_state.current_training_models.append(ensemble_dir)
                                logger.info(f"重新加载最新模型的集成目录到当前训练模型列表: {os.path.basename(ensemble_dir)}")

        # 处理模型文件和集成目录
        for path in st.session_state.current_training_models:
            if os.path.exists(path):
                if path.endswith('.zip'):
                    # 如果是模型文件，直接添加
                    current_model_files.append(os.path.basename(path))
                elif os.path.isdir(path) and path.endswith('_ensemble'):
                    # 如果是集成目录，找到对应的模型文件
                    model_path = path.replace('_ensemble', '.zip')
                    if os.path.exists(model_path):
                        model_name = os.path.basename(model_path)
                        # 记录这个模型有集成目录
                        ensemble_dirs_map[model_name] = path
                        # 确保模型文件在列表中
                        if model_name not in current_model_files:
                            current_model_files.append(model_name)
                            logger.info(f"通过集成目录添加模型: {model_name}")

        # 分类模型文件
        best_models = [f for f in current_model_files if 'BEST' in f]
        regular_models = [f for f in current_model_files if 'BEST' not in f]

        # 按时间排序（最新的在前）
        regular_models.sort(reverse=True)

        # 组合模型列表，最佳模型在前
        sorted_models = best_models + regular_models

        # 保存集成目录映射到会话状态，以便后续使用
        st.session_state.ensemble_dirs_map = ensemble_dirs_map
        logger.info(f"集成目录映射: {ensemble_dirs_map}")

        if not sorted_models:
            # 如果没有找到当前训练的模型，使用所有可用模型
            logger.warning("没有找到当前训练的模型，将显示所有可用模型")
            # 分类模型文件
            best_models = [f for f in model_files if 'BEST' in f]
            regular_models = [f for f in model_files if 'BEST' not in f]

            # 按时间排序（最新的在前）
            regular_models.sort(reverse=True)

            # 组合模型列表，最佳模型在前
            sorted_models = best_models + regular_models

            if not sorted_models:
                st.warning("没有找到保存的模型。请先在'DRL智能体训练'页面训练并保存模型。")
                st.stop()

            st.warning("未检测到当前训练会话的模型。显示所有可用模型，但使用非当前训练的模型可能导致兼容性问题。")
        else:
            st.info("显示当前可用的模型，优先显示最佳模型。")
    else:
        # 分类模型文件
        best_models = [f for f in model_files if 'BEST' in f]
        regular_models = [f for f in model_files if 'BEST' not in f]

        # 按时间排序（最新的在前）
        regular_models.sort(reverse=True)

        # 组合模型列表，最佳模型在前
        sorted_models = best_models + regular_models

        if not sorted_models:
            st.warning("没有找到保存的模型。请先在'DRL智能体训练'页面训练并保存模型。")
            st.stop()

        st.warning("未检测到当前训练会话的模型。显示所有可用模型，但使用非当前训练的模型可能导致兼容性问题。")

    # 从列表中选择模型
    selected_model = st.selectbox(
        "选择模型",
        sorted_models,
        format_func=lambda x: f"🌟 {x}" if 'BEST' in x else x
    )
    model_path = os.path.join("saved_models", selected_model)

    # 显示模型信息
    is_best_model = 'BEST' in selected_model
    if is_best_model:
        st.info("您选择了训练过程中表现最佳的模型。这个模型在验证集上的表现优于训练结束时保存的模型。")

    # 检查是否有对应的VecNormalize参数文件
    vec_normalize_path = model_path.replace('.zip', '_vecnormalize.pkl')
    has_vec_normalize = os.path.exists(vec_normalize_path)
    if has_vec_normalize:
        st.info("检测到环境归一化参数文件，加载模型时将自动使用。")

    # 检查是否是集成学习模型
    ensemble_dir = model_path.replace('.zip', '_ensemble')
    # 优先使用会话状态中的映射来检查是否有集成目录
    if 'ensemble_dirs_map' in st.session_state and selected_model in st.session_state.ensemble_dirs_map:
        ensemble_dir = st.session_state.ensemble_dirs_map[selected_model]
        is_ensemble = True
        logger.info(f"从会话状态映射中找到集成目录: {ensemble_dir}")
    else:
        is_ensemble = os.path.exists(ensemble_dir) and os.path.isdir(ensemble_dir)

    if is_ensemble:
        st.info("检测到集成学习模型，将加载集成模型进行信号生成。")

    # 加载模型按钮
    if st.button("加载模型"):
        try:
            with st.spinner("正在加载模型..."):
                # 检查是否是集成学习模型
                ensemble_dir = model_path.replace('.zip', '_ensemble')
                # 优先使用会话状态中的映射来检查是否有集成目录
                if 'ensemble_dirs_map' in st.session_state and os.path.basename(model_path) in st.session_state.ensemble_dirs_map:
                    ensemble_dir = st.session_state.ensemble_dirs_map[os.path.basename(model_path)]
                    is_ensemble = True
                    logger.info(f"从会话状态映射中找到集成目录: {ensemble_dir}")
                else:
                    is_ensemble = os.path.exists(ensemble_dir) and os.path.isdir(ensemble_dir)

                # 尝试使用增强型DRL智能体加载模型（支持集成学习）
                try:
                    from quant_trading.agents.enhanced_drl_agent import EnhancedDRLAgent

                    # 创建基本的智能体配置
                    agent_config = {
                        'algorithm': 'PPO',  # 默认算法，会根据模型文件名自动检测
                        'use_gpu': gpu_info['available']
                    }

                    # 从模型文件名中尝试检测算法
                    model_filename = os.path.basename(model_path)
                    for algo in ['PPO', 'A2C', 'DQN', 'SAC', 'TD3', 'DDPG']:
                        if algo in model_filename:
                            agent_config['algorithm'] = algo
                            break

                    # 加载模型
                    drl_agent = EnhancedDRLAgent(None, agent_config)  # 暂时传入None作为环境配置
                    drl_agent.load_model(model_path)
                    logger.info(f"使用增强型DRL智能体加载模型: {model_path}")

                    if is_ensemble:
                        logger.info(f"检测到集成学习目录: {ensemble_dir}")
                except Exception as e:
                    logger.warning(f"使用增强型DRL智能体加载模型失败: {str(e)}，将使用标准DRL智能体")
                    # 回退到标准DRL智能体
                    drl_agent = DRLAgent.load_model(model_path)

                st.session_state.model = drl_agent
                st.session_state.model_loaded = True
                st.session_state.model_path = model_path

                # 显示成功消息
                if is_best_model:
                    st.success(f"🌟 最佳模型已成功加载: {selected_model}")
                else:
                    st.success(f"模型已成功加载: {selected_model}")
        except Exception as e:
            error_msg = str(e)
            logger.error(f"加载模型失败: {error_msg}", exc_info=True)

            # 提供更详细的错误信息
            if "incompatible architecture" in error_msg.lower():
                st.error(f"模型架构与当前环境不兼容。这可能是由于特征数量或环境配置变化导致的。\n\n原始错误: {error_msg}")
                st.info("建议: 尝试使用与训练时相同的特征配置，或者重新训练模型。")
            elif "cuda" in error_msg.lower():
                st.error(f"GPU相关错误。尝试在CPU上加载模型。\n\n原始错误: {error_msg}")
                st.info("建议: 禁用GPU选项，或者检查GPU驱动是否正确安装。")
            elif "vecnormalize" in error_msg.lower():
                st.error(f"VecNormalize参数加载失败。可能缺少归一化参数文件或参数不兼容。\n\n原始错误: {error_msg}")
                st.info("建议: 确保模型和对应的归一化参数文件在同一目录下。")
            elif "file not found" in error_msg.lower():
                st.error(f"模型文件不存在: {model_path}")
                st.info("建议: 检查模型文件是否被移动或删除。")
            else:
                st.error(f"加载模型失败: {error_msg}")

            st.stop()

    # 数据获取区
    st.header("最新市场数据获取")

    # 金融产品代码输入
    stock_code = st.text_input("金融产品代码", "sh000001")

    with st.expander("金融产品代码格式说明", expanded=True):
        st.markdown("""
        ### 支持的金融产品代码格式

        #### 股票（推荐使用）
        - 上海证券交易所: `sh` + 6位数字，例如 `sh600000`、`sh601398`
        - 深圳证券交易所: `sz` + 6位数字，例如 `sz000001`、`sz002230`

        #### 指数（推荐使用）
        格式: `index_` + 指数代码

        **上证指数系列**
        - `index_000001`: 上证综指
        - `index_000016`: 上证50指数
        - `index_000300`: 沪深300指数
        - `index_000905`: 中证500指数

        **深证指数系列**
        - `index_399001`: 深证成指
        - `index_399006`: 创业板指数
        - `index_399673`: 创业板50指数

        > **注意**：请严格按照上述格式输入代码，系统将根据代码前缀自动识别数据类型。

        > **重要提示**：期货和加密货币数据提取功能已被禁用，请使用股票或指数数据。
        """)

    # 日期选择
    col1, col2 = st.columns(2)
    with col1:
        # 默认获取最近90天的数据
        end_date = datetime.now()
        start_date = end_date - timedelta(days=90)

        start_date = st.date_input("开始日期", start_date)
    with col2:
        end_date = st.date_input("结束日期 (最新)", end_date)

        # 获取最新交易日选项
        use_latest = st.checkbox("使用最新交易日", True)

    # 获取数据按钮
    if st.button("获取最新数据"):
        try:
            with st.spinner("正在获取最新市场数据..."):
                # 使用适配器类以确保兼容性
                try:
                    from quant_trading.data.adapter import DataHandlerAdapter
                    data_handler = DataHandlerAdapter()
                    logger.info("使用DataHandlerAdapter获取数据")
                except ImportError:
                    # 如果适配器不可用，使用标准数据处理器
                    data_handler = DataHandler()
                    logger.info("使用标准DataHandler获取数据")

                # 如果选择使用最新交易日，获取最新交易日期
                if use_latest:
                    latest_date = data_handler.get_latest_trading_date()
                    st.info(f"最新交易日: {latest_date}")
                    try:
                        # 尝试解析日期字符串
                        if isinstance(latest_date, str):
                            end_date = datetime.strptime(latest_date, "%Y-%m-%d").date()
                        elif isinstance(latest_date, datetime):
                            end_date = latest_date.date()
                        elif isinstance(latest_date, pd.Timestamp):
                            end_date = latest_date.date()
                        else:
                            # 如果是其他类型，记录警告并使用当前日期
                            logger.warning(f"未知的日期类型: {type(latest_date)}，使用当前日期")
                            end_date = datetime.now().date()
                    except Exception as e:
                        # 如果解析失败，记录错误并使用当前日期
                        logger.error(f"解析日期失败: {str(e)}，使用当前日期")
                        end_date = datetime.now().date()

                # 获取数据
                data = data_handler.get_stock_data(
                    stock_code=stock_code,
                    start_date=start_date.strftime("%Y-%m-%d"),
                    end_date=end_date.strftime("%Y-%m-%d"),
                    frequency="日线"
                )

                # 保存到会话状态
                st.session_state.latest_data = data
                st.session_state.latest_data_loaded = True

                # 显示数据概览
                st.subheader("最新数据概览")
                st.dataframe(data.tail())

                # 绘制最新价格走势
                fig, ax = plt.subplots(figsize=(10, 6))
                ax.plot(data.index, data['收盘'])
                ax.set_title(f"{stock_code} 最新价格走势")
                ax.set_xlabel("日期")
                ax.set_ylabel("价格")
                st.pyplot(fig)

                logger.info(f"成功获取股票 {stock_code} 的最新数据，共 {len(data)} 条记录")
                st.success(f"成功获取最新数据，共 {len(data)} 条记录")

                # 自动进行特征工程
                with st.spinner("正在自动生成特征..."):
                    # 检查是否有缓存的特征配置
                    feature_config = st.session_state.get('feature_config', None)

                    # 生成更详细的缓存键，包含股票代码、日期范围和特征配置的哈希值
                    config_hash = "default"
                    if feature_config:
                        import hashlib
                        import json
                        # 将特征配置转换为JSON字符串，然后计算哈希值
                        try:
                            config_str = json.dumps(feature_config, sort_keys=True)
                            config_hash = hashlib.md5(config_str.encode()).hexdigest()[:8]
                        except:
                            pass

                    cache_key = f"{stock_code}_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}_{config_hash}"

                    # 创建全局特征缓存（如果不存在）
                    if 'global_feature_cache' not in st.session_state:
                        st.session_state.global_feature_cache = {}

                    # 检查是否有缓存的处理后数据
                    if cache_key in st.session_state.global_feature_cache:
                        logger.info(f"使用全局缓存的特征数据: {cache_key}")
                        processed_data = st.session_state.global_feature_cache[cache_key]
                        st.success(f"使用缓存的特征数据，形状: {processed_data.shape}")
                    else:
                        # 使用增强特征工程器，但禁用所有计算密集型特征
                        try:
                            from quant_trading.features.enhanced_feature_engineer import EnhancedFeatureEngineer
                            # 创建一个极简特征配置，最大限度减少计算量
                            minimal_config = {
                                'price_features': {'use': True},
                                'technical_indicators': {
                                    'use': True,
                                    'sma': {'use': True, 'periods': [5, 10, 20]},  # 只使用少量SMA
                                    'ema': {'use': True, 'periods': [5, 10]},      # 只使用少量EMA
                                    'rsi': {'use': True, 'periods': [14]},         # 只使用标准RSI
                                    'macd': {'use': False},                        # 禁用MACD
                                    'bbands': {'use': False},                      # 禁用布林带
                                    'stoch': {'use': False},                       # 禁用随机指标
                                    'adx': {'use': False},                         # 禁用ADX
                                    'atr': {'use': False}                          # 禁用ATR
                                },
                                'rolling_stats': {
                                    'use': True,
                                    'windows': [5, 10],                            # 只使用小窗口
                                    'stats': ['mean', 'std']                       # 只计算均值和标准差
                                },
                                'advanced_features': {'use': False},               # 禁用高级特征
                                'market_microstructure': {'use': False},           # 禁用市场微观结构特征
                                'time_features': {'use': False},                   # 禁用时间特征
                                'pattern_features': {'use': False},                # 禁用形态特征
                                'feature_selection': {'use': False},               # 禁用特征选择
                                'transform_features': {'use': False},              # 禁用特征变换
                                'cross_asset': {'use': False},                     # 禁用跨资产特征
                                'check_stationarity': False,                       # 禁用平稳性检查
                                'avoid_lookahead': True                            # 避免前视偏差
                            }

                            # 如果有训练时的特征配置，优先使用它
                            if feature_config:
                                logger.info("使用训练时的特征配置")
                                feature_engineer = EnhancedFeatureEngineer(feature_config)
                            else:
                                logger.info("使用极简特征配置")
                                feature_engineer = EnhancedFeatureEngineer(minimal_config)

                            logger.info("使用增强特征工程器自动生成特征")
                        except ImportError:
                            # 如果增强特征工程器不可用，使用标准特征工程器
                            logger.info("增强特征工程器不可用，使用标准特征工程器")
                            feature_engineer = FeatureEngineer(feature_config)

                        # 生成特征，禁用特征选择以提高性能
                        start_time = time.time()
                        processed_data = feature_engineer.generate_features(data, apply_selection=False)
                        end_time = time.time()
                        logger.info(f"特征生成耗时: {end_time - start_time:.2f} 秒")

                        # 删除NaN值
                        processed_data = processed_data.dropna()

                        # 缓存处理后的数据到全局缓存
                        st.session_state.global_feature_cache[cache_key] = processed_data
                        logger.info(f"特征数据已缓存到全局缓存: {cache_key}")

                        # 限制缓存大小，避免内存占用过大
                        if len(st.session_state.global_feature_cache) > 10:
                            # 删除最早的缓存项
                            oldest_key = list(st.session_state.global_feature_cache.keys())[0]
                            del st.session_state.global_feature_cache[oldest_key]
                            logger.info(f"缓存项过多，删除最早的缓存: {oldest_key}")

                    # 保存到会话状态
                    st.session_state.latest_processed_data = processed_data

                    logger.info(f"自动特征生成完成，处理后数据形状: {processed_data.shape}")
                    st.success(f"自动特征生成完成，处理后数据形状: {processed_data.shape}")
        except Exception as e:
            st.error(f"获取最新数据失败: {str(e)}")
            logger.error(f"获取最新数据失败: {str(e)}", exc_info=True)

    # 特征信息区
    if 'latest_processed_data' in st.session_state:
        st.header("特征信息")

        # 使用与训练时相同的特征配置
        if 'feature_config' in st.session_state:
            feature_config = st.session_state.feature_config
            st.info("使用与训练时相同的特征配置")

            # 显示特征配置概览
            with st.expander("查看特征配置详情"):
                st.json(feature_config)
        else:
            st.info("使用自动计算的特征配置")

        # 显示处理后的数据概览
        st.subheader("处理后的数据概览")
        st.dataframe(st.session_state.latest_processed_data.tail())

    # 信号生成区
    st.header("交易信号生成")

    # 检查是否已加载模型
    if not st.session_state.get('model_loaded', False):
        st.warning("请先加载模型")
        st.stop()

    # 如果没有最新处理数据，自动获取数据并生成特征
    if not st.session_state.get('latest_processed_data') is not None:
        st.info("正在自动获取最新数据并生成特征...")

        try:
            with st.spinner("正在获取最新市场数据..."):
                # 默认获取上证指数的最新数据
                default_stock_code = "sh000001"

                # 获取最新交易日期
                # 使用适配器类以确保兼容性
                try:
                    from quant_trading.data.adapter import DataHandlerAdapter
                    data_handler = DataHandlerAdapter()
                    logger.info("使用DataHandlerAdapter获取数据")
                except ImportError:
                    # 如果适配器不可用，使用标准数据处理器
                    data_handler = DataHandler()
                    logger.info("使用标准DataHandler获取数据")

                latest_date = data_handler.get_latest_trading_date()

                # 计算开始日期（90天前）
                if isinstance(latest_date, str):
                    end_date = datetime.strptime(latest_date, "%Y-%m-%d").date()
                elif isinstance(latest_date, datetime):
                    end_date = latest_date.date()
                elif isinstance(latest_date, pd.Timestamp):
                    end_date = latest_date.date()
                else:
                    end_date = datetime.now().date()

                start_date = end_date - timedelta(days=90)

                # 获取数据
                data = data_handler.get_stock_data(
                    stock_code=default_stock_code,
                    start_date=start_date.strftime("%Y-%m-%d"),
                    end_date=end_date.strftime("%Y-%m-%d"),
                    frequency="日线"
                )

                # 保存到会话状态
                st.session_state.latest_data = data
                st.session_state.latest_data_loaded = True

                logger.info(f"自动获取股票 {default_stock_code} 的最新数据，共 {len(data)} 条记录")

                # 自动进行特征工程
                with st.spinner("正在自动生成特征..."):
                    # 检查是否有缓存的特征配置
                    feature_config = st.session_state.get('feature_config', None)

                    # 生成更详细的缓存键，包含股票代码、日期范围和特征配置的哈希值
                    config_hash = "default"
                    if feature_config:
                        import hashlib
                        import json
                        # 将特征配置转换为JSON字符串，然后计算哈希值
                        try:
                            config_str = json.dumps(feature_config, sort_keys=True)
                            config_hash = hashlib.md5(config_str.encode()).hexdigest()[:8]
                        except:
                            pass

                    cache_key = f"{default_stock_code}_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}_{config_hash}"

                    # 创建全局特征缓存（如果不存在）
                    if 'global_feature_cache' not in st.session_state:
                        st.session_state.global_feature_cache = {}

                    # 检查是否有缓存的处理后数据
                    if cache_key in st.session_state.global_feature_cache:
                        logger.info(f"使用全局缓存的特征数据: {cache_key}")
                        processed_data = st.session_state.global_feature_cache[cache_key]
                        st.success(f"使用缓存的特征数据，形状: {processed_data.shape}")
                    else:
                        # 使用增强特征工程器，但禁用所有计算密集型特征
                        try:
                            from quant_trading.features.enhanced_feature_engineer import EnhancedFeatureEngineer
                            # 创建一个极简特征配置，最大限度减少计算量
                            minimal_config = {
                                'price_features': {'use': True},
                                'technical_indicators': {
                                    'use': True,
                                    'sma': {'use': True, 'periods': [5, 10, 20]},  # 只使用少量SMA
                                    'ema': {'use': True, 'periods': [5, 10]},      # 只使用少量EMA
                                    'rsi': {'use': True, 'periods': [14]},         # 只使用标准RSI
                                    'macd': {'use': False},                        # 禁用MACD
                                    'bbands': {'use': False},                      # 禁用布林带
                                    'stoch': {'use': False},                       # 禁用随机指标
                                    'adx': {'use': False},                         # 禁用ADX
                                    'atr': {'use': False}                          # 禁用ATR
                                },
                                'rolling_stats': {
                                    'use': True,
                                    'windows': [5, 10],                            # 只使用小窗口
                                    'stats': ['mean', 'std']                       # 只计算均值和标准差
                                },
                                'advanced_features': {'use': False},               # 禁用高级特征
                                'market_microstructure': {'use': False},           # 禁用市场微观结构特征
                                'time_features': {'use': False},                   # 禁用时间特征
                                'pattern_features': {'use': False},                # 禁用形态特征
                                'feature_selection': {'use': False},               # 禁用特征选择
                                'transform_features': {'use': False},              # 禁用特征变换
                                'cross_asset': {'use': False},                     # 禁用跨资产特征
                                'check_stationarity': False,                       # 禁用平稳性检查
                                'avoid_lookahead': True                            # 避免前视偏差
                            }

                            # 如果有训练时的特征配置，优先使用它
                            if feature_config:
                                logger.info("使用训练时的特征配置")
                                feature_engineer = EnhancedFeatureEngineer(feature_config)
                            else:
                                logger.info("使用极简特征配置")
                                feature_engineer = EnhancedFeatureEngineer(minimal_config)

                            logger.info("使用增强特征工程器自动生成特征")
                        except ImportError:
                            # 如果增强特征工程器不可用，使用标准特征工程器
                            logger.info("增强特征工程器不可用，使用标准特征工程器")
                            feature_engineer = FeatureEngineer(feature_config)

                        # 生成特征，禁用特征选择以提高性能
                        start_time = time.time()
                        processed_data = feature_engineer.generate_features(data, apply_selection=False)
                        end_time = time.time()
                        logger.info(f"特征生成耗时: {end_time - start_time:.2f} 秒")

                        # 删除NaN值
                        processed_data = processed_data.dropna()

                        # 缓存处理后的数据到全局缓存
                        st.session_state.global_feature_cache[cache_key] = processed_data
                        logger.info(f"特征数据已缓存到全局缓存: {cache_key}")

                        # 限制缓存大小，避免内存占用过大
                        if len(st.session_state.global_feature_cache) > 10:
                            # 删除最早的缓存项
                            oldest_key = list(st.session_state.global_feature_cache.keys())[0]
                            del st.session_state.global_feature_cache[oldest_key]
                            logger.info(f"缓存项过多，删除最早的缓存: {oldest_key}")

                    # 保存到会话状态
                    st.session_state.latest_processed_data = processed_data

                    logger.info(f"自动特征生成完成，处理后数据形状: {processed_data.shape}")
                    st.success(f"已自动获取 {default_stock_code} 的最新数据并生成特征")

                    # 显示数据概览
                    st.subheader("最新数据概览")
                    st.dataframe(data.tail())

                    # 显示处理后的数据概览
                    st.subheader("处理后的数据概览")
                    st.dataframe(processed_data.tail())
        except Exception as e:
            st.error(f"自动获取数据失败: {str(e)}")
            logger.error(f"自动获取数据失败: {str(e)}", exc_info=True)
            st.warning("请手动获取最新数据并生成特征")
            st.stop()

    # 环境参数配置
    st.subheader("环境参数")
    col1, col2 = st.columns(2)

    with col1:
        initial_capital = st.number_input("初始资金", 10000, 10000000, st.session_state.env_config.get('initial_capital', 100000), 10000)
        commission_rate = st.number_input("手续费率", 0.0, 0.01, st.session_state.env_config.get('commission_rate', 0.0003), 0.0001, format="%.5f")

    with col2:
        min_hold_days = st.number_input("最小持仓天数", 1, 10, st.session_state.env_config.get('min_hold_days', 3), 1)
        window_size = st.number_input("观测窗口大小", 5, 50, 20, 1)

    # 生成信号按钮
    if st.button("生成交易信号"):
        try:
            with st.spinner("正在生成交易信号..."):
                # 获取最新数据
                latest_data = st.session_state.latest_processed_data

                # 确保数据足够长
                if len(latest_data) <= window_size:
                    st.error(f"数据长度不足，需要至少 {window_size + 1} 条数据")
                    st.stop()

                # 获取模型的观测空间维度
                if hasattr(st.session_state.model.model, 'observation_space'):
                    model_obs_dim = st.session_state.model.model.observation_space.shape[0]
                    logger.info(f"模型期望的观测空间维度: {model_obs_dim}")
                else:
                    model_obs_dim = 3244  # 如果无法获取，使用错误信息中提到的期望维度
                    logger.warning(f"无法获取模型的观测空间维度，使用默认值: {model_obs_dim}")

                # 计算特征数量
                feature_columns = [col for col in latest_data.columns if col not in ['开盘', '最高', '最低', '收盘', '成交量']]
                n_features = len(feature_columns)

                # 计算需要的特征数量
                # 观测空间维度 = window_size * n_features + 4 (账户状态)
                required_features = (model_obs_dim - 4) // window_size

                logger.info(f"当前特征数量: {n_features}, 需要的特征数量: {required_features}")

                # 创建一个新的数据框，只包含必要的特征
                reduced_data = latest_data[['开盘', '最高', '最低', '收盘', '成交量']].copy()

                # 如果当前特征数量大于需要的特征数量，只保留需要的特征
                if n_features > required_features:
                    logger.info(f"特征数量过多，从 {n_features} 减少到 {required_features}")
                    for i in range(required_features):
                        if i < len(feature_columns):
                            reduced_data[feature_columns[i]] = latest_data[feature_columns[i]]
                        else:
                            # 如果需要更多特征但没有足够的列，添加零列
                            reduced_data[f'feature_{i}'] = 0.0
                # 如果当前特征数量小于需要的特征数量，添加零列
                elif n_features < required_features:
                    logger.info(f"特征数量不足，从 {n_features} 增加到 {required_features}")
                    for i in range(n_features):
                        reduced_data[feature_columns[i]] = latest_data[feature_columns[i]]
                    for i in range(n_features, required_features):
                        reduced_data[f'feature_{i}'] = 0.0
                # 如果特征数量正好，直接使用所有特征
                else:
                    logger.info(f"特征数量正好: {n_features}")
                    for col in feature_columns:
                        reduced_data[col] = latest_data[col]

                # 创建环境
                env_config = {
                    'df_processed_data': reduced_data,
                    'initial_capital': initial_capital,
                    'commission_rate': commission_rate,
                    'min_hold_days': min_hold_days,
                    'window_size': window_size
                }

                # 尝试使用适配器类以确保兼容性
                try:
                    from quant_trading.trading.adapter import TradingEnvironmentAdapter
                    env = TradingEnvironmentAdapter(**env_config)
                    logger.info("使用TradingEnvironmentAdapter生成信号")
                except ImportError:
                    # 如果适配器不可用，使用标准交易环境
                    env = TradingEnvironment(**env_config)
                    logger.info("使用标准TradingEnvironment生成信号")

                # 确保环境的观测空间维度正确
                if env.observation_space.shape[0] != model_obs_dim:
                    logger.warning(f"环境观测空间维度 ({env.observation_space.shape[0]}) 与模型期望维度 ({model_obs_dim}) 不一致，强制更新")
                    # 使用新的方法强制设置观测空间维度
                    env.force_observation_space(model_obs_dim)

                # 重置环境，获取初始观测
                observation, _ = env.reset()

                # 记录观测形状
                logger.info(f"观测形状: {observation.shape}")

                # 确保观测形状与模型期望的一致
                if observation.shape[0] != model_obs_dim:
                    logger.warning(f"观测形状 ({observation.shape[0]}) 与模型期望形状 ({model_obs_dim}) 不一致，强制调整")
                    if observation.shape[0] < model_obs_dim:
                        # 如果观测太小，填充零
                        observation = np.pad(observation, (0, model_obs_dim - observation.shape[0]), 'constant')
                    else:
                        # 如果观测太大，截断
                        observation = observation[:model_obs_dim]
                    logger.info(f"调整后的观测形状: {observation.shape}")

                # 确保observation是numpy数组
                if not isinstance(observation, np.ndarray):
                    observation = np.array(observation, dtype=np.float32)
                    logger.info(f"将observation转换为numpy数组，形状: {observation.shape}")

                # 检查observation的维度是否与模型期望的维度匹配
                if hasattr(st.session_state.model.model, 'observation_space'):
                    model_obs_dim = st.session_state.model.model.observation_space.shape[0]
                    if len(observation) != model_obs_dim:
                        logger.warning(f"观察空间维度不匹配: 当前 {len(observation)}, 期望 {model_obs_dim}")
                        # 调整observation的维度
                        if len(observation) < model_obs_dim:
                            # 如果observation的维度小于模型期望的维度，则填充零
                            padding = np.zeros(model_obs_dim - len(observation), dtype=np.float32)
                            observation = np.concatenate([observation, padding])
                            logger.info(f"观察空间维度从 {len(observation) - len(padding)} 填充到 {model_obs_dim}")
                        else:
                            # 如果observation的维度大于模型期望的维度，则截断
                            observation = observation[:model_obs_dim]
                            logger.info(f"观察空间维度从 {len(observation) + (len(observation) - model_obs_dim)} 截断到 {model_obs_dim}")

                try:
                    # 使用模型预测动作
                    action = st.session_state.model.predict_action(observation, deterministic=True)

                    # 将NumPy数组转换为Python标量
                    if isinstance(action, np.ndarray):
                        action_scalar = action.item()
                        logger.info(f"将NumPy数组动作 {action} 转换为标量 {action_scalar}")
                    else:
                        action_scalar = action
                        logger.info(f"动作已经是标量: {action_scalar}")
                except Exception as e:
                    logger.error(f"预测动作时发生错误: {str(e)}", exc_info=True)
                    st.error(f"预测动作时发生错误: {str(e)}")
                    # 默认动作：保持当前仓位
                    action_scalar = 0
                    logger.warning(f"使用默认动作: {action_scalar} (保持当前仓位)")

                # 解析动作
                action_meaning = {
                    0: "保持当前仓位",
                    1: "买入",
                    2: "卖出"
                }

                # 获取当前价格
                current_price = latest_data['收盘'].iloc[-1]

                # 确保动作值在字典中
                if action_scalar not in action_meaning:
                    logger.warning(f"未知的动作值: {action_scalar}，默认为'保持当前仓位'")
                    action_scalar = 0  # 默认为保持当前仓位

                # 保存信号到会话状态
                st.session_state.latest_signal = {
                    'action': action_scalar,
                    'action_meaning': action_meaning[action_scalar],
                    'price': current_price,
                    'date': latest_data.index[-1],
                    'stock_code': stock_code
                }

                # 记录日志
                logger.info(f"生成交易信号: {stock_code}, 动作: {action_meaning[action_scalar]}, 价格: {current_price}, 日期: {latest_data.index[-1]}")

                # 显示信号结果
                st.success("交易信号生成成功！")
        except Exception as e:
            st.error(f"生成交易信号失败: {str(e)}")
            logger.error(f"生成交易信号失败: {str(e)}", exc_info=True)

    # 显示信号结果
    if 'latest_signal' in st.session_state:
        signal = st.session_state.latest_signal

        st.header("交易信号结果")

        # 使用大号字体和颜色显示信号
        action_color = {
            "买入": "green",
            "卖出": "red",
            "保持当前仓位": "blue"
        }

        col1, col2 = st.columns(2)

        with col1:
            st.markdown(f"""
            <div style='background-color: #f0f2f6; padding: 20px; border-radius: 10px;'>
                <h3>品种: {signal['stock_code']}</h3>
                <h2 style='color: {action_color[signal['action_meaning']]}'>建议操作: {signal['action_meaning']}</h2>
                <p>当前价格: {signal['price']:.2f}</p>
                <p>信号日期: {signal['date'].strftime('%Y-%m-%d')}</p>
            </div>
            """, unsafe_allow_html=True)

        with col2:
            # 绘制最近价格走势和信号点
            if 'latest_data' in st.session_state:
                data = st.session_state.latest_data

                fig, ax = plt.subplots(figsize=(10, 6))
                ax.plot(data.index[-30:], data['收盘'].iloc[-30:])

                # 标记信号点
                ax.scatter([signal['date']], [signal['price']],
                          color=action_color[signal['action_meaning']],
                          marker='o' if signal['action_meaning'] == "保持当前仓位" else
                                 ('^' if signal['action_meaning'] == "买入" else 'v'),
                          s=150)

                ax.set_title(f"{signal['stock_code']} 最近价格走势与信号")
                ax.set_xlabel("日期")
                ax.set_ylabel("价格")
                st.pyplot(fig)

        # 信号解释（如果模型支持）
        st.subheader("信号解释")
        st.info("基于当前市场状态和历史数据模式，DRL智能体生成了上述交易信号。")
        st.info("注意：此信号仅供参考，实际交易决策应结合更多因素考虑。")

        # 记录信号历史
        if 'signal_history' not in st.session_state:
            st.session_state.signal_history = []

        # 检查是否已经有相同日期的信号
        existing_dates = [s['date'] for s in st.session_state.signal_history]
        if signal['date'] not in existing_dates:
            st.session_state.signal_history.append(signal)

        # 显示信号历史
        if st.session_state.signal_history:
            st.subheader("信号历史")
            history_df = pd.DataFrame(st.session_state.signal_history)
            st.dataframe(history_df)

elif page == "日志控制台":
    # 使用自定义样式的标题
    st.markdown('<div class="main-header">日志控制台</div>', unsafe_allow_html=True)

    # 添加用户指南
    with st.expander("📚 用户指南", expanded=True):
        st.markdown("""
        ## 日志控制台指南

        本页面用于查看和管理系统日志，帮助您监控系统运行状态和排查问题。

        ### 使用步骤：

        1. **查看日志内容**
           - 日志内容区域显示了系统的运行日志
           - 日志按时间顺序排列，最新的日志在底部
           - 日志包含不同级别的信息：
             - **INFO**（普通信息）：记录系统正常操作的信息
             - **WARNING**（警告）：记录可能的问题但不影响系统运行
             - **ERROR**（错误）：记录导致功能失败的严重问题
             - **DEBUG**（调试）：记录详细的技术信息，用于开发调试

        2. **日志管理**
           - 点击"清空日志"按钮可以清空当前日志文件
           - 清空前系统会自动备份当前日志，备份文件保存在logs/backup目录下
           - 点击"打开日志目录"按钮可以在文件资源管理器中查看所有日志文件
           - 定期清理日志可以提高系统性能和减少存储空间占用

        3. **日志分析**
           - 通过日志内容可以追踪系统的运行流程和状态变化
           - 日志时间戳帮助您了解事件发生的确切时间和顺序
           - 通过日志可以识别潜在的性能瓶颈和错误模式

        ### 重要提示：
        - 如果系统出现异常行为，请首先查看日志中的ERROR和WARNING信息
        - 日志中记录了数据获取、模型训练、交易信号生成等关键操作的详细信息
        - 在向开发者报告问题时，提供相关日志内容可以帮助快速定位和解决问题
        - 日志文件会随着系统使用逐渐增大，建议定期清理
        - 系统会自动记录所有关键操作，包括模型训练、数据获取和交易信号生成
        """)

        # 添加分隔线
        st.markdown("---")

    # 添加日志管理功能
    st.header("日志管理")
    col1, col2, col3 = st.columns([1, 1, 2])

    with col1:
        if st.button("清空日志", help="清空日志会将当前所有日志文件备份后清空，以减小日志文件大小"):
            try:
                # 获取所有日志文件
                log_files = [f for f in os.listdir("logs") if f.endswith(".log")]

                if log_files:
                    # 创建备份目录
                    backup_dir = os.path.join("logs", "backup")
                    os.makedirs(backup_dir, exist_ok=True)

                    # 备份并清空日志文件
                    backup_time = datetime.now().strftime("%Y%m%d_%H%M%S")
                    for log_file in log_files:
                        log_path = os.path.join("logs", log_file)
                        # 创建备份
                        backup_path = os.path.join(backup_dir, f"{log_file}.{backup_time}")
                        shutil.copy2(log_path, backup_path)

                        # 清空日志文件
                        with open(log_path, 'w') as f:
                            f.write(f"# 日志已清空 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

                    # 记录清空操作
                    logger.info("日志文件已清空，旧日志已备份")
                    st.success(f"日志已清空，旧日志已备份到 logs/backup 目录")
                    st.info(f"备份文件名格式: 原文件名.{backup_time}")
                else:
                    st.info("没有找到日志文件")
            except Exception as e:
                st.error(f"清空日志失败: {str(e)}")
                logger.error(f"清空日志失败: {str(e)}")

    with col2:
        if st.button("打开日志目录", help="在文件资源管理器中打开日志目录"):
            try:
                log_dir = os.path.abspath("logs")
                if os.path.exists(log_dir):
                    # 使用系统默认的文件管理器打开日志目录
                    if os.name == 'nt':  # Windows
                        os.startfile(log_dir)
                    elif os.name == 'posix':  # macOS 或 Linux
                        if sys.platform == 'darwin':  # macOS
                            subprocess.call(['open', log_dir])
                        else:  # Linux
                            subprocess.call(['xdg-open', log_dir])
                    st.success(f"已打开日志目录: {log_dir}")
                else:
                    st.warning(f"日志目录不存在: {log_dir}")
            except Exception as e:
                st.error(f"打开日志目录失败: {str(e)}")
                logger.error(f"打开日志目录失败: {str(e)}")

    with col3:
        st.info("日志文件会随着系统使用逐渐增大，建议定期清理。所有日志备份将保存在logs/backup目录下，以便需要时查看历史记录。")

    # 显示日志内容
    st.header("日志内容")
    if os.path.exists("logs/app.log"):
        with open("logs/app.log", "r") as f:
            log_content = f.read()
        st.text_area("应用日志", log_content, height=600)
    else:
        st.warning("日志文件不存在")

elif page == "策略总结与项目报告":
    # 使用自定义样式的标题
    st.markdown('<div class="main-header">策略总结与项目报告</div>', unsafe_allow_html=True)

    # 添加用户指南
    with st.expander("📚 用户指南", expanded=True):
        st.markdown("""
        ## 策略总结与项目报告指南

        本页面提供项目的整体概览和策略总结，帮助您了解系统的核心功能和性能表现。

        ### 页面内容：

        1. **项目概述与目标回顾**
           - 展示项目的初始目标和核心功能实现情况
           - 列出系统执行的交易约束和规则

        2. **最终DRL策略配置详情**
           - 显示当前加载的模型信息
           - 展示环境配置和特征工程配置

        3. **最终策略性能报告**
           - 汇总展示策略的关键性能指标
           - 对比策略与基准的表现差异
           - 评估策略是否达到预设的性能目标

        4. **交易行为分析**
           - 分析策略的交易频率和持仓时间
           - 展示交易胜率和盈亏比等统计数据

        ### 使用建议：
        - 此页面适合在完成模型训练和回测后查看
        - 通过此页面可以全面评估策略的优势和不足
        - 可以将此页面作为策略报告导出或分享
        - 根据报告结果决定是否需要调整策略参数或特征工程
        """)

        # 添加分隔线
        st.markdown("---")

    # 项目概述与目标回顾
    st.header("项目概述与目标回顾")

    st.markdown("""
    ### 项目初始目标

    本项目旨在开发一个基于深度强化学习（DRL）的量化交易系统，包含用户界面，用于策略监控、数据管理、风险评估和辅助交易决策。

    ### 核心功能实现情况

    | 功能模块 | 完成状态 | 说明 |
    | --- | --- | --- |
    | 数据中心与环境配置 | ✅ 已完成 | 支持从AkShare获取数据，配置交易环境参数 |
    | 特征工程 | ✅ 已完成 | 支持多种技术指标，用户可自定义配置 |
    | DRL交易环境 | ✅ 已完成 | 符合Gymnasium API，实现了严格交易约束 |
    | DRL智能体训练 | ✅ 已完成 | 支持多种算法，可配置超参数，实时监控训练进度 |
    | 策略性能评估 | ✅ 已完成 | 全面的性能指标计算和可视化 |
    | 实况信号决策 | ✅ 已完成 | 基于最新市场数据生成交易信号 |
    | 风险管理 | ✅ 已完成 | 在奖励函数和交易约束中体现 |

    ### 交易约束执行情况

    系统严格执行了以下交易约束：

    - ✅ 所有买卖操作均在当日收盘价执行
    - ✅ 任何已建立头寸，在平仓前必须至少持有3个交易日
    - ✅ 严禁使用任何形式的杠杆
    """)

    # 最终DRL策略配置详情
    st.header("最终DRL策略配置详情")

    # 检查是否有模型和回测结果
    has_model = 'model_loaded' in st.session_state and st.session_state.model_loaded
    has_backtest = 'backtest_results' in st.session_state

    if has_model:
        # 显示模型信息
        st.subheader("当前加载的模型")
        st.info(f"模型路径: {st.session_state.model_path}")

        # 显示环境配置
        st.subheader("环境配置")
        st.json(st.session_state.env_config)

        # 显示特征配置
        if 'feature_config' in st.session_state:
            st.subheader("特征工程配置")
            st.json(st.session_state.feature_config)
    else:
        st.warning("未加载模型。请在'DRL智能体训练'页面训练模型或在'策略性能评估'页面加载已有模型。")

    # 最终策略性能报告
    st.header("最终策略性能报告")

    if has_backtest:
        metrics = st.session_state.backtest_results['metrics']

        # 创建性能指标表格
        col1, col2, col3 = st.columns(3)

        with col1:
            st.subheader("收益指标")
            st.metric("总收益率", f"{metrics['total_return']:.2%}")
            st.metric("年化收益率", f"{metrics['annualized_return']:.2%}")
            if 'benchmark_total_return' in metrics:
                st.metric("基准总收益率", f"{metrics['benchmark_total_return']:.2%}")
                st.metric("超额收益", f"{metrics['total_return'] - metrics['benchmark_total_return']:.2%}")

        with col2:
            st.subheader("风险指标")
            st.metric("最大回撤", f"{metrics['max_drawdown']:.2%}")
            st.metric("最大回撤持续天数", f"{metrics['max_drawdown_duration']}")
            if 'benchmark_max_drawdown' in metrics:
                st.metric("基准最大回撤", f"{metrics['benchmark_max_drawdown']:.2%}")

        with col3:
            st.subheader("风险调整收益")
            st.metric("夏普比率", f"{metrics['sharpe_ratio']:.2f}")
            st.metric("索提诺比率", f"{metrics['sortino_ratio']:.2f}")
            st.metric("卡玛比率", f"{metrics['calmar_ratio']:.2f}")
            if 'information_ratio' in metrics:
                st.metric("信息比率", f"{metrics['information_ratio']:.2f}")

        # 性能目标达成情况
        st.subheader("性能目标达成情况")

        # 定义目标
        target_monthly_return = 0.05  # 5%
        target_max_drawdown = -0.03  # -3%
        target_sharpe = 1.5

        # 计算月度指标
        monthly_return = metrics.get('avg_monthly_return', 0)

        # 创建目标对比表格
        target_data = {
            "指标": ["月平均收益率", "最大回撤", "夏普比率"],
            "目标值": [f"{target_monthly_return:.2%}", f"{target_max_drawdown:.2%}", f"{target_sharpe:.2f}"],
            "实际值": [f"{monthly_return:.2%}", f"{metrics['max_drawdown']:.2%}", f"{metrics['sharpe_ratio']:.2f}"],
            "达成情况": [
                "✅ 达标" if monthly_return >= target_monthly_return else "❌ 未达标",
                "✅ 达标" if metrics['max_drawdown'] >= target_max_drawdown else "❌ 未达标",
                "✅ 达标" if metrics['sharpe_ratio'] >= target_sharpe else "❌ 未达标"
            ]
        }

        target_df = pd.DataFrame(target_data)
        st.table(target_df)

        # 净值曲线图
        st.subheader("净值曲线")
        portfolio_values = st.session_state.backtest_results['portfolio_values']
        benchmark_values = st.session_state.backtest_results['benchmark_values']

        fig = plt.figure(figsize=(12, 6))
        plt.plot(portfolio_values.index, portfolio_values, label="策略")
        if benchmark_values is not None:
            plt.plot(benchmark_values.index, benchmark_values, label="基准", alpha=0.7)
        plt.title("净值曲线")
        plt.xlabel("日期")
        plt.ylabel("净值")
        plt.legend()
        plt.grid(True)
        st.pyplot(fig)
    else:
        st.warning("未找到回测结果。请在'策略性能评估'页面执行回测。")

    # DRL方法在本项目中的应用分析
    st.header("DRL方法在本项目中的应用分析")

    st.markdown("""
    ### DRL方法的优势与潜力

    1. **自适应性强**：DRL智能体能够通过与市场环境的交互不断学习和适应市场变化，无需人为设定固定的交易规则。

    2. **端到端学习**：从原始市场数据直接学习到交易决策，减少了人为特征工程的偏见。

    3. **优化长期收益**：通过折扣因子和精心设计的奖励函数，DRL能够优化长期收益而非短期利润。

    4. **风险管理整合**：可以将风险控制直接整合到奖励函数中，引导智能体学习风险调整后的最优策略。

    ### 面临的挑战

    1. **奖励函数设计敏感性**：奖励函数的设计对DRL性能影响巨大，需要精心调整各组成部分的权重。

    2. **训练稳定性**：DRL训练过程容易不稳定，有时会出现性能突然下降的情况。

    3. **样本效率低**：DRL通常需要大量的交互数据才能学习有效策略，金融市场的历史数据有限。

    4. **过拟合风险**：DRL模型容易对训练数据过拟合，在新市场环境中表现不佳。

    5. **可解释性差**：DRL决策过程是黑盒，难以解释具体的交易逻辑。

    ### 应对措施

    1. **多组分奖励函数**：设计包含多个组成部分的奖励函数，平衡收益、风险和交易成本。

    2. **严格的交易约束**：实施最小持仓期等约束，减少过度交易和市场冲击。

    3. **特征工程优化**：提供丰富的技术指标作为状态空间的输入，帮助智能体更好地理解市场状态。

    4. **训练-测试分离**：严格分离训练和测试数据，确保模型泛化能力。
    """)

    # 交易约束影响分析
    st.header("交易约束影响分析")

    st.markdown("""
    ### 收盘价交易的影响

    **优势**：
    - 简化了模型，避免了日内价格波动的噪声
    - 减少了对市场冲击的担忧
    - 符合大多数机构投资者的实际操作

    **限制**：
    - 无法捕捉日内交易机会
    - 可能错过最优价格点位

    ### 最小持仓3天的影响

    **优势**：
    - 有效减少了过度交易
    - 降低了交易成本
    - 迫使模型学习中期趋势而非短期噪声
    - 符合价值投资理念

    **限制**：
    - 无法快速止损
    - 错过短期反转机会

    ### 无杠杆的影响

    **优势**：
    - 大幅降低了风险
    - 避免了爆仓可能性
    - 使模型更专注于方向判断而非仓位管理

    **限制**：
    - 收益潜力受限
    - 无法利用做空机制捕捉下跌行情
    """)

    # 软件工程实践总结
    st.header("软件工程实践总结")

    st.markdown("""
    ### 模块化设计

    项目采用了高度模块化的设计，核心逻辑与UI完全分离：

    - **core_logic/**: 包含所有核心算法和业务逻辑
    - **main_app.py**: 仅负责UI展示和用户交互
    - **configs/**: 集中管理配置参数

    这种设计使得各模块可以独立开发、测试和维护，大大提高了代码质量和可维护性。

    ### 测试策略

    项目实施了多层次的测试策略：

    - **单元测试**：针对关键算法和函数
    - **集成测试**：验证模块间的交互
    - **环境检查**：使用 `gymnasium.utils.env_checker.check_env()` 确保交易环境符合标准
    - **回测验证**：通过历史数据回测验证策略性能

    ### 文档完善

    - 所有类和关键方法都有详细的文档字符串
    - README.md 提供了完整的项目概述和使用指南
    - UI中提供了丰富的提示信息和帮助文本

    ### 错误处理

    - 实现了全面的异常捕获和处理机制
    - 详细的日志记录，便于调试和问题追踪
    - UI友好的错误提示，提高用户体验
    """)

    # 未来优化与扩展方向建议
    st.header("未来优化与扩展方向建议")

    st.markdown("""
    ### 1. 高级奖励函数设计

    - 引入基于投资组合理论的奖励组件
    - 加入对宏观经济指标的考量
    - 实现动态调整的奖励权重，适应不同市场环境

    ### 2. 多智能体系统

    - 开发专门针对不同市场状态（牛市、熊市、震荡市）的智能体
    - 实现智能体集成决策机制，提高整体稳定性
    - 探索智能体间的协作学习

    ### 3. 在线学习与适应

    - 实现增量学习机制，使模型能够从新数据中持续学习
    - 开发市场状态检测器，自动调整策略参数
    - 实现模型性能监控和自动重训练机制

    ### 4. 多数据源集成

    - 整合基本面数据（财报、估值指标等）
    - 引入情绪数据（新闻情绪、社交媒体分析）
    - 加入宏观经济指标

    ### 5. 模型可解释性增强

    - 实现基于注意力机制的可视化，展示模型决策依据
    - 开发决策树近似，提取DRL策略的关键规则
    - 设计反事实分析工具，解释"如果...会怎样"的问题
    """)

    # 项目总结
    st.header("项目总结")

    st.markdown("""
    本项目成功实现了一个基于深度强化学习的量化交易系统，包含完整的数据处理、特征工程、模型训练、策略评估和实时信号生成功能。系统严格遵循了预设的交易约束，并提供了用户友好的图形界面。

    虽然DRL在量化交易中面临诸多挑战，但通过精心设计的奖励函数、严格的交易约束和丰富的特征工程，系统展现了良好的性能潜力。未来可以通过多智能体系统、在线学习、多数据源集成等方向进一步提升系统性能。

    项目采用了模块化设计、全面的测试策略和详尽的文档，确保了代码质量和可维护性。这为未来的扩展和优化奠定了坚实基础。
    """)

    # 页脚
    st.markdown("---")
    st.info("DRL量化交易系统 © 2023 | 版本: 1.0.0")

# GPU状态与诊断页面
elif page == "GPU状态与诊断":
    st.title("GPU状态与诊断")

    # 刷新GPU信息
    if st.button("刷新GPU信息"):
        st.session_state.gpu_info = get_gpu_info()
        st.session_state.gpu_setup_result = setup_gpu_environment()
        st.experimental_rerun()

    # 显示GPU状态
    st.header("GPU状态")

    col1, col2 = st.columns(2)

    with col1:
        if gpu_info['available']:
            st.success(f"✅ GPU可用")
            st.metric("检测到GPU数量", gpu_info['count'])
            st.info(f"深度学习框架: {gpu_info['framework']}")

            # 显示配置结果
            st.subheader("GPU配置状态")
            if gpu_setup_result['success']:
                st.success(gpu_setup_result['message'])
            else:
                st.warning(gpu_setup_result['message'])
        else:
            st.error("❌ GPU不可用")
            st.warning("系统将使用CPU进行所有计算，这可能会显著降低训练速度。")

    with col2:
        # 显示GPU诊断按钮
        st.subheader("GPU问题诊断")
        col_diag1, col_diag2 = st.columns(2)

        with col_diag1:
            if st.button("运行诊断"):
                with st.spinner("正在诊断GPU问题..."):
                    diagnosis = diagnose_gpu_issues()
                    if diagnosis['issues_detected']:
                        st.error("检测到以下问题:")
                        for problem in diagnosis['problems']:
                            st.write(f"- {problem}")

                        st.info("建议解决方案:")
                        for suggestion in diagnosis['suggestions']:
                            st.write(f"- {suggestion}")
                    else:
                        if gpu_info['available']:
                            st.success("未检测到GPU问题，您的GPU环境配置正常。")
                        else:
                            st.info("未检测到明显的GPU问题，但GPU仍不可用。请检查您的硬件是否支持GPU加速。")

        with col_diag2:
            # 添加自动安装GPU支持的按钮
            if st.button("自动安装GPU支持"):
                with st.spinner("正在准备安装GPU支持..."):
                    # 检查安装脚本是否存在
                    import os
                    script_path = os.path.join("install_gpu_support.py")

                    if os.path.exists(script_path):
                        st.info("正在运行GPU支持安装脚本，请在弹出的终端窗口中查看进度...")
                        try:
                            # 运行安装脚本
                            import subprocess
                            process = subprocess.Popen([sys.executable, script_path],
                                                      stdout=subprocess.PIPE,
                                                      stderr=subprocess.PIPE,
                                                      text=True)

                            # 显示安装进度
                            st.info("安装过程可能需要几分钟时间，请耐心等待...")
                            st.warning("安装完成后，请重启应用程序以应用更改")
                        except Exception as e:
                            st.error(f"运行安装脚本时出错: {str(e)}")
                    else:
                        st.error(f"找不到安装脚本: {script_path}")
                        st.info("请确保安装脚本位于正确的位置")

    # 显示详细的GPU信息
    if gpu_info['available']:
        st.header("GPU详细信息")

        for i, device in enumerate(gpu_info['devices']):
            with st.expander(f"GPU {i+1}: {device}", expanded=True):
                st.write(f"**设备名称**: {device}")

                # 显示显存信息
                if i < len(gpu_info['memory']):
                    memory = gpu_info['memory'][i]

                    # 创建显存使用情况图表
                    if isinstance(memory.get('total'), (int, float)) and isinstance(memory.get('free'), (int, float)):
                        total = memory['total']
                        free = memory['free']
                        used = total - free

                        # 显示显存使用情况
                        st.subheader("显存使用情况")
                        cols = st.columns(3)
                        cols[0].metric("总显存", f"{total:.2f} GB")
                        cols[1].metric("已使用", f"{used:.2f} GB")
                        cols[2].metric("可用", f"{free:.2f} GB")

                        # 显示显存使用率进度条
                        usage_percent = (used / total) * 100 if total > 0 else 0
                        st.progress(usage_percent / 100)
                        st.write(f"显存使用率: {usage_percent:.1f}%")
                    else:
                        st.write("无法获取详细的显存信息")

    # GPU环境配置
    st.header("GPU环境配置")

    # 添加自动配置选项
    st.subheader("虚拟环境GPU配置")

    col_env1, col_env2 = st.columns(2)

    with col_env1:
        # 检查PyTorch GPU支持
        try:
            import torch
            pytorch_installed = True
            pytorch_version = torch.__version__
            pytorch_gpu = torch.cuda.is_available()

            if pytorch_gpu:
                st.success(f"✅ PyTorch {pytorch_version} 已支持GPU")
                st.write(f"CUDA版本: {torch.version.cuda}")
                st.write(f"GPU设备: {torch.cuda.get_device_name(0)}")
            else:
                st.warning(f"⚠️ PyTorch {pytorch_version} 已安装但不支持GPU")
        except ImportError:
            pytorch_installed = False
            st.error("❌ PyTorch未安装")

    with col_env2:
        # 检查TensorFlow GPU支持
        try:
            import tensorflow as tf
            tf_installed = True
            tf_version = tf.__version__
            tf_gpus = tf.config.list_physical_devices('GPU')

            if len(tf_gpus) > 0:
                st.success(f"✅ TensorFlow {tf_version} 已支持GPU")
                st.write(f"检测到 {len(tf_gpus)} 个GPU设备")
            else:
                st.warning(f"⚠️ TensorFlow {tf_version} 已安装但不支持GPU")
        except ImportError:
            tf_installed = False
            st.error("❌ TensorFlow未安装")

    # 添加自动安装选项
    st.subheader("自动安装GPU支持")

    col_install1, col_install2 = st.columns(2)

    with col_install1:
        if st.button("安装PyTorch GPU版本"):
            with st.spinner("正在安装PyTorch GPU版本..."):
                try:
                    import subprocess
                    import sys

                    # 获取安装命令
                    install_cmd = "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118"

                    # 执行安装
                    st.info(f"正在执行: {install_cmd}")
                    process = subprocess.Popen(
                        [sys.executable, "-m"] + install_cmd.split()[1:],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True
                    )

                    # 显示安装进度
                    st.info("安装过程可能需要几分钟时间，请耐心等待...")
                    st.warning("安装完成后，请重启应用程序以应用更改")
                except Exception as e:
                    st.error(f"安装PyTorch GPU版本时出错: {str(e)}")

    with col_install2:
        if st.button("安装TensorFlow GPU版本"):
            with st.spinner("正在安装TensorFlow GPU版本..."):
                try:
                    import subprocess
                    import sys

                    # 执行安装
                    install_cmd = "pip install tensorflow"
                    st.info(f"正在执行: {install_cmd}")
                    process = subprocess.Popen(
                        [sys.executable, "-m", "pip", "install", "tensorflow"],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True
                    )

                    # 显示安装进度
                    st.info("安装过程可能需要几分钟时间，请耐心等待...")
                    st.warning("安装完成后，请重启应用程序以应用更改")
                except Exception as e:
                    st.error(f"安装TensorFlow GPU版本时出错: {str(e)}")

    # 显示完整安装脚本选项
    if st.button("运行完整GPU支持安装脚本"):
        with st.spinner("正在准备安装GPU支持..."):
            # 检查安装脚本是否存在
            import os
            script_path = os.path.join("install_gpu_support.py")

            if os.path.exists(script_path):
                st.info("正在运行GPU支持安装脚本...")
                try:
                    # 运行安装脚本
                    import subprocess
                    process = subprocess.Popen(
                        [sys.executable, script_path],
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True
                    )

                    # 显示安装进度
                    st.info("安装过程可能需要几分钟时间，请耐心等待...")
                    st.warning("安装完成后，请重启应用程序以应用更改")
                except Exception as e:
                    st.error(f"运行安装脚本时出错: {str(e)}")
            else:
                st.error(f"找不到安装脚本: {script_path}")
                st.info("请确保安装脚本位于正确的位置")

    # GPU优化指南
    st.header("GPU优化指南")

    with st.expander("如何优化GPU性能", expanded=False):
        st.markdown("""
        ### 提高GPU训练性能的建议

        1. **使用混合精度训练**
           - 混合精度训练使用FP16（半精度浮点数）进行部分计算，可以显著提高训练速度
           - 在训练页面的GPU选项中启用"使用混合精度训练"

        2. **批量大小优化**
           - 增加批量大小通常可以提高GPU利用率
           - 尝试使用较大的批量大小，但注意不要超出GPU显存限制

        3. **内存管理**
           - 启用"内存增长"选项可以避免一次性分配所有GPU显存
           - 在每次训练前清理GPU缓存

        4. **模型复杂度**
           - 适当调整网络结构的复杂度，平衡性能和训练速度
           - 对于较小的数据集，避免使用过于复杂的网络结构

        5. **数据预处理**
           - 将数据预处理放在CPU上进行，保留GPU资源用于模型训练
           - 使用数据预取和缓存机制减少数据加载延迟
        """)

    with st.expander("常见GPU问题解决方案", expanded=False):
        st.markdown("""
        ### 常见GPU问题及解决方案

        1. **CUDA版本不兼容**
           - 问题: PyTorch或TensorFlow报告CUDA版本不兼容
           - 解决方案: 安装与CUDA版本匹配的深度学习框架版本
           ```
           # 例如，安装与CUDA 11.3兼容的PyTorch
           pip install torch==1.10.0+cu113 -f https://download.pytorch.org/whl/cu113/torch_stable.html
           ```

        2. **GPU显存不足**
           - 问题: 训练时出现"CUDA out of memory"错误
           - 解决方案:
             - 减小批量大小
             - 使用混合精度训练
             - 简化模型结构
             - 清理GPU缓存: `torch.cuda.empty_cache()`

        3. **GPU未被检测到**
           - 问题: 系统无法检测到GPU
           - 解决方案:
             - 确保已安装最新的NVIDIA驱动
             - 检查CUDA和cuDNN安装是否正确
             - 在NVIDIA控制面板中确认应用程序使用GPU

        4. **训练速度慢**
           - 问题: 尽管使用GPU，训练速度仍然很慢
           - 解决方案:
             - 检查GPU利用率，确保模型确实在使用GPU
             - 优化数据加载管道
             - 增加批量大小
             - 使用更高效的算法或模型结构
        """)

# 页面底部信息
st.sidebar.markdown("---")
st.sidebar.info("DRL量化交易系统 © 2023")
