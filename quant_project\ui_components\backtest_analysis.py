"""
回测分析页面组件
实现回测分析和结果可视化功能
"""

import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import logging
import os
from datetime import datetime, timedelta

logger = logging.getLogger('drl_trading')

def render_backtest_analysis():
    """渲染回测分析页面"""
    st.markdown("## 📈 回测分析")
    
    # 初始化会话状态
    if 'backtest_results' not in st.session_state:
        st.session_state.backtest_results = None
    
    # 使用选项卡组织不同功能
    tabs = st.tabs(["回测配置", "回测结果", "绩效报告"])
    
    with tabs[0]:
        st.markdown("### 回测配置")
        
        # 检查是否有数据
        if 'data' not in st.session_state or st.session_state.data is None:
            st.warning("请先在数据中心加载数据")
            st.button("跳转到数据中心", on_click=lambda: setattr(st.session_state, 'current_page', "数据中心"))
            return
        
        # 检查是否有模型或策略
        if 'model' not in st.session_state or st.session_state.model is None:
            st.warning("没有可用的模型，请先训练或加载模型")
            has_model = False
        else:
            has_model = True
            st.success(f"已加载模型: {st.session_state.model['name']}")
        
        # 回测表单
        with st.form("backtest_form"):
            st.markdown("#### 基本配置")
            
            col1, col2 = st.columns(2)
            
            with col1:
                initial_capital = st.number_input("初始资金", min_value=10000, max_value=10000000, value=100000, step=10000,
                                                help="回测的初始资金")
                
                commission_rate = st.number_input("交易佣金率", min_value=0.0, max_value=0.01, value=0.0003, format="%.5f",
                                               help="交易佣金率，如0.0003表示万分之三")
            
            with col2:
                slippage = st.number_input("滑点率", min_value=0.0, max_value=0.01, value=0.0001, format="%.5f",
                                        help="滑点率，表示成交价格与下单价格的偏差比例")
                
                min_order_size = st.number_input("最小订单规模", min_value=0, max_value=10000, value=100, step=100,
                                              help="最小订单规模，小于此规模的订单不会执行")
            
            st.markdown("#### 回测数据")
            
            if 'data' in st.session_state and st.session_state.data is not None:
                # 显示数据信息
                data = st.session_state.data
                st.info(f"当前数据: {len(data)} 条记录，时间范围: {data.index[0].strftime('%Y-%m-%d')} 至 {data.index[-1].strftime('%Y-%m-%d')}")
                
                # 设置回测时间范围
                col1, col2 = st.columns(2)
                
                with col1:
                    start_date = st.date_input("开始日期", 
                                             value=data.index[0].date(),
                                             min_value=data.index[0].date(),
                                             max_value=data.index[-1].date())
                
                with col2:
                    end_date = st.date_input("结束日期", 
                                           value=data.index[-1].date(),
                                           min_value=data.index[0].date(),
                                           max_value=data.index[-1].date())
                
                # 设置基准
                benchmark_types = ["不使用基准", "同期上证指数", "同期深证成指", "同期沪深300"]
                benchmark_type = st.selectbox("基准标的", benchmark_types, index=0)
            
            # 提交按钮
            submitted = st.form_submit_button("开始回测", use_container_width=True, disabled=not has_model)
            
            if submitted and has_model:
                with st.spinner("正在执行回测..."):
                    try:
                        # 获取回测区间数据
                        mask = (data.index.date >= start_date) & (data.index.date <= end_date)
                        backtest_data = data.loc[mask]
                        
                        if len(backtest_data) == 0:
                            st.error("所选日期范围内没有数据")
                            return
                        
                        # 导入回测模块
                        try:
                            from core_logic.backtest import BacktestEngine
                            backtest_engine = BacktestEngine(
                                initial_capital=initial_capital,
                                commission_rate=commission_rate,
                                slippage=slippage,
                                min_order_size=min_order_size
                            )
                        except ImportError:
                            try:
                                from core_logic.backtest.backtest_engine import BacktestEngine
                                backtest_engine = BacktestEngine(
                                    initial_capital=initial_capital,
                                    commission_rate=commission_rate,
                                    slippage=slippage,
                                    min_order_size=min_order_size
                                )
                            except ImportError:
                                raise ImportError("无法导入回测引擎模块")
                        
                        # 实际回测逻辑这里应该使用加载的模型
                        # 在本示例中，我们使用一个简单的模拟策略
                        
                        # 模拟策略类
                        class MockStrategy:
                            def __init__(self, model_info):
                                self.model_info = model_info
                            
                            def predict_action(self, state):
                                # 简单策略：价格高于5日均线时买入，低于时卖出
                                date = state['date']
                                current_price = state['data']['收盘']
                                
                                # 计算5日均线
                                if '收盘_5日均线' in backtest_data.columns:
                                    ma5 = backtest_data.loc[date, '收盘_5日均线']
                                else:
                                    # 如果没有预先计算的均线，临时计算
                                    idx = backtest_data.index.get_loc(date)
                                    if idx >= 4:
                                        prices = backtest_data['收盘'].iloc[idx-4:idx+1]
                                        ma5 = prices.mean()
                                    else:
                                        ma5 = current_price
                                
                                # 生成交易信号
                                if current_price > ma5:
                                    return 0.8  # 买入信号，80%仓位
                                else:
                                    return 0.0  # 卖出信号，0%仓位
                        
                        # 创建策略实例
                        strategy = MockStrategy(st.session_state.model)
                        
                        # 设置基准代码
                        benchmark_symbol = None
                        if benchmark_type == "同期上证指数":
                            benchmark_symbol = "sh000001"
                        elif benchmark_type == "同期深证成指":
                            benchmark_symbol = "sz399001"
                        elif benchmark_type == "同期沪深300":
                            benchmark_symbol = "sh000300"
                        
                        # 执行回测
                        result = backtest_engine.run_backtest(
                            data=backtest_data,
                            strategy=strategy,
                            benchmark_symbol=benchmark_symbol
                        )
                        
                        # 保存结果
                        st.session_state.backtest_results = result
                        
                        # 计算性能指标
                        try:
                            from core_logic.backtest import PerformanceMetrics
                            metrics_calculator = PerformanceMetrics()
                        except ImportError:
                            try:
                                from core_logic.backtest.performance_metrics import PerformanceMetrics
                                metrics_calculator = PerformanceMetrics()
                            except ImportError:
                                raise ImportError("无法导入性能指标模块")
                        
                        metrics = metrics_calculator.calculate_metrics(
                            portfolio_values=result["portfolio_values"],
                            trades=result["trades"],
                            benchmark_values=result["benchmark_values"]
                        )
                        
                        # 保存指标
                        st.session_state.backtest_metrics = metrics
                        
                        # 显示成功消息
                        st.success("回测完成！")
                        
                        # 切换到结果标签页
                        st.experimental_rerun()
                        
                    except Exception as e:
                        st.error(f"回测过程中出错: {str(e)}")
                        logger.error(f"回测过程中出错: {str(e)}")
    
    with tabs[1]:
        st.markdown("### 回测结果")
        
        # 检查是否有回测结果
        if 'backtest_results' not in st.session_state or st.session_state.backtest_results is None:
            st.info("尚未执行回测，请先在回测配置标签页设置参数并执行回测")
            return
        
        # 显示回测结果
        results = st.session_state.backtest_results
        
        # 基本回测信息
        st.markdown("#### 基本信息")
        
        # 显示关键指标
        metrics = st.session_state.backtest_metrics if 'backtest_metrics' in st.session_state else {}
        
        # 创建指标卡片
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            total_return = metrics.get('total_return', 0) * 100
            st.metric("总收益率", f"{total_return:.2f}%", 
                     delta=f"{total_return-0:.2f}%" if total_return != 0 else None)
        
        with col2:
            annual_return = metrics.get('annual_return', 0) * 100
            st.metric("年化收益率", f"{annual_return:.2f}%")
        
        with col3:
            max_drawdown = metrics.get('max_drawdown', 0) * 100
            st.metric("最大回撤", f"{max_drawdown:.2f}%")
        
        with col4:
            sharpe = metrics.get('sharpe_ratio', 0)
            st.metric("夏普比率", f"{sharpe:.2f}")
        
        # 绘制收益曲线
        st.markdown("#### 收益曲线")
        
        # 绘制回测结果图表
        try:
            # 导入可视化模块
            try:
                from core_logic.backtest import BacktestVisualizer
                visualizer = BacktestVisualizer()
            except ImportError:
                try:
                    from core_logic.backtest.visualization import BacktestVisualizer
                    visualizer = BacktestVisualizer()
                except ImportError:
                    raise ImportError("无法导入可视化模块")
            
            # 绘制组合表现图
            fig = visualizer.plot_portfolio_performance(
                portfolio_values=results["portfolio_values"],
                benchmark_values=results["benchmark_values"],
                title="策略表现"
            )
            
            st.pyplot(fig)
            
            # 绘制交易分析图
            if "trades" in results and not results["trades"].empty:
                st.markdown("#### 交易分析")
                
                fig = visualizer.plot_trade_analysis(
                    trades_df=results["trades"],
                    title="交易分析"
                )
                
                st.pyplot(fig)
            
            # 绘制月度收益热图
            st.markdown("#### 月度收益")
            
            fig = visualizer.plot_monthly_returns_heatmap(
                portfolio_values=results["portfolio_values"],
                title="月度收益热图"
            )
            
            st.pyplot(fig)
            
        except Exception as e:
            st.error(f"绘制图表时出错: {str(e)}")
            logger.error(f"绘制图表时出错: {str(e)}")
        
        # 显示交易记录
        if "trades" in results and not results["trades"].empty:
            st.markdown("#### 交易记录")
            
            # 显示交易表格
            trades_df = results["trades"]
            
            # 限制显示最新的100条记录
            if len(trades_df) > 100:
                st.info(f"共有 {len(trades_df)} 条交易记录，显示最新的100条")
                trades_df = trades_df.tail(100)
            
            st.dataframe(trades_df, use_container_width=True)
    
    with tabs[2]:
        st.markdown("### 绩效报告")
        
        # 检查是否有回测结果
        if 'backtest_results' not in st.session_state or st.session_state.backtest_results is None:
            st.info("尚未执行回测，请先在回测配置标签页设置参数并执行回测")
            return
        
        # 显示绩效报告
        metrics = st.session_state.backtest_metrics if 'backtest_metrics' in st.session_state else {}
        
        # 生成报告
        st.markdown("#### 详细绩效指标")
        
        # 分类显示指标
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("##### 收益指标")
            
            metrics_df = pd.DataFrame({
                "指标": [
                    "总收益率",
                    "年化收益率",
                    "月平均收益率",
                    "日平均收益率",
                    "正收益天数比例",
                    "负收益天数比例"
                ],
                "值": [
                    f"{metrics.get('total_return', 0):.2%}",
                    f"{metrics.get('annual_return', 0):.2%}",
                    f"{metrics.get('avg_monthly_return', 0):.2%}" if 'avg_monthly_return' in metrics else "N/A",
                    f"{metrics.get('daily_return_mean', 0):.4%}",
                    f"{metrics.get('positive_days', 0):.2%}",
                    f"{metrics.get('negative_days', 0):.2%}"
                ]
            })
            
            st.dataframe(metrics_df, use_container_width=True, hide_index=True)
        
        with col2:
            st.markdown("##### 风险指标")
            
            risk_df = pd.DataFrame({
                "指标": [
                    "最大回撤",
                    "最大回撤持续时间",
                    "波动率(年化)",
                    "下行波动率",
                    "夏普比率",
                    "索提诺比率"
                ],
                "值": [
                    f"{metrics.get('max_drawdown', 0):.2%}",
                    f"{metrics.get('max_drawdown_duration', 0)} 天",
                    f"{metrics.get('volatility', 0):.2%}",
                    f"{metrics.get('downside_volatility', 0):.2%}",
                    f"{metrics.get('sharpe_ratio', 0):.2f}",
                    f"{metrics.get('sortino_ratio', 0):.2f}"
                ]
            })
            
            st.dataframe(risk_df, use_container_width=True, hide_index=True)
        
        # 交易统计
        st.markdown("##### 交易统计")
        
        col1, col2 = st.columns(2)
        
        with col1:
            trade_df = pd.DataFrame({
                "指标": [
                    "交易总数",
                    "平均每日交易次数",
                    "胜率",
                    "盈亏比"
                ],
                "值": [
                    f"{metrics.get('total_trades', 0)}",
                    f"{metrics.get('avg_trades_per_day', 0):.2f}",
                    f"{metrics.get('win_rate', 0):.2%}",
                    f"{metrics.get('profit_loss_ratio', 0):.2f}"
                ]
            })
            
            st.dataframe(trade_df, use_container_width=True, hide_index=True)
        
        with col2:
            cost_df = pd.DataFrame({
                "指标": [
                    "总手续费",
                    "平均手续费",
                    "手续费率"
                ],
                "值": [
                    f"¥{metrics.get('total_commission', 0):.2f}",
                    f"¥{metrics.get('avg_commission', 0):.2f}",
                    f"{metrics.get('commission_ratio', 0):.4%}"
                ]
            })
            
            st.dataframe(cost_df, use_container_width=True, hide_index=True)
        
        # 导出报告
        st.markdown("#### 导出报告")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("导出HTML报告", help="将回测结果导出为HTML格式报告"):
                try:
                    # 导入报告生成模块
                    try:
                        from core_logic.backtest import ReportGenerator
                        report_generator = ReportGenerator()
                    except ImportError:
                        try:
                            from core_logic.backtest.report_generator import ReportGenerator
                            report_generator = ReportGenerator()
                        except ImportError:
                            raise ImportError("无法导入报告生成模块")
                    
                    # 生成报告
                    results = st.session_state.backtest_results
                    model_info = st.session_state.model if 'model' in st.session_state else {}
                    
                    report_path = report_generator.generate_report(
                        portfolio_values=results["portfolio_values"],
                        trades=results["trades"],
                        benchmark_values=results["benchmark_values"],
                        metrics=metrics,
                        strategy_params=model_info,
                        report_title="回测报告",
                        report_format="html"
                    )
                    
                    st.success(f"HTML报告已生成: {report_path}")
                    
                except Exception as e:
                    st.error(f"生成报告时出错: {str(e)}")
                    logger.error(f"生成报告时出错: {str(e)}")
        
        with col2:
            if st.button("导出JSON报告", help="将回测结果导出为JSON格式报告"):
                try:
                    # 导入报告生成模块
                    try:
                        from core_logic.backtest import ReportGenerator
                        report_generator = ReportGenerator()
                    except ImportError:
                        try:
                            from core_logic.backtest.report_generator import ReportGenerator
                            report_generator = ReportGenerator()
                        except ImportError:
                            raise ImportError("无法导入报告生成模块")
                    
                    # 生成报告
                    results = st.session_state.backtest_results
                    model_info = st.session_state.model if 'model' in st.session_state else {}
                    
                    report_path = report_generator.generate_report(
                        portfolio_values=results["portfolio_values"],
                        trades=results["trades"],
                        benchmark_values=results["benchmark_values"],
                        metrics=metrics,
                        strategy_params=model_info,
                        report_title="回测报告",
                        report_format="json"
                    )
                    
                    st.success(f"JSON报告已生成: {report_path}")
                    
                except Exception as e:
                    st.error(f"生成报告时出错: {str(e)}")
                    logger.error(f"生成报告时出错: {str(e)}")
        
        with col3:
            if st.button("导出Markdown报告", help="将回测结果导出为Markdown格式报告"):
                try:
                    # 导入报告生成模块
                    try:
                        from core_logic.backtest import ReportGenerator
                        report_generator = ReportGenerator()
                    except ImportError:
                        try:
                            from core_logic.backtest.report_generator import ReportGenerator
                            report_generator = ReportGenerator()
                        except ImportError:
                            raise ImportError("无法导入报告生成模块")
                    
                    # 生成报告
                    results = st.session_state.backtest_results
                    model_info = st.session_state.model if 'model' in st.session_state else {}
                    
                    report_path = report_generator.generate_report(
                        portfolio_values=results["portfolio_values"],
                        trades=results["trades"],
                        benchmark_values=results["benchmark_values"],
                        metrics=metrics,
                        strategy_params=model_info,
                        report_title="回测报告",
                        report_format="markdown"
                    )
                    
                    st.success(f"Markdown报告已生成: {report_path}")
                    
                except Exception as e:
                    st.error(f"生成报告时出错: {str(e)}")
                    logger.error(f"生成报告时出错: {str(e)}") 