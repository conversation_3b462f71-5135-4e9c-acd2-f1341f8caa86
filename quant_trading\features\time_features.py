"""
时间特征模块
负责计算时间特征
"""

import pandas as pd
import numpy as np
import logging

class TimeFeatures:
    """
    时间特征类
    负责计算时间特征
    """

    def __init__(self, logger=None):
        """
        初始化时间特征

        参数:
            logger (logging.Logger): 日志记录器
        """
        self.logger = logger or logging.getLogger('drl_trading')
        self.calculator = TimeFeatureCalculator(logger=self.logger)

    def calculate(self, df):
        """
        计算时间特征

        参数:
            df (pandas.DataFrame): 原始数据，索引必须是DatetimeIndex

        返回:
            tuple: (处理后的数据框, 生成的特征名称列表)
        """
        return self.calculator.calculate(df)

class TimeFeatureCalculator:
    """
    时间特征计算器
    负责计算时间特征
    """

    def __init__(self, logger=None):
        """
        初始化时间特征计算器

        参数:
            logger (logging.Logger): 日志记录器
        """
        self.logger = logger or logging.getLogger('drl_trading')

    def calculate(self, df):
        """
        计算时间特征

        参数:
            df (pandas.DataFrame): 原始数据，索引必须是DatetimeIndex

        返回:
            tuple: (处理后的数据框, 生成的特征名称列表)
        """
        # 确保索引是日期类型
        if not isinstance(df.index, pd.DatetimeIndex):
            self.logger.warning("数据索引不是日期类型，无法计算时间特征")
            return df, []

        # 创建一个字典来存储所有特征，避免DataFrame碎片化
        all_features = {}
        feature_names = []

        # 提取日期组件
        all_features['年'] = df.index.year
        feature_names.append('年')

        all_features['月'] = df.index.month
        feature_names.append('月')

        all_features['日'] = df.index.day
        feature_names.append('日')

        all_features['周几'] = df.index.dayofweek  # 0=周一, 6=周日
        feature_names.append('周几')

        all_features['季度'] = df.index.quarter
        feature_names.append('季度')

        all_features['月初'] = (df.index.day <= 5).astype(int)
        feature_names.append('月初')

        all_features['月中'] = ((df.index.day > 5) & (df.index.day <= 25)).astype(int)
        feature_names.append('月中')

        all_features['月末'] = (df.index.day > 25).astype(int)
        feature_names.append('月末')

        all_features['季初'] = ((df.index.month % 3 == 1) & (df.index.day <= 10)).astype(int)
        feature_names.append('季初')

        all_features['季末'] = ((df.index.month % 3 == 0) & (df.index.day >= 20)).astype(int)
        feature_names.append('季末')

        all_features['年初'] = ((df.index.month == 1) & (df.index.day <= 15)).astype(int)
        feature_names.append('年初')

        all_features['年末'] = ((df.index.month == 12) & (df.index.day >= 15)).astype(int)
        feature_names.append('年末')

        # 是否为交易日前后
        all_features['周一'] = (df.index.dayofweek == 0).astype(int)
        feature_names.append('周一')

        all_features['周五'] = (df.index.dayofweek == 4).astype(int)
        feature_names.append('周五')

        # 计算节假日相关特征 (简化版，实际应用中可以使用更完整的节假日日历)
        # 这里仅作为示例，使用周末作为节假日的近似
        all_features['节假日前'] = all_features['周五']
        feature_names.append('节假日前')

        all_features['节假日后'] = all_features['周一']
        feature_names.append('节假日后')

        # 计算时间趋势特征
        time_trend = np.arange(len(df))
        all_features['时间趋势'] = (time_trend - time_trend.min()) / (time_trend.max() - time_trend.min() + 1e-10)
        feature_names.append('时间趋势')

        # 计算周期性特征 (使用正弦和余弦变换)
        # 年度周期
        all_features['年周期_sin'] = np.sin(2 * np.pi * df.index.dayofyear / 365.25)
        feature_names.append('年周期_sin')

        all_features['年周期_cos'] = np.cos(2 * np.pi * df.index.dayofyear / 365.25)
        feature_names.append('年周期_cos')

        # 季度周期
        all_features['季周期_sin'] = np.sin(2 * np.pi * (df.index.dayofyear % 91) / 91)
        feature_names.append('季周期_sin')

        all_features['季周期_cos'] = np.cos(2 * np.pi * (df.index.dayofyear % 91) / 91)
        feature_names.append('季周期_cos')

        # 月度周期
        all_features['月周期_sin'] = np.sin(2 * np.pi * df.index.day / 30.4375)
        feature_names.append('月周期_sin')

        all_features['月周期_cos'] = np.cos(2 * np.pi * df.index.day / 30.4375)
        feature_names.append('月周期_cos')

        # 周度周期
        all_features['周周期_sin'] = np.sin(2 * np.pi * df.index.dayofweek / 5)
        feature_names.append('周周期_sin')

        all_features['周周期_cos'] = np.cos(2 * np.pi * df.index.dayofweek / 5)
        feature_names.append('周周期_cos')

        # 一次性创建所有特征的DataFrame
        features_df = pd.DataFrame(all_features, index=df.index)

        # 将所有特征合并到原始DataFrame
        df = pd.concat([df, features_df], axis=1)

        self.logger.info(f"计算了 {len(feature_names)} 个时间特征")

        return df, feature_names
