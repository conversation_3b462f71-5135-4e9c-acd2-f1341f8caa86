#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
优化特征工程适配器模块
提供与原始特征工程类兼容的接口
"""

import logging
import pandas as pd
import numpy as np
import warnings

from .optimized_feature_engineering import OptimizedFeatureEngineering

class OptimizedFeatureEngineeringAdapter:
    """
    优化特征工程适配器类
    提供与原始特征工程类兼容的接口
    """

    def __init__(self, feature_config=None, data_dictionary_path='data_dictionary.json', use_enhanced=False):
        """
        初始化优化特征工程适配器

        参数:
            feature_config (dict): 特征配置字典，指定要计算的技术指标及其参数
            data_dictionary_path (str): 数据字典文件路径
            use_enhanced (bool): 是否使用增强版特征工程
        """
        self.logger = logging.getLogger('drl_trading')
        self.use_enhanced = use_enhanced

        # 创建优化版特征工程器
        self.optimized_feature_eng = OptimizedFeatureEngineering(
            parallel_processing=True,
            use_cache=True
        )

        # 保存原始特征工程类的属性
        self.feature_config = feature_config or {}
        self.feature_names = []
        self.data_dictionary_path = data_dictionary_path

        # 特征配置到特征列表的映射
        self._init_feature_mapping()

    def _init_feature_mapping(self):
        """初始化特征配置到特征列表的映射"""
        self.feature_mapping = {
            'price_features': ['涨跌幅', '对数收益率', '价格动量', '价格加速度'],
            'sma': lambda periods: [f'SMA_{p}' for p in periods],
            'ema': lambda periods: [f'EMA_{p}' for p in periods],
            'rsi': lambda periods: [f'RSI_{p}' for p in periods],
            'macd': ['MACD', 'MACD_Signal', 'MACD_Hist'],
            'bbands': ['BBands_Upper', 'BBands_Middle', 'BBands_Lower'],
            'atr': ['ATR'],
            'adx': ['ADX'],
            'rolling_stats': lambda window: [f'Rolling_Volatility_{window}', f'Rolling_Skew_{window}', f'Rolling_Kurt_{window}']
        }

    def _config_to_feature_list(self):
        """将配置转换为特征列表"""
        feature_list = []

        # 价格特征
        if self.feature_config.get('price_features', {}).get('use', True):
            feature_list.extend(self.feature_mapping['price_features'])

        # 技术指标
        tech_config = self.feature_config.get('technical_indicators', {})
        if tech_config.get('use', True):
            # SMA
            if tech_config.get('sma', {}).get('use', True):
                periods = tech_config.get('sma', {}).get('periods', [5, 20, 60])
                feature_list.extend(self.feature_mapping['sma'](periods))

            # EMA
            if tech_config.get('ema', {}).get('use', True):
                periods = tech_config.get('ema', {}).get('periods', [5, 20])
                feature_list.extend(self.feature_mapping['ema'](periods))

            # RSI
            if tech_config.get('rsi', {}).get('use', True):
                periods = tech_config.get('rsi', {}).get('periods', [14])
                feature_list.extend(self.feature_mapping['rsi'](periods))

            # MACD
            if tech_config.get('macd', {}).get('use', True):
                feature_list.extend(self.feature_mapping['macd'])

            # Bollinger Bands
            if tech_config.get('bbands', {}).get('use', True):
                feature_list.extend(self.feature_mapping['bbands'])

            # ATR
            if tech_config.get('atr', {}).get('use', True):
                feature_list.extend(self.feature_mapping['atr'])

            # ADX
            if tech_config.get('adx', {}).get('use', True):
                feature_list.extend(self.feature_mapping['adx'])

        # 统计特征
        if self.feature_config.get('rolling_stats', {}).get('use', True):
            window = self.feature_config.get('rolling_stats', {}).get('window', 20)
            feature_list.extend(self.feature_mapping['rolling_stats'](window))

        return feature_list

    def generate_features(self, data, apply_selection=False, target_feature_count=None):
        """
        生成特征

        参数:
            data (pandas.DataFrame): 原始行情数据，必须包含OHLCV列
            apply_selection (bool): 是否应用特征选择
            target_feature_count (int): 目标特征数量，用于确保与原始模块兼容

        返回:
            pandas.DataFrame: 添加了特征的数据
        """
        # 确保列名标准化
        df = self._ensure_column_names(data.copy())

        # 将配置转换为特征列表
        feature_list = self._config_to_feature_list()

        # 调用优化版的特征计算
        try:
            # 首先计算基本价格特征
            df = self.optimized_feature_eng.calculate_price_features(df)

            # 然后计算技术指标
            df = self.optimized_feature_eng.calculate_features(df, feature_list)

            # 如果需要，计算所有特征
            if not feature_list:
                df = self.optimized_feature_eng.calculate_all_features(df)

            # 更新特征名称
            self.feature_names = [col for col in df.columns
                                if col not in ['开盘', '最高', '最低', '收盘', '成交量']]

            # 应用特征选择（如果启用）
            if apply_selection and self.feature_config.get('feature_selection', {}).get('use', True):
                n_features = self.feature_config.get('feature_selection', {}).get('n_features', 30)
                # 简单实现：选择前n个特征
                if len(self.feature_names) > n_features:
                    selected_features = self.feature_names[:n_features]
                    keep_cols = ['开盘', '最高', '最低', '收盘', '成交量'] + selected_features
                    df = df[keep_cols]
                    self.feature_names = selected_features

            # 归一化特征（如果需要）
            if self.feature_config.get('normalization', {}).get('use', True):
                method = self.feature_config.get('normalization', {}).get('method', 'minmax')
                df = self._normalize_features(df, method)

            # 检查是否需要调整特征数量以匹配目标特征数量
            if target_feature_count is not None:
                current_feature_count = len(self.feature_names)
                self.logger.info(f"当前特征数量: {current_feature_count}, 目标特征数量: {target_feature_count}")

                if current_feature_count != target_feature_count:
                    # 调整特征数量
                    if current_feature_count < target_feature_count:
                        # 如果特征数量不足，添加虚拟特征
                        missing_count = target_feature_count - current_feature_count
                        self.logger.warning(f"特征数量不足，添加 {missing_count} 个虚拟特征")

                        for i in range(missing_count):
                            feature_name = f"dummy_feature_{i+1}"
                            df[feature_name] = 0.0  # 使用零值填充
                            self.feature_names.append(feature_name)
                    else:
                        # 如果特征数量过多，只保留前target_feature_count个
                        self.logger.warning(f"特征数量过多，只保留前 {target_feature_count} 个特征")
                        self.feature_names = self.feature_names[:target_feature_count]
                        keep_cols = ['开盘', '最高', '最低', '收盘', '成交量'] + self.feature_names
                        df = df[keep_cols]

            self.logger.info(f"特征生成完成，共 {len(self.feature_names)} 个特征")
            return df

        except Exception as e:
            self.logger.error(f"优化特征计算失败: {str(e)}，尝试使用备用方法")
            # 如果优化版失败，可以实现备用方法
            # 这里可以添加备用的特征计算逻辑
            raise e

    def _ensure_column_names(self, df):
        """
        确保列名标准化

        参数:
            df (pandas.DataFrame): 原始数据

        返回:
            pandas.DataFrame: 标准化列名后的数据
        """
        # 英文列名到中文列名的映射
        column_mapping = {
            'open': '开盘',
            'high': '最高',
            'low': '最低',
            'close': '收盘',
            'volume': '成交量'
        }

        # 检查并重命名列
        for eng, chn in column_mapping.items():
            if eng in df.columns and chn not in df.columns:
                df[chn] = df[eng]

        # 确保必要的列存在
        required_columns = ['开盘', '最高', '最低', '收盘', '成交量']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            self.logger.error(f"数据缺少必要的列: {missing_columns}")
            raise ValueError(f"数据缺少必要的列: {missing_columns}")

        return df

    def _normalize_features(self, df, method='minmax'):
        """
        归一化特征

        参数:
            df (pandas.DataFrame): 包含特征的数据框
            method (str): 归一化方法

        返回:
            pandas.DataFrame: 归一化后的数据框
        """
        # 保留原始价格数据
        price_columns = ['开盘', '最高', '最低', '收盘', '成交量']
        if '成交额' in df.columns:
            price_columns.append('成交额')

        # 只保留存在的列
        existing_price_columns = [col for col in price_columns if col in df.columns]
        original_prices = df[existing_price_columns].copy()

        # 归一化其他特征列
        feature_columns = [col for col in df.columns if col not in price_columns]

        if feature_columns:
            # 简单实现：最小-最大归一化
            if method == 'minmax':
                for col in feature_columns:
                    if df[col].std() > 0:
                        df[col] = (df[col] - df[col].min()) / (df[col].max() - df[col].min())
            # 可以添加其他归一化方法

        # 将原始价格数据放回
        for col in existing_price_columns:
            df[col] = original_prices[col]

        return df
