#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
综合集成测试脚本
测试所有模块的集成功能
"""

import os
import sys
import time
import logging
import pandas as pd
import numpy as np
from datetime import datetime
import traceback
import matplotlib.pyplot as plt

# 设置日志
os.makedirs('logs', exist_ok=True)
log_file = os.path.join('logs', f'integration_test_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('integration_test')

# 添加项目根目录到系统路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_root)

# 添加quant_project目录到系统路径
quant_project_path = os.path.join(project_root, 'quant_project')
if os.path.exists(quant_project_path):
    sys.path.append(quant_project_path)

# 添加quant_trading目录到系统路径
quant_trading_path = os.path.join(project_root, 'quant_trading')
if os.path.exists(quant_trading_path):
    sys.path.append(quant_trading_path)

def import_modules():
    """导入所有必要的模块"""
    logger.info("导入必要模块")

    modules = {}

    # 导入数据处理模块
    try:
        from quant_project.core_logic.data_handler import DataHandler
        modules['DataHandler'] = DataHandler
        logger.info("成功导入DataHandler")
    except ImportError:
        try:
            from quant_trading.data.data_handler import DataHandler
            modules['DataHandler'] = DataHandler
            logger.info("成功导入DataHandler")
        except ImportError:
            logger.error("无法导入DataHandler")
            modules['DataHandler'] = None

    # 导入特征工程模块
    try:
        from quant_project.core_logic.feature_engineer import FeatureEngineer
        modules['FeatureEngineer'] = FeatureEngineer
        logger.info("成功导入FeatureEngineer")
    except ImportError:
        try:
            from quant_trading.features.feature_engineer import FeatureEngineer
            modules['FeatureEngineer'] = FeatureEngineer
            logger.info("成功导入FeatureEngineer")
        except ImportError:
            logger.error("无法导入FeatureEngineer")
            modules['FeatureEngineer'] = None

    # 导入特征工程适配器
    try:
        from quant_project.core_logic.feature_engineering_adapter import FeatureEngineeringAdapter
        modules['FeatureEngineeringAdapter'] = FeatureEngineeringAdapter
        logger.info("成功导入FeatureEngineeringAdapter")
    except ImportError:
        try:
            from quant_trading.features.feature_engineering_adapter import FeatureEngineeringAdapter
            modules['FeatureEngineeringAdapter'] = FeatureEngineeringAdapter
            logger.info("成功导入FeatureEngineeringAdapter")
        except ImportError:
            logger.error("无法导入FeatureEngineeringAdapter")
            modules['FeatureEngineeringAdapter'] = None

    # 导入交易环境模块
    try:
        from quant_project.core_logic.trading_environment import TradingEnvironment
        modules['TradingEnvironment'] = TradingEnvironment
        logger.info("成功导入TradingEnvironment")
    except ImportError:
        try:
            from quant_trading.trading.trading_environment import TradingEnvironment
            modules['TradingEnvironment'] = TradingEnvironment
            logger.info("成功导入TradingEnvironment")
        except ImportError:
            logger.error("无法导入TradingEnvironment")
            modules['TradingEnvironment'] = None

    # 导入DRL智能体模块
    try:
        from quant_project.core_logic.drl_agent import DRLAgent
        modules['DRLAgent'] = DRLAgent
        logger.info("成功导入DRLAgent")
    except ImportError:
        try:
            from quant_trading.agents.drl_agent import DRLAgent
            modules['DRLAgent'] = DRLAgent
            logger.info("成功导入DRLAgent")
        except ImportError:
            logger.error("无法导入DRLAgent")
            modules['DRLAgent'] = None

    # 导入性能分析模块
    try:
        from quant_project.core_logic.performance_analyzer import PerformanceAnalyzer
        modules['PerformanceAnalyzer'] = PerformanceAnalyzer
        logger.info("成功导入PerformanceAnalyzer")
    except ImportError:
        try:
            from quant_trading.evaluation.performance_analyzer import PerformanceAnalyzer
            modules['PerformanceAnalyzer'] = PerformanceAnalyzer
            logger.info("成功导入PerformanceAnalyzer")
        except ImportError:
            logger.error("无法导入PerformanceAnalyzer")
            modules['PerformanceAnalyzer'] = None

    return modules

def test_end_to_end(modules):
    """端到端测试"""
    logger.info("开始端到端测试")

    # 检查必要模块是否已导入
    required_modules = ['DataHandler', 'FeatureEngineer', 'TradingEnvironment', 'DRLAgent', 'PerformanceAnalyzer']
    missing_modules = [module for module in required_modules if modules.get(module) is None]

    if missing_modules:
        logger.error(f"缺少必要模块: {missing_modules}")
        return {
            'success': False,
            'error': f"缺少必要模块: {missing_modules}"
        }

    try:
        # 1. 数据获取
        logger.info("1. 数据获取")
        data_handler = modules['DataHandler']()

        # 测试不同的数据源
        data_sources = [
            {"code": "sh000001", "name": "上证指数"},
            {"code": "index_000300", "name": "沪深300指数"},
            {"code": "sh600519", "name": "贵州茅台"}
        ]

        # 测试不同的时间范围
        time_ranges = [
            {"start": "2022-01-01", "end": "2022-12-31", "name": "一年数据"},
            {"start": "2022-01-01", "end": "2022-03-31", "name": "一季度数据"},
            {"start": "2022-01-01", "end": "2022-01-31", "name": "一个月数据"}
        ]

        # 测试不同的频率
        frequencies = ["日线", "周线", "月线"]

        # 选择一个数据源、时间范围和频率进行测试
        data_source = data_sources[0]
        time_range = time_ranges[0]
        frequency = frequencies[0]

        logger.info(f"获取{data_source['name']}数据, {time_range['name']}, 频率: {frequency}")
        data = data_handler.get_stock_data(
            stock_code=data_source['code'],
            start_date=time_range['start'],
            end_date=time_range['end'],
            frequency=frequency
        )

        if data is None or data.empty:
            logger.error("获取数据失败")
            return {
                'success': False,
                'error': '获取数据失败'
            }

        logger.info(f"成功获取数据: {len(data)} 条记录")

        # 2. 特征工程
        logger.info("2. 特征工程")

        # 创建特征工程配置
        feature_config = {
            'price_features': {'use': True},
            'volume': {'use': True, 'periods': [5, 10, 20]},
            'sma': {'use': True, 'periods': [5, 10, 20, 60]},
            'ema': {'use': True, 'periods': [5, 10, 20]},
            'rsi': {'use': True, 'periods': [6, 14, 21]},
            'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9},
            'bbands': {'use': True, 'period': 20, 'std': 2.0},
            'normalization': {'use': True, 'method': 'minmax'}
        }

        feature_engineer = modules['FeatureEngineer'](feature_config)
        processed_data = feature_engineer.generate_features(data)

        if processed_data is None or processed_data.empty:
            logger.error("特征工程失败")
            return {
                'success': False,
                'error': '特征工程失败'
            }

        logger.info(f"特征工程成功: {processed_data.shape}")

        # 3. 交易环境
        logger.info("3. 交易环境")

        # 创建交易环境配置
        env_config = {
            'initial_capital': 100000,
            'commission_rate': 0.0003,
            'min_hold_days': 3,
            'allow_short': False,
            'max_position': 1.0,
            'window_size': 20,
            'reward_config': {
                'portfolio_return': 1.0,
                'volatility_penalty': 0.1,
                'drawdown_penalty': 0.2,
                'holding_penalty': 0.05,
                'trade_penalty': 0.01
            }
        }

        # 创建交易环境
        # 根据TradingEnvironment的参数签名，使用正确的参数名称
        trading_env = modules['TradingEnvironment'](
            df_processed_data=processed_data,
            initial_capital=env_config['initial_capital'],
            commission_rate=env_config['commission_rate'],
            min_hold_days=env_config['min_hold_days'],
            allow_short=env_config['allow_short'],
            max_position=env_config['max_position'],
            window_size=env_config['window_size'],
            reward_config=env_config['reward_config']
        )

        # 4. 模型训练 (简短训练，仅用于测试)
        logger.info("4. 模型训练 (简短训练)")

        # 创建DRL智能体
        # 根据DRLAgent的参数签名，使用正确的参数
        env_config = {
            'df_processed_data': processed_data,
            'initial_capital': env_config['initial_capital'],
            'commission_rate': env_config['commission_rate'],
            'min_hold_days': env_config['min_hold_days'],
            'allow_short': env_config['allow_short'],
            'max_position': env_config['max_position'],
            'window_size': env_config['window_size'],
            'reward_config': env_config['reward_config']
        }

        agent_config = {
            'algorithm': 'PPO',
            'policy_network': 'MlpPolicy',
            'verbose': 1,
            'learning_rate': 0.0003,
            'gamma': 0.99,
            'n_steps': 2048,
            'batch_size': 64,
            'n_epochs': 10
        }

        drl_agent = modules['DRLAgent'](
            env_config=env_config,
            agent_config=agent_config
        )

        # 简短训练
        logger.info("开始简短训练 (10000 步)")
        drl_agent.train(total_timesteps=10000)

        # 保存模型
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        model_dir = 'saved_models'
        os.makedirs(model_dir, exist_ok=True)

        # 创建模型文件名
        model_base_path = os.path.join(model_dir, f'test_model_{timestamp}')
        model_path = model_base_path
        model_zip_path = model_base_path + '.zip'

        # 检查DRLAgent是否有save方法
        model_saved = False
        try:
            if hasattr(drl_agent, 'save'):
                drl_agent.save(model_path)
                logger.info(f"模型已保存: {model_path}")
                model_saved = True
            elif hasattr(drl_agent, 'save_model'):
                drl_agent.save_model(model_path)
                logger.info(f"模型已保存: {model_path}")
                model_saved = True
            elif hasattr(drl_agent.model, 'save'):
                # 使用带.zip扩展名的路径
                drl_agent.model.save(model_zip_path)
                logger.info(f"模型已保存: {model_zip_path}")
                model_path = model_zip_path  # 更新模型路径为带.zip的路径
                model_saved = True
            else:
                logger.warning("无法保存模型，DRLAgent没有save或save_model方法")
                model_path = None
        except Exception as e:
            logger.error(f"保存模型时出错: {str(e)}")
            model_path = None

        # 5. 回测
        logger.info("5. 回测")

        # 检查模型文件是否存在
        if model_path and model_saved:
            # 检查文件是否存在
            model_exists = False
            if os.path.exists(model_path):
                model_exists = True
                load_path = model_path
            elif os.path.exists(model_path + '.zip'):
                model_exists = True
                load_path = model_path + '.zip'
            elif model_path.endswith('.zip') and os.path.exists(model_path):
                model_exists = True
                load_path = model_path

            if model_exists:
                try:
                    # 检查DRLAgent是否有load方法
                    if hasattr(drl_agent, 'load'):
                        drl_agent.load(load_path)
                        logger.info(f"模型已加载: {load_path}")
                    elif hasattr(drl_agent, 'load_model'):
                        drl_agent.load_model(load_path)
                        logger.info(f"模型已加载: {load_path}")
                    elif hasattr(drl_agent.model, 'load'):
                        # 确保模型路径有.zip扩展名
                        if not load_path.endswith('.zip') and os.path.exists(load_path + '.zip'):
                            load_path += '.zip'
                        drl_agent.model.load(load_path)
                        logger.info(f"模型已加载: {load_path}")
                    else:
                        logger.warning("无法加载模型，DRLAgent没有load或load_model方法")
                except Exception as e:
                    logger.error(f"加载模型时出错: {str(e)}")
                    logger.error(traceback.format_exc())
                    # 继续执行，使用当前模型进行回测
            else:
                logger.warning(f"模型文件不存在: {model_path}")
                # 继续执行，使用当前模型进行回测
        else:
            logger.warning("模型未保存，使用当前模型进行回测")

        # 回测
        try:
            # 检查DRLAgent是否有backtest方法
            if hasattr(drl_agent, 'backtest'):
                trades, portfolio_values = drl_agent.backtest(processed_data)
                logger.info("使用DRLAgent.backtest方法进行回测")
            else:
                # 如果没有backtest方法，尝试手动回测
                logger.warning("DRLAgent没有backtest方法，尝试手动回测")

                # 创建一个简单的回测函数
                def simple_backtest(agent, data):
                    """简单的回测函数"""
                    trades = []
                    initial_capital = 100000
                    cash = initial_capital
                    position = 0
                    portfolio_values = pd.Series(index=data.index)

                    # 确定价格列名
                    price_column = None
                    for col in ['close', 'Close', '收盘价', '收盘', 'close_price']:
                        if col in data.columns:
                            price_column = col
                            break

                    if price_column is None:
                        logger.error(f"无法找到价格列，可用列: {data.columns.tolist()}")
                        # 尝试使用第一个数值列作为价格
                        for col in data.columns:
                            if pd.api.types.is_numeric_dtype(data[col]):
                                price_column = col
                                logger.warning(f"使用列 '{col}' 作为价格列")
                                break

                    if price_column is None:
                        logger.error("无法找到合适的价格列，回测失败")
                        return pd.DataFrame(), pd.Series()

                    for i in range(len(data)):
                        date = data.index[i]
                        price = data.iloc[i][price_column]

                        # 获取观测值
                        if i >= 20:  # 假设窗口大小为20
                            window = data.iloc[i-20:i]
                            # 将DataFrame转换为2D数组，并确保维度正确
                            observation = window.values.flatten()

                            # 检查观测值维度是否与环境要求匹配
                            if hasattr(agent, 'env') and hasattr(agent.env, 'observation_space'):
                                expected_shape = agent.env.observation_space.shape[0]
                                if len(observation) != expected_shape:
                                    # 调整观测值维度
                                    if len(observation) > expected_shape:
                                        # 截断多余的维度
                                        observation = observation[:expected_shape]
                                    else:
                                        # 填充不足的维度
                                        padding = np.zeros(expected_shape - len(observation))
                                        observation = np.concatenate([observation, padding])

                            # 预测动作
                            action = 1  # 默认持有
                            try:
                                if hasattr(agent, 'predict_action'):
                                    action = agent.predict_action(observation)
                                elif hasattr(agent.model, 'predict'):
                                    # 确保观测值是正确的形状
                                    if hasattr(agent.model, 'observation_space'):
                                        expected_shape = agent.model.observation_space.shape
                                        if len(expected_shape) > 1:
                                            # 重塑观测值以匹配模型的期望
                                            observation = observation.reshape(expected_shape)
                                    # 尝试预测
                                    try:
                                        action, _ = agent.model.predict(observation)
                                    except Exception as e1:
                                        logger.warning(f"模型预测失败，尝试不同的输入格式: {str(e1)}")
                                        # 尝试不同的输入格式
                                        try:
                                            observation = np.array([observation])
                                            action, _ = agent.model.predict(observation)
                                        except Exception as e2:
                                            logger.warning(f"第二次尝试预测失败: {str(e2)}")
                                            # 使用默认动作
                                            action = 1
                                elif hasattr(agent, 'predict'):
                                    action = agent.predict(observation)
                            except Exception as e:
                                logger.warning(f"预测动作时出错: {str(e)}")

                            # 执行交易
                            if action == 0 and position > 0:  # 卖出
                                cash += position * price * 0.9997  # 考虑手续费
                                trades.append({
                                    'date': date,
                                    'action': 'sell',
                                    'price': price,
                                    'quantity': position,
                                    'value': position * price
                                })
                                position = 0
                            elif action == 2 and position == 0:  # 买入
                                shares = int(cash / price / 1.0003)  # 考虑手续费
                                if shares > 0:
                                    cash -= shares * price * 1.0003
                                    position = shares
                                    trades.append({
                                        'date': date,
                                        'action': 'buy',
                                        'price': price,
                                        'quantity': shares,
                                        'value': shares * price
                                    })

                        # 计算组合价值
                        portfolio_values[date] = cash + position * price

                    return pd.DataFrame(trades), portfolio_values

                trades, portfolio_values = simple_backtest(drl_agent, processed_data)
                logger.info("使用简单回测函数进行回测")

            if trades is None or portfolio_values is None:
                logger.error("回测失败：交易记录或投资组合价值为None")
                # 创建一些模拟数据用于后续分析
                dates = processed_data.index[-30:]
                portfolio_values = pd.Series(index=dates)
                initial_value = 100000
                for i, date in enumerate(dates):
                    # 简单的随机波动
                    portfolio_values[date] = initial_value * (1 + 0.001 * (i - 15))

                trades = pd.DataFrame({
                    'date': [dates[5], dates[10], dates[15], dates[20]],
                    'action': ['buy', 'sell', 'buy', 'sell'],
                    'price': [100, 105, 103, 108],
                    'quantity': [100, 100, 100, 100],
                    'value': [10000, 10500, 10300, 10800]
                })

                logger.warning("使用模拟数据进行性能分析")
                return {
                    'success': False,
                    'error': '回测失败，使用模拟数据',
                    'data_shape': processed_data.shape,
                    'processed_data_shape': processed_data.shape,
                    'trades_count': len(trades),
                    'portfolio_values': portfolio_values
                }
            elif isinstance(trades, pd.DataFrame) and trades.empty:
                logger.warning("回测成功但没有交易记录，可能是模型选择了持有策略")
                # 如果没有交易但有投资组合价值，继续使用投资组合价值进行分析
                if portfolio_values is not None and len(portfolio_values) > 0:
                    logger.info(f"投资组合价值记录数: {len(portfolio_values)}")
                    # 创建一个空的交易记录DataFrame，但确保它有正确的列
                    trades = pd.DataFrame(columns=['date', 'action', 'price', 'quantity', 'value'])
                else:
                    logger.error("回测失败：投资组合价值为空")
                    # 创建一些模拟数据用于后续分析
                    dates = processed_data.index[-30:]
                    portfolio_values = pd.Series(index=dates)
                    initial_value = 100000
                    for i, date in enumerate(dates):
                        # 简单的随机波动
                        portfolio_values[date] = initial_value * (1 + 0.001 * (i - 15))

                    trades = pd.DataFrame({
                        'date': [dates[5], dates[10], dates[15], dates[20]],
                        'action': ['buy', 'sell', 'buy', 'sell'],
                        'price': [100, 105, 103, 108],
                        'quantity': [100, 100, 100, 100],
                        'value': [10000, 10500, 10300, 10800]
                    })

                    logger.warning("使用模拟数据进行性能分析")
                    return {
                        'success': False,
                        'error': '回测失败，使用模拟数据',
                        'data_shape': processed_data.shape,
                        'processed_data_shape': processed_data.shape,
                        'trades_count': len(trades),
                        'portfolio_values': portfolio_values
                    }
        except Exception as e:
            logger.error(f"回测过程中出错: {str(e)}")
            logger.error(traceback.format_exc())
            return {
                'success': False,
                'error': f'回测失败: {str(e)}'
            }

        logger.info(f"回测成功: {len(trades)} 笔交易")

        # 6. 性能分析
        logger.info("6. 性能分析")

        # 创建性能分析器
        performance_analyzer = modules['PerformanceAnalyzer']()

        # 分析性能
        metrics = performance_analyzer.analyze(trades, portfolio_values)

        logger.info("性能指标:")
        for key, value in metrics.items():
            if isinstance(value, (int, float)):
                logger.info(f"  {key}: {value:.4f}")
            else:
                logger.info(f"  {key}: {value}")

        # 7. 清理
        logger.info("7. 清理")

        # 删除测试模型
        if os.path.exists(model_path):
            os.remove(model_path)
            logger.info(f"已删除测试模型: {model_path}")

        return {
            'success': True,
            'data_shape': data.shape,
            'processed_data_shape': processed_data.shape,
            'trades_count': len(trades),
            'metrics': metrics
        }

    except Exception as e:
        logger.error(f"端到端测试出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            'success': False,
            'error': str(e)
        }

def run_integration_test():
    """运行集成测试"""
    logger.info("开始运行集成测试")

    # 导入模块
    modules = import_modules()

    # 运行端到端测试
    result = test_end_to_end(modules)

    # 输出测试结果
    if result['success']:
        logger.info("集成测试成功")
        logger.info(f"数据形状: {result['data_shape']}")
        logger.info(f"处理后数据形状: {result['processed_data_shape']}")
        logger.info(f"交易次数: {result['trades_count']}")
        logger.info(f"总收益率: {result['metrics']['total_return']:.4f}")
        logger.info(f"年化收益率: {result['metrics']['annualized_return']:.4f}")
        logger.info(f"最大回撤: {result['metrics']['max_drawdown']:.4f}")
        logger.info(f"夏普比率: {result['metrics']['sharpe_ratio']:.4f}")
    else:
        logger.error(f"集成测试失败: {result['error']}")

    return result

if __name__ == "__main__":
    run_integration_test()
