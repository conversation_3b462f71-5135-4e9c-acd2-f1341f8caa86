"""
优化模块整合
将所有优化后的模块整合到一起，提供统一的接口
符合顶尖量化基金的最佳实践
"""

import os
import logging
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import json

from core_logic.enhanced_feature_engineer import EnhancedFeatureEngineer
from core_logic.enhanced_drl_agent import EnhancedDRLAgent
from core_logic.enhanced_trading_environment import EnhancedTradingEnvironment
from core_logic.enhanced_performance_analyzer import EnhancedPerformanceAnalyzer
from core_logic.data_handler import DataHandler
from core_logic.utils import is_gpu_available, get_gpu_info

class OptimizedQuantSystem:
    """
    优化量化系统类
    整合所有优化后的模块，提供统一的接口
    """

    def __init__(self, config_path=None):
        """
        初始化优化量化系统

        参数:
            config_path (str, optional): 配置文件路径
        """
        # 初始化日志
        self.logger = logging.getLogger('drl_trading')

        # 加载配置
        self.config = self._load_config(config_path)

        # 初始化数据处理器
        self.data_handler = DataHandler(
            data_dir=self.config.get('data', {}).get('data_dir', 'data'),
            cache_dir=self.config.get('data', {}).get('cache_dir', 'data_cache')
        )

        # 初始化特征工程器
        self.feature_engineer = EnhancedFeatureEngineer(
            feature_config=self.config.get('feature_engineering', {})
        )

        # 初始化性能分析器
        self.performance_analyzer = EnhancedPerformanceAnalyzer()

        # 初始化环境和智能体
        self.env = None
        self.agent = None

        # 数据和特征
        self.raw_data = None
        self.processed_data = None
        self.benchmark_data = None

        # 训练和回测结果
        self.training_results = None
        self.backtest_results = None

        # 检查GPU可用性
        self.gpu_available = is_gpu_available()
        if self.gpu_available:
            gpu_info = get_gpu_info()
            self.logger.info(f"GPU可用: {gpu_info}")
        else:
            self.logger.info("GPU不可用，将使用CPU")

    def _load_config(self, config_path):
        """
        加载配置

        参数:
            config_path (str): 配置文件路径

        返回:
            dict: 配置字典
        """
        # 默认配置
        default_config = {
            'data': {
                'data_dir': 'data',
                'cache_dir': 'data_cache'
            },
            'feature_engineering': {
                'sma': {'use': True, 'periods': [5, 10, 20, 60]},
                'ema': {'use': True, 'periods': [5, 10, 20, 60]},
                'rsi': {'use': True, 'periods': [6, 14, 21]},
                'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9},
                'bbands': {'use': True, 'periods': [20], 'stds': [2.0]},
                'atr': {'use': True, 'periods': [7, 14, 21]},
                'stoch': {'use': True, 'k_periods': [9, 14], 'd_periods': [3, 5]},
                'volume': {'use': True, 'periods': [5, 10, 20, 60]},
                'rolling_stats': {'use': True, 'windows': [20, 60]},
                'alpha_factors': {'use': True},
                'market_microstructure': {'use': True},
                'time_features': {'use': True},
                'adaptive_features': {'use': True},
                'sentiment_indicators': {'use': True},
                'feature_selection': {'use': True, 'method': 'combined', 'n_features': 40, 'target_days': 5},
                'normalization': {'method': 'robust'}
            },
            'environment': {
                'initial_capital': 100000.0,
                'commission_rate': 0.0003,
                'min_hold_days': 3,
                'allow_short': False,
                'max_position': 1.0,
                'window_size': 20,
                'action_type': 'discrete',
                'n_discrete_actions': 3,
                'slippage_model': 'percentage',
                'slippage_value': 0.001,
                'enable_stop_loss': True,
                'stop_loss_threshold': 0.05,
                'enable_take_profit': True,
                'take_profit_threshold': 0.1,
                'dynamic_risk_management': True,
                'volatility_scaling': True,
                'reward_config': {
                    'return_weight': 1.0,
                    'volatility_weight': -1.0,
                    'drawdown_weight': -1.0,
                    'holding_penalty': -0.0001,
                    'turnover_penalty': -0.001,
                    'monthly_return_target': 0.10,
                    'monthly_drawdown_limit': 0.04,
                    'monthly_sharpe_target': 1.5,
                    'target_achievement_weight': 2.0,
                    'progressive_reward_factor': 1.0
                }
            },
            'agent': {
                'algorithm': 'PPO',  # 可选: 'PPO', 'A2C', 'SAC', 'TD3', 'DDPG'
                'policy_network': 'MlpPolicy',
                'learning_rate': 0.0003,
                'gamma': 0.99,
                'n_steps': 2048,
                'batch_size': 64,
                'n_epochs': 10,
                'gae_lambda': 0.95,
                'clip_range': 0.2,
                'ent_coef': 0.01,
                'vf_coef': 0.5,
                'max_grad_norm': 0.5,
                'use_sde': True,  # 随机探索
                'sde_sample_freq': 4,  # SDE采样频率
                'use_gpu': True,
                'use_vec_env': True,
                'use_vec_normalize': True,
                'early_stopping': {
                    'use': True,
                    'reward_threshold': 0.0,
                    'patience': 10
                },
                'net_arch': [
                    {'pi': [256, 128, 64], 'vf': [256, 128, 64]}  # 更深的网络
                ],
                'activation_fn': 'ReLU',
                'features_extractor': 'attention',  # 使用注意力机制
                'features_dim': 128,  # 特征提取器输出维度
                'normalize_advantage': True,  # 优势归一化
                'target_kl': 0.01,  # KL散度目标
                'curriculum_learning': {  # 课程学习
                    'use': True,
                    'stages': [
                        {'timesteps': 20000, 'reward_weights': {'return_weight': 1.0, 'volatility_weight': -0.5, 'drawdown_weight': -0.5}},
                        {'timesteps': 40000, 'reward_weights': {'return_weight': 1.0, 'volatility_weight': -1.0, 'drawdown_weight': -1.0}},
                        {'timesteps': 60000, 'reward_weights': {'return_weight': 1.0, 'volatility_weight': -1.5, 'drawdown_weight': -1.5}}
                    ]
                },
                'ensemble': {  # 集成学习
                    'use': True,
                    'n_models': 3,
                    'voting_method': 'weighted'  # 'majority', 'weighted', 'average'
                }
            },
            'training': {
                'total_timesteps': 100000,
                'eval_freq': 10000,
                'n_eval_episodes': 5,
                'save_path': 'saved_models'
            },
            'hyperparameter_optimization': {
                'use': True,
                'n_trials': 100,
                'n_startup_trials': 20,
                'n_evaluations': 3,
                'n_timesteps': 50000,
                'optimization_method': 'bayesian',  # 'bayesian', 'random', 'grid'
                'target_metric': 'sharpe_ratio',  # 'return', 'sharpe_ratio', 'sortino_ratio', 'calmar_ratio'
                'param_space': {
                    'learning_rate': {'min': 1e-5, 'max': 1e-3, 'log': True},
                    'gamma': {'min': 0.9, 'max': 0.9999},
                    'n_steps': {'min': 32, 'max': 2048, 'log': True},
                    'batch_size': {'min': 32, 'max': 256, 'log': True},
                    'n_epochs': {'min': 5, 'max': 20},
                    'gae_lambda': {'min': 0.8, 'max': 0.99},
                    'clip_range': {'min': 0.1, 'max': 0.3},
                    'ent_coef': {'min': 0.0, 'max': 0.1, 'log': True},
                    'vf_coef': {'min': 0.1, 'max': 0.9},
                    'net_arch_pi': [
                        [64, 64],
                        [128, 64],
                        [256, 128, 64],
                        [512, 256, 128]
                    ],
                    'net_arch_vf': [
                        [64, 64],
                        [128, 64],
                        [256, 128, 64],
                        [512, 256, 128]
                    ],
                    'activation_fn': ['tanh', 'relu', 'elu'],
                    'features_dim': {'min': 64, 'max': 256, 'step': 32},
                    'sde_sample_freq': {'min': 1, 'max': 8},
                    'target_kl': {'min': 0.001, 'max': 0.05, 'log': True},
                    'reward_weights': [
                        {'return_weight': 1.0, 'volatility_weight': -0.5, 'drawdown_weight': -0.5},
                        {'return_weight': 1.0, 'volatility_weight': -1.0, 'drawdown_weight': -1.0},
                        {'return_weight': 1.0, 'volatility_weight': -1.5, 'drawdown_weight': -1.5},
                        {'return_weight': 1.0, 'volatility_weight': -2.0, 'drawdown_weight': -2.0}
                    ],
                    'stop_loss_threshold': {'min': 0.02, 'max': 0.1},
                    'take_profit_threshold': {'min': 0.05, 'max': 0.2}
                },
                'pruning': {
                    'use': True,
                    'method': 'median',  # 'median', 'percentile'
                    'patience': 5
                }
            }
        }

        # 如果提供了配置文件路径，加载配置
        if config_path and os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)

                # 合并配置
                self._merge_config(default_config, user_config)

                self.logger.info(f"已从 {config_path} 加载配置")
            except Exception as e:
                self.logger.error(f"加载配置文件失败: {str(e)}")

        return default_config

    def _merge_config(self, default_config, user_config):
        """
        合并配置

        参数:
            default_config (dict): 默认配置
            user_config (dict): 用户配置
        """
        for key, value in user_config.items():
            if key in default_config and isinstance(default_config[key], dict) and isinstance(value, dict):
                self._merge_config(default_config[key], value)
            else:
                default_config[key] = value

    def prepare_data(self, code, start_date, end_date, interval='daily', benchmark_code=None):
        """
        准备数据

        参数:
            code (str): 股票代码
            start_date (str): 开始日期
            end_date (str): 结束日期
            interval (str): 时间间隔
            benchmark_code (str, optional): 基准代码

        返回:
            pandas.DataFrame: 处理后的数据
        """
        # 获取原始数据
        self.raw_data = self.data_handler.get_data(code, start_date, end_date, interval)

        if self.raw_data is None or len(self.raw_data) == 0:
            self.logger.error(f"获取数据失败: {code}")
            return None

        self.logger.info(f"获取数据成功: {code}, 数据点数: {len(self.raw_data)}")

        # 如果提供了基准代码，获取基准数据
        if benchmark_code:
            self.benchmark_data = self.data_handler.get_data(benchmark_code, start_date, end_date, interval)
            if self.benchmark_data is not None:
                self.logger.info(f"获取基准数据成功: {benchmark_code}, 数据点数: {len(self.benchmark_data)}")
                self.performance_analyzer = EnhancedPerformanceAnalyzer(benchmark_data=self.benchmark_data)

        # 生成特征
        self.processed_data = self.feature_engineer.generate_features(self.raw_data, apply_selection=True)

        self.logger.info(f"特征生成成功，特征数: {len(self.processed_data.columns)}")

        return self.processed_data

    def create_environment(self):
        """
        创建交易环境

        返回:
            EnhancedTradingEnvironment: 交易环境
        """
        if self.processed_data is None:
            self.logger.error("请先准备数据")
            return None

        # 创建环境
        self.env = EnhancedTradingEnvironment(
            df_processed_data=self.processed_data,
            initial_capital=self.config['environment']['initial_capital'],
            commission_rate=self.config['environment']['commission_rate'],
            min_hold_days=self.config['environment']['min_hold_days'],
            allow_short=self.config['environment']['allow_short'],
            max_position=self.config['environment']['max_position'],
            reward_config=self.config['environment']['reward_config'],
            window_size=self.config['environment']['window_size'],
            action_type=self.config['environment']['action_type'],
            n_discrete_actions=self.config['environment']['n_discrete_actions'],
            slippage_model=self.config['environment'].get('slippage_model', 'percentage'),
            slippage_value=self.config['environment'].get('slippage_value', 0.001),
            enable_stop_loss=self.config['environment'].get('enable_stop_loss', True),
            stop_loss_threshold=self.config['environment'].get('stop_loss_threshold', 0.05),
            enable_take_profit=self.config['environment'].get('enable_take_profit', True),
            take_profit_threshold=self.config['environment'].get('take_profit_threshold', 0.1),
            dynamic_risk_management=self.config['environment'].get('dynamic_risk_management', True),
            volatility_scaling=self.config['environment'].get('volatility_scaling', True)
        )

        self.logger.info("交易环境创建成功")

        return self.env

    def create_agent(self):
        """
        创建智能体

        返回:
            EnhancedDRLAgent: 智能体
        """
        if self.env is None:
            self.logger.error("请先创建环境")
            return None

        # 创建环境配置
        env_config = {
            'df_processed_data': self.processed_data,
            'initial_capital': self.config['environment']['initial_capital'],
            'commission_rate': self.config['environment']['commission_rate'],
            'min_hold_days': self.config['environment']['min_hold_days'],
            'allow_short': self.config['environment']['allow_short'],
            'max_position': self.config['environment']['max_position'],
            'reward_config': self.config['environment']['reward_config'],
            'window_size': self.config['environment']['window_size'],
            'action_type': self.config['environment']['action_type'],
            'n_discrete_actions': self.config['environment']['n_discrete_actions'],
            'slippage_model': self.config['environment'].get('slippage_model', 'percentage'),
            'slippage_value': self.config['environment'].get('slippage_value', 0.001),
            'enable_stop_loss': self.config['environment'].get('enable_stop_loss', True),
            'stop_loss_threshold': self.config['environment'].get('stop_loss_threshold', 0.05),
            'enable_take_profit': self.config['environment'].get('enable_take_profit', True),
            'take_profit_threshold': self.config['environment'].get('take_profit_threshold', 0.1),
            'dynamic_risk_management': self.config['environment'].get('dynamic_risk_management', True),
            'volatility_scaling': self.config['environment'].get('volatility_scaling', True)
        }

        # 创建智能体配置
        agent_config = self.config['agent'].copy()

        # 如果GPU不可用，禁用GPU
        if not self.gpu_available:
            agent_config['use_gpu'] = False

        # 创建超参数优化配置
        hpo_config = self.config['hyperparameter_optimization'] if self.config['hyperparameter_optimization']['use'] else None

        # 创建智能体
        self.agent = EnhancedDRLAgent(
            env_config=env_config,
            agent_config=agent_config,
            hpo_config=hpo_config
        )

        self.logger.info(f"智能体创建成功，算法: {agent_config['algorithm']}")

        return self.agent

    def train_agent(self, total_timesteps=None):
        """
        训练智能体

        参数:
            total_timesteps (int, optional): 训练总步数

        返回:
            dict: 训练结果
        """
        if self.agent is None:
            self.logger.error("请先创建智能体")
            return None

        # 如果没有指定训练步数，使用配置中的步数
        if total_timesteps is None:
            total_timesteps = self.config['training']['total_timesteps']

        # 训练智能体
        self.training_results = self.agent.train(total_timesteps=total_timesteps)

        self.logger.info(f"智能体训练完成，总步数: {total_timesteps}")

        return self.training_results

    def optimize_hyperparameters(self):
        """
        优化超参数

        返回:
            dict: 最佳超参数
        """
        if self.agent is None:
            self.logger.error("请先创建智能体")
            return None

        if not self.config['hyperparameter_optimization']['use']:
            self.logger.warning("超参数优化未启用")
            return None

        # 优化超参数
        best_params = self.agent.optimize_hyperparameters(
            n_trials=self.config['hyperparameter_optimization']['n_trials'],
            n_startup_trials=self.config['hyperparameter_optimization']['n_startup_trials'],
            n_evaluations=self.config['hyperparameter_optimization']['n_evaluations'],
            n_timesteps=self.config['hyperparameter_optimization']['n_timesteps']
        )

        self.logger.info(f"超参数优化完成，最佳参数: {best_params}")

        return best_params

    def backtest(self, deterministic=True):
        """
        回测

        参数:
            deterministic (bool): 是否使用确定性策略

        返回:
            dict: 回测结果
        """
        if self.agent is None:
            self.logger.error("请先创建并训练智能体")
            return None

        # 重置环境
        obs, _ = self.env.reset()

        # 初始化回测结果
        portfolio_history = []

        # 执行回测
        done = False
        while not done:
            # 预测动作
            action = self.agent.predict_action(obs, deterministic=deterministic)

            # 执行动作
            obs, reward, terminated, truncated, info = self.env.step(action)

            # 记录结果
            portfolio_entry = {
                'step': info['step'],
                'date': self.processed_data.index[info['step']],
                'price': self.processed_data.iloc[info['step']]['收盘'],
                'action': action,
                'position': info['position'],
                'portfolio_value': info['portfolio_value'],
                'cash': info['cash'],
                'holding_days': info['holding_days'],
                'return': info['return']
            }

            # 添加风险管理相关信息
            if 'entry_price' in info:
                portfolio_entry['entry_price'] = info['entry_price']
            if 'position_direction' in info:
                portfolio_entry['position_direction'] = info['position_direction']
            if 'volatility_scaling_factor' in info:
                portfolio_entry['volatility_scaling_factor'] = info['volatility_scaling_factor']
            if 'dynamic_stop_loss_threshold' in info:
                portfolio_entry['dynamic_stop_loss_threshold'] = info['dynamic_stop_loss_threshold']
            if 'dynamic_take_profit_threshold' in info:
                portfolio_entry['dynamic_take_profit_threshold'] = info['dynamic_take_profit_threshold']
            if 'stop_loss_triggered' in info:
                portfolio_entry['stop_loss_triggered'] = info['stop_loss_triggered']
            if 'take_profit_triggered' in info:
                portfolio_entry['take_profit_triggered'] = info['take_profit_triggered']

            # 添加月度性能指标（如果有）
            if 'monthly_return' in info:
                portfolio_entry['monthly_return'] = info['monthly_return']
            if 'monthly_drawdown' in info:
                portfolio_entry['monthly_drawdown'] = info['monthly_drawdown']
            if 'monthly_sharpe' in info:
                portfolio_entry['monthly_sharpe'] = info['monthly_sharpe']

            portfolio_history.append(portfolio_entry)

            # 检查是否结束
            done = terminated or truncated

        # 转换为DataFrame
        portfolio_df = pd.DataFrame(portfolio_history)
        portfolio_df.set_index('date', inplace=True)

        # 分析性能
        performance_metrics = self.performance_analyzer.analyze_performance(portfolio_df)

        # 分析风险
        risk_metrics = self.performance_analyzer.analyze_risk(portfolio_df)

        # 生成性能报告
        report = self.performance_analyzer.generate_performance_report(
            portfolio_df,
            output_path='reports'
        )

        # 汇总回测结果
        self.backtest_results = {
            'portfolio_history': portfolio_df,
            'performance_metrics': performance_metrics,
            'risk_metrics': risk_metrics,
            'report': report
        }

        self.logger.info(f"回测完成，最终投资组合价值: {portfolio_df['portfolio_value'].iloc[-1]:.2f}")

        return self.backtest_results

    def save_model(self, save_path=None):
        """
        保存模型

        参数:
            save_path (str, optional): 保存路径

        返回:
            str: 模型保存路径
        """
        if self.agent is None:
            self.logger.error("请先创建并训练智能体")
            return None

        # 如果没有指定保存路径，使用配置中的路径
        if save_path is None:
            save_path = self.config['training']['save_path']

        # 创建保存目录
        os.makedirs(save_path, exist_ok=True)

        # 保存模型
        model_path = self.agent.save_model(save_path)

        self.logger.info(f"模型已保存到: {model_path}")

        return model_path

    def load_model(self, load_path):
        """
        加载模型

        参数:
            load_path (str): 模型路径

        返回:
            EnhancedDRLAgent: 智能体
        """
        if self.agent is None:
            self.logger.error("请先创建智能体")
            return None

        # 加载模型
        self.agent.load_model(load_path)

        self.logger.info(f"模型已从 {load_path} 加载")

        return self.agent

    def generate_trading_signals(self, data=None, deterministic=True):
        """
        生成交易信号

        参数:
            data (pandas.DataFrame, optional): 数据
            deterministic (bool): 是否使用确定性策略

        返回:
            pandas.DataFrame: 交易信号
        """
        if self.agent is None:
            self.logger.error("请先创建并训练智能体")
            return None

        # 如果没有提供数据，使用处理后的数据
        if data is None:
            data = self.processed_data

        # 创建环境
        env = EnhancedTradingEnvironment(
            df_processed_data=data,
            initial_capital=self.config['environment']['initial_capital'],
            commission_rate=self.config['environment']['commission_rate'],
            min_hold_days=self.config['environment']['min_hold_days'],
            allow_short=self.config['environment']['allow_short'],
            max_position=self.config['environment']['max_position'],
            reward_config=self.config['environment']['reward_config'],
            window_size=self.config['environment']['window_size'],
            action_type=self.config['environment']['action_type'],
            n_discrete_actions=self.config['environment']['n_discrete_actions']
        )

        # 重置环境
        obs, _ = env.reset()

        # 初始化交易信号
        signals = []

        # 生成交易信号
        done = False
        while not done:
            # 预测动作
            action = self.agent.predict_action(obs, deterministic=deterministic)

            # 执行动作
            obs, reward, terminated, truncated, info = env.step(action)

            # 记录信号
            signals.append({
                'date': data.index[info['step']],
                'price': data.iloc[info['step']]['收盘'],
                'action': action,
                'position': info['position'],
                'signal': 1 if action > 0 else (-1 if action < 0 else 0)
            })

            # 检查是否结束
            done = terminated or truncated

        # 转换为DataFrame
        signals_df = pd.DataFrame(signals)
        signals_df.set_index('date', inplace=True)

        return signals_df
