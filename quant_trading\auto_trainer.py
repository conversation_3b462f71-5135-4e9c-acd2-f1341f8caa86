import os
import time
import logging
import json
import pandas as pd
import numpy as np
import torch
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import traceback
import psutil
import threading
import matplotlib.pyplot as plt
from typing import Dict, List, Union, Tuple, Callable, Optional, Any
from datetime import datetime, timedelta

# Configure logger
logger = logging.getLogger(__name__)

class AutoTrainer:
    """
    Automated training manager for quantitative trading models.
    Provides utilities for unattended training, monitoring, error recovery,
    and notification.
    """
    
    def __init__(self, 
                model_trainer: Any,
                data_loader: Callable,
                config: Dict = None,
                checkpoint_dir: str = './checkpoints',
                log_dir: str = './logs',
                email_config: Dict = None,
                recovery_mode: bool = True,
                resource_monitoring: bool = True,
                max_training_time: int = None):
        """
        Initialize the AutoTrainer.
        
        Args:
            model_trainer: Model trainer object (must have train, save_checkpoint methods)
            data_loader: Function to load training data
            config: Configuration dictionary
            checkpoint_dir: Directory to save checkpoints
            log_dir: Directory to save logs
            email_config: Email configuration for notifications
            recovery_mode: Whether to enable error recovery
            resource_monitoring: Whether to monitor system resources
            max_training_time: Maximum training time in seconds (None = no limit)
        """
        self.model_trainer = model_trainer
        self.data_loader = data_loader
        self.config = config or {}
        self.checkpoint_dir = checkpoint_dir
        self.log_dir = log_dir
        self.email_config = email_config
        self.recovery_mode = recovery_mode
        self.resource_monitoring = resource_monitoring
        self.max_training_time = max_training_time
        
        # Create directories
        os.makedirs(checkpoint_dir, exist_ok=True)
        os.makedirs(log_dir, exist_ok=True)
        
        # Setup file logger
        self.setup_logger()
        
        # Initialize training state
        self.training_stats = {
            'start_time': None,
            'end_time': None,
            'duration': None,
            'epochs_completed': 0,
            'best_metric': float('inf'),
            'early_stops': 0,
            'errors': [],
            'error_count': 0,
            'recovered_count': 0,
            'checkpoints_saved': 0
        }
        
        # For resource monitoring
        self.resource_stats = {
            'cpu_usage': [],
            'memory_usage': [],
            'gpu_usage': [],
            'timestamps': []
        }
        
        # Initialize monitoring thread
        self.monitoring_thread = None
        self.stop_monitoring = False
        
    def setup_logger(self):
        """
        Setup file logger for training.
        """
        # Create timestamp for log file
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        log_file = os.path.join(self.log_dir, f'training_{timestamp}.log')
        
        # Configure file handler
        file_handler = logging.FileHandler(log_file)
        file_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(file_formatter)
        file_handler.setLevel(logging.INFO)
        
        # Add file handler to logger
        logger.addHandler(file_handler)
        logger.setLevel(logging.INFO)
        
        logger.info(f"AutoTrainer initialized. Log file: {log_file}")
        
    def send_email_notification(self, subject: str, message: str):
        """
        Send email notification.
        
        Args:
            subject: Email subject
            message: Email message
        """
        if self.email_config is None:
            logger.warning("Email configuration not provided, skipping notification")
            return
            
        try:
            # Create email
            msg = MIMEMultipart()
            msg['From'] = self.email_config.get('sender', '<EMAIL>')
            msg['To'] = self.email_config.get('recipient', '<EMAIL>')
            msg['Subject'] = subject
            
            # Add message
            msg.attach(MIMEText(message, 'plain'))
            
            # Connect to SMTP server
            server = smtplib.SMTP(
                self.email_config.get('smtp_server', 'smtp.example.com'), 
                self.email_config.get('smtp_port', 587)
            )
            
            server.starttls()
            server.login(
                self.email_config.get('username', ''), 
                self.email_config.get('password', '')
            )
            
            # Send email
            server.send_message(msg)
            server.quit()
            
            logger.info(f"Email notification sent: {subject}")
            
        except Exception as e:
            logger.error(f"Failed to send email notification: {str(e)}")
            
    def monitor_resources(self):
        """
        Monitor system resources.
        """
        logger.info("Resource monitoring started")
        
        while not self.stop_monitoring:
            # Get CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Get memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            
            # Get GPU usage if available
            gpu_percent = 0
            try:
                if torch.cuda.is_available():
                    # This requires nvidia-smi and is not reliable across all systems
                    # For production, consider using pynvml or similar libraries
                    gpu_percent = torch.cuda.utilization()
            except:
                pass
                
            # Store stats
            self.resource_stats['cpu_usage'].append(cpu_percent)
            self.resource_stats['memory_usage'].append(memory_percent)
            self.resource_stats['gpu_usage'].append(gpu_percent)
            self.resource_stats['timestamps'].append(datetime.now())
            
            # Sleep for a bit
            time.sleep(5)
            
        logger.info("Resource monitoring stopped")
        
    def start_resource_monitoring(self):
        """
        Start resource monitoring thread.
        """
        if not self.resource_monitoring:
            return
            
        self.stop_monitoring = False
        self.monitoring_thread = threading.Thread(target=self.monitor_resources)
        self.monitoring_thread.daemon = True
        self.monitoring_thread.start()
        
    def stop_resource_monitoring(self):
        """
        Stop resource monitoring thread.
        """
        if self.monitoring_thread is None:
            return
            
        self.stop_monitoring = True
        self.monitoring_thread.join(timeout=2)
        
    def plot_resource_usage(self, save_path: str = None):
        """
        Plot resource usage statistics.
        
        Args:
            save_path: Path to save the plot
        """
        if not self.resource_monitoring or len(self.resource_stats['timestamps']) == 0:
            logger.warning("No resource statistics available")
            return
            
        # Convert timestamps to relative time in minutes
        start_time = self.resource_stats['timestamps'][0]
        relative_times = [(t - start_time).total_seconds() / 60.0 for t in self.resource_stats['timestamps']]
        
        # Create plot
        fig, ax = plt.subplots(figsize=(12, 6))
        
        # Plot CPU and memory usage
        ax.plot(relative_times, self.resource_stats['cpu_usage'], label='CPU Usage (%)')
        ax.plot(relative_times, self.resource_stats['memory_usage'], label='Memory Usage (%)')
        
        # Plot GPU usage if available
        if any(gpu > 0 for gpu in self.resource_stats['gpu_usage']):
            ax.plot(relative_times, self.resource_stats['gpu_usage'], label='GPU Usage (%)')
            
        # Set labels and title
        ax.set_xlabel('Time (minutes)')
        ax.set_ylabel('Usage (%)')
        ax.set_title('System Resource Usage During Training')
        ax.legend()
        ax.grid(True)
        
        # Save plot if requested
        if save_path is not None:
            plt.savefig(save_path)
            logger.info(f"Resource usage plot saved to {save_path}")
            
        plt.close(fig)
        
    def train_with_recovery(self):
        """
        Train model with error recovery.
        
        Returns:
            Training statistics
        """
        logger.info("Starting training with recovery mode")
        
        # Initialize training
        self.training_stats['start_time'] = datetime.now()
        start_time = time.time()
        
        # Start resource monitoring
        self.start_resource_monitoring()
        
        try:
            # Load data
            logger.info("Loading data...")
            data = self.data_loader()
            
            # Train model
            logger.info("Starting model training...")
            training_result = self.model_trainer.train()
            
            # Update training stats
            self.training_stats['epochs_completed'] = training_result.get('epochs_completed', 0)
            
            # Save final model
            final_checkpoint_path = os.path.join(self.checkpoint_dir, 'final_model.pt')
            self.model_trainer.save_checkpoint(final_checkpoint_path)
            logger.info(f"Final model saved to {final_checkpoint_path}")
            
            # Update training stats
            self.training_stats['checkpoints_saved'] += 1
            
        except KeyboardInterrupt:
            logger.info("Training interrupted by user")
            
            # Save intermediate model
            interrupt_checkpoint_path = os.path.join(self.checkpoint_dir, 'interrupted_model.pt')
            try:
                self.model_trainer.save_checkpoint(interrupt_checkpoint_path)
                logger.info(f"Interrupted model saved to {interrupt_checkpoint_path}")
                self.training_stats['checkpoints_saved'] += 1
            except Exception as e:
                logger.error(f"Failed to save interrupted model: {str(e)}")
                
            # Send notification
            if self.email_config is not None:
                self.send_email_notification(
                    "Training Interrupted",
                    f"Training was interrupted by user after {time.time() - start_time:.2f} seconds."
                )
                
        except Exception as e:
            error_msg = str(e)
            stack_trace = traceback.format_exc()
            logger.error(f"Error during training: {error_msg}\n{stack_trace}")
            
            # Update error stats
            self.training_stats['errors'].append({
                'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'error': error_msg,
                'traceback': stack_trace
            })
            self.training_stats['error_count'] += 1
            
            # Try to save model if recovery mode is enabled
            if self.recovery_mode:
                error_checkpoint_path = os.path.join(self.checkpoint_dir, 'error_model.pt')
                try:
                    self.model_trainer.save_checkpoint(error_checkpoint_path)
                    logger.info(f"Model state at error saved to {error_checkpoint_path}")
                    self.training_stats['checkpoints_saved'] += 1
                    self.training_stats['recovered_count'] += 1
                except Exception as recovery_error:
                    logger.error(f"Failed to save model at error: {str(recovery_error)}")
                    
            # Send notification
            if self.email_config is not None:
                self.send_email_notification(
                    "Training Error",
                    f"Error during training: {error_msg}\n\nStack trace:\n{stack_trace}"
                )
                
        finally:
            # Stop resource monitoring
            self.stop_resource_monitoring()
            
            # Update training stats
            self.training_stats['end_time'] = datetime.now()
            self.training_stats['duration'] = time.time() - start_time
            
            # Generate resource usage plot
            if self.resource_monitoring:
                resource_plot_path = os.path.join(self.log_dir, 'resource_usage.png')
                self.plot_resource_usage(resource_plot_path)
                
            # Save training stats
            stats_path = os.path.join(self.log_dir, 'training_stats.json')
            with open(stats_path, 'w') as f:
                # Convert datetime objects to strings
                stats_copy = self.training_stats.copy()
                stats_copy['start_time'] = stats_copy['start_time'].strftime('%Y-%m-%d %H:%M:%S') if stats_copy['start_time'] else None
                stats_copy['end_time'] = stats_copy['end_time'].strftime('%Y-%m-%d %H:%M:%S') if stats_copy['end_time'] else None
                json.dump(stats_copy, f, indent=4)
                
            logger.info(f"Training stats saved to {stats_path}")
            
            # Send completion notification
            if self.email_config is not None:
                duration_str = str(timedelta(seconds=int(self.training_stats['duration'])))
                self.send_email_notification(
                    "Training Completed",
                    f"Training completed in {duration_str}.\n"
                    f"Epochs completed: {self.training_stats['epochs_completed']}\n"
                    f"Errors: {self.training_stats['error_count']}\n"
                    f"Recovered: {self.training_stats['recovered_count']}\n"
                    f"Checkpoints saved: {self.training_stats['checkpoints_saved']}"
                )
                
        return self.training_stats
        
    def schedule_training(self, 
                         schedule_time: str = None,
                         recurring: bool = False,
                         interval_hours: int = 24):
        """
        Schedule training for a future time.
        
        Args:
            schedule_time: Time to start training (format: 'HH:MM')
            recurring: Whether to repeat training at intervals
            interval_hours: Interval between recurring training in hours
        """
        if schedule_time is None:
            # Start immediately
            logger.info("Starting training immediately")
            return self.train_with_recovery()
            
        # Parse schedule time
        try:
            hour, minute = map(int, schedule_time.split(':'))
            now = datetime.now()
            schedule_datetime = now.replace(hour=hour, minute=minute, second=0, microsecond=0)
            
            # If scheduled time is in the past, move to next day
            if schedule_datetime < now:
                schedule_datetime += timedelta(days=1)
                
            # Calculate wait time
            wait_seconds = (schedule_datetime - now).total_seconds()
            
            logger.info(f"Training scheduled for {schedule_datetime.strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info(f"Waiting for {wait_seconds:.2f} seconds")
            
            # Wait until scheduled time
            time.sleep(wait_seconds)
            
            # Start training
            training_stats = self.train_with_recovery()
            
            # Handle recurring training
            if recurring:
                logger.info(f"Setting up recurring training every {interval_hours} hours")
                
                while True:
                    # Wait for next interval
                    logger.info(f"Waiting {interval_hours} hours until next training run")
                    time.sleep(interval_hours * 3600)
                    
                    # Start next training run
                    logger.info("Starting recurring training run")
                    training_stats = self.train_with_recovery()
                    
            return training_stats
            
        except Exception as e:
            logger.error(f"Error in scheduling training: {str(e)}")
            return None

class TrainingMonitor:
    """
    Monitor for training progress and resource usage.
    """
    
    def __init__(self, 
                checkpoint_dir: str,
                log_dir: str,
                notification_threshold: Dict = None):
        """
        Initialize the TrainingMonitor.
        
        Args:
            checkpoint_dir: Directory with model checkpoints
            log_dir: Directory with training logs
            notification_threshold: Thresholds for notifications
                e.g. {'cpu_usage': 90, 'memory_usage': 90, 'gpu_usage': 90}
        """
        self.checkpoint_dir = checkpoint_dir
        self.log_dir = log_dir
        self.notification_threshold = notification_threshold or {
            'cpu_usage': 90,
            'memory_usage': 90,
            'gpu_usage': 95
        }
        
    def check_resource_usage(self):
        """
        Check current resource usage.
        
        Returns:
            Dictionary with resource usage and warning flags
        """
        # Get CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # Get memory usage
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        
        # Get GPU usage if available
        gpu_percent = 0
        try:
            if torch.cuda.is_available():
                gpu_percent = torch.cuda.utilization()
        except:
            pass
            
        # Check thresholds
        cpu_warning = cpu_percent > self.notification_threshold.get('cpu_usage', 90)
        memory_warning = memory_percent > self.notification_threshold.get('memory_usage', 90)
        gpu_warning = gpu_percent > self.notification_threshold.get('gpu_usage', 95)
        
        return {
            'timestamp': datetime.now(),
            'cpu_usage': cpu_percent,
            'memory_usage': memory_percent,
            'gpu_usage': gpu_percent,
            'cpu_warning': cpu_warning,
            'memory_warning': memory_warning,
            'gpu_warning': gpu_warning,
            'any_warning': cpu_warning or memory_warning or gpu_warning
        }
        
    def get_training_progress(self):
        """
        Get training progress from logs.
        
        Returns:
            Dictionary with training progress information
        """
        # Find most recent log file
        log_files = [f for f in os.listdir(self.log_dir) if f.endswith('.log')]
        if not log_files:
            return {'status': 'No log files found'}
            
        latest_log = max(log_files, key=lambda f: os.path.getctime(os.path.join(self.log_dir, f)))
        log_path = os.path.join(self.log_dir, latest_log)
        
        # Parse log file for progress
        progress = {
            'log_file': latest_log,
            'last_update': datetime.fromtimestamp(os.path.getmtime(log_path)),
            'errors': 0,
            'warnings': 0,
            'status': 'Running'
        }
        
        try:
            with open(log_path, 'r') as f:
                lines = f.readlines()
                
                # Count errors and warnings
                for line in lines:
                    if ' ERROR ' in line:
                        progress['errors'] += 1
                    elif ' WARNING ' in line:
                        progress['warnings'] += 1
                        
                # Check last few lines for status
                last_lines = lines[-10:]
                for line in reversed(last_lines):
                    if 'Training completed' in line:
                        progress['status'] = 'Completed'
                        break
                    elif 'Training interrupted' in line:
                        progress['status'] = 'Interrupted'
                        break
                    elif 'Error during training' in line:
                        progress['status'] = 'Error'
                        break
                        
        except Exception as e:
            progress['status'] = f'Error reading log: {str(e)}'
            
        return progress
        
    def get_checkpoints_info(self):
        """
        Get information about saved checkpoints.
        
        Returns:
            Dictionary with checkpoint information
        """
        # List checkpoint files
        checkpoint_files = [f for f in os.listdir(self.checkpoint_dir) if f.endswith('.pt')]
        if not checkpoint_files:
            return {'status': 'No checkpoints found'}
            
        # Get file info
        checkpoints = []
        for filename in checkpoint_files:
            file_path = os.path.join(self.checkpoint_dir, filename)
            checkpoints.append({
                'filename': filename,
                'size_mb': os.path.getsize(file_path) / (1024 * 1024),
                'last_modified': datetime.fromtimestamp(os.path.getmtime(file_path))
            })
            
        # Sort by last modified
        checkpoints.sort(key=lambda x: x['last_modified'], reverse=True)
        
        return {
            'count': len(checkpoints),
            'latest': checkpoints[0]['filename'] if checkpoints else None,
            'latest_time': checkpoints[0]['last_modified'] if checkpoints else None,
            'checkpoints': checkpoints
        }
        
    def generate_status_report(self):
        """
        Generate a comprehensive status report.
        
        Returns:
            Dictionary with status report
        """
        # Get current time
        now = datetime.now()
        
        # Get resource usage
        resources = self.check_resource_usage()
        
        # Get training progress
        progress = self.get_training_progress()
        
        # Get checkpoints info
        checkpoints = self.get_checkpoints_info()
        
        # Compile report
        report = {
            'timestamp': now,
            'resources': resources,
            'training': progress,
            'checkpoints': checkpoints,
            'status': progress['status'],
            'warnings': resources['any_warning'] or progress['warnings'] > 0
        }
        
        return report

def configure_email_notifications(smtp_server: str, 
                                 smtp_port: int,
                                 username: str,
                                 password: str,
                                 sender: str,
                                 recipient: str) -> Dict:
    """
    Configure email notifications.
    
    Args:
        smtp_server: SMTP server address
        smtp_port: SMTP server port
        username: SMTP username
        password: SMTP password
        sender: Sender email address
        recipient: Recipient email address
        
    Returns:
        Email configuration dictionary
    """
    return {
        'smtp_server': smtp_server,
        'smtp_port': smtp_port,
        'username': username,
        'password': password,
        'sender': sender,
        'recipient': recipient
    } 