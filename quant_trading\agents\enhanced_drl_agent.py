"""
增强DRL智能体模块
实现高级DRL训练策略，包括多种算法、自适应学习率、早停和超参数优化
符合顶尖量化基金的最佳实践
"""

import os
import logging
import time
import datetime
import numpy as np
import pandas as pd
import gymnasium as gym
from gymnasium.utils.env_checker import check_env
from stable_baselines3 import PPO, A2C, DQN, SAC, TD3, DDPG
from stable_baselines3.common.callbacks import BaseCallback, EvalCallback, CallbackList, StopTrainingOnRewardThreshold
from stable_baselines3.common.monitor import Monitor
from stable_baselines3.common.vec_env import DummyVecEnv, VecNormalize, SubprocVecEnv
from stable_baselines3.common.noise import NormalActionNoise, OrnsteinUhlenbeckActionNoise
from stable_baselines3.common.evaluation import evaluate_policy
import torch
import optuna
from optuna.pruners import MedianPruner
from optuna.samplers import TPESampler

# 尝试导入streamlit，用于UI交互
try:
    import streamlit as st
    STREAMLIT_AVAILABLE = True
except ImportError:
    STREAMLIT_AVAILABLE = False
    print("警告: Streamlit模块不可用，UI交互功能将被禁用")

from quant_trading.trading.trading_environment import TradingEnvironment
from quant_trading.utils.common import is_gpu_available, get_gpu_info
try:
    from quant_trading.agents.ensemble_learning import EnsembleModel
    ENSEMBLE_AVAILABLE = True
except ImportError:
    ENSEMBLE_AVAILABLE = False
    print("警告: 集成学习模块不可用，请确保ensemble_learning.py文件存在")

class MetricsCallback(BaseCallback):
    """
    自定义回调，用于记录训练过程中的指标
    """

    def __init__(self, agent, verbose=0):
        super(MetricsCallback, self).__init__(verbose)
        self.agent = agent
        self.training_stats = agent.training_stats

    def _on_step(self):
        # 记录当前步数
        self.training_stats['steps'].append(self.num_timesteps)

        # 记录当前奖励
        if len(self.model.ep_info_buffer) > 0:
            self.training_stats['rewards'].append(
                np.mean([ep_info['r'] for ep_info in self.model.ep_info_buffer])
            )
            self.training_stats['episodes'].append(
                len(self.training_stats['rewards'])
            )

        # 记录当前学习率
        if hasattr(self.model, 'learning_rate'):
            if callable(self.model.learning_rate):
                lr = self.model.learning_rate(1.0)  # 假设进度为1.0
            else:
                lr = self.model.learning_rate
            self.training_stats['learning_rates'].append(lr)

        return True

class EnhancedDRLAgent:
    """
    增强DRL智能体类
    实现高级DRL训练策略，包括多种算法、自适应学习率、早停和超参数优化
    """

    def __init__(self, env_config, agent_config, hpo_config=None):
        """
        初始化DRL智能体

        参数:
            env_config (dict): 交易环境配置
            agent_config (dict): 智能体配置
            hpo_config (dict, optional): 超参数优化配置
        """
        self.logger = logging.getLogger('drl_trading')
        self.env_config = env_config
        self.agent_config = agent_config
        self.hpo_config = hpo_config

        # 设置模型保存目录
        self.models_dir = 'saved_models'
        os.makedirs(self.models_dir, exist_ok=True)

        # 初始化最佳模型路径
        self.best_model_path = None

        # 初始化集成模型
        self.ensemble_model = None

        # 创建训练环境
        self.env = self._create_environment()

        # 检查环境是否符合Gymnasium API
        try:
            check_env(self.env)
            self.logger.info("环境检查通过")
        except Exception as e:
            self.logger.error(f"环境检查失败: {str(e)}")
            raise

        # 创建DRL模型
        self.model = self._create_model()

        # 训练指标
        self.training_stats = {
            'rewards': [],
            'losses': [],
            'steps': [],
            'episodes': [],
            'learning_rates': []
        }

    def _create_environment(self):
        """
        创建交易环境

        返回:
            gym.Env: 交易环境实例
        """
        # 从环境配置中提取参数
        df_processed_data = self.env_config.get('df_processed_data')
        initial_capital = self.env_config.get('initial_capital', 100000)
        commission_rate = self.env_config.get('commission_rate', 0.0003)
        min_hold_days = self.env_config.get('min_hold_days', 3)
        allow_short = self.env_config.get('allow_short', False)
        max_position = self.env_config.get('max_position', 1.0)
        reward_config = self.env_config.get('reward_config', None)
        window_size = self.env_config.get('window_size', 20)

        # 创建环境
        env = TradingEnvironment(
            df_processed_data=df_processed_data,
            initial_capital=initial_capital,
            commission_rate=commission_rate,
            min_hold_days=min_hold_days,
            allow_short=allow_short,
            max_position=max_position,
            reward_config=reward_config,
            window_size=window_size
        )

        # 包装环境以记录指标
        env = Monitor(env)

        # 是否使用向量化环境
        if self.agent_config.get('use_vec_env', False):
            # 创建向量化环境
            env = DummyVecEnv([lambda: env])

            # 是否使用归一化环境
            if self.agent_config.get('use_vec_normalize', False):
                env = VecNormalize(
                    env,
                    norm_obs=True,
                    norm_reward=True,
                    clip_obs=10.0,
                    clip_reward=10.0,
                    gamma=self.agent_config.get('gamma', 0.99),
                    epsilon=1e-8
                )

        return env

    def _create_model(self):
        """
        创建DRL模型

        返回:
            stable_baselines3.BaseAlgorithm: DRL模型实例
        """
        # 从智能体配置中提取参数
        algorithm = self.agent_config.get('algorithm', 'PPO')
        policy = self.agent_config.get('policy_network', 'MlpPolicy')
        learning_rate = self.agent_config.get('learning_rate', 0.0003)
        gamma = self.agent_config.get('gamma', 0.99)

        # 检查是否有GPU可用
        device = 'cuda' if is_gpu_available() and self.agent_config.get('use_gpu', True) else 'cpu'
        self.logger.info(f"使用设备: {device}")

        # 记录当前算法
        self.logger.info(f"创建模型，算法: {algorithm}")

        # 创建策略关键字参数
        policy_kwargs = {}

        # 如果配置了网络结构
        if 'net_arch' in self.agent_config:
            # 确保网络结构格式与算法匹配
            if algorithm in ['PPO', 'A2C']:
                # 对于PPO和A2C，net_arch应该是字典格式 {'pi': [...], 'vf': [...]}
                if isinstance(self.agent_config['net_arch'], dict):
                    # 已经是字典格式，直接使用
                    policy_kwargs['net_arch'] = self.agent_config['net_arch']
                elif isinstance(self.agent_config['net_arch'], list):
                    # 如果是列表格式，转换为字典格式
                    policy_kwargs['net_arch'] = {'pi': self.agent_config['net_arch'], 'vf': self.agent_config['net_arch']}
                else:
                    # 默认网络结构
                    policy_kwargs['net_arch'] = {'pi': [64, 64], 'vf': [64, 64]}
            else:
                # 对于DQN、SAC等，net_arch应该是列表格式 [64, 64]
                if isinstance(self.agent_config['net_arch'], list):
                    # 已经是列表格式，直接使用
                    policy_kwargs['net_arch'] = self.agent_config['net_arch']
                elif isinstance(self.agent_config['net_arch'], dict) and 'pi' in self.agent_config['net_arch']:
                    # 如果是字典格式，转换为列表格式（使用策略网络的结构）
                    policy_kwargs['net_arch'] = self.agent_config['net_arch']['pi']
                else:
                    # 默认网络结构
                    policy_kwargs['net_arch'] = [64, 64]
        else:
            # 默认网络结构
            if algorithm in ['PPO', 'A2C']:
                policy_kwargs['net_arch'] = {'pi': [64, 64], 'vf': [64, 64]}
            else:
                policy_kwargs['net_arch'] = [64, 64]

        # 如果配置了激活函数
        if 'activation_fn' in self.agent_config:
            activation = self.agent_config['activation_fn']
            if activation == 'ReLU':
                policy_kwargs['activation_fn'] = torch.nn.ReLU
            elif activation == 'Tanh':
                policy_kwargs['activation_fn'] = torch.nn.Tanh
            elif activation == 'ELU':
                policy_kwargs['activation_fn'] = torch.nn.ELU

        # 记录最终的策略关键字参数
        self.logger.info(f"策略关键字参数: {policy_kwargs}")

        # 根据算法创建模型
        if algorithm == 'PPO':
            model = PPO(
                policy=policy,
                env=self.env,
                learning_rate=learning_rate,
                gamma=gamma,
                n_steps=self.agent_config.get('n_steps', 2048),
                batch_size=self.agent_config.get('batch_size', 64),
                n_epochs=self.agent_config.get('n_epochs', 10),
                gae_lambda=self.agent_config.get('gae_lambda', 0.95),
                clip_range=self.agent_config.get('clip_range', 0.2),
                clip_range_vf=self.agent_config.get('clip_range_vf', None),
                normalize_advantage=self.agent_config.get('normalize_advantage', True),
                ent_coef=self.agent_config.get('ent_coef', 0.01),
                vf_coef=self.agent_config.get('vf_coef', 0.5),
                max_grad_norm=self.agent_config.get('max_grad_norm', 0.5),
                use_sde=self.agent_config.get('use_sde', False),
                sde_sample_freq=self.agent_config.get('sde_sample_freq', -1),
                target_kl=self.agent_config.get('target_kl', None),
                tensorboard_log=self.agent_config.get('tensorboard_log', None),
                policy_kwargs=policy_kwargs,
                verbose=1,
                seed=self.agent_config.get('seed', None),
                device=device
            )
        elif algorithm == 'A2C':
            model = A2C(
                policy=policy,
                env=self.env,
                learning_rate=learning_rate,
                gamma=gamma,
                n_steps=self.agent_config.get('n_steps', 5),
                vf_coef=self.agent_config.get('vf_coef', 0.5),
                ent_coef=self.agent_config.get('ent_coef', 0.01),
                max_grad_norm=self.agent_config.get('max_grad_norm', 0.5),
                rms_prop_eps=self.agent_config.get('rms_prop_eps', 1e-5),
                use_rms_prop=self.agent_config.get('use_rms_prop', True),
                use_sde=self.agent_config.get('use_sde', False),
                sde_sample_freq=self.agent_config.get('sde_sample_freq', -1),
                normalize_advantage=self.agent_config.get('normalize_advantage', False),
                tensorboard_log=self.agent_config.get('tensorboard_log', None),
                policy_kwargs=policy_kwargs,
                verbose=1,
                seed=self.agent_config.get('seed', None),
                device=device
            )
        elif algorithm == 'DQN':
            model = DQN(
                policy=policy,
                env=self.env,
                learning_rate=learning_rate,
                gamma=gamma,
                buffer_size=self.agent_config.get('buffer_size', 10000),
                learning_starts=self.agent_config.get('learning_starts', 1000),
                batch_size=self.agent_config.get('batch_size', 32),
                tau=self.agent_config.get('tau', 1.0),
                train_freq=self.agent_config.get('train_freq', 4),
                gradient_steps=self.agent_config.get('gradient_steps', 1),
                target_update_interval=self.agent_config.get('target_update_interval', 1000),
                exploration_fraction=self.agent_config.get('exploration_fraction', 0.1),
                exploration_initial_eps=self.agent_config.get('exploration_initial_eps', 1.0),
                exploration_final_eps=self.agent_config.get('exploration_final_eps', 0.05),
                max_grad_norm=self.agent_config.get('max_grad_norm', 10),
                tensorboard_log=self.agent_config.get('tensorboard_log', None),
                policy_kwargs=policy_kwargs,
                verbose=1,
                seed=self.agent_config.get('seed', None),
                device=device
            )
        elif algorithm == 'SAC':
            model = SAC(
                policy=policy,
                env=self.env,
                learning_rate=learning_rate,
                gamma=gamma,
                buffer_size=self.agent_config.get('buffer_size', 10000),
                learning_starts=self.agent_config.get('learning_starts', 1000),
                batch_size=self.agent_config.get('batch_size', 256),
                tau=self.agent_config.get('tau', 0.005),
                train_freq=self.agent_config.get('train_freq', 1),
                gradient_steps=self.agent_config.get('gradient_steps', 1),
                action_noise=None,  # 连续动作空间才需要
                ent_coef='auto',
                target_update_interval=self.agent_config.get('target_update_interval', 1),
                target_entropy='auto',
                use_sde=self.agent_config.get('use_sde', False),
                sde_sample_freq=self.agent_config.get('sde_sample_freq', -1),
                tensorboard_log=self.agent_config.get('tensorboard_log', None),
                policy_kwargs=policy_kwargs,
                verbose=1,
                seed=self.agent_config.get('seed', None),
                device=device
            )
        elif algorithm == 'TD3':
            # 创建动作噪声对象（对于连续动作空间）
            n_actions = self.env.action_space.shape[0] if hasattr(self.env.action_space, 'shape') else 1
            action_noise = None

            # 如果配置了噪声类型
            noise_type = self.agent_config.get('noise_type', 'normal')
            noise_std = self.agent_config.get('noise_std', 0.1)

            if noise_type == 'normal':
                action_noise = NormalActionNoise(
                    mean=np.zeros(n_actions),
                    sigma=noise_std * np.ones(n_actions)
                )
            elif noise_type == 'ornstein_uhlenbeck':
                action_noise = OrnsteinUhlenbeckActionNoise(
                    mean=np.zeros(n_actions),
                    sigma=noise_std * np.ones(n_actions)
                )

            model = TD3(
                policy=policy,
                env=self.env,
                learning_rate=learning_rate,
                gamma=gamma,
                buffer_size=self.agent_config.get('buffer_size', 10000),
                learning_starts=self.agent_config.get('learning_starts', 1000),
                batch_size=self.agent_config.get('batch_size', 256),
                tau=self.agent_config.get('tau', 0.005),
                policy_delay=self.agent_config.get('policy_delay', 2),
                action_noise=action_noise,
                target_policy_noise=self.agent_config.get('target_policy_noise', 0.2),
                target_noise_clip=self.agent_config.get('target_noise_clip', 0.5),
                train_freq=self.agent_config.get('train_freq', (1, "episode")),
                gradient_steps=self.agent_config.get('gradient_steps', -1),
                tensorboard_log=self.agent_config.get('tensorboard_log', None),
                policy_kwargs=policy_kwargs,
                verbose=1,
                seed=self.agent_config.get('seed', None),
                device=device
            )
        elif algorithm == 'DDPG':
            # 创建动作噪声对象（对于连续动作空间）
            n_actions = self.env.action_space.shape[0] if hasattr(self.env.action_space, 'shape') else 1
            action_noise = None

            # 如果配置了噪声类型
            noise_type = self.agent_config.get('noise_type', 'normal')
            noise_std = self.agent_config.get('noise_std', 0.1)

            if noise_type == 'normal':
                action_noise = NormalActionNoise(
                    mean=np.zeros(n_actions),
                    sigma=noise_std * np.ones(n_actions)
                )
            elif noise_type == 'ornstein_uhlenbeck':
                action_noise = OrnsteinUhlenbeckActionNoise(
                    mean=np.zeros(n_actions),
                    sigma=noise_std * np.ones(n_actions)
                )

            # 使用DDPG算法

            model = DDPG(
                policy=policy,
                env=self.env,
                learning_rate=learning_rate,
                gamma=gamma,
                buffer_size=self.agent_config.get('buffer_size', 10000),
                learning_starts=self.agent_config.get('learning_starts', 1000),
                batch_size=self.agent_config.get('batch_size', 256),
                tau=self.agent_config.get('tau', 0.005),
                action_noise=action_noise,
                train_freq=self.agent_config.get('train_freq', (1, "episode")),
                gradient_steps=self.agent_config.get('gradient_steps', -1),
                tensorboard_log=self.agent_config.get('tensorboard_log', None),
                policy_kwargs=policy_kwargs,
                verbose=1,
                seed=self.agent_config.get('seed', None),
                device=device
            )
        else:
            raise ValueError(f"不支持的算法: {algorithm}")

        return model

    def train(self, total_timesteps, callback_list=None, hpo_trial=None, progress_bar=True):
        """
        训练DRL智能体

        参数:
            total_timesteps (int): 训练总步数
            callback_list (list, optional): 回调函数列表
            hpo_trial (optuna.Trial, optional): Optuna试验对象
            progress_bar (bool): 是否显示进度条

        返回:
            dict: 训练结果统计信息
        """
        start_time = time.time()
        self.logger.info(f"开始训练，总步数: {total_timesteps}")

        # 检查是否使用集成学习
        ensemble_config = self.agent_config.get('ensemble', {})
        use_ensemble = ensemble_config.get('use', False)
        # 确保use_ensemble是布尔值
        use_ensemble = bool(use_ensemble)
        self.logger.info(f"集成学习状态: {'启用' if use_ensemble else '禁用'}")

        n_models = ensemble_config.get('n_models', 3)
        voting_method = ensemble_config.get('voting_method', 'majority')
        # 检查是否使用多算法集成学习
        use_multi_algorithm = ensemble_config.get('use_multi_algorithm', False)

        # 如果使用集成学习，但集成学习模块不可用，则发出警告并禁用集成学习
        if use_ensemble and not ENSEMBLE_AVAILABLE:
            self.logger.warning("集成学习模块不可用，将禁用集成学习")
            use_ensemble = False

        # 如果使用集成学习，创建多个模型
        if use_ensemble:
            if use_multi_algorithm:
                self.logger.info(f"使用多算法集成学习，模型数量: {n_models}, 投票方法: {voting_method}")
            else:
                self.logger.info(f"使用单算法集成学习，模型数量: {n_models}, 投票方法: {voting_method}")

            ensemble_models = []
            ensemble_stats = []

            # 保存原始模型
            original_model = self.model
            original_algorithm = self.agent_config.get('algorithm', 'PPO')

            # 定义要使用的算法列表
            # 检查是否有用户选择的算法列表
            if 'selected_algorithms' in self.agent_config.get('ensemble', {}) and self.agent_config['ensemble']['selected_algorithms']:
                # 使用用户选择的算法列表
                algorithms = self.agent_config['ensemble']['selected_algorithms']
                self.logger.info(f"使用用户选择的算法列表: {algorithms}")
            else:
                # 如果使用多算法集成学习但没有指定算法列表或列表为空
                if use_multi_algorithm:
                    # 使用默认的多种算法进行集成学习
                    algorithms = ['PPO', 'A2C', 'DQN']
                    self.logger.info(f"使用默认算法列表: {algorithms}")
                else:
                    # 使用单一算法进行集成学习
                    # 检查是否在集成学习配置中指定了算法
                    ensemble_algorithm = self.agent_config.get('ensemble', {}).get('algorithm', original_algorithm)
                    algorithms = [ensemble_algorithm] * n_models
                    self.logger.info(f"使用单一算法 {ensemble_algorithm} 进行集成学习")

            # 确保算法数量不超过模型数量
            if len(algorithms) > n_models:
                algorithms = algorithms[:n_models]
                self.logger.info(f"算法数量超过模型数量，截取为: {algorithms}")

            # 确保算法列表不为空
            if not algorithms:
                self.logger.warning("算法列表为空，使用默认算法 'PPO'")
                algorithms = ['PPO']

            # 如果算法数量少于模型数量，重复使用算法
            # 无论是否使用多算法集成学习，都确保算法列表长度等于模型数量
            if len(algorithms) < n_models:
                original_algorithms = algorithms.copy()
                while len(algorithms) < n_models:
                    next_algo = original_algorithms[len(algorithms) % len(original_algorithms)]
                    algorithms.append(next_algo)
                    self.logger.info(f"添加重复算法 {next_algo}，当前算法列表: {algorithms}")

            # 训练多个模型
            for i in range(n_models):
                # 设置当前算法
                current_algorithm = algorithms[i]
                self.logger.info(f"训练集成模型 {i+1}/{n_models}，使用算法: {current_algorithm}")

                # 为每个模型设置不同的随机种子
                seed = self.agent_config.get('seed', 0) + i

                # 创建模型配置副本并更新算法和种子
                model_config = self.agent_config.copy()
                model_config['algorithm'] = current_algorithm
                model_config['seed'] = seed

                # 确保网络结构与当前算法匹配
                if 'net_arch' in model_config:
                    # 根据算法调整网络结构格式
                    if current_algorithm in ['PPO', 'A2C']:
                        # 对于PPO和A2C，确保net_arch是字典格式
                        if not isinstance(model_config['net_arch'], dict):
                            if isinstance(model_config['net_arch'], list):
                                model_config['net_arch'] = {'pi': model_config['net_arch'], 'vf': model_config['net_arch']}
                            else:
                                model_config['net_arch'] = {'pi': [64, 64], 'vf': [64, 64]}
                    else:
                        # 对于DQN、SAC等，确保net_arch是列表格式
                        if not isinstance(model_config['net_arch'], list):
                            if isinstance(model_config['net_arch'], dict) and 'pi' in model_config['net_arch']:
                                model_config['net_arch'] = model_config['net_arch']['pi']
                            else:
                                model_config['net_arch'] = [64, 64]

                # 临时更新智能体配置
                self.agent_config = model_config

                # 记录当前算法和网络结构
                self.logger.info(f"集成模型 {i+1}/{n_models} 使用算法: {current_algorithm}")
                if 'net_arch' in model_config:
                    self.logger.info(f"网络结构: {model_config['net_arch']}")

                # 创建新的模型
                try:
                    self.model = self._create_model()
                except Exception as e:
                    self.logger.error(f"创建模型失败: {str(e)}")
                    raise

                # 创建回调列表
                callbacks = []

                # 如果提供了回调列表，添加到回调列表中
                if callback_list:
                    if isinstance(callback_list, list):
                        callbacks.extend(callback_list)
                    else:
                        callbacks.append(callback_list)

                # 添加自定义回调
                metrics_callback = MetricsCallback(self)
                callbacks.append(metrics_callback)

                # 添加早停回调（如果配置了）
                if self.agent_config.get('early_stopping', {}).get('use', False):
                    reward_threshold = self.agent_config.get('early_stopping', {}).get('reward_threshold', 0.0)
                    patience = self.agent_config.get('early_stopping', {}).get('patience', 10)

                    # 创建评估环境
                    eval_env = self._create_environment()

                    # 创建早停回调
                    stop_train_callback = StopTrainingOnRewardThreshold(reward_threshold=reward_threshold, verbose=1)
                    eval_callback = EvalCallback(
                        eval_env,
                        callback_on_new_best=stop_train_callback,
                        eval_freq=self.agent_config.get('eval_freq', 10000),
                        n_eval_episodes=self.agent_config.get('n_eval_episodes', 5),
                        verbose=1
                    )
                    callbacks.append(eval_callback)

                # 创建回调列表
                callback = CallbackList(callbacks)

                try:
                    # 训练模型
                    self.model.learn(
                        total_timesteps=total_timesteps,
                        callback=callback,
                        progress_bar=progress_bar,
                        tb_log_name=f"{current_algorithm}_ensemble_{i}",
                        reset_num_timesteps=True
                    )

                    # 添加到集成模型列表
                    ensemble_models.append(self.model)

                    # 计算训练时间
                    training_time = time.time() - start_time

                    # 获取训练统计信息
                    stats = {
                        'model_index': i,
                        'algorithm': current_algorithm,
                        'total_timesteps': total_timesteps,
                        'training_time': training_time,
                        'training_time_formatted': str(datetime.timedelta(seconds=int(training_time))),
                        'final_reward_mean': np.mean([ep_info['r'] for ep_info in self.model.ep_info_buffer]) if hasattr(self.model, 'ep_info_buffer') and self.model.ep_info_buffer else None,
                        'final_length_mean': np.mean([ep_info['l'] for ep_info in self.model.ep_info_buffer]) if hasattr(self.model, 'ep_info_buffer') and self.model.ep_info_buffer else None,
                        'rewards': self.training_stats['rewards'],
                        'steps': self.training_stats['steps'],
                        'episodes': self.training_stats['episodes']
                    }

                    ensemble_stats.append(stats)

                    self.logger.info(f"集成模型 {i+1}/{n_models} (算法: {current_algorithm}) 训练完成，耗时: {stats['training_time_formatted']}")
                    self.logger.info(f"集成模型 {i+1}/{n_models} (算法: {current_algorithm}) 最终平均奖励: {stats['final_reward_mean']}")

                except Exception as e:
                    self.logger.error(f"训练集成模型 {i+1}/{n_models} (算法: {current_algorithm}) 过程中发生错误: {str(e)}")
                    # 继续训练下一个模型，而不是中断整个过程
                    continue

            # 恢复原始配置
            self.agent_config = self.agent_config.copy()
            self.agent_config['algorithm'] = original_algorithm

            # 创建集成模型
            if len(ensemble_models) > 0:
                # 计算模型权重（基于最终奖励）
                if voting_method == 'weighted':
                    rewards = [stats['final_reward_mean'] for stats in ensemble_stats]
                    # 将奖励转换为非负数
                    min_reward = min(rewards)
                    if min_reward < 0:
                        rewards = [r - min_reward + 1.0 for r in rewards]
                    weights = rewards
                else:
                    weights = None

                # 创建集成模型
                self.ensemble_model = EnsembleModel(
                    models=ensemble_models,
                    voting_method=voting_method,
                    weights=weights
                )

                # 恢复原始模型（用于保存）
                self.model = original_model

                # 保存集成模型
                algorithm = self.agent_config.get('algorithm', 'PPO')
                timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')

                # 构建保存路径
                model_filename = f"{algorithm}_ENSEMBLE_{timestamp}.zip"
                model_path = os.path.join(self.models_dir, model_filename)

                # 保存主模型
                self.model.save(model_path)
                self.logger.info(f"集成学习主模型已保存到: {model_path}")

                # 保存集成模型
                ensemble_dir = model_path.replace('.zip', '_ensemble')
                os.makedirs(ensemble_dir, exist_ok=True)
                self.ensemble_model.save(ensemble_dir)
                self.logger.info(f"集成模型已保存到: {ensemble_dir}")

                # 更新最佳模型路径
                self.best_model_path = model_path

                # 如果使用了VecNormalize，也保存归一化参数
                if isinstance(self.env, VecNormalize):
                    norm_path = model_path.replace('.zip', '_vecnormalize.pkl')
                    self.env.save(norm_path)
                    self.logger.info(f"环境归一化参数已保存到: {norm_path}")

                # 返回集成统计信息
                ensemble_stats_combined = {
                    'algorithm': self.agent_config.get('algorithm'),
                    'ensemble': True,
                    'n_models': len(ensemble_models),
                    'voting_method': voting_method,
                    'total_timesteps': total_timesteps * len(ensemble_models),
                    'training_time': time.time() - start_time,
                    'training_time_formatted': str(datetime.timedelta(seconds=int(time.time() - start_time))),
                    'model_stats': ensemble_stats,
                    'model_path': model_path,
                    'ensemble_dir': ensemble_dir
                }

                self.logger.info(f"集成学习训练完成，共 {len(ensemble_models)}/{n_models} 个模型")

                return ensemble_stats_combined
            else:
                self.logger.error("所有集成模型训练失败，将使用单个模型")
                use_ensemble = False

        # 如果不使用集成学习或集成学习失败，使用单个模型
        if not use_ensemble:
            self.logger.info("使用单一模型训练，算法: " + self.agent_config.get('algorithm', 'PPO'))

            # 创建回调列表
            callbacks = []

            # 如果提供了回调列表，添加到回调列表中
            if callback_list:
                if isinstance(callback_list, list):
                    callbacks.extend(callback_list)
                else:
                    callbacks.append(callback_list)

            # 添加自定义回调
            metrics_callback = MetricsCallback(self)
            callbacks.append(metrics_callback)

            # 添加早停回调（如果配置了）
            if self.agent_config.get('early_stopping', {}).get('use', False):
                reward_threshold = self.agent_config.get('early_stopping', {}).get('reward_threshold', 0.0)
                patience = self.agent_config.get('early_stopping', {}).get('patience', 10)

                # 创建评估环境
                eval_env = self._create_environment()

                # 创建早停回调
                stop_train_callback = StopTrainingOnRewardThreshold(reward_threshold=reward_threshold, verbose=1)
                eval_callback = EvalCallback(
                    eval_env,
                    callback_on_new_best=stop_train_callback,
                    eval_freq=self.agent_config.get('eval_freq', 10000),
                    n_eval_episodes=self.agent_config.get('n_eval_episodes', 5),
                    verbose=1
                )
                callbacks.append(eval_callback)

            # 创建回调列表
            callback = CallbackList(callbacks)

            try:
                # 确保模型已创建
                if self.model is None:
                    self.logger.info("模型未创建，正在创建模型...")
                    self.model = self._create_model()

                self.logger.info(f"开始训练单一模型，算法: {self.agent_config.get('algorithm', 'PPO')}, 总步数: {total_timesteps}")

                # 训练模型
                self.model.learn(
                    total_timesteps=total_timesteps,
                    callback=callback,
                    progress_bar=progress_bar,
                    tb_log_name=self.agent_config.get('algorithm', 'PPO'),
                    reset_num_timesteps=True
                )

                # 计算训练时间
                training_time = time.time() - start_time

                # 获取训练统计信息
                stats = {
                    'algorithm': self.agent_config.get('algorithm'),
                    'ensemble': False,
                    'total_timesteps': total_timesteps,
                    'training_time': training_time,
                    'training_time_formatted': str(datetime.timedelta(seconds=int(training_time))),
                    'final_reward_mean': np.mean([ep_info['r'] for ep_info in self.model.ep_info_buffer]) if hasattr(self.model, 'ep_info_buffer') and self.model.ep_info_buffer else None,
                    'final_length_mean': np.mean([ep_info['l'] for ep_info in self.model.ep_info_buffer]) if hasattr(self.model, 'ep_info_buffer') and self.model.ep_info_buffer else None,
                    'rewards': self.training_stats['rewards'],
                    'steps': self.training_stats['steps'],
                    'episodes': self.training_stats['episodes']
                }

                self.logger.info(f"训练完成，耗时: {stats['training_time_formatted']}")
                self.logger.info(f"最终平均奖励: {stats['final_reward_mean']}")

                # 保存模型
                model_path = os.path.join(self.models_dir, f"{self.agent_config['algorithm']}_{int(time.time())}.zip")
                self.model.save(model_path)
                self.logger.info(f"模型已保存到: {model_path}")

                # 更新最佳模型路径
                self.best_model_path = model_path

                return stats

            except Exception as e:
                self.logger.error(f"训练单一模型过程中发生错误: {str(e)}", exc_info=True)
                raise

    def predict_action(self, observation, deterministic=True):
        """
        预测动作

        参数:
            observation (numpy.ndarray): 观测值
            deterministic (bool): 是否使用确定性策略

        返回:
            int: 预测的动作（Python标量，非NumPy数组）
        """
        # 检查是否使用集成模型
        if hasattr(self, 'ensemble_model') and self.ensemble_model is not None:
            self.logger.debug("使用集成模型进行预测")
            action, _ = self.ensemble_model.predict(observation, deterministic=deterministic)
        else:
            action, _ = self.model.predict(observation, deterministic=deterministic)

        # 确保返回的是Python标量而不是NumPy数组
        if isinstance(action, np.ndarray):
            try:
                # 尝试将NumPy数组转换为Python标量
                action_scalar = action.item()
                self.logger.debug(f"将NumPy数组动作 {action} 转换为标量 {action_scalar}")
                return action_scalar
            except (ValueError, TypeError) as e:
                # 如果转换失败（例如，数组包含多个元素），记录错误并返回第一个元素
                self.logger.warning(f"无法将动作数组转换为标量: {str(e)}，使用第一个元素")
                return int(action[0]) if len(action) > 0 else 0

        return action

    def save_model(self, save_path=None, stock_code=None, performance_metrics=None, clean_old_models=False):
        """
        保存模型

        参数:
            save_path (str, optional): 保存路径
            stock_code (str, optional): 股票代码，用于文件名
            performance_metrics (dict, optional): 性能指标，用于文件名
            clean_old_models (bool): 是否清理本次训练中的非最佳模型

        返回:
            str: 模型保存路径
        """
        # 检查是否使用集成模型
        use_ensemble = hasattr(self, 'ensemble_model') and self.ensemble_model is not None

        if save_path is None:
            # 创建保存目录
            os.makedirs('saved_models', exist_ok=True)

            # 生成文件名
            algorithm = self.agent_config.get('algorithm', 'PPO')
            timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')

            # 添加股票代码（如果有）
            stock_code_str = f"_{stock_code}" if stock_code else ""

            # 添加性能指标（如果有）
            perf_str = ""
            if performance_metrics:
                if 'return' in performance_metrics:
                    perf_str = f"_R{performance_metrics['return']:.4f}"
                if 'sharpe_ratio' in performance_metrics:
                    perf_str += f"_S{performance_metrics['sharpe_ratio']:.2f}"

            # 添加集成标记（如果使用集成学习）
            ensemble_str = "_ENSEMBLE" if use_ensemble else ""

            save_path = f"saved_models/{algorithm}{stock_code_str}{ensemble_str}_{timestamp}{perf_str}.zip"

        if use_ensemble:
            # 为集成模型创建目录
            ensemble_dir = save_path.replace('.zip', '_ensemble')
            os.makedirs(ensemble_dir, exist_ok=True)

            # 保存集成模型
            self.ensemble_model.save(ensemble_dir)
            self.logger.info(f"集成模型已保存到: {ensemble_dir}")

            # 保存主模型（用于兼容性）
            self.model.save(save_path)
            self.logger.info(f"主模型已保存到: {save_path}")
        else:
            # 保存单个模型
            self.model.save(save_path)
            self.logger.info(f"模型已保存到: {save_path}")

        # 如果使用了VecNormalize，也保存归一化参数
        if isinstance(self.env, VecNormalize):
            norm_path = save_path.replace('.zip', '_vecnormalize.pkl')
            self.env.save(norm_path)
            self.logger.info(f"环境归一化参数已保存到: {norm_path}")

        # 如果需要清理旧模型
        if clean_old_models and performance_metrics and 'return' in performance_metrics:
            # 获取当前时间戳（用于识别本次训练的模型）
            current_timestamp = timestamp

            # 查找同一时间戳的所有模型
            models_dir = "saved_models"
            if os.path.exists(models_dir):
                all_model_files = [f for f in os.listdir(models_dir) if f.endswith(".zip") and current_timestamp in f]

                # 找出最佳模型（假设文件名中包含性能指标）
                best_model = None
                best_return = float('-inf')

                for model_file in all_model_files:
                    # 检查是否已经是最佳模型
                    if "BEST" in model_file:
                        continue

                    # 尝试从文件名中提取性能指标
                    import re
                    return_match = re.search(r'_R([\d\.]+)', model_file)
                    if return_match:
                        model_return = float(return_match.group(1))
                        if model_return > best_return:
                            best_return = model_return
                            best_model = model_file

                # 如果找到了最佳模型，并且当前模型是最佳模型
                if best_model and os.path.basename(save_path) == best_model:
                    # 创建最佳模型的副本
                    best_model_path = os.path.join(models_dir, best_model.replace('.zip', '_BEST.zip'))
                    import shutil
                    shutil.copy2(save_path, best_model_path)
                    self.logger.info(f"创建最佳模型副本: {best_model_path}")

                    # 如果使用了VecNormalize，也复制归一化参数
                    if isinstance(self.env, VecNormalize):
                        norm_path = save_path.replace('.zip', '_vecnormalize.pkl')
                        best_norm_path = best_model_path.replace('.zip', '_vecnormalize.pkl')
                        if os.path.exists(norm_path):
                            shutil.copy2(norm_path, best_norm_path)
                            self.logger.info(f"创建最佳模型归一化参数副本: {best_norm_path}")

                    # 如果是集成学习模型，也复制集成目录
                    if use_ensemble:
                        ensemble_dir = save_path.replace('.zip', '_ensemble')
                        best_ensemble_dir = best_model_path.replace('.zip', '_ensemble')
                        if os.path.exists(ensemble_dir) and os.path.isdir(ensemble_dir):
                            # 如果目标目录已存在，先删除
                            if os.path.exists(best_ensemble_dir):
                                shutil.rmtree(best_ensemble_dir)
                            # 复制整个目录
                            shutil.copytree(ensemble_dir, best_ensemble_dir)
                            self.logger.info(f"创建最佳模型集成目录副本: {best_ensemble_dir}")

                    # 清理非最佳模型
                    self.logger.info("清理本次训练中的非最佳模型，只保留最佳模型")
                    non_best_models = [f for f in all_model_files if "BEST" not in f]
                    for model_file in non_best_models:
                        model_path = os.path.join(models_dir, model_file)
                        # 删除模型文件
                        os.remove(model_path)
                        self.logger.info(f"已删除非最佳模型: {model_file}")

                        # 删除对应的VecNormalize参数文件（如果有）
                        vec_normalize_path = model_path.replace('.zip', '_vecnormalize.pkl')
                        if os.path.exists(vec_normalize_path):
                            os.remove(vec_normalize_path)
                            self.logger.info(f"已删除非最佳模型的归一化参数: {os.path.basename(vec_normalize_path)}")

                        # 删除对应的集成目录（如果有）
                        ensemble_dir = model_path.replace('.zip', '_ensemble')
                        if os.path.exists(ensemble_dir) and os.path.isdir(ensemble_dir):
                            shutil.rmtree(ensemble_dir)
                            self.logger.info(f"已删除非最佳模型的集成目录: {os.path.basename(ensemble_dir)}")

                    self.logger.info("模型清理完成，只保留了最佳模型")

        return save_path

    def load_model(self, load_path, load_vec_normalize=None):
        """
        加载模型

        参数:
            load_path (str): 模型路径
            load_vec_normalize (str, optional): VecNormalize参数路径

        返回:
            stable_baselines3.BaseAlgorithm: 加载的模型
        """
        # 从智能体配置中提取参数
        algorithm = self.agent_config.get('algorithm', 'PPO')

        # 检查是否有GPU可用
        device = 'cuda' if is_gpu_available() and self.agent_config.get('use_gpu', True) else 'cpu'

        # 检查是否是集成模型
        ensemble_dir = load_path.replace('.zip', '_ensemble')
        is_ensemble = os.path.exists(ensemble_dir) and os.path.isdir(ensemble_dir) and ENSEMBLE_AVAILABLE

        if is_ensemble:
            try:
                # 加载集成模型
                self.ensemble_model = EnsembleModel.load(ensemble_dir)
                self.logger.info(f"集成模型已从 {ensemble_dir} 加载")

                # 同时加载主模型（用于兼容性）
                if algorithm == 'PPO':
                    self.model = PPO.load(load_path, env=self.env, device=device)
                elif algorithm == 'A2C':
                    self.model = A2C.load(load_path, env=self.env, device=device)
                elif algorithm == 'DQN':
                    self.model = DQN.load(load_path, env=self.env, device=device)
                elif algorithm == 'SAC':
                    self.model = SAC.load(load_path, env=self.env, device=device)
                else:
                    raise ValueError(f"不支持的算法: {algorithm}")

                self.logger.info(f"主模型已从 {load_path} 加载")
            except Exception as e:
                self.logger.error(f"加载集成模型失败: {str(e)}，将尝试加载单个模型")
                is_ensemble = False

        if not is_ensemble:
            # 根据算法加载模型
            if algorithm == 'PPO':
                self.model = PPO.load(load_path, env=self.env, device=device)
            elif algorithm == 'A2C':
                self.model = A2C.load(load_path, env=self.env, device=device)
            elif algorithm == 'DQN':
                self.model = DQN.load(load_path, env=self.env, device=device)
            elif algorithm == 'SAC':
                self.model = SAC.load(load_path, env=self.env, device=device)
            else:
                raise ValueError(f"不支持的算法: {algorithm}")

            self.logger.info(f"模型已从 {load_path} 加载")

        # 如果提供了VecNormalize参数路径，加载归一化参数
        if load_vec_normalize and isinstance(self.env, VecNormalize):
            self.env = VecNormalize.load(load_vec_normalize, self.env)
            self.logger.info(f"环境归一化参数已从 {load_vec_normalize} 加载")

        return self.model

    def optimize_hyperparameters(self, n_trials=50, n_startup_trials=10, n_evaluations=2, n_timesteps=50000, callback=None):
        """
        使用Optuna优化超参数

        参数:
            n_trials (int): 试验次数
            n_startup_trials (int): 启动试验次数
            n_evaluations (int): 每次试验的评估次数
            n_timesteps (int): 每次评估的时间步数
            callback (callable, optional): 每次试验后调用的回调函数

        返回:
            dict: 最佳超参数
        """
        if self.hpo_config is None:
            self.logger.error("未提供超参数优化配置")
            raise ValueError("未提供超参数优化配置")

        # 创建Optuna研究
        study_name = f"{self.agent_config.get('algorithm', 'PPO')}_optimization"
        study = optuna.create_study(
            study_name=study_name,
            sampler=TPESampler(n_startup_trials=n_startup_trials),
            pruner=MedianPruner(n_startup_trials=n_startup_trials, n_warmup_steps=n_timesteps // 3),
            direction="maximize"
        )

        # 定义目标函数
        def objective(trial):
            # 根据算法获取超参数空间
            algorithm = self.agent_config.get('algorithm', 'PPO')
            hyperparams = self._sample_hyperparameters(trial, algorithm)

            # 更新智能体配置
            agent_config = self.agent_config.copy()
            agent_config.update(hyperparams)

            # 创建新的智能体
            agent = EnhancedDRLAgent(self.env_config, agent_config)

            # 训练并评估
            mean_reward = 0
            for _ in range(n_evaluations):
                # 训练模型
                agent.train(total_timesteps=n_timesteps, hpo_trial=trial)

                # 评估模型
                eval_env = self._create_environment()
                rewards, _ = evaluate_policy(agent.model, eval_env, n_eval_episodes=10, deterministic=True)
                mean_reward += np.mean(rewards)

            # 计算平均奖励
            mean_reward /= n_evaluations

            return mean_reward

        # 运行优化
        try:
            # 如果提供了回调函数，使用它
            if callback:
                study.optimize(objective, n_trials=n_trials, show_progress_bar=True, callbacks=[callback])
            else:
                study.optimize(objective, n_trials=n_trials, show_progress_bar=True)

            # 获取最佳超参数
            best_params = study.best_params
            best_value = study.best_value

            self.logger.info(f"超参数优化完成，最佳奖励: {best_value}")
            self.logger.info(f"最佳超参数: {best_params}")

            # 更新智能体配置
            self.agent_config.update(best_params)

            # 使用最佳超参数重新创建模型
            self.model = self._create_model()

            return best_params

        except Exception as e:
            self.logger.error(f"超参数优化过程中发生错误: {str(e)}")
            raise

    def _sample_hyperparameters(self, trial, algorithm):
        """
        采样超参数

        参数:
            trial (optuna.Trial): Optuna试验对象
            algorithm (str): 算法名称

        返回:
            dict: 采样的超参数
        """
        # 获取超参数空间
        param_space = self.hpo_config.get('param_space', {})

        # 通用超参数
        hyperparams = {
            'learning_rate': trial.suggest_float('learning_rate',
                                               param_space.get('learning_rate', {}).get('min', 1e-5),
                                               param_space.get('learning_rate', {}).get('max', 1e-3),
                                               log=True),
            'gamma': trial.suggest_float('gamma',
                                       param_space.get('gamma', {}).get('min', 0.9),
                                       param_space.get('gamma', {}).get('max', 0.9999))
        }

        # 算法特定超参数
        if algorithm == 'PPO':
            hyperparams.update({
                'n_steps': trial.suggest_int('n_steps',
                                           param_space.get('n_steps', {}).get('min', 32),
                                           param_space.get('n_steps', {}).get('max', 2048)),
                'batch_size': trial.suggest_int('batch_size',
                                              param_space.get('batch_size', {}).get('min', 32),
                                              param_space.get('batch_size', {}).get('max', 256)),
                'n_epochs': trial.suggest_int('n_epochs',
                                            param_space.get('n_epochs', {}).get('min', 5),
                                            param_space.get('n_epochs', {}).get('max', 20)),
                'gae_lambda': trial.suggest_float('gae_lambda',
                                                param_space.get('gae_lambda', {}).get('min', 0.8),
                                                param_space.get('gae_lambda', {}).get('max', 0.99)),
                'clip_range': trial.suggest_float('clip_range',
                                                param_space.get('clip_range', {}).get('min', 0.1),
                                                param_space.get('clip_range', {}).get('max', 0.3)),
                'ent_coef': trial.suggest_float('ent_coef',
                                              param_space.get('ent_coef', {}).get('min', 0.0),
                                              param_space.get('ent_coef', {}).get('max', 0.1)),
                'vf_coef': trial.suggest_float('vf_coef',
                                             param_space.get('vf_coef', {}).get('min', 0.1),
                                             param_space.get('vf_coef', {}).get('max', 0.9))
            })
        elif algorithm == 'A2C':
            hyperparams.update({
                'n_steps': trial.suggest_int('n_steps',
                                           param_space.get('n_steps', {}).get('min', 5),
                                           param_space.get('n_steps', {}).get('max', 20)),
                'ent_coef': trial.suggest_float('ent_coef',
                                              param_space.get('ent_coef', {}).get('min', 0.0),
                                              param_space.get('ent_coef', {}).get('max', 0.1)),
                'vf_coef': trial.suggest_float('vf_coef',
                                             param_space.get('vf_coef', {}).get('min', 0.1),
                                             param_space.get('vf_coef', {}).get('max', 0.9))
            })
        elif algorithm == 'DQN':
            hyperparams.update({
                'buffer_size': trial.suggest_int('buffer_size',
                                               param_space.get('buffer_size', {}).get('min', 10000),
                                               param_space.get('buffer_size', {}).get('max', 100000)),
                'learning_starts': trial.suggest_int('learning_starts',
                                                   param_space.get('learning_starts', {}).get('min', 1000),
                                                   param_space.get('learning_starts', {}).get('max', 10000)),
                'batch_size': trial.suggest_int('batch_size',
                                              param_space.get('batch_size', {}).get('min', 32),
                                              param_space.get('batch_size', {}).get('max', 256)),
                'target_update_interval': trial.suggest_int('target_update_interval',
                                                          param_space.get('target_update_interval', {}).get('min', 500),
                                                          param_space.get('target_update_interval', {}).get('max', 5000)),
                'exploration_fraction': trial.suggest_float('exploration_fraction',
                                                          param_space.get('exploration_fraction', {}).get('min', 0.05),
                                                          param_space.get('exploration_fraction', {}).get('max', 0.5))
            })

        return hyperparams
