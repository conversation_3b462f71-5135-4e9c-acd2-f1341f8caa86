#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
GPU支持自动安装脚本

此脚本用于自动检测系统GPU并安装相应的深度学习框架GPU支持。
支持PyTorch和TensorFlow。
"""

import os
import sys
import subprocess
import platform
import time
import re

def print_header(message):
    """打印带有格式的标题"""
    print("\n" + "=" * 80)
    print(f" {message}")
    print("=" * 80)

def print_step(step, message):
    """打印步骤信息"""
    print(f"\n[{step}] {message}")

def print_info(message):
    """打印信息"""
    print(f"  > {message}")

def print_warning(message):
    """打印警告信息"""
    print(f"  ! 警告: {message}")

def print_error(message):
    """打印错误信息"""
    print(f"  x 错误: {message}")

def print_success(message):
    """打印成功信息"""
    print(f"  √ {message}")

def run_command(command, shell=True):
    """运行命令并返回输出"""
    try:
        result = subprocess.run(
            command,
            shell=shell,
            check=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, e.stderr
    except Exception as e:
        return False, str(e)

def detect_gpu():
    """检测系统GPU"""
    print_step("1", "检测系统GPU...")

    # 检测操作系统
    os_name = platform.system()
    print_info(f"操作系统: {os_name}")

    gpu_detected = False
    gpu_info = {}

    # 尝试使用nvidia-smi检测NVIDIA GPU
    success, output = run_command("nvidia-smi")
    if success:
        print_success("检测到NVIDIA GPU")
        gpu_detected = True
        gpu_info['type'] = 'NVIDIA'

        # 提取驱动版本
        driver_match = re.search(r'Driver Version: (\d+\.\d+\.\d+)', output)
        if driver_match:
            gpu_info['driver_version'] = driver_match.group(1)
            print_info(f"NVIDIA驱动版本: {gpu_info['driver_version']}")

        # 提取GPU型号
        gpu_match = re.search(r'\| (NVIDIA.*?)\s+\|', output)
        if gpu_match:
            gpu_info['model'] = gpu_match.group(1)
            print_info(f"GPU型号: {gpu_info['model']}")
    else:
        # 尝试使用其他方法检测GPU
        if os_name == 'Windows':
            success, output = run_command("wmic path win32_VideoController get name")
            if success and ('nvidia' in output.lower() or 'geforce' in output.lower() or 'quadro' in output.lower()):
                print_success("检测到NVIDIA GPU (通过Windows设备管理器)")
                gpu_detected = True
                gpu_info['type'] = 'NVIDIA'

                # 提取GPU型号
                for line in output.splitlines():
                    if 'nvidia' in line.lower() or 'geforce' in line.lower() or 'quadro' in line.lower():
                        gpu_info['model'] = line.strip()
                        print_info(f"GPU型号: {gpu_info['model']}")
                        break
            elif success and ('amd' in output.lower() or 'radeon' in output.lower()):
                print_warning("检测到AMD GPU，但当前脚本仅支持NVIDIA GPU")
                gpu_detected = False
                gpu_info['type'] = 'AMD'
            else:
                print_warning("未检测到支持的GPU")
                gpu_detected = False
        elif os_name == 'Linux':
            success, output = run_command("lspci | grep -i nvidia")
            if success:
                print_success("检测到NVIDIA GPU (通过lspci)")
                gpu_detected = True
                gpu_info['type'] = 'NVIDIA'

                # 提取GPU型号
                for line in output.splitlines():
                    if 'nvidia' in line.lower() or 'geforce' in line.lower() or 'quadro' in line.lower():
                        gpu_info['model'] = line.strip()
                        print_info(f"GPU型号: {gpu_info['model']}")
                        break
            else:
                print_warning("未检测到NVIDIA GPU")
                gpu_detected = False

    # 检测CUDA
    success, output = run_command("nvcc --version")
    if success:
        cuda_match = re.search(r'release (\d+\.\d+)', output)
        if cuda_match:
            gpu_info['cuda_version'] = cuda_match.group(1)
            print_info(f"CUDA版本: {gpu_info['cuda_version']}")
    else:
        print_info("未检测到CUDA工具包")

    return gpu_detected, gpu_info

def install_pytorch_gpu():
    """安装PyTorch GPU版本"""
    print_step("2", "安装PyTorch GPU版本...")

    # 检查是否已安装PyTorch
    try:
        import torch
        print_info(f"当前PyTorch版本: {torch.__version__}")

        if torch.cuda.is_available():
            print_success("PyTorch已支持GPU")
            print_info(f"CUDA版本: {torch.version.cuda}")
            return True
        else:
            print_warning("PyTorch已安装但不支持GPU，将重新安装GPU版本")
    except ImportError:
        print_info("未安装PyTorch，将安装GPU版本")

    # 安装PyTorch GPU版本
    print_info("正在安装PyTorch GPU版本...")
    success, output = run_command(f"{sys.executable} -m pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118")

    if success:
        print_success("PyTorch GPU版本安装成功")
        return True
    else:
        print_error(f"PyTorch GPU版本安装失败: {output}")
        return False

def install_tensorflow_gpu():
    """安装TensorFlow GPU版本"""
    print_step("3", "安装TensorFlow GPU版本...")

    # 检查是否已安装TensorFlow
    try:
        import tensorflow as tf
        print_info(f"当前TensorFlow版本: {tf.__version__}")

        if len(tf.config.list_physical_devices('GPU')) > 0:
            print_success("TensorFlow已支持GPU")
            return True
        else:
            print_warning("TensorFlow已安装但不支持GPU，将重新安装")
    except ImportError:
        print_info("未安装TensorFlow，将安装GPU版本")

    # 安装TensorFlow
    print_info("正在安装TensorFlow...")
    success, output = run_command(f"{sys.executable} -m pip install tensorflow")

    if success:
        print_success("TensorFlow安装成功")
        return True
    else:
        print_error(f"TensorFlow安装失败: {output}")
        return False

def verify_installation():
    """验证安装结果"""
    print_step("4", "验证安装结果...")

    # 验证PyTorch
    try:
        import torch
        print_info(f"PyTorch版本: {torch.__version__}")

        if torch.cuda.is_available():
            print_success(f"PyTorch GPU支持正常，检测到 {torch.cuda.device_count()} 个GPU设备")
            for i in range(torch.cuda.device_count()):
                print_info(f"GPU {i+1}: {torch.cuda.get_device_name(i)}")
        else:
            print_warning("PyTorch未检测到GPU")
    except ImportError:
        print_warning("PyTorch未安装")

    # 验证TensorFlow
    try:
        import tensorflow as tf
        print_info(f"TensorFlow版本: {tf.__version__}")

        gpus = tf.config.list_physical_devices('GPU')
        if len(gpus) > 0:
            print_success(f"TensorFlow GPU支持正常，检测到 {len(gpus)} 个GPU设备")
            for i, gpu in enumerate(gpus):
                print_info(f"GPU {i+1}: {gpu.name}")
        else:
            print_warning("TensorFlow未检测到GPU")
    except ImportError:
        print_warning("TensorFlow未安装")

def install_gpu_support():
    """
    安装GPU支持

    此函数是对main函数的封装，用于在代码中调用GPU支持安装

    返回:
        dict: 安装结果字典
    """
    return main(return_result=True)

def main(return_result=False):
    """
    主函数

    参数:
        return_result (bool): 是否返回结果字典，用于程序调用

    返回:
        dict: 如果return_result为True，返回安装结果字典
    """
    print_header("GPU支持自动安装脚本")

    # 初始化结果字典
    result = {
        'success': False,
        'gpu_detected': False,
        'gpu_info': {},
        'pytorch_success': False,
        'tensorflow_success': False,
        'message': ''
    }

    # 检测GPU
    gpu_detected, gpu_info = detect_gpu()
    result['gpu_detected'] = gpu_detected
    result['gpu_info'] = gpu_info

    if not gpu_detected:
        print_error("未检测到支持的GPU，无法继续安装")
        print_info("请确保您的系统有NVIDIA GPU并已安装正确的驱动程序")
        result['message'] = "未检测到支持的GPU，无法继续安装"
        if return_result:
            return result
        return

    # 安装PyTorch GPU版本
    pytorch_success = install_pytorch_gpu()
    result['pytorch_success'] = pytorch_success

    # 安装TensorFlow GPU版本
    tensorflow_success = install_tensorflow_gpu()
    result['tensorflow_success'] = tensorflow_success

    # 验证安装结果
    verify_installation()

    # 总结
    print_header("安装总结")

    if pytorch_success:
        print_success("PyTorch GPU支持安装成功")
    else:
        print_error("PyTorch GPU支持安装失败")

    if tensorflow_success:
        print_success("TensorFlow GPU支持安装成功")
    else:
        print_error("TensorFlow GPU支持安装失败")

    print_info("请重启应用程序以应用更改")

    # 设置总体成功状态
    result['success'] = pytorch_success or tensorflow_success
    result['message'] = "GPU支持安装" + ("成功" if result['success'] else "失败")

    # 返回结果
    if return_result:
        return result

if __name__ == "__main__":
    main()
