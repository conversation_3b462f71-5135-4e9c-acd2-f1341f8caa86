#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
配置文件测试脚本
测试系统能否正确加载和应用各种配置
"""

import os
import sys
import yaml
import logging
import tempfile
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'logs/config_test_{datetime.now().strftime("%Y-%m-%d")}.log')
    ]
)

# 确保logs目录存在
os.makedirs('logs', exist_ok=True)

# 添加项目路径到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入相关模块
from quant_trading.utils.common import load_config
from quant_trading.agents.drl_agent import DRLAgent
from quant_trading.trading.trading_environment import TradingEnvironment

def test_load_config():
    """测试配置文件加载功能"""
    print("\n===== 测试配置文件加载 =====")
    
    # 测试加载DRL智能体配置
    try:
        drl_config_path = "quant_project/configs/drl_agent_config.yaml"
        drl_config = load_config(drl_config_path)
        
        if drl_config:
            print(f"成功加载DRL智能体配置文件: {drl_config_path}")
            print(f"算法: {drl_config.get('algorithm')}")
            print(f"学习率: {drl_config.get('learning_rate')}")
            print(f"是否使用GPU: {drl_config.get('use_gpu')}")
        else:
            print(f"加载DRL智能体配置文件失败: {drl_config_path}")
    except Exception as e:
        print(f"加载DRL智能体配置文件时出错: {str(e)}")
    
    # 测试加载环境配置
    try:
        env_config_path = "quant_project/configs/env_config.yaml"
        env_config = load_config(env_config_path)
        
        if env_config:
            print(f"成功加载环境配置文件: {env_config_path}")
            print(f"初始资金: {env_config.get('initial_capital')}")
            print(f"手续费率: {env_config.get('commission_rate')}")
            print(f"最小持仓天数: {env_config.get('min_hold_days')}")
        else:
            print(f"加载环境配置文件失败: {env_config_path}")
    except Exception as e:
        print(f"加载环境配置文件时出错: {str(e)}")

def test_modify_config():
    """测试修改配置并应用"""
    print("\n===== 测试修改配置并应用 =====")
    
    # 创建临时配置文件
    with tempfile.NamedTemporaryFile(mode='w+', suffix='.yaml', delete=False) as temp_drl_config:
        # 写入修改后的DRL智能体配置
        modified_drl_config = {
            'algorithm': 'PPO',
            'policy_network': 'MlpPolicy',
            'learning_rate': 0.0005,  # 修改学习率
            'gamma': 0.98,  # 修改折扣因子
            'use_gpu': True,
            'ppo': {
                'n_steps': 1024,  # 修改步数
                'batch_size': 128,  # 修改批量大小
                'n_epochs': 5,
                'gae_lambda': 0.95,
                'clip_range': 0.2,
                'ent_coef': 0.01,
                'vf_coef': 0.5,
                'max_grad_norm': 0.5
            },
            'network': {
                'net_arch': {
                    'pi': [128, 128],  # 修改网络结构
                    'vf': [128, 128]
                },
                'activation_fn': 'relu'
            },
            'training': {
                'total_timesteps': 50000,
                'eval_freq': 5000,
                'n_eval_episodes': 3,
                'save_freq': 5000
            }
        }
        yaml.dump(modified_drl_config, temp_drl_config)
        temp_drl_config_path = temp_drl_config.name
    
    with tempfile.NamedTemporaryFile(mode='w+', suffix='.yaml', delete=False) as temp_env_config:
        # 写入修改后的环境配置
        modified_env_config = {
            'initial_capital': 200000,  # 修改初始资金
            'commission_rate': 0.0005,  # 修改手续费率
            'min_hold_days': 5,  # 修改最小持仓天数
            'allow_short': False,
            'max_position': 0.8,  # 修改最大仓位
            'window_size': 30,  # 修改观测窗口
            'reward_config': {
                'portfolio_return': 1.0,
                'volatility_penalty': 0.2,  # 修改波动率惩罚
                'drawdown_penalty': 0.3,  # 修改回撤惩罚
                'holding_penalty': 0.1,
                'trade_penalty': 0.02
            }
        }
        yaml.dump(modified_env_config, temp_env_config)
        temp_env_config_path = temp_env_config.name
    
    try:
        # 加载修改后的配置
        drl_config = load_config(temp_drl_config_path)
        env_config = load_config(temp_env_config_path)
        
        print("修改后的DRL智能体配置:")
        print(f"学习率: {drl_config.get('learning_rate')}")
        print(f"批量大小: {drl_config['ppo'].get('batch_size')}")
        print(f"网络结构: {drl_config['network']['net_arch']}")
        
        print("\n修改后的环境配置:")
        print(f"初始资金: {env_config.get('initial_capital')}")
        print(f"手续费率: {env_config.get('commission_rate')}")
        print(f"最小持仓天数: {env_config.get('min_hold_days')}")
        print(f"最大仓位: {env_config.get('max_position')}")
        
        # 验证配置是否正确应用到系统中
        # 这里只是简单检查，实际应用需要创建环境和智能体实例
        print("\n验证配置是否正确应用...")
        
        # 清理临时文件
        os.unlink(temp_drl_config_path)
        os.unlink(temp_env_config_path)
        
        print("配置修改测试完成")
        
    except Exception as e:
        print(f"测试修改配置时出错: {str(e)}")
        # 确保清理临时文件
        if os.path.exists(temp_drl_config_path):
            os.unlink(temp_drl_config_path)
        if os.path.exists(temp_env_config_path):
            os.unlink(temp_env_config_path)

def run_all_tests():
    """运行所有配置测试"""
    try:
        test_load_config()
        test_modify_config()
        print("\n===== 所有配置测试完成 =====")
        return True
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        return False

if __name__ == '__main__':
    run_all_tests()
