"""
数据中心页面组件
实现数据获取、预览和质量检查功能
"""

import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import logging

logger = logging.getLogger('drl_trading')

def render_data_center():
    """渲染数据中心页面"""
    st.markdown("## 📊 数据中心")

    # 使用选项卡组织不同的数据操作
    tabs = st.tabs(["数据获取", "数据预览", "数据质量"])

    with tabs[0]:
        # 左右两列布局，左侧是表单，右侧是预览
        col1, col2 = st.columns([1, 1])

        with col1:
            with st.form("data_fetch_form"):
                st.markdown("### 获取金融数据")

                # 分组参数，让相关选项在一起
                st.markdown("#### 数据来源")
                data_source = st.selectbox("选择数据来源", ["股票", "指数", "基金"],
                                         help="选择要获取的金融数据类型")

                st.markdown("#### 代码与时间范围")
                code_prefix = "sh" if data_source == "股票" else "index_" if data_source == "指数" else "fund_"
                code = st.text_input("输入代码", value=f"{code_prefix}000001",
                                    help="输入金融产品代码，如股票代码、指数代码等")

                col_date1, col_date2 = st.columns(2)
                with col_date1:
                    start_date = st.date_input("开始日期",
                                              value=datetime.now() - timedelta(days=365),
                                              help="选择数据起始日期")
                with col_date2:
                    end_date = st.date_input("结束日期",
                                            value=datetime.now(),
                                            help="选择数据结束日期")

                st.markdown("#### 高级选项")
                use_cache = st.checkbox("使用缓存", value=True,
                                       help="使用缓存可加快数据加载速度")
                frequency = st.radio("数据频率", ["日线", "周线", "月线"],
                                    horizontal=True,
                                    help="选择数据的时间频率")

                submitted = st.form_submit_button("获取数据",
                                                use_container_width=True,
                                                help="点击获取数据")

                if submitted:
                    # 显示加载状态
                    with st.spinner("正在获取数据..."):
                        # 后端交互代码
                        try:
                            # 调用后端数据处理模块
                            try:
                                from core_logic.data_handling import DataHandler
                                from core_logic.data_handling.adapter import DataHandlerAdapter
                                data_handler = DataHandlerAdapter()
                            except ImportError:
                                from core_logic.data_handler import DataHandler
                                data_handler = DataHandler()

                            data = data_handler.get_stock_data(
                                stock_code=code,
                                start_date=start_date.strftime("%Y-%m-%d"),
                                end_date=end_date.strftime("%Y-%m-%d"),
                                frequency=frequency,  # 直接传递中文频率参数
                                use_cache=use_cache,
                                data_source=data_source
                            )
                            st.session_state.data = data
                            st.success(f"成功获取数据：{len(data)}条记录")
                            logger.info(f"成功获取数据: {code}, {len(data)}条记录")
                        except Exception as e:
                            st.error(f"获取数据失败: {str(e)}")
                            logger.error(f"获取数据失败: {str(e)}")

        with col2:
            st.markdown("### 数据预览")
            if 'data' in st.session_state and st.session_state.data is not None:
                # 数据基本信息
                data = st.session_state.data
                st.info(f"共 {len(data)} 条记录，时间范围：{data.index[0].strftime('%Y-%m-%d')} 至 {data.index[-1].strftime('%Y-%m-%d')}")

                # 显示数据表格预览
                st.dataframe(data.head(10), use_container_width=True)

                # 显示价格趋势图
                if '收盘' in data.columns:
                    fig, ax = plt.subplots(figsize=(10, 4))
                    ax.plot(data.index, data['收盘'])
                    ax.set_title('价格趋势')
                    ax.grid(True, alpha=0.3)
                    st.pyplot(fig)
            else:
                st.info("请在左侧获取数据")

    with tabs[1]:
        if 'data' in st.session_state and st.session_state.data is not None:
            data = st.session_state.data
            st.dataframe(data, use_container_width=True)
        else:
            st.info("尚未加载数据，请先在数据获取标签页获取数据")

    with tabs[2]:
        if 'data' in st.session_state and st.session_state.data is not None:
            st.markdown("### 数据质量报告")

            # 调用后端数据质量检查逻辑
            try:
                try:
                    from core_logic.data_handling import DataHandler
                    from core_logic.data_handling.adapter import DataHandlerAdapter
                    data_handler = DataHandlerAdapter()
                except ImportError:
                    from core_logic.data_handler import DataHandler
                    data_handler = DataHandler()

                quality_report = data_handler.get_data_quality_report(st.session_state.data)

                # 用卡片展示数据质量指标
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("数据条数", quality_report.get("record_count", 0))
                with col2:
                    st.metric("缺失值比例", f"{quality_report.get('missing_rate', 0):.2%}")
                with col3:
                    st.metric("异常值数量", quality_report.get("outlier_count", 0))

                # 更多质量指标
                st.write("详细质量指标:")
                st.json(quality_report)
            except Exception as e:
                st.error(f"获取数据质量报告失败: {str(e)}")
                logger.error(f"获取数据质量报告失败: {str(e)}")
        else:
            st.info("尚未加载数据，请先在数据获取标签页获取数据")