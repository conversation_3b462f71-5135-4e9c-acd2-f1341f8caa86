"""
自动训练模块
提供无人值守的模型训练和评估功能
"""

import os
import json
import logging
import numpy as np
import pandas as pd
from datetime import datetime
import torch
import matplotlib.pyplot as plt
from typing import Dict, List, Union, Tuple, Optional, Any, Callable
import traceback
import time
import threading
import socket
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.application import MIMEApplication

from .model_utils import ModelTrainer
from .trading_env import TradingEnvironment
from .drl_agent import PPOAgent
from .backtest_utils import BacktestEngine

logger = logging.getLogger('drl_trading')

class AutoTrainer:
    """自动训练类，提供无人值守的模型训练和评估功能"""
    
    def __init__(self, 
                base_dir: str = './experiments',
                config_path: Optional[str] = None,
                notification_email: Optional[str] = None,
                smtp_config: Optional[Dict] = None):
        """
        初始化自动训练器
        
        参数:
            base_dir: str 实验基础目录
            config_path: str 配置文件路径
            notification_email: str 通知邮箱地址
            smtp_config: Dict SMTP服务器配置
        """
        self.base_dir = base_dir
        self.notification_email = notification_email
        self.smtp_config = smtp_config
        
        # 确保目录存在
        os.makedirs(base_dir, exist_ok=True)
        
        # 设置子目录
        self.checkpoints_dir = os.path.join(base_dir, 'checkpoints')
        self.logs_dir = os.path.join(base_dir, 'logs')
        self.results_dir = os.path.join(base_dir, 'results')
        self.plots_dir = os.path.join(base_dir, 'plots')
        
        # 创建子目录
        for directory in [self.checkpoints_dir, self.logs_dir, self.results_dir, self.plots_dir]:
            os.makedirs(directory, exist_ok=True)
            
        # 加载配置
        self.config = self._load_config(config_path)
        
        # 设置训练状态
        self.training_status = {
            'current_experiment': None,
            'start_time': None,
            'last_update_time': None,
            'progress': 0,
            'best_performance': None,
            'running': False,
            'error': None
        }
        
        # 初始化训练线程
        self.training_thread = None
        
    def _load_config(self, config_path: Optional[str]) -> Dict:
        """加载配置文件"""
        default_config = {
            # 实验配置
            'experiment_name': f'experiment_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
            'random_seed': 42,
            'device': 'cuda' if torch.cuda.is_available() else 'cpu',
            'continue_training': False,
            'checkpoint_path': None,
            
            # 数据配置
            'data_path': None,
            'train_test_split': 0.8,
            'time_features': True,
            'technical_indicators': ['SMA', 'EMA', 'RSI', 'MACD', 'BBANDS'],
            'feature_normalization': True,
            
            # 模型配置
            'model_type': 'drl',  # 'drl' or 'ml'
            'drl_agent': 'ppo',   # 'ppo' or 'sac'
            'ml_model': 'lstm',   # 'lstm', 'gru', 'cnn', 'mlp'
            
            # 训练配置
            'batch_size': 64,
            'learning_rate': 3e-4,
            'n_epochs': 100,
            'early_stopping': 10,
            'checkpoint_interval': 10,
            'eval_interval': 5,
            
            # 强化学习配置
            'gamma': 0.99,
            'reward_scaling': 1e-4,
            'lookback_window_size': 50,
            'ppo_clip_ratio': 0.2,
            'entropy_coef': 0.01,
            'gae_lambda': 0.95,
            
            # 环境配置
            'initial_balance': 1000000.0,
            'commission_rate': 0.0003,
            
            # 回测配置
            'run_backtest': True,
            'backtest_start_date': None,
            'backtest_end_date': None,
            
            # 监控配置
            'max_training_time': 72,  # 小时
            'heartbeat_interval': 30,  # 分钟
            'restart_on_error': True,
            'max_restarts': 3
        }
        
        # 如果提供了配置文件，加载并合并
        if config_path and os.path.exists(config_path):
            try:
                with open(config_path, 'r') as f:
                    user_config = json.load(f)
                # 更新默认配置
                default_config.update(user_config)
                logger.info(f"Loaded configuration from {config_path}")
            except Exception as e:
                logger.error(f"Error loading configuration: {e}")
                logger.error(traceback.format_exc())
        
        return default_config
    
    def setup_experiment(self, experiment_name: Optional[str] = None) -> str:
        """
        设置新实验
        
        参数:
            experiment_name: str 实验名称
            
        返回:
            实验目录路径
        """
        # 使用提供的名称或配置中的名称
        if experiment_name:
            self.config['experiment_name'] = experiment_name
            
        # 创建实验目录
        experiment_dir = os.path.join(self.base_dir, self.config['experiment_name'])
        os.makedirs(experiment_dir, exist_ok=True)
        
        # 创建实验子目录
        for subdir in ['checkpoints', 'logs', 'results', 'plots']:
            os.makedirs(os.path.join(experiment_dir, subdir), exist_ok=True)
            
        # 保存配置文件
        config_path = os.path.join(experiment_dir, 'config.json')
        with open(config_path, 'w') as f:
            json.dump(self.config, f, indent=4)
            
        logger.info(f"Experiment setup at {experiment_dir}")
        
        return experiment_dir
    
    def prepare_data(self, data_path: Optional[str] = None) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        准备训练和测试数据
        
        参数:
            data_path: str 数据文件路径
            
        返回:
            (训练数据, 测试数据)
        """
        # 使用提供的路径或配置中的路径
        if data_path:
            self.config['data_path'] = data_path
            
        if not self.config['data_path'] or not os.path.exists(self.config['data_path']):
            raise ValueError(f"Data path not found: {self.config['data_path']}")
            
        # 加载数据
        try:
            data = pd.read_csv(self.config['data_path'], index_col=0, parse_dates=True)
            logger.info(f"Loaded data from {self.config['data_path']}, shape: {data.shape}")
        except Exception as e:
            logger.error(f"Error loading data: {e}")
            logger.error(traceback.format_exc())
            raise
            
        # 添加时间特征
        if self.config['time_features']:
            from .data_processors import DataProcessor
            data = DataProcessor.create_time_features(data)
            logger.info("Added time features")
            
        # 添加技术指标
        if self.config['technical_indicators']:
            from .data_processors import DataProcessor
            data = DataProcessor.add_technical_indicators(
                data, 
                indicators=self.config['technical_indicators']
            )
            logger.info(f"Added technical indicators: {self.config['technical_indicators']}")
            
        # 处理缺失值
        data = data.dropna()
        logger.info(f"Data shape after preprocessing: {data.shape}")
        
        # 标准化特征
        if self.config['feature_normalization']:
            from .data_processors import DataProcessor
            data = DataProcessor.normalize_features(data)
            logger.info("Normalized features")
            
        # 分割训练集和测试集
        train_size = int(len(data) * self.config['train_test_split'])
        train_data = data.iloc[:train_size]
        test_data = data.iloc[train_size:]
        
        logger.info(f"Train data shape: {train_data.shape}")
        logger.info(f"Test data shape: {test_data.shape}")
        
        return train_data, test_data
    
    def create_environment(self, data: pd.DataFrame) -> TradingEnvironment:
        """
        创建交易环境
        
        参数:
            data: DataFrame 交易数据
            
        返回:
            交易环境
        """
        env = TradingEnvironment(
            data=data,
            lookback_window_size=self.config['lookback_window_size'],
            initial_balance=self.config['initial_balance'],
            commission_rate=self.config['commission_rate'],
            reward_scaling=self.config['reward_scaling'],
            price_col='收盘' if '收盘' in data.columns else 'close'
        )
        
        logger.info(f"Created trading environment with lookback window: {self.config['lookback_window_size']}")
        
        return env
    
    def create_agent(self, env: TradingEnvironment) -> Any:
        """
        创建交易智能体
        
        参数:
            env: TradingEnvironment 交易环境
            
        返回:
            交易智能体
        """
        if self.config['drl_agent'] == 'ppo':
            agent = PPOAgent(
                env=env,
                model_name=self.config['experiment_name'],
                checkpoint_dir=os.path.join(self.checkpoints_dir, self.config['experiment_name']),
                device=torch.device(self.config['device']),
                gamma=self.config['gamma'],
                clip_ratio=self.config['ppo_clip_ratio'],
                policy_lr=self.config['learning_rate'],
                value_lr=self.config['learning_rate'] * 3,
                gae_lambda=self.config['gae_lambda'],
                entropy_coef=self.config['entropy_coef'],
                seed=self.config['random_seed']
            )
            logger.info("Created PPO agent")
            
        # 如果需要继续训练
        if self.config['continue_training'] and self.config['checkpoint_path']:
            agent.load_checkpoint(self.config['checkpoint_path'])
            logger.info(f"Loaded checkpoint from {self.config['checkpoint_path']}")
            
        return agent
    
    def train_agent(self, agent: Any, eval_env: Optional[TradingEnvironment] = None) -> None:
        """
        训练交易智能体
        
        参数:
            agent: 交易智能体
            eval_env: TradingEnvironment 评估环境
        """
        try:
            # 更新训练状态
            self.training_status['running'] = True
            self.training_status['current_experiment'] = self.config['experiment_name']
            self.training_status['start_time'] = datetime.now()
            self.training_status['last_update_time'] = datetime.now()
            
            # 训练参数
            n_episodes = self.config['n_epochs']
            eval_interval = self.config['eval_interval']
            
            # 启动心跳线程
            heartbeat_thread = threading.Thread(target=self._heartbeat_monitor)
            heartbeat_thread.daemon = True
            heartbeat_thread.start()
            
            # 训练智能体
            logger.info(f"Starting training for {n_episodes} episodes")
            
            agent.train(
                n_episodes=n_episodes,
                evaluate_interval=eval_interval
            )
            
            # 训练完成后评估
            if eval_env:
                logger.info("Evaluating trained agent")
                mean_reward, std_reward = agent.evaluate(n_episodes=20, render=False)
                
                # 更新训练状态
                self.training_status['best_performance'] = mean_reward
                
                # 保存评估结果
                eval_results = {
                    'mean_reward': float(mean_reward),
                    'std_reward': float(std_reward),
                    'n_episodes': 20,
                    'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
                
                results_path = os.path.join(
                    self.results_dir, 
                    f"{self.config['experiment_name']}_eval_results.json"
                )
                
                with open(results_path, 'w') as f:
                    json.dump(eval_results, f, indent=4)
                    
                logger.info(f"Evaluation results saved to {results_path}")
            
            # 绘制训练历史
            plot_path = os.path.join(
                self.plots_dir,
                f"{self.config['experiment_name']}_training_history.png"
            )
            
            agent.plot_training_history(save_path=plot_path)
            logger.info(f"Training history plot saved to {plot_path}")
            
            # 更新训练状态
            self.training_status['running'] = False
            self.training_status['progress'] = 100
            
            # 发送通知
            self._send_notification(
                subject=f"Training Completed: {self.config['experiment_name']}",
                message=f"Training completed successfully. Best performance: {self.training_status['best_performance']}"
            )
            
        except Exception as e:
            logger.error(f"Error during training: {e}")
            logger.error(traceback.format_exc())
            
            # 更新训练状态
            self.training_status['running'] = False
            self.training_status['error'] = str(e)
            
            # 发送通知
            self._send_notification(
                subject=f"Training Error: {self.config['experiment_name']}",
                message=f"Error during training: {str(e)}\n\n{traceback.format_exc()}"
            )
            
            # 如果配置了重启，尝试重启训练
            if self.config['restart_on_error'] and self.restart_count < self.config['max_restarts']:
                logger.info(f"Restarting training (attempt {self.restart_count + 1}/{self.config['max_restarts']})")
                self.restart_count += 1
                
                # 重置训练状态
                self.training_status['running'] = False
                self.training_status['error'] = None
                
                # 延迟几秒后重启
                time.sleep(5)
                
                # 重新开始训练
                self.start_training()
    
    def run_backtest(self, agent: Any, test_data: pd.DataFrame) -> Dict:
        """
        运行回测
        
        参数:
            agent: 交易智能体
            test_data: DataFrame 测试数据
            
        返回:
            回测结果
        """
        try:
            # 创建回测引擎
            backtest_engine = BacktestEngine(
                data=test_data,
                initial_capital=self.config['initial_balance'],
                commission_rate=self.config['commission_rate'],
                price_col='收盘' if '收盘' in test_data.columns else 'close',
                trade_log_path=os.path.join(
                    self.results_dir,
                    f"{self.config['experiment_name']}_trades.csv"
                )
            )
            
            # 生成交易信号
            logger.info("Generating trading signals...")
            signals = []
            
            # 创建回测环境
            env = self.create_environment(test_data)
            
            # 重置环境
            obs = env.reset()
            done = False
            
            # 逐步生成信号
            while not done:
                action = agent.select_action(obs, eval=True)
                
                # 转换动作到信号 (0: 持有, 1: 买入, 2: 卖出)
                if action == 1:  # 买入
                    signal = 1
                elif action == 2:  # 卖出
                    signal = -1
                else:  # 持有
                    signal = 0
                    
                signals.append(signal)
                
                # 执行环境步骤
                obs, reward, done, info = env.step(action)
            
            # 补全信号长度
            if len(signals) < len(test_data):
                signals = [0] * (len(test_data) - len(signals)) + signals
            
            # 运行回测
            logger.info("Running backtest...")
            results = backtest_engine.run(signals=pd.Series(signals, index=test_data.index))
            
            # 保存回测结果
            metrics = backtest_engine.get_performance_metrics()
            
            # 保存性能指标
            metrics_path = os.path.join(
                self.results_dir,
                f"{self.config['experiment_name']}_backtest_metrics.json"
            )
            
            with open(metrics_path, 'w') as f:
                # 将浮点数值转换为普通Python浮点数以便序列化
                serializable_metrics = {k: float(v) if isinstance(v, (np.float32, np.float64)) else v 
                                       for k, v in metrics.items()}
                json.dump(serializable_metrics, f, indent=4)
                
            # 绘制回测结果
            plot_path = os.path.join(
                self.plots_dir,
                f"{self.config['experiment_name']}_backtest_results.png"
            )
            
            backtest_engine.plot_results(save_path=plot_path)
            
            logger.info(f"Backtest completed. Results saved to {metrics_path}")
            logger.info(f"Backtest plot saved to {plot_path}")
            
            # 打印性能摘要
            backtest_engine.print_performance_summary()
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error during backtest: {e}")
            logger.error(traceback.format_exc())
            
            # 发送通知
            self._send_notification(
                subject=f"Backtest Error: {self.config['experiment_name']}",
                message=f"Error during backtest: {str(e)}\n\n{traceback.format_exc()}"
            )
            
            return {'error': str(e)}
    
    def start_training(self) -> None:
        """开始训练流程"""
        try:
            # 初始化重启计数
            self.restart_count = 0
            
            # 设置实验
            experiment_dir = self.setup_experiment()
            
            # 准备数据
            train_data, test_data = self.prepare_data()
            
            # 创建环境
            train_env = self.create_environment(train_data)
            eval_env = self.create_environment(test_data)
            
            # 创建智能体
            agent = self.create_agent(train_env)
            
            # 启动训练线程
            self.training_thread = threading.Thread(
                target=self.train_agent,
                args=(agent, eval_env)
            )
            self.training_thread.start()
            
            logger.info(f"Training started in background for experiment: {self.config['experiment_name']}")
            
            # 等待训练完成
            self.training_thread.join()
            
            # 如果配置了回测
            if self.config['run_backtest']:
                logger.info("Running backtest after training...")
                backtest_results = self.run_backtest(agent, test_data)
                
                # 打印主要回测结果
                if 'error' not in backtest_results:
                    logger.info(f"Backtest Annual Return: {backtest_results.get('Annual Return', 0):.2%}")
                    logger.info(f"Backtest Sharpe Ratio: {backtest_results.get('Sharpe Ratio', 0):.2f}")
                    logger.info(f"Backtest Max Drawdown: {backtest_results.get('Max Drawdown', 0):.2%}")
            
        except Exception as e:
            logger.error(f"Error in training workflow: {e}")
            logger.error(traceback.format_exc())
            
            # 更新训练状态
            self.training_status['running'] = False
            self.training_status['error'] = str(e)
            
            # 发送通知
            self._send_notification(
                subject=f"Training Workflow Error: {self.config['experiment_name']}",
                message=f"Error in training workflow: {str(e)}\n\n{traceback.format_exc()}"
            )
    
    def _heartbeat_monitor(self) -> None:
        """心跳监控线程，定期检查训练状态"""
        last_progress = 0
        
        while self.training_status['running']:
            try:
                current_time = datetime.now()
                
                # 计算经过的时间
                elapsed_time = (current_time - self.training_status['start_time']).total_seconds() / 3600
                
                # 检查是否超过最大训练时间
                if elapsed_time > self.config['max_training_time']:
                    logger.warning(f"Training exceeded maximum time limit of {self.config['max_training_time']} hours")
                    
                    # 发送通知
                    self._send_notification(
                        subject=f"Training Time Limit Exceeded: {self.config['experiment_name']}",
                        message=f"Training has been running for {elapsed_time:.2f} hours, "
                                f"exceeding the maximum limit of {self.config['max_training_time']} hours."
                    )
                    
                    # 标记错误但不中断训练
                    self.training_status['error'] = f"Exceeded time limit of {self.config['max_training_time']} hours"
                
                # 计算自上次更新以来的时间
                time_since_update = (current_time - self.training_status['last_update_time']).total_seconds() / 60
                
                # 检查是否应该发送心跳通知
                if time_since_update > self.config['heartbeat_interval']:
                    # 计算训练进度
                    if hasattr(self, 'agent') and hasattr(self.agent, 'train_steps'):
                        steps_done = self.agent.train_steps
                        total_steps = self.config['n_epochs'] * self.agent.env.max_steps
                        progress = min(100, int(steps_done / total_steps * 100))
                        self.training_status['progress'] = progress
                    
                    # 只有在进度变化时才发送通知
                    if self.training_status['progress'] > last_progress:
                        # 发送心跳通知
                        self._send_notification(
                            subject=f"Training Heartbeat: {self.config['experiment_name']}",
                            message=f"Training progress: {self.training_status['progress']}%\n"
                                    f"Running for {elapsed_time:.2f} hours"
                        )
                        
                        # 更新最后通知时间和进度
                        self.training_status['last_update_time'] = current_time
                        last_progress = self.training_status['progress']
                
                # 检查系统资源
                self._check_system_resources()
                
                # 休眠一段时间
                time.sleep(60)  # 每分钟检查一次
                
            except Exception as e:
                logger.error(f"Error in heartbeat monitor: {e}")
                time.sleep(300)  # 发生错误后休眠5分钟
    
    def _check_system_resources(self) -> None:
        """检查系统资源使用情况"""
        try:
            # 检查内存使用
            if hasattr(torch, 'cuda') and torch.cuda.is_available():
                for i in range(torch.cuda.device_count()):
                    # 获取当前GPU内存使用情况
                    allocated = torch.cuda.memory_allocated(i) / (1024 ** 3)  # GB
                    max_mem = torch.cuda.get_device_properties(i).total_memory / (1024 ** 3)  # GB
                    
                    # 如果内存使用过高，记录警告
                    if allocated > max_mem * 0.9:  # 超过90%
                        logger.warning(f"GPU {i} memory usage is high: {allocated:.2f}GB / {max_mem:.2f}GB")
                        
                        # 可能的操作：减小批次大小或尝试清理内存
                        if hasattr(self, 'agent'):
                            torch.cuda.empty_cache()
                            
        except Exception as e:
            logger.error(f"Error checking system resources: {e}")
    
    def _send_notification(self, subject: str, message: str, attachments: List[str] = None) -> None:
        """
        发送电子邮件通知
        
        参数:
            subject: str 邮件主题
            message: str 邮件内容
            attachments: List[str] 附件路径列表
        """
        if not self.notification_email or not self.smtp_config:
            return
            
        try:
            # 创建邮件
            msg = MIMEMultipart()
            msg['From'] = self.smtp_config.get('username', '<EMAIL>')
            msg['To'] = self.notification_email
            msg['Subject'] = subject
            
            # 添加正文
            msg.attach(MIMEText(message, 'plain'))
            
            # 添加训练状态摘要
            status_summary = "\n\n=== Training Status ===\n"
            for key, value in self.training_status.items():
                status_summary += f"{key}: {value}\n"
            
            msg.attach(MIMEText(status_summary, 'plain'))
            
            # 添加系统信息
            system_info = "\n\n=== System Info ===\n"
            system_info += f"Hostname: {socket.gethostname()}\n"
            system_info += f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            
            if hasattr(torch, 'cuda') and torch.cuda.is_available():
                system_info += f"GPU: {torch.cuda.get_device_name(0)}\n"
                allocated = torch.cuda.memory_allocated(0) / (1024 ** 3)  # GB
                system_info += f"GPU Memory: {allocated:.2f}GB\n"
                
            msg.attach(MIMEText(system_info, 'plain'))
            
            # 添加附件
            if attachments:
                for file_path in attachments:
                    if os.path.exists(file_path):
                        with open(file_path, 'rb') as f:
                            attachment = MIMEApplication(f.read())
                            attachment.add_header(
                                'Content-Disposition', 'attachment', 
                                filename=os.path.basename(file_path)
                            )
                            msg.attach(attachment)
            
            # 发送邮件
            server = smtplib.SMTP(
                self.smtp_config.get('server', 'smtp.gmail.com'),
                self.smtp_config.get('port', 587)
            )
            server.starttls()
            server.login(
                self.smtp_config.get('username'),
                self.smtp_config.get('password')
            )
            server.send_message(msg)
            server.quit()
            
            logger.info(f"Notification sent: {subject}")
            
        except Exception as e:
            logger.error(f"Error sending notification: {e}")
    
    def get_training_status(self) -> Dict:
        """获取当前训练状态"""
        return self.training_status 