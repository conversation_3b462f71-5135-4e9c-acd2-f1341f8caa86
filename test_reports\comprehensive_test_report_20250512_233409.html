
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>量化交易系统全面测试报告</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                h1, h2, h3 { color: #333; }
                table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
                tr:nth-child(even) { background-color: #f9f9f9; }
                .success { color: green; }
                .failure { color: red; }
                .warning { color: orange; }
                .summary { background-color: #f0f0f0; padding: 10px; margin-bottom: 20px; }
            </style>
        </head>
        <body>
            <h1>量化交易系统全面测试报告</h1>
            <p>生成时间: 2025-05-12 23:34:09</p>
            
            <div class="summary">
                <h2>测试摘要</h2>
                <table>
                    <tr>
                        <th>测试类别</th>
                        <th>通过数</th>
                        <th>失败数</th>
                        <th>通过率</th>
                    </tr>
        
                    <tr>
                        <td>脚本测试</td>
                        <td>4</td>
                        <td>1</td>
                        <td>80.00%</td>
                    </tr>
                    <tr>
                        <td>模块测试</td>
                        <td>4</td>
                        <td>1</td>
                        <td>80.00%</td>
                    </tr>
                    <tr>
                        <td><strong>总计</strong></td>
                        <td><strong>8</strong></td>
                        <td><strong>2</strong></td>
                        <td><strong>80.00%</strong></td>
                    </tr>
                </table>
            </div>
            
            <h2>脚本测试结果</h2>
            <table>
                <tr>
                    <th>脚本</th>
                    <th>状态</th>
                    <th>错误信息</th>
                </tr>
        
                <tr>
                    <td>run_comprehensive_tests.py</td>
                    <td class="failure">False</td>
                    <td>Command '['C:\\Python313\\python.exe', 'run_comprehensive_tests.py']' returned non-zero exit status 1.</td>
                </tr>
            
                <tr>
                    <td>test_feature_engineering.py</td>
                    <td class="success">True</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>test_trading_environment.py</td>
                    <td class="success">True</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>test_model_training.py</td>
                    <td class="success">True</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>test_ui_components.py</td>
                    <td class="success">True</td>
                    <td></td>
                </tr>
            
            </table>
            
            <h2>模块测试结果</h2>
            <table>
                <tr>
                    <th>模块</th>
                    <th>状态</th>
                    <th>结果摘要</th>
                    <th>错误信息</th>
                </tr>
        
                <tr>
                    <td>run_comprehensive_tests</td>
                    <td class="failure">False</td>
                    <td></td>
                    <td>expected 'except' or 'finally' block (run_comprehensive_tests.py, line 130)</td>
                </tr>
            
                <tr>
                    <td>test_feature_engineering</td>
                    <td class="success">True</td>
                    <td>success: False, error: 无法获取样本数据</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>test_trading_environment</td>
                    <td class="success">True</td>
                    <td>success: False, error: 无法获取处理后的数据</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>test_model_training</td>
                    <td class="success">True</td>
                    <td>success: False, error: 交易环境不可用</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>test_ui_components</td>
                    <td class="success">True</td>
                    <td></td>
                    <td></td>
                </tr>
            
            </table>
            
            <h2>测试结论</h2>
        
            <p>系统测试通过率中等，需要修复部分问题。</p>
            
            <h2>测试详情</h2>
            <p>详细测试结果请查看 test_results 目录下的各个测试结果文件。</p>
            
        </body>
        </html>
        