块 9: 阶段四 (续) (UI集成 - 策略性能评估模块)

本块目标： 将DRL智能体的评估/回测流程与UI的“策略性能评估”模块深度集成，允许用户配置评估参数、启动评估，并以专业、直观的方式展示各项性能指标和可视化结果。

核心任务2：UI集成 – ‘策略性能评估’模块实现

此模块的目标是让用户能通过UI对已训练的DRL智能体进行回测评估，并查看详细结果。

UI功能需求 (‘策略性能评估’ 页面)：
模型选择与加载区：
UI提供下拉菜单或文件浏览器，允许用户选择一个保存在 saved_models/ 目录下的DRL智能体模型文件。
点击“加载模型”按钮后，后端调用 DRLAgent.load_model() 加载智能体。UI显示加载状态。
测试环境配置区：
允许用户选择用于评估/回测的数据集（通常是独立的测试集，其行情数据与特征已通过‘数据中心与环境配置’模块准备好）。
允许用户配置测试环境的参数（如测试期初始资金、手续费率——可从全局配置继承或允许用户覆盖）。
评估/回测控制区：
按钮：“执行评估/回测”。点击后，后端执行以下流程：
根据用户配置实例化一个新的 TradingEnvironment 作为测试环境。
加载的DRL智能体在此测试环境上运行（通常在 deterministic=True 模式下）。
TradingEnvironment 记录交易行为和账户状态变化，生成交易记录和账户净值序列。
将交易记录和净值序列传递给 performance_analyzer.py 的实例进行处理，计算各项指标。
性能指标展示区 (核心区域)：
表格化指标： 清晰、结构化地展示由 PerformanceAnalyzer 计算的所有月度及整体性能指标。
目标对比： 将关键指标（如月均收益、月最大回撤、月夏普）与项目预设的性能目标（月平均收益率5%+，月度最大回撤3%内，月度夏普比率>1.5）进行并列展示，突出达标情况。
可视化结果区：
净值曲线图： 绘制策略的账户净值曲线。允许用户选择一个基准进行对比（例如，买入并持有对应品种的指数；基准数据可通过 data_handler.py 获取或允许用户上传）。图表应支持缩放、平移。
每回合奖励图 (DRL特定)： 展示在测试环境中，智能体每个回合获得的累积奖励变化。
（可选）收益率分布图： 绘制每日、每周或每月收益率的直方图。
（可选）最大回撤时序图： 可视化展示最大回撤的发生时间、深度和持续期。
价格图与交易点标记： 在品种的价格走势图（K线或收盘价线）上，清晰标记出DRL智能体执行的买入点和卖出点。
UI反馈： 评估/回测过程中，UI应显示明确的进度提示。完成后，所有结果区域自动更新。若出错，UI给出清晰错误信息。
模块化测试与验证要求 (UI与评估流程集成后)：

端到端流程测试： 使用一个已成功保存的DRL智能体，通过UI选择模型，配置短测试数据集，执行评估/回测。验证整个流程能否顺利完成，UI能否正确展示所有指标和图表。
指标与图表一致性测试： 核对UI展示的性能指标数值与 PerformanceAnalyzer 模块独立测试时针对相似输入所得的结果是否一致。检查图表是否正确反映回测数据。
错误处理测试： 测试加载不存在或损坏的模型文件、配置无效测试数据集等情况，验证UI的错误提示。
请完成‘策略性能评估’模块的UI实现及其与后端评估逻辑的集成。”

