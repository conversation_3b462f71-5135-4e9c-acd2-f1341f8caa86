#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
核心模块测试脚本
测试量化交易系统的核心模块功能
"""

import os
import sys
import time
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import traceback

# 设置日志
os.makedirs('logs', exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join('logs', 'core_modules_test.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('core_modules_test')

# 导入项目模块
try:
    from core_logic.data_handler import DataHandler
    from core_logic.feature_engineer import FeatureEngineer
    from core_logic.trading_environment import TradingEnvironment
    from core_logic.drl_agent import DRLAgent
    from core_logic.performance_analyzer import PerformanceAnalyzer
    from core_logic.utils import setup_logger, is_gpu_available
    logger.info("成功导入所有核心模块")
except ImportError as e:
    logger.error(f"导入模块失败: {str(e)}")
    sys.exit(1)

def test_module_imports():
    """测试模块导入"""
    logger.info("测试模块导入")
    
    modules = {
        "DataHandler": DataHandler,
        "FeatureEngineer": FeatureEngineer,
        "TradingEnvironment": TradingEnvironment,
        "DRLAgent": DRLAgent,
        "PerformanceAnalyzer": PerformanceAnalyzer
    }
    
    all_imported = True
    for name, module in modules.items():
        if module is not None:
            logger.info(f"成功导入 {name}")
        else:
            logger.error(f"导入 {name} 失败")
            all_imported = False
    
    return all_imported

def test_data_handler():
    """测试数据处理模块"""
    logger.info("测试数据处理模块")
    
    try:
        # 创建数据处理器实例
        data_handler = DataHandler()
        
        # 测试不同股票代码
        stock_codes = ['sh000001', 'sz399001', 'index_000300']
        
        # 测试不同日期范围
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        # 测试不同频率
        frequencies = ['日线', '周线']
        
        results = []
        
        # 测试组合 (减少测试组合以加快测试速度)
        for stock_code in stock_codes[:1]:  # 只测试第一个股票代码
            for frequency in frequencies[:1]:  # 只测试第一个频率
                try:
                    logger.info(f"获取数据: {stock_code}, {start_date} 到 {end_date}, {frequency}")
                    data = data_handler.get_stock_data(
                        stock_code=stock_code,
                        start_date=start_date,
                        end_date=end_date,
                        frequency=frequency
                    )
                    
                    if data is not None and not data.empty:
                        logger.info(f"成功获取数据: {len(data)} 条记录")
                        results.append({
                            'stock_code': stock_code,
                            'start_date': start_date,
                            'end_date': end_date,
                            'frequency': frequency,
                            'success': True,
                            'records': len(data)
                        })
                    else:
                        logger.warning(f"获取数据失败: 数据为空")
                        results.append({
                            'stock_code': stock_code,
                            'start_date': start_date,
                            'end_date': end_date,
                            'frequency': frequency,
                            'success': False,
                            'error': '数据为空'
                        })
                except Exception as e:
                    logger.error(f"获取数据出错: {str(e)}")
                    results.append({
                        'stock_code': stock_code,
                        'start_date': start_date,
                        'end_date': end_date,
                        'frequency': frequency,
                        'success': False,
                        'error': str(e)
                    })
        
        # 测试数据缓存
        logger.info("测试数据缓存")
        cache_test_code = 'sh000001'
        cache_test_start = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        # 第一次获取数据（可能从API获取）
        start_time = time.time()
        data1 = data_handler.get_stock_data(
            stock_code=cache_test_code,
            start_date=cache_test_start,
            end_date=end_date,
            frequency='日线'
        )
        first_fetch_time = time.time() - start_time
        
        # 第二次获取数据（应该从缓存获取）
        start_time = time.time()
        data2 = data_handler.get_stock_data(
            stock_code=cache_test_code,
            start_date=cache_test_start,
            end_date=end_date,
            frequency='日线'
        )
        second_fetch_time = time.time() - start_time
        
        cache_working = second_fetch_time < first_fetch_time
        logger.info(f"缓存测试: 第一次获取时间 {first_fetch_time:.2f}秒, 第二次获取时间 {second_fetch_time:.2f}秒")
        logger.info(f"缓存是否工作: {cache_working}")
        
        # 保存测试结果
        os.makedirs('test_results', exist_ok=True)
        results_df = pd.DataFrame(results)
        results_df.to_csv('test_results/data_handler_test.csv', index=False)
        
        success_rate = results_df['success'].mean() * 100
        logger.info(f"数据处理模块测试完成，成功率: {success_rate:.2f}%")
        
        return {
            'success_rate': success_rate,
            'cache_working': cache_working
        }
    
    except Exception as e:
        logger.error(f"测试数据处理模块时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            'success_rate': 0,
            'cache_working': False
        }

def test_feature_engineer(sample_data):
    """测试特征工程模块"""
    logger.info("测试特征工程模块")
    
    try:
        # 创建特征工程器实例
        feature_engineer = FeatureEngineer()
        
        # 测试基本特征配置
        feature_config = {
            'moving_averages': {'windows': [5, 10, 20]},
            'rsi': {'window': 14},
            'macd': {'fast': 12, 'slow': 26, 'signal': 9}
        }
        
        feature_engineer.feature_config = feature_config
        
        processed_data = feature_engineer.generate_features(sample_data)
        
        if processed_data is not None and not processed_data.empty:
            feature_count = len(processed_data.columns) - len(sample_data.columns)
            logger.info(f"成功生成特征: {feature_count} 个新特征")
            success = True
        else:
            logger.warning(f"生成特征失败: 数据为空")
            success = False
        
        logger.info(f"特征工程模块测试完成，成功: {success}")
        
        return {
            'success': success,
            'feature_count': feature_count if success else 0
        }
    
    except Exception as e:
        logger.error(f"测试特征工程模块时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            'success': False,
            'feature_count': 0
        }

def generate_test_report(results):
    """生成测试报告"""
    logger.info("生成测试报告")
    
    # 创建HTML报告
    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>核心模块测试报告</title>
        <style>
            body {{
                font-family: Arial, sans-serif;
                margin: 20px;
                line-height: 1.6;
            }}
            h1, h2, h3 {{
                color: #333;
            }}
            table {{
                border-collapse: collapse;
                width: 100%;
                margin-bottom: 20px;
            }}
            th, td {{
                border: 1px solid #ddd;
                padding: 8px;
                text-align: left;
            }}
            th {{
                background-color: #f2f2f2;
            }}
            tr:nth-child(even) {{
                background-color: #f9f9f9;
            }}
            .success {{
                color: green;
                font-weight: bold;
            }}
            .failure {{
                color: red;
                font-weight: bold;
            }}
        </style>
    </head>
    <body>
        <h1>核心模块测试报告</h1>
        <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        
        <h2>测试结果</h2>
        <table>
            <tr>
                <th>测试模块</th>
                <th>结果</th>
                <th>详情</th>
            </tr>
    """
    
    # 添加测试结果
    for module, result in results.items():
        if isinstance(result, dict):
            success = result.get('success', False) if 'success' in result else result.get('success_rate', 0) > 50
            details = ", ".join([f"{k}: {v}" for k, v in result.items()])
        else:
            success = result
            details = "通过" if success else "失败"
        
        status_class = 'success' if success else 'failure'
        status_text = '成功' if success else '失败'
        
        html += f"""
            <tr>
                <td>{module}</td>
                <td class="{status_class}">{status_text}</td>
                <td>{details}</td>
            </tr>
        """
    
    html += """
        </table>
        
        <h2>测试结论</h2>
    """
    
    # 计算总体成功率
    success_count = sum(1 for result in results.values() if (isinstance(result, dict) and result.get('success', False)) or result)
    total_count = len(results)
    success_rate = success_count / total_count if total_count > 0 else 0
    
    if success_rate > 0.8:
        html += """
        <p class="success">大部分测试通过！</p>
        """
    else:
        html += """
        <p class="failure">测试通过率较低，需要修复问题。</p>
        """
    
    html += """
    </body>
    </html>
    """
    
    # 写入HTML文件
    os.makedirs('test_results', exist_ok=True)
    report_path = os.path.join('test_results', f'core_modules_test_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.html')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(html)
    
    logger.info(f"测试报告已生成: {report_path}")
    return report_path

def run_all_tests():
    """运行所有测试"""
    logger.info("开始运行所有核心模块测试")
    
    # 确保测试目录存在
    os.makedirs('test_results', exist_ok=True)
    os.makedirs('logs', exist_ok=True)
    
    # 测试模块导入
    module_imports = test_module_imports()
    
    # 获取样本数据用于后续测试
    sample_data = None
    try:
        data_handler = DataHandler()
        sample_data = data_handler.get_stock_data(
            stock_code='sh000001',
            start_date='2023-01-01',
            end_date='2023-12-31',
            frequency='日线'
        )
        logger.info(f"获取样本数据成功: {len(sample_data)} 条记录")
    except Exception as e:
        logger.error(f"获取样本数据失败: {str(e)}")
    
    # 测试数据处理模块
    data_handler_results = test_data_handler()
    
    # 如果有样本数据，测试特征工程模块
    feature_engineer_results = None
    if sample_data is not None and not sample_data.empty:
        feature_engineer_results = test_feature_engineer(sample_data)
    else:
        logger.warning("无法测试特征工程模块: 样本数据不可用")
    
    # 汇总测试结果
    results = {
        '模块导入': module_imports,
        '数据处理模块': data_handler_results,
        '特征工程模块': feature_engineer_results
    }
    
    logger.info(f"所有测试完成，结果: {results}")
    
    # 生成测试报告
    report_path = generate_test_report(results)
    
    return results, report_path

if __name__ == "__main__":
    results, report_path = run_all_tests()
    print(f"\n测试完成！测试报告已生成: {report_path}")
