"""
数据提取和特征工程测试脚本
用于测试数据获取、特征生成和交易环境的功能
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core_logic.data_handler import DataHandler
from core_logic.feature_engineer import FeatureEngineer
from core_logic.trading_environment import TradingEnvironment
from core_logic.utils import setup_logger

# 设置日志
logger = setup_logger(log_file='logs/test.log')

def test_data_extraction():
    """测试数据提取功能"""
    logger.info("开始测试数据提取功能")

    # 创建DataHandler实例
    data_handler = DataHandler()

    # 测试不同股票代码
    stock_codes = ['sh000001', 'sz399001', 'sh600519']

    # 测试不同时间间隔
    time_intervals = [
        # 短期 - 1个月
        {'start': '2023-01-01',
         'end': '2023-01-31'},
        # 中期 - 6个月
        {'start': '2023-01-01',
         'end': '2023-06-30'},
        # 长期 - 1年
        {'start': '2023-01-01',
         'end': '2023-12-31'}
    ]

    # 测试不同频率
    frequencies = ['日线', '周线', '月线']

    results = []

    # 执行测试
    for stock_code in stock_codes:
        for interval in time_intervals:
            for frequency in frequencies:
                try:
                    logger.info(f"获取 {stock_code} 数据，时间区间: {interval['start']} 至 {interval['end']}，频率: {frequency}")

                    # 获取数据
                    data = data_handler.get_stock_data(
                        stock_code=stock_code,
                        start_date=interval['start'],
                        end_date=interval['end'],
                        frequency=frequency
                    )

                    # 记录结果
                    results.append({
                        'stock_code': stock_code,
                        'start_date': interval['start'],
                        'end_date': interval['end'],
                        'frequency': frequency,
                        'data_length': len(data),
                        'success': True,
                        'columns': list(data.columns)
                    })

                    logger.info(f"成功获取数据，共 {len(data)} 条记录")

                except Exception as e:
                    logger.error(f"获取数据失败: {str(e)}")

                    # 记录失败结果
                    results.append({
                        'stock_code': stock_code,
                        'start_date': interval['start'],
                        'end_date': interval['end'],
                        'frequency': frequency,
                        'data_length': 0,
                        'success': False,
                        'error': str(e)
                    })

    # 输出测试结果
    results_df = pd.DataFrame(results)
    print("\n数据提取测试结果:")
    print(results_df[['stock_code', 'frequency', 'data_length', 'success']])

    # 保存测试结果
    results_df.to_csv('tests/data_extraction_results.csv', index=False)

    return results_df

def test_feature_engineering(data=None):
    """测试特征工程功能"""
    logger.info("开始测试特征工程功能")

    if data is None:
        # 如果没有提供数据，获取一个样本数据
        data_handler = DataHandler()
        data = data_handler.get_stock_data(
            stock_code='sh000001',
            start_date='2023-01-01',
            end_date='2023-12-31',
            frequency='日线'
        )

    # 测试不同特征配置
    feature_configs = [
        # 基础配置 - 只使用SMA和RSI
        {
            'sma': {'use': True, 'periods': [5, 20]},
            'ema': {'use': False, 'periods': []},
            'rsi': {'use': True, 'period': 14},
            'macd': {'use': False},
            'bbands': {'use': False},
            'atr': {'use': False}
        },
        # 中级配置 - 使用多种指标
        {
            'sma': {'use': True, 'periods': [5, 20, 60]},
            'ema': {'use': True, 'periods': [5, 20]},
            'rsi': {'use': True, 'period': 14},
            'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9},
            'bbands': {'use': True, 'period': 20, 'std': 2.0},
            'atr': {'use': True, 'period': 14}
        },
        # 高级配置 - 使用所有指标和统计特征
        {
            'sma': {'use': True, 'periods': [5, 10, 20, 60, 120]},
            'ema': {'use': True, 'periods': [5, 10, 20, 60]},
            'rsi': {'use': True, 'period': 14},
            'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9},
            'bbands': {'use': True, 'period': 20, 'std': 2.0},
            'atr': {'use': True, 'period': 14},
            'stoch': {'use': True, 'k_period': 14, 'd_period': 3, 'slowing': 3},
            'volume': {'use': True, 'periods': [5, 20]},
            'rolling_stats': {'use': True, 'window': 20}
        }
    ]

    results = []

    # 执行测试
    for i, config in enumerate(feature_configs):
        try:
            logger.info(f"测试特征配置 {i+1}")

            # 创建FeatureEngineer实例
            feature_engineer = FeatureEngineer(config)

            # 生成特征
            processed_data = feature_engineer.generate_features(data)

            # 删除NaN值
            processed_data = processed_data.dropna()

            # 记录结果
            results.append({
                'config_id': i+1,
                'original_columns': len(data.columns),
                'processed_columns': len(processed_data.columns),
                'new_features': len(processed_data.columns) - len(data.columns),
                'data_length': len(processed_data),
                'success': True,
                'feature_names': list(set(processed_data.columns) - set(data.columns))
            })

            logger.info(f"成功生成特征，原始列数: {len(data.columns)}，处理后列数: {len(processed_data.columns)}")

            # 可视化部分特征
            if i == 1:  # 只为中级配置生成可视化
                plt.figure(figsize=(12, 8))

                # 绘制价格和SMA
                plt.subplot(3, 1, 1)
                plt.plot(processed_data.index, processed_data['收盘'], label='收盘价')
                plt.plot(processed_data.index, processed_data['SMA_20'], label='SMA_20')
                plt.plot(processed_data.index, processed_data['SMA_60'], label='SMA_60')
                plt.title('价格和移动平均线')
                plt.legend()

                # 绘制RSI
                plt.subplot(3, 1, 2)
                plt.plot(processed_data.index, processed_data['RSI_14'], label='RSI_14')
                plt.axhline(y=70, color='r', linestyle='-', alpha=0.3)
                plt.axhline(y=30, color='g', linestyle='-', alpha=0.3)
                plt.title('RSI指标')
                plt.legend()

                # 绘制MACD
                plt.subplot(3, 1, 3)
                plt.plot(processed_data.index, processed_data['MACD_12_26'], label='MACD')
                plt.plot(processed_data.index, processed_data['MACD_Signal_9'], label='Signal')
                plt.bar(processed_data.index, processed_data['MACD_Hist'], label='Histogram', alpha=0.3)
                plt.title('MACD指标')
                plt.legend()

                plt.tight_layout()
                plt.savefig('tests/feature_visualization.png')
                logger.info("特征可视化已保存到 tests/feature_visualization.png")

        except Exception as e:
            logger.error(f"特征生成失败: {str(e)}")

            # 记录失败结果
            results.append({
                'config_id': i+1,
                'original_columns': len(data.columns),
                'processed_columns': 0,
                'new_features': 0,
                'data_length': 0,
                'success': False,
                'error': str(e)
            })

    # 输出测试结果
    results_df = pd.DataFrame(results)
    print("\n特征工程测试结果:")
    print(results_df[['config_id', 'original_columns', 'processed_columns', 'new_features', 'success']])

    # 保存测试结果
    results_df.to_csv('tests/feature_engineering_results.csv', index=False)

    return results_df

def test_trading_environment(data=None):
    """测试交易环境功能"""
    logger.info("开始测试交易环境功能")

    if data is None:
        # 如果没有提供数据，获取并处理样本数据
        data_handler = DataHandler()
        raw_data = data_handler.get_stock_data(
            stock_code='sh000001',
            start_date='2023-01-01',
            end_date='2023-12-31',
            frequency='日线'
        )

        feature_engineer = FeatureEngineer()
        data = feature_engineer.generate_features(raw_data)
        data = data.dropna()

    # 测试不同环境配置
    env_configs = [
        # 基础配置
        {
            'initial_capital': 100000,
            'commission_rate': 0.0003,
            'min_hold_days': 3,
            'window_size': 20
        },
        # 高手续费配置
        {
            'initial_capital': 100000,
            'commission_rate': 0.001,
            'min_hold_days': 3,
            'window_size': 20
        },
        # 长持仓期配置
        {
            'initial_capital': 100000,
            'commission_rate': 0.0003,
            'min_hold_days': 5,
            'window_size': 20
        }
    ]

    results = []

    # 执行测试
    for i, config in enumerate(env_configs):
        try:
            logger.info(f"测试环境配置 {i+1}")

            # 确保数据长度足够
            if len(data) <= config['window_size'] + 10:
                logger.warning(f"数据长度不足，需要至少 {config['window_size'] + 10} 条数据，当前只有 {len(data)} 条")
                # 创建一个更长的模拟数据
                dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
                mock_data = pd.DataFrame({
                    '开盘': np.random.rand(len(dates)) * 100 + 3000,
                    '最高': np.random.rand(len(dates)) * 100 + 3050,
                    '最低': np.random.rand(len(dates)) * 100 + 2950,
                    '收盘': np.random.rand(len(dates)) * 100 + 3000,
                    '成交量': np.random.rand(len(dates)) * 1000000,
                    'SMA_5': np.random.rand(len(dates)) * 100 + 3000,
                    'SMA_20': np.random.rand(len(dates)) * 100 + 3000,
                    'RSI_14': np.random.rand(len(dates)) * 100
                }, index=dates)
                data = mock_data

            # 创建环境
            env = TradingEnvironment(
                df_processed_data=data,
                initial_capital=config['initial_capital'],
                commission_rate=config['commission_rate'],
                min_hold_days=config['min_hold_days'],
                window_size=config['window_size']
            )

            # 重置环境
            observation, info = env.reset()

            # 执行一些随机动作
            actions = [0, 1, 0, 0, 2, 0, 1, 0, 0, 0, 2]  # 简单的动作序列

            # 记录每步的状态
            states = []
            rewards = []
            portfolio_values = []

            # 执行动作序列
            for action in actions:
                observation, reward, terminated, truncated, info = env.step(action)

                states.append(observation)
                rewards.append(reward)
                portfolio_values.append(info['portfolio_value'])

                if terminated or truncated:
                    break

            # 获取交易记录和组合价值历史
            trades = env.get_trades_history()
            portfolio_history = env.get_portfolio_history()

            # 记录结果
            results.append({
                'config_id': i+1,
                'initial_capital': config['initial_capital'],
                'commission_rate': config['commission_rate'],
                'min_hold_days': config['min_hold_days'],
                'steps_executed': len(rewards),
                'final_portfolio_value': portfolio_values[-1] if portfolio_values else config['initial_capital'],
                'total_trades': len(trades),
                'success': True
            })

            logger.info(f"环境测试成功，最终组合价值: {portfolio_values[-1] if portfolio_values else config['initial_capital']}")

            # 为第一个配置生成可视化
            if i == 0 and portfolio_history is not None:
                plt.figure(figsize=(10, 6))
                plt.plot(portfolio_history.index, portfolio_history.values)
                plt.title('组合价值变化')
                plt.xlabel('日期')
                plt.ylabel('价值')
                plt.grid(True)
                plt.savefig('tests/portfolio_value.png')
                logger.info("组合价值可视化已保存到 tests/portfolio_value.png")

        except Exception as e:
            logger.error(f"环境测试失败: {str(e)}")

            # 记录失败结果
            results.append({
                'config_id': i+1,
                'initial_capital': config['initial_capital'],
                'commission_rate': config['commission_rate'],
                'min_hold_days': config['min_hold_days'],
                'steps_executed': 0,
                'final_portfolio_value': config['initial_capital'],
                'total_trades': 0,
                'success': False,
                'error': str(e)
            })

    # 输出测试结果
    results_df = pd.DataFrame(results)
    print("\n交易环境测试结果:")
    print(results_df[['config_id', 'initial_capital', 'commission_rate', 'min_hold_days', 'final_portfolio_value', 'total_trades', 'success']])

    # 保存测试结果
    results_df.to_csv('tests/trading_environment_results.csv', index=False)

    return results_df

if __name__ == "__main__":
    # 确保测试目录存在
    os.makedirs('tests', exist_ok=True)

    # 运行测试
    print("开始全面测试数据提取、特征工程和交易环境功能...")

    # 测试数据提取
    extraction_results = test_data_extraction()

    # 获取一个样本数据用于后续测试
    data_handler = DataHandler()
    sample_data = data_handler.get_stock_data(
        stock_code='sh000001',
        start_date='2023-01-01',
        end_date='2023-12-31',
        frequency='日线'
    )

    # 测试特征工程
    feature_results = test_feature_engineering(sample_data)

    # 处理样本数据
    feature_engineer = FeatureEngineer()
    processed_data = feature_engineer.generate_features(sample_data)
    processed_data = processed_data.dropna()

    # 测试交易环境
    env_results = test_trading_environment(processed_data)

    print("\n测试完成！详细结果已保存到tests目录。")
