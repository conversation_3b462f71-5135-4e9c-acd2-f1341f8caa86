"""
回测模块适配器
为了保持向后兼容性，这个文件从重构后的模块中导入相应的类
"""

import logging
import warnings

# 导入重构后的模块
from quant_project.core_logic.backtest.backtest_engine import BacktestEngine
from quant_project.core_logic.backtest.performance_metrics import PerformanceMetrics
from quant_project.core_logic.backtest.visualization import BacktestVisualizer
from quant_project.core_logic.backtest.optimization import StrategyOptimizer
from quant_project.core_logic.backtest.report_generator import ReportGenerator

# 设置日志
logger = logging.getLogger('drl_trading')

# 发出重构警告
warnings.warn(
    "使用旧的回测模块路径，建议直接从quant_project.core_logic.backtest包中导入相应的类",
    DeprecationWarning,
    stacklevel=2
)

# 导出所有类供向后兼容使用
__all__ = [
    'BacktestEngine',
    'PerformanceMetrics',
    'BacktestVisualizer',
    'StrategyOptimizer',
    'ReportGenerator'
]

# 以下函数为向后兼容提供，推荐直接使用BacktestEngine类

def run_backtest(data, strategy, initial_capital=100000.0, commission_rate=0.0003, 
                slippage=0.0, benchmark_symbol=None):
    """
    运行回测的兼容函数

    参数:
        data: 回测数据
        strategy: 策略对象
        initial_capital: 初始资金
        commission_rate: 佣金率
        slippage: 滑点
        benchmark_symbol: 基准标的

    返回:
        dict: 回测结果
    """
    warnings.warn(
        "使用旧的run_backtest函数，建议直接使用BacktestEngine类",
        DeprecationWarning,
        stacklevel=2
    )
    
    engine = BacktestEngine(
        initial_capital=initial_capital,
        commission_rate=commission_rate,
        slippage=slippage
    )
    
    return engine.run_backtest(data, strategy, benchmark_symbol)

def evaluate_performance(portfolio_values, trades=None, benchmark_values=None, risk_free_rate=0.0):
    """
    评估性能的兼容函数

    参数:
        portfolio_values: 组合价值序列
        trades: 交易记录
        benchmark_values: 基准价值序列
        risk_free_rate: 无风险利率

    返回:
        dict: 性能指标
    """
    warnings.warn(
        "使用旧的evaluate_performance函数，建议直接使用PerformanceMetrics类",
        DeprecationWarning,
        stacklevel=2
    )
    
    metrics = PerformanceMetrics(risk_free_rate=risk_free_rate)
    return metrics.calculate_metrics(portfolio_values, trades, benchmark_values)

def plot_performance(portfolio_values, benchmark_values=None, title="策略表现", save_path=None):
    """
    绘制性能图表的兼容函数

    参数:
        portfolio_values: 组合价值序列
        benchmark_values: 基准价值序列
        title: 图表标题
        save_path: 保存路径

    返回:
        matplotlib.figure.Figure: 图表对象
    """
    warnings.warn(
        "使用旧的plot_performance函数，建议直接使用BacktestVisualizer类",
        DeprecationWarning,
        stacklevel=2
    )
    
    visualizer = BacktestVisualizer()
    return visualizer.plot_portfolio_performance(
        portfolio_values, 
        benchmark_values=benchmark_values,
        title=title,
        save_path=save_path
    )

def optimize_strategy(strategy_class, data, param_grid, fixed_params=None, 
                    eval_metric='sharpe_ratio', maximize=True):
    """
    优化策略参数的兼容函数

    参数:
        strategy_class: 策略类
        data: 回测数据
        param_grid: 参数网格
        fixed_params: 固定参数
        eval_metric: 评估指标
        maximize: 是否最大化指标

    返回:
        pandas.DataFrame: 优化结果
    """
    warnings.warn(
        "使用旧的optimize_strategy函数，建议直接使用StrategyOptimizer类",
        DeprecationWarning,
        stacklevel=2
    )
    
    optimizer = StrategyOptimizer(
        strategy_class=strategy_class,
        data=data,
        eval_metric=eval_metric,
        maximize=maximize
    )
    
    return optimizer.grid_search(param_grid, fixed_params) 