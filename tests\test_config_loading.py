"""
配置文件加载测试脚本
用于测试配置文件的加载和验证功能
"""

import os
import sys
import yaml
import pandas as pd
import numpy as np
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core_logic.utils import setup_logger

# 设置日志
logger = setup_logger(log_file='logs/test_config_loading.log')

def load_config(config_path):
    """加载配置文件"""
    logger.info(f"加载配置文件: {config_path}")
    
    try:
        with open(config_path, 'r', encoding='utf-8') as file:
            config = yaml.safe_load(file)
        
        logger.info(f"配置文件加载成功: {config_path}")
        return config
    
    except Exception as e:
        logger.error(f"配置文件加载失败: {str(e)}")
        return None

def validate_env_config(config):
    """验证环境配置"""
    logger.info("验证环境配置")
    
    # 必需的配置项
    required_keys = [
        'initial_capital',
        'commission_rate',
        'min_hold_days',
        'allow_short',
        'max_position',
        'window_size',
        'reward_config'
    ]
    
    # 验证必需的配置项是否存在
    missing_keys = [key for key in required_keys if key not in config]
    
    if missing_keys:
        logger.error(f"环境配置缺少必需的配置项: {missing_keys}")
        return False
    
    # 验证配置项的类型和值
    validation_results = {}
    
    # 初始资金应该是正数
    validation_results['initial_capital'] = isinstance(config['initial_capital'], (int, float)) and config['initial_capital'] > 0
    
    # 手续费率应该在0到1之间
    validation_results['commission_rate'] = isinstance(config['commission_rate'], (int, float)) and 0 <= config['commission_rate'] <= 1
    
    # 最小持仓天数应该是正整数
    validation_results['min_hold_days'] = isinstance(config['min_hold_days'], int) and config['min_hold_days'] > 0
    
    # 是否允许做空应该是布尔值
    validation_results['allow_short'] = isinstance(config['allow_short'], bool)
    
    # 最大仓位比例应该在0到1之间
    validation_results['max_position'] = isinstance(config['max_position'], (int, float)) and 0 < config['max_position'] <= 1
    
    # 观测窗口大小应该是正整数
    validation_results['window_size'] = isinstance(config['window_size'], int) and config['window_size'] > 0
    
    # 奖励函数配置应该是字典
    validation_results['reward_config'] = isinstance(config['reward_config'], dict)
    
    # 验证结果
    all_valid = all(validation_results.values())
    
    if all_valid:
        logger.info("环境配置验证通过")
    else:
        invalid_keys = [k for k, v in validation_results.items() if not v]
        logger.error(f"以下环境配置项验证失败: {invalid_keys}")
    
    return all_valid

def validate_agent_config(config):
    """验证智能体配置"""
    logger.info("验证智能体配置")
    
    # 必需的配置项
    required_keys = [
        'algorithm',
        'policy',
        'learning_rate',
        'gamma',
        'n_steps',
        'batch_size'
    ]
    
    # 验证必需的配置项是否存在
    missing_keys = [key for key in required_keys if key not in config]
    
    if missing_keys:
        logger.error(f"智能体配置缺少必需的配置项: {missing_keys}")
        return False
    
    # 验证配置项的类型和值
    validation_results = {}
    
    # 算法应该是字符串
    validation_results['algorithm'] = isinstance(config['algorithm'], str)
    
    # 策略应该是字符串
    validation_results['policy'] = isinstance(config['policy'], str)
    
    # 学习率应该是正数
    validation_results['learning_rate'] = isinstance(config['learning_rate'], (int, float)) and config['learning_rate'] > 0
    
    # gamma应该在0到1之间
    validation_results['gamma'] = isinstance(config['gamma'], (int, float)) and 0 <= config['gamma'] <= 1
    
    # n_steps应该是正整数
    validation_results['n_steps'] = isinstance(config['n_steps'], int) and config['n_steps'] > 0
    
    # batch_size应该是正整数
    validation_results['batch_size'] = isinstance(config['batch_size'], int) and config['batch_size'] > 0
    
    # 验证结果
    all_valid = all(validation_results.values())
    
    if all_valid:
        logger.info("智能体配置验证通过")
    else:
        invalid_keys = [k for k, v in validation_results.items() if not v]
        logger.error(f"以下智能体配置项验证失败: {invalid_keys}")
    
    return all_valid

def test_config_loading():
    """测试配置文件加载功能"""
    logger.info("开始测试配置文件加载功能")
    
    # 加载环境配置
    env_config_path = "configs/env_config.yaml"
    env_config = load_config(env_config_path)
    
    # 加载智能体配置
    agent_config_path = "configs/drl_agent_config.yaml"
    agent_config = load_config(agent_config_path)
    
    # 验证配置是否成功加载
    if not env_config:
        logger.error(f"加载环境配置失败: {env_config_path}")
        return False
    
    if not agent_config:
        logger.error(f"加载智能体配置失败: {agent_config_path}")
        return False
    
    # 验证配置内容
    env_config_valid = validate_env_config(env_config)
    agent_config_valid = validate_agent_config(agent_config)
    
    # 汇总测试结果
    all_valid = env_config_valid and agent_config_valid
    
    logger.info(f"配置文件加载测试完成，测试结果: {'全部通过' if all_valid else '部分失败'}")
    
    # 保存测试结果
    results = {
        '环境配置加载': env_config is not None,
        '智能体配置加载': agent_config is not None,
        '环境配置验证': env_config_valid,
        '智能体配置验证': agent_config_valid,
        '总体结果': all_valid
    }
    
    results_df = pd.DataFrame([results])
    results_df.to_csv('tests/config_loading_results.csv', index=False)
    
    return all_valid

if __name__ == "__main__":
    test_config_loading()
