"""
市场状态检测模块
负责检测和分类不同的市场状态（牛市、熊市、震荡市场等）
用于算法鲁棒性验证和风险管理
"""

import numpy as np
import pandas as pd
from enum import Enum
import logging
from scipy import stats
from statsmodels.tsa.stattools import adfuller
import warnings

# 忽略特定警告
warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", category=RuntimeWarning)

class MarketCondition(Enum):
    """市场状态枚举"""
    BULL = 1       # 牛市
    BEAR = 2       # 熊市
    SIDEWAYS = 3   # 震荡市场
    VOLATILE = 4   # 高波动市场
    CALM = 5       # 低波动市场
    TRENDING = 6   # 趋势市场
    MEAN_REVERTING = 7  # 均值回归市场
    CRISIS = 8     # 危机市场

class MarketConditionDetector:
    """
    市场状态检测类
    负责检测和分类不同的市场状态
    """

    def __init__(self, logger=None):
        """
        初始化市场状态检测器

        参数:
            logger (logging.Logger): 日志记录器
        """
        self.logger = logger or logging.getLogger('drl_trading')

    def detect_market_condition(self, data, window_size=60):
        """
        检测市场状态

        参数:
            data (pandas.DataFrame): 金融数据，必须包含'收盘'列
            window_size (int): 用于检测市场状态的窗口大小

        返回:
            dict: 市场状态分析结果
        """
        if data is None or data.empty or len(data) < window_size:
            self.logger.warning(f"数据长度不足，无法检测市场状态，需要至少 {window_size} 个数据点")
            return {
                'primary_condition': MarketCondition.SIDEWAYS,
                'conditions': {},
                'metrics': {}
            }

        # 复制数据，避免修改原始数据
        df = data.copy()

        # 确保收盘价列存在
        if '收盘' not in df.columns:
            self.logger.error("数据中缺少'收盘'列，无法检测市场状态")
            return {
                'primary_condition': MarketCondition.SIDEWAYS,
                'conditions': {},
                'metrics': {}
            }

        # 计算收益率
        df['收益率'] = df['收盘'].pct_change()
        df['对数收益率'] = np.log(df['收盘'] / df['收盘'].shift(1))

        # 计算市场状态指标
        metrics = self._calculate_market_metrics(df, window_size)

        # 根据指标确定市场状态
        conditions = self._determine_market_conditions(metrics)

        # 确定主要市场状态
        primary_condition = self._determine_primary_condition(conditions)

        return {
            'primary_condition': primary_condition,
            'conditions': conditions,
            'metrics': metrics
        }

    def _calculate_market_metrics(self, df, window_size):
        """
        计算市场状态指标

        参数:
            df (pandas.DataFrame): 包含收益率的数据
            window_size (int): 窗口大小

        返回:
            dict: 市场指标
        """
        # 使用最近的window_size个数据点
        recent_data = df.iloc[-window_size:].copy()

        # 计算趋势指标
        recent_data['sma_short'] = recent_data['收盘'].rolling(window=20).mean()
        recent_data['sma_long'] = recent_data['收盘'].rolling(window=50).mean()
        trend_strength = (recent_data['sma_short'].iloc[-1] / recent_data['sma_long'].iloc[-1] - 1) * 100

        # 计算波动率指标
        volatility = recent_data['收益率'].std() * np.sqrt(252)  # 年化波动率
        
        # 计算偏度和峰度
        skew = stats.skew(recent_data['收益率'].dropna())
        kurt = stats.kurtosis(recent_data['收益率'].dropna())

        # 计算回撤
        rolling_max = recent_data['收盘'].rolling(window=window_size, min_periods=1).max()
        drawdown = (rolling_max - recent_data['收盘']) / rolling_max
        max_drawdown = drawdown.max()

        # 计算趋势与均值回归特性
        # 使用Hurst指数来衡量时间序列的持续性
        # H > 0.5表示趋势持续性，H < 0.5表示均值回归
        hurst_exponent = self._calculate_hurst_exponent(recent_data['收盘'])

        # 计算自相关性
        autocorr = recent_data['收益率'].autocorr(lag=1)

        # 计算平稳性（ADF检验）
        try:
            adf_result = adfuller(recent_data['收盘'].dropna())
            is_stationary = adf_result[1] < 0.05  # p值小于0.05表示序列是平稳的
        except:
            is_stationary = False

        # 计算上涨天数比例
        up_days_ratio = (recent_data['收益率'] > 0).mean()

        # 计算极端收益率的频率
        extreme_threshold = 2 * volatility / np.sqrt(252)  # 日波动率的2倍
        extreme_days_ratio = (abs(recent_data['收益率']) > extreme_threshold).mean()

        return {
            'trend_strength': trend_strength,
            'volatility': volatility,
            'skew': skew,
            'kurt': kurt,
            'max_drawdown': max_drawdown,
            'hurst_exponent': hurst_exponent,
            'autocorr': autocorr,
            'is_stationary': is_stationary,
            'up_days_ratio': up_days_ratio,
            'extreme_days_ratio': extreme_days_ratio
        }

    def _determine_market_conditions(self, metrics):
        """
        根据指标确定市场状态

        参数:
            metrics (dict): 市场指标

        返回:
            dict: 各种市场状态的可能性（0-1之间的值）
        """
        conditions = {}

        # 牛市条件：正趋势强度，较低波动率，上涨天数比例高
        bull_score = (
            self._normalize(metrics['trend_strength'], 5, 20) * 0.5 +
            (1 - self._normalize(metrics['volatility'], 0.15, 0.4)) * 0.2 +
            self._normalize(metrics['up_days_ratio'], 0.5, 0.7) * 0.3
        )
        conditions[MarketCondition.BULL] = max(0, min(1, bull_score))

        # 熊市条件：负趋势强度，较高波动率，上涨天数比例低，大回撤
        bear_score = (
            self._normalize(-metrics['trend_strength'], 5, 20) * 0.4 +
            self._normalize(metrics['volatility'], 0.2, 0.4) * 0.2 +
            (1 - self._normalize(metrics['up_days_ratio'], 0.3, 0.5)) * 0.2 +
            self._normalize(metrics['max_drawdown'], 0.1, 0.3) * 0.2
        )
        conditions[MarketCondition.BEAR] = max(0, min(1, bear_score))

        # 震荡市场条件：趋势强度接近0，中等波动率，上涨天数比例接近0.5
        sideways_score = (
            (1 - abs(self._normalize(metrics['trend_strength'], -5, 5))) * 0.5 +
            (1 - abs(self._normalize(metrics['up_days_ratio'] - 0.5, -0.1, 0.1))) * 0.3 +
            (1 - self._normalize(metrics['volatility'], 0.05, 0.25)) * 0.2
        )
        conditions[MarketCondition.SIDEWAYS] = max(0, min(1, sideways_score))

        # 高波动市场条件：高波动率，极端天数比例高
        volatile_score = (
            self._normalize(metrics['volatility'], 0.25, 0.5) * 0.7 +
            self._normalize(metrics['extreme_days_ratio'], 0.05, 0.2) * 0.3
        )
        conditions[MarketCondition.VOLATILE] = max(0, min(1, volatile_score))

        # 低波动市场条件：低波动率，极端天数比例低
        calm_score = (
            (1 - self._normalize(metrics['volatility'], 0.05, 0.15)) * 0.7 +
            (1 - self._normalize(metrics['extreme_days_ratio'], 0.01, 0.05)) * 0.3
        )
        conditions[MarketCondition.CALM] = max(0, min(1, calm_score))

        # 趋势市场条件：Hurst指数高，自相关性高
        trending_score = (
            self._normalize(metrics['hurst_exponent'], 0.5, 0.7) * 0.7 +
            self._normalize(abs(metrics['autocorr']), 0.1, 0.3) * 0.3
        )
        conditions[MarketCondition.TRENDING] = max(0, min(1, trending_score))

        # 均值回归市场条件：Hurst指数低，自相关性为负，平稳性高
        mean_reverting_score = (
            (1 - self._normalize(metrics['hurst_exponent'], 0.3, 0.5)) * 0.5 +
            self._normalize(-metrics['autocorr'], 0, 0.3) * 0.3 +
            (1 if metrics['is_stationary'] else 0) * 0.2
        )
        conditions[MarketCondition.MEAN_REVERTING] = max(0, min(1, mean_reverting_score))

        # 危机市场条件：极高波动率，大幅回撤，负偏度，高峰度
        crisis_score = (
            self._normalize(metrics['volatility'], 0.4, 0.8) * 0.3 +
            self._normalize(metrics['max_drawdown'], 0.2, 0.5) * 0.3 +
            self._normalize(-metrics['skew'], 0.5, 2) * 0.2 +
            self._normalize(metrics['kurt'], 3, 10) * 0.2
        )
        conditions[MarketCondition.CRISIS] = max(0, min(1, crisis_score))

        return conditions

    def _determine_primary_condition(self, conditions):
        """
        确定主要市场状态

        参数:
            conditions (dict): 各种市场状态的可能性

        返回:
            MarketCondition: 主要市场状态
        """
        # 找出得分最高的市场状态
        primary_condition = max(conditions.items(), key=lambda x: x[1])[0]
        return primary_condition

    def _normalize(self, value, min_val, max_val):
        """
        将值归一化到0-1范围

        参数:
            value (float): 要归一化的值
            min_val (float): 最小值
            max_val (float): 最大值

        返回:
            float: 归一化后的值
        """
        if max_val == min_val:
            return 0.5
        return max(0, min(1, (value - min_val) / (max_val - min_val)))

    def _calculate_hurst_exponent(self, ts, max_lag=20):
        """
        计算Hurst指数

        参数:
            ts (pandas.Series): 时间序列
            max_lag (int): 最大滞后期

        返回:
            float: Hurst指数
        """
        try:
            # 确保没有缺失值
            ts = ts.dropna()
            if len(ts) < max_lag * 2:
                return 0.5  # 数据不足，返回默认值

            # 计算不同滞后期的标准差
            lags = range(2, max_lag)
            tau = [np.std(np.subtract(ts.values[lag:], ts.values[:-lag])) for lag in lags]
            
            # 计算Hurst指数
            reg = np.polyfit(np.log(lags), np.log(tau), 1)
            return reg[0] / 2.0
        except Exception as e:
            self.logger.warning(f"计算Hurst指数失败: {str(e)}")
            return 0.5  # 返回默认值
