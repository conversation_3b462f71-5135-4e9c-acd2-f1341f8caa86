作为一名资深的AI量化项目测试工程师，您的任务是全面测试当前量化交易项目的可用性、稳定性和核心功能的正确性。请严格按照以下步骤和要求执行测试，并提供详细的测试报告。

**项目概览与目标：**

本项目是一个基于Python的量化交易系统，核心功能包括数据处理、特征工程、交易策略回测、风险管理和性能评估。测试的目标是确保系统在各种场景下都能稳定运行，数据处理准确无误，交易逻辑符合预期，风险控制有效，并且性能指标计算正确。

**测试范围与重点：**

1.  **环境配置与依赖检查：**
    *   验证项目是否能在标准Python环境中正确安装所有依赖项（参照 `requirements.txt`）。
    *   检查Python版本兼容性。
    *   测试GPU支持是否按预期工作（如果项目包含GPU加速模块，如 `install_gpu_support.py`）。

2.  **数据处理模块测试 (`core_logic/data_handling.py`, `core_logic/feature_engineer.py`)：**
    *   **数据获取与缓存：** 测试从数据源（如AkShare）获取数据的稳定性和准确性，以及数据缓存机制的有效性（检查 `data_cache` 目录）。
    *   **数据清洗与预处理：** 验证对原始数据的清洗（缺失值、异常值处理）和预处理（如复权、标准化）的正确性。
    *   **特征工程：**
        *   验证各类技术指标（SMA, EMA, RSI, MACD, Bollinger Bands, ATR等）计算的准确性，与已知库（如 `ta` 库）或手动计算结果进行对比。
        *   测试特征配置的灵活性和正确性（`feature_config`）。
        *   检查特征归一化和标准化的效果。
    *   **数据质量验证 (`EnhancedDataValidator`)：** 运行数据质量检查脚本，确保能发现并报告数据问题。

3.  **核心交易逻辑与回测环境测试 (`core_logic/trading_environment.py`, `RobustTradingEnvironment`)：**
    *   **交易环境初始化：** 测试使用不同参数（初始资金、手续费率、最小持仓天数、滑点等）初始化交易环境的正确性。
    *   **交易指令执行：**
        *   验证买入、卖出、持仓等操作的正确性。
        *   严格测试交易约束条件：收盘价成交、最小持仓天数限制、无杠杆交易等（参考 `test_trading_environment_constraints.py`）。
    *   **状态观测与奖励机制：** 检查环境返回的观测值（observation）和奖励（reward）是否符合预期设计。
    *   **Gymnasium API兼容性：** 使用 `check_env` 验证环境是否符合Gymnasium标准API。

4.  **模型训练与加载测试 (涉及 `saved_models` 目录和相关脚本如 `check_models.py`, `simple_model_test.py`)：**
    *   测试模型的保存和加载功能是否正常。
    *   验证加载后的模型是否能正确用于回测或预测。
    *   检查模型清理机制（`test_model_cleanup.py`）。

5.  **风险管理模块测试 (`core_logic/risk_management.py`)：**
    *   **止损策略：** 测试不同类型的止损策略（固定比例、波动率止损、移动止损、时间止损）的触发条件和执行效果。
    *   **仓位管理：** 验证不同的仓位管理方法（固定金额、固定比例、波动率调整、凯利公式）的计算和应用是否正确。
    *   **市场状态适应性：** 测试风险管理策略是否能根据不同的市场状态（牛市、熊市、震荡市）进行调整。

6.  **性能评估模块测试 (`core_logic/performance_analyzer.py`)：**
    *   **性能指标计算：** 验证各项性能指标（总收益率、年化收益率、夏普比率、索提诺比率、最大回撤、胜率、盈亏比等）计算的准确性。
    *   **与基准对比：** 测试策略表现与基准指数对比分析的正确性。
    *   **交易记录分析：** 验证对交易列表的统计分析（如平均持仓周期、交易频率等）是否准确。
    *   **图表绘制：** 检查性能报告和相关图表（如净值曲线、回撤曲线）的生成和正确性（参考 `plots` 目录）。

7.  **鲁棒性与验证模块测试 (`core_logic/validation.py`, `test_robust_modules.py`)：**
    *   **市场状态检测 (`MarketConditionDetector`)：** 验证市场状态分类的准确性和稳定性。
    *   **时间序列交叉验证 (`TimeSeriesCV`, `MarketConditionCV`)：** 测试交叉验证机制的划分是否合理，能否有效评估模型在不同时间段的表现。
    *   **过拟合检测 (`OverfittingDetector`)：** 验证过拟合检测机制的有效性。

8.  **脚本与执行流程测试：**
    *   **主运行脚本 (`run_project.py`, `main_app.py`, `simple_run.py`)：** 测试项目的完整执行流程是否顺畅，有无报错。
    *   **测试脚本 (`run_all_tests.py`)：** 确保所有单元测试和集成测试都能通过。
    *   **配置文件加载 (`configs` 目录, `test_config_loading.py`)：** 测试配置文件的读取和解析是否正确。

9.  **日志与错误处理 (`logs` 目录)：**
    *   检查日志记录是否全面、清晰，能否帮助定位问题。
    *   测试系统在遇到异常数据或错误操作时的容错能力和错误提示。

10. **用户界面 (UI) 测试 (如果项目包含UI部分，如 `ui_components`, `test_streamlit.py`)：**
    *   测试UI的布局、交互和数据展示是否符合预期。
    *   验证UI操作能否正确触发后端逻辑。

**测试方法与策略：**

*   **单元测试：** 针对核心函数和类方法编写并执行单元测试。
*   **集成测试：** 测试模块间的交互和数据流转。
*   **端到端测试：** 模拟用户从数据输入到结果输出的完整流程。
*   **边界条件测试：** 测试系统在极端或临界输入值下的表现。
*   **异常处理测试：** 故意引入错误数据或操作，检查系统的错误处理能力。
*   **回归测试：** 在代码修改后，重新运行相关测试，确保原有功能未受影响。

**预期交付物：**

1.  **详细的测试计划：** 说明测试范围、方法、资源和时间安排。
2.  **测试用例集：** 包含每个测试点的具体步骤、输入数据、预期结果和实际结果。
3.  **缺陷报告：** 记录发现的所有缺陷，包括复现步骤、严重程度和建议修复方案。
4.  **测试总结报告：** 概述测试过程、覆盖率、发现的问题、项目整体质量评估以及改进建议。

**注意事项：**


*   在测试过程中，如果发现任何与最佳实践不符或可能导致潜在风险的设计，请一并指出。
*   优先测试核心功能和高风险模块。


请开始您的测试工作，期待您的专业报告！