"""
数据获取模块
负责从AkShare获取金融数据
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import pytz

try:
    import akshare as ak
except ImportError:
    logging.error("未安装AkShare库，请使用 pip install akshare 安装")

class DataFetcher:
    """
    数据获取类
    负责从AkShare获取金融数据
    """

    def __init__(self, logger=None, timezone='Asia/Shanghai'):
        """
        初始化数据获取器

        参数:
            logger (logging.Logger): 日志记录器
            timezone (str): 时区设置，默认为'Asia/Shanghai'
        """
        self.logger = logger or logging.getLogger('drl_trading')
        self.timezone = pytz.timezone(timezone)

        # 指数代码映射表
        self.index_map = {
            # 上证系列指数
            '000001': {'name': '上证指数', 'prefix': 'sh'},
            '000016': {'name': '上证50', 'prefix': 'sh'},
            '000300': {'name': '沪深300', 'prefix': 'sh'},
            '000905': {'name': '中证500', 'prefix': 'sh'},
            '000852': {'name': '中证1000', 'prefix': 'sh'},
            # 深证系列指数
            '399001': {'name': '深证成指', 'prefix': 'sz'},
            '399006': {'name': '创业板指', 'prefix': 'sz'},
            '399673': {'name': '创业板50', 'prefix': 'sz'},
            # 其他重要指数
            '000688': {'name': '科创50', 'prefix': 'sh'},
            '399324': {'name': '深证红利', 'prefix': 'sz'}
        }

    def fetch_stock_data(self, stock_code, start_date, end_date, frequency='日线'):
        """
        获取股票数据

        参数:
            stock_code (str): 股票代码，格式如 'sh000001' 或 'sz399001'
            start_date (str): 开始日期，格式为 'YYYY-MM-DD'
            end_date (str): 结束日期，格式为 'YYYY-MM-DD'
            frequency (str): 数据频率，可选 '日线', '周线', '月线', '小时线', '分钟线'

        返回:
            pandas.DataFrame: 股票数据
        """
        # 解析代码类型和具体代码
        if stock_code.startswith('sh') or stock_code.startswith('sz'):
            market = stock_code[:2]
            code = stock_code[2:]
        else:
            # 默认当作股票处理
            market = 'sh' if stock_code.startswith('6') else 'sz'
            code = stock_code
            self.logger.warning(f"未识别的代码格式: {stock_code}，将当作股票处理")

        self.logger.info(f"获取股票数据: {market}{code}, {start_date} 至 {end_date}, 频率: {frequency}")

        # 根据市场选择不同的API
        try:
            if market == 'sh':
                # 上海证券交易所股票
                self.logger.info(f"获取上海证券交易所股票: {code}")
                try:
                    # 首先尝试使用stock_zh_index_daily_em，这个API不依赖PyMiniRacer
                    self.logger.info(f"尝试使用stock_zh_index_daily_em获取数据: sh{code}")
                    data = ak.stock_zh_index_daily_em(symbol=f"sh{code}")
                    self.logger.info(f"使用stock_zh_index_daily_em获取到的数据列名: {list(data.columns)}")
                except Exception as inner_e:
                    self.logger.error(f"使用stock_zh_index_daily_em获取数据失败: {str(inner_e)}")
                    try:
                        # 如果失败，尝试使用stock_zh_a_daily
                        self.logger.info(f"尝试使用stock_zh_a_daily获取数据: {code}")
                        data = ak.stock_zh_a_daily(symbol=f"{code}", start_date=start_date, end_date=end_date, adjust="qfq")
                        self.logger.info(f"获取到的数据列名: {list(data.columns)}")
                    except Exception as e2:
                        self.logger.error(f"使用stock_zh_a_daily获取数据失败: {str(e2)}")
                        # 尝试使用其他API
                        self.logger.info(f"尝试使用stock_zh_index_daily获取数据: sh{code}")
                        data = ak.stock_zh_index_daily(symbol=f"sh{code}")
                        self.logger.info(f"使用stock_zh_index_daily获取到的数据列名: {list(data.columns)}")
            elif market == 'sz':
                # 深圳证券交易所股票
                self.logger.info(f"获取深圳证券交易所股票: {code}")
                try:
                    # 首先尝试使用stock_zh_index_daily_em，这个API不依赖PyMiniRacer
                    self.logger.info(f"尝试使用stock_zh_index_daily_em获取数据: sz{code}")
                    data = ak.stock_zh_index_daily_em(symbol=f"sz{code}")
                    self.logger.info(f"使用stock_zh_index_daily_em获取到的数据列名: {list(data.columns)}")
                except Exception as inner_e:
                    self.logger.error(f"使用stock_zh_index_daily_em获取数据失败: {str(inner_e)}")
                    try:
                        # 如果失败，尝试使用stock_zh_a_daily
                        self.logger.info(f"尝试使用stock_zh_a_daily获取数据: {code}")
                        data = ak.stock_zh_a_daily(symbol=f"{code}", start_date=start_date, end_date=end_date, adjust="qfq")
                        self.logger.info(f"获取到的数据列名: {list(data.columns)}")
                    except Exception as e2:
                        self.logger.error(f"使用stock_zh_a_daily获取数据失败: {str(e2)}")
                        # 尝试使用其他API
                        self.logger.info(f"尝试使用stock_zh_index_daily获取数据: sz{code}")
                        data = ak.stock_zh_index_daily(symbol=f"sz{code}")
                        self.logger.info(f"使用stock_zh_index_daily获取到的数据列名: {list(data.columns)}")
            else:
                raise ValueError(f"不支持的市场代码: {market}，股票代码应以'sh'或'sz'开头")

            return data
        except Exception as e:
            self.logger.error(f"获取股票数据失败: {str(e)}")
            error_message = f"获取股票数据失败: {str(e)}\n"
            error_message += "股票代码格式说明:\n"
            error_message += "- 上海证券交易所股票: sh + 6位数字，如 sh600000\n"
            error_message += "- 深圳证券交易所股票: sz + 6位数字，如 sz000001\n"
            raise ValueError(error_message)

    def fetch_index_data(self, index_code, start_date, end_date, frequency='日线'):
        """
        获取指数数据

        参数:
            index_code (str): 指数代码，格式如 'index_000001' 或 'sh000001'
            start_date (str): 开始日期，格式为 'YYYY-MM-DD'
            end_date (str): 结束日期，格式为 'YYYY-MM-DD'
            frequency (str): 数据频率，可选 '日线', '周线', '月线', '小时线', '分钟线'

        返回:
            pandas.DataFrame: 指数数据
        """
        # 解析指数代码
        if index_code.startswith('index_'):
            code = index_code[6:]
        else:
            code = index_code

        self.logger.info(f"获取指数数据: {code}, {start_date} 至 {end_date}, 频率: {frequency}")

        # 检查是否是已知的指数代码
        if code in self.index_map:
            index_info = self.index_map[code]
            prefix = index_info['prefix']
            index_name = index_info['name']
            self.logger.info(f"识别到已知指数: {index_name} ({prefix}{code})")
            full_code = f"{prefix}{code}"
        else:
            # 尝试根据代码前缀判断
            if code.startswith('000'):
                prefix = 'sh'
                full_code = f"{prefix}{code}"
                self.logger.info(f"根据前缀判断为上证系列指数: {full_code}")
            elif code.startswith('399'):
                prefix = 'sz'
                full_code = f"{prefix}{code}"
                self.logger.info(f"根据前缀判断为深证系列指数: {full_code}")
            elif code.startswith('sh') or code.startswith('sz'):
                # 已经包含前缀
                full_code = code
                self.logger.info(f"指数代码已包含前缀: {full_code}")
            else:
                # 无法判断，尝试直接使用
                full_code = code
                self.logger.info(f"无法识别的指数代码格式，将直接使用: {full_code}")

        # 系统性尝试多种接口获取指数数据
        data = None
        error_messages = []

        # 方法1: 使用东方财富指数行情接口
        try:
            self.logger.info(f"尝试使用东方财富指数行情接口获取: {full_code}")
            data = ak.stock_zh_index_daily_em(symbol=full_code)

            if data is not None and not data.empty:
                self.logger.info(f"成功使用东方财富指数行情接口获取数据，共 {len(data)} 条记录")
            else:
                error_messages.append("东方财富指数行情接口返回空数据")
                data = None
        except Exception as e:
            error_msg = f"使用东方财富指数行情接口失败: {str(e)}"
            self.logger.warning(error_msg)
            error_messages.append(error_msg)

        # 方法2: 如果方法1失败，尝试使用新浪财经指数行情接口
        if data is None or data.empty:
            try:
                self.logger.info(f"尝试使用新浪财经指数行情接口获取: {code}")
                data = ak.stock_zh_index_daily(symbol=full_code)

                if data is not None and not data.empty:
                    self.logger.info(f"成功使用新浪财经指数行情接口获取数据，共 {len(data)} 条记录")
                else:
                    error_messages.append("新浪财经指数行情接口返回空数据")
                    data = None
            except Exception as e:
                error_msg = f"使用新浪财经指数行情接口失败: {str(e)}"
                self.logger.warning(error_msg)
                error_messages.append(error_msg)

        # 方法3: 如果方法2失败，尝试使用A股指数历史行情接口
        if data is None or data.empty:
            try:
                # 移除可能的前缀
                clean_code = code
                if clean_code.startswith('sh') or clean_code.startswith('sz'):
                    clean_code = clean_code[2:]

                self.logger.info(f"尝试使用A股指数历史行情接口获取: {clean_code}")
                # 转换日期格式为YYYYMMDD
                start_date_fmt = start_date.replace('-', '')
                end_date_fmt = end_date.replace('-', '')

                data = ak.index_zh_a_hist(symbol=clean_code, period="daily",
                                         start_date=start_date_fmt, end_date=end_date_fmt)

                if data is not None and not data.empty:
                    self.logger.info(f"成功使用A股指数历史行情接口获取数据，共 {len(data)} 条记录")
                else:
                    error_messages.append("A股指数历史行情接口返回空数据")
                    data = None
            except Exception as e:
                error_msg = f"使用A股指数历史行情接口失败: {str(e)}"
                self.logger.warning(error_msg)
                error_messages.append(error_msg)

        # 如果所有方法都失败，尝试使用上证指数作为替代
        if data is None or data.empty:
            error_detail = "\n".join(error_messages)
            error_message = f"未能获取到指数 {code} 的数据。尝试了以下方法但均失败:\n{error_detail}\n"
            error_message += "请检查指数代码是否正确，或尝试以下格式:\n"
            error_message += "- 上证系列指数: sh000001(上证指数), sh000300(沪深300), sh000016(上证50), sh000905(中证500)\n"
            error_message += "- 深证系列指数: sz399001(深证成指), sz399006(创业板指), sz399673(创业板50)\n"
            error_message += "- 科创板指数: sh000688(科创50)\n"

            self.logger.warning(error_message)
            self.logger.info("尝试使用上证指数(sh000001)作为替代")

            try:
                # 尝试获取上证指数数据作为替代
                data = ak.stock_zh_index_daily_em(symbol="sh000001")
                if data is not None and not data.empty:
                    self.logger.info(f"成功获取上证指数数据作为替代，共 {len(data)} 条记录")
                else:
                    raise ValueError("获取上证指数数据失败")
            except Exception as fallback_e:
                self.logger.error(f"获取上证指数数据失败: {str(fallback_e)}")
                raise ValueError(error_message)

        return data
