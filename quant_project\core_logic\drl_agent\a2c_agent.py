"""
A2C智能体模块
实现基于优势动作-价值(A2C)算法的强化学习智能体
"""

import os
import logging
import numpy as np
from stable_baselines3 import A2C
import torch

from quant_project.core_logic.drl_agent.agent_base import DRLAgentBase
from quant_project.core_logic.utils import is_gpu_available

class A2CAgent(DRLAgentBase):
    """
    A2C智能体类
    基于优势动作-价值(Advantage Actor-Critic)算法
    """

    def __init__(self, env_config, agent_config, hpo_config=None):
        """
        初始化A2C智能体

        参数:
            env_config (dict): 交易环境配置
            agent_config (dict): 智能体配置
            hpo_config (dict, optional): 超参数优化配置
        """
        # 确保算法设置为A2C
        if agent_config is None:
            agent_config = {}
        agent_config['algorithm'] = 'A2C'
        
        super().__init__(env_config, agent_config, hpo_config)
        
        # 创建A2C模型
        self.model = self._create_model()
        
    def _create_model(self):
        """
        创建A2C模型

        返回:
            stable_baselines3.A2C: A2C模型实例
        """
        # 从智能体配置中提取参数
        policy = self.agent_config.get('policy_network', 'MlpPolicy')
        learning_rate = self.agent_config.get('learning_rate', 0.0007)
        gamma = self.agent_config.get('gamma', 0.99)
        
        # 检查是否有GPU可用
        device = 'cuda' if is_gpu_available() and self.agent_config.get('use_gpu', True) else 'cpu'
        self.logger.info(f"使用设备: {device}")
        
        # 创建A2C模型
        model = A2C(
            policy=policy,
            env=self.env,
            learning_rate=learning_rate,
            gamma=gamma,
            n_steps=self.agent_config.get('n_steps', 5),
            ent_coef=self.agent_config.get('ent_coef', 0.01),
            vf_coef=self.agent_config.get('vf_coef', 0.5),
            max_grad_norm=self.agent_config.get('max_grad_norm', 0.5),
            use_rms_prop=self.agent_config.get('use_rms_prop', True),
            normalize_advantage=self.agent_config.get('normalize_advantage', False),
            verbose=1,
            device=device
        )
        
        return model
    
    @classmethod
    def load_model(cls, model_path, eval_env_config=None):
        """
        加载A2C模型

        参数:
            model_path (str): 模型路径
            eval_env_config (dict, optional): 评估环境配置

        返回:
            A2CAgent: 加载的智能体实例
        """
        # 检查模型文件是否存在
        if not os.path.exists(model_path):
            logging.error(f"模型文件不存在: {model_path}")
            return None
            
        try:
            # 创建评估环境配置
            if eval_env_config is None:
                eval_env_config = {}
                
            # 提取算法信息，默认为A2C
            algorithm = 'A2C'
            
            # 创建智能体配置
            agent_config = {
                'algorithm': algorithm,
                'use_gpu': is_gpu_available()
            }
            
            # 创建智能体实例
            agent = cls(eval_env_config, agent_config)
            
            # 加载模型
            agent.model = A2C.load(model_path, env=agent.env)
            logging.info(f"成功加载模型: {model_path}")
            
            return agent
            
        except Exception as e:
            logging.error(f"加载模型时出错: {str(e)}")
            return None
            
    def optimize_hyperparameters(self, n_trials=50, n_startup_trials=10, n_evaluations=2, n_timesteps=50000):
        """
        优化超参数

        参数:
            n_trials (int): 试验次数
            n_startup_trials (int): 启动试验次数
            n_evaluations (int): 每次试验的评估次数
            n_timesteps (int): 每次评估的时间步数

        返回:
            dict: 优化结果
        """
        try:
            import optuna
            from optuna.pruners import MedianPruner
            from optuna.samplers import TPESampler
        except ImportError:
            self.logger.error("无法导入optuna，请安装: pip install optuna")
            return {"status": "error", "message": "未安装optuna库"}
            
        self.logger.info(f"开始A2C超参数优化，试验次数: {n_trials}")
        
        # 创建optuna研究对象
        study = optuna.create_study(
            sampler=TPESampler(n_startup_trials=n_startup_trials),
            pruner=MedianPruner(n_startup_trials=n_startup_trials // 2),
            direction="maximize"
        )
        
        def objective(trial):
            # 采样超参数
            params = {
                'learning_rate': trial.suggest_float('learning_rate', 1e-5, 1e-2, log=True),
                'gamma': trial.suggest_float('gamma', 0.9, 0.9999),
                'n_steps': trial.suggest_int('n_steps', 3, 30),
                'ent_coef': trial.suggest_float('ent_coef', 0.0, 0.1),
                'vf_coef': trial.suggest_float('vf_coef', 0.1, 0.9),
                'max_grad_norm': trial.suggest_float('max_grad_norm', 0.1, 2.0),
                'use_rms_prop': trial.suggest_categorical('use_rms_prop', [True, False]),
                'normalize_advantage': trial.suggest_categorical('normalize_advantage', [True, False])
            }
            
            # 更新智能体配置
            for key, value in params.items():
                self.agent_config[key] = value
                
            # 重新创建环境和模型
            self.env = self._create_environment()
            self.model = self._create_model()
            
            # 执行多次评估
            mean_rewards = []
            
            for _ in range(n_evaluations):
                # 训练模型
                self.model.learn(total_timesteps=n_timesteps)
                
                # 评估模型
                mean_reward, _ = self.model.evaluate(self.env, n_eval_episodes=10)
                mean_rewards.append(mean_reward)
                
                # 报告进度
                trial.report(mean_reward, _)
                
                # 如果需要修剪
                if trial.should_prune():
                    raise optuna.TrialPruned()
                    
            return np.mean(mean_rewards)
            
        # 开始超参数优化
        try:
            study.optimize(objective, n_trials=n_trials)
            
            # 获取最佳参数
            best_params = study.best_params
            best_value = study.best_value
            
            self.logger.info(f"超参数优化完成，最佳奖励: {best_value:.4f}")
            self.logger.info(f"最佳参数: {best_params}")
            
            # 使用最佳参数更新智能体配置
            for key, value in best_params.items():
                self.agent_config[key] = value
                
            # 重新创建环境和模型
            self.env = self._create_environment()
            self.model = self._create_model()
            
            return {
                "status": "success",
                "best_params": best_params,
                "best_value": best_value
            }
            
        except Exception as e:
            self.logger.error(f"超参数优化过程中出错: {str(e)}")
            return {"status": "error", "message": str(e)} 