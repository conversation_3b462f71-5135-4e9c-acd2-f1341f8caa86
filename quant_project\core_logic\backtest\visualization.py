"""
回测可视化模块
提供回测结果可视化功能
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib.ticker import FuncFormatter
import seaborn as sns
from typing import Dict, List, Union, Tuple, Optional, Any
import logging

class BacktestVisualizer:
    """
    回测可视化类
    提供回测结果可视化功能
    """

    def __init__(self, figsize: Tuple[int, int] = (12, 8), style: str = 'seaborn-v0_8-darkgrid'):
        """
        初始化回测可视化器

        参数:
            figsize (tuple): 图表大小
            style (str): Matplotlib样式
        """
        self.logger = logging.getLogger('drl_trading')
        self.figsize = figsize
        self.style = style
        
        # 设置默认样式
        plt.style.use(self.style)
        
        # 设置中文字体支持（如果可用）
        try:
            plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
            plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
        except Exception as e:
            self.logger.warning(f"设置中文字体失败: {str(e)}")

    def plot_portfolio_performance(self, portfolio_values: pd.Series, 
                                 benchmark_values: Optional[pd.Series] = None,
                                 title: str = '策略表现',
                                 save_path: Optional[str] = None,
                                 show_drawdown: bool = True) -> plt.Figure:
        """
        绘制组合表现图

        参数:
            portfolio_values (pandas.Series): 组合价值序列，索引为日期
            benchmark_values (pandas.Series, optional): 基准价值序列，索引为日期
            title (str): 图表标题
            save_path (str, optional): 保存路径，如果不为None则保存图表
            show_drawdown (bool): 是否显示回撤图

        返回:
            matplotlib.figure.Figure: 图表对象
        """
        # 确保索引是日期类型
        if not isinstance(portfolio_values.index, pd.DatetimeIndex):
            try:
                portfolio_values.index = pd.to_datetime(portfolio_values.index)
            except Exception as e:
                self.logger.error(f"无法将索引转换为日期类型: {str(e)}")
                return plt.figure(figsize=self.figsize)
        
        if benchmark_values is not None and not isinstance(benchmark_values.index, pd.DatetimeIndex):
            try:
                benchmark_values.index = pd.to_datetime(benchmark_values.index)
            except Exception as e:
                self.logger.warning(f"无法将基准索引转换为日期类型: {str(e)}")
                benchmark_values = None
        
        # 归一化价值序列（起始值为100）
        normalized_portfolio = portfolio_values / portfolio_values.iloc[0] * 100
        
        if benchmark_values is not None:
            normalized_benchmark = benchmark_values / benchmark_values.iloc[0] * 100
        
        # 计算回撤序列
        portfolio_drawdown = self._calculate_drawdown_series(portfolio_values)
        
        if show_drawdown:
            # 创建带有两个子图的画布
            fig, (ax1, ax2) = plt.subplots(2, 1, figsize=self.figsize, gridspec_kw={'height_ratios': [3, 1]}, sharex=True)
        else:
            # 创建单一子图的画布
            fig, ax1 = plt.subplots(figsize=self.figsize)
        
        # 绘制组合价值曲线
        ax1.plot(normalized_portfolio.index, normalized_portfolio, label='策略', linewidth=2)
        
        # 如果有基准，绘制基准价值曲线
        if benchmark_values is not None:
            ax1.plot(normalized_benchmark.index, normalized_benchmark, label='基准', linewidth=2, alpha=0.7)
        
        # 设置x轴为日期格式
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax1.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
        
        # 计算年度收益率标记位置
        annual_returns = self._calculate_annual_returns(portfolio_values)
        
        # 在图表上添加年度收益率标记
        for year, ret in annual_returns.items():
            # 找到每年的最后一个交易日
            try:
                year_end = portfolio_values[portfolio_values.index.year == int(year)].index[-1]
                value = normalized_portfolio[year_end]
                ax1.annotate(f'{ret:.1%}', xy=(year_end, value), xytext=(0, 10),
                            textcoords='offset points', ha='center', va='bottom',
                            bbox=dict(boxstyle='round,pad=0.3', fc='yellow', alpha=0.5))
            except Exception as e:
                self.logger.warning(f"添加{year}年收益率标记失败: {str(e)}")
        
        # 设置图例和标题
        ax1.legend(loc='upper left')
        ax1.set_title(title)
        ax1.set_ylabel('价值 (初始值=100)')
        ax1.grid(True)
        
        # 设置y轴为百分比格式
        ax1.yaxis.set_major_formatter(FuncFormatter(lambda y, _: f'{y:.0f}'))
        
        if show_drawdown:
            # 在第二个子图绘制回撤曲线
            ax2.fill_between(portfolio_drawdown.index, 0, portfolio_drawdown.values * 100,
                            color='red', alpha=0.3, label='回撤')
            ax2.set_ylabel('回撤 (%)')
            ax2.set_ylim(portfolio_drawdown.min() * 100 * 1.1, 0)  # 扩大y轴范围，使图形更清晰
            ax2.grid(True)
            
            # 设置y轴为百分比格式（负值）
            ax2.yaxis.set_major_formatter(FuncFormatter(lambda y, _: f'{y:.0f}%'))
            
            # 显示最大回撤标记
            max_drawdown_idx = portfolio_drawdown.idxmin()
            max_drawdown = portfolio_drawdown.min() * 100
            ax2.annotate(f'最大回撤: {max_drawdown:.1f}%', 
                        xy=(max_drawdown_idx, max_drawdown),
                        xytext=(0, -20), textcoords='offset points',
                        arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0.3'),
                        bbox=dict(boxstyle='round,pad=0.3', fc='white', alpha=0.8))
        
        fig.tight_layout()
        
        # 如果指定了保存路径，保存图表
        if save_path:
            try:
                fig.savefig(save_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"图表已保存至 {save_path}")
            except Exception as e:
                self.logger.error(f"保存图表失败: {str(e)}")
        
        return fig

    def plot_returns_distribution(self, portfolio_values: pd.Series,
                                benchmark_values: Optional[pd.Series] = None,
                                title: str = '收益分布',
                                save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制收益分布图

        参数:
            portfolio_values (pandas.Series): 组合价值序列，索引为日期
            benchmark_values (pandas.Series, optional): 基准价值序列，索引为日期
            title (str): 图表标题
            save_path (str, optional): 保存路径，如果不为None则保存图表

        返回:
            matplotlib.figure.Figure: 图表对象
        """
        # 计算日收益率
        returns = portfolio_values.pct_change().dropna()
        
        if benchmark_values is not None:
            benchmark_returns = benchmark_values.pct_change().dropna()
        else:
            benchmark_returns = None
        
        # 创建画布
        fig, ax = plt.subplots(figsize=self.figsize)
        
        # 绘制收益分布直方图
        sns.histplot(returns, kde=True, ax=ax, color='blue', alpha=0.6, label='策略')
        
        if benchmark_returns is not None:
            sns.histplot(benchmark_returns, kde=True, ax=ax, color='red', alpha=0.4, label='基准')
        
        # 添加均值线
        ax.axvline(returns.mean(), color='blue', linestyle='dashed', linewidth=2, label=f'策略均值: {returns.mean():.2%}')
        
        if benchmark_returns is not None:
            ax.axvline(benchmark_returns.mean(), color='red', linestyle='dashed', linewidth=2, 
                      label=f'基准均值: {benchmark_returns.mean():.2%}')
        
        # 设置x轴为百分比格式
        ax.xaxis.set_major_formatter(FuncFormatter(lambda x, _: f'{x:.1%}'))
        
        # 设置图例和标题
        ax.legend()
        ax.set_title(title)
        ax.set_xlabel('日收益率')
        ax.set_ylabel('频率')
        ax.grid(True, alpha=0.3)
        
        # 如果指定了保存路径，保存图表
        if save_path:
            try:
                fig.savefig(save_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"图表已保存至 {save_path}")
            except Exception as e:
                self.logger.error(f"保存图表失败: {str(e)}")
        
        return fig

    def plot_rolling_metrics(self, portfolio_values: pd.Series,
                           metrics: List[str] = ['sharpe', 'volatility', 'drawdown'],
                           window: int = 60,
                           title: str = '滚动性能指标',
                           save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制滚动性能指标图

        参数:
            portfolio_values (pandas.Series): 组合价值序列，索引为日期
            metrics (list): 要绘制的指标列表，可选值包括'sharpe', 'volatility', 'drawdown', 'sortino', 'calmar'
            window (int): 滚动窗口大小，默认为60个交易日
            title (str): 图表标题
            save_path (str, optional): 保存路径，如果不为None则保存图表

        返回:
            matplotlib.figure.Figure: 图表对象
        """
        # 计算日收益率
        returns = portfolio_values.pct_change().dropna()
        
        # 创建画布，根据指标数量确定子图数量
        fig, axes = plt.subplots(len(metrics), 1, figsize=self.figsize, sharex=True)
        
        # 如果只有一个指标，确保axes是一个列表
        if len(metrics) == 1:
            axes = [axes]
        
        # 计算并绘制每个指标
        for i, metric in enumerate(metrics):
            ax = axes[i]
            
            if metric.lower() == 'sharpe':
                # 计算滚动夏普比率
                rolling_sharpe = returns.rolling(window=window).mean() / returns.rolling(window=window).std() * np.sqrt(252)
                ax.plot(rolling_sharpe.index, rolling_sharpe, label=f'{window}日滚动夏普比率', color='green')
                ax.axhline(y=0, color='r', linestyle='-', alpha=0.3)
                ax.set_ylabel('夏普比率')
                
            elif metric.lower() == 'volatility':
                # 计算滚动波动率
                rolling_vol = returns.rolling(window=window).std() * np.sqrt(252)
                ax.plot(rolling_vol.index, rolling_vol, label=f'{window}日滚动波动率', color='orange')
                ax.set_ylabel('波动率 (年化)')
                
            elif metric.lower() == 'drawdown':
                # 计算滚动最大回撤
                def rolling_max_drawdown(x):
                    cumulative_returns = (1 + x).cumprod()
                    peak = cumulative_returns.expanding().max()
                    drawdown = (cumulative_returns - peak) / peak
                    return drawdown.min()
                
                rolling_dd = returns.rolling(window=window).apply(rolling_max_drawdown, raw=False)
                ax.plot(rolling_dd.index, rolling_dd * 100, label=f'{window}日滚动最大回撤', color='red')
                ax.set_ylabel('最大回撤 (%)')
                
            elif metric.lower() == 'sortino':
                # 计算滚动索提诺比率
                def downside_risk(x):
                    negative_returns = x[x < 0]
                    return negative_returns.std() * np.sqrt(252) if len(negative_returns) > 0 else 1e-10
                
                rolling_downside_risk = returns.rolling(window=window).apply(downside_risk, raw=False)
                rolling_sortino = returns.rolling(window=window).mean() / rolling_downside_risk * np.sqrt(252)
                ax.plot(rolling_sortino.index, rolling_sortino, label=f'{window}日滚动索提诺比率', color='purple')
                ax.axhline(y=0, color='r', linestyle='-', alpha=0.3)
                ax.set_ylabel('索提诺比率')
                
            elif metric.lower() == 'calmar':
                # 计算滚动卡玛比率
                def rolling_calmar(x):
                    cumulative_returns = (1 + x).cumprod()
                    peak = cumulative_returns.expanding().max()
                    drawdown = (cumulative_returns - peak) / peak
                    max_dd = abs(drawdown.min())
                    annual_return = x.mean() * 252
                    return annual_return / max_dd if max_dd > 0 else annual_return
                
                rolling_calmar = returns.rolling(window=window).apply(rolling_calmar, raw=False)
                ax.plot(rolling_calmar.index, rolling_calmar, label=f'{window}日滚动卡玛比率', color='brown')
                ax.axhline(y=0, color='r', linestyle='-', alpha=0.3)
                ax.set_ylabel('卡玛比率')
            
            ax.legend(loc='upper left')
            ax.grid(True, alpha=0.3)
            
            # 设置x轴为日期格式
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
        
        # 设置总标题
        fig.suptitle(title)
        fig.tight_layout()
        fig.subplots_adjust(top=0.95)
        
        # 如果指定了保存路径，保存图表
        if save_path:
            try:
                fig.savefig(save_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"图表已保存至 {save_path}")
            except Exception as e:
                self.logger.error(f"保存图表失败: {str(e)}")
        
        return fig

    def plot_monthly_returns_heatmap(self, portfolio_values: pd.Series,
                                   title: str = '月度收益热图',
                                   save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制月度收益热图

        参数:
            portfolio_values (pandas.Series): 组合价值序列，索引为日期
            title (str): 图表标题
            save_path (str, optional): 保存路径，如果不为None则保存图表

        返回:
            matplotlib.figure.Figure: 图表对象
        """
        # 确保索引是日期类型
        if not isinstance(portfolio_values.index, pd.DatetimeIndex):
            try:
                portfolio_values.index = pd.to_datetime(portfolio_values.index)
            except Exception as e:
                self.logger.error(f"无法将索引转换为日期类型: {str(e)}")
                return plt.figure(figsize=self.figsize)
        
        # 计算日收益率
        returns = portfolio_values.pct_change().dropna()
        
        # 创建月度收益表
        monthly_returns = returns.groupby([returns.index.year, returns.index.month]).apply(
            lambda x: (1 + x).prod() - 1
        )
        
        # 将结果转换为透视表格式
        monthly_returns = monthly_returns.unstack()
        
        # 创建画布
        fig, ax = plt.subplots(figsize=self.figsize)
        
        # 使用seaborn绘制热图
        cmap = sns.diverging_palette(10, 220, as_cmap=True)  # 红绿配色
        heatmap = sns.heatmap(monthly_returns, annot=True, fmt='.1%', cmap=cmap, center=0,
                            cbar_kws={'label': '月度收益率'}, ax=ax)
        
        # 设置x轴标签为月份
        month_names = ['一月', '二月', '三月', '四月', '五月', '六月', 
                     '七月', '八月', '九月', '十月', '十一月', '十二月']
        ax.set_xticklabels(month_names)
        
        # 设置y轴为年份
        ax.set_yticklabels(monthly_returns.index, rotation=0)
        
        # 设置标题
        ax.set_title(title)
        
        # 计算并添加年度总收益
        yearly_returns = returns.groupby(returns.index.year).apply(lambda x: (1 + x).prod() - 1)
        
        for i, year in enumerate(monthly_returns.index):
            ax.text(len(month_names) + 0.5, i + 0.5, f'{yearly_returns[year]:.1%}',
                   va='center', ha='center', fontweight='bold', fontsize=10,
                   bbox=dict(boxstyle='round,pad=0.3', fc='yellow', alpha=0.5))
        
        # 添加"全年"标签
        ax.text(len(month_names) + 0.5, -0.5, '全年',
               va='center', ha='center', fontweight='bold')
        
        # 调整布局
        fig.tight_layout()
        
        # 如果指定了保存路径，保存图表
        if save_path:
            try:
                fig.savefig(save_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"图表已保存至 {save_path}")
            except Exception as e:
                self.logger.error(f"保存图表失败: {str(e)}")
        
        return fig

    def plot_trade_analysis(self, trades_df: pd.DataFrame,
                          title: str = '交易分析',
                          save_path: Optional[str] = None) -> plt.Figure:
        """
        绘制交易分析图

        参数:
            trades_df (pandas.DataFrame): 交易记录DataFrame，必须包含date, type, amount列
            title (str): 图表标题
            save_path (str, optional): 保存路径，如果不为None则保存图表

        返回:
            matplotlib.figure.Figure: 图表对象
        """
        if trades_df.empty:
            self.logger.warning("交易记录为空，无法绘制交易分析图")
            return plt.figure(figsize=self.figsize)
        
        # 确保必要的列存在
        required_columns = ['date', 'type', 'amount']
        if not all(col in trades_df.columns for col in required_columns):
            self.logger.error(f"交易记录缺少必要的列: {required_columns}")
            return plt.figure(figsize=self.figsize)
        
        # 创建画布，2x2布局
        fig, axes = plt.subplots(2, 2, figsize=self.figsize)
        
        # 1. 交易类型分布（买入/卖出）
        trade_types = trades_df['type'].value_counts()
        axes[0, 0].pie(trade_types, labels=trade_types.index, autopct='%1.1f%%',
                      colors=['green', 'red'], startangle=90)
        axes[0, 0].set_title('交易类型分布')
        
        # 2. 交易金额随时间变化
        trades_df_sorted = trades_df.sort_values('date')
        buy_trades = trades_df_sorted[trades_df_sorted['type'] == 'buy']
        sell_trades = trades_df_sorted[trades_df_sorted['type'] == 'sell']
        
        axes[0, 1].bar(buy_trades['date'], buy_trades['amount'], color='green', alpha=0.7, label='买入')
        axes[0, 1].bar(sell_trades['date'], sell_trades['amount'], color='red', alpha=0.7, label='卖出')
        axes[0, 1].legend()
        axes[0, 1].set_title('交易金额随时间变化')
        axes[0, 1].set_xlabel('日期')
        axes[0, 1].set_ylabel('交易金额')
        axes[0, 1].xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        axes[0, 1].xaxis.set_major_locator(mdates.MonthLocator(interval=3))
        
        # 3. 每月交易次数
        if 'date' in trades_df.columns and isinstance(trades_df['date'].iloc[0], (pd.Timestamp, np.datetime64)):
            monthly_trades = trades_df.groupby(pd.Grouper(key='date', freq='M')).size()
            axes[1, 0].bar(monthly_trades.index, monthly_trades, color='blue', alpha=0.7)
            axes[1, 0].set_title('每月交易次数')
            axes[1, 0].set_xlabel('月份')
            axes[1, 0].set_ylabel('交易次数')
            axes[1, 0].xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            axes[1, 0].xaxis.set_major_locator(mdates.MonthLocator(interval=3))
        else:
            axes[1, 0].text(0.5, 0.5, '无法绘制每月交易次数图表\n日期列格式不正确',
                           ha='center', va='center', fontsize=12)
            axes[1, 0].set_title('每月交易次数')
        
        # 4. 交易金额分布
        axes[1, 1].hist(trades_df['amount'], bins=20, color='purple', alpha=0.7)
        axes[1, 1].set_title('交易金额分布')
        axes[1, 1].set_xlabel('交易金额')
        axes[1, 1].set_ylabel('频率')
        
        # 设置总标题
        fig.suptitle(title, fontsize=16)
        fig.tight_layout()
        fig.subplots_adjust(top=0.9)
        
        # 如果指定了保存路径，保存图表
        if save_path:
            try:
                fig.savefig(save_path, dpi=300, bbox_inches='tight')
                self.logger.info(f"图表已保存至 {save_path}")
            except Exception as e:
                self.logger.error(f"保存图表失败: {str(e)}")
        
        return fig

    def _calculate_drawdown_series(self, portfolio_values: pd.Series) -> pd.Series:
        """
        计算回撤序列

        参数:
            portfolio_values (pandas.Series): 组合价值序列

        返回:
            pandas.Series: 回撤序列
        """
        # 计算累计最大值
        peak = portfolio_values.expanding().max()
        # 计算回撤
        drawdown = (portfolio_values - peak) / peak
        return drawdown

    def _calculate_annual_returns(self, portfolio_values: pd.Series) -> Dict[str, float]:
        """
        计算年度收益率

        参数:
            portfolio_values (pandas.Series): 组合价值序列

        返回:
            dict: 年度收益率字典 {年份: 收益率}
        """
        annual_returns = {}
        
        for year in sorted(set(portfolio_values.index.year)):
            year_data = portfolio_values[portfolio_values.index.year == year]
            if len(year_data) >= 2:
                annual_return = (year_data.iloc[-1] / year_data.iloc[0]) - 1
                annual_returns[str(year)] = annual_return
        
        return annual_returns 