"""
因子管理页面组件
实现因子挖掘、评估和管理功能
"""

import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import logging
import time
from datetime import datetime, timedelta

logger = logging.getLogger('drl_trading')

def render_factor_management():
    """渲染因子管理页面"""
    st.markdown("## 🧩 因子管理")
    
    # 确保必要的会话状态
    if 'factor_mining_results' not in st.session_state:
        st.session_state.factor_mining_results = None
    if 'best_factors' not in st.session_state:
        st.session_state.best_factors = None
    if 'mining_logs' not in st.session_state:
        st.session_state.mining_logs = []
    
    # 使用选项卡组织不同功能
    tabs = st.tabs(["因子挖掘", "因子评估", "因子应用"])
    
    with tabs[0]:
        st.markdown("### 因子挖掘配置")
        
        # 检查是否已加载数据
        if 'data' not in st.session_state or st.session_state.data is None:
            st.warning("请先在数据中心加载数据")
            st.button("跳转到数据中心", on_click=lambda: setattr(st.session_state, 'current_page', "数据中心"))
            return
        
        # 因子挖掘表单
        with st.form("factor_mining_form"):
            st.markdown("#### 基本配置")
            
            # 两列布局
            col1, col2 = st.columns(2)
            
            with col1:
                min_ic = st.slider("最小IC绝对值", min_value=0.01, max_value=0.2, value=0.05, step=0.01,
                                 help="IC值越高表示因子与未来收益相关性越强")
                
                corr_threshold = st.slider("相关性阈值", min_value=0.5, max_value=0.95, value=0.7, step=0.05,
                                         help="用于过滤高度相关的因子，保留IC值更高的因子")
            
            with col2:
                top_n = st.slider("保留因子数量", min_value=5, max_value=50, value=20, step=5,
                                help="最终选择的顶级因子数量")
                
                use_parallel = st.checkbox("启用并行计算", value=False, 
                                         help="利用多核CPU加速计算，但可能增加内存使用")
            
            st.markdown("#### 因子类型")
            col1, col2, col3 = st.columns(3)
            
            with col1:
                use_tech = st.checkbox("技术指标因子", value=True,
                                     help="包括MACD, RSI, KDJ等技术指标")
            
            with col2:
                use_cross = st.checkbox("交叉因子", value=True,
                                      help="不同指标间的交叉比较")
            
            with col3:
                use_time = st.checkbox("时间序列因子", value=True,
                                     help="价格、成交量等的时间序列变换")
            
            submitted = st.form_submit_button("开始挖掘因子", use_container_width=True)
            
            if submitted:
                # 显示进度条
                progress_bar = st.progress(0)
                status_text = st.empty()
                
                try:
                    # 加载自动因子挖掘模块
                    try:
                        from core_logic.factor_mining import AutoFactorPipeline
                    except ImportError:
                        from core_logic.auto_factor_mining import AutoFactorPipeline
                    
                    # 创建数据处理器（用于传递给AutoFactorPipeline）
                    try:
                        from core_logic.data_handling import DataHandler
                        from core_logic.data_handling.adapter import DataHandlerAdapter
                        data_handler = DataHandlerAdapter()
                    except ImportError:
                        from core_logic.data_handler import DataHandler
                        data_handler = DataHandler()
                    
                    # 创建因子挖掘流水线
                    pipeline = AutoFactorPipeline(data_handler)
                    
                    # 配置流水线
                    pipeline.configure(
                        min_ic_abs=min_ic,
                        corr_threshold=corr_threshold,
                        top_n_factors=top_n,
                        generate_technical_factors=use_tech,
                        generate_cross_factors=use_cross,
                        generate_time_factors=use_time,
                        enable_parallel=use_parallel,
                        verbose=True
                    )
                    
                    # 进度回调函数
                    def progress_callback(stage, progress, message):
                        # 更新进度条和状态文本
                        progress_bar.progress(progress)
                        status_text.text(f"阶段: {stage} - {message}")
                        # 记录日志
                        logger.info(f"因子挖掘进度: {stage} - {progress:.0%} - {message}")
                        # 添加到会话状态中的日志
                        if 'mining_logs' not in st.session_state:
                            st.session_state.mining_logs = []
                        st.session_state.mining_logs.append({
                            'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                            'level': 'INFO',
                            'message': f"{stage} - {message}",
                            'progress': progress
                        })
                    
                    # 执行因子挖掘
                    status_text.text("正在初始化因子挖掘流程...")
                    
                    # 运行挖掘流程
                    results = pipeline.run_with_data(
                        st.session_state.data,
                        min_ic_abs=min_ic,
                        corr_threshold=corr_threshold,
                        top_n_factors=top_n,
                        progress_callback=progress_callback
                    )
                    
                    # 保存结果到会话状态
                    st.session_state.factor_mining_results = results
                    st.session_state.best_factors = results.get('best_factors', {})
                    
                    # 更新进度条到完成状态
                    progress_bar.progress(100)
                    status_text.text("因子挖掘完成！")
                    
                    # 显示成功消息
                    st.success(f"成功挖掘出 {len(results.get('best_factors', {}))} 个高质量因子")
                    
                except Exception as e:
                    # 错误处理
                    st.error(f"因子挖掘失败: {str(e)}")
                    logger.error(f"因子挖掘失败: {str(e)}")
                    status_text.text(f"错误: {str(e)}")
                    
                    # 添加错误日志
                    if 'mining_logs' not in st.session_state:
                        st.session_state.mining_logs = []
                    st.session_state.mining_logs.append({
                        'time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                        'level': 'ERROR',
                        'message': f"因子挖掘失败: {str(e)}",
                        'color': 'red'
                    })
    
    with tabs[1]:
        st.markdown("### 因子评估")
        
        # 检查是否有挖掘结果
        if st.session_state.factor_mining_results is None or not st.session_state.best_factors:
            st.info("尚未进行因子挖掘或未找到有效因子，请先在因子挖掘标签页进行挖掘")
            return
        
        # 显示因子评估结果
        results = st.session_state.factor_mining_results
        best_factors = st.session_state.best_factors
        
        # 基本信息
        st.info(f"共挖掘出 {len(best_factors)} 个高质量因子，从 {results.get('total_factors', 0)} 个初始因子中筛选")
        
        # 因子列表
        st.markdown("#### 因子列表")
        
        # 创建因子信息表格
        factor_info = []
        for name, factor in best_factors.items():
            if 'ic_mean' in results and name in results['ic_mean']:
                ic_mean = results['ic_mean'][name]
            else:
                ic_mean = 0
                
            if 'ic_decay' in results and name in results['ic_decay']:
                ic_decay = results['ic_decay'][name]
            else:
                ic_decay = 0
            
            factor_info.append({
                "因子名称": name,
                "IC值": ic_mean,
                "IC衰减率": ic_decay,
                "有效值比例": factor.notna().mean()
            })
        
        if factor_info:
            factor_df = pd.DataFrame(factor_info)
            st.dataframe(factor_df.sort_values("IC值", ascending=False), use_container_width=True)
            
            # 因子相关性热图
            st.markdown("#### 因子相关性")
            
            # 创建因子相关性矩阵
            if len(best_factors) > 1:
                factor_df = pd.DataFrame({name: factor for name, factor in best_factors.items()})
                corr_matrix = factor_df.corr()
                
                # 绘制热图
                fig, ax = plt.subplots(figsize=(10, 8))
                sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, ax=ax)
                ax.set_title("因子相关性矩阵")
                st.pyplot(fig)
            else:
                st.info("需要至少2个因子才能显示相关性热图")
    
    with tabs[2]:
        st.markdown("### 因子应用")
        
        # 检查是否有挖掘结果
        if st.session_state.best_factors is None or not st.session_state.best_factors:
            st.info("尚未进行因子挖掘或未找到有效因子，请先在因子挖掘标签页进行挖掘")
            return
        
        st.markdown("#### 应用因子到当前数据")
        
        if st.button("应用所有因子", help="将挖掘的因子应用到当前加载的数据"):
            with st.spinner("正在应用因子..."):
                try:
                    # 导入因子应用函数
                    try:
                        from auto_factor_mining_page import apply_factors_to_dataframe
                    except ImportError:
                        # 如果无法导入，定义一个简单的版本
                        def apply_factors_to_dataframe(data, factors, add_prefix=True):
                            new_data = data.copy()
                            factors_added = []
                            
                            for name, factor_series in factors.items():
                                try:
                                    # 对齐索引
                                    if isinstance(new_data.index, pd.DatetimeIndex) and isinstance(factor_series.index, pd.DatetimeIndex):
                                        aligned_factor = factor_series.reindex(new_data.index)
                                        aligned_factor = aligned_factor.fillna(method='ffill').fillna(method='bfill')
                                        factor_name = f"factor_{name}" if add_prefix else name
                                        new_data[factor_name] = aligned_factor.values
                                        factors_added.append(name)
                                except Exception:
                                    pass
                            
                            return new_data, factors_added
                    
                    # 应用因子
                    data_with_factors, added_factors = apply_factors_to_dataframe(
                        st.session_state.data, 
                        st.session_state.best_factors,
                        add_prefix=True
                    )
                    
                    # 更新数据
                    st.session_state.data = data_with_factors
                    
                    # 显示成功消息
                    st.success(f"成功应用 {len(added_factors)} 个因子到数据")
                    
                    # 显示新的数据预览
                    st.dataframe(data_with_factors.head(), use_container_width=True)
                    
                except Exception as e:
                    st.error(f"应用因子失败: {str(e)}")
                    logger.error(f"应用因子失败: {str(e)}")
        
        # 导出因子
        st.markdown("#### 导出和导入因子")
        
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("导出因子到文件", help="将挖掘的因子保存到文件中"):
                try:
                    import json
                    import os
                    
                    # 确保目录存在
                    os.makedirs("saved_factors", exist_ok=True)
                    
                    # 创建文件名
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"saved_factors/factors_{timestamp}.json"
                    
                    # 序列化因子（转换为列表）
                    factor_dict = {name: factor.tolist() for name, factor in st.session_state.best_factors.items()}
                    
                    # 添加元数据
                    export_data = {
                        "metadata": {
                            "timestamp": timestamp,
                            "factor_count": len(st.session_state.best_factors),
                            "data_length": len(next(iter(st.session_state.best_factors.values())))
                        },
                        "factors": factor_dict,
                        "index": [str(idx) for idx in next(iter(st.session_state.best_factors.values())).index.tolist()]
                    }
                    
                    # 保存到文件
                    with open(filename, 'w') as f:
                        json.dump(export_data, f)
                    
                    st.success(f"因子已导出到: {filename}")
                    
                except Exception as e:
                    st.error(f"导出因子失败: {str(e)}")
                    logger.error(f"导出因子失败: {str(e)}")
        
        with col2:
            uploaded_file = st.file_uploader("导入因子文件", type="json", 
                                           help="选择之前导出的因子文件")
            
            if uploaded_file is not None:
                try:
                    import json
                    
                    # 加载文件
                    factor_data = json.load(uploaded_file)
                    
                    # 检查文件格式
                    if "factors" not in factor_data:
                        st.error("无效的因子文件格式")
                        return
                    
                    # 转换为Series
                    imported_factors = {}
                    for name, values in factor_data["factors"].items():
                        if "index" in factor_data:
                            # 使用文件中的索引
                            try:
                                index = pd.to_datetime(factor_data["index"])
                                imported_factors[name] = pd.Series(values, index=index)
                            except:
                                # 如果无法解析索引，使用默认索引
                                imported_factors[name] = pd.Series(values)
                        else:
                            imported_factors[name] = pd.Series(values)
                    
                    # 更新会话状态
                    st.session_state.best_factors = imported_factors
                    
                    # 显示成功消息
                    st.success(f"成功导入 {len(imported_factors)} 个因子")
                    
                except Exception as e:
                    st.error(f"导入因子失败: {str(e)}")
                    logger.error(f"导入因子失败: {str(e)}") 