"""
核心逻辑模块包
包含数据处理、特征工程、交易环境、DRL智能体、市场分析、风险管理、验证和评估等功能
"""

# 版本信息
__version__ = '2.0.0'

# 导入子模块 - 修复循环导入问题
try:
    from .data_handling.enhanced_data_validator import EnhancedDataValidator
except ImportError:
    EnhancedDataValidator = None

try:
    from .trading_env import RobustTradingEnvironment
except ImportError:
    RobustTradingEnvironment = None

try:
    from .market_analysis import MarketConditionDetector, MarketCondition
except ImportError:
    MarketConditionDetector = None
    MarketCondition = None

try:
    from .risk_management import RiskManager, StopLossType, PositionSizingMethod
except ImportError:
    RiskManager = None
    StopLossType = None
    PositionSizingMethod = None

try:
    from .validation import TimeSeriesCV, CVMethod, OverfittingDetector, MarketConditionCV
except ImportError:
    TimeSeriesCV = None
    CVMethod = None
    OverfittingDetector = None
    MarketConditionCV = None

try:
    from .evaluation import ModelEvaluator
except ImportError:
    ModelEvaluator = None

# 注释掉有问题的导入，避免循环导入
# from .robust_drl_agent import RobustDRLAgent, RobustMetricsCallback

# 导出模块 - 只导出成功导入的模块
__all__ = []

# 动态添加成功导入的模块到 __all__
if EnhancedDataValidator is not None:
    __all__.append('EnhancedDataValidator')
if RobustTradingEnvironment is not None:
    __all__.append('RobustTradingEnvironment')
if MarketConditionDetector is not None:
    __all__.extend(['MarketConditionDetector', 'MarketCondition'])
if RiskManager is not None:
    __all__.extend(['RiskManager', 'StopLossType', 'PositionSizingMethod'])
if TimeSeriesCV is not None:
    __all__.extend(['TimeSeriesCV', 'CVMethod', 'OverfittingDetector', 'MarketConditionCV'])
if ModelEvaluator is not None:
    __all__.append('ModelEvaluator')
