"""
DRL智能体模块
负责DRL智能体的定义、训练、预测、保存和加载
"""

import os
import logging
import time
import datetime
import numpy as np
import pandas as pd
import gymnasium as gym
from gymnasium.utils.env_checker import check_env
from stable_baselines3 import PPO, A2C, DQN
from stable_baselines3.common.callbacks import BaseCallback, EvalCallback, StopTrainingOnRewardThreshold
from stable_baselines3.common.monitor import Monitor
from stable_baselines3.common.vec_env import DummyVecEnv, VecNormalize
from stable_baselines3.common.evaluation import evaluate_policy
import torch

from quant_trading.trading.trading_environment import TradingEnvironment
from quant_trading.utils.common import is_gpu_available

class DRLAgent:
    """
    DRL智能体类
    负责DRL智能体的定义、训练、预测、保存和加载
    """

    def __init__(self, env_config, agent_config, hpo_config=None):
        """
        初始化DRL智能体

        参数:
            env_config (dict): 交易环境配置
            agent_config (dict): 智能体配置
            hpo_config (dict, optional): 超参数优化配置
        """
        self.logger = logging.getLogger('drl_trading')
        self.env_config = env_config
        self.agent_config = agent_config
        self.hpo_config = hpo_config

        # 记录初始化参数
        self.logger.info("初始化DRL智能体")
        self.logger.info(f"环境配置: {env_config.keys() if env_config else None}")
        self.logger.info(f"智能体配置: {agent_config.keys() if agent_config else None}")
        self.logger.info(f"超参数优化配置: {hpo_config.keys() if hpo_config else None}")

        # 创建训练环境
        self.env = self._create_environment()

        # 检查环境是否符合Gymnasium API
        try:
            check_env(self.env)
            self.logger.info("环境检查通过")
        except Exception as e:
            self.logger.error(f"环境检查失败: {str(e)}")
            raise

        # 创建DRL模型
        self.model = self._create_model()

    def _create_environment(self):
        """
        创建交易环境

        返回:
            gym.Env: 交易环境实例
        """
        # 从环境配置中提取参数
        df_processed_data = self.env_config.get('df_processed_data')
        initial_capital = self.env_config.get('initial_capital', 100000)
        commission_rate = self.env_config.get('commission_rate', 0.0003)
        min_hold_days = self.env_config.get('min_hold_days', 3)
        allow_short = self.env_config.get('allow_short', False)
        max_position = self.env_config.get('max_position', 1.0)
        reward_config = self.env_config.get('reward_config', None)
        window_size = self.env_config.get('window_size', 20)

        # 创建环境
        env = TradingEnvironment(
            df_processed_data=df_processed_data,
            initial_capital=initial_capital,
            commission_rate=commission_rate,
            min_hold_days=min_hold_days,
            allow_short=allow_short,
            max_position=max_position,
            reward_config=reward_config,
            window_size=window_size
        )

        # 包装环境以记录指标
        env = Monitor(env)

        return env

    def _create_model(self):
        """
        创建DRL模型

        返回:
            stable_baselines3.BaseAlgorithm: DRL模型实例
        """
        # 从智能体配置中提取参数
        algorithm = self.agent_config.get('algorithm', 'PPO')
        policy = self.agent_config.get('policy_network', 'MlpPolicy')
        learning_rate = self.agent_config.get('learning_rate', 0.0003)
        gamma = self.agent_config.get('gamma', 0.99)

        # 检查是否有GPU可用
        device = 'cuda' if is_gpu_available() and self.agent_config.get('use_gpu', True) else 'cpu'
        self.logger.info(f"使用设备: {device}")

        # 根据算法创建模型
        if algorithm == 'PPO':
            model = PPO(
                policy=policy,
                env=self.env,
                learning_rate=learning_rate,
                gamma=gamma,
                n_steps=self.agent_config.get('n_steps', 2048),
                batch_size=self.agent_config.get('batch_size', 64),
                n_epochs=self.agent_config.get('n_epochs', 10),
                gae_lambda=self.agent_config.get('gae_lambda', 0.95),
                clip_range=self.agent_config.get('clip_range', 0.2),
                ent_coef=self.agent_config.get('ent_coef', 0.01),
                vf_coef=self.agent_config.get('vf_coef', 0.5),
                max_grad_norm=self.agent_config.get('max_grad_norm', 0.5),
                verbose=1,
                device=device
            )
        elif algorithm == 'A2C':
            model = A2C(
                policy=policy,
                env=self.env,
                learning_rate=learning_rate,
                gamma=gamma,
                n_steps=self.agent_config.get('n_steps', 5),
                ent_coef=self.agent_config.get('ent_coef', 0.01),
                vf_coef=self.agent_config.get('vf_coef', 0.5),
                max_grad_norm=self.agent_config.get('max_grad_norm', 0.5),
                verbose=1,
                device=device
            )
        elif algorithm == 'DQN':
            model = DQN(
                policy=policy,
                env=self.env,
                learning_rate=learning_rate,
                gamma=gamma,
                buffer_size=self.agent_config.get('buffer_size', 10000),
                learning_starts=self.agent_config.get('learning_starts', 1000),
                batch_size=self.agent_config.get('batch_size', 32),
                tau=self.agent_config.get('tau', 1.0),
                train_freq=self.agent_config.get('train_freq', 4),
                gradient_steps=self.agent_config.get('gradient_steps', 1),
                target_update_interval=self.agent_config.get('target_update_interval', 1000),
                exploration_fraction=self.agent_config.get('exploration_fraction', 0.1),
                exploration_initial_eps=self.agent_config.get('exploration_initial_eps', 1.0),
                exploration_final_eps=self.agent_config.get('exploration_final_eps', 0.05),
                max_grad_norm=self.agent_config.get('max_grad_norm', 10),
                verbose=1,
                device=device
            )
        else:
            raise ValueError(f"不支持的算法: {algorithm}")

        return model

    def train(self, total_timesteps, callback_list=None, hpo_trial=None, progress_bar=False, save_best_model=True):
        """
        训练DRL智能体

        参数:
            total_timesteps (int): 训练总步数
            callback_list (list, optional): 回调函数列表
            hpo_trial (optuna.Trial, optional): Optuna试验对象
            progress_bar (bool): 是否显示进度条，需要安装tqdm和rich
            save_best_model (bool): 是否保存训练过程中的最佳模型

        返回:
            dict: 训练结果统计信息
        """
        # 参数验证
        if total_timesteps <= 0:
            self.logger.warning(f"无效的训练步数: {total_timesteps}，已调整为默认值10000")
            total_timesteps = 10000

        start_time = time.time()
        self.logger.info(f"开始训练，算法: {self.agent_config.get('algorithm')}, 总步数: {total_timesteps}")

        # 记录内存使用情况
        import psutil
        process = psutil.Process()
        mem_before = process.memory_info().rss / 1024 / 1024  # MB
        self.logger.info(f"训练前内存使用: {mem_before:.2f} MB")

        # 记录设备信息
        device = self.agent_config.get('device', 'cpu')
        self.logger.info(f"训练设备: {device}")

        # 记录环境信息
        self.logger.info(f"环境观测空间: {self.env.observation_space}")
        self.logger.info(f"环境动作空间: {self.env.action_space}")

        # 准备回调列表
        callbacks = callback_list if callback_list is not None else []

        # 如果需要保存最佳模型，添加BestModelCallback
        if save_best_model:
            # 创建评估环境（与训练环境相同的配置）
            eval_env = self._create_environment()

            # 生成最佳模型保存路径（使用"BEST_算法+训练完成时间"的格式）
            algorithm = self.agent_config.get('algorithm', 'PPO')
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

            # 获取环境配置中的股票代码（如果有）
            stock_code = None
            if hasattr(self.env, 'df') and hasattr(self.env.df, 'name'):
                stock_code = self.env.df.name

            # 构建基本文件名
            filename_parts = ["BEST", algorithm]

            # 添加股票代码（如果有）
            if stock_code:
                filename_parts.append(stock_code)

            # 添加时间戳，确保唯一性
            filename_parts.append(timestamp)

            # 组合文件名
            best_model_filename = "_".join(filename_parts) + ".zip"
            best_model_path = f"saved_models/{best_model_filename}"

            # 创建并添加最佳模型回调
            eval_freq = min(10000, total_timesteps // 10)  # 至少评估10次
            best_model_callback = BestModelCallback(
                eval_env=eval_env,
                eval_freq=eval_freq,
                n_eval_episodes=5,
                save_path=best_model_path,
                agent_config=self.agent_config,
                verbose=1
            )
            callbacks.append(best_model_callback)
            self.logger.info(f"已启用最佳模型保存，评估频率: 每{eval_freq}步，保存路径: {best_model_path}")

        try:
            # 训练模型
            self.model.learn(
                total_timesteps=total_timesteps,
                callback=callbacks,
                progress_bar=progress_bar
            )

            # 计算训练时间和内存使用
            training_time = time.time() - start_time
            mem_after = process.memory_info().rss / 1024 / 1024  # MB
            mem_used = mem_after - mem_before

            # 获取训练统计信息
            ep_rewards = []
            ep_lengths = []
            if hasattr(self.model, 'ep_info_buffer') and self.model.ep_info_buffer:
                ep_rewards = [ep_info['r'] for ep_info in self.model.ep_info_buffer]
                ep_lengths = [ep_info['l'] for ep_info in self.model.ep_info_buffer]

            stats = {
                'algorithm': self.agent_config.get('algorithm'),
                'policy': self.agent_config.get('policy_network', 'MlpPolicy'),
                'device': device,
                'total_timesteps': total_timesteps,
                'training_time': training_time,
                'training_time_formatted': str(datetime.timedelta(seconds=int(training_time))),
                'memory_used_mb': mem_used,
                'episodes_completed': len(ep_rewards),
                'final_reward_mean': np.mean(ep_rewards) if ep_rewards else None,
                'final_reward_std': np.std(ep_rewards) if len(ep_rewards) > 1 else 0,
                'final_reward_min': np.min(ep_rewards) if ep_rewards else None,
                'final_reward_max': np.max(ep_rewards) if ep_rewards else None,
                'final_length_mean': np.mean(ep_lengths) if ep_lengths else None,
                'steps_per_second': total_timesteps / training_time if training_time > 0 else 0
            }

            self.logger.info(f"训练完成，耗时: {stats['training_time_formatted']}")
            self.logger.info(f"最终平均奖励: {stats['final_reward_mean']:.4f} (±{stats['final_reward_std']:.4f})")
            self.logger.info(f"训练速度: {stats['steps_per_second']:.1f} 步/秒")
            self.logger.info(f"内存使用增加: {mem_used:.2f} MB")

            return stats

        except Exception as e:
            self.logger.error(f"训练过程中发生错误: {str(e)}", exc_info=True)

            # 尝试返回部分统计信息
            training_time = time.time() - start_time
            stats = {
                'algorithm': self.agent_config.get('algorithm'),
                'total_timesteps': total_timesteps,
                'training_time': training_time,
                'training_time_formatted': str(datetime.timedelta(seconds=int(training_time))),
                'error': str(e),
                'status': 'failed'
            }

            # 重新抛出异常
            raise

    def predict_action(self, observation, deterministic=True):
        """
        预测动作

        参数:
            observation (numpy.ndarray): 观测值
            deterministic (bool): 是否使用确定性策略

        返回:
            int: 预测的动作（Python标量，非NumPy数组）
        """
        # 检查是否有集成模型，如果有则使用集成模型进行预测
        if hasattr(self, 'ensemble_model') and self.ensemble_model is not None:
            self.logger.debug("使用集成模型进行预测")
            action, _ = self.ensemble_model.predict(observation, deterministic=deterministic)
        else:
            action, _ = self.model.predict(observation, deterministic=deterministic)

        # 确保返回的是Python标量而不是NumPy数组
        if isinstance(action, np.ndarray):
            try:
                # 尝试将NumPy数组转换为Python标量
                action_scalar = action.item()
                self.logger.debug(f"将NumPy数组动作 {action} 转换为标量 {action_scalar}")
                return action_scalar
            except (ValueError, TypeError) as e:
                # 如果转换失败（例如，数组包含多个元素），记录错误并返回第一个元素
                self.logger.warning(f"无法将动作数组转换为标量: {str(e)}，使用第一个元素")
                return int(action[0]) if len(action) > 0 else 0

        return action

    def save_model(self, save_path=None, stock_code=None, performance_metrics=None, clean_old_models=False):
        """
        保存模型，使用更加独特清晰的命名格式

        参数:
            save_path (str, optional): 保存路径
            stock_code (str, optional): 股票代码
            performance_metrics (dict, optional): 性能指标
            clean_old_models (bool): 是否清理本次训练中的非最佳模型

        返回:
            str: 模型保存路径
        """
        if save_path is None:
            # 生成默认保存路径，使用更加详细的命名格式
            algorithm = self.agent_config.get('algorithm', 'PPO')
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

            # 构建基本文件名 - 使用"算法+训练完成时间"的格式
            filename_parts = [algorithm]

            # 添加股票代码（如果有）
            if stock_code:
                filename_parts.append(stock_code)

            # 添加时间戳，确保唯一性
            filename_parts.append(timestamp)

            # 组合文件名
            filename = "_".join(filename_parts) + ".zip"
            save_path = f"saved_models/{filename}"

        # 确保目录存在
        os.makedirs(os.path.dirname(save_path), exist_ok=True)

        # 保存模型
        self.model.save(save_path)
        self.logger.info(f"模型已保存到: {save_path}")

        # 如果需要清理本次训练中的非最佳模型
        if clean_old_models:
            self._clean_non_best_models(timestamp)

        return save_path

    def optimize_hyperparameters(self, n_trials=50, n_startup_trials=10, n_evaluations=2, n_timesteps=50000, callback=None):
        """
        使用Optuna优化超参数

        参数:
            n_trials (int): 试验次数
            n_startup_trials (int): 启动试验次数
            n_evaluations (int): 每次试验的评估次数
            n_timesteps (int): 每次评估的时间步数
            callback (callable, optional): 每次试验后调用的回调函数

        返回:
            dict: 最佳超参数
        """
        if self.hpo_config is None:
            self.logger.error("未提供超参数优化配置")
            raise ValueError("未提供超参数优化配置")

        # 导入必要的库
        try:
            import optuna
            from optuna.samplers import TPESampler
            from optuna.pruners import MedianPruner
        except ImportError:
            self.logger.error("未安装Optuna库，无法进行超参数优化")
            raise ImportError("未安装Optuna库，请使用pip install optuna安装")

        self.logger.info(f"开始超参数优化，试验次数: {n_trials}")

        # 创建Optuna研究
        study_name = f"{self.agent_config.get('algorithm', 'PPO')}_optimization"
        study = optuna.create_study(
            study_name=study_name,
            sampler=TPESampler(n_startup_trials=n_startup_trials),
            pruner=MedianPruner(n_startup_trials=n_startup_trials, n_warmup_steps=n_timesteps // 3),
            direction="maximize"
        )

        # 定义目标函数
        def objective(trial):
            # 采样超参数
            hyperparams = self._sample_hyperparameters(trial)

            # 更新智能体配置
            agent_config = self.agent_config.copy()
            agent_config.update(hyperparams)

            # 创建新的智能体
            agent = DRLAgent(self.env_config, agent_config)

            # 训练并评估
            mean_reward = 0
            for _ in range(n_evaluations):
                # 训练模型
                agent.train(total_timesteps=n_timesteps)

                # 评估模型
                eval_env = agent._create_environment()
                rewards, _ = evaluate_policy(agent.model, eval_env, n_eval_episodes=10, deterministic=True)
                mean_reward += np.mean(rewards)

            # 计算平均奖励
            mean_reward /= n_evaluations

            return mean_reward

        # 运行优化
        try:
            # 如果提供了回调函数，使用它
            if callback:
                study.optimize(objective, n_trials=n_trials, callbacks=[callback])
            else:
                study.optimize(objective, n_trials=n_trials)

            # 获取最佳超参数
            best_params = study.best_params
            best_value = study.best_value

            self.logger.info(f"超参数优化完成，最佳奖励: {best_value}")
            self.logger.info(f"最佳超参数: {best_params}")

            # 更新智能体配置
            self.agent_config.update(best_params)

            # 使用最佳超参数重新创建模型
            self.model = self._create_model()

            return best_params

        except Exception as e:
            self.logger.error(f"超参数优化过程中发生错误: {str(e)}")
            raise

    def _sample_hyperparameters(self, trial):
        """
        采样超参数

        参数:
            trial (optuna.Trial): Optuna试验对象

        返回:
            dict: 采样的超参数
        """
        # 获取算法
        algorithm = self.agent_config.get('algorithm', 'PPO')

        # 获取超参数空间
        param_space = self.hpo_config.get('param_space', {})

        # 通用超参数
        hyperparams = {
            'learning_rate': trial.suggest_float('learning_rate',
                                               param_space.get('learning_rate', {}).get('min', 1e-5),
                                               param_space.get('learning_rate', {}).get('max', 1e-3),
                                               log=True),
            'gamma': trial.suggest_float('gamma',
                                       param_space.get('gamma', {}).get('min', 0.9),
                                       param_space.get('gamma', {}).get('max', 0.9999))
        }

        # 算法特定超参数
        if algorithm == 'PPO':
            hyperparams.update({
                'n_steps': trial.suggest_int('n_steps',
                                           param_space.get('n_steps', {}).get('min', 32),
                                           param_space.get('n_steps', {}).get('max', 2048)),
                'batch_size': trial.suggest_int('batch_size',
                                              param_space.get('batch_size', {}).get('min', 32),
                                              param_space.get('batch_size', {}).get('max', 256)),
                'n_epochs': trial.suggest_int('n_epochs',
                                            param_space.get('n_epochs', {}).get('min', 5),
                                            param_space.get('n_epochs', {}).get('max', 20)),
                'clip_range': trial.suggest_float('clip_range',
                                                param_space.get('clip_range', {}).get('min', 0.1),
                                                param_space.get('clip_range', {}).get('max', 0.3))
            })
        elif algorithm == 'A2C':
            hyperparams.update({
                'n_steps': trial.suggest_int('n_steps',
                                           param_space.get('n_steps', {}).get('min', 5),
                                           param_space.get('n_steps', {}).get('max', 20)),
                'ent_coef': trial.suggest_float('ent_coef',
                                              param_space.get('ent_coef', {}).get('min', 0.0),
                                              param_space.get('ent_coef', {}).get('max', 0.1))
            })
        elif algorithm == 'DQN':
            hyperparams.update({
                'buffer_size': trial.suggest_int('buffer_size',
                                               param_space.get('buffer_size', {}).get('min', 10000),
                                               param_space.get('buffer_size', {}).get('max', 100000)),
                'learning_starts': trial.suggest_int('learning_starts',
                                                   param_space.get('learning_starts', {}).get('min', 1000),
                                                   param_space.get('learning_starts', {}).get('max', 10000)),
                'target_update_interval': trial.suggest_int('target_update_interval',
                                                          param_space.get('target_update_interval', {}).get('min', 500),
                                                          param_space.get('target_update_interval', {}).get('max', 5000))
            })

        return hyperparams

    def _clean_non_best_models(self, current_timestamp):
        """
        清理本次训练中的非最佳模型，只保留最佳模型

        参数:
            current_timestamp (str): 当前训练的时间戳
        """
        try:
            # 获取saved_models目录中的所有模型文件
            models_dir = "saved_models"
            if not os.path.exists(models_dir):
                return

            model_files = [f for f in os.listdir(models_dir) if f.endswith(".zip")]

            # 找出本次训练的所有最佳模型（可能有多个带有不同性能指标的版本）
            best_model_files = [f for f in model_files if "BEST" in f and current_timestamp in f]

            # 如果有多个最佳模型文件（带有不同性能指标），只保留一个（最新的）
            if len(best_model_files) > 1:
                self.logger.info(f"发现多个最佳模型文件，共 {len(best_model_files)} 个")

                # 按文件修改时间排序，保留最新的
                best_model_files.sort(key=lambda f: os.path.getmtime(os.path.join(models_dir, f)), reverse=True)

                # 保留最新的一个，删除其他的
                keep_model = best_model_files[0]
                self.logger.info(f"保留最新的最佳模型: {keep_model}")

                for model_file in best_model_files[1:]:
                    model_path = os.path.join(models_dir, model_file)
                    # 删除模型文件
                    os.remove(model_path)
                    self.logger.info(f"已删除冗余的最佳模型: {model_file}")

                    # 删除对应的VecNormalize参数文件（如果有）
                    vec_normalize_path = model_path.replace('.zip', '_vecnormalize.pkl')
                    if os.path.exists(vec_normalize_path):
                        os.remove(vec_normalize_path)
                        self.logger.info(f"已删除冗余的归一化参数: {os.path.basename(vec_normalize_path)}")

                # 更新最佳模型文件列表
                best_model_files = [keep_model]

            # 找出本次训练的非最佳模型（包含当前时间戳但不是最佳模型）
            non_best_model_files = [f for f in model_files if "BEST" not in f and current_timestamp in f]

            # 如果找到了最佳模型和非最佳模型，删除非最佳模型
            if best_model_files and non_best_model_files:
                self.logger.info(f"找到本次训练的最佳模型: {best_model_files[0]}")
                self.logger.info(f"清理本次训练的非最佳模型，共 {len(non_best_model_files)} 个")

                for model_file in non_best_model_files:
                    model_path = os.path.join(models_dir, model_file)
                    # 删除模型文件
                    os.remove(model_path)
                    self.logger.info(f"已删除非最佳模型: {model_file}")

                    # 删除对应的VecNormalize参数文件（如果有）
                    vec_normalize_path = model_path.replace('.zip', '_vecnormalize.pkl')
                    if os.path.exists(vec_normalize_path):
                        os.remove(vec_normalize_path)
                        self.logger.info(f"已删除非最佳模型的归一化参数: {os.path.basename(vec_normalize_path)}")

                self.logger.info("模型清理完成，只保留了最佳模型")
            elif not best_model_files:
                self.logger.info("未找到本次训练的最佳模型")
            elif not non_best_model_files:
                self.logger.info("未找到需要清理的非最佳模型")

        except Exception as e:
            self.logger.error(f"清理非最佳模型时发生错误: {str(e)}")
            # 不抛出异常，避免影响正常流程

    @classmethod
    def load_model(cls, model_path, eval_env_config=None):
        """
        加载模型

        参数:
            model_path (str): 模型路径
            eval_env_config (dict, optional): 评估环境配置

        返回:
            DRLAgent: DRL智能体实例
        """
        logger = logging.getLogger('drl_trading')

        try:
            # 检查模型文件是否存在
            if not os.path.exists(model_path):
                raise FileNotFoundError(f"模型文件不存在: {model_path}")

            # 检查是否是最佳模型路径
            is_best_model = 'BEST' in os.path.basename(model_path)

            # 检查是否是集成模型
            is_ensemble = False
            ensemble_model = None

            # 检查是否有对应的集成目录
            ensemble_dir = model_path.replace('.zip', '_ensemble')
            if os.path.exists(ensemble_dir) and os.path.isdir(ensemble_dir):
                is_ensemble = True
                logger.info(f"检测到集成学习目录: {ensemble_dir}")

                # 尝试导入EnsembleModel
                try:
                    from quant_trading.agents.ensemble_learning import EnsembleModel
                    # 尝试加载集成模型
                    ensemble_model = EnsembleModel.load(ensemble_dir)
                    logger.info(f"集成模型已从 {ensemble_dir} 加载")
                except ImportError:
                    logger.warning("无法导入EnsembleModel类，将忽略集成模型")
                    is_ensemble = False
                except Exception as ensemble_error:
                    logger.error(f"加载集成模型失败: {str(ensemble_error)}")
                    is_ensemble = False

            # 确定算法类型
            if 'PPO' in model_path:
                model_class = PPO
                algorithm = 'PPO'
            elif 'A2C' in model_path:
                model_class = A2C
                algorithm = 'A2C'
            elif 'DQN' in model_path:
                model_class = DQN
                algorithm = 'DQN'
            else:
                # 默认使用PPO
                model_class = PPO
                algorithm = 'PPO'
                logger.warning(f"无法从路径确定算法类型: {model_path}，使用默认算法: PPO")

            # 检查是否有对应的VecNormalize参数文件
            vec_normalize_path = model_path.replace('.zip', '_vecnormalize.pkl')
            has_vec_normalize = os.path.exists(vec_normalize_path)

            # 如果提供了评估环境配置，创建评估环境
            if eval_env_config:
                try:
                    # 先加载模型以获取观测空间维度
                    temp_model = model_class.load(model_path)

                    # 获取模型的观测空间维度
                    if hasattr(temp_model, 'observation_space'):
                        model_obs_dim = temp_model.observation_space.shape[0]
                        logger.info(f"模型观测空间维度: {model_obs_dim}")
                    else:
                        model_obs_dim = None
                        logger.warning("无法获取模型的观测空间维度")

                    # 创建评估环境
                    env = TradingEnvironment(**eval_env_config)

                    # 检查环境的观测空间维度是否与模型匹配
                    env_obs_dim = env.observation_space.shape[0]
                    logger.info(f"环境观测空间维度: {env_obs_dim}")

                    if model_obs_dim is not None and model_obs_dim != env_obs_dim:
                        logger.warning(f"观测空间维度不匹配: 模型 {model_obs_dim} vs 环境 {env_obs_dim}")
                        logger.info("尝试调整环境特征维度以匹配模型...")

                        # 尝试调整环境的特征维度
                        if hasattr(env, 'adjust_observation_space'):
                            env.adjust_observation_space(model_obs_dim)
                            logger.info(f"已调整环境观测空间维度为: {model_obs_dim}")
                        else:
                            logger.error("环境不支持调整观测空间维度")
                            raise ValueError(f"观测空间不匹配: {model_obs_dim} != {env_obs_dim}")

                    env = Monitor(env)

                    # 如果有VecNormalize参数，加载它
                    if has_vec_normalize:
                        logger.info(f"发现环境归一化参数文件: {vec_normalize_path}")
                        env = DummyVecEnv([lambda: env])
                        env = VecNormalize.load(vec_normalize_path, env)
                        env.training = False  # 禁用归一化统计更新
                        env.norm_reward = False  # 禁用奖励归一化
                        logger.info("已加载环境归一化参数")

                    # 加载模型并连接到环境
                    model = model_class.load(model_path, env=env)
                    logger.info(f"模型已加载: {model_path}，并连接到新的评估环境")
                except Exception as env_error:
                    logger.error(f"创建评估环境失败: {str(env_error)}")
                    # 尝试不使用环境加载模型
                    logger.info("尝试不使用环境加载模型...")
                    model = model_class.load(model_path)
                    logger.info(f"模型已加载: {model_path}（无环境）")
            else:
                # 加载模型（不连接环境）
                model = model_class.load(model_path)
                logger.info(f"模型已加载: {model_path}")

            # 创建DRLAgent实例
            agent = cls.__new__(cls)
            agent.logger = logger
            agent.model = model
            agent.env = model.get_env() if hasattr(model, 'get_env') else None

            # 如果有集成模型，设置到agent实例
            if is_ensemble and ensemble_model is not None:
                agent.ensemble_model = ensemble_model
                logger.info("集成模型已设置到智能体实例")

            # 设置基本配置
            agent.agent_config = {'algorithm': algorithm}
            agent.env_config = eval_env_config if eval_env_config else {}

            # 记录模型信息
            if is_best_model:
                logger.info("已加载最佳模型")
            if is_ensemble:
                logger.info("已加载集成模型")

            return agent

        except Exception as e:
            logger.error(f"加载模型失败: {str(e)}", exc_info=True)
            # 提供更详细的错误信息
            if "incompatible architecture" in str(e).lower():
                logger.error("模型架构与当前环境不兼容，可能是由于特征数量或环境配置变化导致的")
            elif "cuda" in str(e).lower():
                logger.error("GPU相关错误，尝试在CPU上加载模型")
            elif "vecnormalize" in str(e).lower():
                logger.error("VecNormalize参数加载失败，可能缺少归一化参数文件或参数不兼容")

            raise

# 自定义回调类，用于在训练过程中收集指标
class MetricsCallback(BaseCallback):
    """
    收集训练指标的回调类
    """

    def __init__(self, verbose=0):
        super(MetricsCallback, self).__init__(verbose)
        self.rewards = []
        self.episode_lengths = []
        self.episode_rewards = []
        self.episode_times = []
        self.training_start_time = time.time()

    def _on_step(self):
        # 记录每个步骤的奖励
        if len(self.model.ep_info_buffer) > 0:
            # ep_info_buffer中的"r"是一个浮点数，不是列表
            self.rewards.append(self.model.ep_info_buffer[-1]["r"])

        return True

    def _on_rollout_end(self):
        # 记录每个回合结束时的指标
        if len(self.model.ep_info_buffer) > 0:
            self.episode_rewards.append(self.model.ep_info_buffer[-1]["r"])
            self.episode_lengths.append(self.model.ep_info_buffer[-1]["l"])
            self.episode_times.append(time.time() - self.training_start_time)

        return True

    def get_metrics(self):
        """
        获取收集的指标

        返回:
            dict: 训练指标
        """
        return {
            'rewards': self.rewards,
            'episode_rewards': self.episode_rewards,
            'episode_lengths': self.episode_lengths,
            'episode_times': self.episode_times
        }


class BestModelCallback(BaseCallback):
    """
    保存训练过程中最佳模型的回调类，使用更加独特清晰的命名格式
    """

    def __init__(self, eval_env, eval_freq=10000, n_eval_episodes=5, save_path="saved_models/best_model",
                 save_vec_normalize=True, agent_config=None, verbose=1):
        """
        初始化最佳模型回调

        参数:
            eval_env: 评估环境
            eval_freq: 评估频率（步数）
            n_eval_episodes: 评估时的回合数
            save_path: 保存路径
            save_vec_normalize: 是否保存VecNormalize参数
            agent_config: 智能体配置，用于生成更详细的文件名
            verbose: 详细程度
        """
        super(BestModelCallback, self).__init__(verbose)
        self.eval_env = eval_env
        self.eval_freq = eval_freq
        self.n_eval_episodes = n_eval_episodes
        self.save_path = save_path
        self.save_vec_normalize = save_vec_normalize
        self.agent_config = {} if agent_config is None else agent_config
        self.best_mean_reward = -float('inf')
        self.custom_logger = logging.getLogger('drl_trading')

        # 确保保存目录存在
        os.makedirs(os.path.dirname(save_path), exist_ok=True)

    def _on_step(self):
        # 每eval_freq步评估一次
        if self.n_calls % self.eval_freq == 0:
            # 评估当前模型
            mean_reward, std_reward = evaluate_policy(
                self.model,
                self.eval_env,
                n_eval_episodes=self.n_eval_episodes,
                deterministic=True
            )

            # 记录评估结果
            self.custom_logger.info(f"评估: {self.n_calls}步, 平均奖励: {mean_reward:.2f} ± {std_reward:.2f}")

            # 如果是最佳模型，保存它
            if mean_reward > self.best_mean_reward:
                self.best_mean_reward = mean_reward

                # 获取基本保存路径（不包含性能指标）
                base_path = os.path.dirname(self.save_path)
                filename = os.path.basename(self.save_path)

                # 提取文件名部分（不含扩展名）
                filename_parts = os.path.splitext(filename)[0].split("_")

                # 提取算法名称和时间戳（保持不变的部分）
                algorithm = None
                timestamp = None
                for part in filename_parts:
                    if part == "BEST":
                        continue
                    elif part in ["PPO", "A2C", "DQN", "SAC", "TD3"]:  # 支持的算法列表
                        algorithm = part
                    elif part.startswith("20") and len(part) == 15:  # 时间戳格式: 20YYMMDD_HHMMSS
                        timestamp = part

                if not algorithm or not timestamp:
                    # 如果无法提取算法或时间戳，使用原始文件名
                    self.custom_logger.warning("无法从文件名中提取算法或时间戳，使用原始文件名")
                    algorithm = filename_parts[1] if len(filename_parts) > 1 else "UNKNOWN"
                    timestamp = filename_parts[2] if len(filename_parts) > 2 else time.strftime("%Y%m%d_%H%M%S")

                # 创建新的文件名（只包含固定部分和最新的性能指标）
                new_filename = f"BEST_{algorithm}_{timestamp}_ret{mean_reward:.2f}_std{std_reward:.2f}.zip"
                new_save_path = os.path.join(base_path, new_filename)

                # 删除所有同一时间戳的旧的最佳模型文件
                for old_file in os.listdir(base_path):
                    if (old_file.endswith(".zip") and
                        "BEST" in old_file and
                        timestamp in old_file and
                        old_file != new_filename):
                        old_path = os.path.join(base_path, old_file)
                        try:
                            # 删除旧的模型文件
                            os.remove(old_path)
                            self.custom_logger.info(f"删除旧的最佳模型文件: {old_file}")

                            # 删除对应的VecNormalize参数文件（如果有）
                            old_norm_path = old_path.replace('.zip', '_vecnormalize.pkl')
                            if os.path.exists(old_norm_path):
                                os.remove(old_norm_path)
                                self.custom_logger.info(f"删除旧的归一化参数文件: {os.path.basename(old_norm_path)}")
                        except Exception as e:
                            self.custom_logger.warning(f"删除旧文件时出错: {str(e)}")

                # 更新保存路径
                self.save_path = new_save_path

                # 保存模型
                self.model.save(self.save_path)

                # 如果使用了VecNormalize，也保存归一化参数
                if self.save_vec_normalize and isinstance(self.eval_env, VecNormalize):
                    norm_path = self.save_path.replace('.zip', '_vecnormalize.pkl')
                    self.eval_env.save(norm_path)
                    self.custom_logger.info(f"环境归一化参数已保存到: {norm_path}")

                self.custom_logger.info(f"新的最佳模型! 平均奖励: {mean_reward:.2f} ± {std_reward:.2f}, 已保存到: {self.save_path}")

        return True
