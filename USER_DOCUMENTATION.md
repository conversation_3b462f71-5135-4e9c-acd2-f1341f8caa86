# Quantitative Trading System User Documentation

## Project Overview

This quantitative trading system is a deep reinforcement learning (DRL) based platform that supports data extraction, feature engineering, model training, and backtesting for various financial products (stocks, indices). The system employs state-of-the-art DRL algorithms to learn market patterns and optimize trading decisions, providing comprehensive performance analysis and visualization capabilities.

## System Requirements

- Python 3.8+
- Dependencies: See `requirements.txt`
- Recommended: NVIDIA GPU (for accelerated model training)

## Installation Guide

### 1. Create a Virtual Environment

```bash
# Create virtual environment
python -m venv quant_env

# Activate virtual environment
# Windows
quant_env\Scripts\activate
# Linux/Mac
source quant_env/bin/activate
```

### 2. Install Dependencies

```bash
# Install required libraries
pip install -r requirements.txt
```

### 3. GPU Support (Optional but Recommended)

If your system has an NVIDIA GPU, you can install the GPU version of PyTorch to accelerate training:

```bash
# Install CUDA-enabled PyTorch
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

## Project Structure

```
quant_project/
├── core_logic/              # Core logic modules
│   ├── data_handler.py      # Data processing module
│   ├── feature_engineer.py  # Feature engineering module
│   ├── trading_environment.py # Trading environment module
│   ├── drl_agent.py         # DRL agent module
│   ├── performance_analyzer.py # Performance analysis module
│   └── utils.py             # Utility functions
├── configs/                 # Configuration files
│   ├── env_config.yaml      # Environment configuration
│   └── drl_agent_config.yaml # DRL agent configuration
├── data_cache/              # Data cache directory
├── logs/                    # Log directory
├── saved_models/            # Saved models
├── tests/                   # Test scripts
└── app.py                   # Main application
```

## Usage Guide

### 1. Data Extraction

The system supports data extraction for various financial products:

- **Stocks**: Use format `sh000001` or `sz399001`
- **Indices**: Use format `index_000300` (CSI 300)

Note: Cryptocurrency data extraction has been disabled.

Example code:

```python
from core_logic.data_handler import DataHandler

# Create data handler
data_handler = DataHandler()

# Get Shanghai Composite Index data
sh_index_data = data_handler.get_stock_data(
    stock_code='sh000001',
    start_date='2022-01-01',
    end_date='2022-12-31',
    frequency='daily'
)

# Get CSI 300 Index data
csi300_data = data_handler.get_stock_data(
    stock_code='index_000300',
    start_date='2022-01-01',
    end_date='2022-12-31',
    frequency='daily'
)

# Note: Cryptocurrency data extraction has been disabled
# The following code will raise an error:
# btc_data = data_handler.get_stock_data(
#     stock_code='crypto_BTC',
#     start_date='2022-01-01',
#     end_date='2022-12-31',
#     frequency='daily'
# )
```

### 2. Feature Engineering

The system supports generating various technical indicators:

```python
from core_logic.feature_engineer import FeatureEngineer

# Create feature engineer
feature_config = {
    'sma': {'use': True, 'periods': [5, 20, 60]},
    'ema': {'use': True, 'periods': [5, 20]},
    'rsi': {'use': True, 'period': 14},
    'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9},
    'bbands': {'use': True, 'period': 20, 'std': 2.0},
    'atr': {'use': True, 'period': 14}
}

feature_engineer = FeatureEngineer(feature_config)

# Generate features
processed_data = feature_engineer.generate_features(data)
```

### 3. Creating Trading Environment

```python
from core_logic.trading_environment import TradingEnvironment

# Create trading environment
env = TradingEnvironment(
    df_processed_data=processed_data,
    initial_capital=100000,
    commission_rate=0.0003,
    min_hold_days=3,
    window_size=20
)
```

### 4. Training DRL Agent

```python
from core_logic.drl_agent import DRLAgent, MetricsCallback

# Environment configuration
env_config = {
    'df_processed_data': processed_data,
    'initial_capital': 100000,
    'commission_rate': 0.0003,
    'min_hold_days': 3,
    'window_size': 20
}

# Agent configuration
agent_config = {
    'algorithm': 'PPO',  # Options: 'PPO', 'A2C', 'DQN'
    'policy_network': 'MlpPolicy',
    'learning_rate': 0.0003,
    'gamma': 0.99
}

# Create DRL agent
drl_agent = DRLAgent(env_config, agent_config)

# Create callback
metrics_callback = MetricsCallback()

# Train model
training_stats = drl_agent.train(
    total_timesteps=10000,
    callback_list=[metrics_callback]
)

# Save model
model_path = drl_agent.save_model(save_path="saved_models/my_model.zip")
```

### 5. Backtesting and Performance Analysis

```python
from core_logic.performance_analyzer import PerformanceAnalyzer

# Load saved model
loaded_agent = DRLAgent.load_model("saved_models/my_model.zip")

# Create backtesting environment
test_env_config = {
    'df_processed_data': test_data,
    'initial_capital': 100000,
    'commission_rate': 0.0003,
    'min_hold_days': 3,
    'window_size': 20
}

test_env = TradingEnvironment(**test_env_config)

# Run backtest
observation, info = test_env.reset()
done = False

while not done:
    action = loaded_agent.predict_action(observation)
    observation, reward, terminated, truncated, info = test_env.step(action)
    done = terminated or truncated

# Get trade records and portfolio value history
trades = test_env.get_trades_history()
portfolio_values = test_env.get_portfolio_history()

# Performance analysis
analyzer = PerformanceAnalyzer()
metrics = analyzer.analyze(trades, portfolio_values)

# Print performance metrics
print(f"Total Return: {metrics['total_return']:.2%}")
print(f"Annualized Return: {metrics['annualized_return']:.2%}")
print(f"Maximum Drawdown: {metrics['max_drawdown']:.2%}")
print(f"Sharpe Ratio: {metrics['sharpe_ratio']:.2f}")
```

## Running Comprehensive Tests

The system provides comprehensive test scripts to verify the functionality of each module:

```bash
# Run all tests
python run_all_tests.py

# Run specific tests
python tests/test_data_extraction.py
python tests/test_drl_agent.py
```

## Frequently Asked Questions

### 1. Data Extraction Failure

If you encounter data extraction failures, please check:
- Network connection
- Correct stock/index code format
- Date format is 'YYYY-MM-DD'

### 2. GPU Not Available

If the system cannot detect your GPU:
- Confirm NVIDIA drivers are installed
- Confirm CUDA toolkit is installed
- Confirm GPU version of PyTorch is installed

### 3. Slow Training Speed

Suggestions to improve training speed:
- Use GPU for training
- Reduce the time range of training data
- Simplify feature configuration
- Adjust batch size and learning rate

## Contact and Support

For any questions or suggestions, please contact the project maintainer.

---

Happy trading!
