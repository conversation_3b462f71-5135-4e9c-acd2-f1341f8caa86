"""
集成学习UI逻辑测试脚本

该脚本专门测试集成学习相关的UI逻辑，包括：
1. 当启用多算法集成学习时，主算法选择应被禁用
2. 当启用集成学习时，UI应正确反映算法选择逻辑
3. 测试集成学习训练后模型保存和加载功能
"""

import os
import sys
import time
import logging
import unittest
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(parent_dir, 'logs', 'ensemble_ui_test.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('ensemble_ui_test')

class EnsembleUILogicTest(unittest.TestCase):
    """集成学习UI逻辑测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        logger.info("设置测试环境")
        
        # 启动Streamlit应用
        cls.start_streamlit_app()
        
        # 设置WebDriver
        chrome_options = Options()
        # chrome_options.add_argument("--headless")  # 无头模式，可以注释掉以查看浏览器操作
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--start-maximized")
        
        cls.driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)
        cls.driver.get("http://localhost:8501")
        
        # 等待页面加载完成
        WebDriverWait(cls.driver, 30).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        logger.info("Streamlit应用已加载")
        
        # 等待侧边栏加载完成
        WebDriverWait(cls.driver, 30).until(
            EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'stSidebar')]"))
        )
        logger.info("侧边栏已加载")
    
    @classmethod
    def tearDownClass(cls):
        """清理测试环境"""
        logger.info("清理测试环境")
        
        # 关闭WebDriver
        if cls.driver:
            cls.driver.quit()
        
        # 停止Streamlit应用
        cls.stop_streamlit_app()
    
    @classmethod
    def start_streamlit_app(cls):
        """启动Streamlit应用"""
        logger.info("启动Streamlit应用")
        
        # 使用subprocess启动应用
        import subprocess
        import threading
        
        def run_app():
            app_file = os.path.join(parent_dir, 'main_app.py')
            subprocess.run([sys.executable, "-m", "streamlit", "run", app_file], 
                          stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 在后台线程中启动应用
        cls.app_thread = threading.Thread(target=run_app)
        cls.app_thread.daemon = True
        cls.app_thread.start()
        
        # 等待应用启动
        time.sleep(15)
        logger.info("Streamlit应用已启动")
    
    @classmethod
    def stop_streamlit_app(cls):
        """停止Streamlit应用"""
        logger.info("停止Streamlit应用")
        
        # 在Windows上使用taskkill命令终止Streamlit进程
        import subprocess
        subprocess.run(["taskkill", "/f", "/im", "streamlit.exe"], 
                      stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        subprocess.run(["taskkill", "/f", "/im", "python.exe", "/fi", "WINDOWTITLE eq streamlit"], 
                      stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        logger.info("Streamlit应用已停止")
    
    def wait_for_element(self, by, value, timeout=10):
        """等待元素出现"""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((by, value))
            )
            return element
        except TimeoutException:
            logger.error(f"等待元素超时: {by}={value}")
            return None
    
    def wait_for_clickable_element(self, by, value, timeout=10):
        """等待元素可点击"""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.element_to_be_clickable((by, value))
            )
            return element
        except TimeoutException:
            logger.error(f"等待元素可点击超时: {by}={value}")
            return None
    
    def scroll_to_element(self, element):
        """滚动到元素位置"""
        self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
        time.sleep(1)  # 等待滚动完成
    
    def navigate_to_page(self, page_name):
        """导航到指定页面"""
        logger.info(f"导航到页面: {page_name}")
        
        # 查找侧边栏导航选项
        nav_options = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'stRadio')]//label")
        for option in nav_options:
            if page_name in option.text:
                self.scroll_to_element(option)
                option.click()
                time.sleep(3)  # 等待页面加载
                logger.info(f"已导航到页面: {page_name}")
                return True
        
        logger.error(f"未找到页面: {page_name}")
        return False
    
    def prepare_data_and_features(self):
        """准备数据和特征"""
        logger.info("准备数据和特征")
        
        # 导航到数据中心页面
        self.navigate_to_page("数据中心与环境配置")
        
        # 输入股票代码
        stock_code_input = self.wait_for_element(By.XPATH, "//input[@aria-label='金融产品代码']")
        self.assertIsNotNone(stock_code_input)
        stock_code_input.clear()
        stock_code_input.send_keys("sh000001")
        
        # 点击获取数据按钮
        get_data_button = self.wait_for_clickable_element(By.XPATH, "//button[text()='获取数据']")
        self.assertIsNotNone(get_data_button)
        get_data_button.click()
        
        # 等待数据加载
        time.sleep(15)
        
        # 验证数据是否加载成功
        success_message = self.wait_for_element(By.XPATH, "//div[contains(text(), '成功获取数据')]")
        self.assertIsNotNone(success_message)
        
        # 滚动到特征工程部分
        feature_engineering_header = self.wait_for_element(By.XPATH, "//h2[text()='特征工程配置']")
        self.assertIsNotNone(feature_engineering_header)
        self.scroll_to_element(feature_engineering_header)
        
        # 点击生成特征按钮
        generate_features_button = self.wait_for_clickable_element(By.XPATH, "//button[text()='生成特征']")
        self.assertIsNotNone(generate_features_button)
        generate_features_button.click()
        
        # 等待特征生成
        time.sleep(20)
        
        # 验证特征是否生成成功
        success_message = self.wait_for_element(By.XPATH, "//div[contains(text(), '成功生成特征')]")
        self.assertIsNotNone(success_message)
        
        logger.info("数据和特征准备完成")
    
    def test_01_ensemble_ui_logic(self):
        """测试集成学习UI逻辑"""
        logger.info("测试集成学习UI逻辑")
        
        # 准备数据和特征
        self.prepare_data_and_features()
        
        # 导航到DRL智能体训练页面
        self.navigate_to_page("DRL智能体训练")
        
        # 检查集成学习选项
        ensemble_checkbox = self.wait_for_clickable_element(By.XPATH, "//label[contains(text(), '使用集成学习')]")
        self.assertIsNotNone(ensemble_checkbox)
        
        # 确保集成学习被选中
        if not ensemble_checkbox.is_selected():
            ensemble_checkbox.click()
            time.sleep(2)
        
        # 检查多算法集成学习选项
        multi_algo_checkbox = self.wait_for_clickable_element(By.XPATH, "//label[contains(text(), '使用多算法集成学习')]")
        self.assertIsNotNone(multi_algo_checkbox)
        
        # 点击启用多算法集成学习
        if not multi_algo_checkbox.is_selected():
            multi_algo_checkbox.click()
            time.sleep(2)
        
        # 验证主算法选择是否被禁用
        main_algo_select = self.wait_for_element(By.XPATH, "//label[text()='DRL算法']/..//div[contains(@class, 'stSelectbox')]")
        self.assertIsNotNone(main_algo_select)
        
        # 检查是否有禁用状态的标记
        disabled_attr = main_algo_select.get_attribute("aria-disabled")
        self.assertEqual(disabled_attr, "true", "主算法选择未被禁用")
        
        # 验证是否显示警告信息
        warning_message = self.wait_for_element(By.XPATH, "//div[contains(text(), '您已启用多算法集成学习')]")
        self.assertIsNotNone(warning_message)
        
        # 选择要集成的算法
        algo_multiselect = self.wait_for_element(By.XPATH, "//label[text()='选择要集成的算法']/..//div[contains(@class, 'stMultiSelect')]")
        self.assertIsNotNone(algo_multiselect)
        
        # 验证成功消息
        success_message = self.wait_for_element(By.XPATH, "//div[contains(text(), '将使用您选择的算法')]")
        self.assertIsNotNone(success_message)
        
        logger.info("集成学习UI逻辑测试通过")
    
    def test_02_disable_main_algorithm(self):
        """测试禁用主算法选择"""
        logger.info("测试禁用主算法选择")
        
        # 导航到DRL智能体训练页面
        self.navigate_to_page("DRL智能体训练")
        
        # 检查集成学习选项
        ensemble_checkbox = self.wait_for_clickable_element(By.XPATH, "//label[contains(text(), '使用集成学习')]")
        self.assertIsNotNone(ensemble_checkbox)
        
        # 确保集成学习被选中
        if not ensemble_checkbox.is_selected():
            ensemble_checkbox.click()
            time.sleep(2)
        
        # 检查多算法集成学习选项
        multi_algo_checkbox = self.wait_for_clickable_element(By.XPATH, "//label[contains(text(), '使用多算法集成学习')]")
        self.assertIsNotNone(multi_algo_checkbox)
        
        # 点击启用多算法集成学习
        if not multi_algo_checkbox.is_selected():
            multi_algo_checkbox.click()
            time.sleep(2)
        
        # 验证主算法选择是否被禁用
        main_algo_select = self.wait_for_element(By.XPATH, "//label[text()='DRL算法']/..//div[contains(@class, 'stSelectbox')]")
        self.assertIsNotNone(main_algo_select)
        
        # 检查是否有禁用状态的标记
        disabled_attr = main_algo_select.get_attribute("aria-disabled")
        self.assertEqual(disabled_attr, "true", "主算法选择未被禁用")
        
        logger.info("禁用主算法选择测试通过")

if __name__ == "__main__":
    unittest.main()
