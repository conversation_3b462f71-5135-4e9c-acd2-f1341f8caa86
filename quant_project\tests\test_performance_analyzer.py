"""
性能分析模块测试脚本
用于测试性能分析模块的各项指标计算功能
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core_logic.performance_analyzer import PerformanceAnalyzer
from core_logic.utils import setup_logger

# 设置日志
logger = setup_logger(log_file='logs/test_performance.log')

def create_test_data():
    """创建测试数据"""
    logger.info("创建测试数据")
    
    # 创建日期范围
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    
    # 创建模拟的组合净值序列
    # 场景1: 稳定上涨
    rising_values = pd.Series(np.linspace(100000, 150000, len(dates)), index=dates)
    
    # 场景2: 波动上涨
    np.random.seed(42)  # 设置随机种子，确保结果可重现
    random_returns = np.random.normal(0.001, 0.01, len(dates))
    volatile_values = pd.Series(100000 * (1 + random_returns).cumprod(), index=dates)
    
    # 场景3: 先涨后跌
    rise_fall_values = pd.Series(index=dates)
    mid_point = len(dates) // 2
    rise_fall_values.iloc[:mid_point] = np.linspace(100000, 130000, mid_point)
    rise_fall_values.iloc[mid_point:] = np.linspace(130000, 110000, len(dates) - mid_point)
    
    # 创建模拟的基准净值序列
    benchmark_values = pd.Series(np.linspace(100000, 120000, len(dates)), index=dates)
    
    # 创建模拟的交易记录
    trades = []
    
    # 场景1的交易记录: 全部盈利
    for i in range(5):
        buy_date = dates[i * 30]
        sell_date = dates[i * 30 + 15]
        trades.append({
            'symbol': 'AAPL',
            'buy_date': buy_date,
            'sell_date': sell_date,
            'direction': 'long',
            'buy_price': 150.0,
            'sell_price': 160.0,
            'quantity': 100,
            'commission': 30.0,
            'profit': 1000 - 30.0
        })
    
    # 场景2的交易记录: 部分盈利，部分亏损
    for i in range(5, 10):
        buy_date = dates[i * 30]
        sell_date = dates[i * 30 + 15]
        buy_price = 150.0
        sell_price = 145.0 if i % 2 == 0 else 155.0
        profit = (sell_price - buy_price) * 100 - 30.0
        trades.append({
            'symbol': 'AAPL',
            'buy_date': buy_date,
            'sell_date': sell_date,
            'direction': 'long',
            'buy_price': buy_price,
            'sell_price': sell_price,
            'quantity': 100,
            'commission': 30.0,
            'profit': profit
        })
    
    # 场景3的交易记录: 全部亏损
    for i in range(10, 12):
        buy_date = dates[i * 30] if i * 30 < len(dates) else dates[-30]
        sell_date = dates[i * 30 + 15] if i * 30 + 15 < len(dates) else dates[-15]
        trades.append({
            'symbol': 'AAPL',
            'buy_date': buy_date,
            'sell_date': sell_date,
            'direction': 'long',
            'buy_price': 150.0,
            'sell_price': 140.0,
            'quantity': 100,
            'commission': 30.0,
            'profit': -1000 - 30.0
        })
    
    return {
        'rising_values': rising_values,
        'volatile_values': volatile_values,
        'rise_fall_values': rise_fall_values,
        'benchmark_values': benchmark_values,
        'trades': trades
    }

def test_total_return():
    """测试总回报率计算"""
    logger.info("测试总回报率计算")
    
    # 创建测试数据
    test_data = create_test_data()
    
    # 创建性能分析器
    analyzer = PerformanceAnalyzer()
    
    # 测试不同场景
    scenarios = [
        ('稳定上涨', test_data['rising_values']),
        ('波动上涨', test_data['volatile_values']),
        ('先涨后跌', test_data['rise_fall_values'])
    ]
    
    results = []
    
    for name, values in scenarios:
        # 计算总回报率
        total_return = analyzer.calculate_total_return(values)
        
        # 手动计算预期值
        expected_return = (values.iloc[-1] / values.iloc[0]) - 1
        
        # 验证结果
        is_correct = np.isclose(total_return, expected_return, rtol=1e-5)
        
        results.append({
            'scenario': name,
            'calculated': total_return,
            'expected': expected_return,
            'is_correct': is_correct
        })
        
        logger.info(f"场景: {name}, 计算值: {total_return:.4f}, 预期值: {expected_return:.4f}, 正确: {is_correct}")
    
    return pd.DataFrame(results)

def test_annualized_return():
    """测试年化回报率计算"""
    logger.info("测试年化回报率计算")
    
    # 创建测试数据
    test_data = create_test_data()
    
    # 创建性能分析器
    analyzer = PerformanceAnalyzer()
    
    # 测试不同场景
    scenarios = [
        ('稳定上涨', test_data['rising_values']),
        ('波动上涨', test_data['volatile_values']),
        ('先涨后跌', test_data['rise_fall_values'])
    ]
    
    results = []
    
    for name, values in scenarios:
        # 计算年化回报率
        annualized_return = analyzer.calculate_annualized_return(values)
        
        # 手动计算预期值
        total_return = (values.iloc[-1] / values.iloc[0]) - 1
        days = (values.index[-1] - values.index[0]).days
        expected_return = (1 + total_return) ** (365 / days) - 1
        
        # 验证结果
        is_correct = np.isclose(annualized_return, expected_return, rtol=1e-5)
        
        results.append({
            'scenario': name,
            'calculated': annualized_return,
            'expected': expected_return,
            'is_correct': is_correct
        })
        
        logger.info(f"场景: {name}, 计算值: {annualized_return:.4f}, 预期值: {expected_return:.4f}, 正确: {is_correct}")
    
    return pd.DataFrame(results)

def test_max_drawdown():
    """测试最大回撤计算"""
    logger.info("测试最大回撤计算")
    
    # 创建测试数据
    test_data = create_test_data()
    
    # 创建性能分析器
    analyzer = PerformanceAnalyzer()
    
    # 测试不同场景
    scenarios = [
        ('稳定上涨', test_data['rising_values']),
        ('波动上涨', test_data['volatile_values']),
        ('先涨后跌', test_data['rise_fall_values'])
    ]
    
    results = []
    
    for name, values in scenarios:
        # 计算最大回撤
        drawdown_info = analyzer.calculate_max_drawdown(values)
        max_drawdown = drawdown_info['max_drawdown']
        
        # 手动计算预期值
        cummax = values.cummax()
        drawdown = (values - cummax) / cummax
        expected_max_drawdown = drawdown.min()
        
        # 验证结果
        is_correct = np.isclose(max_drawdown, expected_max_drawdown, rtol=1e-5)
        
        results.append({
            'scenario': name,
            'calculated': max_drawdown,
            'expected': expected_max_drawdown,
            'is_correct': is_correct
        })
        
        logger.info(f"场景: {name}, 计算值: {max_drawdown:.4f}, 预期值: {expected_max_drawdown:.4f}, 正确: {is_correct}")
    
    return pd.DataFrame(results)

def test_sharpe_ratio():
    """测试夏普比率计算"""
    logger.info("测试夏普比率计算")
    
    # 创建测试数据
    test_data = create_test_data()
    
    # 创建性能分析器
    analyzer = PerformanceAnalyzer(risk_free_rate=0.02)  # 设置无风险利率为2%
    
    # 测试不同场景
    scenarios = [
        ('稳定上涨', test_data['rising_values']),
        ('波动上涨', test_data['volatile_values']),
        ('先涨后跌', test_data['rise_fall_values'])
    ]
    
    results = []
    
    for name, values in scenarios:
        # 计算收益率序列
        returns = values.pct_change().dropna()
        
        # 计算夏普比率
        sharpe_ratio = analyzer.calculate_sharpe_ratio(returns)
        
        # 手动计算预期值
        excess_returns = returns - analyzer.risk_free_rate / 252  # 日化无风险利率
        expected_sharpe = np.sqrt(252) * excess_returns.mean() / excess_returns.std()
        
        # 验证结果
        is_correct = np.isclose(sharpe_ratio, expected_sharpe, rtol=1e-5)
        
        results.append({
            'scenario': name,
            'calculated': sharpe_ratio,
            'expected': expected_sharpe,
            'is_correct': is_correct
        })
        
        logger.info(f"场景: {name}, 计算值: {sharpe_ratio:.4f}, 预期值: {expected_sharpe:.4f}, 正确: {is_correct}")
    
    return pd.DataFrame(results)

def test_trade_statistics():
    """测试交易统计计算"""
    logger.info("测试交易统计计算")
    
    # 创建测试数据
    test_data = create_test_data()
    trades = test_data['trades']
    
    # 创建性能分析器
    analyzer = PerformanceAnalyzer()
    
    # 计算交易统计
    trade_stats = analyzer.calculate_trade_statistics(pd.DataFrame(trades))
    
    # 手动计算预期值
    trades_df = pd.DataFrame(trades)
    total_trades = len(trades_df)
    profitable_trades = trades_df[trades_df['profit'] > 0]
    loss_trades = trades_df[trades_df['profit'] <= 0]
    
    expected_win_rate = len(profitable_trades) / total_trades
    expected_avg_profit = profitable_trades['profit'].mean() if len(profitable_trades) > 0 else 0
    expected_avg_loss = loss_trades['profit'].mean() if len(loss_trades) > 0 else 0
    expected_profit_loss_ratio = abs(expected_avg_profit / expected_avg_loss) if expected_avg_loss != 0 else 0
    
    # 验证结果
    results = {
        'total_trades': {'calculated': trade_stats['total_trades'], 'expected': total_trades},
        'win_rate': {'calculated': trade_stats['win_rate'], 'expected': expected_win_rate},
        'avg_profit': {'calculated': trade_stats['avg_profit'], 'expected': expected_avg_profit},
        'avg_loss': {'calculated': trade_stats['avg_loss'], 'expected': expected_avg_loss},
        'profit_loss_ratio': {'calculated': trade_stats['profit_loss_ratio'], 'expected': expected_profit_loss_ratio}
    }
    
    for metric, values in results.items():
        is_correct = np.isclose(values['calculated'], values['expected'], rtol=1e-5)
        logger.info(f"指标: {metric}, 计算值: {values['calculated']:.4f}, 预期值: {values['expected']:.4f}, 正确: {is_correct}")
    
    return results

def run_all_tests():
    """运行所有测试"""
    logger.info("开始运行所有性能分析模块测试")
    
    # 确保测试目录存在
    os.makedirs('tests', exist_ok=True)
    
    # 运行各项测试
    total_return_results = test_total_return()
    annualized_return_results = test_annualized_return()
    max_drawdown_results = test_max_drawdown()
    sharpe_ratio_results = test_sharpe_ratio()
    trade_stats_results = test_trade_statistics()
    
    # 汇总测试结果
    all_correct = (
        total_return_results['is_correct'].all() and
        annualized_return_results['is_correct'].all() and
        max_drawdown_results['is_correct'].all() and
        sharpe_ratio_results['is_correct'].all()
    )
    
    logger.info(f"所有测试完成，测试结果: {'全部通过' if all_correct else '部分失败'}")
    
    # 保存测试结果
    total_return_results.to_csv('tests/performance_total_return_results.csv', index=False)
    annualized_return_results.to_csv('tests/performance_annualized_return_results.csv', index=False)
    max_drawdown_results.to_csv('tests/performance_max_drawdown_results.csv', index=False)
    sharpe_ratio_results.to_csv('tests/performance_sharpe_ratio_results.csv', index=False)
    
    return all_correct

if __name__ == "__main__":
    run_all_tests()
