"""
模型训练页面组件
实现DRL模型训练、监控和管理功能
"""

import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import logging
import os
import time
from datetime import datetime

logger = logging.getLogger('drl_trading')

def render_model_training():
    """渲染模型训练页面"""
    st.markdown("## 🤖 模型训练")
    
    # 初始化会话状态
    if 'training_status' not in st.session_state:
        st.session_state.training_status = None
    if 'training_progress' not in st.session_state:
        st.session_state.training_progress = 0
    if 'training_metrics' not in st.session_state:
        st.session_state.training_metrics = []
    if 'model' not in st.session_state:
        st.session_state.model = None
    
    # 使用选项卡组织不同功能
    tabs = st.tabs(["训练配置", "训练监控", "模型管理"])
    
    with tabs[0]:
        st.markdown("### 训练配置")
        
        # 检查是否已加载数据
        if 'data' not in st.session_state or st.session_state.data is None:
            st.warning("请先在数据中心加载数据")
            st.button("跳转到数据中心", on_click=lambda: setattr(st.session_state, 'current_page', "数据中心"))
            return
        
        # 使用表单收集训练参数
        with st.form("training_form"):
            st.markdown("#### 环境配置")
            
            col1, col2 = st.columns(2)
            
            with col1:
                initial_capital = st.number_input("初始资金", min_value=10000, max_value=10000000, value=100000, step=10000,
                                                help="模拟交易的初始资金")
                
                commission_rate = st.number_input("交易佣金率", min_value=0.0, max_value=0.01, value=0.0003, format="%.5f",
                                               help="交易佣金率，如0.0003表示万分之三")
            
            with col2:
                max_position = st.slider("最大仓位比例", min_value=0.1, max_value=1.0, value=1.0, step=0.1,
                                       help="最大允许的仓位比例")
                
                allow_short = st.checkbox("允许做空", value=False,
                                       help="是否允许卖空操作")
            
            st.markdown("#### 模型配置")
            
            col1, col2 = st.columns(2)
            
            with col1:
                algorithm = st.selectbox("DRL算法", ["DQN", "A2C", "PPO"],
                                      help="选择深度强化学习算法")
                
                policy = st.selectbox("策略网络", ["MlpPolicy", "CnnPolicy"],
                                    help="选择策略网络类型")
            
            with col2:
                learning_rate = st.number_input("学习率", min_value=0.00001, max_value=0.01, value=0.0001, format="%.5f",
                                             help="模型学习率")
                
                gamma = st.slider("折扣因子", min_value=0.8, max_value=0.999, value=0.99, step=0.01,
                                help="未来奖励的折扣因子")
            
            st.markdown("#### 训练设置")
            
            col1, col2 = st.columns(2)
            
            with col1:
                total_timesteps = st.number_input("训练步数", min_value=10000, max_value=10000000, value=100000, step=10000,
                                                help="总训练步数")
                
                model_name = st.text_input("模型名称", value=f"model_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                                         help="保存模型的名称")
            
            with col2:
                use_gpu = st.checkbox("使用GPU", value=True,
                                     help="是否使用GPU加速训练")
                
                eval_freq = st.number_input("评估频率", min_value=1000, max_value=100000, value=10000, step=1000,
                                          help="每多少步评估一次模型")
            
            # 提交按钮
            submitted = st.form_submit_button("开始训练", use_container_width=True)
            
            if submitted:
                # 更新训练状态
                st.session_state.training_status = "preparing"
                st.session_state.training_progress = 0
                st.session_state.training_metrics = []
                
                # 准备环境配置
                env_config = {
                    "initial_capital": initial_capital,
                    "commission_rate": commission_rate,
                    "max_position": max_position,
                    "allow_short": allow_short,
                    "data": st.session_state.data
                }
                
                # 准备模型配置
                agent_config = {
                    "algorithm": algorithm,
                    "policy_network": policy,
                    "learning_rate": learning_rate,
                    "gamma": gamma,
                    "use_gpu": use_gpu
                }
                
                # 准备训练配置
                training_config = {
                    "total_timesteps": total_timesteps,
                    "model_name": model_name,
                    "eval_freq": eval_freq,
                    "env_config": env_config,
                    "agent_config": agent_config
                }
                
                # 保存配置到会话状态
                st.session_state.training_config = training_config
                
                # 更新状态
                st.session_state.training_status = "running"
                
                # 启动训练（后台运行）
                st.rerun()
    
    with tabs[1]:
        st.markdown("### 训练监控")
        
        # 检查训练状态
        if st.session_state.training_status == "running":
            # 显示训练进度
            st.info("模型训练中...")
            
            # 进度条
            progress_bar = st.progress(st.session_state.training_progress)
            status_text = st.empty()
            
            # 模拟训练过程（在真实环境中，这里会调用实际训练函数）
            if 'training_config' in st.session_state:
                try:
                    # 导入模块
                    try:
                        from core_logic.drl_agent import DRLAgent
                    except ImportError:
                        # 回退到旧版本
                        from core_logic.drl_agent import DRLAgent
                    
                    # 创建交易环境
                    try:
                        from core_logic.trading_env import TradingEnvironment, TradingEnvironmentAdapter
                        env_class = TradingEnvironmentAdapter
                    except ImportError:
                        try:
                            from core_logic.trading_environment import TradingEnvironment
                            env_class = TradingEnvironment
                        except ImportError:
                            raise ImportError("无法导入交易环境模块")
                    
                    # 创建环境和DRL智能体
                    config = st.session_state.training_config
                    
                    # 训练回调类，用于更新UI
                    class TrainingCallback:
                        def __init__(self, total_timesteps):
                            self.total_timesteps = total_timesteps
                            self.current_step = 0
                            self.metrics = []
                        
                        def update(self, locals_dict, globals_dict):
                            self.current_step += 1
                            # 计算进度
                            progress = min(1.0, self.current_step / self.total_timesteps)
                            # 更新会话状态
                            st.session_state.training_progress = progress
                            # 每100步采样一次指标
                            if self.current_step % 100 == 0:
                                # 模拟收集指标
                                reward = locals_dict.get('rewards', [0])[0] if 'rewards' in locals_dict else 0
                                self.metrics.append({
                                    'step': self.current_step,
                                    'reward': reward,
                                    'loss': locals_dict.get('loss', 0) if 'loss' in locals_dict else 0
                                })
                                st.session_state.training_metrics = self.metrics
                            return True
                    
                    # 创建训练回调
                    callback = TrainingCallback(config['total_timesteps'])
                    
                    # 开始训练
                    status_text.text("正在准备训练环境和模型...")
                    
                    # 真实的训练代码（这里模拟进度）
                    # 在实际应用中，这里应该创建实际的环境和智能体，并调用train方法
                    # 本示例中，我们只模拟训练过程来演示UI功能
                    
                    # 模拟训练进度
                    import random
                    for i in range(1, 101):
                        # 更新进度
                        st.session_state.training_progress = i / 100
                        progress_bar.progress(i / 100)
                        
                        # 更新状态文本
                        status_text.text(f"训练进度: {i}% - 正在训练中...")
                        
                        # 模拟指标
                        if i % 10 == 0:
                            # 模拟添加训练指标
                            st.session_state.training_metrics.append({
                                'step': i * (config['total_timesteps'] // 100),
                                'reward': random.uniform(-1, 1) * i / 30,  # 模拟奖励逐渐提高
                                'loss': 1.0 / (i / 10)  # 模拟损失逐渐下降
                            })
                        
                        # 模拟计算时间
                        time.sleep(0.1)
                    
                    # 训练完成
                    st.session_state.training_status = "completed"
                    st.session_state.model = {
                        "name": config['model_name'],
                        "algorithm": config['agent_config']['algorithm'],
                        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                        "metrics": st.session_state.training_metrics
                    }
                    
                    # 模拟保存模型
                    os.makedirs("saved_models", exist_ok=True)
                    model_path = f"saved_models/{config['model_name']}"
                    
                    status_text.text(f"训练完成！模型已保存到 {model_path}")
                    st.success(f"模型训练成功，已保存到 {model_path}")
                    
                except Exception as e:
                    st.error(f"训练过程中出错: {str(e)}")
                    logger.error(f"训练过程中出错: {str(e)}")
                    st.session_state.training_status = "failed"
                    status_text.text(f"训练失败: {str(e)}")
            
            # 显示实时指标
            if st.session_state.training_metrics:
                st.markdown("#### 训练指标")
                
                # 转换为DataFrame
                metrics_df = pd.DataFrame(st.session_state.training_metrics)
                
                # 创建图表
                fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8), sharex=True)
                
                # 绘制奖励曲线
                if 'reward' in metrics_df.columns:
                    ax1.plot(metrics_df['step'], metrics_df['reward'], label='奖励', color='green')
                    ax1.set_ylabel('奖励值')
                    ax1.grid(True, alpha=0.3)
                    ax1.legend()
                
                # 绘制损失曲线
                if 'loss' in metrics_df.columns:
                    ax2.plot(metrics_df['step'], metrics_df['loss'], label='损失', color='red')
                    ax2.set_ylabel('损失值')
                    ax2.set_xlabel('训练步数')
                    ax2.grid(True, alpha=0.3)
                    ax2.legend()
                
                st.pyplot(fig)
        
        elif st.session_state.training_status == "completed":
            st.success("模型训练已完成")
            
            # 显示训练结果
            if st.session_state.model:
                st.markdown("#### 训练结果")
                
                # 显示模型信息
                model = st.session_state.model
                st.info(f"模型名称: {model['name']}, 算法: {model['algorithm']}, 训练时间: {model['timestamp']}")
                
                # 显示最终指标
                if 'metrics' in model and model['metrics']:
                    metrics_df = pd.DataFrame(model['metrics'])
                    
                    # 创建图表
                    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8), sharex=True)
                    
                    # 绘制奖励曲线
                    if 'reward' in metrics_df.columns:
                        ax1.plot(metrics_df['step'], metrics_df['reward'], label='奖励', color='green')
                        ax1.set_ylabel('奖励值')
                        ax1.grid(True, alpha=0.3)
                        ax1.legend()
                    
                    # 绘制损失曲线
                    if 'loss' in metrics_df.columns:
                        ax2.plot(metrics_df['step'], metrics_df['loss'], label='损失', color='red')
                        ax2.set_ylabel('损失值')
                        ax2.set_xlabel('训练步数')
                        ax2.grid(True, alpha=0.3)
                        ax2.legend()
                    
                    st.pyplot(fig)
            
            # 训练新模型按钮
            if st.button("训练新模型"):
                st.session_state.training_status = None
                st.session_state.training_progress = 0
                st.session_state.training_metrics = []
                st.rerun()
        
        elif st.session_state.training_status == "failed":
            st.error("模型训练失败")
            
            # 重试按钮
            if st.button("重新尝试"):
                st.session_state.training_status = None
                st.session_state.training_progress = 0
                st.session_state.training_metrics = []
                st.rerun()
        
        else:
            st.info("尚未开始训练，请在训练配置标签页设置参数并开始训练")
    
    with tabs[2]:
        st.markdown("### 模型管理")
        
        # 检查模型目录
        models_dir = "saved_models"
        os.makedirs(models_dir, exist_ok=True)
        
        # 获取可用模型列表
        available_models = []
        for root, dirs, files in os.walk(models_dir):
            for file in files:
                if file.endswith(".zip"):
                    model_path = os.path.join(root, file)
                    model_name = os.path.splitext(file)[0]
                    # 获取文件修改时间
                    mod_time = os.path.getmtime(model_path)
                    mod_time_str = datetime.fromtimestamp(mod_time).strftime("%Y-%m-%d %H:%M:%S")
                    # 获取文件大小
                    size_bytes = os.path.getsize(model_path)
                    size_mb = size_bytes / (1024 * 1024)
                    
                    available_models.append({
                        "name": model_name,
                        "path": model_path,
                        "modified": mod_time_str,
                        "size_mb": size_mb
                    })
        
        if available_models:
            # 显示模型列表
            st.markdown("#### 可用模型")
            
            # 创建模型表格
            models_df = pd.DataFrame(available_models)
            st.dataframe(models_df, use_container_width=True)
            
            # 模型操作
            st.markdown("#### 模型操作")
            
            col1, col2 = st.columns(2)
            
            with col1:
                # 加载模型
                selected_model = st.selectbox("选择模型", [model["name"] for model in available_models])
                
                if st.button("加载模型"):
                    with st.spinner("正在加载模型..."):
                        try:
                            # 获取模型路径
                            model_path = next(model["path"] for model in available_models if model["name"] == selected_model)
                            
                            # 加载模型（模拟）
                            st.session_state.model = {
                                "name": selected_model,
                                "algorithm": "DRL",
                                "timestamp": next(model["modified"] for model in available_models if model["name"] == selected_model),
                                "path": model_path
                            }
                            
                            st.success(f"模型 {selected_model} 加载成功")
                            logger.info(f"模型 {selected_model} 加载成功")
                        except Exception as e:
                            st.error(f"加载模型失败: {str(e)}")
                            logger.error(f"加载模型失败: {str(e)}")
            
            with col2:
                # 删除模型
                if st.button("删除选中模型", help="永久删除选择的模型"):
                    try:
                        # 获取模型路径
                        model_path = next(model["path"] for model in available_models if model["name"] == selected_model)
                        
                        # 删除模型文件
                        os.remove(model_path)
                        
                        st.success(f"模型 {selected_model} 已删除")
                        logger.info(f"模型 {selected_model} 已删除")
                        
                        # 刷新页面
                        st.rerun()
                    except Exception as e:
                        st.error(f"删除模型失败: {str(e)}")
                        logger.error(f"删除模型失败: {str(e)}")
        else:
            st.info("未找到保存的模型，请先训练模型")

    # 如果是自动训练模式并且有需要的参数，自动启动训练
    if st.session_state.get('auto_train_with_factors', False) and not st.session_state.get('auto_train_action_executed', False):
        st.session_state.auto_train_action_executed = True
        # 获取自动训练参数
        auto_params = st.session_state.get('auto_train_params', {})
        
        # 设置训练参数
        if 'algorithm' in auto_params:
            st.session_state.algorithm = auto_params['algorithm']
        if 'epochs' in auto_params:
            st.session_state.epochs = auto_params['epochs']
        if 'use_gpu' in auto_params:
            st.session_state.use_gpu = auto_params['use_gpu']
        if 'use_ensemble' in auto_params:
            st.session_state.use_ensemble = auto_params['use_ensemble']
        
        # 自动点击开始训练按钮
        st.session_state.start_training = True
        st.rerun() 