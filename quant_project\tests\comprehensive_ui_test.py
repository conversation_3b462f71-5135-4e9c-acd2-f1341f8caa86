"""
全面UI测试脚本

该脚本实现了UI测试方案中的所有测试任务，包括：
1. 基础功能测试
2. 数据处理模块交互测试
3. 特征工程模块交互测试
4. 交易环境模块交互测试
5. DRL智能体模块交互测试
6. 模型评估与回测模块交互测试
7. 参数组合逻辑测试
8. 会话状态管理测试
"""

import os
import sys
import time
import logging
import unittest
import json
import re
import subprocess
from datetime import datetime, timedelta
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(parent_dir, 'logs', 'comprehensive_ui_test.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('comprehensive_ui_test')

class ComprehensiveUITest(unittest.TestCase):
    """全面UI测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        logger.info("设置测试环境")
        
        # 启动Streamlit应用
        cls.start_streamlit_app()
        
        # 设置WebDriver
        chrome_options = Options()
        # chrome_options.add_argument("--headless")  # 无头模式，可以注释掉以查看浏览器操作
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--start-maximized")
        
        cls.driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)
        cls.driver.get("http://localhost:8501")
        
        # 等待页面加载完成
        WebDriverWait(cls.driver, 30).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        logger.info("Streamlit应用已加载")
        
        # 等待侧边栏加载完成
        WebDriverWait(cls.driver, 30).until(
            EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'stSidebar')]"))
        )
        logger.info("侧边栏已加载")
    
    @classmethod
    def tearDownClass(cls):
        """清理测试环境"""
        logger.info("清理测试环境")
        
        # 关闭WebDriver
        if cls.driver:
            cls.driver.quit()
        
        # 停止Streamlit应用
        cls.stop_streamlit_app()
    
    @classmethod
    def start_streamlit_app(cls):
        """启动Streamlit应用"""
        logger.info("启动Streamlit应用")
        
        # 使用subprocess启动应用
        import subprocess
        import threading
        
        def run_app():
            app_file = os.path.join(parent_dir, 'main_app.py')
            subprocess.run([sys.executable, "-m", "streamlit", "run", app_file], 
                          stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 在后台线程中启动应用
        cls.app_thread = threading.Thread(target=run_app)
        cls.app_thread.daemon = True
        cls.app_thread.start()
        
        # 等待应用启动
        time.sleep(15)
        logger.info("Streamlit应用已启动")
    
    @classmethod
    def stop_streamlit_app(cls):
        """停止Streamlit应用"""
        logger.info("停止Streamlit应用")
        
        # 在Windows上使用taskkill命令终止Streamlit进程
        import subprocess
        subprocess.run(["taskkill", "/f", "/im", "streamlit.exe"], 
                      stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        subprocess.run(["taskkill", "/f", "/im", "python.exe", "/fi", "WINDOWTITLE eq streamlit"], 
                      stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        logger.info("Streamlit应用已停止")
    
    def wait_for_element(self, by, value, timeout=10):
        """等待元素出现"""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((by, value))
            )
            return element
        except TimeoutException:
            logger.error(f"等待元素超时: {by}={value}")
            return None
    
    def wait_for_clickable_element(self, by, value, timeout=10):
        """等待元素可点击"""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.element_to_be_clickable((by, value))
            )
            return element
        except TimeoutException:
            logger.error(f"等待元素可点击超时: {by}={value}")
            return None
    
    def scroll_to_element(self, element):
        """滚动到元素位置"""
        self.driver.execute_script("arguments[0].scrollIntoView(true);", element)
        time.sleep(1)  # 等待滚动完成
    
    def navigate_to_page(self, page_name):
        """导航到指定页面"""
        logger.info(f"导航到页面: {page_name}")
        
        # 查找侧边栏导航选项
        nav_options = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'stRadio')]//label")
        for option in nav_options:
            if page_name in option.text:
                self.scroll_to_element(option)
                option.click()
                time.sleep(3)  # 等待页面加载
                logger.info(f"已导航到页面: {page_name}")
                return True
        
        logger.error(f"未找到页面: {page_name}")
        return False
    
    def test_01_app_startup(self):
        """测试应用启动"""
        logger.info("测试应用启动")
        
        # 验证页面标题
        self.assertIn("DRL量化交易系统", self.driver.title)
        
        # 验证侧边栏存在
        sidebar = self.wait_for_element(By.XPATH, "//div[contains(@class, 'stSidebar')]")
        self.assertIsNotNone(sidebar)
        
        # 验证导航选项存在
        nav_options = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'stRadio')]//label")
        self.assertGreaterEqual(len(nav_options), 5)
        
        # 验证页面主标题存在
        main_header = self.wait_for_element(By.XPATH, "//div[contains(@class, 'main-header')]")
        self.assertIsNotNone(main_header)
        
        logger.info("应用启动测试通过")
    
    def test_02_module_imports(self):
        """测试模块导入"""
        logger.info("测试模块导入")
        
        # 检查日志控制台页面，查看模块导入信息
        self.navigate_to_page("日志控制台")
        
        # 等待日志内容加载
        log_content = self.wait_for_element(By.XPATH, "//pre")
        self.assertIsNotNone(log_content)
        
        # 验证核心模块导入成功
        log_text = log_content.text
        self.assertIn("成功导入", log_text)
        
        logger.info("模块导入测试通过")
    
    def test_03_data_acquisition(self):
        """测试数据获取功能"""
        logger.info("测试数据获取功能")
        
        # 导航到数据中心页面
        self.navigate_to_page("数据中心与环境配置")
        
        # 等待页面加载
        time.sleep(3)
        
        # 输入股票代码
        stock_code_input = self.wait_for_element(By.XPATH, "//input[@aria-label='金融产品代码']")
        self.assertIsNotNone(stock_code_input)
        stock_code_input.clear()
        stock_code_input.send_keys("sh000001")
        
        # 设置日期范围（简化处理）
        
        # 选择数据频率
        frequency_select = self.wait_for_element(By.XPATH, "//div[text()='数据频率']/..//div[contains(@class, 'stSelectbox')]")
        if frequency_select:
            frequency_select.click()
            time.sleep(1)
            daily_option = self.wait_for_element(By.XPATH, "//div[text()='日线']")
            if daily_option:
                daily_option.click()
        
        # 点击获取数据按钮
        get_data_button = self.wait_for_clickable_element(By.XPATH, "//button[text()='获取数据']")
        self.assertIsNotNone(get_data_button)
        get_data_button.click()
        
        # 等待数据加载
        time.sleep(15)
        
        # 验证数据是否加载成功
        success_message = self.wait_for_element(By.XPATH, "//div[contains(text(), '成功获取数据')]")
        self.assertIsNotNone(success_message)
        
        # 验证数据表格是否显示
        data_table = self.wait_for_element(By.XPATH, "//div[contains(@class, 'stDataFrame')]")
        self.assertIsNotNone(data_table)
        
        logger.info("数据获取功能测试通过")
    
    def test_04_feature_engineering(self):
        """测试特征工程功能"""
        logger.info("测试特征工程功能")
        
        # 确保数据已加载
        if not self.wait_for_element(By.XPATH, "//div[contains(@class, 'stDataFrame')]"):
            self.test_03_data_acquisition()
        
        # 滚动到特征工程部分
        feature_engineering_header = self.wait_for_element(By.XPATH, "//h2[text()='特征工程配置']")
        self.assertIsNotNone(feature_engineering_header)
        self.scroll_to_element(feature_engineering_header)
        
        # 点击生成特征按钮
        generate_features_button = self.wait_for_clickable_element(By.XPATH, "//button[text()='生成特征']")
        self.assertIsNotNone(generate_features_button)
        generate_features_button.click()
        
        # 等待特征生成
        time.sleep(20)
        
        # 验证特征是否生成成功
        success_message = self.wait_for_element(By.XPATH, "//div[contains(text(), '成功生成特征')]")
        self.assertIsNotNone(success_message)
        
        # 验证处理后的数据表格是否显示
        processed_data_table = self.wait_for_element(By.XPATH, "//h3[text()='处理后的数据预览']/..//div[contains(@class, 'stDataFrame')]")
        self.assertIsNotNone(processed_data_table)
        
        logger.info("特征工程功能测试通过")
    
    def test_05_environment_config(self):
        """测试交易环境配置功能"""
        logger.info("测试交易环境配置功能")
        
        # 滚动到交易环境参数配置部分
        env_config_header = self.wait_for_element(By.XPATH, "//h2[text()='交易环境参数配置']")
        self.assertIsNotNone(env_config_header)
        self.scroll_to_element(env_config_header)
        
        # 设置初始资金
        initial_capital_input = self.wait_for_element(By.XPATH, "//label[text()='初始资金']/..//input")
        self.assertIsNotNone(initial_capital_input)
        initial_capital_input.clear()
        initial_capital_input.send_keys("200000")
        
        # 设置手续费率
        commission_rate_input = self.wait_for_element(By.XPATH, "//label[text()='手续费率 (单边)']/..//input")
        self.assertIsNotNone(commission_rate_input)
        commission_rate_input.clear()
        commission_rate_input.send_keys("0.0005")
        
        # 点击保存环境配置按钮
        save_config_button = self.wait_for_clickable_element(By.XPATH, "//button[text()='保存环境配置']")
        self.assertIsNotNone(save_config_button)
        save_config_button.click()
        
        # 等待配置保存
        time.sleep(3)
        
        # 验证配置是否保存成功
        success_message = self.wait_for_element(By.XPATH, "//div[contains(text(), '环境配置已保存')]")
        self.assertIsNotNone(success_message)
        
        logger.info("交易环境配置功能测试通过")
    
    def test_06_drl_agent_training(self):
        """测试DRL智能体训练功能"""
        logger.info("测试DRL智能体训练功能")
        
        # 切换到DRL智能体训练页面
        self.navigate_to_page("DRL智能体训练")
        
        # 设置算法参数
        algorithm_select = self.wait_for_element(By.XPATH, "//label[text()='DRL算法']/..//div[contains(@class, 'stSelectbox')]")
        self.assertIsNotNone(algorithm_select)
        algorithm_select.click()
        time.sleep(1)
        ppo_option = self.wait_for_element(By.XPATH, "//div[text()='PPO']")
        self.assertIsNotNone(ppo_option)
        ppo_option.click()
        
        # 设置训练步数
        timesteps_input = self.wait_for_element(By.XPATH, "//label[text()='训练总步数']/..//input")
        self.assertIsNotNone(timesteps_input)
        timesteps_input.clear()
        timesteps_input.send_keys("10000")  # 设置较小的步数以加快测试
        
        # 点击开始训练按钮
        start_training_button = self.wait_for_clickable_element(By.XPATH, "//button[text()='开始训练']")
        self.assertIsNotNone(start_training_button)
        start_training_button.click()
        
        # 等待训练开始
        time.sleep(10)
        
        # 验证训练是否开始
        training_progress = self.wait_for_element(By.XPATH, "//div[contains(text(), '训练进度')]")
        self.assertIsNotNone(training_progress)
        
        # 测试停止训练按钮
        stop_training_button = self.wait_for_clickable_element(By.XPATH, "//button[text()='停止训练']")
        self.assertIsNotNone(stop_training_button)
        stop_training_button.click()
        
        # 等待训练停止
        time.sleep(5)
        
        logger.info("DRL智能体训练功能测试通过")

if __name__ == "__main__":
    unittest.main()
