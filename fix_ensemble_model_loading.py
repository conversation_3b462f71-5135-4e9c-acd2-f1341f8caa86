"""
修复集成学习模型加载问题

这个脚本用于修复集成学习模型加载问题，确保集成学习模型能够被正确加载和识别。
"""

import os
import sys
import logging
import json
import shutil
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('fix_ensemble_model')

def check_ensemble_model_files():
    """检查集成学习模型文件"""
    # 检查saved_models目录
    saved_models_dir = Path("saved_models")
    if not saved_models_dir.exists():
        logger.error("saved_models目录不存在")
        return False
    
    # 获取所有模型文件
    model_files = [f for f in os.listdir(saved_models_dir) if f.endswith('.zip')]
    logger.info(f"找到 {len(model_files)} 个模型文件")
    
    # 检查集成学习模型
    ensemble_models = []
    for model_file in model_files:
        if 'ENSEMBLE' in model_file:
            ensemble_models.append(model_file)
            
            # 检查对应的集成目录
            ensemble_dir = model_file.replace('.zip', '_ensemble')
            ensemble_dir_path = saved_models_dir / ensemble_dir
            
            if ensemble_dir_path.exists() and ensemble_dir_path.is_dir():
                logger.info(f"找到集成学习模型: {model_file}，对应集成目录: {ensemble_dir}")
                
                # 检查集成配置文件
                config_file = ensemble_dir_path / "ensemble_config.json"
                if config_file.exists():
                    try:
                        with open(config_file, 'r') as f:
                            config = json.load(f)
                        logger.info(f"集成配置文件正常: {config_file}")
                        
                        # 检查子模型文件
                        for model_path in config['model_paths']:
                            if not os.path.exists(model_path):
                                logger.error(f"子模型文件不存在: {model_path}")
                                return False
                    except Exception as e:
                        logger.error(f"读取集成配置文件失败: {str(e)}")
                        return False
                else:
                    logger.error(f"集成配置文件不存在: {config_file}")
                    return False
            else:
                logger.warning(f"集成学习模型 {model_file} 没有对应的集成目录")
    
    logger.info(f"找到 {len(ensemble_models)} 个集成学习模型")
    return True

def fix_main_app():
    """修复main_app.py中的模型加载代码"""
    main_app_path = Path("main_app.py")
    if not main_app_path.exists():
        logger.error("main_app.py文件不存在")
        return False
    
    # 读取文件内容
    with open(main_app_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否需要修复
    if "启动时添加集成模型到当前训练模型列表" not in content:
        # 添加集成模型加载代码
        # 查找初始化代码位置
        init_pattern = "# 初始化training_in_progress=False"
        if init_pattern in content:
            # 在初始化代码后添加集成模型加载代码
            new_code = """
    # 检查并加载集成学习模型
    ensemble_models = []
    for model_file in os.listdir("saved_models"):
        if model_file.endswith('.zip') and ('ENSEMBLE' in model_file or '_ENSEMBLE' in model_file):
            # 检查对应的集成目录
            ensemble_dir = model_file.replace('.zip', '_ensemble')
            ensemble_dir_path = os.path.join("saved_models", ensemble_dir)
            
            if os.path.exists(ensemble_dir_path) and os.path.isdir(ensemble_dir_path):
                ensemble_models.append(model_file)
                logger.info(f"启动时添加集成模型到当前训练模型列表: {model_file}")
    
    # 将集成模型添加到当前训练模型列表
    if 'current_trained_models' not in st.session_state:
        st.session_state.current_trained_models = []
    
    for model in ensemble_models:
        if model not in st.session_state.current_trained_models:
            st.session_state.current_trained_models.append(model)
            """
            
            content = content.replace(init_pattern, init_pattern + new_code)
            logger.info("添加了集成模型加载代码")
        else:
            logger.warning("未找到初始化代码位置")
    else:
        logger.info("已存在集成模型加载代码，无需修复")
    
    # 保存修改后的文件
    with open(main_app_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    logger.info("main_app.py文件修复完成")
    return True

def fix_model_loading_code():
    """修复模型加载代码"""
    # 检查core_logic/enhanced_drl_agent.py文件
    agent_path = Path("core_logic/enhanced_drl_agent.py")
    if not agent_path.exists():
        logger.error("core_logic/enhanced_drl_agent.py文件不存在")
        return False
    
    # 读取文件内容
    with open(agent_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否需要修复
    if "# 如果没有找到标准命名的集成目录，尝试其他可能的命名格式" not in content:
        # 查找集成模型加载代码位置
        load_pattern = "# 检查是否是集成模型"
        if load_pattern in content:
            # 在集成模型加载代码后添加更多检查逻辑
            new_code = """
        # 检查是否是集成模型
        ensemble_dir = load_path.replace('.zip', '_ensemble')
        is_ensemble = os.path.exists(ensemble_dir) and os.path.isdir(ensemble_dir) and ENSEMBLE_AVAILABLE

        # 如果没有找到标准命名的集成目录，尝试其他可能的命名格式
        if not is_ensemble and ENSEMBLE_AVAILABLE:
            # 检查是否是ENSEMBLE模型
            model_filename = os.path.basename(load_path)
            if 'ENSEMBLE' in model_filename or '_ENSEMBLE' in model_filename:
                # 尝试查找对应的集成目录
                possible_dirs = [
                    os.path.join(os.path.dirname(load_path), model_filename.replace('.zip', '_ensemble')),
                    os.path.join(os.path.dirname(load_path), model_filename.replace('.zip', '') + '_ensemble'),
                    os.path.join(os.path.dirname(load_path), model_filename.replace('.zip', '') + '_ENSEMBLE')
                ]

                for possible_dir in possible_dirs:
                    if os.path.exists(possible_dir) and os.path.isdir(possible_dir):
                        ensemble_dir = possible_dir
                        is_ensemble = True
                        self.logger.info(f"找到非标准命名的集成目录: {ensemble_dir}")
                        break"""
            
            content = content.replace(load_pattern, new_code)
            logger.info("添加了更多集成模型检查逻辑")
        else:
            logger.warning("未找到集成模型加载代码位置")
    else:
        logger.info("已存在更多集成模型检查逻辑，无需修复")
    
    # 保存修改后的文件
    with open(agent_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    logger.info("core_logic/enhanced_drl_agent.py文件修复完成")
    return True

def main():
    """主函数"""
    logger.info("开始修复集成学习模型加载问题")
    
    # 检查集成学习模型文件
    if not check_ensemble_model_files():
        logger.error("集成学习模型文件检查失败")
        return False
    
    # 修复main_app.py
    if not fix_main_app():
        logger.error("修复main_app.py失败")
        return False
    
    # 修复模型加载代码
    if not fix_model_loading_code():
        logger.error("修复模型加载代码失败")
        return False
    
    logger.info("集成学习模型加载问题修复完成")
    return True

if __name__ == "__main__":
    main()
