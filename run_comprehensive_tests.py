#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全面测试执行脚本
根据强化测试方案执行全面测试
"""

import os
import sys
import time
import logging
import subprocess
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import traceback
import importlib
import json
import yaml
import matplotlib.pyplot as plt

# 设置日志
os.makedirs('logs', exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join('logs', 'comprehensive_test.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('comprehensive_test')

def test_environment_setup():
    """测试环境设置"""
    logger.info("测试环境设置")

    # 检查Python版本
    python_version = sys.version
    logger.info(f"Python版本: {python_version}")

    # 检查项目结构
    expected_dirs = ['core_logic', 'quant_project', 'data_cache', 'saved_models', 'logs']
    missing_dirs = [d for d in expected_dirs if not os.path.isdir(d)]

    if missing_dirs:
        logger.warning(f"缺少目录: {missing_dirs}")
    else:
        logger.info("项目目录结构完整")

    # 检查依赖项
    try:
        import pandas as pd
        import numpy as np
        import matplotlib.pyplot as plt
        import akshare
        import torch
        import gymnasium
        import stable_baselines3
        logger.info("成功导入所有核心依赖")
        dependencies_ok = True
    except ImportError as e:
        logger.error(f"导入依赖失败: {str(e)}")
        dependencies_ok = False

    # 检查GPU支持
    try:
        import torch
        gpu_available = torch.cuda.is_available()
        gpu_count = torch.cuda.device_count() if gpu_available else 0
        gpu_name = torch.cuda.get_device_name(0) if gpu_available and gpu_count > 0 else "N/A"

        logger.info(f"GPU支持: {'可用' if gpu_available else '不可用'}")
        logger.info(f"GPU数量: {gpu_count}")
        logger.info(f"GPU名称: {gpu_name}")

        gpu_support = {
            'available': gpu_available,
            'count': gpu_count,
            'name': gpu_name
        }
    except Exception as e:
        logger.error(f"检查GPU支持时出错: {str(e)}")
        gpu_support = {
            'available': False,
            'count': 0,
            'name': "检查失败"
        }

    return {
        'python_version': python_version,
        'missing_dirs': missing_dirs,
        'dependencies_ok': dependencies_ok,
        'gpu_support': gpu_support
    }

def test_data_processing():
    """测试数据处理模块"""
    logger.info("测试数据处理模块")

    try:
        # 尝试导入数据处理模块
        try:
            # 添加项目根目录到Python路径
            sys.path.append(os.path.abspath('.'))
            sys.path.append(os.path.abspath('./quant_project'))

            try:
                from quant_project.core_logic.data_handler import DataHandler
                logger.info("成功导入DataHandler模块")
            except ImportError:
                try:
                    from core_logic.data_handler import DataHandler
                    logger.info("成功导入DataHandler模块(从根目录)")
                except ImportError:
                    try:
                        import quant_project
                        logger.info(f"quant_project路径: {quant_project.__path__}")
                        from quant_project.core_logic.data_handler import DataHandler
                        logger.info("成功导入DataHandler模块(使用绝对路径)")
                    except ImportError as e:
                        logger.error(f"导入DataHandler模块失败: {str(e)}")
                        return {
                            'success': False,
                            'error': f"导入DataHandler模块失败: {str(e)}"
                        }

        # 创建数据处理器实例
        data_handler = DataHandler()

        # 测试不同数据类型
        data_types = [
            {'type': '股票', 'codes': ['sh000001', 'sh600000', 'sz000001']},
            {'type': '指数', 'codes': ['sh000001', 'sz399001', 'sh000300']}
        ]

        # 测试不同日期范围
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_dates = [
            (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'),
            (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
        ]

        # 测试不同频率
        frequencies = ['日线', '周线']

        results = []

        # 测试组合
        for data_type in data_types:
            for code in data_type['codes'][:1]:  # 只测试每种类型的第一个代码
                for start_date in start_dates[:1]:  # 只测试第一个日期范围
                    for frequency in frequencies[:1]:  # 只测试第一个频率
                        try:
                            logger.info(f"获取{data_type['type']}数据: {code}, {start_date} 到 {end_date}, {frequency}")
                            data = data_handler.get_stock_data(
                                stock_code=code,
                                start_date=start_date,
                                end_date=end_date,
                                frequency=frequency
                            )

                            if data is not None and not data.empty:
                                logger.info(f"成功获取数据: {len(data)} 条记录")
                                results.append({
                                    'type': data_type['type'],
                                    'code': code,
                                    'start_date': start_date,
                                    'end_date': end_date,
                                    'frequency': frequency,
                                    'success': True,
                                    'records': len(data)
                                })
                            else:
                                logger.warning(f"获取数据失败: 数据为空")
                                results.append({
                                    'type': data_type['type'],
                                    'code': code,
                                    'start_date': start_date,
                                    'end_date': end_date,
                                    'frequency': frequency,
                                    'success': False,
                                    'error': '数据为空'
                                })
                        except Exception as e:
                            logger.error(f"获取数据出错: {str(e)}")
                            results.append({
                                'type': data_type['type'],
                                'code': code,
                                'start_date': start_date,
                                'end_date': end_date,
                                'frequency': frequency,
                                'success': False,
                                'error': str(e)
                            })

        # 测试数据缓存
        logger.info("测试数据缓存")
        cache_test_code = 'sh000001'
        cache_test_start = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')

        # 第一次获取数据（可能从API获取）
        start_time = time.time()
        data1 = data_handler.get_stock_data(
            stock_code=cache_test_code,
            start_date=cache_test_start,
            end_date=end_date,
            frequency='日线'
        )
        first_fetch_time = time.time() - start_time

        # 第二次获取数据（应该从缓存获取）
        start_time = time.time()
        data2 = data_handler.get_stock_data(
            stock_code=cache_test_code,
            start_date=cache_test_start,
            end_date=end_date,
            frequency='日线'
        )
        second_fetch_time = time.time() - start_time

        cache_working = second_fetch_time < first_fetch_time
        logger.info(f"缓存测试: 第一次获取时间 {first_fetch_time:.2f}秒, 第二次获取时间 {second_fetch_time:.2f}秒")
        logger.info(f"缓存是否工作: {cache_working}")

        # 保存测试结果
        os.makedirs('test_results', exist_ok=True)
        results_df = pd.DataFrame(results)
        results_df.to_csv('test_results/data_processing_test.csv', index=False)

        success_rate = results_df['success'].mean() * 100 if not results_df.empty else 0
        logger.info(f"数据处理模块测试完成，成功率: {success_rate:.2f}%")

        return {
            'success': True,
            'success_rate': success_rate,
            'cache_working': cache_working,
            'sample_data': data1 if 'data1' in locals() and data1 is not None and not data1.empty else None
        }

    except Exception as e:
        logger.error(f"测试数据处理模块时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            'success': False,
            'error': str(e),
            'sample_data': None
        }

def run_all_tests():
    """运行所有测试"""
    logger.info("开始运行全面测试")

    # 确保测试目录存在
    os.makedirs('test_results', exist_ok=True)
    os.makedirs('logs', exist_ok=True)

    # 测试环境设置
    environment_results = test_environment_setup()
    logger.info(f"环境设置测试结果: {environment_results}")

    # 测试数据处理模块
    data_processing_results = test_data_processing()
    logger.info(f"数据处理模块测试结果: {data_processing_results}")

    # 汇总测试结果
    results = {
        'environment': environment_results,
        'data_processing': data_processing_results
    }

    # 保存测试结果
    with open('test_results/comprehensive_test_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=4, default=str)

    logger.info("全面测试完成，结果已保存到 test_results/comprehensive_test_results.json")

    return results

if __name__ == "__main__":
    run_all_tests()
