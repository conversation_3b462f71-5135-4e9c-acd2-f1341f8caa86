"""
测试日期处理修复
"""

import os
import sys
import logging
from datetime import datetime, date

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('test_date_handling')

def test_date_conversion():
    """测试日期转换函数"""
    
    # 测试各种日期类型的转换
    test_cases = [
        # 字符串格式的日期
        "2023-05-11",
        "20230511",
        
        # datetime对象
        datetime.now(),
        
        # date对象
        date.today(),
    ]
    
    for test_case in test_cases:
        try:
            # 模拟get_latest_trading_date的处理逻辑
            if isinstance(test_case, str):
                try:
                    # 尝试解析日期字符串，然后重新格式化以确保格式一致
                    parsed_date = datetime.strptime(test_case, '%Y-%m-%d')
                    result = parsed_date.strftime('%Y-%m-%d')
                    logger.info(f"成功解析日期字符串 '{test_case}' 为 '{result}'")
                except ValueError:
                    # 如果格式不是 'YYYY-MM-DD'，尝试其他常见格式
                    try:
                        # 尝试解析为 'YYYYMMDD' 格式
                        parsed_date = datetime.strptime(test_case, '%Y%m%d')
                        result = parsed_date.strftime('%Y-%m-%d')
                        logger.info(f"成功解析日期字符串 '{test_case}' 为 '{result}'")
                    except ValueError:
                        # 如果仍然失败，记录错误
                        logger.error(f"无法解析日期字符串: {test_case}")
                        result = datetime.now().strftime('%Y-%m-%d')
            elif isinstance(test_case, datetime):
                # 如果是 datetime 对象，直接格式化
                result = test_case.strftime('%Y-%m-%d')
                logger.info(f"成功将 datetime 对象转换为字符串 '{result}'")
            elif isinstance(test_case, date):
                # 如果是 date 对象，转换为字符串
                result = test_case.strftime('%Y-%m-%d')
                logger.info(f"成功将 date 对象转换为字符串 '{result}'")
            elif hasattr(test_case, 'strftime'):
                # 如果是其他具有 strftime 方法的日期对象
                result = test_case.strftime('%Y-%m-%d')
                logger.info(f"成功将日期对象转换为字符串 '{result}'")
            else:
                # 如果是其他类型，尝试转换为字符串
                logger.warning(f"未知的日期类型: {type(test_case)}，尝试转换为字符串")
                result = str(test_case)
            
            # 测试在实况信号决策页面中的处理逻辑
            try:
                # 尝试解析日期字符串
                if isinstance(result, str):
                    end_date = datetime.strptime(result, "%Y-%m-%d").date()
                    logger.info(f"成功将字符串 '{result}' 转换为 date 对象 {end_date}")
                elif isinstance(result, datetime):
                    end_date = result.date()
                    logger.info(f"成功将 datetime 对象转换为 date 对象 {end_date}")
                elif isinstance(result, date):
                    end_date = result
                    logger.info(f"已经是 date 对象 {end_date}")
                else:
                    # 如果是其他类型，记录警告并使用当前日期
                    logger.warning(f"未知的日期类型: {type(result)}，使用当前日期")
                    end_date = datetime.now().date()
            except Exception as e:
                # 如果解析失败，记录错误并使用当前日期
                logger.error(f"解析日期失败: {str(e)}，使用当前日期")
                end_date = datetime.now().date()
            
            print(f"测试用例: {test_case} ({type(test_case)}) -> 结果: {result} -> end_date: {end_date}")
            
        except Exception as e:
            logger.error(f"处理测试用例 {test_case} 时出错: {str(e)}")

if __name__ == "__main__":
    print("开始测试日期处理...")
    test_date_conversion()
    print("测试完成!")
