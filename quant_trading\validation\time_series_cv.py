"""
时间序列交叉验证模块
实现适用于金融时间序列的交叉验证方法
严格遵循时间顺序，避免前视偏差
"""

import numpy as np
import pandas as pd
import logging
from enum import Enum
from typing import Tuple, List, Dict, Optional, Union, Iterator

class CVMethod(Enum):
    """交叉验证方法枚举"""
    ROLLING_WINDOW = 1  # 滚动窗口法
    EXPANDING_WINDOW = 2  # 扩展窗口法
    PURGED_K_FOLD = 3  # 带清洗的K折交叉验证

class TimeSeriesCV:
    """
    时间序列交叉验证类
    实现适用于金融时间序列的交叉验证方法
    """

    def __init__(self, 
                 method: CVMethod = CVMethod.ROLLING_WINDOW, 
                 n_splits: int = 5,
                 train_size: Optional[int] = None,
                 test_size: Optional[int] = None,
                 gap: int = 0,
                 embargo: int = 0,
                 logger: Optional[logging.Logger] = None):
        """
        初始化时间序列交叉验证器

        参数:
            method (CVMethod): 交叉验证方法
            n_splits (int): 分割数量
            train_size (int, optional): 训练集大小，如果为None则自动计算
            test_size (int, optional): 测试集大小，如果为None则自动计算
            gap (int): 训练集和测试集之间的间隔（用于避免数据泄露）
            embargo (int): 禁运期大小（用于避免标签泄露）
            logger (logging.Logger, optional): 日志记录器
        """
        self.method = method
        self.n_splits = n_splits
        self.train_size = train_size
        self.test_size = test_size
        self.gap = gap
        self.embargo = embargo
        self.logger = logger or logging.getLogger('drl_trading')

    def split(self, X: Union[pd.DataFrame, np.ndarray], y: Optional[Union[pd.Series, np.ndarray]] = None) -> Iterator[Tuple[np.ndarray, np.ndarray]]:
        """
        生成训练集和测试集的索引

        参数:
            X (pandas.DataFrame or numpy.ndarray): 特征数据
            y (pandas.Series or numpy.ndarray, optional): 目标数据

        返回:
            iterator: 生成(train_index, test_index)元组的迭代器
        """
        n_samples = len(X)
        indices = np.arange(n_samples)

        if self.method == CVMethod.ROLLING_WINDOW:
            yield from self._rolling_window_split(indices, n_samples)
        elif self.method == CVMethod.EXPANDING_WINDOW:
            yield from self._expanding_window_split(indices, n_samples)
        elif self.method == CVMethod.PURGED_K_FOLD:
            yield from self._purged_k_fold_split(indices, n_samples)
        else:
            raise ValueError(f"不支持的交叉验证方法: {self.method}")

    def _rolling_window_split(self, indices: np.ndarray, n_samples: int) -> Iterator[Tuple[np.ndarray, np.ndarray]]:
        """
        滚动窗口分割

        参数:
            indices (numpy.ndarray): 索引数组
            n_samples (int): 样本数量

        返回:
            iterator: 生成(train_index, test_index)元组的迭代器
        """
        # 计算测试集大小
        if self.test_size is None:
            test_size = n_samples // (self.n_splits + 1)
        else:
            test_size = self.test_size

        # 计算训练集大小
        if self.train_size is None:
            train_size = n_samples // (self.n_splits + 1) * self.n_splits
        else:
            train_size = self.train_size

        # 确保训练集和测试集大小合理
        if train_size + test_size + self.gap > n_samples:
            self.logger.warning(f"训练集大小({train_size})+测试集大小({test_size})+间隔({self.gap})超过样本总数({n_samples})，将自动调整")
            if self.train_size is None:
                train_size = int(n_samples * 0.7) - self.gap
            if self.test_size is None:
                test_size = int(n_samples * 0.3)

        # 计算步长
        step = (n_samples - train_size - test_size - self.gap) // (self.n_splits - 1) if self.n_splits > 1 else 1
        if step < 1:
            step = 1
            self.logger.warning(f"步长过小，已调整为1，这可能导致分割重叠")

        # 生成分割
        for i in range(self.n_splits):
            start_train = i * step
            end_train = start_train + train_size
            start_test = end_train + self.gap
            end_test = start_test + test_size

            # 确保不超出范围
            if end_test > n_samples:
                break

            train_indices = indices[start_train:end_train]
            test_indices = indices[start_test:end_test]

            yield train_indices, test_indices

    def _expanding_window_split(self, indices: np.ndarray, n_samples: int) -> Iterator[Tuple[np.ndarray, np.ndarray]]:
        """
        扩展窗口分割

        参数:
            indices (numpy.ndarray): 索引数组
            n_samples (int): 样本数量

        返回:
            iterator: 生成(train_index, test_index)元组的迭代器
        """
        # 计算初始训练集大小
        if self.train_size is None:
            initial_train_size = n_samples // (self.n_splits + 1)
        else:
            initial_train_size = self.train_size

        # 计算测试集大小
        if self.test_size is None:
            test_size = n_samples // (self.n_splits + 1)
        else:
            test_size = self.test_size

        # 确保初始训练集和测试集大小合理
        if initial_train_size + test_size + self.gap > n_samples:
            self.logger.warning(f"初始训练集大小({initial_train_size})+测试集大小({test_size})+间隔({self.gap})超过样本总数({n_samples})，将自动调整")
            if self.train_size is None:
                initial_train_size = int(n_samples * 0.4) - self.gap
            if self.test_size is None:
                test_size = int(n_samples * 0.2)

        # 计算步长
        remaining = n_samples - initial_train_size - test_size - self.gap
        step = remaining // self.n_splits if self.n_splits > 0 else 1
        if step < 1:
            step = 1
            self.logger.warning(f"步长过小，已调整为1，这可能导致分割重叠")

        # 生成分割
        for i in range(self.n_splits):
            train_end = initial_train_size + i * step
            test_start = train_end + self.gap
            test_end = test_start + test_size

            # 确保不超出范围
            if test_end > n_samples:
                break

            train_indices = indices[:train_end]
            test_indices = indices[test_start:test_end]

            yield train_indices, test_indices

    def _purged_k_fold_split(self, indices: np.ndarray, n_samples: int) -> Iterator[Tuple[np.ndarray, np.ndarray]]:
        """
        带清洗的K折交叉验证分割

        参数:
            indices (numpy.ndarray): 索引数组
            n_samples (int): 样本数量

        返回:
            iterator: 生成(train_index, test_index)元组的迭代器
        """
        # 计算每折的大小
        fold_size = n_samples // self.n_splits
        
        # 生成分割
        for i in range(self.n_splits):
            # 计算测试集的起始和结束索引
            test_start = i * fold_size
            test_end = test_start + fold_size if i < self.n_splits - 1 else n_samples
            
            # 计算训练集索引（排除测试集和间隔）
            train_indices = []
            
            # 添加测试集之前的索引
            if test_start > 0:
                purge_start = max(0, test_start - self.gap)
                train_indices.extend(indices[0:purge_start])
            
            # 添加测试集之后的索引
            if test_end < n_samples:
                embargo_end = min(n_samples, test_end + self.embargo)
                train_indices.extend(indices[embargo_end:])
            
            test_indices = indices[test_start:test_end]
            
            yield np.array(train_indices), test_indices

    def get_n_splits(self) -> int:
        """
        获取分割数量

        返回:
            int: 分割数量
        """
        return self.n_splits
