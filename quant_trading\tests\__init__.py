#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试模块
提供单元测试和集成测试
"""

# 导入测试模块
from quant_trading.tests.test_imports import TestImports
from quant_trading.tests.test_data import TestDataHandler
from quant_trading.tests.test_features import TestFeatureEngineering
from quant_trading.tests.test_trading import TestTradingEnvironment
from quant_trading.tests.test_agents import TestDRLAgent
from quant_trading.tests.test_agents_loading import TestModelLoading
from quant_trading.tests.test_agents_cleanup import TestModelCleanup
from quant_trading.tests.test_core import TestCoreModules
from quant_trading.tests.test_robust import TestRobustModules
from quant_trading.tests.test_ui import TestUI
from quant_trading.tests.test_ui_advanced import TestUIAdvanced
from quant_trading.tests.test_ui_components import TestUIComponents
from quant_trading.tests.test_ui_session import TestUISession

# 导出测试模块
__all__ = [
    'TestImports',
    'TestDataHandler',
    'TestFeatureEngineering',
    'TestTradingEnvironment',
    'TestDRLAgent',
    'TestModelLoading',
    'TestModelCleanup',
    'TestCoreModules',
    'TestRobustModules',
    'TestUI',
    'TestUIAdvanced',
    'TestUIComponents',
    'TestUISession'
]
