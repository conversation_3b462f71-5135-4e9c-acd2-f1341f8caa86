
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>量化交易系统测试报告</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                h1, h2, h3 { color: #333; }
                table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
                tr:nth-child(even) { background-color: #f9f9f9; }
                .success { color: green; }
                .failure { color: red; }
                .warning { color: orange; }
            </style>
        </head>
        <body>
            <h1>量化交易系统测试报告</h1>
            <p>生成时间: 2025-05-11 11:25:39</p>

            <h2>测试摘要</h2>
            <table>
                <tr>
                    <th>测试类别</th>
                    <th>通过数</th>
                    <th>失败数</th>
                    <th>通过率</th>
                </tr>
        
                <tr>
                    <td>所有测试</td>
                    <td>7</td>
                    <td>0</td>
                    <td>100.00%</td>
                </tr>
            </table>

            <h2>环境设置</h2>
            <table>
                <tr>
                    <th>项目</th>
                    <th>状态</th>
                    <th>详情</th>
                </tr>
                <tr>
                    <td>Python版本</td>
                    <td class="success">可用</td>
                    <td>3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]</td>
                </tr>
                <tr>
                    <td>项目目录结构</td>
                    <td class="success">
                        完整
                    </td>
                    <td>
                        所有必要目录都存在
                    </td>
                </tr>
                <tr>
                    <td>配置文件</td>
                    <td class="success">
                        完整
                    </td>
                    <td>
                        所有必要配置文件都存在
                    </td>
                </tr>
            </table>

            <h3>核心模块状态</h3>
            <table>
                <tr>
                    <th>模块</th>
                    <th>状态</th>
                </tr>
        
                <tr>
                    <td>core_logic.data_handler</td>
                    <td class="success">True</td>
                </tr>
            
                <tr>
                    <td>core_logic.feature_engineer</td>
                    <td class="success">True</td>
                </tr>
            
                <tr>
                    <td>core_logic.trading_environment</td>
                    <td class="success">True</td>
                </tr>
            
                <tr>
                    <td>core_logic.drl_agent</td>
                    <td class="success">True</td>
                </tr>
            
                <tr>
                    <td>core_logic.performance_analyzer</td>
                    <td class="success">True</td>
                </tr>
            
                <tr>
                    <td>core_logic.utils</td>
                    <td class="success">True</td>
                </tr>
            
            </table>

            <h2>模块测试结果</h2>
            <table>
                <tr>
                    <th>模块</th>
                    <th>函数</th>
                    <th>状态</th>
                    <th>结果</th>
                    <th>错误信息</th>
                </tr>
        
                <tr>
                    <td>tests.test_performance_analyzer</td>
                    <td>run_all_tests</td>
                    <td class="success">True</td>
                    <td>True</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>tests.test_trading_environment_constraints</td>
                    <td>run_all_tests</td>
                    <td class="success">True</td>
                    <td>True</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>tests.test_gpu_detection</td>
                    <td>run_all_tests</td>
                    <td class="success">True</td>
                    <td>False</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>tests.test_config_loading</td>
                    <td>test_config_loading</td>
                    <td class="success">True</td>
                    <td>True</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>tests.test_extended_data_extraction</td>
                    <td>run_all_tests</td>
                    <td class="success">True</td>
                    <td>True</td>
                    <td></td>
                </tr>
            
            </table>

            <h2>脚本测试结果</h2>
            <table>
                <tr>
                    <th>脚本</th>
                    <th>状态</th>
                    <th>错误信息</th>
                </tr>
        
                <tr>
                    <td>tests/test_data_extraction.py</td>
                    <td class="success">True</td>
                    <td></td>
                </tr>
            
                <tr>
                    <td>tests/test_drl_agent.py</td>
                    <td class="success">True</td>
                    <td></td>
                </tr>
            
            </table>

            <h2>测试结论</h2>
        
            <p>系统测试通过率高，整体质量良好。</p>

        </body>
        </html>
        