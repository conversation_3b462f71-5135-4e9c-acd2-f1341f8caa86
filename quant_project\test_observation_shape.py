"""
测试观测形状修复
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

from core_logic.trading_environment import TradingEnvironment

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('test_observation_shape')

def create_test_data(n_rows=100, n_features=5):
    """创建测试数据"""
    # 创建日期索引
    dates = pd.date_range(start='2023-01-01', periods=n_rows, freq='D')
    
    # 创建OHLCV数据
    data = {
        '开盘': np.random.rand(n_rows) * 100 + 100,
        '最高': np.random.rand(n_rows) * 10 + 110,
        '最低': np.random.rand(n_rows) * 10 + 90,
        '收盘': np.random.rand(n_rows) * 100 + 100,
        '成交量': np.random.rand(n_rows) * 1000000
    }
    
    # 创建特征数据
    for i in range(n_features):
        data[f'特征_{i+1}'] = np.random.rand(n_rows)
    
    # 创建DataFrame
    df = pd.DataFrame(data, index=dates)
    
    return df

def test_observation_shape():
    """测试观测形状修复"""
    
    # 测试不同特征数量和窗口大小的组合
    test_cases = [
        # (特征数量, 窗口大小, 观测空间维度)
        (5, 10, 5*10+4),  # 正常情况
        (10, 10, 10*10+4),  # 更多特征
        (5, 20, 5*20+4),  # 更大窗口
        (20, 5, 20*5+4),  # 更多特征，更小窗口
    ]
    
    for n_features, window_size, expected_dim in test_cases:
        try:
            logger.info(f"测试特征数量={n_features}, 窗口大小={window_size}, 预期维度={expected_dim}")
            
            # 创建测试数据
            df = create_test_data(n_rows=100, n_features=n_features)
            
            # 创建交易环境
            env = TradingEnvironment(
                df_processed_data=df,
                initial_capital=100000,
                commission_rate=0.0003,
                min_hold_days=3,
                window_size=window_size
            )
            
            # 重置环境
            observation, _ = env.reset()
            
            # 检查观测形状
            actual_dim = observation.shape[0]
            logger.info(f"观测形状: {observation.shape}, 维度: {actual_dim}")
            
            # 验证观测维度是否符合预期
            if actual_dim == expected_dim:
                logger.info(f"测试通过: 观测维度 {actual_dim} 符合预期 {expected_dim}")
            else:
                logger.warning(f"测试失败: 观测维度 {actual_dim} 不符合预期 {expected_dim}")
            
            # 测试执行一步
            action = 0  # 保持当前仓位
            observation, reward, terminated, truncated, info = env.step(action)
            
            # 再次检查观测形状
            actual_dim = observation.shape[0]
            logger.info(f"执行一步后的观测形状: {observation.shape}, 维度: {actual_dim}")
            
            # 验证观测维度是否符合预期
            if actual_dim == expected_dim:
                logger.info(f"测试通过: 执行一步后的观测维度 {actual_dim} 符合预期 {expected_dim}")
            else:
                logger.warning(f"测试失败: 执行一步后的观测维度 {actual_dim} 不符合预期 {expected_dim}")
            
            print(f"测试用例: 特征数量={n_features}, 窗口大小={window_size}, 预期维度={expected_dim} -> 实际维度: {actual_dim}")
            
        except Exception as e:
            logger.error(f"测试特征数量={n_features}, 窗口大小={window_size} 时出错: {str(e)}")
    
    # 测试特征数量变化的情况
    try:
        logger.info("测试特征数量变化的情况")
        
        # 创建初始测试数据
        df1 = create_test_data(n_rows=100, n_features=5)
        
        # 创建交易环境
        env = TradingEnvironment(
            df_processed_data=df1,
            initial_capital=100000,
            commission_rate=0.0003,
            min_hold_days=3,
            window_size=10
        )
        
        # 重置环境
        observation1, _ = env.reset()
        
        # 记录初始观测维度
        initial_dim = observation1.shape[0]
        logger.info(f"初始观测形状: {observation1.shape}, 维度: {initial_dim}")
        
        # 创建特征数量不同的新测试数据
        df2 = create_test_data(n_rows=100, n_features=10)
        
        # 更新环境的数据
        env.df = df2
        
        # 重置环境
        observation2, _ = env.reset()
        
        # 检查新的观测形状
        new_dim = observation2.shape[0]
        logger.info(f"特征数量变化后的观测形状: {observation2.shape}, 维度: {new_dim}")
        
        # 验证观测维度是否保持不变
        if new_dim == initial_dim:
            logger.info(f"测试通过: 特征数量变化后的观测维度 {new_dim} 与初始维度 {initial_dim} 一致")
        else:
            logger.warning(f"测试失败: 特征数量变化后的观测维度 {new_dim} 与初始维度 {initial_dim} 不一致")
        
        print(f"特征数量变化测试: 初始维度={initial_dim} -> 变化后维度: {new_dim}")
        
    except Exception as e:
        logger.error(f"测试特征数量变化时出错: {str(e)}")

if __name__ == "__main__":
    print("开始测试观测形状修复...")
    test_observation_shape()
    print("测试完成!")
