"""
测试超参数优化功能
"""

import os
import sys
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_hpo')

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 导入必要的模块
try:
    from core_logic.enhanced_drl_agent import EnhancedDRLAgent
    from core_logic.data_handler import DataHandler
    from core_logic.feature_engineer import FeatureEngineer
    logger.info("成功导入模块")
except ImportError as e:
    logger.error(f"导入模块失败: {str(e)}")
    sys.exit(1)

def create_test_data():
    """创建测试数据"""
    # 创建日期范围
    start_date = datetime(2020, 1, 1)
    end_date = datetime(2022, 1, 1)
    date_range = pd.date_range(start=start_date, end=end_date, freq='D')
    
    # 创建价格数据
    np.random.seed(42)
    n = len(date_range)
    close = np.random.normal(loc=100, scale=10, size=n).cumsum() + 1000
    open_price = close * np.random.normal(loc=1, scale=0.01, size=n)
    high = np.maximum(close, open_price) * np.random.normal(loc=1.02, scale=0.01, size=n)
    low = np.minimum(close, open_price) * np.random.normal(loc=0.98, scale=0.01, size=n)
    volume = np.random.normal(loc=1000000, scale=200000, size=n)
    
    # 创建DataFrame
    df = pd.DataFrame({
        'date': date_range,
        'open': open_price,
        'high': high,
        'low': low,
        'close': close,
        'volume': volume
    })
    
    return df

def test_hyperparameter_optimization():
    """测试超参数优化功能"""
    logger.info("开始测试超参数优化功能")
    
    # 创建测试数据
    df = create_test_data()
    logger.info(f"创建测试数据完成，形状: {df.shape}")
    
    # 特征工程
    feature_engineer = FeatureEngineer()
    df_features = feature_engineer.generate_features(df)
    logger.info(f"特征工程完成，形状: {df_features.shape}")
    
    # 环境配置
    env_config = {
        'df_processed_data': df_features,
        'initial_capital': 100000,
        'commission_rate': 0.0003,
        'min_hold_days': 3,
        'allow_short': False,
        'max_position': 1.0,
        'window_size': 20
    }
    
    # 智能体配置
    agent_config = {
        'algorithm': 'PPO',
        'policy_network': 'MlpPolicy',
        'learning_rate': 0.0003,
        'gamma': 0.99,
        'n_steps': 128,
        'batch_size': 64,
        'n_epochs': 4,
        'use_gpu': True
    }
    
    # 超参数优化配置
    hpo_config = {
        'use': True,
        'n_trials': 2,  # 为了快速测试，只使用2次试验
        'n_startup_trials': 1,
        'n_evaluations': 1,
        'n_timesteps': 1000,  # 为了快速测试，只使用1000步
        'optimization_method': 'tpe',
        'target_metric': 'reward',
        'param_space': {
            'learning_rate': {'min': 1e-5, 'max': 1e-3},
            'gamma': {'min': 0.9, 'max': 0.9999}
        },
        'pruning': {
            'use': True,
            'method': 'median',
            'patience': 5
        }
    }
    
    # 创建DRL智能体
    drl_agent = EnhancedDRLAgent(env_config, agent_config, hpo_config)
    logger.info("创建DRL智能体完成")
    
    # 执行超参数优化
    try:
        logger.info("开始执行超参数优化")
        best_params = drl_agent.optimize_hyperparameters(
            n_trials=hpo_config['n_trials'],
            n_startup_trials=hpo_config['n_startup_trials'],
            n_evaluations=hpo_config['n_evaluations'],
            n_timesteps=hpo_config['n_timesteps']
        )
        logger.info(f"超参数优化完成，最佳参数: {best_params}")
        return True
    except Exception as e:
        logger.error(f"超参数优化过程中发生错误: {str(e)}", exc_info=True)
        return False

if __name__ == "__main__":
    success = test_hyperparameter_optimization()
    if success:
        logger.info("测试成功")
        sys.exit(0)
    else:
        logger.error("测试失败")
        sys.exit(1)
