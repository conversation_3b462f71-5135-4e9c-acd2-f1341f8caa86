#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
模型保存与加载测试脚本
测试模型保存与加载功能
"""

import os
import sys
import pandas as pd
import numpy as np
import logging
import traceback
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import random
import torch

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'logs/model_save_load_test_{datetime.now().strftime("%Y-%m-%d")}.log')
    ]
)

# 确保logs目录存在
os.makedirs('logs', exist_ok=True)

# 添加项目路径到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入相关模块
from quant_trading.data.data_handler import DataHandler
from quant_trading.features.feature_engineer import FeatureEngineer
from quant_trading.trading.trading_environment import TradingEnvironment
from quant_trading.agents.drl_agent import DRLAgent
from quant_trading.utils.common import load_config

def test_model_save_load():
    """测试模型保存与加载"""
    print("\n===== 测试模型保存与加载 =====")
    
    # 获取测试数据
    handler = DataHandler(cache_dir='data_cache')
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=120)).strftime('%Y-%m-%d')
    stock = 'sh000001'
    
    try:
        # 获取原始数据
        data = handler.get_stock_data(stock, start_date, end_date)
        
        if data is None or data.empty:
            print("获取测试数据失败，无法进行模型保存与加载测试")
            return
        
        # 加载配置
        env_config = load_config("quant_project/configs/env_config.yaml")
        agent_config = load_config("quant_project/configs/drl_agent_config.yaml")
        
        # 生成特征
        feature_engineer = FeatureEngineer(env_config.get('feature_config', {}))
        processed_data = feature_engineer.generate_features(data)
        
        # 创建环境配置
        env_params = {
            'df_processed_data': processed_data,
            'initial_capital': env_config.get('initial_capital', 100000),
            'commission_rate': env_config.get('commission_rate', 0.0003),
            'min_hold_days': env_config.get('min_hold_days', 3),
            'allow_short': env_config.get('allow_short', False),
            'max_position': env_config.get('max_position', 1.0),
            'window_size': min(20, len(processed_data) - 1),  # 确保窗口大小不超过数据长度
            'reward_config': env_config.get('reward_config', {})
        }
        
        # 创建DRL智能体
        agent_config['algorithm'] = 'PPO'  # 使用PPO算法
        drl_agent = DRLAgent(env_params, agent_config)
        
        # 训练模型（使用较小的步数）
        print("训练简单模型...")
        drl_agent.train(total_timesteps=2000, progress_bar=True)
        
        # 保存模型
        print("保存模型...")
        model_path = drl_agent.save_model(stock_code=stock)
        print(f"模型已保存到: {model_path}")
        
        # 加载模型
        print("加载模型...")
        loaded_agent = DRLAgent.load_model(model_path)
        
        if loaded_agent:
            print("模型加载成功")
            
            # 测试加载的模型进行预测
            print("使用加载的模型进行预测...")
            
            # 创建测试环境
            test_env_params = env_params.copy()
            test_env = TradingEnvironment(**test_env_params)
            
            # 重置环境
            observation, info = test_env.reset()
            
            # 使用原始模型预测
            original_action = drl_agent.predict_action(observation)
            
            # 使用加载的模型预测
            loaded_action = loaded_agent.predict_action(observation)
            
            print(f"原始模型预测动作: {original_action}")
            print(f"加载的模型预测动作: {loaded_action}")
            
            if original_action == loaded_action:
                print("验证通过: 加载的模型与原始模型预测结果一致")
            else:
                print("警告: 加载的模型与原始模型预测结果不一致")
                
            # 测试模型在多个观测上的预测
            print("\n测试模型在多个观测上的预测...")
            
            # 执行几个步骤
            match_count = 0
            total_steps = 10
            
            for i in range(total_steps):
                # 重置环境
                observation, info = test_env.reset()
                
                # 使用原始模型预测
                original_action = drl_agent.predict_action(observation)
                
                # 使用加载的模型预测
                loaded_action = loaded_agent.predict_action(observation)
                
                if original_action == loaded_action:
                    match_count += 1
                
                # 执行动作
                observation, reward, terminated, truncated, info = test_env.step(original_action)
            
            match_rate = match_count / total_steps * 100
            print(f"在 {total_steps} 个观测上的预测匹配率: {match_rate:.2f}%")
            
            if match_rate >= 90:
                print("验证通过: 加载的模型与原始模型预测结果高度一致")
            else:
                print("警告: 加载的模型与原始模型预测结果一致性较低")
                
            # 测试模型在完整回合上的表现
            print("\n测试模型在完整回合上的表现...")
            
            # 使用原始模型执行回合
            observation, info = test_env.reset()
            done = False
            original_total_reward = 0
            
            while not done:
                action = drl_agent.predict_action(observation)
                observation, reward, terminated, truncated, info = test_env.step(action)
                original_total_reward += reward
                done = terminated or truncated
            
            original_portfolio_value = info['portfolio_value']
            
            # 使用加载的模型执行回合
            observation, info = test_env.reset()
            done = False
            loaded_total_reward = 0
            
            while not done:
                action = loaded_agent.predict_action(observation)
                observation, reward, terminated, truncated, info = test_env.step(action)
                loaded_total_reward += reward
                done = terminated or truncated
            
            loaded_portfolio_value = info['portfolio_value']
            
            print(f"原始模型总奖励: {original_total_reward:.4f}")
            print(f"加载的模型总奖励: {loaded_total_reward:.4f}")
            print(f"原始模型最终组合价值: {original_portfolio_value:.2f}")
            print(f"加载的模型最终组合价值: {loaded_portfolio_value:.2f}")
            
            reward_diff = abs(original_total_reward - loaded_total_reward)
            portfolio_diff = abs(original_portfolio_value - loaded_portfolio_value) / original_portfolio_value * 100
            
            if reward_diff < 0.1 and portfolio_diff < 1:
                print("验证通过: 加载的模型与原始模型表现一致")
            else:
                print(f"警告: 加载的模型与原始模型表现存在差异 (奖励差异: {reward_diff:.4f}, 组合价值差异: {portfolio_diff:.2f}%)")
        else:
            print("模型加载失败")
        
    except Exception as e:
        print(f"测试模型保存与加载时出错: {str(e)}")
        print(traceback.format_exc())

if __name__ == '__main__':
    test_model_save_load()
