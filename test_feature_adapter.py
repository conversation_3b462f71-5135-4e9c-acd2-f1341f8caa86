#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试特征工程适配器脚本
"""

import os
import sys
import logging
from datetime import datetime
import traceback

# 设置日志
os.makedirs('logs', exist_ok=True)
log_file = os.path.join('logs', f'test_feature_adapter_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('test_feature_adapter')

# 添加项目根目录到系统路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_root)

# 导入特征工程适配器
from quant_project.core_logic.feature_engineering_adapter import FeatureEngineeringAdapter

def test_feature_engineering_adapter():
    """测试特征工程适配器"""
    logger.info("测试特征工程适配器")
    
    try:
        # 创建适配器实例
        adapter = FeatureEngineeringAdapter()
        
        # 测试扁平格式配置
        flat_config = {
            'use_price': True,
            'use_volume': True,
            'use_technical': True,
            'sma_periods': [5, 10, 20, 30, 60],
            'ema_periods': [5, 10, 20, 30, 60],
            'rsi_periods': [14],
            'macd_params': {'fast': 12, 'slow': 26, 'signal': 9},
            'bb_params': {'window': 20, 'num_std': 2},
            'atr_periods': [14],
            'normalization': 'zscore'
        }
        
        # 转换为标准格式
        standard_config = adapter.adapt_config(flat_config)
        
        logger.info(f"扁平格式配置: {flat_config}")
        logger.info(f"转换后的标准格式配置: {standard_config}")
        
        # 测试标准格式配置
        standard_config_input = {
            'price_features': {'use': True},
            'volume': {'use': True, 'periods': [5, 10, 20]},
            'sma': {'use': True, 'periods': [5, 10, 20, 60]},
            'ema': {'use': True, 'periods': [5, 10, 20]},
            'rsi': {'use': True, 'periods': [6, 14, 21]},
            'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9},
            'bbands': {'use': True, 'period': 20, 'std': 2.0},
            'normalization': {'use': True, 'method': 'minmax'}
        }
        
        # 确认标准格式配置不变
        result_config = adapter.adapt_config(standard_config_input)
        
        logger.info(f"标准格式配置: {standard_config_input}")
        logger.info(f"适配后的配置: {result_config}")
        
        # 验证结果
        is_flat_converted = all(key in standard_config for key in ['sma', 'ema', 'rsi', 'macd', 'bbands'])
        is_standard_unchanged = result_config == standard_config_input
        
        return {
            'success': is_flat_converted and is_standard_unchanged,
            'flat_converted': is_flat_converted,
            'standard_unchanged': is_standard_unchanged
        }
    except Exception as e:
        logger.error(f"测试特征工程适配器时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            'success': False,
            'error': str(e)
        }

def main():
    """主函数"""
    logger.info("=" * 50)
    logger.info("开始测试特征工程适配器")
    logger.info("=" * 50)
    
    # 测试特征工程适配器
    feature_result = test_feature_engineering_adapter()
    
    # 输出测试结果
    logger.info("=" * 50)
    logger.info("测试结果:")
    logger.info(f"特征工程适配器: {'成功' if feature_result['success'] else '失败'}")
    if not feature_result['success']:
        logger.info(f"   错误: {feature_result.get('error', '未知错误')}")
    
    logger.info("=" * 50)
    
    return feature_result['success']

if __name__ == "__main__":
    main()
