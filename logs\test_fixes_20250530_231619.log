2025-05-30 23:16:19,394 - test_fixes - INFO - ==================================================
2025-05-30 23:16:19,394 - test_fixes - INFO - 开始测试修复后的功能
2025-05-30 23:16:19,395 - test_fixes - INFO - ==================================================
2025-05-30 23:16:19,395 - test_fixes - INFO - 测试GPU检测功能
2025-05-30 23:16:19,396 - gpu_installer - INFO - 检测系统GPU...
2025-05-30 23:16:19,483 - gpu_installer - INFO - 检测到NVIDIA GPU
2025-05-30 23:16:19,484 - gpu_installer - WARNING - 检测GPU时出错: argument of type 'NoneType' is not iterable
2025-05-30 23:16:19,493 - gpu_installer - WARNING - 检测CUDA时出错: [WinError 2] 系统找不到指定的文件。
2025-05-30 23:16:21,336 - gpu_installer - WARNING - TensorFlow GPU检测失败
2025-05-30 23:16:21,336 - test_fixes - INFO - GPU检测结果: {'has_gpu': True, 'gpu_count': 1, 'gpu_names': [], 'cuda_version': None, 'pytorch_gpu': True, 'tensorflow_gpu': False}
2025-05-30 23:16:21,336 - test_fixes - INFO - 测试特征工程适配器
2025-05-30 23:16:24,848 - drl_trading - INFO - 检测到扁平格式配置，转换为标准格式
2025-05-30 23:16:24,849 - test_fixes - INFO - 扁平格式配置: {'use_price': True, 'use_volume': True, 'use_technical': True, 'sma_periods': [5, 10, 20, 30, 60], 'ema_periods': [5, 10, 20, 30, 60], 'rsi_periods': [14], 'macd_params': {'fast': 12, 'slow': 26, 'signal': 9}, 'bb_params': {'window': 20, 'num_std': 2}, 'atr_periods': [14], 'normalization': 'zscore'}
2025-05-30 23:16:24,849 - test_fixes - INFO - 转换后的标准格式配置: {'price_features': {'use': True}, 'volume': {'use': True}, 'sma': {'use': True, 'periods': [5, 10, 20, 30, 60]}, 'ema': {'use': True, 'periods': [5, 10, 20, 30, 60]}, 'rsi': {'use': True, 'periods': [14]}, 'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9}, 'bbands': {'use': True, 'period': 20, 'std': 2}, 'atr': {'use': True, 'periods': [14]}, 'normalization': {'use': True, 'method': 'zscore'}}
2025-05-30 23:16:24,849 - drl_trading - INFO - 检测到标准格式配置，进行验证和填充默认值
2025-05-30 23:16:24,849 - test_fixes - INFO - 标准格式配置: {'price_features': {'use': True}, 'volume': {'use': True, 'periods': [5, 10, 20]}, 'sma': {'use': True, 'periods': [5, 10, 20, 60]}, 'ema': {'use': True, 'periods': [5, 10, 20]}, 'rsi': {'use': True, 'periods': [6, 14, 21]}, 'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9}, 'bbands': {'use': True, 'period': 20, 'std': 2.0}, 'normalization': {'use': True, 'method': 'minmax'}}
2025-05-30 23:16:24,849 - test_fixes - INFO - 适配后的配置: {'price_features': {'use': True}, 'volume': {'use': True, 'periods': [5, 10, 20]}, 'sma': {'use': True, 'periods': [5, 10, 20, 60]}, 'ema': {'use': True, 'periods': [5, 10, 20]}, 'rsi': {'use': True, 'periods': [6, 14, 21]}, 'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9}, 'bbands': {'use': True, 'period': 20, 'std': 2.0}, 'atr': {'use': True, 'periods': [14]}, 'normalization': {'use': True, 'method': 'minmax'}}
2025-05-30 23:16:24,849 - test_fixes - INFO - 测试性能分析器参数处理
2025-05-30 23:16:24,851 - test_fixes - INFO - 测试直接传入DataFrame
2025-05-30 23:16:24,854 - drl_trading - INFO - 交易记录中没有profit列，尝试从交易记录计算
2025-05-30 23:16:24,855 - test_fixes - ERROR - 测试性能分析器参数处理时出错: 'shares'
2025-05-30 23:16:24,858 - test_fixes - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pandas\core\indexes\base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'shares'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\cursor\量化\test_fixes.py", line 165, in test_performance_analyzer
    metrics_df = analyzer.analyze(trades_df, portfolio_values)
  File "C:\cursor\量化\quant_project\core_logic\performance_analyzer.py", line 87, in analyze
    trade_stats = self.calculate_trade_statistics(trades_df)
  File "C:\cursor\量化\quant_project\core_logic\performance_analyzer.py", line 370, in calculate_trade_statistics
    shares = buy_trade['shares']  # 假设买入和卖出的股数相同
             ~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pandas\core\series.py", line 1121, in __getitem__
    return self._get_value(key)
           ~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pandas\core\series.py", line 1237, in _get_value
    loc = self.index.get_loc(label)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'shares'

2025-05-30 23:16:24,858 - test_fixes - INFO - === 测试数据获取功能 ===
2025-05-30 23:16:24,883 - test_fixes - INFO - 测试数据获取: {'stock_code': 'sh000001', 'start_date': '2020-01-01', 'end_date': '2020-12-31', 'frequency': '日线', 'data_source': '指数'}
2025-05-30 23:16:24,883 - drl_trading - INFO - 缓存文件不存在: data_cache\c15b165ee622bcd2366ea8205db43b32.csv
2025-05-30 23:16:24,883 - drl_trading - INFO - 从数据源获取数据: sh000001, 2020-01-01 至 2020-12-31, 频率: D
2025-05-30 23:16:24,883 - drl_trading - ERROR - 获取数据失败: 不支持的数据频率: D，目前仅支持 '日线', '周线', '月线'
2025-05-30 23:16:24,884 - drl_trading - WARNING - 使用内部频率 D 获取数据失败，尝试使用原始频率: 日线
2025-05-30 23:16:24,884 - drl_trading - INFO - 缓存文件不存在: data_cache\a8c3892a8f5a2d9bcf50c6153734f9f1.csv
2025-05-30 23:16:24,884 - drl_trading - INFO - 从数据源获取数据: sh000001, 2020-01-01 至 2020-12-31, 频率: 日线
2025-05-30 23:16:24,884 - drl_trading - INFO - 获取股票数据: sh000001, 2020-01-01 至 2020-12-31, 频率: 日线
2025-05-30 23:16:24,884 - drl_trading - INFO - 获取上海证券交易所股票: 000001
2025-05-30 23:16:24,884 - drl_trading - INFO - 尝试使用stock_zh_index_daily_em获取数据: sh000001
2025-05-30 23:16:25,645 - drl_trading - INFO - 使用stock_zh_index_daily_em获取到的数据列名: ['date', 'open', 'close', 'high', 'low', 'volume', 'amount']
2025-05-30 23:16:25,646 - drl_trading - INFO - 标准化了以下列名: {'date': '日期', 'open': '开盘', 'close': '收盘', 'high': '最高', 'low': '最低', 'volume': '成交量', 'amount': '成交额'}
2025-05-30 23:16:25,646 - drl_trading - INFO - 找到日期列: 日期
2025-05-30 23:16:25,649 - drl_trading - INFO - 将 日期 设置为索引
2025-05-30 23:16:25,651 - drl_trading - INFO - 列 开盘 中检测到 34 个异常值
2025-05-30 23:16:25,652 - drl_trading - INFO - 列 开盘 中修改了 170 个异常值
2025-05-30 23:16:25,653 - drl_trading - INFO - 列 收盘 中检测到 33 个异常值
2025-05-30 23:16:25,654 - drl_trading - INFO - 列 收盘 中修改了 170 个异常值
2025-05-30 23:16:25,655 - drl_trading - INFO - 列 最高 中检测到 34 个异常值
2025-05-30 23:16:25,656 - drl_trading - INFO - 列 最高 中修改了 170 个异常值
2025-05-30 23:16:25,656 - drl_trading - INFO - 列 最低 中检测到 31 个异常值
2025-05-30 23:16:25,658 - drl_trading - INFO - 列 最低 中修改了 170 个异常值
2025-05-30 23:16:25,659 - drl_trading - INFO - 列 成交量 中检测到 138 个异常值
2025-05-30 23:16:25,660 - drl_trading - INFO - 列 成交量 中修改了 170 个异常值
2025-05-30 23:16:25,661 - drl_trading - INFO - 列 成交额 中检测到 155 个异常值
2025-05-30 23:16:25,662 - drl_trading - INFO - 列 成交额 中修改了 170 个异常值
2025-05-30 23:16:25,663 - drl_trading - INFO - 数据清洗完成，处理后数据形状: (8409, 6)
2025-05-30 23:16:25,666 - drl_trading - WARNING - 数据中存在 81 个异常大的时间间隔，最大间隔为 20 days 00:00:00
2025-05-30 23:16:25,698 - drl_trading - INFO - 数据已缓存: data_cache\a8c3892a8f5a2d9bcf50c6153734f9f1.csv
2025-05-30 23:16:25,698 - drl_trading - INFO - 数据处理完成，共 8409 条记录
2025-05-30 23:16:25,698 - test_fixes - INFO - 成功获取数据: 8409 条记录
2025-05-30 23:16:25,699 - test_fixes - INFO - 数据范围: 1990-12-19 00:00:00 至 2025-05-30 00:00:00
2025-05-30 23:16:25,699 - test_fixes - INFO - 数据列: ['开盘', '收盘', '最高', '最低', '成交量', '成交额']
2025-05-30 23:16:25,699 - test_fixes - INFO - 测试数据获取: {'stock_code': 'sh600000', 'start_date': '2021-01-01', 'end_date': '2021-06-30', 'frequency': '周线', 'data_source': '股票'}
2025-05-30 23:16:25,699 - drl_trading - INFO - 缓存文件不存在: data_cache\286bf51681d0b8e6c2c23716c10f3f3f.csv
2025-05-30 23:16:25,700 - drl_trading - INFO - 从数据源获取数据: sh600000, 2021-01-01 至 2021-06-30, 频率: W
2025-05-30 23:16:25,700 - drl_trading - ERROR - 获取数据失败: 不支持的数据频率: W，目前仅支持 '日线', '周线', '月线'
2025-05-30 23:16:25,700 - drl_trading - WARNING - 使用内部频率 W 获取数据失败，尝试使用原始频率: 周线
2025-05-30 23:16:25,700 - drl_trading - INFO - 缓存文件不存在: data_cache\a29b0c2df38fe6158765b1683d3b6bbd.csv
2025-05-30 23:16:25,700 - drl_trading - INFO - 从数据源获取数据: sh600000, 2021-01-01 至 2021-06-30, 频率: 周线
2025-05-30 23:16:25,700 - drl_trading - INFO - 从数据源获取数据: sh600000, 2021-01-01 至 2021-06-30, 频率: 日线
2025-05-30 23:16:25,701 - drl_trading - INFO - 获取股票数据: sh600000, 2021-01-01 至 2021-06-30, 频率: 日线
2025-05-30 23:16:25,701 - drl_trading - INFO - 获取上海证券交易所股票: 600000
2025-05-30 23:16:25,701 - drl_trading - INFO - 尝试使用stock_zh_index_daily_em获取数据: sh600000
2025-05-30 23:16:26,209 - drl_trading - INFO - 使用stock_zh_index_daily_em获取到的数据列名: ['date', 'open', 'close', 'high', 'low', 'volume', 'amount']
2025-05-30 23:16:26,209 - drl_trading - INFO - 标准化了以下列名: {'date': '日期', 'open': '开盘', 'close': '收盘', 'high': '最高', 'low': '最低', 'volume': '成交量', 'amount': '成交额'}
2025-05-30 23:16:26,210 - drl_trading - INFO - 找到日期列: 日期
2025-05-30 23:16:26,212 - drl_trading - INFO - 将 日期 设置为索引
2025-05-30 23:16:26,213 - drl_trading - INFO - 列 开盘 中检测到 155 个异常值
2025-05-30 23:16:26,214 - drl_trading - INFO - 列 开盘 中修改了 120 个异常值
2025-05-30 23:16:26,215 - drl_trading - INFO - 列 收盘 中检测到 155 个异常值
2025-05-30 23:16:26,217 - drl_trading - INFO - 列 收盘 中修改了 120 个异常值
2025-05-30 23:16:26,218 - drl_trading - INFO - 列 最高 中检测到 155 个异常值
2025-05-30 23:16:26,219 - drl_trading - INFO - 列 最高 中修改了 121 个异常值
2025-05-30 23:16:26,220 - drl_trading - INFO - 列 最低 中检测到 153 个异常值
2025-05-30 23:16:26,221 - drl_trading - INFO - 列 最低 中修改了 121 个异常值
2025-05-30 23:16:26,222 - drl_trading - INFO - 列 成交量 中检测到 133 个异常值
2025-05-30 23:16:26,223 - drl_trading - INFO - 列 成交量 中修改了 122 个异常值
2025-05-30 23:16:26,224 - drl_trading - INFO - 列 成交额 中检测到 139 个异常值
2025-05-30 23:16:26,225 - drl_trading - INFO - 列 成交额 中修改了 122 个异常值
2025-05-30 23:16:26,226 - drl_trading - INFO - 数据清洗完成，处理后数据形状: (6073, 6)
2025-05-30 23:16:26,228 - drl_trading - WARNING - 数据中存在 79 个异常大的时间间隔，最大间隔为 53 days 00:00:00
2025-05-30 23:16:26,228 - drl_trading - INFO - 数据处理完成，共 6073 条记录
2025-05-30 23:16:26,264 - drl_trading - INFO - 数据重采样完成，从 6073 条记录重采样为 1283 条记录
2025-05-30 23:16:26,265 - drl_trading - INFO - 数据已经使用日期索引
2025-05-30 23:16:26,265 - drl_trading - INFO - 列 开盘 中检测到 33 个异常值
2025-05-30 23:16:26,267 - drl_trading - INFO - 列 开盘 中修改了 13 个异常值
2025-05-30 23:16:26,268 - drl_trading - INFO - 列 最高 中检测到 33 个异常值
2025-05-30 23:16:26,269 - drl_trading - INFO - 列 最高 中修改了 12 个异常值
2025-05-30 23:16:26,270 - drl_trading - INFO - 列 最低 中检测到 32 个异常值
2025-05-30 23:16:26,271 - drl_trading - INFO - 列 最低 中修改了 13 个异常值
2025-05-30 23:16:26,272 - drl_trading - INFO - 列 收盘 中检测到 33 个异常值
2025-05-30 23:16:26,273 - drl_trading - INFO - 列 收盘 中修改了 13 个异常值
2025-05-30 23:16:26,273 - drl_trading - INFO - 列 成交量 中检测到 33 个异常值
2025-05-30 23:16:26,275 - drl_trading - INFO - 列 成交量 中修改了 26 个异常值
2025-05-30 23:16:26,275 - drl_trading - INFO - 列 成交额 中检测到 32 个异常值
2025-05-30 23:16:26,276 - drl_trading - INFO - 列 成交额 中修改了 26 个异常值
2025-05-30 23:16:26,277 - drl_trading - INFO - 数据清洗完成，处理后数据形状: (1283, 6)
2025-05-30 23:16:26,278 - drl_trading - WARNING - 数据中存在 1282 个异常大的时间间隔，最大间隔为 49 days 00:00:00
2025-05-30 23:16:26,285 - drl_trading - INFO - 数据已缓存: data_cache\a29b0c2df38fe6158765b1683d3b6bbd.csv
2025-05-30 23:16:26,285 - drl_trading - INFO - 数据处理完成，共 1283 条记录
2025-05-30 23:16:26,285 - test_fixes - INFO - 成功获取数据: 1283 条记录
2025-05-30 23:16:26,285 - test_fixes - INFO - 数据范围: 1999-11-14 00:00:00 至 2025-06-01 00:00:00
2025-05-30 23:16:26,286 - test_fixes - INFO - 数据列: ['开盘', '最高', '最低', '收盘', '成交量', '成交额']
2025-05-30 23:16:26,286 - test_fixes - INFO - 数据获取功能测试通过
2025-05-30 23:16:26,286 - test_fixes - INFO - === 测试因子挖掘功能 ===
2025-05-30 23:16:26,296 - drl_trading - INFO - 缓存文件不存在: data_cache\c15b165ee622bcd2366ea8205db43b32.csv
2025-05-30 23:16:26,296 - drl_trading - INFO - 从数据源获取数据: sh000001, 2020-01-01 至 2020-12-31, 频率: D
2025-05-30 23:16:26,297 - drl_trading - ERROR - 获取数据失败: 不支持的数据频率: D，目前仅支持 '日线', '周线', '月线'
2025-05-30 23:16:26,297 - drl_trading - WARNING - 使用内部频率 D 获取数据失败，尝试使用原始频率: 日线
2025-05-30 23:16:26,297 - drl_trading - INFO - 从缓存加载数据: data_cache\a8c3892a8f5a2d9bcf50c6153734f9f1.csv
2025-05-30 23:16:26,314 - drl_trading - INFO - 从缓存加载数据成功，共 8409 条记录
2025-05-30 23:16:26,314 - drl_trading - INFO - 开始运行自动因子挖掘流水线...
2025-05-30 23:16:26,314 - test_fixes - INFO - 因子挖掘进度: 初始化 - 0% - 开始因子挖掘流程
2025-05-30 23:16:26,315 - drl_trading - INFO - 初始化 - 0% - 开始因子挖掘流程
2025-05-30 23:16:26,315 - test_fixes - INFO - 因子挖掘进度: 因子生成 - 10% - 开始生成因子
2025-05-30 23:16:26,315 - drl_trading - INFO - 因子生成 - 10% - 开始生成因子
2025-05-30 23:16:26,315 - test_fixes - INFO - 因子挖掘进度: 因子生成 - 15% - 生成技术指标因子
2025-05-30 23:16:26,315 - drl_trading - INFO - 因子生成 - 15% - 生成技术指标因子
2025-05-30 23:16:26,315 - drl_trading - INFO - 生成技术指标因子...
2025-05-30 23:16:26,316 - drl_trading - INFO - 输入数据形状: (8409, 6), 列名: ['开盘', '收盘', '最高', '最低', '成交量', '成交额']
2025-05-30 23:16:26,316 - drl_trading - INFO - 找到列 '开盘' 映射到标准名称 'open'
2025-05-30 23:16:26,316 - drl_trading - INFO - 找到列 '最高' 映射到标准名称 'high'
2025-05-30 23:16:26,316 - drl_trading - INFO - 找到列 '最低' 映射到标准名称 'low'
2025-05-30 23:16:26,316 - drl_trading - INFO - 找到列 '收盘' 映射到标准名称 'close'
2025-05-30 23:16:26,317 - drl_trading - INFO - 找到列 '成交量' 映射到标准名称 'volume'
2025-05-30 23:16:26,317 - drl_trading - INFO - 最终列映射: {'open': '开盘', 'high': '最高', 'low': '最低', 'close': '收盘', 'volume': '成交量'}
2025-05-30 23:16:33,949 - drl_trading - INFO - 成功生成 177 个技术指标因子
2025-05-30 23:16:33,950 - test_fixes - INFO - 因子挖掘进度: 因子生成 - 20% - 生成了 177 个技术指标因子
2025-05-30 23:16:33,950 - drl_trading - INFO - 因子生成 - 20% - 生成了 177 个技术指标因子
2025-05-30 23:16:33,950 - test_fixes - INFO - 因子挖掘进度: 因子生成 - 25% - 生成交叉因子
2025-05-30 23:16:33,950 - drl_trading - INFO - 因子生成 - 25% - 生成交叉因子
2025-05-30 23:16:33,952 - drl_trading - INFO - 成功生成 30 个交叉因子
2025-05-30 23:16:33,952 - test_fixes - INFO - 因子挖掘进度: 因子生成 - 30% - 生成了 30 个交叉因子
2025-05-30 23:16:33,952 - drl_trading - INFO - 因子生成 - 30% - 生成了 30 个交叉因子
2025-05-30 23:16:33,952 - test_fixes - INFO - 因子挖掘进度: 因子生成 - 35% - 生成时间序列因子
2025-05-30 23:16:33,952 - drl_trading - INFO - 因子生成 - 35% - 生成时间序列因子
2025-05-30 23:16:33,967 - drl_trading - INFO - 成功生成 100 个时间序列因子
2025-05-30 23:16:33,967 - test_fixes - INFO - 因子挖掘进度: 因子生成 - 40% - 生成了 100 个时间序列因子
2025-05-30 23:16:33,967 - drl_trading - INFO - 因子生成 - 40% - 生成了 100 个时间序列因子
2025-05-30 23:16:33,967 - test_fixes - INFO - 因子挖掘进度: 因子生成 - 40% - 因子生成完成，共 307 个因子，耗时 7.65 秒
2025-05-30 23:16:33,968 - drl_trading - INFO - 因子生成 - 40% - 因子生成完成，共 307 个因子，耗时 7.65 秒
2025-05-30 23:16:33,968 - test_fixes - INFO - 因子挖掘进度: 因子评估 - 45% - 开始评估因子
2025-05-30 23:16:33,968 - drl_trading - INFO - 因子评估 - 45% - 开始评估因子
2025-05-30 23:16:33,968 - test_fixes - INFO - 因子挖掘进度: 因子评估 - 50% - 评估 307 个因子
2025-05-30 23:16:33,968 - drl_trading - INFO - 因子评估 - 50% - 评估 307 个因子
2025-05-30 23:16:33,968 - drl_trading - INFO - 开始评估 307 个因子...
2025-05-30 23:16:34,708 - drl_trading - INFO - 已评估 10 个因子...
2025-05-30 23:16:35,718 - drl_trading - INFO - 已评估 20 个因子...
2025-05-30 23:16:36,707 - drl_trading - INFO - 已评估 30 个因子...
2025-05-30 23:16:37,683 - drl_trading - INFO - 已评估 40 个因子...
2025-05-30 23:16:37,721 - drl_trading - WARNING - 评估因子 'kama_20' 时出错: Bin edges must be unique: Index([0.0, 0.0, 129.74134483681013, 131.57702224858568, 131.9191473266997,
       134.25],
      dtype='float64', name='factor').
You can drop duplicate edges by setting the 'duplicates' kwarg
2025-05-30 23:16:38,453 - drl_trading - INFO - 已评估 50 个因子...
2025-05-30 23:16:39,206 - drl_trading - INFO - 已评估 60 个因子...
2025-05-30 23:16:39,976 - drl_trading - INFO - 已评估 70 个因子...
2025-05-30 23:16:41,341 - drl_trading - INFO - 已评估 80 个因子...
2025-05-30 23:16:42,076 - drl_trading - INFO - 已评估 90 个因子...
2025-05-30 23:16:42,978 - drl_trading - INFO - 已评估 100 个因子...
2025-05-30 23:16:44,549 - drl_trading - INFO - 已评估 110 个因子...
2025-05-30 23:16:45,288 - drl_trading - INFO - 已评估 120 个因子...
2025-05-30 23:16:46,143 - drl_trading - INFO - 已评估 130 个因子...
2025-05-30 23:16:47,190 - drl_trading - INFO - 已评估 140 个因子...
2025-05-30 23:16:48,231 - drl_trading - INFO - 已评估 150 个因子...
2025-05-30 23:16:49,762 - drl_trading - INFO - 已评估 160 个因子...
2025-05-30 23:16:51,126 - drl_trading - INFO - 已评估 170 个因子...
2025-05-30 23:16:51,962 - drl_trading - INFO - 已评估 180 个因子...
2025-05-30 23:16:53,208 - drl_trading - INFO - 已评估 190 个因子...
2025-05-30 23:16:54,700 - drl_trading - INFO - 已评估 200 个因子...
2025-05-30 23:16:56,101 - drl_trading - INFO - 因子评估完成。评估了 204 个因子，跳过了 103 个因子。
2025-05-30 23:16:56,102 - test_fixes - INFO - 因子挖掘进度: 因子评估 - 60% - 因子评估完成，共评估了 204 个因子，耗时 22.13 秒
2025-05-30 23:16:56,102 - drl_trading - INFO - 因子评估 - 60% - 因子评估完成，共评估了 204 个因子，耗时 22.13 秒
2025-05-30 23:16:56,102 - test_fixes - INFO - 因子挖掘进度: 因子筛选 - 65% - 开始筛选因子
2025-05-30 23:16:56,102 - drl_trading - INFO - 因子筛选 - 65% - 开始筛选因子
2025-05-30 23:16:56,102 - test_fixes - INFO - 因子挖掘进度: 因子筛选 - 70% - 多周期过滤
2025-05-30 23:16:56,102 - drl_trading - INFO - 因子筛选 - 70% - 多周期过滤
2025-05-30 23:16:56,103 - drl_trading - INFO - 开始多周期过滤 (最少有效周期: 3, 有效周期比例: 0.6)...
2025-05-30 23:16:56,103 - drl_trading - INFO - 多周期过滤后剩余 164 个因子
2025-05-30 23:16:56,103 - test_fixes - INFO - 因子挖掘进度: 因子筛选 - 75% - 相关性过滤
2025-05-30 23:16:56,103 - drl_trading - INFO - 因子筛选 - 75% - 相关性过滤
2025-05-30 23:16:56,103 - drl_trading - INFO - 开始移除高度相关因子 (阈值: 0.7, 方法: keep_highest_ic)...
2025-05-30 23:16:56,571 - drl_trading - INFO - 发现 3521 对高度相关因子对
2025-05-30 23:16:56,572 - drl_trading - INFO - 移除了 151 个冗余因子，剩余 13 个因子
2025-05-30 23:16:56,573 - test_fixes - INFO - 因子挖掘进度: 因子筛选 - 80% - 选择最终因子
2025-05-30 23:16:56,573 - drl_trading - INFO - 因子筛选 - 80% - 选择最终因子
2025-05-30 23:16:56,573 - drl_trading - INFO - 选择最佳因子 (top 10, 使用综合得分: True)...
2025-05-30 23:16:56,574 - drl_trading - INFO - 选择了 10 个最佳因子
2025-05-30 23:16:56,574 - test_fixes - INFO - 因子挖掘进度: 因子筛选 - 85% - 因子筛选完成，选择了 10 个最佳因子，耗时 0.47 秒
2025-05-30 23:16:56,574 - drl_trading - INFO - 因子筛选 - 85% - 因子筛选完成，选择了 10 个最佳因子，耗时 0.47 秒
2025-05-30 23:16:56,574 - test_fixes - INFO - 因子挖掘进度: 结果处理 - 90% - 绘制结果
2025-05-30 23:16:56,574 - drl_trading - INFO - 结果处理 - 90% - 绘制结果
2025-05-30 23:16:57,559 - test_fixes - INFO - 因子挖掘进度: 结果处理 - 95% - 保存评估图表到 factor_results\factor_evaluation_20250530_231657.png
2025-05-30 23:16:57,559 - drl_trading - INFO - 结果处理 - 95% - 保存评估图表到 factor_results\factor_evaluation_20250530_231657.png
2025-05-30 23:16:57,559 - test_fixes - INFO - 因子挖掘进度: 完成 - 100% - 因子挖掘成功完成
2025-05-30 23:16:57,559 - drl_trading - INFO - 完成 - 100% - 因子挖掘成功完成
2025-05-30 23:16:57,559 - drl_trading - INFO - 保存因子挖掘结果...
2025-05-30 23:16:57,621 - drl_trading - INFO - 保存因子数据到 factor_results\best_factors_20250530_231657.csv
2025-05-30 23:16:57,630 - drl_trading - INFO - 保存评估摘要到 factor_results\factor_evaluation_20250530_231657.csv
2025-05-30 23:16:57,630 - drl_trading - INFO - 自动因子挖掘流水线完成，共耗时 31.32 秒
2025-05-30 23:16:57,633 - test_fixes - INFO - 成功挖掘出 10 个因子
2025-05-30 23:16:57,633 - test_fixes - INFO - 因子列表: ['kama_10', 'close_sma5_ratio', 'sma_5', 'volume_ratio_20', 'sma_5_to_rsi_14', 'bb_width_20_10', 'rsi_6', 'sma_5_to_volatility_20', 'close_sma60_ratio', 'atr_7']
2025-05-30 23:16:57,634 - test_fixes - INFO - 因子挖掘功能测试通过
2025-05-30 23:16:57,635 - test_fixes - INFO - === 测试DRL智能体功能 ===
2025-05-30 23:16:57,635 - drl_trading - INFO - 初始化DRL智能体
2025-05-30 23:16:57,635 - drl_trading - INFO - 环境配置: None
2025-05-30 23:16:57,635 - drl_trading - INFO - 智能体配置: dict_keys(['algorithm'])
2025-05-30 23:16:57,636 - drl_trading - INFO - 超参数优化配置: None
2025-05-30 23:16:57,636 - drl_trading - WARNING - 创建DRL智能体时出错: 'NoneType' object has no attribute 'columns'，将使用空智能体
2025-05-30 23:16:57,636 - drl_trading - WARNING - 创建空智能体 (PPO)，仅用于兼容性测试
2025-05-30 23:16:57,637 - test_fixes - INFO - DRL智能体算法: PPO
2025-05-30 23:16:57,637 - test_fixes - INFO - 智能体方法: ['__class__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__firstlineno__', '__format__', '__ge__', '__getattribute__', '__getstate__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__setattr__', '__sizeof__', '__static_attributes__', '__str__', '__subclasshook__', '__weakref__', '_create_agent', 'agent', 'agent_config', 'algorithm', 'env_config', 'load', 'predict', 'save', 'train']
2025-05-30 23:16:57,637 - test_fixes - INFO - DRL智能体功能测试通过
2025-05-30 23:16:57,637 - test_fixes - INFO - ==================================================
2025-05-30 23:16:57,637 - test_fixes - INFO - 测试结果:
2025-05-30 23:16:57,637 - test_fixes - INFO - 1. GPU检测功能: 成功
2025-05-30 23:16:57,638 - test_fixes - INFO - 2. 特征工程适配器: 失败
2025-05-30 23:16:57,638 - test_fixes - INFO -    错误: 未知错误
2025-05-30 23:16:57,638 - test_fixes - INFO - 3. 性能分析器参数处理: 失败
2025-05-30 23:16:57,638 - test_fixes - INFO -    错误: 'shares'
2025-05-30 23:16:57,638 - test_fixes - INFO - 4. 数据获取功能: 成功
2025-05-30 23:16:57,638 - test_fixes - INFO - 5. 因子挖掘功能: 成功
2025-05-30 23:16:57,638 - test_fixes - INFO - 6. DRL智能体功能: 成功
2025-05-30 23:16:57,638 - test_fixes - INFO - ==================================================
2025-05-30 23:16:57,638 - test_fixes - INFO - 修复测试总结: 部分失败
