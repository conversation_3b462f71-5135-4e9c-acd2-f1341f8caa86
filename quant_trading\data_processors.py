import numpy as np
import pandas as pd
from typing import Union, List, Dict, Tuple, Optional, Callable
import logging
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
from sklearn.impute import SimpleImputer
import talib
from datetime import datetime, timedelta

# Configure logger
logger = logging.getLogger(__name__)

class DataProcessor:
    """
    Class for processing financial data for quantitative trading.
    Provides methods for handling outliers, normalization, missing values,
    feature engineering, and time series transformations.
    """
    
    def __init__(self, config: Dict = None):
        """
        Initialize the DataProcessor with configuration settings.
        
        Args:
            config: Dictionary containing configuration parameters
        """
        self.config = config or {}
        self.scalers = {}
        self.imputers = {}
        
    def remove_outliers(self, data: pd.DataFrame, 
                        columns: List[str] = None, 
                        method: str = 'zscore',
                        threshold: float = 3.0) -> pd.DataFrame:
        """
        Remove outliers from specified columns using various methods.
        
        Args:
            data: DataFrame containing financial data
            columns: List of columns to process (None = all numeric columns)
            method: Method to detect outliers ('zscore', 'iqr', 'winsorize')
            threshold: Threshold value for outlier detection
            
        Returns:
            DataFrame with outliers handled
        """
        if columns is None:
            columns = data.select_dtypes(include=np.number).columns.tolist()
            
        result_df = data.copy()
        
        for col in columns:
            if col not in result_df.columns:
                logger.warning(f"Column {col} not found in data")
                continue
                
            if method == 'zscore':
                # Z-score method
                z_scores = np.abs((result_df[col] - result_df[col].mean()) / result_df[col].std())
                result_df.loc[z_scores > threshold, col] = np.nan
                
            elif method == 'iqr':
                # Interquartile range method
                Q1 = result_df[col].quantile(0.25)
                Q3 = result_df[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - threshold * IQR
                upper_bound = Q3 + threshold * IQR
                result_df.loc[(result_df[col] < lower_bound) | (result_df[col] > upper_bound), col] = np.nan
                
            elif method == 'winsorize':
                # Winsorize method (clip values instead of removing)
                lower_bound = result_df[col].quantile(threshold / 100)
                upper_bound = result_df[col].quantile(1 - threshold / 100)
                result_df[col] = result_df[col].clip(lower=lower_bound, upper=upper_bound)
                
            else:
                logger.warning(f"Unknown outlier removal method: {method}")
                
        return result_df
    
    def normalize_features(self, data: pd.DataFrame,
                          columns: List[str] = None,
                          method: str = 'standard',
                          fit: bool = True) -> pd.DataFrame:
        """
        Normalize features using various scaling methods.
        
        Args:
            data: DataFrame containing financial data
            columns: List of columns to normalize (None = all numeric columns)
            method: Normalization method ('standard', 'minmax', 'robust')
            fit: Whether to fit the scaler or use previously fitted scaler
            
        Returns:
            DataFrame with normalized features
        """
        if columns is None:
            columns = data.select_dtypes(include=np.number).columns.tolist()
            
        result_df = data.copy()
        
        for col in columns:
            if col not in result_df.columns:
                logger.warning(f"Column {col} not found in data")
                continue
                
            if fit:
                if method == 'standard':
                    self.scalers[col] = StandardScaler()
                elif method == 'minmax':
                    self.scalers[col] = MinMaxScaler()
                elif method == 'robust':
                    self.scalers[col] = RobustScaler()
                else:
                    logger.warning(f"Unknown normalization method: {method}")
                    continue
                    
                # Reshape for sklearn's API
                values = result_df[col].values.reshape(-1, 1)
                result_df[col] = self.scalers[col].fit_transform(values).flatten()
                
            else:
                if col not in self.scalers:
                    logger.warning(f"No fitted scaler found for column {col}")
                    continue
                    
                values = result_df[col].values.reshape(-1, 1)
                result_df[col] = self.scalers[col].transform(values).flatten()
                
        return result_df
    
    def impute_missing_values(self, data: pd.DataFrame,
                             columns: List[str] = None,
                             method: str = 'mean',
                             fit: bool = True) -> pd.DataFrame:
        """
        Impute missing values in the data.
        
        Args:
            data: DataFrame containing financial data
            columns: List of columns to impute (None = all numeric columns)
            method: Imputation method ('mean', 'median', 'most_frequent', 'constant', 'ffill', 'bfill')
            fit: Whether to fit the imputer or use previously fitted imputer
            
        Returns:
            DataFrame with imputed values
        """
        if columns is None:
            columns = data.select_dtypes(include=np.number).columns.tolist()
            
        result_df = data.copy()
        
        # Handle time-series specific methods
        if method in ['ffill', 'bfill']:
            for col in columns:
                if col not in result_df.columns:
                    logger.warning(f"Column {col} not found in data")
                    continue
                    
                if method == 'ffill':
                    result_df[col] = result_df[col].fillna(method='ffill')
                    # If there are still NaNs at the beginning, use bfill
                    result_df[col] = result_df[col].fillna(method='bfill')
                else:
                    result_df[col] = result_df[col].fillna(method='bfill')
                    # If there are still NaNs at the end, use ffill
                    result_df[col] = result_df[col].fillna(method='ffill')
                    
            return result_df
        
        # Handle sklearn imputer methods
        for col in columns:
            if col not in result_df.columns:
                logger.warning(f"Column {col} not found in data")
                continue
                
            if fit:
                if method in ['mean', 'median', 'most_frequent']:
                    self.imputers[col] = SimpleImputer(strategy=method)
                elif method == 'constant':
                    self.imputers[col] = SimpleImputer(strategy='constant', fill_value=0)
                else:
                    logger.warning(f"Unknown imputation method: {method}")
                    continue
                    
                # Reshape for sklearn's API
                values = result_df[col].values.reshape(-1, 1)
                result_df[col] = self.imputers[col].fit_transform(values).flatten()
                
            else:
                if col not in self.imputers:
                    logger.warning(f"No fitted imputer found for column {col}")
                    continue
                    
                values = result_df[col].values.reshape(-1, 1)
                result_df[col] = self.imputers[col].transform(values).flatten()
                
        return result_df
    
    def generate_time_features(self, data: pd.DataFrame, 
                              date_column: str = 'date') -> pd.DataFrame:
        """
        Generate time-based features from a date column.
        
        Args:
            data: DataFrame containing financial data
            date_column: Name of the column containing dates
            
        Returns:
            DataFrame with additional time-based features
        """
        if date_column not in data.columns:
            logger.warning(f"Date column {date_column} not found in data")
            return data
            
        result_df = data.copy()
        
        # Ensure date column is datetime type
        if not pd.api.types.is_datetime64_any_dtype(result_df[date_column]):
            result_df[date_column] = pd.to_datetime(result_df[date_column])
            
        # Extract date components
        result_df['day_of_week'] = result_df[date_column].dt.dayofweek
        result_df['day_of_month'] = result_df[date_column].dt.day
        result_df['week_of_year'] = result_df[date_column].dt.isocalendar().week
        result_df['month'] = result_df[date_column].dt.month
        result_df['quarter'] = result_df[date_column].dt.quarter
        result_df['year'] = result_df[date_column].dt.year
        
        # Create cyclical features for day of week, month, etc.
        result_df['day_of_week_sin'] = np.sin(2 * np.pi * result_df['day_of_week'] / 7)
        result_df['day_of_week_cos'] = np.cos(2 * np.pi * result_df['day_of_week'] / 7)
        
        result_df['month_sin'] = np.sin(2 * np.pi * result_df['month'] / 12)
        result_df['month_cos'] = np.cos(2 * np.pi * result_df['month'] / 12)
        
        # Is trading day (Monday-Friday)
        result_df['is_trading_day'] = (result_df['day_of_week'] < 5).astype(int)
        
        # Is month end/start
        result_df['is_month_end'] = result_df[date_column].dt.is_month_end.astype(int)
        result_df['is_month_start'] = result_df[date_column].dt.is_month_start.astype(int)
        
        # Is quarter end/start
        result_df['is_quarter_end'] = result_df[date_column].dt.is_quarter_end.astype(int)
        result_df['is_quarter_start'] = result_df[date_column].dt.is_quarter_start.astype(int)
        
        # Is year end/start
        result_df['is_year_end'] = result_df[date_column].dt.is_year_end.astype(int)
        result_df['is_year_start'] = result_df[date_column].dt.is_year_start.astype(int)
        
        return result_df
    
    def calculate_technical_indicators(self, data: pd.DataFrame,
                                      ohlcv_columns: Dict[str, str] = None) -> pd.DataFrame:
        """
        Calculate common technical indicators using TA-Lib.
        
        Args:
            data: DataFrame containing OHLCV data
            ohlcv_columns: Dictionary mapping of column names to OHLCV fields
                           e.g. {'open': 'Open', 'high': 'High', ...}
                           
        Returns:
            DataFrame with additional technical indicators
        """
        # Default column mapping if not provided
        default_mapping = {
            'open': 'open',
            'high': 'high',
            'low': 'low',
            'close': 'close',
            'volume': 'volume'
        }
        
        cols = ohlcv_columns or default_mapping
        
        # Check if required columns exist
        for key, col in cols.items():
            if col not in data.columns:
                logger.warning(f"Required column {col} for {key} not found in data")
                return data
                
        result_df = data.copy()
        
        # Extract OHLCV arrays for TA-Lib
        open_data = result_df[cols['open']].values
        high_data = result_df[cols['high']].values
        low_data = result_df[cols['low']].values
        close_data = result_df[cols['close']].values
        volume_data = result_df[cols['volume']].values if 'volume' in cols else None
        
        # Calculate moving averages
        result_df['sma_5'] = talib.SMA(close_data, timeperiod=5)
        result_df['sma_10'] = talib.SMA(close_data, timeperiod=10)
        result_df['sma_20'] = talib.SMA(close_data, timeperiod=20)
        result_df['sma_50'] = talib.SMA(close_data, timeperiod=50)
        result_df['sma_200'] = talib.SMA(close_data, timeperiod=200)
        
        # Calculate exponential moving averages
        result_df['ema_5'] = talib.EMA(close_data, timeperiod=5)
        result_df['ema_10'] = talib.EMA(close_data, timeperiod=10)
        result_df['ema_20'] = talib.EMA(close_data, timeperiod=20)
        result_df['ema_50'] = talib.EMA(close_data, timeperiod=50)
        result_df['ema_200'] = talib.EMA(close_data, timeperiod=200)
        
        # Calculate RSI (Relative Strength Index)
        result_df['rsi_14'] = talib.RSI(close_data, timeperiod=14)
        
        # Calculate MACD (Moving Average Convergence Divergence)
        macd, macd_signal, macd_hist = talib.MACD(close_data)
        result_df['macd'] = macd
        result_df['macd_signal'] = macd_signal
        result_df['macd_hist'] = macd_hist
        
        # Calculate Bollinger Bands
        upper, middle, lower = talib.BBANDS(close_data, timeperiod=20)
        result_df['bb_upper'] = upper
        result_df['bb_middle'] = middle
        result_df['bb_lower'] = lower
        
        # Calculate ATR (Average True Range) - volatility indicator
        result_df['atr_14'] = talib.ATR(high_data, low_data, close_data, timeperiod=14)
        
        # Calculate Stochastic Oscillator
        slowk, slowd = talib.STOCH(high_data, low_data, close_data)
        result_df['stoch_k'] = slowk
        result_df['stoch_d'] = slowd
        
        # Calculate On-Balance Volume if volume data is available
        if volume_data is not None:
            result_df['obv'] = talib.OBV(close_data, volume_data)
            
        # Calculate ADX (Average Directional Index) - trend strength
        result_df['adx_14'] = talib.ADX(high_data, low_data, close_data, timeperiod=14)
        
        # Calculate CCI (Commodity Channel Index)
        result_df['cci_14'] = talib.CCI(high_data, low_data, close_data, timeperiod=14)
        
        # Calculate Williams %R
        result_df['willr_14'] = talib.WILLR(high_data, low_data, close_data, timeperiod=14)
        
        # Calculate MOM (Momentum)
        result_df['mom_10'] = talib.MOM(close_data, timeperiod=10)
        
        # Calculate ROC (Rate of Change)
        result_df['roc_10'] = talib.ROC(close_data, timeperiod=10)
        
        return result_df
    
    def calculate_returns(self, data: pd.DataFrame,
                         price_column: str = 'close',
                         periods: List[int] = [1, 5, 10, 20]) -> pd.DataFrame:
        """
        Calculate returns over different periods.
        
        Args:
            data: DataFrame containing price data
            price_column: Name of the column containing price data
            periods: List of periods to calculate returns for
            
        Returns:
            DataFrame with additional return columns
        """
        if price_column not in data.columns:
            logger.warning(f"Price column {price_column} not found in data")
            return data
            
        result_df = data.copy()
        
        # Calculate simple returns
        for period in periods:
            col_name = f'return_{period}d'
            result_df[col_name] = result_df[price_column].pct_change(period)
            
        # Calculate log returns
        for period in periods:
            col_name = f'log_return_{period}d'
            result_df[col_name] = np.log(result_df[price_column] / result_df[price_column].shift(period))
            
        # Calculate cumulative returns
        result_df['cum_return'] = (1 + result_df['return_1d'].fillna(0)).cumprod() - 1
        
        # Calculate volatility using rolling standard deviation of returns
        result_df['volatility_5d'] = result_df['return_1d'].rolling(window=5).std()
        result_df['volatility_20d'] = result_df['return_1d'].rolling(window=20).std()
        
        # Calculate Sharpe ratio (simplified without risk-free rate)
        result_df['sharpe_20d'] = (result_df['return_1d'].rolling(window=20).mean() / 
                                  result_df['return_1d'].rolling(window=20).std() * np.sqrt(252))
        
        return result_df
    
    def resample_data(self, data: pd.DataFrame,
                     date_column: str = 'date',
                     freq: str = 'D',
                     agg_dict: Dict[str, str] = None) -> pd.DataFrame:
        """
        Resample time series data to a different frequency.
        
        Args:
            data: DataFrame containing time series data
            date_column: Name of the column containing dates
            freq: Target frequency ('D'=daily, 'W'=weekly, 'M'=monthly, etc.)
            agg_dict: Dictionary of aggregation functions for each column
            
        Returns:
            DataFrame with resampled data
        """
        if date_column not in data.columns:
            logger.warning(f"Date column {date_column} not found in data")
            return data
            
        result_df = data.copy()
        
        # Ensure date column is datetime type
        if not pd.api.types.is_datetime64_any_dtype(result_df[date_column]):
            result_df[date_column] = pd.to_datetime(result_df[date_column])
            
        # Set date as index for resampling
        result_df = result_df.set_index(date_column)
        
        # Default aggregation dictionary if not provided
        if agg_dict is None:
            # Detect numeric columns for sum/mean and object columns for first/last
            numeric_cols = result_df.select_dtypes(include=np.number).columns
            object_cols = result_df.select_dtypes(include=object).columns
            
            agg_dict = {}
            # For OHLCV data
            if 'open' in result_df.columns:
                agg_dict['open'] = 'first'
            if 'high' in result_df.columns:
                agg_dict['high'] = 'max'
            if 'low' in result_df.columns:
                agg_dict['low'] = 'min'
            if 'close' in result_df.columns:
                agg_dict['close'] = 'last'
            if 'volume' in result_df.columns:
                agg_dict['volume'] = 'sum'
                
            # For remaining columns
            for col in numeric_cols:
                if col not in agg_dict:
                    agg_dict[col] = 'mean'
                    
            for col in object_cols:
                if col not in agg_dict:
                    agg_dict[col] = 'first'
        
        # Resample and aggregate
        resampled_df = result_df.resample(freq).agg(agg_dict)
        
        # Reset index to have date as a column again
        resampled_df = resampled_df.reset_index()
        
        return resampled_df

# Standalone functions that can be used without instantiating the class

def apply_rolling_window(data: pd.DataFrame, 
                         columns: List[str],
                         window_size: int,
                         function: Callable) -> pd.DataFrame:
    """
    Apply a rolling window function to specified columns.
    
    Args:
        data: DataFrame containing financial data
        columns: List of columns to apply the function to
        window_size: Size of the rolling window
        function: Function to apply to the rolling window (e.g., np.mean, np.std)
        
    Returns:
        DataFrame with additional rolling window columns
    """
    result_df = data.copy()
    
    for col in columns:
        if col not in result_df.columns:
            logger.warning(f"Column {col} not found in data")
            continue
            
        func_name = function.__name__
        result_df[f'{col}_{func_name}_{window_size}'] = result_df[col].rolling(window=window_size).apply(function)
        
    return result_df

def create_lagged_features(data: pd.DataFrame,
                          columns: List[str],
                          lags: List[int]) -> pd.DataFrame:
    """
    Create lagged features for specified columns.
    
    Args:
        data: DataFrame containing financial data
        columns: List of columns to create lags for
        lags: List of lag periods
        
    Returns:
        DataFrame with additional lagged features
    """
    result_df = data.copy()
    
    for col in columns:
        if col not in result_df.columns:
            logger.warning(f"Column {col} not found in data")
            continue
            
        for lag in lags:
            result_df[f'{col}_lag_{lag}'] = result_df[col].shift(lag)
            
    return result_df

def calculate_price_momentum(data: pd.DataFrame,
                            price_column: str = 'close',
                            windows: List[int] = [5, 10, 20, 60]) -> pd.DataFrame:
    """
    Calculate price momentum features.
    
    Args:
        data: DataFrame containing price data
        price_column: Name of the column containing price data
        windows: List of window sizes for momentum calculation
        
    Returns:
        DataFrame with additional momentum features
    """
    if price_column not in data.columns:
        logger.warning(f"Price column {price_column} not found in data")
        return data
        
    result_df = data.copy()
    
    # Calculate price momentum
    for window in windows:
        # Current price relative to n periods ago
        result_df[f'momentum_{window}'] = result_df[price_column] / result_df[price_column].shift(window) - 1
        
        # Current price relative to moving average
        ma_col = f'ma_{window}'
        result_df[ma_col] = result_df[price_column].rolling(window=window).mean()
        result_df[f'price_to_ma_{window}'] = result_df[price_column] / result_df[ma_col] - 1
        
    return result_df

def calculate_volatility_features(data: pd.DataFrame,
                                 price_column: str = 'close',
                                 windows: List[int] = [5, 10, 20, 60]) -> pd.DataFrame:
    """
    Calculate volatility-based features.
    
    Args:
        data: DataFrame containing price data
        price_column: Name of the column containing price data
        windows: List of window sizes for volatility calculation
        
    Returns:
        DataFrame with additional volatility features
    """
    if price_column not in data.columns:
        logger.warning(f"Price column {price_column} not found in data")
        return data
        
    result_df = data.copy()
    
    # Calculate returns
    result_df['daily_return'] = result_df[price_column].pct_change()
    
    # Calculate rolling volatility
    for window in windows:
        result_df[f'volatility_{window}d'] = result_df['daily_return'].rolling(window=window).std() * np.sqrt(252)
        
        # Parkinson volatility estimator (using high-low range)
        if 'high' in result_df.columns and 'low' in result_df.columns:
            hl_ratio = np.log(result_df['high'] / result_df['low'])
            result_df[f'parkinson_vol_{window}d'] = np.sqrt(
                1 / (4 * np.log(2)) * hl_ratio.pow(2).rolling(window=window).mean() * 252
            )
            
    # GARCH-like volatility feature (simple approximation)
    result_df['abs_return'] = np.abs(result_df['daily_return'])
    result_df['sq_return'] = result_df['daily_return'] ** 2
    
    # Exponentially weighted volatility
    for window in [5, 20]:
        halflife = window / 2
        result_df[f'ewm_volatility_{window}d'] = np.sqrt(
            result_df['sq_return'].ewm(halflife=halflife).mean() * 252
        )
        
    return result_df

def engineer_cross_sectional_features(data: pd.DataFrame, 
                                     groupby_column: str,
                                     value_column: str,
                                     date_column: str = 'date') -> pd.DataFrame:
    """
    Engineer cross-sectional features (e.g., sector/industry-relative metrics).
    
    Args:
        data: DataFrame containing financial data
        groupby_column: Column to group by (e.g., 'sector', 'industry')
        value_column: Column to calculate relative values for
        date_column: Name of the column containing dates
        
    Returns:
        DataFrame with additional cross-sectional features
    """
    required_cols = [groupby_column, value_column, date_column]
    for col in required_cols:
        if col not in data.columns:
            logger.warning(f"Required column {col} not found in data")
            return data
            
    result_df = data.copy()
    
    # Ensure date column is datetime type
    if not pd.api.types.is_datetime64_any_dtype(result_df[date_column]):
        result_df[date_column] = pd.to_datetime(result_df[date_column])
        
    # Group by date and the groupby column
    grouped = result_df.groupby([date_column, groupby_column])
    
    # Calculate group mean
    group_mean = grouped[value_column].transform('mean')
    
    # Calculate value relative to group mean
    result_df[f'{value_column}_rel_to_{groupby_column}_mean'] = result_df[value_column] / group_mean
    
    # Calculate z-score within group
    group_std = grouped[value_column].transform('std')
    result_df[f'{value_column}_{groupby_column}_zscore'] = (result_df[value_column] - group_mean) / group_std
    
    # Calculate percentile rank within group
    result_df[f'{value_column}_{groupby_column}_rank'] = grouped[value_column].transform(
        lambda x: (x.rank(pct=True) * 100).astype(int)
    )
    
    return result_df 