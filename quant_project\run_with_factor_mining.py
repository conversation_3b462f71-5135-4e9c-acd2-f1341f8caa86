#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
DRL量化交易系统 - 带自动因子挖掘的运行脚本
该脚本用于启动DRL量化交易系统，并集成了自动因子挖掘功能
"""

import os
import sys
import logging
import subprocess

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
sys.path.append(os.path.dirname(current_dir))

# 确保日志目录存在
os.makedirs('logs', exist_ok=True)
os.makedirs('data_cache', exist_ok=True)
os.makedirs('plots', exist_ok=True)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/app.log')
    ]
)

logger = logging.getLogger('drl_trading')

def main():
    """主函数"""
    logger.info("启动DRL量化交易系统（带自动因子挖掘功能）")
    
    try:
        # 检查自动因子挖掘模块是否可用
        try:
            from core_logic.auto_factor_mining import AutoFactorPipeline
            logger.info("自动因子挖掘模块已加载")
        except ImportError as e:
            logger.error(f"导入自动因子挖掘模块失败: {str(e)}")
            logger.info("请确保已正确安装自动因子挖掘模块")
            return
        
        # 检查自动因子挖掘页面模块是否可用
        try:
            from auto_factor_mining_page import display_auto_factor_mining_page
            logger.info("自动因子挖掘页面模块已加载")
        except ImportError as e:
            logger.error(f"导入自动因子挖掘页面模块失败: {str(e)}")
            logger.info("请确保已创建自动因子挖掘页面模块")
            return
        
        # 运行主应用程序
        import streamlit as st
        import subprocess
        
        # 使用streamlit运行应用程序
        cmd = [sys.executable, "-m", "streamlit", "run", "main_app_with_factor_mining.py"]
        logger.info(f"执行命令: {' '.join(cmd)}")
        
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 显示输出
        for line in process.stdout:
            print(line, end='')
        
        # 等待进程完成
        process.wait()
        
        if process.returncode != 0:
            logger.error(f"应用程序退出，返回码: {process.returncode}")
            for line in process.stderr:
                print(line, end='')
        else:
            logger.info("应用程序正常退出")
        
    except Exception as e:
        logger.error(f"运行应用程序时出错: {str(e)}")

if __name__ == "__main__":
    main() 