#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
UI高级测试脚本
测试DRL智能体、交易环境和性能分析模块的UI交互
"""

import os
import sys
import time
import logging
import subprocess
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import traceback
import json
import yaml

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join('logs', 'ui_advanced_test.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('ui_advanced_test')

# 导入项目模块
try:
    from core_logic.data_handler import DataHandler
    from core_logic.feature_engineer import FeatureEngineer
    from core_logic.trading_environment import TradingEnvironment
    from core_logic.drl_agent import DRLAgent
    from core_logic.performance_analyzer import PerformanceAnalyzer
    from core_logic.utils import setup_logger, is_gpu_available
    logger.info("成功导入所有核心模块")
except ImportError as e:
    logger.error(f"导入模块失败: {str(e)}")
    sys.exit(1)

def test_trading_environment(processed_data):
    """测试交易环境模块"""
    logger.info("测试交易环境模块")
    
    try:
        # 测试不同环境参数
        env_configs = [
            # 基本配置
            {
                'initial_capital': 100000,
                'commission_rate': 0.0003,
                'min_hold_days': 3,
                'window_size': 20
            },
            # 高初始资金配置
            {
                'initial_capital': 1000000,
                'commission_rate': 0.0003,
                'min_hold_days': 3,
                'window_size': 20
            },
            # 高手续费配置
            {
                'initial_capital': 100000,
                'commission_rate': 0.001,
                'min_hold_days': 3,
                'window_size': 20
            },
            # 长持仓期配置
            {
                'initial_capital': 100000,
                'commission_rate': 0.0003,
                'min_hold_days': 5,
                'window_size': 20
            },
            # 长观测窗口配置
            {
                'initial_capital': 100000,
                'commission_rate': 0.0003,
                'min_hold_days': 3,
                'window_size': 30
            }
        ]
        
        results = []
        
        for i, config in enumerate(env_configs):
            try:
                logger.info(f"测试环境配置 {i+1}")
                
                env = TradingEnvironment(
                    df_processed_data=processed_data,
                    initial_capital=config['initial_capital'],
                    commission_rate=config['commission_rate'],
                    min_hold_days=config['min_hold_days'],
                    window_size=config['window_size']
                )
                
                # 测试环境重置
                observation = env.reset()
                
                # 检查观测空间
                obs_shape = env.observation_space.shape
                
                # 检查动作空间
                action_space = env.action_space
                
                logger.info(f"环境创建成功: 观测空间形状 {obs_shape}, 动作空间 {action_space}")
                
                # 测试环境步进
                action = 1  # 买入动作
                next_obs, reward, done, truncated, info = env.step(action)
                
                logger.info(f"环境步进成功: 奖励 {reward}, 完成 {done}, 信息 {info}")
                
                results.append({
                    'config_id': i+1,
                    'success': True,
                    'obs_shape': str(obs_shape),
                    'action_space': str(action_space),
                    'reward': reward
                })
            except Exception as e:
                logger.error(f"测试环境配置 {i+1} 出错: {str(e)}")
                results.append({
                    'config_id': i+1,
                    'success': False,
                    'error': str(e)
                })
        
        # 保存测试结果
        results_df = pd.DataFrame(results)
        os.makedirs('test_results', exist_ok=True)
        results_df.to_csv('test_results/trading_environment_test.csv', index=False)
        
        success_rate = results_df['success'].mean() * 100
        logger.info(f"交易环境模块测试完成，成功率: {success_rate:.2f}%")
        
        return {
            'success_rate': success_rate,
            'configs_tested': len(env_configs)
        }
    
    except Exception as e:
        logger.error(f"测试交易环境模块时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            'success_rate': 0,
            'configs_tested': 0
        }

def test_drl_agent(processed_data):
    """测试DRL智能体模块"""
    logger.info("测试DRL智能体模块")
    
    try:
        # 创建基本环境
        env_config = {
            'df_processed_data': processed_data,
            'initial_capital': 100000,
            'commission_rate': 0.0003,
            'min_hold_days': 3,
            'window_size': 20
        }
        
        # 测试不同算法配置
        agent_configs = [
            # PPO配置
            {
                'algorithm': 'PPO',
                'policy_network': 'MlpPolicy',
                'learning_rate': 0.0003,
                'gamma': 0.99,
                'verbose': 0
            },
            # A2C配置
            {
                'algorithm': 'A2C',
                'policy_network': 'MlpPolicy',
                'learning_rate': 0.0007,
                'gamma': 0.99,
                'verbose': 0
            },
            # DQN配置
            {
                'algorithm': 'DQN',
                'policy_network': 'MlpPolicy',
                'learning_rate': 0.0001,
                'gamma': 0.99,
                'verbose': 0
            }
        ]
        
        results = []
        
        for i, config in enumerate(agent_configs):
            try:
                logger.info(f"测试智能体配置 {i+1}: {config['algorithm']}")
                
                # 创建智能体
                agent = DRLAgent(env_config=env_config)
                
                # 创建模型
                model = agent.create_model(
                    algorithm=config['algorithm'],
                    policy=config['policy_network'],
                    learning_rate=config['learning_rate'],
                    gamma=config['gamma'],
                    verbose=config['verbose']
                )
                
                logger.info(f"模型创建成功: {config['algorithm']}")
                
                # 简短训练（仅测试功能，不追求性能）
                total_timesteps = 1000  # 仅用于测试
                model = agent.train_model(
                    model=model,
                    total_timesteps=total_timesteps,
                    log_dir='logs/test_training'
                )
                
                logger.info(f"模型训练成功: {config['algorithm']}, {total_timesteps} 步")
                
                # 保存模型
                save_path = os.path.join('test_results', f"test_model_{config['algorithm']}.zip")
                agent.save_model(model, save_path)
                
                # 加载模型
                loaded_model = agent.load_model(save_path)
                
                logger.info(f"模型保存和加载成功: {config['algorithm']}")
                
                results.append({
                    'config_id': i+1,
                    'algorithm': config['algorithm'],
                    'success': True,
                    'timesteps': total_timesteps
                })
            except Exception as e:
                logger.error(f"测试智能体配置 {i+1} 出错: {str(e)}")
                results.append({
                    'config_id': i+1,
                    'algorithm': config['algorithm'],
                    'success': False,
                    'error': str(e)
                })
        
        # 保存测试结果
        results_df = pd.DataFrame(results)
        os.makedirs('test_results', exist_ok=True)
        results_df.to_csv('test_results/drl_agent_test.csv', index=False)
        
        success_rate = results_df['success'].mean() * 100
        logger.info(f"DRL智能体模块测试完成，成功率: {success_rate:.2f}%")
        
        return {
            'success_rate': success_rate,
            'algorithms_tested': len(agent_configs)
        }
    
    except Exception as e:
        logger.error(f"测试DRL智能体模块时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            'success_rate': 0,
            'algorithms_tested': 0
        }

def test_performance_analyzer(processed_data):
    """测试性能分析模块"""
    logger.info("测试性能分析模块")
    
    try:
        # 创建基本环境和智能体
        env_config = {
            'df_processed_data': processed_data,
            'initial_capital': 100000,
            'commission_rate': 0.0003,
            'min_hold_days': 3,
            'window_size': 20
        }
        
        # 创建环境
        env = TradingEnvironment(
            df_processed_data=processed_data,
            initial_capital=env_config['initial_capital'],
            commission_rate=env_config['commission_rate'],
            min_hold_days=env_config['min_hold_days'],
            window_size=env_config['window_size']
        )
        
        # 创建智能体
        agent = DRLAgent(env_config=env_config)
        
        # 创建简单模型
        model = agent.create_model(
            algorithm='PPO',
            policy='MlpPolicy',
            learning_rate=0.0003,
            gamma=0.99,
            verbose=0
        )
        
        # 简短训练
        total_timesteps = 1000  # 仅用于测试
        model = agent.train_model(
            model=model,
            total_timesteps=total_timesteps,
            log_dir='logs/test_training'
        )
        
        # 执行回测
        obs = env.reset()
        done = False
        actions = []
        rewards = []
        portfolio_values = []
        
        while not done:
            action, _ = model.predict(obs, deterministic=True)
            obs, reward, done, truncated, info = env.step(action)
            
            actions.append(action)
            rewards.append(reward)
            portfolio_values.append(info.get('portfolio_value', 0))
        
        # 创建性能分析器
        analyzer = PerformanceAnalyzer()
        
        # 分析性能
        performance = analyzer.analyze_performance(
            portfolio_values=portfolio_values,
            benchmark=None,  # 可以添加基准数据
            risk_free_rate=0.02
        )
        
        logger.info(f"性能分析成功: {performance}")
        
        # 保存测试结果
        os.makedirs('test_results', exist_ok=True)
        with open('test_results/performance_analyzer_test.json', 'w') as f:
            json.dump(performance, f, indent=4)
        
        return {
            'success': True,
            'metrics_calculated': len(performance)
        }
    
    except Exception as e:
        logger.error(f"测试性能分析模块时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            'success': False,
            'error': str(e)
        }

def run_all_tests():
    """运行所有高级测试"""
    logger.info("开始运行所有UI高级测试")
    
    # 确保测试目录存在
    os.makedirs('test_results', exist_ok=True)
    os.makedirs('logs', exist_ok=True)
    
    # 获取样本数据用于测试
    try:
        data_handler = DataHandler()
        sample_data = data_handler.get_stock_data(
            stock_code='sh000001',
            start_date='2023-01-01',
            end_date='2023-12-31',
            frequency='日线'
        )
        logger.info(f"获取样本数据成功: {len(sample_data)} 条记录")
        
        # 处理样本数据
        feature_engineer = FeatureEngineer()
        processed_data = feature_engineer.generate_features(sample_data)
        processed_data = processed_data.dropna()
        logger.info(f"处理样本数据成功: {len(processed_data)} 条记录, {len(processed_data.columns)} 个特征")
    except Exception as e:
        logger.error(f"获取或处理样本数据失败: {str(e)}")
        processed_data = None
    
    results = {}
    
    # 如果有处理好的数据，测试交易环境模块
    if processed_data is not None and not processed_data.empty:
        # 测试交易环境模块
        trading_env_results = test_trading_environment(processed_data)
        results['交易环境模块'] = trading_env_results
        
        # 测试DRL智能体模块
        drl_agent_results = test_drl_agent(processed_data)
        results['DRL智能体模块'] = drl_agent_results
        
        # 测试性能分析模块
        performance_analyzer_results = test_performance_analyzer(processed_data)
        results['性能分析模块'] = performance_analyzer_results
    else:
        logger.warning("无法测试高级模块: 处理后的样本数据不可用")
    
    logger.info(f"所有高级测试完成，结果: {results}")
    
    return results

if __name__ == "__main__":
    run_all_tests()
