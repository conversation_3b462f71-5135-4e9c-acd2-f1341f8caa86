"""
特征工程工具模块
提供特征工程相关的工具函数
"""

import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
from sklearn.feature_selection import SelectKBest, mutual_info_regression, f_regression
import logging

def normalize_features(df, method='minmax', columns=None, logger=None):
    """
    归一化特征
    
    参数:
        df (pandas.DataFrame): 包含特征的数据框
        method (str): 归一化方法，可选'minmax'(默认),'standard','robust'
        columns (list): 要归一化的列，如果为None则归一化所有数值列
        logger (logging.Logger): 日志记录器
        
    返回:
        pandas.DataFrame: 归一化后的数据框
    """
    if logger is None:
        logger = logging.getLogger('drl_trading')
    
    # 复制数据框，避免修改原始数据
    df_normalized = df.copy()
    
    # 如果没有指定列，则选择所有数值列
    if columns is None:
        columns = df.select_dtypes(include=['number']).columns.tolist()
        # 排除OHLCV列
        ohlcv_columns = ['开盘', '最高', '最低', '收盘', '成交量']
        columns = [col for col in columns if col not in ohlcv_columns]
    
    # 创建归一化器
    if method == 'minmax':
        scaler = MinMaxScaler()
    elif method == 'standard':
        scaler = StandardScaler()
    elif method == 'robust':
        scaler = RobustScaler()
    else:
        logger.warning(f"不支持的归一化方法: {method}，使用默认的MinMaxScaler")
        scaler = MinMaxScaler()
    
    # 归一化特征
    try:
        # 提取要归一化的数据
        features = df_normalized[columns].values
        
        # 处理NaN值
        features_no_nan = np.nan_to_num(features, nan=0)
        
        # 归一化
        normalized_features = scaler.fit_transform(features_no_nan)
        
        # 更新数据框
        df_normalized[columns] = normalized_features
        
        logger.info(f"成功归一化 {len(columns)} 个特征，使用方法: {method}")
    except Exception as e:
        logger.error(f"归一化特征失败: {str(e)}")
    
    return df_normalized

def select_features(df, target_col, n_features=30, method='mutual_info', logger=None):
    """
    特征选择
    
    参数:
        df (pandas.DataFrame): 包含特征的数据框
        target_col (str): 目标列名
        n_features (int): 要选择的特征数量
        method (str): 特征选择方法，可选'mutual_info'(默认),'f_regression'
        logger (logging.Logger): 日志记录器
        
    返回:
        tuple: (选择特征后的数据框, 特征重要性字典)
    """
    if logger is None:
        logger = logging.getLogger('drl_trading')
    
    # 复制数据框，避免修改原始数据
    df_selected = df.copy()
    
    # 排除OHLCV列和目标列
    ohlcv_columns = ['开盘', '最高', '最低', '收盘', '成交量']
    feature_columns = [col for col in df.columns if col not in ohlcv_columns and col != target_col]
    
    # 如果特征数量小于等于要选择的数量，则返回原始数据框
    if len(feature_columns) <= n_features:
        logger.info(f"特征数量 ({len(feature_columns)}) 小于等于要选择的数量 ({n_features})，不进行特征选择")
        # 创建一个特征重要性字典，所有特征重要性相同
        feature_importance = {col: 1.0 for col in feature_columns}
        return df_selected, feature_importance
    
    # 准备特征和目标
    X = df[feature_columns]
    y = df[target_col]
    
    # 处理NaN值
    X = X.fillna(0)
    y = y.fillna(0)
    
    # 创建特征选择器
    if method == 'mutual_info':
        selector = SelectKBest(mutual_info_regression, k=n_features)
    elif method == 'f_regression':
        selector = SelectKBest(f_regression, k=n_features)
    else:
        logger.warning(f"不支持的特征选择方法: {method}，使用默认的mutual_info")
        selector = SelectKBest(mutual_info_regression, k=n_features)
    
    # 特征选择
    try:
        selector.fit(X, y)
        
        # 获取特征得分
        scores = selector.scores_
        
        # 创建特征重要性字典
        feature_importance = {feature: score for feature, score in zip(feature_columns, scores)}
        
        # 按重要性排序
        sorted_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)
        
        # 选择前n_features个特征
        selected_features = [feature for feature, _ in sorted_features[:n_features]]
        
        # 更新数据框，只保留选定的特征和OHLCV列
        df_selected = df[ohlcv_columns + [target_col] + selected_features]
        
        logger.info(f"成功选择 {len(selected_features)} 个特征，使用方法: {method}")
    except Exception as e:
        logger.error(f"特征选择失败: {str(e)}")
        # 返回原始数据框
        feature_importance = {col: 1.0 for col in feature_columns}
    
    return df_selected, feature_importance

def check_stationarity(df, columns=None, logger=None):
    """
    检查特征平稳性
    
    参数:
        df (pandas.DataFrame): 包含特征的数据框
        columns (list): 要检查的列，如果为None则检查所有数值列
        logger (logging.Logger): 日志记录器
        
    返回:
        dict: 平稳性检查结果
    """
    from statsmodels.tsa.stattools import adfuller
    
    if logger is None:
        logger = logging.getLogger('drl_trading')
    
    # 如果没有指定列，则选择所有数值列
    if columns is None:
        columns = df.select_dtypes(include=['number']).columns.tolist()
        # 排除OHLCV列
        ohlcv_columns = ['开盘', '最高', '最低', '收盘', '成交量']
        columns = [col for col in columns if col not in ohlcv_columns]
    
    # 平稳性检查结果
    stationarity_results = {}
    
    for col in columns:
        try:
            # 去除NaN值
            series = df[col].dropna()
            
            # 如果数据点太少，跳过
            if len(series) < 20:
                stationarity_results[col] = {
                    'stationary': False,
                    'p_value': 1.0,
                    'reason': '数据点太少'
                }
                continue
            
            # 进行ADF测试
            result = adfuller(series)
            
            # 解析结果
            p_value = result[1]
            stationary = p_value < 0.05
            
            stationarity_results[col] = {
                'stationary': stationary,
                'p_value': p_value,
                'critical_values': result[4]
            }
        except Exception as e:
            logger.warning(f"检查特征 {col} 的平稳性失败: {str(e)}")
            stationarity_results[col] = {
                'stationary': False,
                'p_value': 1.0,
                'reason': str(e)
            }
    
    return stationarity_results
