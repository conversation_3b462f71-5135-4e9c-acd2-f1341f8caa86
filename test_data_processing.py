#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据处理与特征工程测试脚本
测试数据获取、缓存、清洗和特征工程功能
"""

import os
import sys
import pandas as pd
import numpy as np
import logging
import traceback
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import talib

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'logs/data_processing_test_{datetime.now().strftime("%Y-%m-%d")}.log')
    ]
)

# 确保logs目录存在
os.makedirs('logs', exist_ok=True)

# 添加项目路径到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入相关模块
from quant_trading.data.data_handler import DataHandler
from quant_trading.features.feature_engineer import FeatureEngineer
from quant_trading.utils.common import load_config

def test_data_acquisition():
    """测试数据获取与缓存功能"""
    print("\n===== 测试数据获取与缓存 =====")

    # 创建数据处理器实例
    handler = DataHandler(cache_dir='data_cache')

    # 设置测试日期范围
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=60)).strftime('%Y-%m-%d')

    # 测试股票数据获取
    stocks = ['sh000001', 'sz399001']

    for stock in stocks:
        try:
            print(f"\n获取股票数据: {stock}")
            # 首次获取数据（应该从API获取并缓存）
            data1 = handler.get_stock_data(stock, start_date, end_date)

            if data1 is not None and not data1.empty:
                print(f"成功获取 {stock} 数据, 行数: {len(data1)}")
                print(f"数据日期范围: {data1.index.min()} 到 {data1.index.max()}")
                print(f"数据列: {data1.columns.tolist()}")

                # 再次获取相同数据（应该从缓存获取）
                print(f"\n再次获取相同数据（应从缓存获取）")
                data2 = handler.get_stock_data(stock, start_date, end_date)

                if data2 is not None and not data2.empty:
                    print(f"成功从缓存获取 {stock} 数据, 行数: {len(data2)}")

                    # 验证两次获取的数据是否相同
                    if data1.equals(data2):
                        print("验证通过: 两次获取的数据完全相同")
                    else:
                        print("警告: 两次获取的数据不同")
                else:
                    print(f"从缓存获取 {stock} 数据失败: 数据为空")
            else:
                print(f"获取 {stock} 数据失败: 数据为空")
        except Exception as e:
            print(f"获取 {stock} 数据时出错: {str(e)}")
            print(traceback.format_exc())

    # 测试指数数据获取
    indices = ['index_000001', 'index_399001']

    for idx in indices:
        try:
            print(f"\n获取指数数据: {idx}")
            data = handler.get_stock_data(idx, start_date, end_date)

            if data is not None and not data.empty:
                print(f"成功获取 {idx} 数据, 行数: {len(data)}")
                print(f"数据日期范围: {data.index.min()} 到 {data.index.max()}")
                print(f"数据列: {data.columns.tolist()}")
            else:
                print(f"获取 {idx} 数据失败: 数据为空")
        except Exception as e:
            print(f"获取 {idx} 数据时出错: {str(e)}")
            print(traceback.format_exc())

    # 测试不同频率数据获取
    stock = 'sh000001'
    frequencies = ['日线', '周线', '月线']

    for freq in frequencies:
        try:
            print(f"\n获取 {stock} 的 {freq} 数据")
            data = handler.get_stock_data(stock, start_date, end_date, frequency=freq)

            if data is not None and not data.empty:
                print(f"成功获取 {freq} 数据, 行数: {len(data)}")
                print(f"数据日期范围: {data.index.min()} 到 {data.index.max()}")
            else:
                print(f"获取 {freq} 数据失败: 数据为空")
        except Exception as e:
            print(f"获取 {freq} 数据时出错: {str(e)}")
            print(traceback.format_exc())

def test_data_cleaning():
    """测试数据清洗与预处理功能"""
    print("\n===== 测试数据清洗与预处理 =====")

    # 创建数据处理器实例
    handler = DataHandler(cache_dir='data_cache')

    # 获取测试数据
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=60)).strftime('%Y-%m-%d')
    stock = 'sh000001'

    try:
        # 获取原始数据
        data = handler.get_stock_data(stock, start_date, end_date)

        if data is None or data.empty:
            print("获取测试数据失败，无法进行数据清洗测试")
            return

        print(f"获取到原始数据，行数: {len(data)}")

        # 创建带有缺失值的测试数据
        test_data = data.copy()

        # 随机引入缺失值
        np.random.seed(42)
        mask = np.random.random(test_data.shape) < 0.05  # 5%的数据设为缺失
        test_data[mask] = np.nan

        # 随机引入异常值
        for col in test_data.select_dtypes(include=[np.number]).columns:
            # 在随机位置设置异常值
            outlier_idx = np.random.choice(test_data.index, size=2)
            test_data.loc[outlier_idx, col] = test_data[col].mean() * 10  # 设置为均值的10倍

        print(f"人为引入缺失值和异常值后，缺失值数量: {test_data.isna().sum().sum()}")

        # 测试填充缺失值
        print("\n测试填充缺失值...")
        # 使用内部方法_clean_data进行数据清洗
        filled_data = handler._clean_data(test_data)

        print(f"填充后缺失值数量: {filled_data.isna().sum().sum()}")
        if filled_data.isna().sum().sum() == 0:
            print("验证通过: 所有缺失值已填充")
        else:
            print(f"警告: 仍有 {filled_data.isna().sum().sum()} 个缺失值未填充")

        # 测试异常值处理
        print("\n测试异常值处理...")
        # 检查异常值是否被处理
        for col in filled_data.select_dtypes(include=[np.number]).columns:
            # 计算Z分数来检测异常值
            z_scores = np.abs((filled_data[col] - filled_data[col].mean()) / filled_data[col].std())
            outliers = z_scores > 3  # 通常认为Z分数大于3的为异常值
            if outliers.sum() > 0:
                print(f"列 {col} 中仍有 {outliers.sum()} 个异常值")
            else:
                print(f"列 {col} 中的异常值已被处理")

        # 测试时间序列对齐
        print("\n测试时间序列对齐...")
        # 创建不连续的时间序列
        irregular_data = data.copy()
        drop_indices = np.random.choice(irregular_data.index, size=5, replace=False)
        irregular_data = irregular_data.drop(drop_indices)

        print(f"删除随机日期后的数据行数: {len(irregular_data)}")

        # 对齐时间序列
        aligned_data = handler._align_time_series(irregular_data, start_date, end_date)

        print(f"对齐后的数据行数: {len(aligned_data)}")

    except Exception as e:
        print(f"测试数据清洗时出错: {str(e)}")
        print(traceback.format_exc())

def test_feature_engineering():
    """测试特征工程功能"""
    print("\n===== 测试特征工程 =====")

    # 创建数据处理器实例
    handler = DataHandler(cache_dir='data_cache')

    # 获取测试数据
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=120)).strftime('%Y-%m-%d')
    stock = 'sh000001'

    try:
        # 获取原始数据
        data = handler.get_stock_data(stock, start_date, end_date)

        if data is None or data.empty:
            print("获取测试数据失败，无法进行特征工程测试")
            return

        print(f"获取到原始数据，行数: {len(data)}")

        # 加载特征配置
        env_config = load_config("quant_project/configs/env_config.yaml")
        feature_config = env_config.get('feature_config', {})

        # 创建特征工程实例
        feature_engineer = FeatureEngineer(feature_config)

        # 生成特征
        print("\n生成技术指标特征...")
        features_data = feature_engineer.generate_features(data)

        print(f"生成特征后的数据行数: {len(features_data)}")
        print(f"生成的特征列: {features_data.columns.tolist()}")

        # 验证特征计算的准确性
        print("\n验证特征计算的准确性...")

        # 使用talib计算一些指标进行对比
        if 'close' in data.columns:
            # 计算SMA
            if feature_config.get('sma', {}).get('use', False):
                periods = feature_config.get('sma', {}).get('periods', [5, 20])
                for period in periods:
                    talib_sma = talib.SMA(data['close'].values, timeperiod=period)
                    feature_name = f'sma_{period}'

                    if feature_name in features_data.columns:
                        # 忽略前面的NaN值
                        valid_idx = ~np.isnan(talib_sma)
                        correlation = np.corrcoef(
                            talib_sma[valid_idx],
                            features_data[feature_name].values[valid_idx]
                        )[0, 1]

                        print(f"SMA_{period} 相关性: {correlation:.4f}")
                        if correlation > 0.99:
                            print(f"验证通过: SMA_{period} 计算准确")
                        else:
                            print(f"警告: SMA_{period} 计算可能不准确")

            # 计算RSI
            if feature_config.get('rsi', {}).get('use', False):
                period = feature_config.get('rsi', {}).get('period', 14)
                talib_rsi = talib.RSI(data['close'].values, timeperiod=period)
                feature_name = f'rsi_{period}'

                if feature_name in features_data.columns:
                    # 忽略前面的NaN值
                    valid_idx = ~np.isnan(talib_rsi)
                    correlation = np.corrcoef(
                        talib_rsi[valid_idx],
                        features_data[feature_name].values[valid_idx]
                    )[0, 1]

                    print(f"RSI_{period} 相关性: {correlation:.4f}")
                    if correlation > 0.99:
                        print(f"验证通过: RSI_{period} 计算准确")
                    else:
                        print(f"警告: RSI_{period} 计算可能不准确")

        # 测试特征归一化
        print("\n测试特征归一化...")
        # 使用内部方法_normalize_features进行特征归一化
        normalized_features = feature_engineer._normalize_features(features_data)

        # 检查归一化后的值是否在[-1, 1]范围内
        # 排除原始价格列
        price_columns = ['开盘', '最高', '最低', '收盘', '成交量', '成交额']
        feature_columns = [col for col in normalized_features.columns if col not in price_columns]

        if feature_columns:
            min_values = normalized_features[feature_columns].min()
            max_values = normalized_features[feature_columns].max()

            print(f"归一化后的最小值: {min_values.min()}")
            print(f"归一化后的最大值: {max_values.max()}")

            if min_values.min() >= -1 and max_values.max() <= 1:
                print("验证通过: 特征归一化正确，所有值在[-1, 1]范围内")
            else:
                print("警告: 特征归一化可能不正确，有值超出[-1, 1]范围")
        else:
            print("警告: 没有找到需要归一化的特征列")

    except Exception as e:
        print(f"测试特征工程时出错: {str(e)}")
        print(traceback.format_exc())

def run_all_tests():
    """运行所有数据处理与特征工程测试"""
    try:
        test_data_acquisition()
        test_data_cleaning()
        test_feature_engineering()
        print("\n===== 所有数据处理与特征工程测试完成 =====")
        return True
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        print(traceback.format_exc())
        return False

if __name__ == '__main__':
    run_all_tests()
