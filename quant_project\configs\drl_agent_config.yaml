# DRL智能体配置文件

# 算法配置
algorithm: PPO  # 可选: PPO, A2C, DQN
policy_network: MlpPolicy  # 策略网络类型

# 通用超参数
learning_rate: 0.0003  # 学习率
gamma: 0.99  # 折扣因子
use_gpu: true  # 是否使用GPU

# PPO特定参数
ppo:
  n_steps: 2048  # 每次更新前收集的步数
  batch_size: 64  # 每次优化的小批量大小
  n_epochs: 10  # 每次更新时的优化轮数
  gae_lambda: 0.95  # GAE参数
  clip_range: 0.2  # PPO裁剪参数
  ent_coef: 0.01  # 熵系数
  vf_coef: 0.5  # 价值函数系数
  max_grad_norm: 0.5  # 梯度裁剪

# A2C特定参数
a2c:
  n_steps: 5  # 每次更新前收集的步数
  ent_coef: 0.01  # 熵系数
  vf_coef: 0.5  # 价值函数系数
  max_grad_norm: 0.5  # 梯度裁剪

# DQN特定参数
dqn:
  buffer_size: 10000  # 经验回放缓冲区大小
  learning_starts: 1000  # 开始学习前收集的步数
  batch_size: 32  # 每次优化的小批量大小
  tau: 1.0  # 目标网络更新率
  train_freq: 4  # 训练频率
  gradient_steps: 1  # 每次更新的梯度步数
  target_update_interval: 1000  # 目标网络更新间隔
  exploration_fraction: 0.1  # 探索率衰减的比例
  exploration_initial_eps: 1.0  # 初始探索率
  exploration_final_eps: 0.05  # 最终探索率
  max_grad_norm: 10  # 梯度裁剪

# 网络结构
network:
  net_arch:
    pi: [64, 64]  # 策略网络隐藏层
    vf: [64, 64]  # 价值网络隐藏层
  activation_fn: relu  # 激活函数

# 训练配置
training:
  total_timesteps: 100000  # 总训练步数
  eval_freq: 10000  # 评估频率
  n_eval_episodes: 5  # 评估回合数
  save_freq: 10000  # 保存频率
