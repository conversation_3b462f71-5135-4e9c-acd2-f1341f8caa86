#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全面测试主脚本
运行所有测试并生成综合报告
"""

import os
import sys
import time
import logging
import subprocess
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import traceback
import json
import matplotlib.pyplot as plt
import importlib

# 设置日志
os.makedirs('logs', exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join('logs', 'all_comprehensive_tests.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('all_comprehensive_tests')

def run_test_script(script_path):
    """运行测试脚本"""
    logger.info(f"运行测试脚本: {script_path}")
    
    try:
        # 运行脚本
        result = subprocess.run(
            [sys.executable, script_path],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            check=True
        )
        
        logger.info(f"测试脚本 {script_path} 运行完成")
        logger.debug(f"标准输出: {result.stdout}")
        
        if result.stderr:
            logger.warning(f"标准错误: {result.stderr}")
        
        return {
            'script': script_path,
            'success': True,
            'stdout': result.stdout,
            'stderr': result.stderr,
            'error': None
        }
    
    except subprocess.CalledProcessError as e:
        logger.error(f"测试脚本 {script_path} 运行失败: {str(e)}")
        logger.debug(f"标准输出: {e.stdout}")
        logger.error(f"标准错误: {e.stderr}")
        
        return {
            'script': script_path,
            'success': False,
            'stdout': e.stdout,
            'stderr': e.stderr,
            'error': str(e)
        }
    
    except Exception as e:
        logger.error(f"测试脚本 {script_path} 运行失败: {str(e)}")
        
        return {
            'script': script_path,
            'success': False,
            'stdout': None,
            'stderr': None,
            'error': str(e)
        }

def run_test_module(module_name):
    """运行测试模块"""
    logger.info(f"运行测试模块: {module_name}")
    
    try:
        # 导入模块
        module = importlib.import_module(module_name)
        
        # 获取测试函数
        test_function = getattr(module, 'run_all_tests')
        
        # 运行测试
        result = test_function()
        
        logger.info(f"测试模块 {module_name} 运行完成")
        
        return {
            'module': module_name,
            'success': True,
            'result': result,
            'error': None
        }
    
    except Exception as e:
        logger.error(f"测试模块 {module_name} 运行失败: {str(e)}")
        logger.error(traceback.format_exc())
        
        return {
            'module': module_name,
            'success': False,
            'result': None,
            'error': str(e)
        }

def generate_test_report(results):
    """生成测试报告"""
    logger.info("生成测试报告")
    
    # 创建报告目录
    os.makedirs('test_reports', exist_ok=True)
    
    # 报告时间戳
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # 创建HTML报告
    report_path = f'test_reports/comprehensive_test_report_{timestamp}.html'
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(f'''
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>量化交易系统全面测试报告</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                h1, h2, h3 {{ color: #333; }}
                table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
                tr:nth-child(even) {{ background-color: #f9f9f9; }}
                .success {{ color: green; }}
                .failure {{ color: red; }}
                .warning {{ color: orange; }}
                .summary {{ background-color: #f0f0f0; padding: 10px; margin-bottom: 20px; }}
            </style>
        </head>
        <body>
            <h1>量化交易系统全面测试报告</h1>
            <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            
            <div class="summary">
                <h2>测试摘要</h2>
                <table>
                    <tr>
                        <th>测试类别</th>
                        <th>通过数</th>
                        <th>失败数</th>
                        <th>通过率</th>
                    </tr>
        ''')
        
        # 计算测试摘要
        script_tests = results['script_tests']
        module_tests = results['module_tests']
        
        script_passed = sum(1 for test in script_tests if test['success'])
        script_total = len(script_tests)
        script_pass_rate = script_passed / script_total * 100 if script_total > 0 else 0
        
        module_passed = sum(1 for test in module_tests if test['success'])
        module_total = len(module_tests)
        module_pass_rate = module_passed / module_total * 100 if module_total > 0 else 0
        
        total_passed = script_passed + module_passed
        total_tests = script_total + module_total
        total_pass_rate = total_passed / total_tests * 100 if total_tests > 0 else 0
        
        f.write(f'''
                    <tr>
                        <td>脚本测试</td>
                        <td>{script_passed}</td>
                        <td>{script_total - script_passed}</td>
                        <td>{script_pass_rate:.2f}%</td>
                    </tr>
                    <tr>
                        <td>模块测试</td>
                        <td>{module_passed}</td>
                        <td>{module_total - module_passed}</td>
                        <td>{module_pass_rate:.2f}%</td>
                    </tr>
                    <tr>
                        <td><strong>总计</strong></td>
                        <td><strong>{total_passed}</strong></td>
                        <td><strong>{total_tests - total_passed}</strong></td>
                        <td><strong>{total_pass_rate:.2f}%</strong></td>
                    </tr>
                </table>
            </div>
            
            <h2>脚本测试结果</h2>
            <table>
                <tr>
                    <th>脚本</th>
                    <th>状态</th>
                    <th>错误信息</th>
                </tr>
        ''')
        
        for test in script_tests:
            f.write(f'''
                <tr>
                    <td>{test['script']}</td>
                    <td class="{'success' if test['success'] else 'failure'}">{test['success']}</td>
                    <td>{test['error'] if test['error'] else ''}</td>
                </tr>
            ''')
        
        f.write('''
            </table>
            
            <h2>模块测试结果</h2>
            <table>
                <tr>
                    <th>模块</th>
                    <th>状态</th>
                    <th>结果摘要</th>
                    <th>错误信息</th>
                </tr>
        ''')
        
        for test in module_tests:
            result_summary = ''
            if test['success'] and test['result']:
                if isinstance(test['result'], dict):
                    # 提取关键信息
                    keys_to_show = ['success', 'success_rate', 'error']
                    result_summary = ', '.join(f"{k}: {v}" for k, v in test['result'].items() if k in keys_to_show and v is not None)
            
            f.write(f'''
                <tr>
                    <td>{test['module']}</td>
                    <td class="{'success' if test['success'] else 'failure'}">{test['success']}</td>
                    <td>{result_summary}</td>
                    <td>{test['error'] if test['error'] else ''}</td>
                </tr>
            ''')
        
        f.write('''
            </table>
            
            <h2>测试结论</h2>
        ''')
        
        if total_pass_rate >= 90:
            conclusion = "系统测试通过率高，整体质量良好。"
        elif total_pass_rate >= 70:
            conclusion = "系统测试通过率中等，需要修复部分问题。"
        else:
            conclusion = "系统测试通过率低，需要大量修复工作。"
        
        f.write(f'''
            <p>{conclusion}</p>
            
            <h2>测试详情</h2>
            <p>详细测试结果请查看 test_results 目录下的各个测试结果文件。</p>
            
        </body>
        </html>
        ''')
    
    logger.info(f"测试报告已生成: {report_path}")
    
    return report_path

def run_all_tests():
    """运行所有测试"""
    logger.info("开始运行所有全面测试")
    
    # 确保测试目录存在
    os.makedirs('test_results', exist_ok=True)
    os.makedirs('logs', exist_ok=True)
    os.makedirs('test_reports', exist_ok=True)
    
    # 定义要运行的测试脚本
    test_scripts = [
        'run_comprehensive_tests.py',
        'test_feature_engineering.py',
        'test_trading_environment.py',
        'test_model_training.py',
        'test_ui_components.py'
    ]
    
    # 定义要运行的测试模块
    test_modules = [
        'run_comprehensive_tests',
        'test_feature_engineering',
        'test_trading_environment',
        'test_model_training',
        'test_ui_components'
    ]
    
    # 运行测试脚本
    script_results = []
    
    for script in test_scripts:
        if os.path.exists(script):
            result = run_test_script(script)
            script_results.append(result)
        else:
            logger.warning(f"测试脚本 {script} 不存在，跳过")
            script_results.append({
                'script': script,
                'success': False,
                'error': '脚本不存在'
            })
    
    # 运行测试模块
    module_results = []
    
    for module in test_modules:
        try:
            result = run_test_module(module)
            module_results.append(result)
        except Exception as e:
            logger.error(f"运行测试模块 {module} 时出错: {str(e)}")
            module_results.append({
                'module': module,
                'success': False,
                'error': str(e)
            })
    
    # 汇总测试结果
    all_results = {
        'script_tests': script_results,
        'module_tests': module_results
    }
    
    # 生成测试报告
    report_path = generate_test_report(all_results)
    
    # 计算测试通过率
    script_passed = sum(1 for test in script_results if test['success'])
    script_total = len(script_results)
    script_pass_rate = script_passed / script_total * 100 if script_total > 0 else 0
    
    module_passed = sum(1 for test in module_results if test['success'])
    module_total = len(module_results)
    module_pass_rate = module_passed / module_total * 100 if module_total > 0 else 0
    
    total_passed = script_passed + module_passed
    total_tests = script_total + module_total
    total_pass_rate = total_passed / total_tests * 100 if total_tests > 0 else 0
    
    logger.info(f"所有测试完成，脚本测试通过率: {script_pass_rate:.2f}%, 模块测试通过率: {module_pass_rate:.2f}%, 总通过率: {total_pass_rate:.2f}%")
    logger.info(f"测试报告: {report_path}")
    
    return {
        'script_pass_rate': script_pass_rate,
        'module_pass_rate': module_pass_rate,
        'total_pass_rate': total_pass_rate,
        'report_path': report_path
    }

if __name__ == "__main__":
    run_all_tests()
