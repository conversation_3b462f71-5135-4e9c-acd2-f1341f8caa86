#!/usr/bin/env python3
"""
综合测试脚本 - 验证所有修复是否正常工作
测试数据获取、特征工程、回测等完整流程
"""

import sys
import os
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('comprehensive_test')

def test_data_acquisition_fix():
    """测试数据获取修复"""
    print("=" * 80)
    print("测试1: 数据获取日期过滤修复")
    print("=" * 80)
    
    try:
        from core_logic.data_handling.data_handler import DataHandler
        handler = DataHandler()
        
        # 测试不同的日期范围
        test_cases = [
            {
                'name': '2024年1月数据',
                'params': {
                    'stock_code': 'sh000001',
                    'start_date': '2024-01-01',
                    'end_date': '2024-01-31',
                    'frequency': '日线'
                }
            },
            {
                'name': '2024年2月数据',
                'params': {
                    'stock_code': 'sh000001',
                    'start_date': '2024-02-01',
                    'end_date': '2024-02-29',
                    'frequency': '日线'
                }
            }
        ]
        
        results = []
        for test_case in test_cases:
            print(f"\n测试: {test_case['name']}")
            data = handler.get_stock_data(**test_case['params'])
            
            if data is not None and not data.empty:
                actual_start = data.index.min().strftime('%Y-%m-%d')
                actual_end = data.index.max().strftime('%Y-%m-%d')
                expected_start = test_case['params']['start_date']
                expected_end = test_case['params']['end_date']
                
                print(f"  请求范围: {expected_start} 至 {expected_end}")
                print(f"  实际范围: {actual_start} 至 {actual_end}")
                print(f"  数据条数: {len(data)}")
                
                # 检查日期范围是否正确
                if actual_start >= expected_start and actual_end <= expected_end:
                    print(f"  ✅ 日期过滤正确")
                    results.append(True)
                else:
                    print(f"  ❌ 日期过滤错误")
                    results.append(False)
            else:
                print(f"  ❌ 获取数据失败")
                results.append(False)
        
        success_rate = sum(results) / len(results) * 100
        print(f"\n数据获取修复测试结果: {sum(results)}/{len(results)} 成功 ({success_rate:.1f}%)")
        return sum(results) == len(results)
        
    except Exception as e:
        print(f"❌ 数据获取测试失败: {str(e)}")
        return False

def test_backtesting_fix():
    """测试回测修复"""
    print("\n" + "=" * 80)
    print("测试2: 回测引擎Index levels修复")
    print("=" * 80)
    
    try:
        from core_logic.backtest.backtest_engine import BacktestEngine
        from core_logic.data_handling.data_handler import DataHandler
        
        # 获取真实数据进行回测
        handler = DataHandler()
        data = handler.get_stock_data(
            stock_code='sh000001',
            start_date='2024-01-01',
            end_date='2024-01-31',
            frequency='日线'
        )
        
        if data is None or data.empty:
            print("❌ 无法获取测试数据")
            return False
        
        print(f"✅ 获取测试数据: {data.shape}")
        print(f"   数据列: {list(data.columns)}")
        print(f"   索引类型: {type(data.index)}")
        
        # 创建回测引擎
        backtest_engine = BacktestEngine(initial_capital=100000)
        print("✅ 创建回测引擎成功")
        
        # 创建简单策略
        class SimpleStrategy:
            def predict_action(self, state):
                # 简单的买入持有策略
                return 0.5  # 50%仓位
        
        strategy = SimpleStrategy()
        print("✅ 创建测试策略")
        
        # 运行回测
        print("开始回测...")
        results = backtest_engine.run_backtest(data=data, strategy=strategy)
        
        if results and results.get('status') != 'error':
            print("✅ 回测执行成功")
            
            # 检查结果
            if 'portfolio_values' in results:
                portfolio_values = results['portfolio_values']
                print(f"   组合价值序列长度: {len(portfolio_values)}")
                print(f"   初始价值: {portfolio_values.iloc[0]:.2f}")
                print(f"   最终价值: {portfolio_values.iloc[-1]:.2f}")
                
            if 'trades' in results:
                trades = results['trades']
                print(f"   交易记录数量: {len(trades)}")
            
            return True
        else:
            error_msg = results.get('message', '未知错误') if results else '回测返回空结果'
            print(f"❌ 回测失败: {error_msg}")
            return False
            
    except Exception as e:
        print(f"❌ 回测测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_feature_engineering():
    """测试特征工程"""
    print("\n" + "=" * 80)
    print("测试3: 特征工程")
    print("=" * 80)
    
    try:
        from core_logic.data_handling.data_handler import DataHandler
        from core_logic.feature_engineering.adapter import FeatureEngineeringAdapter
        
        # 获取数据
        handler = DataHandler()
        data = handler.get_stock_data(
            stock_code='sh000001',
            start_date='2024-01-01',
            end_date='2024-01-31',
            frequency='日线'
        )
        
        if data is None or data.empty:
            print("❌ 无法获取测试数据")
            return False
        
        print(f"✅ 获取原始数据: {data.shape}")
        
        # 特征工程
        fe_adapter = FeatureEngineeringAdapter()
        features = fe_adapter.engineer_features(data)
        
        if features is not None and not features.empty:
            print(f"✅ 特征工程成功: {features.shape}")
            print(f"   原始特征: {data.shape[1]} 个")
            print(f"   工程特征: {features.shape[1]} 个")
            
            # 检查缺失值
            missing_count = features.isnull().sum().sum()
            print(f"   缺失值数量: {missing_count}")
            
            if missing_count == 0:
                print("   ✅ 无缺失值")
                return True
            else:
                print("   ⚠️  存在缺失值")
                return True  # 仍然算成功，只是有警告
        else:
            print("❌ 特征工程失败")
            return False
            
    except Exception as e:
        print(f"❌ 特征工程测试失败: {str(e)}")
        return False

def test_end_to_end_workflow():
    """测试端到端工作流程"""
    print("\n" + "=" * 80)
    print("测试4: 端到端工作流程")
    print("=" * 80)
    
    try:
        # 1. 数据获取
        print("步骤1: 数据获取")
        from core_logic.data_handling.data_handler import DataHandler
        handler = DataHandler()
        
        data = handler.get_stock_data(
            stock_code='sh000001',
            start_date='2024-01-01',
            end_date='2024-01-31',
            frequency='日线'
        )
        
        if data is None or data.empty:
            print("❌ 数据获取失败")
            return False
        print(f"✅ 数据获取成功: {data.shape}")
        
        # 2. 特征工程
        print("\n步骤2: 特征工程")
        from core_logic.feature_engineering.adapter import FeatureEngineeringAdapter
        fe_adapter = FeatureEngineeringAdapter()
        features = fe_adapter.engineer_features(data)
        
        if features is None or features.empty:
            print("❌ 特征工程失败")
            return False
        print(f"✅ 特征工程成功: {features.shape}")
        
        # 3. 回测
        print("\n步骤3: 回测")
        from core_logic.backtest.backtest_engine import BacktestEngine
        
        class TestStrategy:
            def predict_action(self, state):
                return 0.3  # 30%仓位
        
        backtest_engine = BacktestEngine(initial_capital=100000)
        strategy = TestStrategy()
        
        results = backtest_engine.run_backtest(data=data, strategy=strategy)
        
        if results and results.get('status') != 'error':
            print("✅ 回测成功")
            
            # 获取性能摘要
            performance = backtest_engine.get_performance_summary()
            if 'total_return' in performance:
                print(f"   总收益率: {performance['total_return']:.2%}")
                print(f"   最大回撤: {performance['max_drawdown']:.2%}")
                print(f"   夏普比率: {performance['sharpe_ratio']:.2f}")
            
            print("✅ 端到端工作流程测试成功")
            return True
        else:
            print("❌ 回测失败")
            return False
            
    except Exception as e:
        print(f"❌ 端到端测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始综合修复验证测试...")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行所有测试
    test_results = []
    
    test_results.append(test_data_acquisition_fix())
    test_results.append(test_backtesting_fix())
    test_results.append(test_feature_engineering())
    test_results.append(test_end_to_end_workflow())
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("综合测试结果汇总")
    print("=" * 80)
    
    test_names = [
        "数据获取日期过滤修复",
        "回测引擎Index levels修复", 
        "特征工程",
        "端到端工作流程"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, test_results), 1):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i}. {name}: {status}")
    
    success_count = sum(test_results)
    total_count = len(test_results)
    success_rate = success_count / total_count * 100
    
    print(f"\n总体结果: {success_count}/{total_count} 测试通过 ({success_rate:.1f}%)")
    
    if success_count == total_count:
        print("🎉 所有关键修复验证成功！项目已准备就绪。")
    else:
        print("⚠️  部分测试失败，需要进一步调试。")
    
    return success_count == total_count

if __name__ == "__main__":
    main()
