"""
回测报告生成模块
提供生成回测报告的功能
"""

import os
import logging
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Union, Tuple, Optional, Any
from datetime import datetime
import json
import base64
from io import BytesIO

class ReportGenerator:
    """
    回测报告生成类
    提供生成回测报告的功能
    """

    def __init__(self, output_dir: str = 'reports'):
        """
        初始化报告生成器

        参数:
            output_dir (str): 报告输出目录
        """
        self.logger = logging.getLogger('drl_trading')
        self.output_dir = output_dir
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 导入可视化模块
        from quant_project.core_logic.backtest.visualization import BacktestVisualizer
        self.visualizer = BacktestVisualizer()
        
        self.logger.info(f"初始化回测报告生成器: 输出目录={output_dir}")

    def generate_report(self,
                      portfolio_values: pd.Series,
                      trades: Optional[Union[pd.DataFrame, List[Dict]]] = None,
                      benchmark_values: Optional[pd.Series] = None,
                      metrics: Optional[Dict[str, Any]] = None,
                      strategy_params: Optional[Dict[str, Any]] = None,
                      report_title: str = "回测报告",
                      report_format: str = "html") -> str:
        """
        生成回测报告

        参数:
            portfolio_values (pandas.Series): 组合价值序列，索引为日期
            trades (list or pandas.DataFrame, optional): 交易记录
            benchmark_values (pandas.Series, optional): 基准价值序列，索引为日期
            metrics (dict, optional): 性能指标字典，如果为None则自动计算
            strategy_params (dict, optional): 策略参数字典
            report_title (str): 报告标题
            report_format (str): 报告格式，支持 'html', 'json', 'markdown'

        返回:
            str: 报告文件路径
        """
        # 验证输入
        if not isinstance(portfolio_values, pd.Series) or len(portfolio_values) < 2:
            self.logger.error("组合价值序列无效或数据不足")
            return ""
            
        # 如果没有提供指标，计算指标
        if metrics is None:
            from quant_project.core_logic.backtest.performance_metrics import PerformanceMetrics
            metrics_calculator = PerformanceMetrics()
            metrics = metrics_calculator.calculate_metrics(portfolio_values, trades, benchmark_values)
        
        # 转换交易记录为DataFrame
        if trades is not None and not isinstance(trades, pd.DataFrame):
            try:
                trades = pd.DataFrame(trades)
            except Exception as e:
                self.logger.warning(f"无法将交易记录转换为DataFrame: {str(e)}")
                trades = None
        
        # 根据格式生成报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"backtest_report_{timestamp}"
        
        if report_format.lower() == "html":
            return self._generate_html_report(filename, portfolio_values, trades, benchmark_values, 
                                           metrics, strategy_params, report_title)
        elif report_format.lower() == "json":
            return self._generate_json_report(filename, portfolio_values, trades, benchmark_values,
                                           metrics, strategy_params, report_title)
        elif report_format.lower() == "markdown":
            return self._generate_markdown_report(filename, portfolio_values, trades, benchmark_values,
                                               metrics, strategy_params, report_title)
        else:
            self.logger.error(f"不支持的报告格式: {report_format}")
            return ""

    def _generate_html_report(self, filename: str, portfolio_values: pd.Series,
                           trades: Optional[pd.DataFrame], benchmark_values: Optional[pd.Series],
                           metrics: Dict[str, Any], strategy_params: Optional[Dict[str, Any]],
                           report_title: str) -> str:
        """
        生成HTML格式的回测报告

        参数:
            filename (str): 文件名（不含扩展名）
            其他参数同generate_report

        返回:
            str: 报告文件路径
        """
        # 生成图表并保存为base64编码的字符串
        chart_images = self._generate_chart_images(portfolio_values, trades, benchmark_values)
        
        # 生成HTML内容
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{report_title}</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; color: #333; }}
                .container {{ max-width: 1200px; margin: 0 auto; }}
                h1, h2, h3 {{ color: #2c3e50; }}
                table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; }}
                th, td {{ text-align: left; padding: 12px; }}
                th {{ background-color: #2c3e50; color: white; }}
                tr:nth-child(even) {{ background-color: #f2f2f2; }}
                .chart-container {{ margin-bottom: 30px; }}
                .chart {{ max-width: 100%; height: auto; }}
                .metrics-container {{ display: flex; flex-wrap: wrap; }}
                .metric-box {{ width: 30%; margin: 1%; padding: 10px; border-radius: 5px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }}
                .metric-value {{ font-size: 24px; font-weight: bold; margin: 10px 0; }}
                .positive {{ color: green; }}
                .negative {{ color: red; }}
                .section {{ margin-bottom: 30px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>{report_title}</h1>
                <p>生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
                
                <!-- 性能指标摘要 -->
                <div class="section">
                    <h2>性能指标摘要</h2>
                    <div class="metrics-container">
        """
        
        # 添加关键指标
        key_metrics = [
            ('总收益率', metrics.get('total_return', 0), '{:.2%}', 
             lambda x: 'positive' if x > 0 else 'negative'),
            ('年化收益率', metrics.get('annual_return', 0), '{:.2%}', 
             lambda x: 'positive' if x > 0 else 'negative'),
            ('最大回撤', metrics.get('max_drawdown', 0), '{:.2%}', 
             lambda x: 'negative'),
            ('夏普比率', metrics.get('sharpe_ratio', 0), '{:.2f}', 
             lambda x: 'positive' if x > 0 else 'negative'),
            ('索提诺比率', metrics.get('sortino_ratio', 0), '{:.2f}', 
             lambda x: 'positive' if x > 0 else 'negative'),
            ('交易次数', metrics.get('total_trades', 0), '{}', 
             lambda x: ''),
        ]
        
        for name, value, fmt, class_func in key_metrics:
            formatted_value = fmt.format(value)
            class_name = class_func(value)
            html_content += f"""
                        <div class="metric-box">
                            <h3>{name}</h3>
                            <div class="metric-value {class_name}">{formatted_value}</div>
                        </div>
            """
        
        html_content += """
                    </div>
                </div>
                
                <!-- 回测图表 -->
                <div class="section">
                    <h2>回测图表</h2>
        """
        
        # 添加图表
        for chart_title, img_data in chart_images.items():
            html_content += f"""
                    <div class="chart-container">
                        <h3>{chart_title}</h3>
                        <img class="chart" src="data:image/png;base64,{img_data}" alt="{chart_title}">
                    </div>
            """
        
        html_content += """
                </div>
                
                <!-- 详细指标 -->
                <div class="section">
                    <h2>详细性能指标</h2>
                    <table>
                        <tr>
                            <th>指标</th>
                            <th>值</th>
                        </tr>
        """
        
        # 添加所有指标
        for key, value in sorted(metrics.items()):
            # 跳过复杂对象和序列
            if isinstance(value, (dict, list, pd.Series, pd.DataFrame)):
                continue
                
            formatted_value = value
            if isinstance(value, float):
                if 'return' in key or 'drawdown' in key or 'rate' in key:
                    formatted_value = f"{value:.2%}"
                else:
                    formatted_value = f"{value:.4f}"
                    
            html_content += f"""
                        <tr>
                            <td>{key}</td>
                            <td>{formatted_value}</td>
                        </tr>
            """
            
        html_content += """
                    </table>
                </div>
        """
        
        # 添加策略参数（如果有）
        if strategy_params:
            html_content += """
                <!-- 策略参数 -->
                <div class="section">
                    <h2>策略参数</h2>
                    <table>
                        <tr>
                            <th>参数</th>
                            <th>值</th>
                        </tr>
            """
            
            for key, value in sorted(strategy_params.items()):
                html_content += f"""
                        <tr>
                            <td>{key}</td>
                            <td>{value}</td>
                        </tr>
                """
                
            html_content += """
                    </table>
                </div>
            """
            
        # 添加交易记录（如果有）
        if trades is not None and not trades.empty:
            html_content += """
                <!-- 交易记录 -->
                <div class="section">
                    <h2>交易记录</h2>
                    <table>
                        <tr>
            """
            
            # 添加表头
            for col in trades.columns:
                html_content += f"<th>{col}</th>"
                
            html_content += """
                        </tr>
            """
            
            # 添加交易记录（最多显示100条）
            for _, row in trades.head(100).iterrows():
                html_content += "<tr>"
                for col in trades.columns:
                    value = row[col]
                    if isinstance(value, float):
                        value = f"{value:.4f}"
                    elif isinstance(value, pd.Timestamp):
                        value = value.strftime("%Y-%m-%d")
                    html_content += f"<td>{value}</td>"
                html_content += "</tr>"
                
            if len(trades) > 100:
                html_content += f"""
                        <tr>
                            <td colspan="{len(trades.columns)}">显示前100条交易，共{len(trades)}条</td>
                        </tr>
                """
                
            html_content += """
                    </table>
                </div>
            """
            
        # 关闭HTML
        html_content += """
            </div>
        </body>
        </html>
        """
        
        # 保存HTML文件
        file_path = os.path.join(self.output_dir, f"{filename}.html")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
            
        self.logger.info(f"HTML报告已生成: {file_path}")
        return file_path

    def _generate_json_report(self, filename: str, portfolio_values: pd.Series,
                           trades: Optional[pd.DataFrame], benchmark_values: Optional[pd.Series],
                           metrics: Dict[str, Any], strategy_params: Optional[Dict[str, Any]],
                           report_title: str) -> str:
        """
        生成JSON格式的回测报告

        参数:
            filename (str): 文件名（不含扩展名）
            其他参数同generate_report

        返回:
            str: 报告文件路径
        """
        # 准备JSON数据
        report_data = {
            'title': report_title,
            'generated_at': datetime.now().isoformat(),
            'metrics': self._convert_to_json_serializable(metrics),
            'strategy_params': strategy_params
        }
        
        # 添加关键价值序列的摘要（不包含完整序列）
        report_data['portfolio_summary'] = {
            'start_date': portfolio_values.index[0].isoformat(),
            'end_date': portfolio_values.index[-1].isoformat(),
            'initial_value': float(portfolio_values.iloc[0]),
            'final_value': float(portfolio_values.iloc[-1]),
            'data_points': len(portfolio_values)
        }
        
        # 添加交易记录摘要
        if trades is not None and not trades.empty:
            report_data['trades_summary'] = {
                'total_trades': len(trades),
                'first_trade_date': trades['date'].min().isoformat() if 'date' in trades.columns else None,
                'last_trade_date': trades['date'].max().isoformat() if 'date' in trades.columns else None
            }
        
        # 保存JSON文件
        file_path = os.path.join(self.output_dir, f"{filename}.json")
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=4)
            
        self.logger.info(f"JSON报告已生成: {file_path}")
        return file_path

    def _generate_markdown_report(self, filename: str, portfolio_values: pd.Series,
                               trades: Optional[pd.DataFrame], benchmark_values: Optional[pd.Series],
                               metrics: Dict[str, Any], strategy_params: Optional[Dict[str, Any]],
                               report_title: str) -> str:
        """
        生成Markdown格式的回测报告

        参数:
            filename (str): 文件名（不含扩展名）
            其他参数同generate_report

        返回:
            str: 报告文件路径
        """
        # 生成Markdown内容
        md_content = f"# {report_title}\n\n"
        md_content += f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        # 添加性能指标摘要
        md_content += "## 性能指标摘要\n\n"
        md_content += "| 指标 | 值 |\n"
        md_content += "| --- | --- |\n"
        
        key_metrics = [
            ('总收益率', metrics.get('total_return', 0), '{:.2%}'),
            ('年化收益率', metrics.get('annual_return', 0), '{:.2%}'),
            ('最大回撤', metrics.get('max_drawdown', 0), '{:.2%}'),
            ('夏普比率', metrics.get('sharpe_ratio', 0), '{:.2f}'),
            ('索提诺比率', metrics.get('sortino_ratio', 0), '{:.2f}'),
            ('交易次数', metrics.get('total_trades', 0), '{}'),
        ]
        
        for name, value, fmt in key_metrics:
            formatted_value = fmt.format(value)
            md_content += f"| {name} | {formatted_value} |\n"
            
        md_content += "\n"
        
        # 添加详细指标
        md_content += "## 详细性能指标\n\n"
        md_content += "| 指标 | 值 |\n"
        md_content += "| --- | --- |\n"
        
        for key, value in sorted(metrics.items()):
            # 跳过复杂对象和序列
            if isinstance(value, (dict, list, pd.Series, pd.DataFrame)):
                continue
                
            formatted_value = value
            if isinstance(value, float):
                if 'return' in key or 'drawdown' in key or 'rate' in key:
                    formatted_value = f"{value:.2%}"
                else:
                    formatted_value = f"{value:.4f}"
                    
            md_content += f"| {key} | {formatted_value} |\n"
            
        md_content += "\n"
        
        # 添加策略参数（如果有）
        if strategy_params:
            md_content += "## 策略参数\n\n"
            md_content += "| 参数 | 值 |\n"
            md_content += "| --- | --- |\n"
            
            for key, value in sorted(strategy_params.items()):
                md_content += f"| {key} | {value} |\n"
                
            md_content += "\n"
        
        # 添加交易记录摘要（如果有）
        if trades is not None and not trades.empty:
            md_content += "## 交易记录摘要\n\n"
            md_content += f"- 总交易次数: {len(trades)}\n"
            if 'date' in trades.columns:
                md_content += f"- 首次交易日期: {trades['date'].min()}\n"
                md_content += f"- 最后交易日期: {trades['date'].max()}\n"
            if 'type' in trades.columns:
                buy_count = len(trades[trades['type'] == 'buy'])
                sell_count = len(trades[trades['type'] == 'sell'])
                md_content += f"- 买入交易次数: {buy_count}\n"
                md_content += f"- 卖出交易次数: {sell_count}\n"
            
            md_content += "\n"
        
        # 保存Markdown文件
        file_path = os.path.join(self.output_dir, f"{filename}.md")
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(md_content)
            
        self.logger.info(f"Markdown报告已生成: {file_path}")
        return file_path

    def _generate_chart_images(self, portfolio_values: pd.Series,
                             trades: Optional[pd.DataFrame],
                             benchmark_values: Optional[pd.Series]) -> Dict[str, str]:
        """
        生成回测图表并转换为base64编码的字符串

        参数:
            portfolio_values (pandas.Series): 组合价值序列
            trades (pandas.DataFrame, optional): 交易记录
            benchmark_values (pandas.Series, optional): 基准价值序列

        返回:
            dict: 图表标题到base64编码图像的映射
        """
        chart_images = {}
        
        # 1. 组合表现图
        try:
            fig = self.visualizer.plot_portfolio_performance(
                portfolio_values, 
                benchmark_values=benchmark_values,
                title='策略表现'
            )
            chart_images['策略表现'] = self._fig_to_base64(fig)
            plt.close(fig)
        except Exception as e:
            self.logger.error(f"生成策略表现图失败: {str(e)}")
        
        # 2. 收益分布图
        try:
            fig = self.visualizer.plot_returns_distribution(
                portfolio_values,
                benchmark_values=benchmark_values,
                title='收益分布'
            )
            chart_images['收益分布'] = self._fig_to_base64(fig)
            plt.close(fig)
        except Exception as e:
            self.logger.error(f"生成收益分布图失败: {str(e)}")
        
        # 3. 月度收益热图
        try:
            fig = self.visualizer.plot_monthly_returns_heatmap(
                portfolio_values,
                title='月度收益热图'
            )
            chart_images['月度收益热图'] = self._fig_to_base64(fig)
            plt.close(fig)
        except Exception as e:
            self.logger.error(f"生成月度收益热图失败: {str(e)}")
        
        # 4. 交易分析图（如果有交易记录）
        if trades is not None and not trades.empty:
            try:
                fig = self.visualizer.plot_trade_analysis(
                    trades,
                    title='交易分析'
                )
                chart_images['交易分析'] = self._fig_to_base64(fig)
                plt.close(fig)
            except Exception as e:
                self.logger.error(f"生成交易分析图失败: {str(e)}")
        
        return chart_images

    def _fig_to_base64(self, fig: plt.Figure) -> str:
        """
        将Matplotlib图表转换为base64编码的字符串

        参数:
            fig (matplotlib.figure.Figure): Matplotlib图表

        返回:
            str: base64编码的图像字符串
        """
        buf = BytesIO()
        fig.savefig(buf, format='png', dpi=100, bbox_inches='tight')
        buf.seek(0)
        img_base64 = base64.b64encode(buf.getvalue()).decode('utf-8')
        buf.close()
        return img_base64

    def _convert_to_json_serializable(self, obj: Any) -> Any:
        """
        将对象转换为JSON可序列化的对象

        参数:
            obj: 任意对象

        返回:
            转换后的对象
        """
        if isinstance(obj, (pd.Series, pd.DataFrame)):
            return None  # 排除大型序列数据
        elif isinstance(obj, (np.ndarray, np.number)):
            return obj.tolist() if hasattr(obj, 'tolist') else float(obj)
        elif isinstance(obj, (datetime, pd.Timestamp)):
            return obj.isoformat()
        elif isinstance(obj, dict):
            return {k: self._convert_to_json_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_to_json_serializable(item) for item in obj]
        elif np.isnan(obj) if isinstance(obj, float) else False:
            return None
        return obj 