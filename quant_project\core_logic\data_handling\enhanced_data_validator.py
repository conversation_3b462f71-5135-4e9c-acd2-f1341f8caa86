"""
增强数据验证模块
实现更全面的数据质量验证和异常检测
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from scipy import stats
from typing import Dict, List, Optional, Tuple, Union
import matplotlib.pyplot as plt
import seaborn as sns
from statsmodels.tsa.stattools import adfuller, kpss
import warnings

# 忽略特定警告
warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", category=RuntimeWarning)

class EnhancedDataValidator:
    """
    增强数据验证类
    实现更全面的数据质量验证和异常检测
    """

    def __init__(self, logger=None):
        """
        初始化数据验证器

        参数:
            logger (logging.Logger): 日志记录器
        """
        self.logger = logger or logging.getLogger('drl_trading')

    def validate_data(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        验证数据

        参数:
            data (pandas.DataFrame): 金融数据

        返回:
            tuple: (bool, str) - 验证结果和错误信息
        """
        if data is None or data.empty:
            return False, "数据为空"

        # 验证数据结构
        structure_valid, structure_error = self._validate_structure(data)
        if not structure_valid:
            return False, structure_error

        # 验证数据完整性
        completeness_valid, completeness_error = self._validate_completeness(data)
        if not completeness_valid:
            return False, completeness_error

        # 验证数据有效性
        validity_valid, validity_error = self._validate_validity(data)
        if not validity_valid:
            return False, validity_error

        # 验证时间序列
        time_series_valid, time_series_error = self._validate_time_series(data)
        if not time_series_valid:
            return False, time_series_error

        # 验证数据分布
        distribution_valid, distribution_error = self._validate_distribution(data)
        if not distribution_valid:
            self.logger.warning(distribution_error)
            # 不返回错误，只是警告

        # 验证数据平稳性
        stationarity_valid, stationarity_error = self._validate_stationarity(data)
        if not stationarity_valid:
            self.logger.warning(stationarity_error)
            # 不返回错误，只是警告

        return True, "数据验证通过"

    def _validate_structure(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        验证数据结构

        参数:
            data (pandas.DataFrame): 金融数据

        返回:
            tuple: (bool, str) - 验证结果和错误信息
        """
        # 检查必要的列是否存在
        required_columns = ['开盘', '最高', '最低', '收盘', '成交量']
        missing_columns = [col for col in required_columns if col not in data.columns]
        
        if missing_columns:
            error_msg = f"数据缺少必要的列: {missing_columns}"
            self.logger.error(error_msg)
            return False, error_msg
        
        # 检查索引是否是日期类型
        if not isinstance(data.index, pd.DatetimeIndex):
            error_msg = "数据索引不是日期类型"
            self.logger.error(error_msg)
            return False, error_msg
        
        # 检查数据类型
        for col in required_columns:
            if not pd.api.types.is_numeric_dtype(data[col]):
                error_msg = f"列 {col} 不是数值类型"
                self.logger.error(error_msg)
                return False, error_msg
        
        return True, "数据结构验证通过"

    def _validate_completeness(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        验证数据完整性

        参数:
            data (pandas.DataFrame): 金融数据

        返回:
            tuple: (bool, str) - 验证结果和错误信息
        """
        # 检查是否有缺失值
        missing_values = data.isnull().sum()
        total_missing = missing_values.sum()
        
        if total_missing > 0:
            missing_ratio = total_missing / (data.shape[0] * data.shape[1])
            if missing_ratio > 0.1:  # 如果缺失值比例超过10%
                error_msg = f"数据中有过多缺失值，缺失比例: {missing_ratio:.2%}"
                self.logger.error(error_msg)
                return False, error_msg
            else:
                warning_msg = f"数据中有 {total_missing} 个缺失值，按列分布: {missing_values[missing_values > 0]}"
                self.logger.warning(warning_msg)
                # 不返回错误，因为缺失值可以在清洗阶段处理
        
        # 检查数据长度是否足够
        if len(data) < 60:  # 至少需要60个数据点（约3个月的交易日）
            error_msg = f"数据长度不足，只有 {len(data)} 个数据点，至少需要60个"
            self.logger.error(error_msg)
            return False, error_msg
        
        # 检查是否有连续的缺失值
        for col in ['开盘', '最高', '最低', '收盘', '成交量']:
            # 计算连续缺失值的最大长度
            max_consecutive_missing = self._get_max_consecutive_missing(data[col])
            if max_consecutive_missing > 5:  # 如果连续缺失超过5个
                error_msg = f"列 {col} 中存在 {max_consecutive_missing} 个连续缺失值"
                self.logger.error(error_msg)
                return False, error_msg
        
        return True, "数据完整性验证通过"

    def _validate_validity(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        验证数据有效性

        参数:
            data (pandas.DataFrame): 金融数据

        返回:
            tuple: (bool, str) - 验证结果和错误信息
        """
        # 检查价格是否为正
        price_columns = ['开盘', '最高', '最低', '收盘']
        for col in price_columns:
            if (data[col] <= 0).any():
                error_msg = f"列 {col} 中存在非正值"
                self.logger.error(error_msg)
                return False, error_msg
        
        # 检查最高价是否大于等于最低价
        if (data['最高'] < data['最低']).any():
            error_msg = "存在最高价小于最低价的记录"
            self.logger.error(error_msg)
            return False, error_msg
        
        # 检查开盘价和收盘价是否在最高价和最低价之间
        invalid_open = (data['开盘'] > data['最高']) | (data['开盘'] < data['最低'])
        invalid_close = (data['收盘'] > data['最高']) | (data['收盘'] < data['最低'])
        
        if invalid_open.any():
            invalid_count = invalid_open.sum()
            error_msg = f"存在 {invalid_count} 条开盘价不在最高价和最低价之间的记录"
            self.logger.error(error_msg)
            return False, error_msg
        
        if invalid_close.any():
            invalid_count = invalid_close.sum()
            error_msg = f"存在 {invalid_count} 条收盘价不在最高价和最低价之间的记录"
            self.logger.error(error_msg)
            return False, error_msg
        
        # 检查成交量是否为正
        if (data['成交量'] < 0).any():
            error_msg = "存在成交量为负的记录"
            self.logger.error(error_msg)
            return False, error_msg
        
        # 检查是否有异常大的价格变化
        returns = data['收盘'].pct_change()
        # 计算3倍标准差作为异常阈值
        std = returns.std()
        threshold = 3 * std
        extreme_returns = returns[abs(returns) > threshold].dropna()
        
        if len(extreme_returns) > 0:
            warning_msg = f"存在 {len(extreme_returns)} 条异常大的价格变化，超过3倍标准差 ({threshold:.2%})"
            self.logger.warning(warning_msg)
            # 不返回错误，只是警告
        
        return True, "数据有效性验证通过"

    def _validate_time_series(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        验证时间序列

        参数:
            data (pandas.DataFrame): 金融数据

        返回:
            tuple: (bool, str) - 验证结果和错误信息
        """
        # 检查索引是否有重复
        if data.index.duplicated().any():
            error_msg = "数据索引存在重复"
            self.logger.error(error_msg)
            return False, error_msg
        
        # 检查索引是否按时间排序
        if not data.index.is_monotonic_increasing:
            error_msg = "数据索引未按时间排序"
            self.logger.warning(error_msg)
            # 不返回错误，因为可以在清洗阶段排序
        
        # 检查时间间隔是否一致（对于日线数据）
        # 计算相邻日期之间的间隔
        date_diffs = data.index.to_series().diff().dropna()
        
        # 检查是否有异常大的间隔（例如超过5天）
        max_expected_diff = pd.Timedelta(days=5)
        large_gaps = date_diffs[date_diffs > max_expected_diff]
        
        if not large_gaps.empty:
            warning_msg = f"数据中存在 {len(large_gaps)} 个异常大的时间间隔，最大间隔为 {large_gaps.max()}"
            self.logger.warning(warning_msg)
            # 不返回错误，只是警告
        
        # 检查是否有重复的日期（考虑到时间部分可能不同）
        date_only = data.index.date
        if len(date_only) != len(set(date_only)):
            warning_msg = "数据中存在同一天的多个记录"
            self.logger.warning(warning_msg)
            # 不返回错误，只是警告
        
        return True, "时间序列验证通过"

    def _validate_distribution(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        验证数据分布

        参数:
            data (pandas.DataFrame): 金融数据

        返回:
            tuple: (bool, str) - 验证结果和错误信息
        """
        # 计算收益率
        returns = data['收盘'].pct_change().dropna()
        
        # 检查是否有足够的数据点
        if len(returns) < 30:
            return False, "数据点不足，无法验证分布"
        
        # 计算偏度和峰度
        skew = stats.skew(returns)
        kurt = stats.kurtosis(returns)
        
        # 检查是否存在极端偏度或峰度
        if abs(skew) > 3:
            warning_msg = f"收益率分布存在极端偏度: {skew:.2f}"
            self.logger.warning(warning_msg)
            return False, warning_msg
        
        if kurt > 10:
            warning_msg = f"收益率分布存在极端峰度: {kurt:.2f}"
            self.logger.warning(warning_msg)
            return False, warning_msg
        
        # 进行正态性检验
        _, p_value = stats.normaltest(returns)
        if p_value < 0.05:
            warning_msg = f"收益率分布不符合正态分布 (p值: {p_value:.4f})"
            self.logger.warning(warning_msg)
            # 不返回错误，因为金融时间序列通常不符合正态分布
        
        return True, "数据分布验证通过"

    def _validate_stationarity(self, data: pd.DataFrame) -> Tuple[bool, str]:
        """
        验证数据平稳性

        参数:
            data (pandas.DataFrame): 金融数据

        返回:
            tuple: (bool, str) - 验证结果和错误信息
        """
        # 计算收益率
        returns = data['收盘'].pct_change().dropna()
        
        # 检查是否有足够的数据点
        if len(returns) < 30:
            return False, "数据点不足，无法验证平稳性"
        
        try:
            # 进行ADF检验
            adf_result = adfuller(returns)
            adf_p_value = adf_result[1]
            
            # 进行KPSS检验
            kpss_result = kpss(returns)
            kpss_p_value = kpss_result[1]
            
            # 检查平稳性
            if adf_p_value > 0.05:
                warning_msg = f"ADF检验表明收益率序列可能不平稳 (p值: {adf_p_value:.4f})"
                self.logger.warning(warning_msg)
                return False, warning_msg
            
            if kpss_p_value < 0.05:
                warning_msg = f"KPSS检验表明收益率序列可能不平稳 (p值: {kpss_p_value:.4f})"
                self.logger.warning(warning_msg)
                return False, warning_msg
            
        except Exception as e:
            warning_msg = f"平稳性检验失败: {str(e)}"
            self.logger.warning(warning_msg)
            return False, warning_msg
        
        return True, "数据平稳性验证通过"

    def check_data_quality(self, data: pd.DataFrame) -> Dict:
        """
        检查数据质量并返回质量评分

        参数:
            data (pandas.DataFrame): 金融数据

        返回:
            dict: 数据质量评分和详细信息
        """
        quality_score = {
            'completeness': 0,  # 完整性
            'validity': 0,      # 有效性
            'consistency': 0,   # 一致性
            'timeliness': 0,    # 时效性
            'distribution': 0,  # 分布特性
            'stationarity': 0,  # 平稳性
            'overall': 0        # 总体评分
        }
        
        # 评估完整性 (0-100)
        missing_ratio = data.isnull().sum().sum() / (data.shape[0] * data.shape[1])
        quality_score['completeness'] = int(100 * (1 - missing_ratio))
        
        # 评估有效性 (0-100)
        # 检查价格和成交量是否合理
        price_columns = ['开盘', '最高', '最低', '收盘']
        price_validity = 0
        for col in price_columns:
            if col in data.columns:
                # 价格应该为正
                valid_ratio = (data[col] > 0).mean()
                price_validity += valid_ratio
        
        price_validity = price_validity / len(price_columns) if price_columns else 0
        volume_validity = (data['成交量'] >= 0).mean() if '成交量' in data.columns else 0
        
        quality_score['validity'] = int(100 * (price_validity * 0.7 + volume_validity * 0.3))
        
        # 评估一致性 (0-100)
        # 检查高低价关系是否一致
        high_low_consistent = ((data['最高'] >= data['最低']).mean() if '最高' in data.columns and '最低' in data.columns else 0)
        open_range_consistent = ((data['开盘'] <= data['最高']) & (data['开盘'] >= data['最低'])).mean() if '开盘' in data.columns else 0
        close_range_consistent = ((data['收盘'] <= data['最高']) & (data['收盘'] >= data['最低'])).mean() if '收盘' in data.columns else 0
        
        quality_score['consistency'] = int(100 * (high_low_consistent * 0.4 + open_range_consistent * 0.3 + close_range_consistent * 0.3))
        
        # 评估时效性 (0-100)
        # 检查数据的最新日期
        if isinstance(data.index, pd.DatetimeIndex) and len(data) > 0:
            latest_date = data.index.max()
            days_old = (datetime.now() - latest_date).days
            # 如果数据不超过30天，则时效性为100，否则按比例降低
            quality_score['timeliness'] = max(0, int(100 * (1 - days_old / 30))) if days_old <= 30 else 0
        else:
            quality_score['timeliness'] = 0
        
        # 评估分布特性 (0-100)
        try:
            returns = data['收盘'].pct_change().dropna()
            skew = abs(stats.skew(returns))
            kurt = stats.kurtosis(returns)
            
            # 偏度和峰度评分
            skew_score = max(0, 100 - skew * 20)  # 偏度越小越好
            kurt_score = max(0, 100 - max(0, kurt - 3) * 10)  # 峰度接近3最好
            
            quality_score['distribution'] = int((skew_score + kurt_score) / 2)
        except Exception:
            quality_score['distribution'] = 50  # 默认中等分数
        
        # 评估平稳性 (0-100)
        try:
            returns = data['收盘'].pct_change().dropna()
            
            # ADF检验
            adf_result = adfuller(returns)
            adf_p_value = adf_result[1]
            
            # KPSS检验
            kpss_result = kpss(returns)
            kpss_p_value = kpss_result[1]
            
            # 平稳性评分
            adf_score = 100 if adf_p_value < 0.05 else int(100 * (1 - adf_p_value))
            kpss_score = 100 if kpss_p_value >= 0.05 else int(100 * kpss_p_value)
            
            quality_score['stationarity'] = int((adf_score + kpss_score) / 2)
        except Exception:
            quality_score['stationarity'] = 50  # 默认中等分数
        
        # 计算总体评分
        weights = {
            'completeness': 0.25,
            'validity': 0.25,
            'consistency': 0.2,
            'timeliness': 0.1,
            'distribution': 0.1,
            'stationarity': 0.1
        }
        
        quality_score['overall'] = int(
            quality_score['completeness'] * weights['completeness'] +
            quality_score['validity'] * weights['validity'] +
            quality_score['consistency'] * weights['consistency'] +
            quality_score['timeliness'] * weights['timeliness'] +
            quality_score['distribution'] * weights['distribution'] +
            quality_score['stationarity'] * weights['stationarity']
        )
        
        return quality_score

    def _get_max_consecutive_missing(self, series: pd.Series) -> int:
        """
        获取序列中连续缺失值的最大长度

        参数:
            series (pandas.Series): 数据序列

        返回:
            int: 连续缺失值的最大长度
        """
        # 创建缺失值掩码
        mask = series.isna()
        
        if not mask.any():
            return 0
        
        # 计算连续缺失值的长度
        mask_int = mask.astype(int)
        mask_diff = mask_int.diff().fillna(0)
        
        # 找出连续缺失值的开始位置
        starts = np.where(mask_diff == 1)[0]
        
        # 找出连续缺失值的结束位置
        ends = np.where(mask_diff == -1)[0]
        
        # 如果序列以缺失值结束，添加序列长度作为最后一个结束位置
        if mask.iloc[-1]:
            ends = np.append(ends, len(mask))
        
        # 如果序列以缺失值开始，添加0作为第一个开始位置
        if mask.iloc[0]:
            starts = np.insert(starts, 0, 0)
        
        # 计算每段连续缺失值的长度
        if len(starts) > 0 and len(ends) > 0:
            lengths = ends - starts
            return max(lengths)
        
        return 0
