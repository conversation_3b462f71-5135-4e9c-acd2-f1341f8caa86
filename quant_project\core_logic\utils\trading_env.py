"""
深度强化学习交易环境模块
提供与OpenAI Gym兼容的交易环境
"""

import numpy as np
import pandas as pd
import gym
from gym import spaces
from typing import Dict, List, Union, Tuple, Optional, Any
import logging
import matplotlib.pyplot as plt

logger = logging.getLogger('drl_trading')

class TradingEnvironment(gym.Env):
    """交易环境类，实现了OpenAI Gym接口"""
    
    metadata = {'render.modes': ['human', 'rgb_array']}
    
    def __init__(self, 
                data: pd.DataFrame,
                lookback_window_size: int = 50,
                initial_balance: float = 1000000.0,
                commission_rate: float = 0.0003,
                reward_scaling: float = 1e-4,
                price_col: str = '收盘',
                features: Optional[List[str]] = None,
                reward_function: Optional[callable] = None,
                max_steps: Optional[int] = None):
        """
        初始化交易环境
        
        参数:
            data: DataFrame 市场数据
            lookback_window_size: int 观察窗口大小
            initial_balance: float 初始资金
            commission_rate: float 手续费率
            reward_scaling: float 奖励缩放因子
            price_col: str 价格列名
            features: List[str] 用于观察的特征列表，如不指定则使用所有数值列
            reward_function: callable 自定义奖励函数，接收(env, action)参数
            max_steps: int 每个episode的最大步数，如不指定则使用数据长度
        """
        super(TradingEnvironment, self).__init__()
        
        self.data = data.copy()
        self.lookback_window_size = lookback_window_size
        self.initial_balance = initial_balance
        self.commission_rate = commission_rate
        self.reward_scaling = reward_scaling
        self.price_col = price_col
        self.custom_reward_function = reward_function
        
        # 设置特征列
        if features is not None:
            self.features = [f for f in features if f in self.data.columns]
        else:
            # 默认使用所有数值列作为特征
            self.features = list(self.data.select_dtypes(include=['float64', 'int64']).columns)
            
        # 如果没有特征，至少使用价格
        if not self.features and self.price_col in self.data.columns:
            self.features = [self.price_col]
            
        # 特征标准化
        self._normalize_features()
        
        # 设置最大步数
        if max_steps is None:
            self.max_steps = len(self.data) - self.lookback_window_size
        else:
            self.max_steps = min(max_steps, len(self.data) - self.lookback_window_size)
        
        # 初始化当前步骤
        self.current_step = 0
        
        # 定义动作空间 (0: 持有, 1: 买入, 2: 卖出)
        self.action_space = spaces.Discrete(3)
        
        # 定义观察空间
        # 状态包括: 市场特征 + 账户信息(余额、持仓、未实现盈亏)
        feature_shape = (self.lookback_window_size, len(self.features))
        account_shape = (4,)  # 余额、持仓数量、持仓价值、未实现盈亏
        
        self.observation_space = spaces.Dict({
            'market': spaces.Box(low=-np.inf, high=np.inf, shape=feature_shape),
            'account': spaces.Box(low=-np.inf, high=np.inf, shape=account_shape)
        })
        
        # 重置环境
        self.reset()
        
        # 渲染相关
        self.fig = None
        self.axes = None
        
    def _normalize_features(self) -> None:
        """对特征进行标准化处理"""
        self.normalized_data = self.data.copy()
        
        # 标准化市场特征
        for feature in self.features:
            mean = self.normalized_data[feature].mean()
            std = self.normalized_data[feature].std()
            if std > 0:
                self.normalized_data[feature] = (self.normalized_data[feature] - mean) / std
            else:
                self.normalized_data[feature] = 0
    
    def _get_observation(self) -> Dict[str, np.ndarray]:
        """获取当前状态的观察"""
        # 市场特征 (lookback_window_size, features)
        start_idx = self.current_step
        end_idx = self.current_step + self.lookback_window_size
        
        market_obs = self.normalized_data.iloc[start_idx:end_idx][self.features].values
        
        # 当前价格
        current_price = self.data.iloc[end_idx-1][self.price_col]
        
        # 账户信息
        balance = self.balance
        position = self.position
        position_value = position * current_price
        unrealized_pnl = position_value - (position * self.position_avg_price) if position > 0 else 0
        
        account_obs = np.array([
            balance / self.initial_balance,  # 标准化余额
            position,
            position_value / self.initial_balance,  # 标准化持仓价值
            unrealized_pnl / self.initial_balance  # 标准化未实现盈亏
        ])
        
        return {
            'market': market_obs,
            'account': account_obs
        }
    
    def _calculate_reward(self, action: int) -> float:
        """计算奖励"""
        if self.custom_reward_function is not None:
            return self.custom_reward_function(self, action)
            
        # 默认奖励是投资组合价值的变化
        prev_portfolio_value = self.portfolio_values[-2] if len(self.portfolio_values) > 1 else self.initial_balance
        current_portfolio_value = self.portfolio_values[-1]
        
        reward = (current_portfolio_value - prev_portfolio_value) * self.reward_scaling
        
        # 给高交易频率添加惩罚
        if action != 0:  # 非持有动作
            reward -= self.commission_rate * current_portfolio_value * self.reward_scaling * 0.1
        
        return reward
        
    def step(self, action: int) -> Tuple[Dict[str, np.ndarray], float, bool, Dict]:
        """
        执行一步交易
        
        参数:
            action: int 交易动作 (0: 持有, 1: 买入, 2: 卖出)
            
        返回:
            (observation, reward, done, info)
        """
        # 检查动作是否有效
        assert self.action_space.contains(action), f"动作 {action} 不在动作空间内"
        
        # 更新当前步骤
        self.current_step += 1
        done = self.current_step >= self.max_steps
        
        # 获取当前价格
        current_price = self.data.iloc[self.current_step + self.lookback_window_size - 1][self.price_col]
        
        # 执行交易
        if action == 1:  # 买入
            if self.position == 0:  # 只有当前没有持仓时才买入
                # 计算可买入的最大数量
                max_shares = self.balance / (current_price * (1 + self.commission_rate))
                # 全仓买入
                shares_to_buy = max_shares
                # 计算手续费
                commission = shares_to_buy * current_price * self.commission_rate
                # 更新账户
                self.balance -= (shares_to_buy * current_price + commission)
                self.position = shares_to_buy
                self.position_avg_price = current_price
                # 记录交易
                self.trades.append({
                    'step': self.current_step,
                    'price': current_price,
                    'action': 'BUY',
                    'shares': shares_to_buy,
                    'commission': commission
                })
                
        elif action == 2:  # 卖出
            if self.position > 0:  # 只有当前有持仓时才卖出
                # 计算手续费
                commission = self.position * current_price * self.commission_rate
                # 更新账户
                self.balance += (self.position * current_price - commission)
                # 记录交易
                self.trades.append({
                    'step': self.current_step,
                    'price': current_price,
                    'action': 'SELL',
                    'shares': self.position,
                    'commission': commission
                })
                # 清空持仓
                self.position = 0
                self.position_avg_price = 0
        
        # 计算当前投资组合价值
        portfolio_value = self.balance + (self.position * current_price)
        self.portfolio_values.append(portfolio_value)
        
        # 获取当前观察
        observation = self._get_observation()
        
        # 计算奖励
        reward = self._calculate_reward(action)
        
        # 记录历史
        self.history.append({
            'step': self.current_step,
            'price': current_price,
            'action': action,
            'balance': self.balance,
            'position': self.position,
            'portfolio_value': portfolio_value,
            'reward': reward
        })
        
        # 返回信息
        info = {
            'portfolio_value': portfolio_value,
            'balance': self.balance,
            'position': self.position,
            'step': self.current_step
        }
        
        return observation, reward, done, info
    
    def reset(self) -> Dict[str, np.ndarray]:
        """重置环境"""
        # 重置当前步骤为随机值
        self.current_step = np.random.randint(0, len(self.data) - self.lookback_window_size - self.max_steps)
        
        # 重置账户状态
        self.balance = self.initial_balance
        self.position = 0
        self.position_avg_price = 0
        
        # 初始化历史记录
        self.history = []
        self.trades = []
        self.portfolio_values = [self.initial_balance]
        
        # 获取初始观察
        return self._get_observation()
    
    def render(self, mode='human'):
        """渲染当前环境状态"""
        if mode not in ['human', 'rgb_array']:
            raise ValueError(f"不支持的渲染模式: {mode}")
            
        # 创建图形对象
        if self.fig is None or self.axes is None:
            self.fig, self.axes = plt.subplots(3, 1, figsize=(12, 8), sharex=True)
        
        # 清除旧图
        for ax in self.axes:
            ax.clear()
            
        # 设置可视化范围
        start_idx = max(0, self.current_step - 100)
        end_idx = self.current_step + self.lookback_window_size
        
        # 获取数据切片
        price_data = self.data.iloc[start_idx:end_idx][self.price_col]
        dates = price_data.index if isinstance(price_data.index, pd.DatetimeIndex) else range(len(price_data))
        
        # 绘制价格
        self.axes[0].set_title('Price and Trades')
        self.axes[0].plot(dates, price_data.values, 'b-')
        
        # 标记交易点
        for trade in self.trades:
            if start_idx <= trade['step'] < end_idx:
                idx = trade['step'] - start_idx + self.lookback_window_size - 1
                if idx < len(dates):
                    if trade['action'] == 'BUY':
                        self.axes[0].scatter(dates[idx], price_data.iloc[idx], color='g', marker='^', s=100)
                    else:  # 'SELL'
                        self.axes[0].scatter(dates[idx], price_data.iloc[idx], color='r', marker='v', s=100)
        
        # 绘制投资组合价值
        if len(self.history) > 0:
            portfolio_values = [h['portfolio_value'] for h in self.history]
            steps = [h['step'] for h in self.history]
            valid_steps = [s for i, s in enumerate(steps) if start_idx <= s < end_idx]
            valid_values = [portfolio_values[i] for i, s in enumerate(steps) if start_idx <= s < end_idx]
            
            if valid_steps:
                self.axes[1].set_title('Portfolio Value')
                self.axes[1].plot(valid_steps, valid_values, 'g-')
        
        # 绘制奖励
        if len(self.history) > 0:
            rewards = [h['reward'] for h in self.history]
            steps = [h['step'] for h in self.history]
            valid_steps = [s for i, s in enumerate(steps) if start_idx <= s < end_idx]
            valid_rewards = [rewards[i] for i, s in enumerate(steps) if start_idx <= s < end_idx]
            
            if valid_steps:
                self.axes[2].set_title('Rewards')
                self.axes[2].plot(valid_steps, valid_rewards, 'r-')
        
        # 更新布局
        plt.tight_layout()
        
        # 根据模式返回
        if mode == 'human':
            plt.pause(0.1)
            return None
        elif mode == 'rgb_array':
            fig.canvas.draw()
            image = np.array(fig.canvas.renderer.buffer_rgba())
            plt.close()
            return image
    
    def close(self):
        """关闭环境"""
        if self.fig is not None:
            plt.close(self.fig)
            self.fig = None
            self.axes = None


# 自定义奖励函数示例
def sharpe_ratio_reward(env: TradingEnvironment, action: int) -> float:
    """
    基于夏普比率的奖励函数
    
    参数:
        env: TradingEnvironment 交易环境
        action: int 交易动作
        
    返回:
        奖励值
    """
    # 获取当前和历史投资组合价值
    if len(env.portfolio_values) < 3:
        return 0
        
    # 计算近期收益率
    recent_values = env.portfolio_values[-20:]
    returns = np.diff(recent_values) / recent_values[:-1]
    
    # 计算夏普比率成分
    mean_return = np.mean(returns)
    std_return = np.std(returns) + 1e-6  # 避免除零
    
    # 夏普比率
    sharpe = mean_return / std_return
    
    # 加入交易惩罚
    if action != 0:  # 非持有动作
        trading_penalty = env.commission_rate * env.portfolio_values[-1] * env.reward_scaling
        sharpe -= trading_penalty
        
    return sharpe * env.reward_scaling * 100  # 缩放


# 自定义奖励函数示例：基于回撤控制的奖励
def drawdown_controlled_reward(env: TradingEnvironment, action: int) -> float:
    """
    基于回撤控制的奖励函数
    
    参数:
        env: TradingEnvironment 交易环境
        action: int 交易动作
        
    返回:
        奖励值
    """
    # 获取当前和前一个投资组合价值
    if len(env.portfolio_values) < 2:
        return 0
        
    current_value = env.portfolio_values[-1]
    prev_value = env.portfolio_values[-2]
    
    # 计算基本收益奖励
    basic_reward = (current_value - prev_value) * env.reward_scaling
    
    # 计算最大回撤惩罚
    peak = max(env.portfolio_values)
    drawdown = (peak - current_value) / peak if peak > 0 else 0
    
    # 回撤惩罚（回撤越大，惩罚越严重）
    drawdown_penalty = drawdown * drawdown * 10 * env.reward_scaling
    
    # 最终奖励 = 基本收益奖励 - 回撤惩罚
    reward = basic_reward - drawdown_penalty
    
    # 加入交易惩罚
    if action != 0:  # 非持有动作
        trading_penalty = env.commission_rate * current_value * env.reward_scaling
        reward -= trading_penalty
        
    return reward