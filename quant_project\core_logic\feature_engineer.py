"""
特征工程模块
负责从原始行情数据计算技术指标和其他特征
此文件现在从重构后的模块中导入相应的类，以保持向后兼容性
"""

import logging
import warnings

# 导入重构后的模块
from quant_project.core_logic.feature_engineering.base import FeatureEngineer as ModularFeatureEngineer
from quant_project.core_logic.feature_engineering.enhanced import EnhancedFeatureEngineer
from quant_project.core_logic.feature_engineering.adapter import FeatureEngineerAdapter

# 忽略特定警告
warnings.filterwarnings("ignore", category=RuntimeWarning, message=".*invalid value encountered.*")
warnings.filterwarnings("ignore", category=RuntimeWarning, message=".*divide by zero.*")

class FeatureEngineer:
    """
    特征工程类
    负责从原始行情数据计算技术指标和其他特征
    此类现在委托给重构后的模块化特征工程类
    """

    def __init__(self, feature_config=None, data_dictionary_path='data_dictionary.json'):
        """
        初始化特征工程器

        参数:
            feature_config (dict): 特征配置字典，指定要计算的技术指标及其参数
            data_dictionary_path (str): 数据字典文件路径
        """
        self.logger = logging.getLogger('drl_trading')
        self.logger.info("初始化特征工程器，使用模块化实现")
        
        # 创建适配器，委托所有操作
        self.adapter = FeatureEngineerAdapter(
            feature_config=feature_config,
            data_dictionary_path=data_dictionary_path,
            use_enhanced=False
        )
        
        # 从适配器复制属性
        self.feature_config = self.adapter.feature_config
        self.feature_names = self.adapter.feature_names

    def generate_features(self, data):
        """
        生成特征

        参数:
            data (pandas.DataFrame): 原始行情数据，必须包含OHLCV列
        返回:
            pandas.DataFrame: 添加了特征的数据
        """
        # 委托给适配器
        df = self.adapter.generate_features(data)
        
        # 更新特征名称
        self.feature_names = self.adapter.feature_names
        
        return df

    # 以下所有方法都委托给适配器
    def _ensure_column_names(self, df):
        return self.adapter._ensure_column_names(df)
        
    def _calculate_price_features(self, df):
        return self.adapter._calculate_price_features(df)
        
    def _calculate_technical_indicators(self, df):
        return self.adapter._calculate_technical_indicators(df)
        
    def _calculate_statistical_features(self, df):
        return self.adapter._calculate_statistical_features(df)
        
    def _calculate_advanced_features(self, df):
        return self.adapter._calculate_advanced_features(df)
        
    def _calculate_microstructure_features(self, df):
        if hasattr(self.adapter, '_calculate_microstructure_features'):
            return self.adapter._calculate_microstructure_features(df)
        return df
        
    def _calculate_time_features(self, df):
        return self.adapter._calculate_time_features(df)
        
    def _calculate_pattern_features(self, df):
        if hasattr(self.adapter, '_calculate_pattern_features'):
            return self.adapter._calculate_pattern_features(df)
        return df
        
    def _select_features(self, df):
        return self.adapter._select_features(df)
        
    def _normalize_features(self, df):
        return self.adapter._normalize_features(df)
        
    def _update_data_dictionary(self):
        if hasattr(self.adapter, '_update_data_dictionary'):
            self.adapter._update_data_dictionary()

# 定义增强版特征工程类
class EnhancedFeatureEngineer(FeatureEngineer):
    """
    增强版特征工程类
    扩展了基础特征工程类，提供更多高级特征
    此类现在委托给重构后的增强版特征工程类
    """
    
    def __init__(self, feature_config=None, data_dictionary_path='data_dictionary.json'):
        """
        初始化增强版特征工程器

        参数:
            feature_config (dict): 特征配置字典，指定要计算的技术指标及其参数
            data_dictionary_path (str): 数据字典文件路径
        """
        super().__init__(feature_config, data_dictionary_path)
        self.logger.info("初始化增强版特征工程器，使用模块化实现")
        
        # 重新创建适配器，使用增强版特征工程
        self.adapter = FeatureEngineerAdapter(
            feature_config=feature_config,
            data_dictionary_path=data_dictionary_path,
            use_enhanced=True
        )
        
        # 从适配器复制属性
        self.feature_config = self.adapter.feature_config
        self.feature_names = self.adapter.feature_names

# 向后兼容导出
__all__ = ['FeatureEngineer', 'EnhancedFeatureEngineer']
