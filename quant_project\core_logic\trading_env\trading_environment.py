"""
交易环境模块
实现与Gymnasium API兼容的自定义交易环境
"""

import logging
import numpy as np
import pandas as pd
import gymnasium as gym
from gymnasium import spaces

from .observation import ObservationHandler
from .reward import RewardCalculator
from .action import ActionHandler
from .rendering import Renderer

class TradingEnvironment(gym.Env):
    """
    交易环境类
    实现与Gymnasium API兼容的自定义交易环境，用于DRL智能体的训练和评估
    """

    metadata = {'render_modes': ['human', 'rgb_array']}

    def __init__(self,
                 df_processed_data,
                 initial_capital=100000,
                 commission_rate=0.0003,
                 min_hold_days=3,
                 allow_short=False,
                 max_position=1.0,
                 reward_config=None,
                 window_size=20,
                 render_mode=None):
        """
        初始化交易环境

        参数:
            df_processed_data (pandas.DataFrame): 处理好的行情数据和特征
            initial_capital (float): 初始资金
            commission_rate (float): 手续费率（单边）
            min_hold_days (int): 最小持仓天数
            allow_short (bool): 是否允许做空
            max_position (float): 最大仓位比例，范围[0, 1]
            reward_config (dict): 奖励函数配置
            window_size (int): 观测窗口大小
            render_mode (str): 渲染模式，可选 'human', 'rgb_array'
        """
        super(TradingEnvironment, self).__init__()

        self.logger = logging.getLogger('drl_trading')
        self.df = df_processed_data
        self.initial_capital = initial_capital
        self.commission_rate = commission_rate
        self.min_hold_days = min_hold_days
        self.allow_short = allow_short
        self.max_position = max_position
        self.window_size = window_size
        self.render_mode = render_mode

        # 初始化组件
        self.observation_handler = ObservationHandler(
            df=self.df,
            window_size=self.window_size,
            logger=self.logger
        )

        self.reward_calculator = RewardCalculator(
            reward_config=reward_config,
            logger=self.logger
        )

        self.action_handler = ActionHandler(
            commission_rate=self.commission_rate,
            min_hold_days=self.min_hold_days,
            allow_short=self.allow_short,
            max_position=self.max_position,
            logger=self.logger
        )

        self.renderer = Renderer(
            df=self.df,
            logger=self.logger
        )

        # 设置动作空间和观察空间
        self.action_space = self.action_handler.action_space
        self.observation_space = self.observation_handler.observation_space

        # 重置环境
        self.reset()

    def reset(self, seed=None, options=None):
        """
        重置环境到初始状态

        参数:
            seed (int): 随机种子
            options (dict): 重置选项

        返回:
            tuple: (observation, info)
        """
        # 调用父类的reset方法设置随机种子
        super().reset(seed=seed)

        # 设置当前时间步
        self.current_step = self.window_size

        # 初始化账户状态
        self.cash = self.initial_capital
        self.shares_held = 0
        self.current_price = self.df['收盘'].iloc[self.current_step]
        self.cost_basis = 0
        self.total_commission = 0
        self.holding_days = 0

        # 重置组件
        self.reward_calculator.reset()
        self.action_handler.reset()

        # 账户价值历史
        self.portfolio_values = [self.initial_capital]

        # 获取初始观测
        observation = self.observation_handler.get_observation(
            current_step=self.current_step,
            cash=self.cash,
            shares_held=self.shares_held,
            current_price=self.current_price,
            cost_basis=self.cost_basis,
            holding_days=self.holding_days
        )

        # 信息字典
        info = {
            'step': self.current_step,
            'portfolio_value': self._calculate_portfolio_value(),
            'cash': self.cash,
            'shares_held': self.shares_held,
            'current_price': self.current_price,
            'holding_days': self.holding_days
        }

        return observation, info

    def step(self, action):
        """
        执行一个动作并更新环境

        参数:
            action (int): 动作，0=保持, 1=买入, 2=卖出

        返回:
            tuple: (observation, reward, terminated, truncated, info)
        """
        # 保存上一步的组合价值用于计算奖励
        prev_portfolio_value = self._calculate_portfolio_value()

        # 获取当前价格
        self.current_price = self.df['收盘'].iloc[self.current_step]

        # 获取当前日期
        current_date = self.df.index[self.current_step]

        # 执行交易动作
        self.cash, self.shares_held, self.holding_days, trade_cost, is_trade, cost_basis = self.action_handler.execute_action(
            action=action,
            current_step=self.current_step,
            current_price=self.current_price,
            cash=self.cash,
            shares_held=self.shares_held,
            holding_days=self.holding_days,
            initial_capital=self.initial_capital
        )

        # 更新交易记录中的日期
        if is_trade and len(self.action_handler.trades) > 0:
            # 更新最新交易记录的日期
            latest_trade = self.action_handler.trades[-1]
            latest_trade['date'] = current_date

        # 更新成本基础
        if cost_basis > 0:
            self.cost_basis = cost_basis

        # 更新总手续费
        self.total_commission += trade_cost

        # 计算当前组合价值
        current_portfolio_value = self._calculate_portfolio_value()

        # 记录组合价值历史
        self.portfolio_values.append(current_portfolio_value)

        # 计算奖励
        reward, reward_components = self.reward_calculator.calculate_reward(
            prev_portfolio_value=prev_portfolio_value,
            current_portfolio_value=current_portfolio_value,
            holding_days=self.holding_days,
            trade_cost=trade_cost,
            is_trade=is_trade
        )

        # 移动到下一个时间步
        self.current_step += 1

        # 检查是否结束
        terminated = self.current_step >= len(self.df) - 1
        truncated = False  # 在金融环境中通常不使用truncated

        # 获取新的观测
        observation = self.observation_handler.get_observation(
            current_step=self.current_step,
            cash=self.cash,
            shares_held=self.shares_held,
            current_price=self.current_price,
            cost_basis=self.cost_basis,
            holding_days=self.holding_days
        )

        # 更新渲染器数据
        self.renderer.update_data(
            portfolio_values=self.portfolio_values,
            trades=self.action_handler.get_trades()
        )

        # 信息字典
        info = {
            'step': self.current_step,
            'portfolio_value': current_portfolio_value,
            'cash': self.cash,
            'shares_held': self.shares_held,
            'current_price': self.current_price,
            'holding_days': self.holding_days,
            'total_commission': self.total_commission,
            'return': (current_portfolio_value / prev_portfolio_value) - 1,
            'reward_components': reward_components
        }

        return observation, reward, terminated, truncated, info

    def render(self, mode=None):
        """
        渲染环境状态

        参数:
            mode (str): 渲染模式，可选 'human', 'rgb_array'

        返回:
            numpy.ndarray: 如果mode='rgb_array'，返回RGB数组
        """
        mode = mode or self.render_mode
        if mode is None:
            mode = 'human'

        return self.renderer.render(
            current_step=self.current_step,
            mode=mode
        )

    def close(self):
        """
        关闭环境
        """
        pass

    def _calculate_portfolio_value(self):
        """
        计算当前组合价值

        返回:
            float: 组合价值
        """
        return self.cash + self.shares_held * self.current_price

    def get_portfolio_values(self):
        """
        获取组合价值历史

        返回:
            list: 组合价值历史
        """
        return self.portfolio_values

    def get_trades(self):
        """
        获取交易记录

        返回:
            list: 交易记录
        """
        return self.action_handler.get_trades()

    def get_performance_metrics(self):
        """
        获取性能指标

        返回:
            dict: 性能指标
        """
        # 计算收益率
        if len(self.portfolio_values) > 1:
            returns = np.diff(self.portfolio_values) / self.portfolio_values[:-1]

            # 计算年化收益率
            annual_return = np.mean(returns) * 252

            # 计算年化波动率
            annual_volatility = np.std(returns) * np.sqrt(252)

            # 计算夏普比率
            sharpe_ratio = self.reward_calculator.calculate_sharpe_ratio()

            # 计算索提诺比率
            sortino_ratio = self.reward_calculator.calculate_sortino_ratio()

            # 计算最大回撤
            max_drawdown = self.reward_calculator.calculate_max_drawdown()

            # 计算卡玛比率
            calmar_ratio = self.reward_calculator.calculate_calmar_ratio()

            # 计算总收益率
            total_return = (self.portfolio_values[-1] / self.portfolio_values[0]) - 1

            # 计算胜率
            win_rate = np.mean(returns > 0)

            # 计算盈亏比
            profit_loss_ratio = np.mean(returns[returns > 0]) / abs(np.mean(returns[returns < 0])) if np.any(returns < 0) else float('inf')

            return {
                'annual_return': annual_return,
                'annual_volatility': annual_volatility,
                'sharpe_ratio': sharpe_ratio,
                'sortino_ratio': sortino_ratio,
                'max_drawdown': max_drawdown,
                'calmar_ratio': calmar_ratio,
                'total_return': total_return,
                'win_rate': win_rate,
                'profit_loss_ratio': profit_loss_ratio
            }
        else:
            return {
                'annual_return': 0,
                'annual_volatility': 0,
                'sharpe_ratio': 0,
                'sortino_ratio': 0,
                'max_drawdown': 0,
                'calmar_ratio': 0,
                'total_return': 0,
                'win_rate': 0,
                'profit_loss_ratio': 0
            }

    def save_performance_plot(self, filename, benchmark=None):
        """
        保存性能图表

        参数:
            filename (str): 文件名
            benchmark (pandas.Series): 基准收益率，如果为None则不绘制

        返回:
            bool: 是否成功保存
        """
        return self.renderer.save_performance_plot(
            portfolio_values=self.portfolio_values,
            trades=self.action_handler.get_trades(),
            filename=filename,
            benchmark=benchmark
        )
