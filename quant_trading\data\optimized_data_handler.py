#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
优化版数据处理模块
专注于提高数据检索和特征计算的性能
"""

import os
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import hashlib
import json
import time
import pytz
from scipy import stats
import concurrent.futures
from functools import lru_cache

try:
    import akshare as ak
except ImportError:
    logging.error("未安装AkShare库，请使用 pip install akshare 安装")

class OptimizedDataHandler:
    """
    优化版数据处理类
    专注于提高数据检索和特征计算的性能
    """

    def __init__(self, cache_dir='data_cache', timezone='Asia/Shanghai',
                 missing_value_strategy='ffill', outlier_detection=True,
                 data_validation=True, adjust_price=True,
                 use_memory_cache=True, parallel_processing=True,
                 max_workers=4):
        """
        初始化优化版数据处理器

        参数:
            cache_dir (str): 缓存目录路径
            timezone (str): 时区设置，默认为'Asia/Shanghai'
            missing_value_strategy (str): 缺失值处理策略，可选'ffill'(前向填充),'bfill'(后向填充),'mean'(均值填充),'median'(中位数填充),'none'(不处理)
            outlier_detection (bool): 是否进行异常值检测
            data_validation (bool): 是否进行数据验证
            adjust_price (bool): 是否使用复权价格
            use_memory_cache (bool): 是否使用内存缓存
            parallel_processing (bool): 是否使用并行处理
            max_workers (int): 并行处理的最大工作线程数
        """
        self.cache_dir = cache_dir
        self.logger = logging.getLogger('drl_trading')
        self.timezone = pytz.timezone(timezone)
        self.missing_value_strategy = missing_value_strategy
        self.outlier_detection = outlier_detection
        self.data_validation = data_validation
        self.adjust_price = adjust_price
        self.use_memory_cache = use_memory_cache
        self.parallel_processing = parallel_processing
        self.max_workers = max_workers

        # 内存缓存
        self.memory_cache = {}

        # API成功记录，用于智能选择API
        self.successful_apis = {}

        # 确保缓存目录存在
        os.makedirs(self.cache_dir, exist_ok=True)

        # 指数代码映射表
        self.index_map = {
            # 上证系列指数
            '000001': {'name': '上证指数', 'prefix': 'sh'},
            '000016': {'name': '上证50', 'prefix': 'sh'},
            '000300': {'name': '沪深300', 'prefix': 'sh'},
            '000905': {'name': '中证500', 'prefix': 'sh'},
            '000852': {'name': '中证1000', 'prefix': 'sh'},
            # 深证系列指数
            '399001': {'name': '深证成指', 'prefix': 'sz'},
            '399006': {'name': '创业板指', 'prefix': 'sz'},
            '399673': {'name': '创业板50', 'prefix': 'sz'},
            # 其他重要指数
            '000688': {'name': '科创50', 'prefix': 'sh'},
            '399324': {'name': '深证红利', 'prefix': 'sz'}
        }

    @lru_cache(maxsize=128)
    def get_stock_data(self, stock_code, start_date, end_date, frequency='日线', use_cache=True):
        """
        获取金融数据（优化版）

        参数:
            stock_code (str): 金融产品代码
            start_date (str): 开始日期，格式为 'YYYY-MM-DD'
            end_date (str): 结束日期，格式为 'YYYY-MM-DD'
            frequency (str): 数据频率，可选 '日线', '周线', '月线'
            use_cache (bool): 是否使用缓存

        返回:
            pandas.DataFrame: 金融数据
        """
        # 首先检查是否是加密货币代码，如果是则直接禁止
        if stock_code.startswith('crypto_'):
            self.logger.warning("加密货币数据提取功能已被禁用")
            error_message = "加密货币数据提取功能已被禁用，请使用其他类型的金融数据。"
            raise ValueError(error_message)

        # 生成缓存键
        cache_key = self._generate_cache_key(stock_code, start_date, end_date, frequency)

        # 检查内存缓存
        if self.use_memory_cache and cache_key in self.memory_cache:
            self.logger.info(f"从内存缓存加载数据: {cache_key}")
            return self.memory_cache[cache_key]

        # 检查磁盘缓存
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.csv")
        if use_cache and os.path.exists(cache_file):
            try:
                start_time = time.time()
                data = pd.read_csv(cache_file, index_col=0, parse_dates=True)
                self.logger.info(f"从磁盘缓存加载数据: {cache_file}, 耗时: {time.time() - start_time:.4f}秒")

                # 添加到内存缓存
                if self.use_memory_cache:
                    self.memory_cache[cache_key] = data

                return data
            except Exception as e:
                self.logger.warning(f"从缓存加载数据失败: {str(e)}，将重新获取数据")

        # 从AkShare获取数据
        try:
            start_time = time.time()
            self.logger.info(f"从AkShare获取数据: {stock_code}, {start_date} 至 {end_date}, 频率: {frequency}")

            # 智能选择API获取数据
            data = self._smart_get_data(stock_code, start_date, end_date, frequency)

            # 处理数据
            if data is not None and not data.empty:
                # 标准化列名
                data = self._standardize_column_names(data)

                # 设置日期索引
                data = self._set_date_index(data, start_date)

                # 数据清洗（并行处理）
                if self.parallel_processing:
                    data = self._parallel_clean_data(data)
                else:
                    data = self._clean_data(data)

                # 时间序列对齐
                freq_map = {'日线': 'D', '周线': 'W', '月线': 'M'}
                freq = freq_map.get(frequency, 'D')
                data = self._align_time_series(data, start_date, end_date, freq=freq)

                # 缓存数据
                if use_cache:
                    # 磁盘缓存
                    data.to_csv(cache_file)

                    # 内存缓存
                    if self.use_memory_cache:
                        self.memory_cache[cache_key] = data

                self.logger.info(f"数据获取和处理完成，耗时: {time.time() - start_time:.4f}秒")
                return data
            else:
                raise ValueError(f"获取的数据为空，请检查输入的代码是否正确: {stock_code}")

        except Exception as e:
            self.logger.error(f"获取数据失败: {str(e)}")
            raise

    def _smart_get_data(self, stock_code, start_date, end_date, frequency):
        """
        智能选择API获取数据

        参数:
            stock_code (str): 金融产品代码
            start_date (str): 开始日期
            end_date (str): 结束日期
            frequency (str): 数据频率

        返回:
            pandas.DataFrame: 金融数据
        """
        # 解析代码类型
        if stock_code.startswith('sh') or stock_code.startswith('sz'):
            # 股票数据
            market = stock_code[:2]
            code = stock_code[2:]
            data_type = 'stock'
        elif stock_code.startswith('futures_'):
            # 期货数据
            code = stock_code[9:]
            data_type = 'futures'
        elif stock_code.startswith('index_'):
            # 指数数据
            code = stock_code[6:]
            data_type = 'index'
        elif stock_code.startswith('crypto_'):
            # 加密货币数据已被禁用
            raise ValueError("加密货币数据提取功能已被禁用，请使用其他类型的金融数据。")
        else:
            # 默认当作股票处理
            market = 'sh' if stock_code.startswith('6') else 'sz'
            code = stock_code
            data_type = 'stock'

        # 根据数据类型选择API
        if frequency == '日线':
            if data_type == 'stock':
                return self._get_stock_daily_data(market, code, start_date, end_date)
            elif data_type == 'futures':
                raise ValueError("期货数据提取功能已被禁用，请使用其他类型的金融数据。")
            elif data_type == 'index':
                return self._get_index_daily_data(code, start_date, end_date)
        elif frequency == '周线':
            # 使用日线数据重采样为周线
            daily_data = self.get_stock_data(stock_code, start_date, end_date, '日线', use_cache=False)
            return self._resample_data(daily_data, 'W')
        elif frequency == '月线':
            # 使用日线数据重采样为月线
            daily_data = self.get_stock_data(stock_code, start_date, end_date, '日线', use_cache=False)
            return self._resample_data(daily_data, 'M')
        else:
            raise ValueError(f"不支持的频率: {frequency}")

    def _get_stock_daily_data(self, market, code, start_date, end_date):
        """
        获取股票日线数据（优化版）

        参数:
            market (str): 市场代码，'sh'或'sz'
            code (str): 股票代码
            start_date (str): 开始日期
            end_date (str): 结束日期

        返回:
            pandas.DataFrame: 股票日线数据
        """
        # 检查是否有成功记录
        key = f"{market}{code}_daily"
        if key in self.successful_apis:
            api_method = self.successful_apis[key]
            try:
                if api_method == "stock_zh_index_daily_em":
                    data = ak.stock_zh_index_daily_em(symbol=f"{market}{code}")
                elif api_method == "stock_zh_a_daily":
                    data = ak.stock_zh_a_daily(symbol=f"{code}", start_date=start_date, end_date=end_date, adjust="qfq")
                elif api_method == "stock_zh_index_daily":
                    data = ak.stock_zh_index_daily(symbol=f"{market}{code}")

                if data is not None and not data.empty:
                    self.logger.info(f"使用记录的成功API {api_method} 获取数据成功")
                    return data
            except Exception as e:
                self.logger.warning(f"使用记录的成功API {api_method} 获取数据失败: {str(e)}")
                # 从成功记录中移除
                del self.successful_apis[key]

        # 尝试不同的API方法
        api_methods = [
            ("stock_zh_index_daily_em", lambda: ak.stock_zh_index_daily_em(symbol=f"{market}{code}")),
            ("stock_zh_a_daily", lambda: ak.stock_zh_a_daily(symbol=f"{code}", start_date=start_date, end_date=end_date, adjust="qfq")),
            ("stock_zh_index_daily", lambda: ak.stock_zh_index_daily(symbol=f"{market}{code}"))
        ]

        for method_name, method_func in api_methods:
            try:
                self.logger.info(f"尝试使用 {method_name} 获取数据")
                data = method_func()

                if data is not None and not data.empty:
                    # 记录成功的API方法
                    self.successful_apis[key] = method_name
                    self.logger.info(f"使用 {method_name} 获取数据成功")
                    return data
            except Exception as e:
                self.logger.warning(f"使用 {method_name} 获取数据失败: {str(e)}")

        # 如果所有方法都失败，尝试使用上证指数作为替代
        self.logger.warning(f"所有API方法都失败，尝试使用上证指数(sh000001)作为替代")
        try:
            data = ak.stock_zh_index_daily_em(symbol="sh000001")
            if data is not None and not data.empty:
                return data
        except Exception as e:
            self.logger.error(f"获取上证指数数据失败: {str(e)}")

        raise ValueError(f"无法获取股票 {market}{code} 的数据")

    def _get_index_daily_data(self, code, start_date, end_date):
        """
        获取指数日线数据（优化版）

        参数:
            code (str): 指数代码
            start_date (str): 开始日期
            end_date (str): 结束日期

        返回:
            pandas.DataFrame: 指数日线数据
        """
        # 检查是否是已知的指数代码
        if code in self.index_map:
            index_info = self.index_map[code]
            prefix = index_info['prefix']
            full_code = f"{prefix}{code}"
        else:
            # 尝试根据代码前缀判断
            if code.startswith('000'):
                prefix = 'sh'
                full_code = f"{prefix}{code}"
            elif code.startswith('399'):
                prefix = 'sz'
                full_code = f"{prefix}{code}"
            elif code.startswith('sh') or code.startswith('sz'):
                # 已经包含前缀
                full_code = code
            else:
                # 无法判断，尝试直接使用
                full_code = code

        # 检查是否有成功记录
        key = f"{full_code}_index"
        if key in self.successful_apis:
            api_method = self.successful_apis[key]
            try:
                if api_method == "stock_zh_index_daily_em":
                    data = ak.stock_zh_index_daily_em(symbol=full_code)
                elif api_method == "stock_zh_index_daily":
                    data = ak.stock_zh_index_daily(symbol=full_code)
                elif api_method == "index_zh_a_hist":
                    # 移除可能的前缀
                    clean_code = code
                    if clean_code.startswith('sh') or clean_code.startswith('sz'):
                        clean_code = clean_code[2:]
                    # 转换日期格式为YYYYMMDD
                    start_date_fmt = start_date.replace('-', '')
                    end_date_fmt = end_date.replace('-', '')
                    data = ak.index_zh_a_hist(symbol=clean_code, period="daily",
                                             start_date=start_date_fmt, end_date=end_date_fmt)

                if data is not None and not data.empty:
                    self.logger.info(f"使用记录的成功API {api_method} 获取指数数据成功")
                    return data
            except Exception as e:
                self.logger.warning(f"使用记录的成功API {api_method} 获取指数数据失败: {str(e)}")
                # 从成功记录中移除
                del self.successful_apis[key]

        # 尝试不同的API方法
        api_methods = [
            ("stock_zh_index_daily_em", lambda: ak.stock_zh_index_daily_em(symbol=full_code)),
            ("stock_zh_index_daily", lambda: ak.stock_zh_index_daily(symbol=full_code)),
            ("index_zh_a_hist", lambda: ak.index_zh_a_hist(
                symbol=code.replace('sh', '').replace('sz', ''),
                period="daily",
                start_date=start_date.replace('-', ''),
                end_date=end_date.replace('-', '')
            ))
        ]

        for method_name, method_func in api_methods:
            try:
                self.logger.info(f"尝试使用 {method_name} 获取指数数据")
                data = method_func()

                if data is not None and not data.empty:
                    # 记录成功的API方法
                    self.successful_apis[key] = method_name
                    self.logger.info(f"使用 {method_name} 获取指数数据成功")
                    return data
            except Exception as e:
                self.logger.warning(f"使用 {method_name} 获取指数数据失败: {str(e)}")

        # 如果所有方法都失败，尝试使用上证指数作为替代
        self.logger.warning(f"所有API方法都失败，尝试使用上证指数(sh000001)作为替代")
        try:
            data = ak.stock_zh_index_daily_em(symbol="sh000001")
            if data is not None and not data.empty:
                return data
        except Exception as e:
            self.logger.error(f"获取上证指数数据失败: {str(e)}")

        raise ValueError(f"无法获取指数 {code} 的数据")

    def _set_date_index(self, data, start_date):
        """
        设置日期索引（优化版）

        参数:
            data (pandas.DataFrame): 原始数据
            start_date (str): 开始日期

        返回:
            pandas.DataFrame: 设置了日期索引的数据
        """
        # 检查日期列
        date_column_candidates = ['date', '日期', 'trade_date', '交易日期', '日期时间']
        date_column = None

        for col in date_column_candidates:
            if col in data.columns:
                date_column = col
                break

        if date_column is not None:
            # 将日期列转换为datetime类型
            try:
                data[date_column] = pd.to_datetime(data[date_column])
                # 设置日期索引
                data.set_index(date_column, inplace=True)
            except Exception as e:
                self.logger.warning(f"将 {date_column} 设置为索引时出错: {str(e)}")
                # 创建一个新的日期列
                data['date'] = pd.date_range(start=pd.to_datetime(start_date), periods=len(data), freq='D')
                data.set_index('date', inplace=True)
        elif isinstance(data.index, pd.DatetimeIndex):
            # 如果索引已经是DatetimeIndex，不需要额外处理
            pass
        else:
            # 如果没有找到日期列，创建一个日期索引
            data.index = pd.date_range(start=pd.to_datetime(start_date), periods=len(data), freq='D')

        return data

    def _parallel_clean_data(self, data):
        """
        并行数据清洗

        参数:
            data (pandas.DataFrame): 原始数据

        返回:
            pandas.DataFrame: 清洗后的数据
        """
        if data is None or data.empty:
            return data

        # 复制数据，避免修改原始数据
        df = data.copy()

        # 确保所有数值列都是浮点数类型
        numeric_columns = df.select_dtypes(include=['number']).columns
        for col in numeric_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')

        # 处理缺失值
        if self.missing_value_strategy != 'none':
            # 记录缺失值数量
            missing_before = df.isna().sum().sum()

            # 应用缺失值处理策略
            if self.missing_value_strategy == 'ffill':
                # 前向填充
                df = df.fillna(method='ffill')
                # 如果仍有缺失值（例如序列开始处），使用后向填充
                df = df.fillna(method='bfill')
            elif self.missing_value_strategy == 'bfill':
                # 后向填充
                df = df.fillna(method='bfill')
                # 如果仍有缺失值（例如序列结束处），使用前向填充
                df = df.fillna(method='ffill')
            elif self.missing_value_strategy == 'mean':
                # 均值填充
                for col in numeric_columns:
                    df[col] = df[col].fillna(df[col].mean())
            elif self.missing_value_strategy == 'median':
                # 中位数填充
                for col in numeric_columns:
                    df[col] = df[col].fillna(df[col].median())

            # 记录处理后的缺失值数量
            missing_after = df.isna().sum().sum()
            if missing_before > 0:
                self.logger.info(f"缺失值处理: 从 {missing_before} 减少到 {missing_after}")

        # 处理异常值（并行）
        if self.outlier_detection:
            # 获取价格和成交量列
            price_volume_cols = []
            for col in ['开盘', '最高', '最低', '收盘', '成交量']:
                if col in df.columns:
                    price_volume_cols.append(col)

            # 并行处理每一列的异常值
            if self.parallel_processing and len(price_volume_cols) > 1:
                with concurrent.futures.ThreadPoolExecutor(max_workers=min(self.max_workers, len(price_volume_cols))) as executor:
                    # 提交任务
                    futures = {executor.submit(self._detect_and_fix_outliers, df, col): col for col in price_volume_cols}

                    # 获取结果
                    for future in concurrent.futures.as_completed(futures):
                        col = futures[future]
                        try:
                            df[col] = future.result()
                        except Exception as e:
                            self.logger.error(f"处理列 {col} 的异常值时出错: {str(e)}")
            else:
                # 串行处理
                for col in price_volume_cols:
                    try:
                        df[col] = self._detect_and_fix_outliers(df, col)
                    except Exception as e:
                        self.logger.error(f"处理列 {col} 的异常值时出错: {str(e)}")

        # 确保价格关系合理
        if '最高' in df.columns and '最低' in df.columns and '开盘' in df.columns and '收盘' in df.columns:
            # 修复最高价
            df['最高'] = df[['最高', '开盘', '收盘']].max(axis=1)
            # 修复最低价
            df['最低'] = df[['最低', '开盘', '收盘']].min(axis=1)

        # 确保成交量非负
        if '成交量' in df.columns:
            df.loc[df['成交量'] < 0, '成交量'] = 0

        return df

    def _detect_and_fix_outliers(self, df, column):
        """
        检测并修复单列的异常值

        参数:
            df (pandas.DataFrame): 数据框
            column (str): 列名

        返回:
            pandas.Series: 修复后的列数据
        """
        # 复制列数据
        series = df[column].copy()

        # 使用IQR方法检测异常值（最有效的方法之一）
        try:
            Q1 = series.quantile(0.25)
            Q3 = series.quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            outliers = ((series < lower_bound) | (series > upper_bound)) & ~series.isna()

            # 统计异常值数量
            outlier_count = outliers.sum()

            if outlier_count > 0:
                self.logger.info(f"列 {column} 中检测到 {outlier_count} 个异常值 (占比 {outlier_count/len(df)*100:.2f}%)")

                # 使用滑动窗口计算局部统计量
                window_size = 5
                if column in ['开盘', '最高', '最低', '收盘']:
                    # 对于价格列，使用局部中位数替换异常值
                    rolling_median = series.rolling(window=window_size, center=True, min_periods=1).median()
                    series.loc[outliers] = rolling_median[outliers]
                elif column == '成交量':
                    # 对于成交量列，使用局部平均值替换异常值
                    rolling_mean = series.rolling(window=window_size, center=True, min_periods=1).mean()
                    series.loc[outliers] = rolling_mean[outliers]
        except Exception as e:
            self.logger.warning(f"IQR异常值检测失败: {str(e)}")

        return series

    def _clean_data(self, data):
        """
        清洗数据，处理缺失值和异常值（非并行版本）

        参数:
            data (pandas.DataFrame): 原始数据

        返回:
            pandas.DataFrame: 清洗后的数据
        """
        if data is None or data.empty:
            return data

        # 复制数据，避免修改原始数据
        df = data.copy()

        # 确保所有数值列都是浮点数类型
        numeric_columns = df.select_dtypes(include=['number']).columns
        for col in numeric_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')

        # 处理缺失值
        if self.missing_value_strategy != 'none':
            # 记录缺失值数量
            missing_before = df.isna().sum().sum()

            # 应用缺失值处理策略
            if self.missing_value_strategy == 'ffill':
                # 前向填充
                df = df.fillna(method='ffill')
                # 如果仍有缺失值（例如序列开始处），使用后向填充
                df = df.fillna(method='bfill')
            elif self.missing_value_strategy == 'bfill':
                # 后向填充
                df = df.fillna(method='bfill')
                # 如果仍有缺失值（例如序列结束处），使用前向填充
                df = df.fillna(method='ffill')
            elif self.missing_value_strategy == 'mean':
                # 均值填充
                for col in numeric_columns:
                    df[col] = df[col].fillna(df[col].mean())
            elif self.missing_value_strategy == 'median':
                # 中位数填充
                for col in numeric_columns:
                    df[col] = df[col].fillna(df[col].median())

            # 记录处理后的缺失值数量
            missing_after = df.isna().sum().sum()
            if missing_before > 0:
                self.logger.info(f"缺失值处理: 从 {missing_before} 减少到 {missing_after}")

        # 处理异常值
        if self.outlier_detection:
            # 获取价格和成交量列
            price_volume_cols = []
            for col in ['开盘', '最高', '最低', '收盘', '成交量']:
                if col in df.columns:
                    price_volume_cols.append(col)

            # 处理每一列的异常值
            for col in price_volume_cols:
                try:
                    df[col] = self._detect_and_fix_outliers(df, col)
                except Exception as e:
                    self.logger.error(f"处理列 {col} 的异常值时出错: {str(e)}")

        # 确保价格关系合理
        if '最高' in df.columns and '最低' in df.columns and '开盘' in df.columns and '收盘' in df.columns:
            # 修复最高价
            df['最高'] = df[['最高', '开盘', '收盘']].max(axis=1)
            # 修复最低价
            df['最低'] = df[['最低', '开盘', '收盘']].min(axis=1)

        # 确保成交量非负
        if '成交量' in df.columns:
            df.loc[df['成交量'] < 0, '成交量'] = 0

        return df

    def _align_time_series(self, data, start_date, end_date, freq='D'):
        """
        确保时间序列在指定的日期范围内连续，处理交易日历问题

        参数:
            data (pandas.DataFrame): 原始数据
            start_date (str): 开始日期
            end_date (str): 结束日期
            freq (str): 频率，'D'表示日频

        返回:
            pandas.DataFrame: 对齐后的数据
        """
        if data is None or data.empty:
            return data

        # 确保数据有日期索引
        if not isinstance(data.index, pd.DatetimeIndex):
            self.logger.warning("数据没有日期索引，无法进行时间序列对齐")
            return data

        # 转换日期字符串为datetime对象
        start_dt = pd.to_datetime(start_date)
        end_dt = pd.to_datetime(end_date)

        # 创建完整的日期范围
        full_date_range = pd.date_range(start=start_dt, end=end_dt, freq=freq)

        # 如果是交易日数据，过滤掉周末
        if freq == 'D':
            # 只保留周一至周五
            full_date_range = full_date_range[full_date_range.dayofweek < 5]

        # 重新索引数据，填充缺失值
        reindexed_data = data.reindex(full_date_range)

        # 应用缺失值处理策略
        if self.missing_value_strategy != 'none':
            if self.missing_value_strategy == 'ffill':
                reindexed_data = reindexed_data.fillna(method='ffill')
                reindexed_data = reindexed_data.fillna(method='bfill')  # 处理开始部分的缺失值
            elif self.missing_value_strategy == 'bfill':
                reindexed_data = reindexed_data.fillna(method='bfill')
                reindexed_data = reindexed_data.fillna(method='ffill')  # 处理结束部分的缺失值
            elif self.missing_value_strategy == 'mean':
                # 对每列应用均值填充
                for col in reindexed_data.columns:
                    if pd.api.types.is_numeric_dtype(reindexed_data[col]):
                        col_mean = data[col].mean()
                        reindexed_data[col] = reindexed_data[col].fillna(col_mean)
            elif self.missing_value_strategy == 'median':
                # 对每列应用中位数填充
                for col in reindexed_data.columns:
                    if pd.api.types.is_numeric_dtype(reindexed_data[col]):
                        col_median = data[col].median()
                        reindexed_data[col] = reindexed_data[col].fillna(col_median)

        return reindexed_data

    def _standardize_column_names(self, data):
        """
        标准化列名为中文

        参数:
            data (pandas.DataFrame): 要标准化的数据

        返回:
            pandas.DataFrame: 标准化后的数据
        """
        # 英文列名到中文列名的映射
        column_mapping = {
            'open': '开盘',
            'high': '最高',
            'low': '最低',
            'close': '收盘',
            'volume': '成交量',
            'amount': '成交额',
            'turn': '换手率',
            'pct_chg': '涨跌幅',
            'date': '日期'
        }

        # 重命名列
        renamed_columns = {}
        for col in data.columns:
            if col in column_mapping:
                renamed_columns[col] = column_mapping[col]

        if renamed_columns:
            data = data.rename(columns=renamed_columns)

        return data

    def _generate_cache_key(self, stock_code, start_date, end_date, frequency):
        """
        生成缓存键

        参数:
            stock_code (str): 股票代码
            start_date (str): 开始日期
            end_date (str): 结束日期
            frequency (str): 数据频率

        返回:
            str: 缓存键
        """
        # 创建一个包含所有参数的字符串
        params_str = f"{stock_code}_{start_date}_{end_date}_{frequency}"
        # 使用MD5生成一个固定长度的哈希值
        return hashlib.md5(params_str.encode()).hexdigest()

    def _resample_data(self, data, freq):
        """
        重采样数据到指定频率

        参数:
            data (pandas.DataFrame): 要重采样的数据
            freq (str): 频率，'W'表示周，'M'表示月

        返回:
            pandas.DataFrame: 重采样后的数据
        """
        # 确保数据有日期索引
        if not isinstance(data.index, pd.DatetimeIndex):
            if '日期' in data.columns:
                data['日期'] = pd.to_datetime(data['日期'])
                data.set_index('日期', inplace=True)
            else:
                raise ValueError("数据没有日期列或索引")

        # 定义OHLC聚合方法
        ohlc_dict = {
            '开盘': 'first',
            '最高': 'max',
            '最低': 'min',
            '收盘': 'last',
            '成交量': 'sum'
        }

        # 如果有成交额列，也进行聚合
        if '成交额' in data.columns:
            ohlc_dict['成交额'] = 'sum'

        # 重采样
        resampled = data.resample(freq).agg(ohlc_dict)

        return resampled

    def clear_cache(self, memory_only=False):
        """
        清除缓存

        参数:
            memory_only (bool): 是否只清除内存缓存，不清除磁盘缓存
        """
        # 清除内存缓存
        self.memory_cache.clear()
        self.logger.info("内存缓存已清除")

        # 清除磁盘缓存
        if not memory_only:
            try:
                cache_files = [f for f in os.listdir(self.cache_dir) if f.endswith('.csv')]
                for file in cache_files:
                    os.remove(os.path.join(self.cache_dir, file))
                self.logger.info(f"磁盘缓存已清除，共删除 {len(cache_files)} 个文件")
            except Exception as e:
                self.logger.error(f"清除磁盘缓存失败: {str(e)}")

    def get_latest_trading_date(self):
        """
        获取最新交易日期

        返回:
            str: 最新交易日期，格式为 'YYYY-MM-DD'
        """
        try:
            # 使用上证指数获取最新交易日
            latest_data = ak.stock_zh_index_daily_em(symbol="sh000001")
            if latest_data is not None and not latest_data.empty:
                # 获取最新日期
                if 'date' in latest_data.columns:
                    latest_date = latest_data.iloc[-1]['date']
                elif '日期' in latest_data.columns:
                    latest_date = latest_data.iloc[-1]['日期']
                else:
                    # 尝试使用索引
                    latest_date = latest_data.index[-1]

                # 确保返回的是字符串格式
                if isinstance(latest_date, str):
                    # 尝试解析日期字符串，然后重新格式化以确保格式一致
                    try:
                        parsed_date = datetime.strptime(latest_date, '%Y-%m-%d')
                        return parsed_date.strftime('%Y-%m-%d')
                    except ValueError:
                        # 如果格式不是 'YYYY-MM-DD'，尝试其他常见格式
                        try:
                            parsed_date = datetime.strptime(latest_date, '%Y%m%d')
                            return parsed_date.strftime('%Y-%m-%d')
                        except ValueError:
                            # 如果仍然失败，记录错误并返回当前日期
                            self.logger.error(f"无法解析日期字符串: {latest_date}")
                            return datetime.now().strftime('%Y-%m-%d')
                elif isinstance(latest_date, datetime) or isinstance(latest_date, pd.Timestamp):
                    # 如果是 datetime 对象或 pandas Timestamp，直接格式化
                    return latest_date.strftime('%Y-%m-%d')
                else:
                    # 如果是其他类型，尝试转换为字符串
                    self.logger.warning(f"未知的日期类型: {type(latest_date)}，尝试转换为字符串")
                    return str(latest_date)
            else:
                self.logger.error("获取上证指数数据失败，返回当前日期")
                return datetime.now().strftime('%Y-%m-%d')
        except Exception as e:
            self.logger.error(f"获取最新交易日期失败: {str(e)}")
            # 如果失败，返回当前日期
            return datetime.now().strftime('%Y-%m-%d')