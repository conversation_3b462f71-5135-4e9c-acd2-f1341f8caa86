# 自动因子挖掘系统使用指南

本指南详细介绍如何使用DRL量化交易系统中的自动因子挖掘功能，包括功能概述、安装配置和使用方法。

## 1. 功能概述

自动因子挖掘系统是DRL量化交易系统的强大扩展，它可以：

- **自动生成交易因子**：基于技术指标、交叉特征和时序特征
- **评估因子有效性**：通过IC值和IR值等指标
- **选择最佳因子组合**：去除冗余因子，选取预测能力最强的因子
- **将因子应用于DRL模型**：无缝集成到模型训练流程

## 2. 安装配置

### 2.1 前置条件

确保您已安装以下依赖：

```bash
# 基础依赖
pip install -r requirements.txt

# 额外依赖
pip install pandas-ta scikit-learn statsmodels optuna
```

### 2.2 文件结构

自动因子挖掘系统由以下关键文件组成：

- `quant_project/core_logic/auto_factor_mining.py` - 核心挖掘逻辑
- `quant_project/auto_factor_mining_page.py` - UI界面实现
- `quant_project/test_auto_factor_mining.py` - 测试脚本
- `quant_project/verify_factor_mining.py` - 验证脚本

## 3. 启动系统

### 3.1 方式一：使用启动脚本（推荐）

```bash
cd quant_project
python start_system.py
```

然后在出现的菜单中选择选项2: "启动带自动因子挖掘的系统"

### 3.2 方式二：直接指定启动模式

```bash
cd quant_project
python start_system.py --mode factor_mining
```

### 3.3 方式三：直接运行主应用

```bash
cd quant_project
python -m streamlit run main_app_with_factor_mining.py --server.headless true
```

> 注意：添加 `--server.headless true` 参数可以在服务器上运行时阻止自动打开浏览器。在本地开发时可以省略此参数。

## 4. 使用指南

### 4.1 基本流程

自动因子挖掘系统的使用流程包括以下步骤：

1. 在导航栏中选择"自动因子挖掘"标签页
2. 配置因子挖掘参数
3. 执行因子挖掘
4. 评估和可视化因子
5. 应用选中的因子到DRL模型训练

### 4.2 因子挖掘配置

在"因子挖掘配置"标签页中配置以下参数：

| 参数 | 说明 | 推荐值 |
|------|------|--------|
| 股票代码 | 要分析的股票或指数代码 | sh000001（上证指数） |
| 开始日期 | 分析的起始日期 | 至少1年以上的历史 |
| 结束日期 | 分析的结束日期 | 当前日期 |
| 技术指标因子 | 是否生成技术指标因子 | 是 |
| 交叉因子 | 是否生成交叉因子 | 是 |
| 时序因子 | 是否生成时序因子 | 是 |
| 最大因子数量 | 生成的最大因子数量 | 100-200 |

### 4.3 因子评估

因子评估页面展示各因子的：
- IC值（信息系数）
- IR值（信息比率）
- 相关性矩阵
- 稳定性指标

### 4.4 因子可视化

可视化页面提供：
- 因子值随时间变化图表
- 因子与收益关系散点图
- 分位数分析图表
- 因子表现热力图

### 4.5 因子应用

选择最佳因子后，可以：
- 将因子导出为JSON或CSV格式
- 将选定因子直接应用到DRL模型训练
- 与基准策略进行对比分析

## 5. 最佳实践

- **数据质量**：使用高质量、无缺失的价格数据
- **因子种类**：尝试不同类型的因子组合
- **避免过拟合**：使用训练-测试分离验证因子有效性
- **定期更新**：根据市场变化定期更新因子

## 6. 故障排除

### 6.1 常见问题

- **ImportError**: 确保已安装所有依赖
- **内存错误**: 减少最大因子数量或使用更小的数据集
- **计算时间过长**: 对于大数据集，可以增加采样间隔

### 6.2 技术支持

如遇问题，请参考：
- 项目GitHub仓库的Issues页面
- 检查logs目录下的日志文件

## 7. 进阶功能

- **自定义因子创建**：扩展FactorGenerator类添加自定义因子
- **高级筛选策略**：修改FactorSelector的选择算法
- **因子组合优化**：实现更复杂的因子组合方法

---

**注意**：自动因子挖掘是计算密集型任务，对于大数据集可能需要较长处理时间。建议首先在小型数据集上测试系统功能。 