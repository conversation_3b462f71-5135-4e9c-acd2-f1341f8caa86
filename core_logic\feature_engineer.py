"""
特征工程模块
负责从原始行情数据计算技术指标和其他特征
"""

import logging
import pandas as pd
import numpy as np
import ta
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Try different import paths
try:
    from quant_project.core_logic.utils import normalize_data
except ImportError:
    try:
        from core_logic.utils import normalize_data
    except ImportError:
        # Define a simple normalize_data function as fallback
        def normalize_data(data, method='minmax'):
            """Fallback normalization function"""
            if isinstance(data, pd.DataFrame):
                data = data.copy()
                for col in data.columns:
                    try:
                        data.loc[:, col] = pd.to_numeric(data[col], errors='coerce')
                    except:
                        pass

                if method == 'minmax':
                    min_vals = data.min()
                    max_vals = data.max()
                    return (data - min_vals) / (max_vals - min_vals + 1e-8)
                elif method == 'zscore':
                    return (data - data.mean()) / (data.std() + 1e-8)
            else:
                if method == 'minmax':
                    return (data - np.nanmin(data)) / (np.nanmax(data) - np.nanmin(data) + 1e-8)
                elif method == 'zscore':
                    return (data - np.nanmean(data)) / (np.nanstd(data) + 1e-8)

            return data

class FeatureEngineer:
    """
    特征工程类
    负责从原始行情数据计算技术指标和其他特征
    """

    def __init__(self, feature_config=None):
        """
        初始化特征工程器

        参数:
            feature_config (dict): 特征配置字典，指定要计算的技术指标及其参数
        """
        self.logger = logging.getLogger('drl_trading')
        self.feature_config = feature_config or {}

    def generate_features(self, data):
        """
        生成特征

        参数:
            data (pandas.DataFrame): 原始行情数据，必须包含OHLCV列
        返回:
            pandas.DataFrame: 添加了特征的数据
        """
        # 复制数据，避免修改原始数据
        df = data.copy()

        # 确保列名标准化
        df = self._ensure_column_names(df)

        # 计算基本价格特征
        df = self._calculate_price_features(df)

        # 根据配置计算技术指标
        df = self._calculate_technical_indicators(df)

        # 计算统计特征
        df = self._calculate_statistical_features(df)

        # 归一化特征
        df = self._normalize_features(df)

        self.logger.info(f"特征生成完成，共 {len(df.columns)} 个特征")
        return df

    def _ensure_column_names(self, df):
        """
        确保列名标准化
        参数:
            df (pandas.DataFrame): 原始数据

        返回:
            pandas.DataFrame: 标准化列名后的数据
        """
        # 英文列名到中文列名的映射
        column_mapping = {
            'open': '开盘',
            'high': '最高',
            'low': '最低',
            'close': '收盘',
            'volume': '成交量'
        }

        # 检查并重命名列
        for eng, chn in column_mapping.items():
            if eng in df.columns and chn not in df.columns:
                df[chn] = df[eng]

        # 确保必要的列存在
        required_columns = ['开盘', '最高', '最低', '收盘', '成交量']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            self.logger.error(f"数据缺少必要的列: {missing_columns}")
            raise ValueError(f"数据缺少必要的列: {missing_columns}")

        return df

    def _calculate_price_features(self, df):
        """
        计算基本价格特征

        参数:
            df (pandas.DataFrame): 原始数据

        返回:
            pandas.DataFrame: 添加了价格特征的数据
        """
        # 确保价格列是数值类型
        price_columns = ['开盘', '最高', '最低', '收盘', '成交量']
        for col in price_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # 计算涨跌幅
        if '涨跌幅' not in df.columns:
            df['涨跌幅'] = df['收盘'].pct_change()

        # 计算真实波动幅度 (True Range)
        try:
            df['TR'] = ta.volatility.average_true_range(df['最高'], df['最低'], df['收盘'], window=1, fillna=True)
        except Exception as e:
            self.logger.warning(f"计算TR失败: {str(e)}，使用简单方法计算")
            # 使用简单方法计算TR
            high_low = df['最高'] - df['最低']
            high_close = (df['最高'] - df['收盘'].shift(1)).abs()
            low_close = (df['最低'] - df['收盘'].shift(1)).abs()
            df['TR'] = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)

        # 计算收盘价相对开盘价的变化
        df['日内涨跌幅'] = (df['收盘'] - df['开盘']) / df['开盘'].replace(0, np.nan)

        # 计算高低价差占开盘价的比例
        df['日内波动率'] = (df['最高'] - df['最低']) / df['开盘'].replace(0, np.nan)

        return df

    def _calculate_technical_indicators(self, df):
        """
        计算技术指标
        参数:
            df (pandas.DataFrame): 原始数据

        返回:
            pandas.DataFrame: 添加了技术指标的数据
        """
        # 简单移动平均线 (SMA)
        if self.feature_config.get('sma', {}).get('use', True):
            periods = self.feature_config.get('sma', {}).get('periods', [5, 20, 60])
            for period in periods:
                df[f'SMA_{period}'] = ta.trend.sma_indicator(df['收盘'], window=period, fillna=True)
                # 计算价格相对SMA的位置
                df[f'收盘/SMA_{period}'] = df['收盘'] / df[f'SMA_{period}']

        # 指数移动平均线(EMA)
        if self.feature_config.get('ema', {}).get('use', True):
            periods = self.feature_config.get('ema', {}).get('periods', [5, 20])
            for period in periods:
                df[f'EMA_{period}'] = ta.trend.ema_indicator(df['收盘'], window=period, fillna=True)

        # 相对强弱指标 (RSI)
        if self.feature_config.get('rsi', {}).get('use', True):
            period = self.feature_config.get('rsi', {}).get('period', 14)
            df[f'RSI_{period}'] = ta.momentum.rsi(df['收盘'], window=period, fillna=True)

        # MACD
        if self.feature_config.get('macd', {}).get('use', True):
            fast = self.feature_config.get('macd', {}).get('fast', 12)
            slow = self.feature_config.get('macd', {}).get('slow', 26)
            signal = self.feature_config.get('macd', {}).get('signal', 9)

            df[f'MACD_{fast}_{slow}'] = ta.trend.macd(df['收盘'], window_slow=slow, window_fast=fast, fillna=True)
            df[f'MACD_Signal_{signal}'] = ta.trend.macd_signal(df['收盘'], window_slow=slow, window_fast=fast, window_sign=signal, fillna=True)
            df[f'MACD_Hist'] = ta.trend.macd_diff(df['收盘'], window_slow=slow, window_fast=fast, window_sign=signal, fillna=True)

        # 布林带
        if self.feature_config.get('bbands', {}).get('use', True):
            period = self.feature_config.get('bbands', {}).get('period', 20)
            std = self.feature_config.get('bbands', {}).get('std', 2.0)

            df[f'BBands_Upper_{period}'] = ta.volatility.bollinger_hband(df['收盘'], window=period, window_dev=std, fillna=True)
            df[f'BBands_Middle_{period}'] = ta.volatility.bollinger_mavg(df['收盘'], window=period, fillna=True)
            df[f'BBands_Lower_{period}'] = ta.volatility.bollinger_lband(df['收盘'], window=period, window_dev=std, fillna=True)

            # 计算价格在布林带中的位置 (0-1)
            upper = df[f'BBands_Upper_{period}']
            lower = df[f'BBands_Lower_{period}']
            df[f'BBands_Position_{period}'] = (df['收盘'] - lower) / (upper - lower + 1e-10)

        # 平均真实波动幅度 (ATR)
        if self.feature_config.get('atr', {}).get('use', True):
            period = self.feature_config.get('atr', {}).get('period', 14)
            df[f'ATR_{period}'] = ta.volatility.average_true_range(
                df['最高'],
                df['最低'],
                df['收盘'],
                window=period,
                fillna=True
            )
            # 计算ATR占收盘价的比例
            df[f'ATR_{period}_Pct'] = df[f'ATR_{period}'] / df['收盘']

        # 随机指标 (Stochastic)
        if self.feature_config.get('stoch', {}).get('use', False):
            k_period = self.feature_config.get('stoch', {}).get('k_period', 14)
            d_period = self.feature_config.get('stoch', {}).get('d_period', 3)
            slowing = self.feature_config.get('stoch', {}).get('slowing', 3)

            df[f'Stoch_K_{k_period}'] = ta.momentum.stoch(df['最高'], df['最低'], df['收盘'], window=k_period, smooth_window=slowing, fillna=True)
            df[f'Stoch_D_{d_period}'] = ta.momentum.stoch_signal(df['最高'], df['最低'], df['收盘'], window=k_period, smooth_window=slowing, fillna=True)

        # 成交量指标
        if self.feature_config.get('volume', {}).get('use', False):
            # 成交量移动平均
            periods = self.feature_config.get('volume', {}).get('periods', [5, 20])
            for period in periods:
                df[f'Volume_SMA_{period}'] = ta.trend.sma_indicator(df['成交量'], window=period, fillna=True)
                # 计算成交量相对其移动平均的比例
                df[f'Volume/SMA_{period}'] = df['成交量'] / df[f'Volume_SMA_{period}']

            # 成交量变化率
            df['Volume_Change'] = df['成交量'].pct_change()

            # 能量潮指标(OBV)
            df['OBV'] = ta.volume.on_balance_volume(df['收盘'], df['成交量'], fillna=True)

        return df

    def _calculate_statistical_features(self, df):
        """
        计算统计特征

        参数:
            df (pandas.DataFrame): 原始数据

        返回:
            pandas.DataFrame: 添加了统计特征的数据
        """
        # 收益率的滚动统计特征
        if self.feature_config.get('rolling_stats', {}).get('use', False):
            window = self.feature_config.get('rolling_stats', {}).get('window', 20)
            # 滚动波动率
            df[f'Rolling_Volatility_{window}'] = df['涨跌幅'].rolling(window=window).std()
            # 滚动偏度
            df[f'Rolling_Skew_{window}'] = df['涨跌幅'].rolling(window=window).skew()
            # 滚动峰度
            df[f'Rolling_Kurt_{window}'] = df['涨跌幅'].rolling(window=window).kurt()

        return df

    def _normalize_features(self, df):
        """
        归一化特征
        参数:
            df (pandas.DataFrame): 原始数据

        返回:
            pandas.DataFrame: 归一化后的数据
        """
        # 保留原始价格数据
        price_columns = ['开盘', '最高', '最低', '收盘', '成交量']
        # 如果有成交额列，也加入
        if '成交额' in df.columns:
            price_columns.append('成交额')

        # 只保留存在的列
        existing_price_columns = [col for col in price_columns if col in df.columns]
        original_prices = df[existing_price_columns].copy()

        # 归一化其他特征列
        feature_columns = [col for col in df.columns if col not in price_columns]
        if feature_columns:
            df[feature_columns] = normalize_data(df[feature_columns], method='minmax')

        # 将原始价格数据放回
        for col in existing_price_columns:
            df[col] = original_prices[col]

        return df
