import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.distributions import Normal
from typing import Dict, List, Union, Tuple, Callable, Optional, Any
import logging
import os
import time
import gym
from collections import deque
import random

# Configure logger
logger = logging.getLogger(__name__)

class PolicyNetwork(nn.Module):
    """
    Policy network for continuous action spaces.
    """
    
    def __init__(self, 
                input_dim: int, 
                action_dim: int,
                hidden_dims: List[int] = [256, 128]):
        """
        Initialize the policy network.
        
        Args:
            input_dim: Dimension of the input features
            action_dim: Dimension of the action space
            hidden_dims: List of hidden layer dimensions
        """
        super(PolicyNetwork, self).__init__()
        
        # Create layers
        layers = []
        dims = [input_dim] + hidden_dims
        
        for i in range(len(dims) - 1):
            layers.append(nn.Linear(dims[i], dims[i+1]))
            layers.append(nn.ReLU())
            
        self.feature_extractor = nn.Sequential(*layers)
        
        # Output layers for mean and log_std of action distribution
        self.mean_layer = nn.Linear(dims[-1], action_dim)
        self.log_std_layer = nn.Linear(dims[-1], action_dim)
        
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass through the network.
        
        Args:
            x: Input tensor
            
        Returns:
            Tuple of (action mean, action log std)
        """
        features = self.feature_extractor(x)
        
        # Calculate mean and log_std
        mean = self.mean_layer(features)
        log_std = self.log_std_layer(features)
        
        # Clamp log_std for numerical stability
        log_std = torch.clamp(log_std, -20, 2)
        
        return mean, log_std
    
    def get_action(self, 
                  state: torch.Tensor, 
                  deterministic: bool = False) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """
        Sample action from the policy.
        
        Args:
            state: State tensor
            deterministic: Whether to sample deterministically
            
        Returns:
            Tuple of (action, log_prob, entropy)
        """
        mean, log_std = self.forward(state)
        std = log_std.exp()
        
        # Create normal distribution
        dist = Normal(mean, std)
        
        # Sample action
        if deterministic:
            action = mean
        else:
            action = dist.sample()
            
        # Calculate log probability and entropy
        log_prob = dist.log_prob(action).sum(dim=-1, keepdim=True)
        entropy = dist.entropy().sum(dim=-1, keepdim=True)
        
        return action, log_prob, entropy

class ValueNetwork(nn.Module):
    """
    Value network for estimating the value function.
    """
    
    def __init__(self, 
                input_dim: int,
                hidden_dims: List[int] = [256, 128]):
        """
        Initialize the value network.
        
        Args:
            input_dim: Dimension of the input features
            hidden_dims: List of hidden layer dimensions
        """
        super(ValueNetwork, self).__init__()
        
        # Create layers
        layers = []
        dims = [input_dim] + hidden_dims
        
        for i in range(len(dims) - 1):
            layers.append(nn.Linear(dims[i], dims[i+1]))
            layers.append(nn.ReLU())
            
        # Output layer
        layers.append(nn.Linear(dims[-1], 1))
        
        self.network = nn.Sequential(*layers)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Forward pass through the network.
        
        Args:
            x: Input tensor
            
        Returns:
            Value estimate
        """
        return self.network(x)

class PPOAgent:
    """
    Proximal Policy Optimization (PPO) agent for continuous action spaces.
    """
    
    def __init__(self, 
                state_dim: int,
                action_dim: int,
                hidden_dims: List[int] = [256, 128],
                lr_policy: float = 3e-4,
                lr_value: float = 3e-4,
                gamma: float = 0.99,
                gae_lambda: float = 0.95,
                clip_ratio: float = 0.2,
                value_coef: float = 0.5,
                entropy_coef: float = 0.01,
                max_grad_norm: float = 0.5,
                update_epochs: int = 10,
                device: str = None):
        """
        Initialize the PPO agent.
        
        Args:
            state_dim: Dimension of the state space
            action_dim: Dimension of the action space
            hidden_dims: List of hidden layer dimensions
            lr_policy: Learning rate for the policy network
            lr_value: Learning rate for the value network
            gamma: Discount factor
            gae_lambda: GAE lambda parameter
            clip_ratio: PPO clipping parameter
            value_coef: Value loss coefficient
            entropy_coef: Entropy loss coefficient
            max_grad_norm: Maximum gradient norm for clipping
            update_epochs: Number of epochs to update on the same batch
            device: Device to use for computation
        """
        # Set device
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = torch.device(device)
            
        # Create networks
        self.policy = PolicyNetwork(state_dim, action_dim, hidden_dims).to(self.device)
        self.value = ValueNetwork(state_dim, hidden_dims).to(self.device)
        
        # Create optimizers
        self.policy_optimizer = optim.Adam(self.policy.parameters(), lr=lr_policy)
        self.value_optimizer = optim.Adam(self.value.parameters(), lr=lr_value)
        
        # Store hyperparameters
        self.gamma = gamma
        self.gae_lambda = gae_lambda
        self.clip_ratio = clip_ratio
        self.value_coef = value_coef
        self.entropy_coef = entropy_coef
        self.max_grad_norm = max_grad_norm
        self.update_epochs = update_epochs
        
        # Initialize buffers
        self.states = []
        self.actions = []
        self.log_probs = []
        self.rewards = []
        self.values = []
        self.dones = []
        
        # For tracking training
        self.training_stats = {
            'policy_loss': [],
            'value_loss': [],
            'entropy': [],
            'approx_kl': [],
            'clip_fraction': []
        }
        
    def select_action(self, 
                     state: Union[np.ndarray, Dict], 
                     deterministic: bool = False) -> np.ndarray:
        """
        Select action based on the current policy.
        
        Args:
            state: Current state
            deterministic: Whether to select action deterministically
            
        Returns:
            Selected action
        """
        # Convert state to tensor and move to device
        if isinstance(state, dict):
            # Handle dictionary observation (from trading environment)
            # Flatten and concatenate market and account data
            market_data = state['market'].reshape(-1)
            account_data = state['account']
            state_tensor = np.concatenate([market_data, account_data])
        else:
            state_tensor = state
            
        state_tensor = torch.FloatTensor(state_tensor).unsqueeze(0).to(self.device)
        
        # Get action from policy
        with torch.no_grad():
            action, log_prob, entropy = self.policy.get_action(state_tensor, deterministic)
            value = self.value(state_tensor)
            
        # Add to buffers (only in training mode)
        if not deterministic:
            self.states.append(state_tensor)
            self.actions.append(action)
            self.log_probs.append(log_prob)
            self.values.append(value)
            
        # Return action as numpy array
        return action.cpu().numpy()[0]
    
    def store_transition(self, reward: float, done: bool):
        """
        Store reward and done flag from the environment.
        
        Args:
            reward: Reward received
            done: Whether the episode is done
        """
        self.rewards.append(reward)
        self.dones.append(done)
        
    def update(self) -> Dict[str, float]:
        """
        Update the agent using PPO.
        
        Returns:
            Dictionary with training statistics
        """
        # Check if we have enough data
        if len(self.states) == 0:
            logger.warning("No data to update agent")
            return {}
            
        # Convert lists to tensors
        states = torch.cat(self.states)
        actions = torch.cat(self.actions)
        old_log_probs = torch.cat(self.log_probs)
        values = torch.cat(self.values).detach()
        rewards = torch.FloatTensor(self.rewards).unsqueeze(1).to(self.device)
        dones = torch.FloatTensor(self.dones).unsqueeze(1).to(self.device)
        
        # Calculate returns and advantages using GAE
        returns = torch.zeros_like(rewards)
        advantages = torch.zeros_like(rewards)
        
        # Final next value
        with torch.no_grad():
            next_value = self.value(states[-1].unsqueeze(0)) if len(states) > 0 else torch.tensor([0.0]).to(self.device)
            
        # Calculate returns and advantages
        last_gae_lambda = 0
        for t in reversed(range(len(rewards))):
            if t == len(rewards) - 1:
                next_non_terminal = 1.0 - dones[t]
                next_value = next_value
            else:
                next_non_terminal = 1.0 - dones[t]
                next_value = values[t+1]
                
            delta = rewards[t] + self.gamma * next_value * next_non_terminal - values[t]
            last_gae_lambda = delta + self.gamma * self.gae_lambda * next_non_terminal * last_gae_lambda
            advantages[t] = last_gae_lambda
            
        # Returns = advantages + values
        returns = advantages + values
        
        # Normalize advantages
        advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)
        
        # Training statistics
        epoch_stats = {
            'policy_loss': 0,
            'value_loss': 0,
            'entropy': 0,
            'approx_kl': 0,
            'clip_fraction': 0
        }
        
        # Perform multiple epochs of updates
        for epoch in range(self.update_epochs):
            # Get new action probabilities and values
            _, new_log_probs, entropy = self.policy.get_action(states, deterministic=False)
            new_values = self.value(states)
            
            # Calculate ratio and clipped ratio
            ratio = torch.exp(new_log_probs - old_log_probs)
            clipped_ratio = torch.clamp(ratio, 1.0 - self.clip_ratio, 1.0 + self.clip_ratio)
            
            # Calculate surrogate losses
            surrogate1 = ratio * advantages
            surrogate2 = clipped_ratio * advantages
            policy_loss = -torch.min(surrogate1, surrogate2).mean()
            
            # Value loss
            value_loss = nn.MSELoss()(new_values, returns)
            
            # Entropy loss
            entropy_loss = -entropy.mean()
            
            # Total loss
            loss = policy_loss + self.value_coef * value_loss + self.entropy_coef * entropy_loss
            
            # Update policy
            self.policy_optimizer.zero_grad()
            self.value_optimizer.zero_grad()
            loss.backward()
            
            # Clip gradients
            nn.utils.clip_grad_norm_(self.policy.parameters(), self.max_grad_norm)
            nn.utils.clip_grad_norm_(self.value.parameters(), self.max_grad_norm)
            
            # Apply gradients
            self.policy_optimizer.step()
            self.value_optimizer.step()
            
            # Calculate statistics
            with torch.no_grad():
                approx_kl = ((old_log_probs - new_log_probs).abs()).mean().item()
                clip_fraction = ((ratio - 1.0).abs() > self.clip_ratio).float().mean().item()
                
            # Update statistics
            epoch_stats['policy_loss'] += policy_loss.item() / self.update_epochs
            epoch_stats['value_loss'] += value_loss.item() / self.update_epochs
            epoch_stats['entropy'] += entropy_loss.item() / self.update_epochs
            epoch_stats['approx_kl'] += approx_kl / self.update_epochs
            epoch_stats['clip_fraction'] += clip_fraction / self.update_epochs
            
        # Clear buffers
        self.states = []
        self.actions = []
        self.log_probs = []
        self.rewards = []
        self.values = []
        self.dones = []
        
        # Update training statistics
        for key, value in epoch_stats.items():
            self.training_stats[key].append(value)
            
        return epoch_stats
    
    def save_checkpoint(self, filepath: str) -> None:
        """
        Save agent checkpoint to file.
        
        Args:
            filepath: Path to save the checkpoint
        """
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        # Create checkpoint
        checkpoint = {
            'policy_state_dict': self.policy.state_dict(),
            'value_state_dict': self.value.state_dict(),
            'policy_optimizer_state_dict': self.policy_optimizer.state_dict(),
            'value_optimizer_state_dict': self.value_optimizer.state_dict(),
            'training_stats': self.training_stats
        }
        
        # Save checkpoint
        torch.save(checkpoint, filepath)
        logger.info(f"Agent checkpoint saved to {filepath}")
        
    def load_checkpoint(self, filepath: str) -> None:
        """
        Load agent checkpoint from file.
        
        Args:
            filepath: Path to the checkpoint file
        """
        if not os.path.exists(filepath):
            logger.error(f"Checkpoint file {filepath} not found")
            return
            
        # Load checkpoint
        checkpoint = torch.load(filepath, map_location=self.device)
        
        # Load state dictionaries
        self.policy.load_state_dict(checkpoint['policy_state_dict'])
        self.value.load_state_dict(checkpoint['value_state_dict'])
        self.policy_optimizer.load_state_dict(checkpoint['policy_optimizer_state_dict'])
        self.value_optimizer.load_state_dict(checkpoint['value_optimizer_state_dict'])
        
        # Load training statistics
        if 'training_stats' in checkpoint:
            self.training_stats = checkpoint['training_stats']
            
        logger.info(f"Agent checkpoint loaded from {filepath}")

class PPOTrainer:
    """
    Trainer for PPO agent.
    """
    
    def __init__(self, 
                env: gym.Env,
                agent: PPOAgent,
                num_steps: int = 2048,
                max_episodes: int = 1000,
                max_timesteps: int = 1000,
                save_freq: int = 20,
                log_freq: int = 1,
                checkpoint_dir: str = './checkpoints',
                verbose: bool = True):
        """
        Initialize the PPO trainer.
        
        Args:
            env: Gym environment
            agent: PPO agent
            num_steps: Number of steps to collect before updating
            max_episodes: Maximum number of episodes to train
            max_timesteps: Maximum timesteps per episode
            save_freq: Frequency of saving checkpoints (in episodes)
            log_freq: Frequency of logging (in episodes)
            checkpoint_dir: Directory to save checkpoints
            verbose: Whether to print verbose output
        """
        self.env = env
        self.agent = agent
        self.num_steps = num_steps
        self.max_episodes = max_episodes
        self.max_timesteps = max_timesteps
        self.save_freq = save_freq
        self.log_freq = log_freq
        self.checkpoint_dir = checkpoint_dir
        self.verbose = verbose
        
        # Create checkpoint directory
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        # For tracking training progress
        self.episode_rewards = []
        self.episode_lengths = []
        
    def train(self) -> Dict[str, List]:
        """
        Train the agent.
        
        Returns:
            Dictionary with training statistics
        """
        # For tracking progress
        total_steps = 0
        
        # Training loop
        for episode in range(1, self.max_episodes + 1):
            # Reset environment
            state = self.env.reset()
            
            # Initialize episode variables
            episode_reward = 0
            episode_length = 0
            
            # Episode loop
            for t in range(1, self.max_timesteps + 1):
                # Select action
                action = self.agent.select_action(state)
                
                # Take action in environment
                next_state, reward, done, info = self.env.step(action)
                
                # Store transition
                self.agent.store_transition(reward, done)
                
                # Update state and counters
                state = next_state
                episode_reward += reward
                episode_length += 1
                total_steps += 1
                
                # Update agent if we have collected enough steps
                if total_steps % self.num_steps == 0:
                    stats = self.agent.update()
                    
                    if self.verbose:
                        logger.info(f"Update at step {total_steps}")
                        for key, value in stats.items():
                            logger.info(f"  {key}: {value:.4f}")
                            
                # Check if episode is done
                if done or t >= self.max_timesteps:
                    break
                    
            # Store episode statistics
            self.episode_rewards.append(episode_reward)
            self.episode_lengths.append(episode_length)
            
            # Log progress
            if episode % self.log_freq == 0:
                avg_reward = np.mean(self.episode_rewards[-self.log_freq:])
                avg_length = np.mean(self.episode_lengths[-self.log_freq:])
                
                logger.info(f"Episode {episode}/{self.max_episodes}")
                logger.info(f"  Average Reward: {avg_reward:.2f}")
                logger.info(f"  Average Length: {avg_length:.2f}")
                logger.info(f"  Total Steps: {total_steps}")
                
            # Save checkpoint
            if episode % self.save_freq == 0:
                checkpoint_path = os.path.join(self.checkpoint_dir, f"agent_ep{episode}.pt")
                self.agent.save_checkpoint(checkpoint_path)
                
        # Final checkpoint
        final_checkpoint_path = os.path.join(self.checkpoint_dir, "agent_final.pt")
        self.agent.save_checkpoint(final_checkpoint_path)
        
        # Return statistics
        return {
            'episode_rewards': self.episode_rewards,
            'episode_lengths': self.episode_lengths,
            'policy_loss': self.agent.training_stats['policy_loss'],
            'value_loss': self.agent.training_stats['value_loss'],
            'entropy': self.agent.training_stats['entropy'],
            'approx_kl': self.agent.training_stats['approx_kl'],
            'clip_fraction': self.agent.training_stats['clip_fraction']
        }
    
    def evaluate(self, 
                num_episodes: int = 10,
                render: bool = True) -> Dict[str, float]:
        """
        Evaluate the agent.
        
        Args:
            num_episodes: Number of episodes to evaluate
            render: Whether to render the environment
            
        Returns:
            Dictionary with evaluation statistics
        """
        # For tracking evaluation
        eval_rewards = []
        eval_lengths = []
        
        # Evaluation loop
        for episode in range(1, num_episodes + 1):
            # Reset environment
            state = self.env.reset()
            
            # Initialize episode variables
            episode_reward = 0
            episode_length = 0
            
            # Episode loop
            done = False
            while not done:
                # Select action deterministically
                action = self.agent.select_action(state, deterministic=True)
                
                # Take action in environment
                next_state, reward, done, info = self.env.step(action)
                
                # Update state and counters
                state = next_state
                episode_reward += reward
                episode_length += 1
                
                # Render if requested
                if render:
                    self.env.render()
                    
                # Check if episode is done
                if done:
                    break
                    
            # Store episode statistics
            eval_rewards.append(episode_reward)
            eval_lengths.append(episode_length)
            
            if self.verbose:
                logger.info(f"Evaluation Episode {episode}/{num_episodes}")
                logger.info(f"  Reward: {episode_reward:.2f}")
                logger.info(f"  Length: {episode_length}")
                
        # Calculate statistics
        avg_reward = np.mean(eval_rewards)
        avg_length = np.mean(eval_lengths)
        
        logger.info(f"Evaluation Results ({num_episodes} episodes)")
        logger.info(f"  Average Reward: {avg_reward:.2f}")
        logger.info(f"  Average Length: {avg_length:.2f}")
        
        # Return statistics
        return {
            'avg_reward': avg_reward,
            'avg_length': avg_length,
            'rewards': eval_rewards,
            'lengths': eval_lengths
        }

class ReplayBuffer:
    """
    Replay buffer for storing transitions.
    """
    
    def __init__(self, capacity: int = 100000):
        """
        Initialize the replay buffer.
        
        Args:
            capacity: Maximum capacity of the buffer
        """
        self.capacity = capacity
        self.buffer = []
        self.position = 0
        
    def push(self, 
            state: np.ndarray,
            action: np.ndarray,
            reward: float,
            next_state: np.ndarray,
            done: bool):
        """
        Push a transition to the buffer.
        
        Args:
            state: Current state
            action: Action taken
            reward: Reward received
            next_state: Next state
            done: Whether the episode is done
        """
        if len(self.buffer) < self.capacity:
            self.buffer.append(None)
            
        self.buffer[self.position] = (state, action, reward, next_state, done)
        self.position = (self.position + 1) % self.capacity
        
    def sample(self, batch_size: int) -> Tuple:
        """
        Sample a batch of transitions from the buffer.
        
        Args:
            batch_size: Size of the batch to sample
            
        Returns:
            Tuple of (states, actions, rewards, next_states, dones)
        """
        batch = random.sample(self.buffer, batch_size)
        state, action, reward, next_state, done = map(np.stack, zip(*batch))
        return state, action, reward, next_state, done
    
    def __len__(self) -> int:
        """
        Get the current size of the buffer.
        
        Returns:
            Current size of the buffer
        """
        return len(self.buffer)

def flatten_dict_observation(observation: Dict) -> np.ndarray:
    """
    Flatten a dictionary observation for use with the agent.
    
    Args:
        observation: Dictionary observation
        
    Returns:
        Flattened observation array
    """
    # Extract market and account data
    market_data = observation['market'].reshape(-1)
    account_data = observation['account']
    
    # Concatenate into a single array
    return np.concatenate([market_data, account_data])

def process_state_for_drl(state: Dict) -> torch.Tensor:
    """
    Process state for DRL agent.
    
    Args:
        state: Dictionary state
        
    Returns:
        Processed state tensor
    """
    # Flatten state
    flat_state = flatten_dict_observation(state)
    
    # Convert to tensor
    return torch.FloatTensor(flat_state)

def calculate_state_dim(env: gym.Env) -> int:
    """
    Calculate state dimension for the environment.
    
    Args:
        env: Gym environment
        
    Returns:
        State dimension
    """
    # Get sample observation
    sample_obs = env.reset()
    
    # Check if observation is a dictionary
    if isinstance(sample_obs, dict):
        # For trading environment
        market_shape = sample_obs['market'].shape
        account_shape = sample_obs['account'].shape
        
        # Calculate total dimension
        market_dim = np.prod(market_shape)
        account_dim = np.prod(account_shape)
        
        return int(market_dim + account_dim)
    else:
        # Regular gym environment
        return int(np.prod(env.observation_space.shape))

def calculate_action_dim(env: gym.Env) -> int:
    """
    Calculate action dimension for the environment.
    
    Args:
        env: Gym environment
        
    Returns:
        Action dimension
    """
    return int(np.prod(env.action_space.shape)) 