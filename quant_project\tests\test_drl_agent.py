"""
DRL智能体测试脚本
用于测试DRL智能体的创建、训练、保存和加载功能
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core_logic.data_handler import DataHandler
from core_logic.feature_engineer import FeatureEngineer
from core_logic.trading_environment import TradingEnvironment
from core_logic.drl_agent import DRLAgent, MetricsCallback
from core_logic.utils import setup_logger

# 设置日志
logger = setup_logger(log_file='logs/test_drl.log')

def test_drl_agent_creation():
    """测试DRL智能体创建功能"""
    logger.info("开始测试DRL智能体创建功能")

    # 创建模拟数据
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    data = pd.DataFrame({
        '开盘': np.random.rand(len(dates)) * 100 + 3000,
        '最高': np.random.rand(len(dates)) * 100 + 3050,
        '最低': np.random.rand(len(dates)) * 100 + 2950,
        '收盘': np.random.rand(len(dates)) * 100 + 3000,
        '成交量': np.random.rand(len(dates)) * 1000000,
        'SMA_5': np.random.rand(len(dates)) * 100 + 3000,
        'SMA_20': np.random.rand(len(dates)) * 100 + 3000,
        'RSI_14': np.random.rand(len(dates)) * 100
    }, index=dates)

    # 创建环境配置
    env_config = {
        'df_processed_data': data,
        'initial_capital': 100000,
        'commission_rate': 0.0003,
        'min_hold_days': 3,
        'window_size': 20
    }

    # 创建智能体配置
    agent_configs = [
        # PPO配置
        {
            'algorithm': 'PPO',
            'policy_network': 'MlpPolicy',
            'learning_rate': 0.0003,
            'gamma': 0.99
        },
        # A2C配置
        {
            'algorithm': 'A2C',
            'policy_network': 'MlpPolicy',
            'learning_rate': 0.0003,
            'gamma': 0.99
        },
        # DQN配置
        {
            'algorithm': 'DQN',
            'policy_network': 'MlpPolicy',
            'learning_rate': 0.0003,
            'gamma': 0.99
        }
    ]

    results = []

    # 测试不同算法的智能体创建
    for i, agent_config in enumerate(agent_configs):
        try:
            logger.info(f"测试创建 {agent_config['algorithm']} 智能体")

            # 创建DRL智能体
            drl_agent = DRLAgent(env_config, agent_config)

            # 记录结果
            results.append({
                'algorithm': agent_config['algorithm'],
                'success': True
            })

            logger.info(f"{agent_config['algorithm']} 智能体创建成功")

        except Exception as e:
            logger.error(f"{agent_config['algorithm']} 智能体创建失败: {str(e)}")

            # 记录失败结果
            results.append({
                'algorithm': agent_config['algorithm'],
                'success': False,
                'error': str(e)
            })

    # 输出测试结果
    results_df = pd.DataFrame(results)
    print("\nDRL智能体创建测试结果:")
    print(results_df[['algorithm', 'success']])

    # 保存测试结果
    results_df.to_csv('tests/drl_agent_creation_results.csv', index=False)

    return results_df

def test_drl_agent_training():
    """测试DRL智能体训练功能"""
    logger.info("开始测试DRL智能体训练功能")

    # 创建模拟数据
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    data = pd.DataFrame({
        '开盘': np.random.rand(len(dates)) * 100 + 3000,
        '最高': np.random.rand(len(dates)) * 100 + 3050,
        '最低': np.random.rand(len(dates)) * 100 + 2950,
        '收盘': np.random.rand(len(dates)) * 100 + 3000,
        '成交量': np.random.rand(len(dates)) * 1000000,
        'SMA_5': np.random.rand(len(dates)) * 100 + 3000,
        'SMA_20': np.random.rand(len(dates)) * 100 + 3000,
        'RSI_14': np.random.rand(len(dates)) * 100
    }, index=dates)

    # 创建环境配置
    env_config = {
        'df_processed_data': data,
        'initial_capital': 100000,
        'commission_rate': 0.0003,
        'min_hold_days': 3,
        'window_size': 20
    }

    # 创建智能体配置 (使用PPO，因为它通常最稳定)
    agent_config = {
        'algorithm': 'PPO',
        'policy_network': 'MlpPolicy',
        'learning_rate': 0.0003,
        'gamma': 0.99,
        'n_steps': 64,  # 使用较小的值加速测试
        'batch_size': 32
    }

    try:
        # 创建DRL智能体
        drl_agent = DRLAgent(env_config, agent_config)

        # 创建回调
        metrics_callback = MetricsCallback()

        # 训练模型 (使用较少的步数加速测试)
        logger.info("开始训练模型 (1000步)")
        training_stats = drl_agent.train(total_timesteps=1000, callback_list=[metrics_callback], progress_bar=False)

        # 保存模型
        model_path = drl_agent.save_model(save_path="saved_models/test_model.zip")
        logger.info(f"模型已保存到: {model_path}")

        # 加载模型
        loaded_agent = DRLAgent.load_model(model_path)
        logger.info("模型加载成功")

        # 测试预测功能
        observation, _ = drl_agent.env.reset()
        action = drl_agent.predict_action(observation)
        logger.info(f"预测动作: {action}")

        # 获取训练指标
        metrics = metrics_callback.get_metrics()

        # 可视化训练过程
        if metrics['episode_rewards']:
            plt.figure(figsize=(10, 6))
            plt.plot(metrics['episode_rewards'])
            plt.title('训练过程中的回合奖励')
            plt.xlabel('回合')
            plt.ylabel('奖励')
            plt.grid(True)
            plt.savefig('tests/training_rewards.png')
            logger.info("训练奖励可视化已保存到 tests/training_rewards.png")

        print("\nDRL智能体训练测试结果: 成功")
        print(f"训练统计信息: {training_stats}")

        return True, training_stats

    except Exception as e:
        logger.error(f"DRL智能体训练失败: {str(e)}")
        print("\nDRL智能体训练测试结果: 失败")
        print(f"错误: {str(e)}")

        return False, str(e)

if __name__ == "__main__":
    # 确保测试目录存在
    os.makedirs('tests', exist_ok=True)
    os.makedirs('saved_models', exist_ok=True)

    # 运行测试
    print("开始测试DRL智能体功能...")

    # 测试DRL智能体创建
    creation_results = test_drl_agent_creation()

    # 测试DRL智能体训练
    training_success, training_results = test_drl_agent_training()

    # 总结
    if all(creation_results['success']) and training_success:
        print("\nDRL智能体功能测试全部通过！")
    else:
        print("\nDRL智能体功能测试存在问题，请查看日志获取详细信息。")
