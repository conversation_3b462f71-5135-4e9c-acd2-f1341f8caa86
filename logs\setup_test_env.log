2025-05-29 08:56:14,523 - setup_test_env - INFO - 开始设置测试环境
2025-05-29 08:56:14,524 - setup_test_env - INFO - 目录 'logs' 创建成功
2025-05-29 08:56:14,524 - setup_test_env - INFO - 目录 'data_cache' 创建成功
2025-05-29 08:56:14,524 - setup_test_env - INFO - 目录 'plots' 创建成功
2025-05-29 08:56:14,524 - setup_test_env - INFO - 目录 'test_results' 创建成功
2025-05-29 08:56:14,525 - setup_test_env - INFO - 目录 'api_test_results' 创建成功
2025-05-29 08:56:14,525 - setup_test_env - INFO - 目录 'test_reports' 创建成功
2025-05-29 08:56:14,525 - setup_test_env - INFO - 目录 'saved_models' 创建成功
2025-05-29 08:56:14,525 - setup_test_env - INFO - 所有测试脚本都存在
2025-05-29 08:56:14,525 - setup_test_env - INFO - 所有核心模块都存在
2025-05-29 08:56:14,526 - setup_test_env - INFO - 配置文件 'test_config.json' 创建成功
2025-05-29 08:56:14,526 - setup_test_env - INFO - 准备测试数据
2025-05-29 08:56:14,526 - setup_test_env - INFO - 数据缓存目录不为空，可能已有测试数据
2025-05-29 08:56:14,526 - setup_test_env - INFO - ==================================================
2025-05-29 08:56:14,526 - setup_test_env - INFO - 测试环境设置完成
2025-05-29 08:56:14,526 - setup_test_env - INFO - ==================================================
2025-05-29 08:56:14,526 - setup_test_env - INFO - 测试环境完整，可以开始测试
