"""
数据处理适配器模块
提供与原始数据处理类兼容的接口
"""

import os
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from .data_handler import DataHandler

class DataHandlerAdapter:
    """
    数据处理适配器类
    提供与原始数据处理类兼容的接口
    """

    def __init__(self, cache_dir='data_cache', timezone='Asia/Shanghai',
                 missing_value_strategy='ffill', outlier_detection=True,
                 data_validation=True, adjust_price=True):
        """
        初始化数据处理适配器

        参数:
            cache_dir (str): 缓存目录路径
            timezone (str): 时区设置，默认为'Asia/Shanghai'
            missing_value_strategy (str): 缺失值处理策略，可选'ffill'(前向填充),'bfill'(后向填充),'mean'(均值填充),'median'(中位数填充),'none'(不处理)
            outlier_detection (bool): 是否进行异常值检测
            data_validation (bool): 是否进行数据验证
            adjust_price (bool): 是否使用复权价格
        """
        self.logger = logging.getLogger('drl_trading')

        # 创建新的数据处理器
        self.data_handler = DataHandler(
            cache_dir=cache_dir,
            timezone=timezone,
            missing_value_strategy=missing_value_strategy,
            outlier_detection=outlier_detection,
            data_validation=data_validation,
            adjust_price=adjust_price
        )

        # 保存原始数据处理类的属性
        self.cache_dir = cache_dir
        self.timezone = timezone
        self.missing_value_strategy = missing_value_strategy
        self.outlier_detection = outlier_detection
        self.data_validation = data_validation
        self.adjust_price = adjust_price

        # 保存原始类中的指数映射表
        self.index_map = self.data_handler.fetcher.index_map

    def get_stock_data(self, stock_code, start_date, end_date, frequency='日线', use_cache=True, data_source=None):
        """
        获取金融数据（股票、指数）

        参数:
            stock_code (str): 金融产品代码，格式如下：
                - 股票: 'sh000001' 或 'sz399001'
                - 指数: 'index_000300'，如 'index_000300'
            start_date (str): 开始日期，格式为 'YYYY-MM-DD'
            end_date (str): 结束日期，格式为 'YYYY-MM-DD'
            frequency (str): 数据频率，可选 '日线', '周线', '月线', '小时线', '分钟线'
            use_cache (bool): 是否使用缓存
            data_source (str): 数据来源，可选 '股票', '指数', '基金'，默认为None

        返回:
            pandas.DataFrame: 金融数据
        """
        # 根据data_source修改stock_code前缀
        if data_source and not stock_code.startswith(('sh', 'sz', 'index_', 'fund_')):
            if data_source == '指数':
                if not stock_code.startswith('index_'):
                    stock_code = f"index_{stock_code}"
            elif data_source == '基金':
                if not stock_code.startswith('fund_'):
                    stock_code = f"fund_{stock_code}"
            elif data_source == '股票':
                if not (stock_code.startswith('sh') or stock_code.startswith('sz')):
                    # 根据股票代码特点自动判断市场
                    if stock_code.startswith('60') or stock_code.startswith('68'):
                        stock_code = f"sh{stock_code}"
                    elif stock_code.startswith('00') or stock_code.startswith('30'):
                        stock_code = f"sz{stock_code}"
                    else:
                        stock_code = f"sh{stock_code}"

        # 频率映射：支持中文和英文频率参数
        freq_map_to_chinese = {
            'D': '日线',
            'W': '周线',
            'M': '月线',
            'H': '小时线',
            'min': '分钟线',
            'daily': '日线',
            'weekly': '周线',
            'monthly': '月线',
            'hourly': '小时线',
            'minute': '分钟线'
        }

        freq_map_to_english = {
            '日线': 'D',
            '周线': 'W',
            '月线': 'M',
            '小时线': 'H',
            '分钟线': 'min'
        }

        # 统一转换为中文格式，因为后端数据处理器期望中文参数
        if frequency in freq_map_to_chinese:
            # 如果是英文格式，转换为中文
            chinese_freq = freq_map_to_chinese[frequency]
            self.logger.info(f"频率参数从英文 '{frequency}' 转换为中文 '{chinese_freq}'")
        elif frequency in freq_map_to_english.keys():
            # 如果已经是中文格式，直接使用
            chinese_freq = frequency
            self.logger.info(f"使用中文频率参数: '{chinese_freq}'")
        else:
            # 未知格式，使用原始值并记录警告
            chinese_freq = frequency
            self.logger.warning(f"未识别的频率格式: '{frequency}'，将直接传递给后端")

        # 调用原始数据处理器的方法
        try:
            self.logger.info(f"获取数据: code={stock_code}, freq={chinese_freq}, source={data_source}")
            return self.data_handler.get_stock_data(stock_code, start_date, end_date, chinese_freq, use_cache)
        except Exception as e:
            self.logger.error(f"获取数据失败: {str(e)}")
            # 提供更详细的错误信息
            error_message = f"无法获取数据，代码: {stock_code}, 频率: {frequency}"
            if frequency != chinese_freq:
                error_message += f" (转换为: {chinese_freq})"
            error_message += f", 原因: {str(e)}"
            raise ValueError(error_message)

    def _generate_cache_key(self, stock_code, start_date, end_date, frequency):
        """
        生成缓存键

        参数:
            stock_code (str): 金融产品代码
            start_date (str): 开始日期
            end_date (str): 结束日期
            frequency (str): 数据频率

        返回:
            str: 缓存键
        """
        return self.data_handler.cache._generate_cache_key(stock_code, start_date, end_date, frequency)

    def _cache_data(self, data, stock_code, start_date, end_date, frequency):
        """
        缓存数据

        参数:
            data (pandas.DataFrame): 要缓存的数据
            stock_code (str): 金融产品代码
            start_date (str): 开始日期
            end_date (str): 结束日期
            frequency (str): 数据频率

        返回:
            bool: 缓存是否成功
        """
        return self.data_handler.cache.cache_data(data, stock_code, start_date, end_date, frequency)

    def _resample_data(self, data, freq):
        """
        重采样数据

        参数:
            data (pandas.DataFrame): 原始数据
            freq (str): 重采样频率，'W'表示周，'M'表示月

        返回:
            pandas.DataFrame: 重采样后的数据
        """
        return self.data_handler._resample_data(data, freq)

    def get_data_quality_report(self, data):
        """
        获取数据质量报告

        参数:
            data (pandas.DataFrame): 金融数据

        返回:
            dict: 数据质量报告
        """
        return self.data_handler.get_data_quality_report(data)

    def get_cache_stats(self):
        """
        获取缓存统计信息

        返回:
            dict: 缓存统计信息
        """
        return self.data_handler.get_cache_stats()

    def clear_cache(self):
        """
        清理过期缓存

        返回:
            int: 清理的缓存文件数量
        """
        return self.data_handler.clear_cache()

    def get_latest_trading_date(self):
        """
        获取最新交易日期

        返回:
            str: 最新交易日期，格式为 'YYYY-MM-DD'
        """
        return self.data_handler.get_latest_trading_date()
