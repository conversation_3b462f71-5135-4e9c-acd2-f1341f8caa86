#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
自动因子挖掘模块测试脚本
测试自动因子挖掘流水线的各项功能
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)
sys.path.insert(0, os.path.dirname(current_dir))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/factor_mining_test.log')
    ]
)

logger = logging.getLogger('factor_mining_test')

# 导入模块
try:
    from core_logic.auto_factor_mining import (
        FactorGenerator, 
        FactorEvaluator, 
        FactorSelector, 
        AutoFactorPipeline,
        AdaptiveFactorSystem
    )
    from core_logic.data_handler import DataHandler
    
    logger.info("成功导入自动因子挖掘模块")
except ImportError as e:
    logger.error(f"导入自动因子挖掘模块失败: {str(e)}")
    sys.exit(1)


def test_factor_generator(data_handler):
    """测试因子生成器"""
    logger.info("=" * 50)
    logger.info("测试因子生成器")
    logger.info("=" * 50)
    
    try:
        # 获取测试数据
        stock_code = 'sh000001'  # 上证指数
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=500)).strftime('%Y-%m-%d')
        
        data = data_handler.get_stock_data(
            stock_code=stock_code,
            start_date=start_date,
            end_date=end_date
        )
        
        logger.info(f"获取到 {len(data)} 行数据")
        
        # 初始化因子生成器
        generator = FactorGenerator(data_handler)
        
        # 生成技术指标因子
        technical_factors = generator.generate_technical_factors(data)
        logger.info(f"生成 {len(technical_factors)} 个技术指标因子")
        logger.info(f"技术指标因子样例: {list(technical_factors.keys())[:5]}")
        
        # 生成交叉因子
        cross_factors = generator.generate_cross_factors(technical_factors)
        logger.info(f"生成 {len(cross_factors)} 个交叉因子")
        logger.info(f"交叉因子样例: {list(cross_factors.keys())[:5]}")
        
        # 生成时序因子
        time_factors = generator.generate_time_factors(technical_factors)
        logger.info(f"生成 {len(time_factors)} 个时序因子")
        logger.info(f"时序因子样例: {list(time_factors.keys())[:5]}")
        
        # 合并所有因子
        all_factors = {**technical_factors, **cross_factors, **time_factors}
        logger.info(f"总计生成 {len(all_factors)} 个因子")
        
        # 检查因子数据质量
        nan_counts = {name: pd.isna(factor).sum() for name, factor in all_factors.items()}
        high_nan_factors = {name: count for name, count in nan_counts.items() if count > len(data) * 0.3}
        
        logger.info(f"数据缺失严重的因子数量: {len(high_nan_factors)}")
        if high_nan_factors:
            logger.info(f"前五个缺失严重的因子样例: {list(high_nan_factors.items())[:5]}")
            
        return True, all_factors
        
    except Exception as e:
        logger.error(f"因子生成器测试失败: {str(e)}")
        return False, {}


def test_factor_evaluator(data_handler, factors=None):
    """测试因子评估器"""
    logger.info("=" * 50)
    logger.info("测试因子评估器")
    logger.info("=" * 50)
    
    try:
        # 获取测试数据
        stock_code = 'sh000001'  # 上证指数
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=500)).strftime('%Y-%m-%d')
        
        data = data_handler.get_stock_data(
            stock_code=stock_code,
            start_date=start_date,
            end_date=end_date
        )
        
        # 如果没有提供因子，生成一些测试因子
        if factors is None or not factors:
            generator = FactorGenerator(data_handler)
            factors = generator.generate_technical_factors(data)
            logger.info(f"为评估器测试生成了 {len(factors)} 个因子")
        
        # 初始化因子评估器
        evaluator = FactorEvaluator(data)
        
        # 限制因子数量，避免测试时间过长
        if len(factors) > 50:
            factors = dict(list(factors.items())[:50])
            logger.info(f"为加速测试，限制因子数量为 {len(factors)}")
        
        # 评估因子
        valid_factors, evaluation_results = evaluator.evaluate_factors(factors, min_ic_abs=0.03)
        
        logger.info(f"评估了 {len(factors)} 个因子，其中 {len(valid_factors)} 个为有效因子")
        
        # 打印部分评估结果
        if evaluation_results:
            top_factors = evaluator.rank_factors(evaluation_results)[:5]
            logger.info("顶级因子评估结果:")
            for name, result in top_factors:
                logger.info(f"  {name}: IC均值={result['ic_mean']:.4f}, IR={result['ir']:.4f}")
        
        # 测试排序功能
        ranked_factors = evaluator.rank_factors(evaluation_results)
        logger.info(f"因子排序结果有 {len(ranked_factors)} 项")
        
        # 测试可视化
        if len(evaluation_results) > 0:
            fig = evaluator.plot_factor_evaluation(evaluation_results, top_n=10)
            fig.savefig('plots/factor_evaluation.png')
            logger.info("因子评估可视化图表已保存到 plots/factor_evaluation.png")
        
        return True, valid_factors, evaluation_results
    
    except Exception as e:
        logger.error(f"因子评估器测试失败: {str(e)}")
        return False, {}, {}


def test_factor_selector(valid_factors=None, evaluation_results=None):
    """测试因子选择器"""
    logger.info("=" * 50)
    logger.info("测试因子选择器")
    logger.info("=" * 50)
    
    try:
        if not valid_factors or not evaluation_results:
            logger.warning("未提供有效因子或评估结果，跳过因子选择器测试")
            return False, {}
        
        # 初始化因子选择器
        selector = FactorSelector()
        
        # 去除高相关因子
        decorrelated_factors = selector.remove_highly_correlated(valid_factors, threshold=0.7)
        logger.info(f"去除高相关因子后，从 {len(valid_factors)} 个减少到 {len(decorrelated_factors)} 个")
        
        # 选择最佳因子
        best_factors = selector.select_best_factors(decorrelated_factors, evaluation_results, top_n=10)
        logger.info(f"选择了 {len(best_factors)} 个最佳因子")
        logger.info(f"最佳因子列表: {list(best_factors.keys())}")
        
        # 组合因子
        combined_factor = selector.combine_factors(best_factors, method='equal_weight')
        logger.info(f"成功组合因子，组合因子长度: {len(combined_factor)}")
        
        return True, best_factors
    
    except Exception as e:
        logger.error(f"因子选择器测试失败: {str(e)}")
        return False, {}


def test_auto_factor_pipeline(data_handler):
    """测试自动因子挖掘流水线"""
    logger.info("=" * 50)
    logger.info("测试自动因子挖掘流水线")
    logger.info("=" * 50)
    
    try:
        # 初始化流水线
        pipeline = AutoFactorPipeline(data_handler)
        
        # 运行流水线
        stock_code = 'sh000001'  # 上证指数
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=500)).strftime('%Y-%m-%d')
        
        best_factors, evaluation_results, stats = pipeline.run(
            stock_code=stock_code,
            start_date=start_date,
            end_date=end_date,
            min_ic_abs=0.03,
            corr_threshold=0.7,
            top_n_factors=15
        )
        
        # 打印结果统计
        logger.info("流水线统计信息:")
        logger.info(f"  初始因子数量: {stats['initial_factors']}")
        logger.info(f"  有效因子数量: {stats['valid_factors']}")
        logger.info(f"  去相关后因子数量: {stats['decorrelated_factors']}")
        logger.info(f"  最终因子数量: {stats['final_factors']}")
        logger.info(f"  最佳因子列表: {stats['best_factors']}")
        
        return True, best_factors, evaluation_results, stats
    
    except Exception as e:
        logger.error(f"自动因子挖掘流水线测试失败: {str(e)}")
        return False, {}, {}, {}


def main():
    """主函数"""
    # 确保输出目录存在
    os.makedirs('logs', exist_ok=True)
    os.makedirs('plots', exist_ok=True)
    
    logger.info("开始自动因子挖掘模块测试")
    
    # 初始化数据处理器
    data_handler = DataHandler(cache_dir='data_cache')
    
    # 测试因子生成器
    success_generator, factors = test_factor_generator(data_handler)
    
    # 如果因子生成成功，继续测试因子评估器
    if success_generator and factors:
        success_evaluator, valid_factors, evaluation_results = test_factor_evaluator(data_handler, factors)
    else:
        logger.error("因子生成器测试失败，无法继续测试")
        return False
    
    # 如果因子评估成功，继续测试因子选择器
    if success_evaluator and valid_factors and evaluation_results:
        success_selector, best_factors = test_factor_selector(valid_factors, evaluation_results)
    else:
        logger.error("因子评估器测试失败，无法继续测试")
        return False
    
    # 测试自动因子挖掘流水线
    success_pipeline, pipeline_factors, pipeline_evaluations, pipeline_stats = test_auto_factor_pipeline(data_handler)
    
    # 测试自适应因子系统
    if success_pipeline:
        success_adaptive = test_adaptive_factor_system(data_handler, pipeline_factors)
    else:
        logger.warning("自动因子挖掘流水线测试失败，将使用选择器的最佳因子测试自适应系统")
        success_adaptive = test_adaptive_factor_system(data_handler, best_factors if success_selector else {})
    
    # 打印测试结果汇总
    logger.info("=" * 50)
    logger.info("自动因子挖掘模块测试完成")
    logger.info("=" * 50)
    logger.info("测试结果汇总:")
    logger.info(f"  因子生成器: {'成功' if success_generator else '失败'}")
    logger.info(f"  因子评估器: {'成功' if success_evaluator else '失败'}")
    logger.info(f"  因子选择器: {'成功' if success_selector else '失败'}")
    logger.info(f"  自动因子挖掘流水线: {'成功' if success_pipeline else '失败'}")
    logger.info(f"  自适应因子系统: {'成功' if success_adaptive else '失败'}")
    
    # 返回测试结果
    overall_success = success_generator and success_evaluator and success_selector and success_pipeline and success_adaptive
    logger.info(f"总体测试结果: {'成功' if overall_success else '失败'}")
    
    return overall_success


def test_adaptive_factor_system(data_handler, best_factors=None):
    """测试自适应因子系统"""
    logger.info("=" * 50)
    logger.info("测试自适应因子系统")
    logger.info("=" * 50)
    
    try:
        # 初始化自适应因子系统
        adaptive_system = AdaptiveFactorSystem(data_handler)
        
        # 测试更新因子
        stock_code = 'sh000001'  # 上证指数
        if best_factors is None or not best_factors:
            # 如果没有提供最佳因子，则运行更新因子
            updated_factors, evaluations, stats = adaptive_system.update_factors(
                stock_code=stock_code,
                lookback_days=365,
                min_ic_abs=0.03,
                top_n=10
            )
            
            logger.info(f"更新因子完成，获取到 {len(updated_factors)} 个最佳因子")
            logger.info(f"因子列表: {list(updated_factors.keys())}")
        else:
            updated_factors = best_factors
            logger.info(f"使用提供的 {len(best_factors)} 个因子进行测试")
        
        # 测试因子导出
        factors_dir = 'test_results'
        os.makedirs(factors_dir, exist_ok=True)
        export_path = f"{factors_dir}/test_factors_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl"
        
        export_success = adaptive_system.export_factors(updated_factors, export_path)
        logger.info(f"因子导出测试: {'成功' if export_success else '失败'}")
        
        # 测试因子导入
        if export_success:
            imported_factors = adaptive_system.import_factors(export_path)
            import_success = len(imported_factors) > 0
            logger.info(f"因子导入测试: {'成功' if import_success else '失败'}")
            logger.info(f"成功导入 {len(imported_factors)} 个因子")
        
        # 测试模拟模型的因子应用
        # 创建一个模拟模型类
        class MockModel:
            def __init__(self):
                self.feature_processor = type('obj', (object,), {
                    'features': {},
                    'add_features': lambda self, features: self.features.update(features)
                })()
                self.feature_processor.features = {}
                self.observation_space = None
            
            def get_observation_space(self):
                return None
        
        # 创建模拟模型
        mock_model = MockModel()
        
        # 应用因子到模型
        updated_model = adaptive_system.apply_factors_to_model(mock_model, updated_factors)
        
        # 检查因子是否已应用
        model_has_factors = (hasattr(updated_model.feature_processor, 'features') and 
                            len(updated_model.feature_processor.features) > 0)
        
        logger.info(f"因子应用到模型测试: {'成功' if model_has_factors else '失败'}")
        
        # 测试结果
        success = export_success and (import_success if export_success else False) and model_has_factors
        
        return success
    
    except Exception as e:
        logger.error(f"自适应因子系统测试失败: {str(e)}")
        return False


if __name__ == "__main__":
    main() 