"""
UI问题修复脚本

该脚本分析UI测试结果，并自动修复常见问题。
"""

import os
import sys
import json
import logging
import re
import glob
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(parent_dir, 'logs', 'ui_fix.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('ui_fix')

class UIFixer:
    """UI问题修复类"""
    
    def __init__(self):
        """初始化"""
        self.issues = []
        self.fixes = []
        self.test_results = None
        self.main_app_path = os.path.join(parent_dir, 'main_app.py')
    
    def load_test_results(self):
        """加载最新的测试结果"""
        logger.info("加载最新的测试结果")
        
        # 查找最新的测试结果文件
        reports_dir = os.path.join(parent_dir, 'test_reports', 'ui_tests')
        json_files = glob.glob(os.path.join(reports_dir, 'test_results_*.json'))
        
        if not json_files:
            logger.error("未找到测试结果文件")
            return False
        
        # 按修改时间排序，获取最新的文件
        latest_file = max(json_files, key=os.path.getmtime)
        logger.info(f"找到最新的测试结果文件: {latest_file}")
        
        # 加载测试结果
        try:
            with open(latest_file, 'r', encoding='utf-8') as f:
                self.test_results = json.load(f)
            
            logger.info(f"成功加载测试结果: {self.test_results['timestamp']}")
            return True
        except Exception as e:
            logger.error(f"加载测试结果失败: {str(e)}")
            return False
    
    def analyze_issues(self):
        """分析测试结果中的问题"""
        logger.info("分析测试结果中的问题")
        
        if not self.test_results:
            logger.error("未加载测试结果，无法分析问题")
            return False
        
        # 分析失败的测试
        for failure in self.test_results.get('failures', []):
            self.analyze_failure(failure)
        
        # 分析错误的测试
        for error in self.test_results.get('error_details', []):
            self.analyze_error(error)
        
        logger.info(f"分析完成，发现 {len(self.issues)} 个问题")
        return True
    
    def analyze_failure(self, failure):
        """分析失败的测试"""
        test_name = failure['test']
        message = failure['message']
        
        logger.info(f"分析失败的测试: {test_name}")
        
        # 检查常见的UI问题
        if "等待元素超时" in message:
            # 元素定位问题
            match = re.search(r"等待元素超时: (By\.\w+)=(.+)", message)
            if match:
                by_type, selector = match.groups()
                self.issues.append({
                    'type': 'element_timeout',
                    'test': test_name,
                    'by_type': by_type,
                    'selector': selector,
                    'message': message
                })
        elif "AssertionError" in message:
            # 断言失败问题
            self.issues.append({
                'type': 'assertion_error',
                'test': test_name,
                'message': message
            })
        else:
            # 其他问题
            self.issues.append({
                'type': 'unknown_failure',
                'test': test_name,
                'message': message
            })
    
    def analyze_error(self, error):
        """分析错误的测试"""
        test_name = error['test']
        message = error['message']
        
        logger.info(f"分析错误的测试: {test_name}")
        
        # 检查常见的错误
        if "NoSuchElementException" in message:
            # 元素不存在问题
            self.issues.append({
                'type': 'element_not_found',
                'test': test_name,
                'message': message
            })
        elif "TimeoutException" in message:
            # 超时问题
            self.issues.append({
                'type': 'timeout',
                'test': test_name,
                'message': message
            })
        else:
            # 其他错误
            self.issues.append({
                'type': 'unknown_error',
                'test': test_name,
                'message': message
            })
    
    def fix_issues(self):
        """修复发现的问题"""
        logger.info("开始修复问题")
        
        if not self.issues:
            logger.info("没有需要修复的问题")
            return True
        
        # 读取main_app.py文件内容
        try:
            with open(self.main_app_path, 'r', encoding='utf-8') as f:
                main_app_content = f.read()
        except Exception as e:
            logger.error(f"读取main_app.py失败: {str(e)}")
            return False
        
        # 修复每个问题
        for issue in self.issues:
            if issue['type'] == 'element_timeout' or issue['type'] == 'element_not_found':
                self.fix_element_issue(issue, main_app_content)
            elif issue['type'] == 'timeout':
                self.fix_timeout_issue(issue, main_app_content)
            elif issue['type'] == 'assertion_error':
                self.fix_assertion_issue(issue, main_app_content)
            else:
                logger.warning(f"未知问题类型，无法自动修复: {issue['type']}")
        
        # 应用修复
        if self.fixes:
            return self.apply_fixes()
        else:
            logger.info("没有需要应用的修复")
            return True
    
    def fix_element_issue(self, issue, content):
        """修复元素定位问题"""
        logger.info(f"修复元素定位问题: {issue['test']}")
        
        # 根据测试名称确定相关功能区域
        if "data_acquisition" in issue['test']:
            self.fixes.append({
                'type': 'suggestion',
                'message': f"数据获取功能中的元素定位问题: {issue.get('message', '')}"
            })
        elif "feature_engineering" in issue['test']:
            self.fixes.append({
                'type': 'suggestion',
                'message': f"特征工程功能中的元素定位问题: {issue.get('message', '')}"
            })
        elif "environment_config" in issue['test']:
            self.fixes.append({
                'type': 'suggestion',
                'message': f"环境配置功能中的元素定位问题: {issue.get('message', '')}"
            })
        elif "drl_agent_training" in issue['test']:
            self.fixes.append({
                'type': 'suggestion',
                'message': f"DRL智能体训练功能中的元素定位问题: {issue.get('message', '')}"
            })
        else:
            self.fixes.append({
                'type': 'suggestion',
                'message': f"未知功能区域的元素定位问题: {issue.get('message', '')}"
            })
    
    def fix_timeout_issue(self, issue, content):
        """修复超时问题"""
        logger.info(f"修复超时问题: {issue['test']}")
        
        self.fixes.append({
            'type': 'suggestion',
            'message': f"页面加载超时问题: {issue.get('message', '')}"
        })
    
    def fix_assertion_issue(self, issue, content):
        """修复断言失败问题"""
        logger.info(f"修复断言失败问题: {issue['test']}")
        
        self.fixes.append({
            'type': 'suggestion',
            'message': f"断言失败问题: {issue.get('message', '')}"
        })
    
    def apply_fixes(self):
        """应用修复"""
        logger.info("应用修复")
        
        # 生成修复报告
        self.generate_fix_report()
        
        return True
    
    def generate_fix_report(self):
        """生成修复报告"""
        logger.info("生成修复报告")
        
        # 确保报告目录存在
        reports_dir = os.path.join(parent_dir, 'test_reports')
        os.makedirs(reports_dir, exist_ok=True)
        
        # 创建修复报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_content = f"""
# UI问题修复报告

**生成时间**: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## 问题摘要

发现 {len(self.issues)} 个问题，生成 {len(self.fixes)} 个修复建议。

## 问题详情

{self.generate_issue_details()}

## 修复建议

{self.generate_fix_suggestions()}

## 后续步骤

1. 检查并实施上述修复建议
2. 重新运行UI测试验证修复效果
3. 如果问题仍然存在，考虑手动检查相关UI组件

"""
        
        # 保存修复报告
        report_path = os.path.join(reports_dir, f"ui_fix_report_{timestamp}.md")
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        logger.info(f"修复报告已保存到: {report_path}")
    
    def generate_issue_details(self):
        """生成问题详情"""
        if not self.issues:
            return "无问题"
        
        details = ""
        for i, issue in enumerate(self.issues):
            details += f"{i+1}. **{issue['type']}** ({issue['test']})\n   - {issue.get('message', '无详细信息')}\n\n"
        
        return details
    
    def generate_fix_suggestions(self):
        """生成修复建议"""
        if not self.fixes:
            return "无修复建议"
        
        suggestions = ""
        for i, fix in enumerate(self.fixes):
            suggestions += f"{i+1}. **{fix['type']}**\n   - {fix.get('message', '无详细信息')}\n\n"
        
        return suggestions

def main():
    """主函数"""
    logger.info("=" * 80)
    logger.info("UI问题修复脚本")
    logger.info("=" * 80)
    
    # 创建UI修复器
    fixer = UIFixer()
    
    # 加载测试结果
    if not fixer.load_test_results():
        logger.error("加载测试结果失败，无法继续")
        sys.exit(1)
    
    # 分析问题
    if not fixer.analyze_issues():
        logger.error("分析问题失败，无法继续")
        sys.exit(1)
    
    # 修复问题
    if not fixer.fix_issues():
        logger.error("修复问题失败")
        sys.exit(1)
    
    logger.info("UI问题修复完成")
    sys.exit(0)

if __name__ == "__main__":
    main()
