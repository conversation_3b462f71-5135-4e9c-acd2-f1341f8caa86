"""
数据缓存模块
负责缓存和管理金融数据
"""

import os
import logging
import pandas as pd
import numpy as np
import hashlib
import json
import time
from datetime import datetime, timedelta

class DataCache:
    """
    数据缓存类
    负责缓存和管理金融数据，提高数据获取效率
    """

    def __init__(self, cache_dir='data_cache', max_age_days=7, logger=None):
        """
        初始化数据缓存器

        参数:
            cache_dir (str): 缓存目录路径
            max_age_days (int): 缓存数据的最大有效期（天）
            logger (logging.Logger): 日志记录器
        """
        self.cache_dir = cache_dir
        self.max_age_days = max_age_days
        self.logger = logger or logging.getLogger('drl_trading')

        # 确保缓存目录存在
        os.makedirs(self.cache_dir, exist_ok=True)

        # 缓存元数据
        self.metadata_file = os.path.join(self.cache_dir, 'cache_metadata.json')
        self.metadata = self._load_metadata()

    def _load_metadata(self):
        """
        加载缓存元数据

        返回:
            dict: 缓存元数据
        """
        if os.path.exists(self.metadata_file):
            try:
                with open(self.metadata_file, 'r') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.error(f"加载缓存元数据失败: {str(e)}")
                return {}
        else:
            return {}

    def _save_metadata(self):
        """
        保存缓存元数据
        """
        try:
            with open(self.metadata_file, 'w') as f:
                json.dump(self.metadata, f, indent=2)
        except Exception as e:
            self.logger.error(f"保存缓存元数据失败: {str(e)}")

    def _generate_cache_key(self, stock_code, start_date, end_date, frequency):
        """
        生成缓存键

        参数:
            stock_code (str): 金融产品代码
            start_date (str): 开始日期
            end_date (str): 结束日期
            frequency (str): 数据频率

        返回:
            str: 缓存键
        """
        # 创建一个唯一的缓存键
        key_string = f"{stock_code}_{start_date}_{end_date}_{frequency}"
        return hashlib.md5(key_string.encode()).hexdigest()

    def get_cached_data(self, stock_code, start_date, end_date, frequency):
        """
        获取缓存数据

        参数:
            stock_code (str): 金融产品代码
            start_date (str): 开始日期
            end_date (str): 结束日期
            frequency (str): 数据频率

        返回:
            pandas.DataFrame: 缓存的数据，如果缓存不存在或已过期则返回None
        """
        cache_key = self._generate_cache_key(stock_code, start_date, end_date, frequency)
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.csv")

        # 检查缓存文件是否存在
        if not os.path.exists(cache_file):
            self.logger.info(f"缓存文件不存在: {cache_file}")
            return None

        # 检查缓存是否过期
        if cache_key in self.metadata:
            cache_time = self.metadata[cache_key].get('timestamp', 0)
            cache_age = (time.time() - cache_time) / (24 * 3600)  # 转换为天
            
            if cache_age > self.max_age_days:
                self.logger.info(f"缓存已过期，已经 {cache_age:.1f} 天未更新")
                return None
        else:
            # 如果元数据中没有记录，检查文件修改时间
            file_mtime = os.path.getmtime(cache_file)
            file_age = (time.time() - file_mtime) / (24 * 3600)  # 转换为天
            
            if file_age > self.max_age_days:
                self.logger.info(f"缓存已过期，文件已经 {file_age:.1f} 天未修改")
                return None

        # 从缓存加载数据
        try:
            self.logger.info(f"从缓存加载数据: {cache_file}")
            data = pd.read_csv(cache_file, index_col=0, parse_dates=True)
            return data
        except Exception as e:
            self.logger.warning(f"从缓存加载数据失败: {str(e)}")
            return None

    def cache_data(self, data, stock_code, start_date, end_date, frequency):
        """
        缓存数据

        参数:
            data (pandas.DataFrame): 要缓存的数据
            stock_code (str): 金融产品代码
            start_date (str): 开始日期
            end_date (str): 结束日期
            frequency (str): 数据频率

        返回:
            bool: 缓存是否成功
        """
        if data is None or data.empty:
            self.logger.warning("数据为空，不进行缓存")
            return False

        cache_key = self._generate_cache_key(stock_code, start_date, end_date, frequency)
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.csv")

        try:
            # 保存数据到缓存文件
            data.to_csv(cache_file)
            
            # 更新元数据
            self.metadata[cache_key] = {
                'stock_code': stock_code,
                'start_date': start_date,
                'end_date': end_date,
                'frequency': frequency,
                'timestamp': time.time(),
                'rows': len(data),
                'columns': list(data.columns)
            }
            
            # 保存元数据
            self._save_metadata()
            
            self.logger.info(f"数据已缓存: {cache_file}")
            return True
        except Exception as e:
            self.logger.error(f"缓存数据失败: {str(e)}")
            return False

    def clear_expired_cache(self):
        """
        清理过期的缓存

        返回:
            int: 清理的缓存文件数量
        """
        cleared_count = 0
        current_time = time.time()
        
        # 遍历元数据中的所有缓存记录
        expired_keys = []
        for key, info in self.metadata.items():
            cache_time = info.get('timestamp', 0)
            cache_age = (current_time - cache_time) / (24 * 3600)  # 转换为天
            
            if cache_age > self.max_age_days:
                cache_file = os.path.join(self.cache_dir, f"{key}.csv")
                
                if os.path.exists(cache_file):
                    try:
                        os.remove(cache_file)
                        cleared_count += 1
                        self.logger.info(f"已删除过期缓存: {cache_file}")
                    except Exception as e:
                        self.logger.error(f"删除过期缓存失败: {str(e)}")
                
                expired_keys.append(key)
        
        # 从元数据中删除过期记录
        for key in expired_keys:
            self.metadata.pop(key, None)
        
        # 保存更新后的元数据
        self._save_metadata()
        
        self.logger.info(f"清理了 {cleared_count} 个过期缓存文件")
        return cleared_count

    def get_cache_stats(self):
        """
        获取缓存统计信息

        返回:
            dict: 缓存统计信息
        """
        stats = {
            'total_files': 0,
            'total_size_mb': 0,
            'oldest_cache_days': 0,
            'newest_cache_days': float('inf'),
            'avg_cache_age_days': 0,
            'cache_hit_ratio': 0
        }
        
        # 统计缓存文件数量和大小
        cache_files = [f for f in os.listdir(self.cache_dir) if f.endswith('.csv')]
        stats['total_files'] = len(cache_files)
        
        current_time = time.time()
        total_age = 0
        
        for file in cache_files:
            file_path = os.path.join(self.cache_dir, file)
            
            # 计算文件大小
            file_size = os.path.getsize(file_path) / (1024 * 1024)  # 转换为MB
            stats['total_size_mb'] += file_size
            
            # 计算文件年龄
            file_mtime = os.path.getmtime(file_path)
            file_age = (current_time - file_mtime) / (24 * 3600)  # 转换为天
            
            total_age += file_age
            stats['oldest_cache_days'] = max(stats['oldest_cache_days'], file_age)
            stats['newest_cache_days'] = min(stats['newest_cache_days'], file_age)
        
        # 计算平均缓存年龄
        if stats['total_files'] > 0:
            stats['avg_cache_age_days'] = total_age / stats['total_files']
        
        # 如果没有缓存文件，将newest_cache_days设为0
        if stats['newest_cache_days'] == float('inf'):
            stats['newest_cache_days'] = 0
        
        # 四舍五入到2位小数
        stats['total_size_mb'] = round(stats['total_size_mb'], 2)
        stats['oldest_cache_days'] = round(stats['oldest_cache_days'], 2)
        stats['newest_cache_days'] = round(stats['newest_cache_days'], 2)
        stats['avg_cache_age_days'] = round(stats['avg_cache_age_days'], 2)
        
        return stats
