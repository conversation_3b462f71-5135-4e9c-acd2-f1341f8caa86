"""
全面UI测试与修复主脚本

该脚本运行所有UI测试，包括基础UI测试和集成学习特定测试，并修复发现的问题。
"""

import os
import sys
import subprocess
import logging
import time
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/master_ui_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('master_ui_test')

def ensure_dependencies():
    """确保所有依赖已安装"""
    logger.info("检查并安装依赖")
    
    dependencies = [
        "selenium",
        "webdriver_manager",
        "html-testRunner"
    ]
    
    for dep in dependencies:
        try:
            __import__(dep.replace("-", "_"))
            logger.info(f"依赖已安装: {dep}")
        except ImportError:
            logger.info(f"安装依赖: {dep}")
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
    
    logger.info("所有依赖已安装")

def run_basic_ui_tests():
    """运行基础UI测试"""
    logger.info("运行基础UI测试")
    
    # 运行测试并生成报告
    test_script = os.path.join("tests", "generate_ui_test_report.py")
    
    try:
        subprocess.check_call([sys.executable, test_script])
        logger.info("基础UI测试完成")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"基础UI测试失败: {str(e)}")
        return False

def run_ensemble_ui_tests():
    """运行集成学习UI测试"""
    logger.info("运行集成学习UI测试")
    
    # 运行测试
    test_script = os.path.join("tests", "test_ensemble_ui_logic.py")
    
    try:
        subprocess.check_call([sys.executable, test_script])
        logger.info("集成学习UI测试完成")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"集成学习UI测试失败: {str(e)}")
        return False

def fix_ui_issues():
    """修复UI问题"""
    logger.info("修复UI问题")
    
    # 运行修复脚本
    fix_script = os.path.join("tests", "fix_ui_issues.py")
    
    try:
        subprocess.check_call([sys.executable, fix_script])
        logger.info("UI问题修复完成")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"UI问题修复失败: {str(e)}")
        return False

def fix_ensemble_model_detection():
    """修复集成学习模型检测问题"""
    logger.info("修复集成学习模型检测问题")
    
    # 运行修复脚本
    fix_script = os.path.join("tests", "fix_ensemble_model_detection.py")
    
    try:
        subprocess.check_call([sys.executable, fix_script])
        logger.info("集成学习模型检测问题修复完成")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"集成学习模型检测问题修复失败: {str(e)}")
        return False

def verify_fixes():
    """验证修复效果"""
    logger.info("验证修复效果")
    
    # 等待一段时间，确保之前的进程已完全退出
    time.sleep(5)
    
    # 再次运行基础UI测试
    logger.info("再次运行基础UI测试验证修复效果")
    basic_test_success = run_basic_ui_tests()
    
    # 再次运行集成学习UI测试
    logger.info("再次运行集成学习UI测试验证修复效果")
    ensemble_test_success = run_ensemble_ui_tests()
    
    return basic_test_success and ensemble_test_success

def generate_final_report(test_results, fix_results, verify_results):
    """生成最终报告"""
    logger.info("生成最终报告")
    
    # 确保报告目录存在
    reports_dir = "test_reports"
    os.makedirs(reports_dir, exist_ok=True)
    
    # 创建最终报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_content = f"""
# UI测试与修复最终报告

**生成时间**: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## 测试与修复流程

1. 运行基础UI测试: {'成功' if test_results['basic_ui'] else '失败'}
2. 运行集成学习UI测试: {'成功' if test_results['ensemble_ui'] else '失败'}
3. 修复UI问题: {'成功' if fix_results['ui_issues'] else '失败'}
4. 修复集成学习模型检测问题: {'成功' if fix_results['ensemble_model'] else '失败'}
5. 验证修复效果: {'成功' if verify_results else '失败'}

## 测试结果

详细测试结果请查看 `test_reports/ui_tests` 目录下的HTML报告。

## 修复结果

详细修复结果请查看 `test_reports/ui_fix_report_*.md` 文件。

## 建议

1. 定期运行UI测试，确保系统功能稳定
2. 在开发新功能时添加相应的UI测试用例
3. 关注用户反馈，及时修复UI问题
4. 特别关注集成学习功能的模型保存和加载逻辑

## 集成学习功能修复说明

集成学习功能修复主要解决以下问题：

1. 当启用多算法集成学习时，主算法选择应被禁用
2. 集成学习训练完成后，策略性能评估和实况信号决策模块无法找到最佳模型
3. 集成学习模型保存和加载逻辑的完善

这些修复确保了集成学习功能的正常工作，用户可以顺利地训练集成模型并在后续模块中使用。

"""
    
    # 保存最终报告
    report_path = os.path.join(reports_dir, f"ui_test_master_report_{timestamp}.md")
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    logger.info(f"最终报告已保存到: {report_path}")
    return report_path

def main():
    """主函数"""
    logger.info("=" * 80)
    logger.info("全面UI测试与修复主脚本")
    logger.info("=" * 80)
    
    # 确保日志目录存在
    os.makedirs("logs", exist_ok=True)
    
    # 确保依赖已安装
    ensure_dependencies()
    
    # 运行测试
    test_results = {
        'basic_ui': run_basic_ui_tests(),
        'ensemble_ui': run_ensemble_ui_tests()
    }
    
    # 修复问题
    fix_results = {
        'ui_issues': fix_ui_issues(),
        'ensemble_model': fix_ensemble_model_detection()
    }
    
    # 验证修复效果
    verify_results = verify_fixes()
    
    # 生成最终报告
    final_report_path = generate_final_report(test_results, fix_results, verify_results)
    logger.info(f"最终报告已生成: {final_report_path}")
    
    # 输出总结
    logger.info("=" * 80)
    logger.info("UI测试与修复总结")
    logger.info("=" * 80)
    logger.info(f"基础UI测试: {'成功' if test_results['basic_ui'] else '失败'}")
    logger.info(f"集成学习UI测试: {'成功' if test_results['ensemble_ui'] else '失败'}")
    logger.info(f"UI问题修复: {'成功' if fix_results['ui_issues'] else '失败'}")
    logger.info(f"集成学习模型检测问题修复: {'成功' if fix_results['ensemble_model'] else '失败'}")
    logger.info(f"验证修复效果: {'成功' if verify_results else '失败'}")
    logger.info("=" * 80)
    
    # 根据结果设置退出代码
    if all(test_results.values()) and all(fix_results.values()) and verify_results:
        logger.info("测试与修复流程成功完成")
        sys.exit(0)
    else:
        logger.error("测试与修复流程未完全成功")
        sys.exit(1)

if __name__ == "__main__":
    main()
