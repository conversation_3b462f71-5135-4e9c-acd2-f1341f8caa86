"""
UI测试自动化脚本

该脚本使用Selenium WebDriver自动化测试Streamlit应用的UI功能。
根据UI测试方案文档实现各项测试用例。
"""

import os
import sys
import time
import logging
import unittest
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
import json
import re
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(parent_dir, 'logs', 'ui_test.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('ui_test')

class StreamlitUITest(unittest.TestCase):
    """Streamlit UI测试类"""
    
    @classmethod
    def setUpClass(cls):
        """设置测试环境"""
        logger.info("设置测试环境")
        
        # 启动Streamlit应用
        cls.start_streamlit_app()
        
        # 设置WebDriver
        chrome_options = Options()
        chrome_options.add_argument("--headless")  # 无头模式，可以注释掉以查看浏览器操作
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        
        cls.driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)
        cls.driver.maximize_window()
        cls.driver.get("http://localhost:8501")
        
        # 等待页面加载完成
        WebDriverWait(cls.driver, 30).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        logger.info("Streamlit应用已加载")
        
        # 等待侧边栏加载完成
        WebDriverWait(cls.driver, 30).until(
            EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'stSidebar')]"))
        )
        logger.info("侧边栏已加载")
    
    @classmethod
    def tearDownClass(cls):
        """清理测试环境"""
        logger.info("清理测试环境")
        
        # 关闭WebDriver
        if cls.driver:
            cls.driver.quit()
        
        # 停止Streamlit应用
        cls.stop_streamlit_app()
    
    @classmethod
    def start_streamlit_app(cls):
        """启动Streamlit应用"""
        logger.info("启动Streamlit应用")
        
        # 使用subprocess启动应用
        import subprocess
        import threading
        
        def run_app():
            app_file = os.path.join(parent_dir, 'main_app.py')
            subprocess.run([sys.executable, "-m", "streamlit", "run", app_file], 
                          stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # 在后台线程中启动应用
        cls.app_thread = threading.Thread(target=run_app)
        cls.app_thread.daemon = True
        cls.app_thread.start()
        
        # 等待应用启动
        time.sleep(10)
        logger.info("Streamlit应用已启动")
    
    @classmethod
    def stop_streamlit_app(cls):
        """停止Streamlit应用"""
        logger.info("停止Streamlit应用")
        
        # 在Windows上使用taskkill命令终止Streamlit进程
        import subprocess
        subprocess.run(["taskkill", "/f", "/im", "streamlit.exe"], 
                      stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        subprocess.run(["taskkill", "/f", "/im", "python.exe", "/fi", "WINDOWTITLE eq streamlit"], 
                      stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        logger.info("Streamlit应用已停止")
    
    def wait_for_element(self, by, value, timeout=10):
        """等待元素出现"""
        try:
            element = WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((by, value))
            )
            return element
        except TimeoutException:
            logger.error(f"等待元素超时: {by}={value}")
            return None
    
    def test_01_app_startup(self):
        """测试应用启动"""
        logger.info("测试应用启动")
        
        # 验证页面标题
        self.assertIn("DRL量化交易系统", self.driver.title)
        
        # 验证侧边栏存在
        sidebar = self.wait_for_element(By.XPATH, "//div[contains(@class, 'stSidebar')]")
        self.assertIsNotNone(sidebar)
        
        # 验证导航选项存在
        nav_options = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'stRadio')]//label")
        self.assertGreaterEqual(len(nav_options), 5)
        
        logger.info("应用启动测试通过")
    
    def test_02_data_acquisition(self):
        """测试数据获取功能"""
        logger.info("测试数据获取功能")
        
        # 确保在数据中心页面
        nav_options = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'stRadio')]//label")
        for option in nav_options:
            if "数据中心" in option.text:
                option.click()
                break
        
        # 等待页面加载
        time.sleep(3)
        
        # 输入股票代码
        stock_code_input = self.wait_for_element(By.XPATH, "//input[@aria-label='金融产品代码']")
        stock_code_input.clear()
        stock_code_input.send_keys("sh000001")
        
        # 设置日期范围
        # 注意：日期选择器的交互比较复杂，这里简化处理
        
        # 选择数据频率
        frequency_select = self.wait_for_element(By.XPATH, "//div[text()='数据频率']/..//div[contains(@class, 'stSelectbox')]")
        if frequency_select:
            frequency_select.click()
            time.sleep(1)
            daily_option = self.wait_for_element(By.XPATH, "//div[text()='日线']")
            if daily_option:
                daily_option.click()
        
        # 点击获取数据按钮
        get_data_button = self.wait_for_element(By.XPATH, "//button[text()='获取数据']")
        if get_data_button:
            get_data_button.click()
            
            # 等待数据加载
            time.sleep(10)
            
            # 验证数据是否加载成功
            success_message = self.wait_for_element(By.XPATH, "//div[contains(text(), '成功获取数据')]")
            self.assertIsNotNone(success_message)
            
            # 验证数据表格是否显示
            data_table = self.wait_for_element(By.XPATH, "//div[contains(@class, 'stDataFrame')]")
            self.assertIsNotNone(data_table)
        
        logger.info("数据获取功能测试通过")
    
    def test_03_feature_engineering(self):
        """测试特征工程功能"""
        logger.info("测试特征工程功能")
        
        # 确保数据已加载
        if not self.wait_for_element(By.XPATH, "//div[contains(@class, 'stDataFrame')]"):
            self.test_02_data_acquisition()
        
        # 滚动到特征工程部分
        feature_engineering_header = self.wait_for_element(By.XPATH, "//h2[text()='特征工程配置']")
        self.driver.execute_script("arguments[0].scrollIntoView();", feature_engineering_header)
        time.sleep(2)
        
        # 选择技术指标
        # 这里只测试部分指标，完整测试可以扩展
        sma_checkbox = self.wait_for_element(By.XPATH, "//label[contains(text(), '简单移动平均线')]")
        if sma_checkbox:
            sma_checkbox.click()
            time.sleep(1)
            sma_checkbox.click()  # 再次点击以确保选中
        
        # 点击生成特征按钮
        generate_features_button = self.wait_for_element(By.XPATH, "//button[text()='生成特征']")
        if generate_features_button:
            generate_features_button.click()
            
            # 等待特征生成
            time.sleep(15)
            
            # 验证特征是否生成成功
            success_message = self.wait_for_element(By.XPATH, "//div[contains(text(), '成功生成特征')]")
            self.assertIsNotNone(success_message)
            
            # 验证处理后的数据表格是否显示
            processed_data_table = self.wait_for_element(By.XPATH, "//h3[text()='处理后的数据预览']/..//div[contains(@class, 'stDataFrame')]")
            self.assertIsNotNone(processed_data_table)
        
        logger.info("特征工程功能测试通过")
    
    def test_04_environment_config(self):
        """测试交易环境配置功能"""
        logger.info("测试交易环境配置功能")
        
        # 滚动到交易环境参数配置部分
        env_config_header = self.wait_for_element(By.XPATH, "//h2[text()='交易环境参数配置']")
        self.driver.execute_script("arguments[0].scrollIntoView();", env_config_header)
        time.sleep(2)
        
        # 设置初始资金
        initial_capital_input = self.wait_for_element(By.XPATH, "//label[text()='初始资金']/..//input")
        if initial_capital_input:
            initial_capital_input.clear()
            initial_capital_input.send_keys("200000")
        
        # 设置手续费率
        commission_rate_input = self.wait_for_element(By.XPATH, "//label[text()='手续费率 (单边)']/..//input")
        if commission_rate_input:
            commission_rate_input.clear()
            commission_rate_input.send_keys("0.0005")
        
        # 点击保存环境配置按钮
        save_config_button = self.wait_for_element(By.XPATH, "//button[text()='保存环境配置']")
        if save_config_button:
            save_config_button.click()
            
            # 等待配置保存
            time.sleep(3)
            
            # 验证配置是否保存成功
            success_message = self.wait_for_element(By.XPATH, "//div[contains(text(), '环境配置已保存')]")
            self.assertIsNotNone(success_message)
        
        logger.info("交易环境配置功能测试通过")
    
    def test_05_drl_agent_training(self):
        """测试DRL智能体训练功能"""
        logger.info("测试DRL智能体训练功能")
        
        # 切换到DRL智能体训练页面
        nav_options = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'stRadio')]//label")
        for option in nav_options:
            if "DRL智能体训练" in option.text:
                option.click()
                break
        
        # 等待页面加载
        time.sleep(5)
        
        # 设置算法参数
        algorithm_select = self.wait_for_element(By.XPATH, "//label[text()='DRL算法']/..//div[contains(@class, 'stSelectbox')]")
        if algorithm_select:
            algorithm_select.click()
            time.sleep(1)
            ppo_option = self.wait_for_element(By.XPATH, "//div[text()='PPO']")
            if ppo_option:
                ppo_option.click()
        
        # 设置训练步数
        timesteps_input = self.wait_for_element(By.XPATH, "//label[text()='训练总步数']/..//input")
        if timesteps_input:
            timesteps_input.clear()
            timesteps_input.send_keys("10000")  # 设置较小的步数以加快测试
        
        # 点击开始训练按钮
        start_training_button = self.wait_for_element(By.XPATH, "//button[text()='开始训练']")
        if start_training_button:
            start_training_button.click()
            
            # 等待训练开始
            time.sleep(10)
            
            # 验证训练是否开始
            training_progress = self.wait_for_element(By.XPATH, "//div[contains(text(), '训练进度')]")
            self.assertIsNotNone(training_progress)
            
            # 测试停止训练按钮
            stop_training_button = self.wait_for_element(By.XPATH, "//button[text()='停止训练']")
            if stop_training_button:
                stop_training_button.click()
                
                # 等待训练停止
                time.sleep(5)
                
                # 验证训练是否停止
                # 这里可能需要根据实际UI行为调整验证方式
        
        logger.info("DRL智能体训练功能测试通过")

if __name__ == "__main__":
    unittest.main()
