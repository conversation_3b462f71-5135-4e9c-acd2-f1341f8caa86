"""
GPU检测与配置测试脚本
用于测试系统的GPU检测和配置功能
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core_logic.utils import setup_logger, is_gpu_available, get_gpu_info, setup_gpu_environment, diagnose_gpu_issues

# 设置日志
logger = setup_logger(log_file='logs/test_gpu.log')

def test_gpu_detection():
    """测试GPU检测功能"""
    logger.info("测试GPU检测功能")
    
    # 检测GPU是否可用
    gpu_available = is_gpu_available()
    logger.info(f"GPU可用: {gpu_available}")
    
    # 获取GPU详细信息
    gpu_info = get_gpu_info()
    logger.info(f"GPU信息: {gpu_info}")
    
    # 记录测试结果
    results = {
        'gpu_available': gpu_available,
        'gpu_count': gpu_info['count'],
        'gpu_framework': gpu_info['framework'],
        'detection_method': gpu_info['detection_method']
    }
    
    # 如果有GPU设备，记录设备信息
    if gpu_info['count'] > 0 and len(gpu_info['devices']) > 0:
        results['gpu_devices'] = gpu_info['devices']
    
    # 保存测试结果
    results_df = pd.DataFrame([results])
    results_df.to_csv('tests/gpu_detection_results.csv', index=False)
    
    return results

def test_gpu_environment_setup():
    """测试GPU环境配置功能"""
    logger.info("测试GPU环境配置功能")
    
    # 设置GPU环境
    setup_result = setup_gpu_environment()
    logger.info(f"GPU环境配置结果: {setup_result}")
    
    # 记录测试结果
    results = {
        'setup_success': setup_result['success'],
        'framework': setup_result['framework'],
        'message': setup_result['message']
    }
    
    # 如果有CUDA版本信息，记录版本信息
    if setup_result['cuda_version']:
        results['cuda_version'] = setup_result['cuda_version']
    
    # 如果有cuDNN版本信息，记录版本信息
    if setup_result['cudnn_version']:
        results['cudnn_version'] = setup_result['cudnn_version']
    
    # 保存测试结果
    results_df = pd.DataFrame([results])
    results_df.to_csv('tests/gpu_environment_setup_results.csv', index=False)
    
    return results

def test_gpu_diagnosis():
    """测试GPU诊断功能"""
    logger.info("测试GPU诊断功能")
    
    # 诊断GPU问题
    diagnosis = diagnose_gpu_issues()
    logger.info(f"GPU诊断结果: {diagnosis}")
    
    # 记录测试结果
    results = {
        'issues_detected': diagnosis['issues_detected'],
        'gpu_detected': diagnosis['gpu_detected'],
        'cuda_detected': diagnosis['cuda_detected'],
        'pytorch_gpu': diagnosis['pytorch_gpu'],
        'tensorflow_gpu': diagnosis['tensorflow_gpu']
    }
    
    # 如果检测到问题，记录问题和建议
    if diagnosis['issues_detected']:
        results['problems'] = diagnosis['problems']
        results['suggestions'] = diagnosis['suggestions']
    
    # 如果有驱动版本信息，记录版本信息
    if diagnosis['driver_version']:
        results['driver_version'] = diagnosis['driver_version']
    
    # 如果有CUDA版本信息，记录版本信息
    if diagnosis['cuda_version']:
        results['cuda_version'] = diagnosis['cuda_version']
    
    # 保存测试结果
    results_df = pd.DataFrame([results])
    results_df.to_csv('tests/gpu_diagnosis_results.csv', index=False)
    
    return results

def test_pytorch_gpu():
    """测试PyTorch GPU支持"""
    logger.info("测试PyTorch GPU支持")
    
    try:
        import torch
        
        # 检查PyTorch是否可用
        pytorch_available = True
        pytorch_version = torch.__version__
        
        # 检查CUDA是否可用
        cuda_available = torch.cuda.is_available()
        
        # 如果CUDA可用，获取设备信息
        if cuda_available:
            device_count = torch.cuda.device_count()
            devices = [torch.cuda.get_device_name(i) for i in range(device_count)]
            
            # 测试简单的张量操作
            x = torch.rand(1000, 1000)
            y = torch.rand(1000, 1000)
            
            # 在CPU上计算
            start_time = datetime.now()
            z_cpu = x @ y
            cpu_time = (datetime.now() - start_time).total_seconds()
            
            # 在GPU上计算
            x_gpu = x.cuda()
            y_gpu = y.cuda()
            
            start_time = datetime.now()
            z_gpu = x_gpu @ y_gpu
            gpu_time = (datetime.now() - start_time).total_seconds()
            
            # 验证结果一致性
            result_match = torch.allclose(z_cpu, z_gpu.cpu(), rtol=1e-5)
            
            # 计算加速比
            speedup = cpu_time / gpu_time if gpu_time > 0 else 0
            
            logger.info(f"PyTorch GPU测试: CPU时间={cpu_time:.4f}秒, GPU时间={gpu_time:.4f}秒, 加速比={speedup:.2f}倍, 结果一致: {result_match}")
        else:
            device_count = 0
            devices = []
            speedup = 0
            result_match = False
            
            logger.warning("PyTorch不支持CUDA")
        
        # 记录测试结果
        results = {
            'pytorch_available': pytorch_available,
            'pytorch_version': pytorch_version,
            'cuda_available': cuda_available,
            'device_count': device_count,
            'speedup': speedup,
            'result_match': result_match
        }
        
        # 如果有设备信息，记录设备信息
        if device_count > 0:
            results['devices'] = devices
        
    except ImportError:
        logger.error("PyTorch未安装")
        
        # 记录测试结果
        results = {
            'pytorch_available': False,
            'cuda_available': False
        }
    
    # 保存测试结果
    results_df = pd.DataFrame([results])
    results_df.to_csv('tests/pytorch_gpu_results.csv', index=False)
    
    return results

def test_tensorflow_gpu():
    """测试TensorFlow GPU支持"""
    logger.info("测试TensorFlow GPU支持")
    
    try:
        import tensorflow as tf
        
        # 检查TensorFlow是否可用
        tensorflow_available = True
        tensorflow_version = tf.__version__
        
        # 检查GPU是否可用
        gpus = tf.config.list_physical_devices('GPU')
        gpu_available = len(gpus) > 0
        
        # 如果GPU可用，获取设备信息
        if gpu_available:
            device_count = len(gpus)
            devices = [gpu.name for gpu in gpus]
            
            # 测试简单的矩阵乘法
            with tf.device('/cpu:0'):
                x_cpu = tf.random.normal([1000, 1000])
                y_cpu = tf.random.normal([1000, 1000])
                
                start_time = datetime.now()
                z_cpu = tf.matmul(x_cpu, y_cpu)
                cpu_time = (datetime.now() - start_time).total_seconds()
            
            with tf.device('/gpu:0'):
                x_gpu = tf.random.normal([1000, 1000])
                y_gpu = tf.random.normal([1000, 1000])
                
                start_time = datetime.now()
                z_gpu = tf.matmul(x_gpu, y_gpu)
                gpu_time = (datetime.now() - start_time).total_seconds()
            
            # 计算加速比
            speedup = cpu_time / gpu_time if gpu_time > 0 else 0
            
            logger.info(f"TensorFlow GPU测试: CPU时间={cpu_time:.4f}秒, GPU时间={gpu_time:.4f}秒, 加速比={speedup:.2f}倍")
        else:
            device_count = 0
            devices = []
            speedup = 0
            
            logger.warning("TensorFlow未检测到GPU")
        
        # 记录测试结果
        results = {
            'tensorflow_available': tensorflow_available,
            'tensorflow_version': tensorflow_version,
            'gpu_available': gpu_available,
            'device_count': device_count,
            'speedup': speedup
        }
        
        # 如果有设备信息，记录设备信息
        if device_count > 0:
            results['devices'] = devices
        
    except ImportError:
        logger.error("TensorFlow未安装")
        
        # 记录测试结果
        results = {
            'tensorflow_available': False,
            'gpu_available': False
        }
    
    # 保存测试结果
    results_df = pd.DataFrame([results])
    results_df.to_csv('tests/tensorflow_gpu_results.csv', index=False)
    
    return results

def run_all_tests():
    """运行所有测试"""
    logger.info("开始运行所有GPU检测与配置测试")
    
    # 确保测试目录存在
    os.makedirs('tests', exist_ok=True)
    
    # 运行各项测试
    detection_results = test_gpu_detection()
    setup_results = test_gpu_environment_setup()
    diagnosis_results = test_gpu_diagnosis()
    pytorch_results = test_pytorch_gpu()
    tensorflow_results = test_tensorflow_gpu()
    
    # 汇总测试结果
    gpu_available = detection_results['gpu_available']
    gpu_setup_success = setup_results['setup_success']
    
    pytorch_gpu_working = False
    tensorflow_gpu_working = False
    
    if 'cuda_available' in pytorch_results and pytorch_results['cuda_available']:
        pytorch_gpu_working = pytorch_results['speedup'] > 1.0
    
    if 'gpu_available' in tensorflow_results and tensorflow_results['gpu_available']:
        tensorflow_gpu_working = tensorflow_results['speedup'] > 1.0
    
    # 判断整体GPU支持情况
    gpu_support_working = gpu_available and (pytorch_gpu_working or tensorflow_gpu_working)
    
    logger.info(f"GPU检测结果: {'可用' if gpu_available else '不可用'}")
    logger.info(f"GPU环境配置结果: {'成功' if gpu_setup_success else '失败'}")
    logger.info(f"PyTorch GPU支持: {'正常工作' if pytorch_gpu_working else '不工作'}")
    logger.info(f"TensorFlow GPU支持: {'正常工作' if tensorflow_gpu_working else '不工作'}")
    logger.info(f"整体GPU支持情况: {'正常工作' if gpu_support_working else '不工作'}")
    
    # 保存汇总结果
    summary = {
        'gpu_available': gpu_available,
        'gpu_setup_success': gpu_setup_success,
        'pytorch_gpu_working': pytorch_gpu_working,
        'tensorflow_gpu_working': tensorflow_gpu_working,
        'gpu_support_working': gpu_support_working
    }
    
    summary_df = pd.DataFrame([summary])
    summary_df.to_csv('tests/gpu_support_summary.csv', index=False)
    
    return gpu_support_working

if __name__ == "__main__":
    run_all_tests()
