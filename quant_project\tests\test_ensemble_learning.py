#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试集成学习功能
"""

import os
import sys
import unittest
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import logging
import shutil

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core_logic.drl_agent import DRLAgent
from core_logic.ensemble_learning import EnsembleModel as EnsembleLearning
from core_logic.data_handler import DataHandler
from core_logic.trading_env import TradingEnvironment

# 设置日志
logger = logging.getLogger('drl_trading')
logger.setLevel(logging.INFO)
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)
logger.addHandler(console_handler)

class TestEnsembleLearning(unittest.TestCase):
    """测试集成学习功能"""

    @classmethod
    def setUpClass(cls):
        """测试前的准备工作"""
        # 获取测试数据
        cls.data_handler = DataHandler()
        start_date = '2023-01-01'
        end_date = '2023-03-31'
        cls.stock_data = cls.data_handler.get_stock_data('sh000001', start_date, end_date, '日线')

        # 确保数据存在
        if cls.stock_data is None or len(cls.stock_data) == 0:
            raise ValueError("无法获取测试数据")

        # 创建保存模型的目录
        os.makedirs('saved_models/ensemble', exist_ok=True)

        logger.info(f"测试数据准备完成，共 {len(cls.stock_data)} 条记录")

    @classmethod
    def tearDownClass(cls):
        """测试后的清理工作"""
        # 清理测试生成的模型文件
        if os.path.exists('saved_models/ensemble'):
            shutil.rmtree('saved_models/ensemble')

        # 清理测试生成的单个模型文件
        for file in os.listdir('saved_models'):
            if file.startswith('test_ensemble_'):
                os.remove(os.path.join('saved_models', file))

    def test_single_algorithm_ensemble(self):
        """测试单一算法集成学习"""
        logger.info("测试单一算法集成学习")

        # 环境配置
        env_config = {
            'df_processed_data': self.stock_data,
            'initial_capital': 100000,
            'commission_rate': 0.001,
            'min_hold_days': 3,
            'window_size': 20
        }

        # 创建多个DRL智能体
        n_models = 3
        models = []

        for i in range(n_models):
            # 智能体配置
            agent_config = {
                'algorithm': 'PPO',
                'policy_network': 'MlpPolicy',
                'learning_rate': 0.0003,
                'gamma': 0.99,
                'n_steps': 64,
                'batch_size': 64,
                'seed': 42 + i
            }

            # 创建智能体
            agent = DRLAgent(env_config=env_config, agent_config=agent_config)

            # 训练模型（仅训练少量步数用于测试）
            agent.train(total_timesteps=100)
            # 保存模型
            agent.save_model(save_path=f"saved_models/test_ensemble_{i}.zip")

            # 添加到模型列表
            models.append(agent.model)

        # 创建集成学习对象
        ensemble = EnsembleLearning(models=models, voting_method='majority')

        # 检查是否生成了正确数量的模型
        self.assertEqual(len(ensemble.models), n_models)

        # 测试预测
        env = TradingEnvironment(**env_config)
        obs, _ = env.reset()  # Gymnasium API返回(obs, info)元组
        action, _ = ensemble.predict(obs)

        # 检查预测结果是否合理
        self.assertIn(action.item(), [0, 1, 2])

        logger.info(f"单一算法集成学习测试完成，生成了 {len(ensemble.models)} 个模型")
        return ensemble

    def test_multi_algorithm_ensemble(self):
        """测试多算法集成学习"""
        logger.info("测试多算法集成学习")

        # 环境配置
        env_config = {
            'df_processed_data': self.stock_data,
            'initial_capital': 100000,
            'commission_rate': 0.001,
            'min_hold_days': 3,
            'window_size': 20
        }

        # 创建多个不同算法的DRL智能体
        algorithms = ['PPO', 'A2C', 'DQN']
        models = []

        for i, algorithm in enumerate(algorithms):
            # 智能体配置
            agent_config = {
                'algorithm': algorithm,
                'policy_network': 'MlpPolicy',
                'learning_rate': 0.0003,
                'gamma': 0.99,
                'n_steps': 64,
                'batch_size': 64,
                'seed': 42
            }

            # 创建智能体
            agent = DRLAgent(env_config=env_config, agent_config=agent_config)

            # 训练模型（仅训练少量步数用于测试）
            agent.train(total_timesteps=100)
            # 保存模型
            agent.save_model(save_path=f"saved_models/test_ensemble_{i}.zip")

            # 添加到模型列表
            models.append(agent.model)

        # 创建集成学习对象，使用加权投票
        weights = [0.5, 0.3, 0.2]
        ensemble = EnsembleLearning(models=models, voting_method='weighted', weights=weights)

        # 检查是否生成了正确数量的模型
        self.assertEqual(len(ensemble.models), len(algorithms))

        # 测试预测
        env = TradingEnvironment(**env_config)
        obs, _ = env.reset()  # Gymnasium API返回(obs, info)元组
        action, _ = ensemble.predict(obs)

        # 检查预测结果是否合理
        self.assertIn(action.item(), [0, 1, 2])

        logger.info(f"多算法集成学习测试完成，生成了 {len(ensemble.models)} 个模型")
        return ensemble

    def test_ensemble_save_load(self):
        """测试集成模型的保存和加载"""
        logger.info("测试集成模型的保存和加载")

        # 先训练一个集成模型
        ensemble = self.test_single_algorithm_ensemble()

        # 保存集成模型
        save_path = 'saved_models/ensemble/test_save_load_ensemble'
        os.makedirs(save_path, exist_ok=True)
        ensemble.save(save_path)

        # 检查保存的文件是否存在
        self.assertTrue(os.path.exists(os.path.join(save_path, "ensemble_config.json")))
        for i in range(len(ensemble.models)):
            self.assertTrue(os.path.exists(os.path.join(save_path, f"model_{i}.zip")))

        # 加载集成模型
        new_ensemble = EnsembleLearning.load(save_path)

        # 检查加载的模型数量是否正确
        self.assertEqual(len(new_ensemble.models), len(ensemble.models))

        # 检查配置是否正确加载
        self.assertEqual(new_ensemble.voting_method, ensemble.voting_method)

        logger.info("集成模型保存和加载测试完成")

    def test_voting_methods(self):
        """测试不同的投票方法"""
        logger.info("测试不同的投票方法")

        # 环境配置
        env_config = {
            'df_processed_data': self.stock_data,
            'initial_capital': 100000,
            'commission_rate': 0.001,
            'min_hold_days': 3,
            'window_size': 20
        }

        # 创建多个DRL智能体
        n_models = 3
        models = []

        for i in range(n_models):
            # 智能体配置
            agent_config = {
                'algorithm': 'PPO',
                'policy_network': 'MlpPolicy',
                'learning_rate': 0.0003,
                'gamma': 0.99,
                'n_steps': 64,
                'batch_size': 64,
                'seed': 42 + i
            }

            # 创建智能体
            agent = DRLAgent(env_config=env_config, agent_config=agent_config)

            # 训练模型（仅训练少量步数用于测试）
            agent.train(total_timesteps=100)
            # 保存模型
            agent.save_model(save_path=f"saved_models/test_ensemble_{i}.zip")

            # 添加到模型列表
            models.append(agent.model)

        # 测试多数投票
        ensemble_majority = EnsembleLearning(models=models, voting_method='majority')

        # 测试加权投票
        ensemble_weighted = EnsembleLearning(models=models, voting_method='weighted', weights=[0.5, 0.3, 0.2])

        # 测试软投票
        ensemble_soft = EnsembleLearning(models=models, voting_method='soft')

        # 测试预测
        env = TradingEnvironment(**env_config)
        obs, _ = env.reset()  # Gymnasium API返回(obs, info)元组

        action_majority, _ = ensemble_majority.predict(obs)
        action_weighted, _ = ensemble_weighted.predict(obs)
        action_soft, _ = ensemble_soft.predict(obs)

        # 检查预测结果是否合理
        self.assertIn(action_majority.item(), [0, 1, 2])
        self.assertIn(action_weighted.item(), [0, 1, 2])
        self.assertIn(action_soft.item(), [0, 1, 2])

        logger.info("不同投票方法测试完成")

def run_tests():
    """运行所有测试"""
    logger.info("开始运行集成学习测试")
    unittest.main(argv=['first-arg-is-ignored'], exit=False)
    logger.info("集成学习测试完成")

if __name__ == '__main__':
    run_tests()
