块 2: 项目结构与阶段一 (UI初步搭建与数据模块基础)

本块目标： 建立标准项目文件结构，搭建UI基本框架，并开始实现“数据中心与环境配置”模块的基础功能，特别是数据获取与校验。

接上一块的任务，我们现在开始【阶段一：项目设置、UI骨架、DRL交易环境基础与数据探索】。

核心依赖安装：

在已创建的虚拟环境中，安装以下核心依赖：Pandas, NumPy, Scikit-learn, Matplotlib, Gymnasium, 你选择的DRL框架 (如Stable-Baselines3), PyTorch/TensorFlow (与DRL框架对应), 以及你选择的UI框架 (Streamlit/Dash)。
项目结构建立：

请创建以下项目结构：
quant_project/
├── main_app.py             # UI启动与主控
├── configs/                # 存放DRL算法、环境、应用配置 (e.g., drl_agent_config.yaml)
├── core_logic/
│   ├── data_handler.py     # 数据获取、校验、缓存
│   ├── feature_engineer.py # 特征工程，为DRL状态空间做准备
│   ├── trading_environment.py # **核心：实现Gymnasium兼容的交易环境**
│   ├── drl_agent.py        # DRL智能体定义、训练与加载逻辑
│   ├── performance_analyzer.py # DRL策略性能分析与指标计算
│   └── utils.py            # 通用工具函数
├── data_cache/             # 存放缓存的数据文件
├── saved_models/           # 存放训练好的DRL智能体
├── logs/
│   └── app.log             # 应用程序日志
└── ui_components/          # (可选) 存放自定义的UI组件模块
UI基础框架搭建 (main_app.py)：

在 main_app.py 中搭建UI基础框架。
包含一个侧边栏导航，导航菜单项调整为：‘数据中心与环境配置’、‘DRL智能体训练’、‘策略性能评估’、‘实况信号决策’、‘日志控制台’。
‘数据中心与环境配置’模块 (UI与data_handler.py初步实现)：

UI部分：
允许用户输入金融品种代码（应根据akshare API文档给出格式建议）、选择历史数据区间（开始日期、结束日期）、数据频率（如日线、小时线）。
提供按钮触发数据获取。
获取数据后，在UI上展示数据概览（例如，DataFrame的head，列名，数据形状）。用户应能确认关键列（如开高低收盘价、成交量、日期）。确保日期列被设置为了DataFrame的索引。
core_logic/data_handler.py 部分：
实现函数，根据UI传入的参数，通过 akshare API获取金融数据。
执行数据源可靠性验证（例如，检查数据是否为空，是否有预期列）。
实现简单的数据缓存机制（例如，将获取的数据保存到 data_cache/ 目录，下次请求相同参数时可先从缓存加载）。
所有操作和潜在错误都应有日志记录。
UI部分（续）：
允许用户配置部分交易环境参数，例如初始资金、手续费率。这些参数后续将传递给 trading_environment.py。
日志系统初始化与UI集成：

在项目中初始化一个日志系统（例如使用Python的 logging 模块），配置日志格式、级别，并将日志输出到 logs/app.log 文件。
在UI的‘日志控制台’模块，初步实现一个简单的日志查看器，能够显示 app.log 的内容。
完成以上任务后，请对 data_handler.py 和相关的UI功能进行初步测试。”

