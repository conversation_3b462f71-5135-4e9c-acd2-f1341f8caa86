"""
过拟合检测模块
实现过拟合检测和防止过拟合的方法
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Optional, Tuple, Union, Any, Callable
import matplotlib.pyplot as plt
from sklearn.metrics import mean_squared_error, r2_score
from quant_trading.validation.time_series_cv import TimeSeriesCV, CVMethod

class OverfittingDetector:
    """
    过拟合检测类
    实现过拟合检测和防止过拟合的方法
    """

    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化过拟合检测器

        参数:
            logger (logging.Logger, optional): 日志记录器
        """
        self.logger = logger or logging.getLogger('drl_trading')

    def detect_overfitting(self, 
                          train_scores: List[float], 
                          test_scores: List[float], 
                          threshold: float = 0.1) -> Tuple[bool, float, Dict]:
        """
        检测过拟合

        参数:
            train_scores (list): 训练集上的评分列表
            test_scores (list): 测试集上的评分列表
            threshold (float): 过拟合阈值，训练集和测试集评分差异的最大允许值

        返回:
            tuple: (是否过拟合, 过拟合程度, 详细信息)
        """
        if len(train_scores) != len(test_scores) or len(train_scores) == 0:
            self.logger.error("训练集和测试集评分列表长度不一致或为空")
            return True, 1.0, {"error": "评分列表无效"}

        # 计算平均评分
        avg_train_score = np.mean(train_scores)
        avg_test_score = np.mean(test_scores)
        
        # 计算评分差异
        score_diff = avg_train_score - avg_test_score
        
        # 计算过拟合程度
        if avg_train_score > 0:
            overfitting_degree = score_diff / avg_train_score
        else:
            overfitting_degree = score_diff
        
        # 判断是否过拟合
        is_overfitting = overfitting_degree > threshold
        
        # 计算评分方差
        train_var = np.var(train_scores)
        test_var = np.var(test_scores)
        
        # 计算评分趋势
        train_trend = self._calculate_trend(train_scores)
        test_trend = self._calculate_trend(test_scores)
        
        # 详细信息
        details = {
            "avg_train_score": avg_train_score,
            "avg_test_score": avg_test_score,
            "score_diff": score_diff,
            "overfitting_degree": overfitting_degree,
            "train_variance": train_var,
            "test_variance": test_var,
            "train_trend": train_trend,
            "test_trend": test_trend,
            "threshold": threshold
        }
        
        if is_overfitting:
            self.logger.warning(f"检测到过拟合，程度: {overfitting_degree:.4f}，阈值: {threshold}")
        else:
            self.logger.info(f"未检测到过拟合，程度: {overfitting_degree:.4f}，阈值: {threshold}")
        
        return is_overfitting, overfitting_degree, details

    def evaluate_model_stability(self, 
                                model_fn: Callable, 
                                X: pd.DataFrame, 
                                y: pd.Series, 
                                cv: Optional[TimeSeriesCV] = None, 
                                scoring_fn: Optional[Callable] = None) -> Dict:
        """
        评估模型稳定性

        参数:
            model_fn (callable): 模型训练函数，接受(X_train, y_train)并返回模型
            X (pandas.DataFrame): 特征数据
            y (pandas.Series): 目标数据
            cv (TimeSeriesCV, optional): 交叉验证器
            scoring_fn (callable, optional): 评分函数，接受(model, X, y)并返回分数

        返回:
            dict: 稳定性评估结果
        """
        # 如果没有提供交叉验证器，创建一个默认的
        if cv is None:
            cv = TimeSeriesCV(method=CVMethod.ROLLING_WINDOW, n_splits=5, logger=self.logger)
        
        # 如果没有提供评分函数，使用默认的R²评分
        if scoring_fn is None:
            scoring_fn = lambda model, X, y: r2_score(y, model.predict(X))
        
        # 初始化结果列表
        train_scores = []
        test_scores = []
        predictions = []
        actuals = []
        
        # 进行交叉验证
        for train_index, test_index in cv.split(X, y):
            # 获取训练集和测试集
            X_train, y_train = X.iloc[train_index], y.iloc[train_index]
            X_test, y_test = X.iloc[test_index], y.iloc[test_index]
            
            # 训练模型
            model = model_fn(X_train, y_train)
            
            # 评估模型
            train_score = scoring_fn(model, X_train, y_train)
            test_score = scoring_fn(model, X_test, y_test)
            
            # 记录评分
            train_scores.append(train_score)
            test_scores.append(test_score)
            
            # 记录预测值和实际值
            y_pred = model.predict(X_test)
            predictions.extend(y_pred)
            actuals.extend(y_test)
        
        # 检测过拟合
        is_overfitting, overfitting_degree, overfitting_details = self.detect_overfitting(train_scores, test_scores)
        
        # 计算预测稳定性
        prediction_stability = self._calculate_prediction_stability(predictions, actuals)
        
        # 汇总结果
        results = {
            "train_scores": train_scores,
            "test_scores": test_scores,
            "avg_train_score": np.mean(train_scores),
            "avg_test_score": np.mean(test_scores),
            "train_score_std": np.std(train_scores),
            "test_score_std": np.std(test_scores),
            "is_overfitting": is_overfitting,
            "overfitting_degree": overfitting_degree,
            "overfitting_details": overfitting_details,
            "prediction_stability": prediction_stability
        }
        
        return results

    def plot_learning_curves(self, 
                            train_sizes: List[int], 
                            train_scores: List[float], 
                            test_scores: List[float], 
                            title: str = "Learning Curves") -> plt.Figure:
        """
        绘制学习曲线

        参数:
            train_sizes (list): 训练集大小列表
            train_scores (list): 训练集评分列表
            test_scores (list): 测试集评分列表
            title (str): 图表标题

        返回:
            matplotlib.figure.Figure: 图表对象
        """
        fig, ax = plt.subplots(figsize=(10, 6))
        
        # 绘制训练集和测试集评分
        ax.plot(train_sizes, train_scores, 'o-', color='r', label="Training score")
        ax.plot(train_sizes, test_scores, 'o-', color='g', label="Cross-validation score")
        
        # 设置图表属性
        ax.set_title(title)
        ax.set_xlabel("Training examples")
        ax.set_ylabel("Score")
        ax.grid(True)
        ax.legend(loc="best")
        
        return fig

    def suggest_regularization(self, overfitting_degree: float) -> Dict:
        """
        根据过拟合程度提供正则化建议

        参数:
            overfitting_degree (float): 过拟合程度

        返回:
            dict: 正则化建议
        """
        suggestions = {}
        
        if overfitting_degree <= 0.1:
            suggestions["status"] = "良好"
            suggestions["message"] = "模型拟合良好，无需额外正则化"
            suggestions["actions"] = []
        elif overfitting_degree <= 0.3:
            suggestions["status"] = "轻微过拟合"
            suggestions["message"] = "模型存在轻微过拟合，建议尝试以下方法"
            suggestions["actions"] = [
                "增加L2正则化（权重衰减）",
                "使用早停（Early Stopping）",
                "增加训练数据"
            ]
        elif overfitting_degree <= 0.5:
            suggestions["status"] = "中度过拟合"
            suggestions["message"] = "模型存在中度过拟合，建议尝试以下方法"
            suggestions["actions"] = [
                "增加L1和L2正则化",
                "使用早停（Early Stopping）",
                "减少模型复杂度（如减少网络层数或神经元数量）",
                "使用Dropout",
                "增加训练数据或使用数据增强"
            ]
        else:
            suggestions["status"] = "严重过拟合"
            suggestions["message"] = "模型存在严重过拟合，建议尝试以下方法"
            suggestions["actions"] = [
                "大幅减少模型复杂度",
                "增加强正则化（L1和L2）",
                "使用高Dropout率",
                "重新设计特征",
                "使用集成方法（如Bagging）",
                "考虑使用更简单的模型"
            ]
        
        return suggestions

    def _calculate_trend(self, scores: List[float]) -> float:
        """
        计算评分趋势

        参数:
            scores (list): 评分列表

        返回:
            float: 趋势系数（正值表示上升趋势，负值表示下降趋势）
        """
        if len(scores) < 2:
            return 0.0
        
        # 使用简单线性回归计算趋势
        x = np.arange(len(scores))
        y = np.array(scores)
        
        # 计算趋势系数
        n = len(x)
        sum_x = np.sum(x)
        sum_y = np.sum(y)
        sum_xy = np.sum(x * y)
        sum_xx = np.sum(x * x)
        
        # 计算斜率
        slope = (n * sum_xy - sum_x * sum_y) / (n * sum_xx - sum_x * sum_x)
        
        return slope

    def _calculate_prediction_stability(self, predictions: List[float], actuals: List[float]) -> Dict:
        """
        计算预测稳定性

        参数:
            predictions (list): 预测值列表
            actuals (list): 实际值列表

        返回:
            dict: 稳定性指标
        """
        if len(predictions) != len(actuals) or len(predictions) == 0:
            return {"error": "预测值和实际值列表长度不一致或为空"}
        
        # 转换为NumPy数组
        y_pred = np.array(predictions)
        y_true = np.array(actuals)
        
        # 计算误差
        errors = y_pred - y_true
        
        # 计算均方误差
        mse = mean_squared_error(y_true, y_pred)
        
        # 计算R²
        r2 = r2_score(y_true, y_pred)
        
        # 计算误差分布
        error_mean = np.mean(errors)
        error_std = np.std(errors)
        error_skew = stats.skew(errors) if len(errors) > 8 else 0
        error_kurt = stats.kurtosis(errors) if len(errors) > 8 else 0
        
        # 计算误差自相关
        if len(errors) > 1:
            error_autocorr = np.corrcoef(errors[:-1], errors[1:])[0, 1]
        else:
            error_autocorr = 0
        
        # 汇总结果
        stability = {
            "mse": mse,
            "r2": r2,
            "error_mean": error_mean,
            "error_std": error_std,
            "error_skew": error_skew,
            "error_kurt": error_kurt,
            "error_autocorr": error_autocorr
        }
        
        return stability
