块 8: 阶段四 (性能分析模块 - performance_analyzer.py)

本块目标： 实现 performance_analyzer.py 模块，该模块负责根据回测产生的交易记录和账户净值序列，计算所有必需的金融交易性能指标。

核心任务1：实现 core_logic/performance_analyzer.py – 性能分析与指标计算核心模块

此模块负责计算交易性能指标，应独立于特定DRL框架。

类定义 PerformanceAnalyzer 或一系列独立函数。

输入数据格式定义：

交易记录 (Trades Log)： 结构化的列表或DataFrame，每条记录包含：品种代码、开仓日期/时间、平仓日期/时间、交易方向（多/空）、开仓价格、平仓价格、手数/数量、手续费、该笔交易盈亏。
账户净值序列 (Equity Curve)： Pandas Series或DataFrame，索引为日期/时间，值为当时的账户总净值。
基准净值序列 (Benchmark Equity Curve) (可选)： 格式同上，用于对比。
核心指标计算函数 (每个函数应有清晰的docstring和单元测试)：

总收益率 (Total Return)
年化收益率 (Annualized Return) (需考虑回测期长度)
最大回撤 (Maximum Drawdown) (包括回撤值、百分比、起止日期、持续时长)
夏普比率 (Sharpe Ratio) (需传入或配置年化无风险利率，并明确收益率周期)
索提诺比率 (Sortino Ratio)
卡玛比率 (Calmar Ratio) (年化收益率 / 最大回撤绝对值)
盈利因子 (Profit Factor) (总盈利 / 总亏损绝对值)
胜率 (Win Rate) (盈利交易次数 / 总交易次数)
平均盈亏比 (Average Win/Loss Ratio)
交易次数 (Total Trades)
平均持仓周期 (Average Holding Period)
(可选) 月度/年度收益率统计：计算每个月/年的收益率，并展示其均值、标准差等。
可视化辅助函数 (可选，也可直接在UI层实现)：

用于生成净值曲线数据（策略 vs 基准）、收益率分布直方图数据、回撤序列数据等，供UI模块调用。
模块化测试与验证要求 (performance_analyzer.py 完成时)：

单元测试 (在 tests/unit/test_performance_analyzer.py 中概念性完成或实际编写)：
为每一个核心性能指标计算函数编写独立的单元测试用例。
构造几组不同特征的模拟交易记录和账户净值序列作为输入数据。
手动计算或使用其他可靠工具预先计算这些模拟数据对应的性能指标期望值。
断言验证 performance_analyzer.py 中各函数计算得到的指标值与预期值是否（在合理浮点精度范围内）一致。
测试边界条件（如无交易、全亏损、全盈利、回测期过短等）。
文档同步： 为 PerformanceAnalyzer 类（或各函数）及其所有公共方法、参数、返回值编写详尽的docstrings。
请完成 performance_analyzer.py 模块的实现和测试思路。确保所有指标计算逻辑准确无误。”

