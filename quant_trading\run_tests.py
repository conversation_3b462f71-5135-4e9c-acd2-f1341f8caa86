#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
运行测试脚本
用于运行所有测试
"""

import os
import sys
import unittest
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join('logs', 'run_tests.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('run_tests')

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.append(project_root)

def run_tests():
    """运行所有测试"""
    logger.info("开始运行测试")

    # 确保日志目录存在
    os.makedirs('logs', exist_ok=True)

    # 发现并运行测试
    test_loader = unittest.TestLoader()
    test_suite = test_loader.discover('quant_trading/tests', pattern='test_*.py')

    # 运行测试
    test_runner = unittest.TextTestRunner(verbosity=2)
    result = test_runner.run(test_suite)

    # 输出测试结果
    logger.info(f"测试完成，结果: 运行 {result.testsRun} 个测试，失败 {len(result.failures)}，错误 {len(result.errors)}")

    # 如果有失败或错误，输出详细信息
    if result.failures:
        logger.error("失败的测试:")
        for test, traceback in result.failures:
            logger.error(f"{test}: {traceback}")

    if result.errors:
        logger.error("错误的测试:")
        for test, traceback in result.errors:
            logger.error(f"{test}: {traceback}")

    return result.wasSuccessful()

if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)
