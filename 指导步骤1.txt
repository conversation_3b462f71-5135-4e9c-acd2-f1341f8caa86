块 1: 项目初始化、核心目标与环境配置

本块目标： 初始化项目，明确总体目标与核心技术选型，并建立隔离的Python开发环境。

我需要你协助我构建一个复杂的量化交易模型。该模型将使用深度强化学习（DRL）进行训练，并集成到一个带用户界面的应用程序中。我们将分阶段进行。

项目核心愿景：

开发一个基于DRL的量化交易模型。
最终交付物是一个功能完善的应用程序，包含用户界面（UI），用于策略监控、数据管理、风险评估和辅助交易决策。
核心原则： 每个模块完成后必须严格测试。整个项目交付前必须进行全面测试。
一、项目基础：核心目标、约束与技术选型

运行环境：

要求： 所有开发、依赖管理和运行都必须在隔离的Python虚拟环境（如 venv 或 conda）中进行，确保环境纯净与可复现。
操作： 请创建并激活此虚拟环境。后续所有Python包都将安装在此环境中。
用户界面 (UI) 开发原则：

要求： 项目必须包含专业且用户友好的图形用户界面。
UI框架选型： 请在 Streamlit（快速迭代、Pythonic）和 Dash（复杂交互、回调更强）中选择其一，并保持一致性。选择时请考虑项目需求和可维护性。
UI状态管理： 必须实现健壮的UI状态管理，确保用户在不同UI模块间的操作和数据（如选定的品种、DRL参数、训练状态）得以妥善保存和传递。
主要数据源：

来源： 使用 akshare API 获取金融品种数据。
关键初始操作：
请查阅 akshare 的官方文档和网站，充分理解其数据提取规则。
在开始大规模数据相关开发前，务必验证此数据源当前的可用性和可靠性，并向我报告结果。
核心编程及DRL技术栈：

主要编程语言： Python。
DRL框架（选择其一并精通其API）：
Stable-Baselines3 (基于PyTorch，用户友好，支持Gymnasium)。
RLlib (Ray) (功能强大，支持分布式，学习曲线稍陡)。
强烈建议使用 Stable-Baselines3。
深度学习后端： PyTorch (Stable-Baselines3默认) 或 TensorFlow。
交易环境接口标准： 自定义交易环境必须遵循 Gymnasium (前OpenAI Gym) API标准。
数据处理与分析库： Pandas, NumPy。
机器学习库（辅助）： Scikit-learn (可用于特征工程或基准模型对比)。
可视化库： Matplotlib, Seaborn, Plotly (根据UI框架选择)。
严格交易约束（在DRL环境及回测中强制执行）：

所有买卖操作均在当日收盘价执行。
任何已建立头寸，在平仓前必须至少持有3个交易日。此规则必须在DRL环境的 step 函数中强制执行（例如，违规尝试卖出则动作无效或受惩罚）。
严禁使用任何形式的杠杆。资金管理需体现在动作空间设计或环境逻辑中。
计算效率优化：

GPU利用： DRL训练神经网络时，应有效利用GPU。请确保项目虚拟环境正确配置并启用GPU支持。UI应能提示是否正在使用GPU训练。
CPU利用： 超参数优化、多环境并行采样等应充分利用多核CPU。
代码质量与软件架构：

遵循面向对象和模块化设计，确保代码高内聚、低耦合，易于测试、维护和扩展。
严格分离UI逻辑与 core_logic（包含DRL智能体、训练环境、特征工程等）。
采用清晰命名规范、详尽注释和文档字符串。
DRL智能体持久化：

训练完成的DRL智能体（网络权重等）必须能稳定保存到磁盘（使用DRL框架的API），并能重新加载用于评估或生成信号。
配置管理：

推荐使用配置文件（YAML或JSON）管理关键参数（DRL超参数、环境参数、数据源配置、回测参数等），便于版本控制和实验复现。UI可加载和修改这些配置。
