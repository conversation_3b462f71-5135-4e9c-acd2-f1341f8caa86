#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
交易环境测试脚本
测试量化交易系统的交易环境模块
"""

import os
import sys
import time
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import traceback
import json
import matplotlib.pyplot as plt
import gymnasium as gym

# 设置日志
os.makedirs('logs', exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join('logs', 'trading_environment_test.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('trading_environment_test')

def get_processed_data():
    """获取处理后的数据用于测试"""
    logger.info("获取处理后的数据")
    
    try:
        # 尝试导入数据处理模块和特征工程模块
        try:
            from quant_project.core_logic.data_handler import DataHandler
            from quant_project.core_logic.feature_engineer import FeatureEngineer
            logger.info("成功导入DataHandler和FeatureEngineer模块")
        except ImportError:
            try:
                from quant_trading.data.data_handler import DataHandler
                from quant_trading.features.feature_engineer import FeatureEngineer
                logger.info("成功导入DataHandler和FeatureEngineer模块(从根目录)")
            except ImportError as e:
                logger.error(f"导入模块失败: {str(e)}")
                return None
        
        # 创建数据处理器实例
        data_handler = DataHandler()
        
        # 获取样本数据
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')
        
        sample_data = data_handler.get_stock_data(
            stock_code='sh000001',
            start_date=start_date,
            end_date=end_date,
            frequency='日线'
        )
        
        if sample_data is None or sample_data.empty:
            logger.warning("获取样本数据失败: 数据为空")
            return None
        
        # 创建特征工程器实例
        feature_engineer = FeatureEngineer()
        
        # 设置特征配置
        feature_config = {
            'moving_averages': {'windows': [5, 10, 20]},
            'rsi': {'window': 14},
            'macd': {'fast': 12, 'slow': 26, 'signal': 9}
        }
        
        feature_engineer.feature_config = feature_config
        
        # 生成特征
        processed_data = feature_engineer.generate_features(sample_data)
        
        if processed_data is not None and not processed_data.empty:
            logger.info(f"成功生成特征: {len(processed_data.columns) - len(sample_data.columns)} 个新特征")
            return processed_data
        else:
            logger.warning("生成特征失败: 数据为空")
            return None
    
    except Exception as e:
        logger.error(f"获取处理后的数据时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return None

def test_trading_environment(processed_data):
    """测试交易环境模块"""
    logger.info("测试交易环境模块")
    
    if processed_data is None or processed_data.empty:
        logger.error("无法测试交易环境模块: 处理后的数据不可用")
        return {
            'success': False,
            'error': '处理后的数据不可用'
        }
    
    try:
        # 尝试导入交易环境模块
        try:
            from quant_project.core_logic.trading_environment import TradingEnvironment
            logger.info("成功导入TradingEnvironment模块")
        except ImportError:
            try:
                from quant_trading.trading.trading_environment import TradingEnvironment
                logger.info("成功导入TradingEnvironment模块(从根目录)")
            except ImportError as e:
                logger.error(f"导入TradingEnvironment模块失败: {str(e)}")
                return {
                    'success': False,
                    'error': f"导入TradingEnvironment模块失败: {str(e)}"
                }
        
        # 测试不同参数配置
        env_configs = [
            # 基本配置
            {
                'initial_capital': 100000,
                'transaction_fee_rate': 0.0003,
                'min_holding_days': 1,
                'slippage_rate': 0.0001
            },
            # 高手续费配置
            {
                'initial_capital': 100000,
                'transaction_fee_rate': 0.001,
                'min_holding_days': 1,
                'slippage_rate': 0.0001
            },
            # 长期持有配置
            {
                'initial_capital': 100000,
                'transaction_fee_rate': 0.0003,
                'min_holding_days': 5,
                'slippage_rate': 0.0001
            }
        ]
        
        results = []
        
        for i, config in enumerate(env_configs):
            try:
                logger.info(f"测试交易环境配置 {i+1}")
                
                # 创建交易环境
                env = TradingEnvironment(
                    data=processed_data,
                    **config
                )
                
                # 测试环境初始化
                observation, info = env.reset()
                
                logger.info(f"环境初始化成功，观测值形状: {observation.shape}")
                
                # 测试交易操作
                actions = [0, 1, 2]  # 持有, 买入, 卖出
                action_results = []
                
                for action in actions:
                    # 重置环境
                    observation, info = env.reset()
                    
                    # 执行动作
                    next_observation, reward, terminated, truncated, info = env.step(action)
                    
                    action_results.append({
                        'action': action,
                        'reward': reward,
                        'terminated': terminated,
                        'truncated': truncated,
                        'info': str(info)
                    })
                    
                    logger.info(f"执行动作 {action}, 奖励: {reward}, 终止: {terminated}, 截断: {truncated}")
                
                # 测试完整交易序列
                observation, info = env.reset()
                done = False
                total_reward = 0
                steps = 0
                
                while not done and steps < 100:  # 限制步数以避免无限循环
                    action = np.random.choice(actions)  # 随机选择动作
                    next_observation, reward, terminated, truncated, info = env.step(action)
                    total_reward += reward
                    done = terminated or truncated
                    steps += 1
                
                logger.info(f"完整交易序列测试完成，总步数: {steps}, 总奖励: {total_reward:.4f}")
                
                # 验证环境是否符合Gymnasium标准
                try:
                    from stable_baselines3.common.env_checker import check_env
                    check_env(env, warn=True)
                    gym_compatible = True
                    logger.info("环境符合Gymnasium标准")
                except Exception as e:
                    gym_compatible = False
                    logger.warning(f"环境不符合Gymnasium标准: {str(e)}")
                
                results.append({
                    'config_id': i+1,
                    'success': True,
                    'action_results': action_results,
                    'total_steps': steps,
                    'total_reward': total_reward,
                    'gym_compatible': gym_compatible
                })
            
            except Exception as e:
                logger.error(f"测试交易环境配置 {i+1} 时出错: {str(e)}")
                logger.error(traceback.format_exc())
                results.append({
                    'config_id': i+1,
                    'success': False,
                    'error': str(e)
                })
        
        # 保存测试结果
        os.makedirs('test_results', exist_ok=True)
        
        # 将结果转换为可序列化的格式
        serializable_results = []
        for result in results:
            serializable_result = {}
            for k, v in result.items():
                if k == 'action_results':
                    serializable_result[k] = [{kk: str(vv) for kk, vv in action.items()} for action in v]
                else:
                    serializable_result[k] = str(v) if not isinstance(v, (bool, int, float, str)) else v
            serializable_results.append(serializable_result)
        
        with open('test_results/trading_environment_test.json', 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=4)
        
        success_rate = sum(1 for r in results if r.get('success', False)) / len(results) * 100
        logger.info(f"交易环境模块测试完成，成功率: {success_rate:.2f}%")
        
        return {
            'success': True,
            'success_rate': success_rate,
            'env_configs_tested': len(env_configs),
            'results': results
        }
    
    except Exception as e:
        logger.error(f"测试交易环境模块时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            'success': False,
            'error': str(e)
        }

def run_all_tests():
    """运行所有交易环境测试"""
    logger.info("开始运行交易环境测试")
    
    # 获取处理后的数据
    processed_data = get_processed_data()
    
    # 测试交易环境模块
    if processed_data is not None and not processed_data.empty:
        results = test_trading_environment(processed_data)
    else:
        results = {
            'success': False,
            'error': '无法获取处理后的数据'
        }
    
    logger.info(f"交易环境测试完成，结果: {results}")
    
    return results

if __name__ == "__main__":
    run_all_tests()
