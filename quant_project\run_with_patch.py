"""
带补丁运行量化交易系统

此脚本应用必要的补丁并启动量化交易系统。
解决 Python 3.13 + PyTorch + Streamlit 兼容性问题。
"""

import os
import sys
import subprocess
import importlib.util
import argparse

def check_patch_file():
    """检查补丁文件是否存在"""
    patch_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'fix_asyncio_torch.py')
    return os.path.exists(patch_file)

def apply_patches():
    """应用所有补丁"""
    try:
        # 导入并应用补丁
        from fix_asyncio_torch import apply_all_patches
        result = apply_all_patches()
        print(f"补丁应用{'成功' if result else '失败'}")
        return result
    except Exception as e:
        print(f"应用补丁时出错: {str(e)}")
        return False

def run_streamlit_app(headless=False):
    """运行Streamlit应用"""
    app_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'main_app.py')
    
    if not os.path.exists(app_file):
        print(f"错误: 找不到应用文件 {app_file}")
        return False
    
    print(f"正在启动应用: {app_file}")
    
    # 使用subprocess运行Streamlit应用
    try:
        cmd = [sys.executable, "-m", "streamlit", "run", app_file]
        if headless:
            cmd.extend(["--server.headless", "true"])
        
        print(f"执行命令: {' '.join(cmd)}")
        subprocess.run(cmd, check=True)
        return True
    except subprocess.CalledProcessError as e:
        print(f"运行应用时出错: {str(e)}")
        return False
    except Exception as e:
        print(f"发生未知错误: {str(e)}")
        return False

def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='运行带补丁的量化交易系统')
    parser.add_argument('--headless', action='store_true', help='以无浏览器模式运行（不自动打开浏览器）')
    args = parser.parse_args()
    
    print("=" * 80)
    print("带补丁运行量化交易系统")
    if args.headless:
        print("以无浏览器模式运行（不会自动打开浏览器）")
    print("=" * 80)
    
    # 检查补丁文件
    if not check_patch_file():
        print("错误: 找不到补丁文件 fix_asyncio_torch.py")
        return
    
    # 应用补丁
    if not apply_patches():
        print("警告: 补丁应用失败，但仍将尝试运行应用")
    
    # 运行应用
    run_streamlit_app(headless=args.headless)

if __name__ == "__main__":
    main()
