2025-05-29 09:45:46,507 - fixed_app - INFO - Starting application
2025-05-29 09:45:46,508 - fixed_app - INFO - Current directory: C:\cursor\量化\quant_project
2025-05-29 09:45:46,508 - fixed_app - INFO - Python path: ['fixed_main_app.py', '', 'C:\\cursor\\量化\\quant_project', 'C:\\Python313\\python313.zip', 'C:\\Python313\\DLLs', 'C:\\Python313\\Lib', 'C:\\Python313', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\Pythonwin', 'C:\\Python313\\Lib\\site-packages', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化']
2025-05-29 09:45:47,159 - fixed_app - INFO - Basic libraries imported successfully
2025-05-29 09:45:47,224 - fixed_app - INFO - Streamlit page configuration set
2025-05-29 09:45:50,364 - fixed_app - INFO - Auto factor mining modules imported successfully
2025-05-29 09:45:50,365 - fixed_app - INFO - Auto factor mining page imported successfully
2025-05-29 09:45:50,367 - fixed_app - INFO - Executing main function
2025-05-29 09:46:29,137 - fixed_app - INFO - Starting application
2025-05-29 09:46:29,137 - fixed_app - INFO - Current directory: C:\cursor\量化\quant_project
2025-05-29 09:46:29,138 - fixed_app - INFO - Python path: ['fixed_main_app.py', '', 'C:\\cursor\\量化\\quant_project', 'C:\\Python313\\python313.zip', 'C:\\Python313\\DLLs', 'C:\\Python313\\Lib', 'C:\\Python313', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\Pythonwin', 'C:\\Python313\\Lib\\site-packages', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化']
2025-05-29 09:46:29,138 - fixed_app - INFO - Basic libraries imported successfully
2025-05-29 09:46:29,138 - fixed_app - INFO - Streamlit page configuration set
2025-05-29 09:46:29,138 - fixed_app - INFO - Auto factor mining modules imported successfully
2025-05-29 09:46:29,139 - fixed_app - INFO - Auto factor mining page imported successfully
2025-05-29 09:46:29,139 - fixed_app - INFO - Executing main function
2025-05-29 09:46:29,141 - fixed_app - INFO - Loading auto factor mining page...
2025-05-29 09:46:29,649 - fixed_app - INFO - Auto factor mining page loaded successfully
2025-05-29 09:46:42,223 - fixed_app - INFO - Starting application
2025-05-29 09:46:42,224 - fixed_app - INFO - Current directory: C:\cursor\量化\quant_project
2025-05-29 09:46:42,224 - fixed_app - INFO - Python path: ['fixed_main_app.py', '', 'C:\\cursor\\量化\\quant_project', 'C:\\Python313\\python313.zip', 'C:\\Python313\\DLLs', 'C:\\Python313\\Lib', 'C:\\Python313', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\Pythonwin', 'C:\\Python313\\Lib\\site-packages', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化']
2025-05-29 09:46:42,224 - fixed_app - INFO - Basic libraries imported successfully
2025-05-29 09:46:42,225 - fixed_app - INFO - Streamlit page configuration set
2025-05-29 09:46:42,226 - fixed_app - INFO - Auto factor mining modules imported successfully
2025-05-29 09:46:42,226 - fixed_app - INFO - Auto factor mining page imported successfully
2025-05-29 09:46:42,227 - fixed_app - INFO - Executing main function
2025-05-29 09:46:42,229 - fixed_app - INFO - Loading auto factor mining page...
2025-05-29 09:46:42,764 - drl_trading - INFO - 启动自动因子挖掘流水线，股票: sh000001, 时间: 2024-05-29 至 2025-05-29
2025-05-29 09:46:42,765 - drl_trading - INFO - 从AkShare获取数据: sh000001, 2024-05-29 至 2025-05-29, 频率: 日线
2025-05-29 09:46:42,765 - drl_trading - INFO - 获取上海证券交易所股票: 000001
2025-05-29 09:46:42,765 - drl_trading - INFO - 尝试使用stock_zh_index_daily_em获取数据: sh000001
2025-05-29 09:46:43,337 - drl_trading - INFO - 使用stock_zh_index_daily_em获取到的数据列名: ['date', 'open', 'close', 'high', 'low', 'volume', 'amount']
2025-05-29 09:46:43,367 - drl_trading - INFO - 数据已缓存: data_cache\dfeed5235d08e2f4ea6576c2e89e8245.csv
2025-05-29 09:46:43,367 - drl_trading - INFO - API返回的数据列名: ['date', 'open', 'close', 'high', 'low', 'volume', 'amount']
2025-05-29 09:46:43,373 - drl_trading - INFO - API返回的数据前5行: 
         date    open   close    high     low  volume    amount
0  1990-12-19   96.05   99.98   99.98   95.79    1260  494000.0
1  1990-12-20  104.30  104.39  104.39   99.98     197   84000.0
2  1990-12-21  109.07  109.13  109.13  103.73      28   16000.0
3  1990-12-24  113.57  114.55  114.55  109.13      32   31000.0
4  1990-12-25  120.09  120.25  120.25  114.55      15    6000.0
2025-05-29 09:46:43,374 - drl_trading - INFO - 找到日期列: date
2025-05-29 09:46:43,375 - drl_trading - INFO - 找到日期列用于设置索引: date
2025-05-29 09:46:43,379 - drl_trading - INFO - 成功将 date 设置为索引
2025-05-29 09:46:43,380 - drl_trading - INFO - 开始数据清洗过程...
2025-05-29 09:46:43,385 - drl_trading - INFO - 列 开盘 中检测到 43 个异常值 (占比 0.51%)
2025-05-29 09:46:43,388 - drl_trading - INFO - 已使用局部中位数替换 开盘 列中的异常值
2025-05-29 09:46:43,393 - drl_trading - INFO - 列 最高 中检测到 44 个异常值 (占比 0.52%)
2025-05-29 09:46:43,395 - drl_trading - INFO - 已使用局部中位数替换 最高 列中的异常值
2025-05-29 09:46:43,398 - drl_trading - INFO - 列 最低 中检测到 41 个异常值 (占比 0.49%)
2025-05-29 09:46:43,400 - drl_trading - INFO - 已使用局部中位数替换 最低 列中的异常值
2025-05-29 09:46:43,403 - drl_trading - INFO - 列 收盘 中检测到 42 个异常值 (占比 0.50%)
2025-05-29 09:46:43,406 - drl_trading - INFO - 已使用局部中位数替换 收盘 列中的异常值
2025-05-29 09:46:43,411 - drl_trading - INFO - 列 成交量 中检测到 364 个异常值 (占比 4.33%)
2025-05-29 09:46:43,413 - drl_trading - INFO - 已使用局部平均值替换 成交量 列中的异常值
2025-05-29 09:46:43,416 - drl_trading - INFO - 数据清洗完成
2025-05-29 09:46:43,416 - drl_trading - INFO - 开始时间序列对齐...
2025-05-29 09:46:43,419 - drl_trading - INFO - 时间序列对齐完成
2025-05-29 09:46:43,421 - drl_trading - INFO - 成功设置时区为 Asia/Shanghai
2025-05-29 09:46:43,425 - drl_trading - INFO - 数据已缓存: data_cache\dfeed5235d08e2f4ea6576c2e89e8245.csv
2025-05-29 09:46:43,425 - drl_trading - INFO - 获取到 262 行数据
2025-05-29 09:46:43,426 - drl_trading - INFO - 生成基础因子...
2025-05-29 09:46:43,426 - drl_trading - INFO - 生成技术指标因子...
2025-05-29 09:46:43,426 - drl_trading - WARNING - 输入数据缺少必要的列: open
2025-05-29 09:46:43,426 - drl_trading - INFO - 生成组合因子...
2025-05-29 09:46:43,426 - drl_trading - INFO - 生成交叉特征...
2025-05-29 09:46:43,426 - drl_trading - INFO - 成功生成 0 个交叉特征
2025-05-29 09:46:43,427 - drl_trading - INFO - 生成时序特征...
2025-05-29 09:46:43,427 - drl_trading - INFO - 成功生成 0 个时序特征
2025-05-29 09:46:43,427 - drl_trading - INFO - 共生成 0 个因子
2025-05-29 09:46:43,427 - drl_trading - INFO - 评估因子...
2025-05-29 09:46:43,427 - drl_trading - INFO - 开始评估因子...
2025-05-29 09:46:43,427 - drl_trading - INFO - 因子评估完成，共 0 个因子，有效因子 0 个
2025-05-29 09:46:43,427 - drl_trading - INFO - 去除高度相关因子...
2025-05-29 09:46:43,427 - drl_trading - INFO - 开始去除高相关因子，相关性阈值: 0.7...
2025-05-29 09:46:43,428 - drl_trading - INFO - 原始因子数量: 0, 去除高相关后: 0, 删除: 0
2025-05-29 09:46:43,428 - drl_trading - INFO - 选择最终因子...
2025-05-29 09:46:43,428 - drl_trading - INFO - 选择前20个最佳因子...
2025-05-29 09:46:43,428 - drl_trading - INFO - 选择了 0 个最佳因子
2025-05-29 09:46:43,428 - drl_trading - INFO - 自动因子挖掘流水线完成，最终选出 0 个有效因子
2025-05-29 09:46:43,437 - fixed_app - INFO - Auto factor mining page loaded successfully
2025-05-29 09:46:54,945 - fixed_app - INFO - Starting application
2025-05-29 09:46:54,946 - fixed_app - INFO - Current directory: C:\cursor\量化\quant_project
2025-05-29 09:46:54,946 - fixed_app - INFO - Python path: ['fixed_main_app.py', '', 'C:\\cursor\\量化\\quant_project', 'C:\\Python313\\python313.zip', 'C:\\Python313\\DLLs', 'C:\\Python313\\Lib', 'C:\\Python313', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\Pythonwin', 'C:\\Python313\\Lib\\site-packages', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化']
2025-05-29 09:46:54,946 - fixed_app - INFO - Basic libraries imported successfully
2025-05-29 09:46:54,947 - fixed_app - INFO - Streamlit page configuration set
2025-05-29 09:46:54,948 - fixed_app - INFO - Auto factor mining modules imported successfully
2025-05-29 09:46:54,948 - fixed_app - INFO - Auto factor mining page imported successfully
2025-05-29 09:46:54,948 - fixed_app - INFO - Executing main function
2025-05-29 09:46:54,950 - fixed_app - INFO - Loading auto factor mining page...
2025-05-29 09:46:55,469 - fixed_app - INFO - Auto factor mining page loaded successfully
2025-05-29 09:46:55,652 - fixed_app - INFO - Starting application
2025-05-29 09:46:55,653 - fixed_app - INFO - Current directory: C:\cursor\量化\quant_project
2025-05-29 09:46:55,653 - fixed_app - INFO - Python path: ['fixed_main_app.py', '', 'C:\\cursor\\量化\\quant_project', 'C:\\Python313\\python313.zip', 'C:\\Python313\\DLLs', 'C:\\Python313\\Lib', 'C:\\Python313', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\Pythonwin', 'C:\\Python313\\Lib\\site-packages', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化']
2025-05-29 09:46:55,653 - fixed_app - INFO - Basic libraries imported successfully
2025-05-29 09:46:55,653 - fixed_app - INFO - Streamlit page configuration set
2025-05-29 09:46:55,654 - fixed_app - INFO - Auto factor mining modules imported successfully
2025-05-29 09:46:55,654 - fixed_app - INFO - Auto factor mining page imported successfully
2025-05-29 09:46:55,655 - fixed_app - INFO - Executing main function
2025-05-29 09:46:55,656 - fixed_app - INFO - Loading auto factor mining page...
2025-05-29 09:46:56,170 - drl_trading - INFO - 启动自动因子挖掘流水线，股票: sh000001, 时间: 2022-05-05 至 2025-05-29
2025-05-29 09:46:56,170 - drl_trading - INFO - 从AkShare获取数据: sh000001, 2022-05-05 至 2025-05-29, 频率: 日线
2025-05-29 09:46:56,171 - drl_trading - INFO - 获取上海证券交易所股票: 000001
2025-05-29 09:46:56,171 - drl_trading - INFO - 尝试使用stock_zh_index_daily_em获取数据: sh000001
2025-05-29 09:46:56,773 - drl_trading - INFO - 使用stock_zh_index_daily_em获取到的数据列名: ['date', 'open', 'close', 'high', 'low', 'volume', 'amount']
2025-05-29 09:46:56,801 - drl_trading - INFO - 数据已缓存: data_cache\70fd205ee4dde485f1cdb0dd71b30409.csv
2025-05-29 09:46:56,802 - drl_trading - INFO - API返回的数据列名: ['date', 'open', 'close', 'high', 'low', 'volume', 'amount']
2025-05-29 09:46:56,807 - drl_trading - INFO - API返回的数据前5行: 
         date    open   close    high     low  volume    amount
0  1990-12-19   96.05   99.98   99.98   95.79    1260  494000.0
1  1990-12-20  104.30  104.39  104.39   99.98     197   84000.0
2  1990-12-21  109.07  109.13  109.13  103.73      28   16000.0
3  1990-12-24  113.57  114.55  114.55  109.13      32   31000.0
4  1990-12-25  120.09  120.25  120.25  114.55      15    6000.0
2025-05-29 09:46:56,807 - drl_trading - INFO - 找到日期列: date
2025-05-29 09:46:56,809 - drl_trading - INFO - 找到日期列用于设置索引: date
2025-05-29 09:46:56,813 - drl_trading - INFO - 成功将 date 设置为索引
2025-05-29 09:46:56,813 - drl_trading - INFO - 开始数据清洗过程...
2025-05-29 09:46:56,819 - drl_trading - INFO - 列 开盘 中检测到 43 个异常值 (占比 0.51%)
2025-05-29 09:46:56,823 - drl_trading - INFO - 已使用局部中位数替换 开盘 列中的异常值
2025-05-29 09:46:56,826 - drl_trading - INFO - 列 最高 中检测到 44 个异常值 (占比 0.52%)
2025-05-29 09:46:56,829 - drl_trading - INFO - 已使用局部中位数替换 最高 列中的异常值
2025-05-29 09:46:56,832 - drl_trading - INFO - 列 最低 中检测到 41 个异常值 (占比 0.49%)
2025-05-29 09:46:56,834 - drl_trading - INFO - 已使用局部中位数替换 最低 列中的异常值
2025-05-29 09:46:56,837 - drl_trading - INFO - 列 收盘 中检测到 42 个异常值 (占比 0.50%)
2025-05-29 09:46:56,840 - drl_trading - INFO - 已使用局部中位数替换 收盘 列中的异常值
2025-05-29 09:46:56,843 - drl_trading - INFO - 列 成交量 中检测到 364 个异常值 (占比 4.33%)
2025-05-29 09:46:56,845 - drl_trading - INFO - 已使用局部平均值替换 成交量 列中的异常值
2025-05-29 09:46:56,848 - drl_trading - INFO - 数据清洗完成
2025-05-29 09:46:56,848 - drl_trading - INFO - 开始时间序列对齐...
2025-05-29 09:46:56,851 - drl_trading - INFO - 时间序列对齐完成
2025-05-29 09:46:56,851 - drl_trading - INFO - 成功设置时区为 Asia/Shanghai
2025-05-29 09:46:56,859 - drl_trading - INFO - 数据已缓存: data_cache\70fd205ee4dde485f1cdb0dd71b30409.csv
2025-05-29 09:46:56,860 - drl_trading - INFO - 获取到 801 行数据
2025-05-29 09:46:56,860 - drl_trading - INFO - 生成基础因子...
2025-05-29 09:46:56,860 - drl_trading - INFO - 生成技术指标因子...
2025-05-29 09:46:56,860 - drl_trading - WARNING - 输入数据缺少必要的列: open
2025-05-29 09:46:56,860 - drl_trading - INFO - 生成组合因子...
2025-05-29 09:46:56,860 - drl_trading - INFO - 生成交叉特征...
2025-05-29 09:46:56,861 - drl_trading - INFO - 成功生成 0 个交叉特征
2025-05-29 09:46:56,861 - drl_trading - INFO - 生成时序特征...
2025-05-29 09:46:56,861 - drl_trading - INFO - 成功生成 0 个时序特征
2025-05-29 09:46:56,861 - drl_trading - INFO - 共生成 0 个因子
2025-05-29 09:46:56,861 - drl_trading - INFO - 评估因子...
2025-05-29 09:46:56,861 - drl_trading - INFO - 开始评估因子...
2025-05-29 09:46:56,861 - drl_trading - INFO - 因子评估完成，共 0 个因子，有效因子 0 个
2025-05-29 09:46:56,861 - drl_trading - INFO - 去除高度相关因子...
2025-05-29 09:46:56,862 - drl_trading - INFO - 开始去除高相关因子，相关性阈值: 0.7...
2025-05-29 09:46:56,862 - drl_trading - INFO - 原始因子数量: 0, 去除高相关后: 0, 删除: 0
2025-05-29 09:46:56,862 - drl_trading - INFO - 选择最终因子...
2025-05-29 09:46:56,862 - drl_trading - INFO - 选择前20个最佳因子...
2025-05-29 09:46:56,863 - drl_trading - INFO - 选择了 0 个最佳因子
2025-05-29 09:46:56,863 - drl_trading - INFO - 自动因子挖掘流水线完成，最终选出 0 个有效因子
2025-05-29 09:46:56,872 - fixed_app - INFO - Auto factor mining page loaded successfully
2025-05-29 09:47:00,079 - fixed_app - INFO - Starting application
2025-05-29 09:47:00,079 - fixed_app - INFO - Current directory: C:\cursor\量化\quant_project
2025-05-29 09:47:00,080 - fixed_app - INFO - Python path: ['fixed_main_app.py', '', 'C:\\cursor\\量化\\quant_project', 'C:\\Python313\\python313.zip', 'C:\\Python313\\DLLs', 'C:\\Python313\\Lib', 'C:\\Python313', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\Pythonwin', 'C:\\Python313\\Lib\\site-packages', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化']
2025-05-29 09:47:00,080 - fixed_app - INFO - Basic libraries imported successfully
2025-05-29 09:47:00,080 - fixed_app - INFO - Streamlit page configuration set
2025-05-29 09:47:00,080 - fixed_app - INFO - Auto factor mining modules imported successfully
2025-05-29 09:47:00,081 - fixed_app - INFO - Auto factor mining page imported successfully
2025-05-29 09:47:00,082 - fixed_app - INFO - Executing main function
2025-05-29 09:47:00,083 - fixed_app - INFO - Loading auto factor mining page...
2025-05-29 09:47:00,600 - fixed_app - INFO - Auto factor mining page loaded successfully
2025-05-29 09:47:00,989 - fixed_app - INFO - Starting application
2025-05-29 09:47:00,989 - fixed_app - INFO - Current directory: C:\cursor\量化\quant_project
2025-05-29 09:47:00,990 - fixed_app - INFO - Python path: ['fixed_main_app.py', '', 'C:\\cursor\\量化\\quant_project', 'C:\\Python313\\python313.zip', 'C:\\Python313\\DLLs', 'C:\\Python313\\Lib', 'C:\\Python313', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\Pythonwin', 'C:\\Python313\\Lib\\site-packages', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化']
2025-05-29 09:47:00,990 - fixed_app - INFO - Basic libraries imported successfully
2025-05-29 09:47:00,991 - fixed_app - INFO - Streamlit page configuration set
2025-05-29 09:47:00,991 - fixed_app - INFO - Auto factor mining modules imported successfully
2025-05-29 09:47:00,991 - fixed_app - INFO - Auto factor mining page imported successfully
2025-05-29 09:47:00,992 - fixed_app - INFO - Executing main function
2025-05-29 09:47:00,993 - fixed_app - INFO - Loading auto factor mining page...
2025-05-29 09:47:01,501 - drl_trading - INFO - 启动自动因子挖掘流水线，股票: sh000001, 时间: 2022-05-05 至 2025-05-29
2025-05-29 09:47:01,501 - drl_trading - INFO - 从缓存加载数据: data_cache\70fd205ee4dde485f1cdb0dd71b30409.csv
2025-05-29 09:47:01,526 - drl_trading - INFO - 获取到 801 行数据
2025-05-29 09:47:01,527 - drl_trading - INFO - 生成基础因子...
2025-05-29 09:47:01,527 - drl_trading - INFO - 生成技术指标因子...
2025-05-29 09:47:01,527 - drl_trading - WARNING - 输入数据缺少必要的列: open
2025-05-29 09:47:01,527 - drl_trading - INFO - 生成组合因子...
2025-05-29 09:47:01,528 - drl_trading - INFO - 生成交叉特征...
2025-05-29 09:47:01,528 - drl_trading - INFO - 成功生成 0 个交叉特征
2025-05-29 09:47:01,528 - drl_trading - INFO - 生成时序特征...
2025-05-29 09:47:01,528 - drl_trading - INFO - 成功生成 0 个时序特征
2025-05-29 09:47:01,528 - drl_trading - INFO - 共生成 0 个因子
2025-05-29 09:47:01,528 - drl_trading - INFO - 评估因子...
2025-05-29 09:47:01,528 - drl_trading - INFO - 开始评估因子...
2025-05-29 09:47:01,529 - drl_trading - INFO - 因子评估完成，共 0 个因子，有效因子 0 个
2025-05-29 09:47:01,529 - drl_trading - INFO - 去除高度相关因子...
2025-05-29 09:47:01,529 - drl_trading - INFO - 开始去除高相关因子，相关性阈值: 0.7...
2025-05-29 09:47:01,529 - drl_trading - INFO - 原始因子数量: 0, 去除高相关后: 0, 删除: 0
2025-05-29 09:47:01,530 - drl_trading - INFO - 选择最终因子...
2025-05-29 09:47:01,530 - drl_trading - INFO - 选择前20个最佳因子...
2025-05-29 09:47:01,530 - drl_trading - INFO - 选择了 0 个最佳因子
2025-05-29 09:47:01,530 - drl_trading - INFO - 自动因子挖掘流水线完成，最终选出 0 个有效因子
2025-05-29 09:47:01,539 - fixed_app - INFO - Auto factor mining page loaded successfully
2025-05-29 09:47:29,220 - fixed_app - INFO - Starting application
2025-05-29 09:47:29,220 - fixed_app - INFO - Current directory: C:\cursor\量化\quant_project
2025-05-29 09:47:29,221 - fixed_app - INFO - Python path: ['fixed_main_app.py', '', 'C:\\cursor\\量化\\quant_project', 'C:\\Python313\\python313.zip', 'C:\\Python313\\DLLs', 'C:\\Python313\\Lib', 'C:\\Python313', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\win32', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\win32\\lib', 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python313\\site-packages\\Pythonwin', 'C:\\Python313\\Lib\\site-packages', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化', 'C:\\cursor\\量化\\quant_project', 'C:\\cursor\\量化']
2025-05-29 09:47:29,221 - fixed_app - INFO - Basic libraries imported successfully
2025-05-29 09:47:29,222 - fixed_app - INFO - Streamlit page configuration set
2025-05-29 09:47:29,222 - fixed_app - INFO - Auto factor mining modules imported successfully
2025-05-29 09:47:29,222 - fixed_app - INFO - Auto factor mining page imported successfully
2025-05-29 09:47:29,223 - fixed_app - INFO - Executing main function
