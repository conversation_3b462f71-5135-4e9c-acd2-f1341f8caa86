#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
特征工程适配器模块
提供特征工程配置格式的转换和兼容性支持
"""

import logging
import copy
from typing import Dict, List, Any, Union, Optional
import pandas as pd
import numpy as np

logger = logging.getLogger('drl_trading')

class FeatureEngineeringAdapter:
    """
    特征工程适配器类
    用于处理不同格式的特征配置，提供兼容性支持
    """
    
    def __init__(self):
        """初始化特征工程适配器"""
        self.logger = logger
    
    def adapt_config(self, config):
        """
        适配特征工程配置，将扁平结构转换为标准结构
        
        参数:
            config (dict): 特征工程配置，可以是扁平结构或标准结构
            
        返回:
            dict: 转换后的标准结构配置
        """
        # 检查配置是否已经是标准结构
        if self._is_standard_config(config):
            return config
            
        # 将扁平结构转换为标准结构
        return self._convert_flat_to_standard(config)
    
    def _is_standard_config(self, config):
        """
        检查配置是否为标准结构
        
        参数:
            config (dict): 特征工程配置
            
        返回:
            bool: 是否为标准结构
        """
        # 检查是否包含标准结构的特征模块
        standard_modules = ['price_features', 'volume', 'sma', 'ema', 'rsi', 'macd', 'bbands']
        
        # 如果配置中包含至少3个标准模块，则认为是标准结构
        count = sum(1 for module in standard_modules if module in config)
        return count >= 3
    
    def _convert_flat_to_standard(self, flat_config):
        """
        将扁平结构转换为标准结构
        
        参数:
            flat_config (dict): 扁平结构配置
            
        返回:
            dict: 标准结构配置
        """
        standard_config = {}
        
        # 处理价格特征
        if 'use_price' in flat_config:
            standard_config['price_features'] = {'use': flat_config.get('use_price', True)}
        
        # 处理成交量特征
        if 'use_volume' in flat_config:
            standard_config['volume'] = {
                'use': flat_config.get('use_volume', True),
                'periods': flat_config.get('volume_periods', [5, 10, 20])
            }
        
        # 处理SMA特征
        if 'sma_periods' in flat_config:
            standard_config['sma'] = {
                'use': True,
                'periods': flat_config.get('sma_periods', [5, 10, 20, 60])
            }
        
        # 处理EMA特征
        if 'ema_periods' in flat_config:
            standard_config['ema'] = {
                'use': True,
                'periods': flat_config.get('ema_periods', [5, 10, 20, 60])
            }
        
        # 处理RSI特征
        if 'rsi_periods' in flat_config:
            standard_config['rsi'] = {
                'use': True,
                'periods': flat_config.get('rsi_periods', [14])
            }
        
        # 处理MACD特征
        if 'macd_params' in flat_config:
            macd_params = flat_config.get('macd_params', {'fast': 12, 'slow': 26, 'signal': 9})
            standard_config['macd'] = {
                'use': True,
                'fast': macd_params.get('fast', 12),
                'slow': macd_params.get('slow', 26),
                'signal': macd_params.get('signal', 9)
            }
        
        # 处理布林带特征
        if 'bb_params' in flat_config:
            bb_params = flat_config.get('bb_params', {'window': 20, 'num_std': 2})
            standard_config['bbands'] = {
                'use': True,
                'period': bb_params.get('window', 20),
                'std': bb_params.get('num_std', 2.0)
            }
        
        # 处理ATR特征
        if 'atr_periods' in flat_config:
            standard_config['atr'] = {
                'use': True,
                'periods': flat_config.get('atr_periods', [14])
            }
        
        # 处理标准化方法
        if 'normalization' in flat_config:
            norm_value = flat_config.get('normalization')
            if isinstance(norm_value, str):
                standard_config['normalization'] = {
                    'use': True,
                    'method': norm_value
                }
            else:
                standard_config['normalization'] = norm_value
        
        # 处理技术指标总开关
        if 'use_technical' in flat_config:
            standard_config['technical_indicators'] = {
                'use': flat_config.get('use_technical', True)
            }
        
        return standard_config
    
    def get_feature_list(self, config):
        """
        获取配置中启用的特征列表
        
        参数:
            config (dict): 特征工程配置
            
        返回:
            list: 特征名称列表
        """
        # 确保配置是标准格式
        std_config = self.adapt_config(config)
        
        features = []
        
        # 价格特征
        if 'price_features' in std_config and std_config['price_features'].get('use', False):
            features.extend(['open', 'high', 'low', 'close'])
        
        # 成交量特征
        if 'volume' in std_config and std_config['volume'].get('use', False):
            features.append('volume')
            periods = std_config['volume'].get('periods', [])
            for period in periods:
                features.append(f'volume_ma_{period}')
        
        # SMA特征
        if 'sma' in std_config and std_config['sma'].get('use', False):
            periods = std_config['sma'].get('periods', [])
            for period in periods:
                features.append(f'sma_{period}')
        
        # EMA特征
        if 'ema' in std_config and std_config['ema'].get('use', False):
            periods = std_config['ema'].get('periods', [])
            for period in periods:
                features.append(f'ema_{period}')
        
        # RSI特征
        if 'rsi' in std_config and std_config['rsi'].get('use', False):
            periods = std_config['rsi'].get('periods', [])
            for period in periods:
                features.append(f'rsi_{period}')
        
        # MACD特征
        if 'macd' in std_config and std_config['macd'].get('use', False):
            features.extend(['macd', 'macd_signal', 'macd_hist'])
        
        # 布林带特征
        if 'bbands' in std_config and std_config['bbands'].get('use', False):
            features.extend(['bb_upper', 'bb_middle', 'bb_lower'])
        
        # ATR特征
        if 'atr' in std_config and std_config['atr'].get('use', False):
            periods = std_config['atr'].get('periods', [])
            for period in periods:
                features.append(f'atr_{period}')
        
        return features
