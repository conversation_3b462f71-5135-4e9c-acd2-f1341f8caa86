"""
自动因子挖掘模块 (导入文件)
此文件现在从重构后的模块中导入相应的类，以保持向后兼容性
"""

# 导入重构后的类
from quant_project.core_logic.factor_mining.factor_generator import FactorGenerator
from quant_project.core_logic.factor_mining.factor_evaluator import FactorEvaluator
from quant_project.core_logic.factor_mining.factor_selector import FactorSelector
from quant_project.core_logic.factor_mining.factor_pipeline import AutoFactorPipeline, AdaptiveFactorSystem

# 向后兼容导出
__all__ = [
    'FactorGenerator',
    'FactorEvaluator',
    'FactorSelector',
    'AutoFactorPipeline',
    'AdaptiveFactorSystem'
] 