#!/usr/bin/env python3
"""
项目清理分析脚本
识别重复、过时或未使用的文件和代码
"""

import os
import sys
import ast
import re
from pathlib import Path
from collections import defaultdict
import json

class ProjectCleanupAnalyzer:
    """项目清理分析器"""
    
    def __init__(self, project_root):
        self.project_root = Path(project_root)
        self.duplicate_files = []
        self.test_files = []
        self.unused_files = []
        self.redundant_modules = []
        self.cleanup_recommendations = []
        
    def analyze_duplicate_files(self):
        """分析重复文件"""
        print("=" * 60)
        print("分析重复文件")
        print("=" * 60)
        
        # 查找可能的重复文件模式
        duplicate_patterns = [
            # 数据处理相关
            ('data_handler.py', 'optimized_data_handler.py'),
            ('data_handling/data_handler.py', 'data_handler.py'),
            
            # 特征工程相关
            ('feature_engineer.py', 'enhanced_feature_engineer.py'),
            ('feature_engineering_adapter.py', 'optimized_feature_engineering_adapter.py'),
            
            # DRL智能体相关
            ('drl_agent.py', 'enhanced_drl_agent.py'),
            ('drl_agent.py', 'robust_drl_agent.py'),
            
            # 交易环境相关
            ('trading_environment.py', 'enhanced_trading_environment.py'),
            
            # 主应用文件
            ('main_app.py', 'fixed_main_app.py'),
            ('main_app.py', 'main_app_with_factor_mining.py'),
        ]
        
        for file1, file2 in duplicate_patterns:
            path1 = self.project_root / file1
            path2 = self.project_root / file2
            
            if path1.exists() and path2.exists():
                self.duplicate_files.append((str(path1), str(path2)))
                print(f"发现重复文件: {file1} <-> {file2}")
        
        return self.duplicate_files
    
    def analyze_test_files(self):
        """分析测试文件"""
        print("\n" + "=" * 60)
        print("分析测试文件")
        print("=" * 60)
        
        # 查找所有测试文件
        test_patterns = [
            'test_*.py',
            '*_test.py',
            'debug_*.py',
            'check_*.py',
            'verify_*.py'
        ]
        
        for pattern in test_patterns:
            for file_path in self.project_root.rglob(pattern):
                if file_path.is_file():
                    self.test_files.append(str(file_path))
                    print(f"测试文件: {file_path.relative_to(self.project_root)}")
        
        # 特殊的测试目录
        test_dirs = ['tests', 'test_env']
        for test_dir in test_dirs:
            test_path = self.project_root / test_dir
            if test_path.exists():
                print(f"测试目录: {test_dir}")
        
        return self.test_files
    
    def analyze_redundant_modules(self):
        """分析冗余模块"""
        print("\n" + "=" * 60)
        print("分析冗余模块")
        print("=" * 60)
        
        # 查找可能冗余的模块
        redundant_candidates = [
            # 优化版本 vs 原版本
            ('core_logic/optimized_data_handler.py', 'core_logic/data_handler.py'),
            ('core_logic/optimized_feature_engineering.py', 'core_logic/feature_engineer.py'),
            
            # 增强版本 vs 原版本
            ('core_logic/enhanced_drl_agent.py', 'core_logic/drl_agent.py'),
            ('core_logic/enhanced_feature_engineer.py', 'core_logic/feature_engineer.py'),
            ('core_logic/enhanced_trading_environment.py', 'core_logic/trading_environment.py'),
            
            # 适配器版本
            ('core_logic/feature_engineering_adapter.py', 'core_logic/feature_engineer.py'),
            ('core_logic/optimized_data_handler_adapter.py', 'core_logic/data_handler.py'),
        ]
        
        for enhanced_file, original_file in redundant_candidates:
            enhanced_path = self.project_root / enhanced_file
            original_path = self.project_root / original_file
            
            if enhanced_path.exists() and original_path.exists():
                self.redundant_modules.append((str(enhanced_path), str(original_path)))
                print(f"冗余模块: {enhanced_file} (可能替代 {original_file})")
        
        return self.redundant_modules
    
    def analyze_unused_files(self):
        """分析未使用的文件"""
        print("\n" + "=" * 60)
        print("分析可能未使用的文件")
        print("=" * 60)
        
        # 查找可能未使用的文件
        potentially_unused = [
            'api_test.py',
            'debug_app.py',
            'debug_main.py',
            'setup_test_env.py',
            'simple_run.py',
            'start_system.py',
            'run_project.py',
            'run_tests.py',
            'run_all_tests.py',
            'install_gpu_support.py',
            'fix_asyncio_torch.py',
        ]
        
        for filename in potentially_unused:
            file_path = self.project_root / filename
            if file_path.exists():
                self.unused_files.append(str(file_path))
                print(f"可能未使用: {filename}")
        
        return self.unused_files
    
    def generate_cleanup_recommendations(self):
        """生成清理建议"""
        print("\n" + "=" * 60)
        print("生成清理建议")
        print("=" * 60)
        
        recommendations = []
        
        # 1. 重复文件建议
        if self.duplicate_files:
            recommendations.append({
                'category': '重复文件清理',
                'action': 'remove_duplicates',
                'files': self.duplicate_files,
                'description': '删除重复的文件，保留最新或最完整的版本'
            })
        
        # 2. 测试文件建议
        if self.test_files:
            # 区分临时测试文件和永久测试文件
            temp_test_files = [f for f in self.test_files if any(pattern in f for pattern in [
                'test_data_acquisition_bug.py',
                'test_backtesting_bug.py', 
                'test_comprehensive_fixes.py',
                'debug_', 'check_', 'verify_'
            ])]
            
            if temp_test_files:
                recommendations.append({
                    'category': '临时测试文件清理',
                    'action': 'remove_temp_tests',
                    'files': temp_test_files,
                    'description': '删除调试过程中创建的临时测试文件'
                })
        
        # 3. 冗余模块建议
        if self.redundant_modules:
            recommendations.append({
                'category': '冗余模块整合',
                'action': 'consolidate_modules',
                'files': self.redundant_modules,
                'description': '保留最优版本，删除冗余模块'
            })
        
        # 4. 未使用文件建议
        if self.unused_files:
            recommendations.append({
                'category': '未使用文件清理',
                'action': 'remove_unused',
                'files': self.unused_files,
                'description': '删除不再使用的脚本和工具文件'
            })
        
        # 5. 缓存和日志清理
        cache_dirs = ['data_cache', 'logs', '__pycache__']
        cache_files = []
        for cache_dir in cache_dirs:
            cache_path = self.project_root / cache_dir
            if cache_path.exists():
                cache_files.append(str(cache_path))
        
        if cache_files:
            recommendations.append({
                'category': '缓存和日志清理',
                'action': 'clean_cache',
                'files': cache_files,
                'description': '清理缓存文件和旧日志（保留最近的）'
            })
        
        self.cleanup_recommendations = recommendations
        
        # 打印建议
        for i, rec in enumerate(recommendations, 1):
            print(f"\n{i}. {rec['category']}")
            print(f"   动作: {rec['action']}")
            print(f"   描述: {rec['description']}")
            print(f"   涉及文件数: {len(rec['files'])}")
        
        return recommendations
    
    def save_analysis_report(self):
        """保存分析报告"""
        report = {
            'timestamp': str(Path(__file__).stat().st_mtime),
            'project_root': str(self.project_root),
            'analysis_results': {
                'duplicate_files': self.duplicate_files,
                'test_files': self.test_files,
                'redundant_modules': self.redundant_modules,
                'unused_files': self.unused_files
            },
            'cleanup_recommendations': self.cleanup_recommendations
        }
        
        report_file = self.project_root / 'cleanup_analysis_report.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"\n分析报告已保存到: {report_file}")
        return report_file

def main():
    """主函数"""
    project_root = Path(__file__).parent
    analyzer = ProjectCleanupAnalyzer(project_root)
    
    print("开始项目清理分析...")
    print(f"项目根目录: {project_root}")
    
    # 执行分析
    analyzer.analyze_duplicate_files()
    analyzer.analyze_test_files()
    analyzer.analyze_redundant_modules()
    analyzer.analyze_unused_files()
    analyzer.generate_cleanup_recommendations()
    
    # 保存报告
    report_file = analyzer.save_analysis_report()
    
    print("\n" + "=" * 60)
    print("分析完成")
    print("=" * 60)
    
    return analyzer

if __name__ == "__main__":
    analyzer = main()
