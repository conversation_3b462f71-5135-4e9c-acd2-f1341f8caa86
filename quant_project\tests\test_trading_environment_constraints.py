"""
交易环境约束测试脚本
用于测试交易环境的核心约束：收盘价成交、最小持仓3天、无杠杆
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from gymnasium.utils.env_checker import check_env

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core_logic.trading_environment import TradingEnvironment
from core_logic.utils import setup_logger

# 设置日志
logger = setup_logger(log_file='logs/test_env_constraints.log')

def create_test_data():
    """创建测试数据"""
    logger.info("创建测试数据")
    
    # 创建日期范围
    dates = pd.date_range(start='2023-01-01', end='2023-01-31', freq='D')
    
    # 创建模拟的行情数据
    data = pd.DataFrame({
        '开盘': np.random.rand(len(dates)) * 100 + 3000,
        '最高': np.random.rand(len(dates)) * 100 + 3050,
        '最低': np.random.rand(len(dates)) * 100 + 2950,
        '收盘': np.random.rand(len(dates)) * 100 + 3000,
        '成交量': np.random.rand(len(dates)) * 1000000,
        'SMA_5': np.random.rand(len(dates)) * 100 + 3000,
        'SMA_20': np.random.rand(len(dates)) * 100 + 3000,
        'RSI_14': np.random.rand(len(dates)) * 100
    }, index=dates)
    
    return data

def test_env_api_compatibility():
    """测试环境API兼容性"""
    logger.info("测试环境API兼容性")
    
    # 创建测试数据
    data = create_test_data()
    
    # 创建交易环境
    env = TradingEnvironment(
        df_processed_data=data,
        initial_capital=100000,
        commission_rate=0.0003,
        min_hold_days=3,
        window_size=5
    )
    
    try:
        # 使用gymnasium的环境检查器验证API兼容性
        check_env(env)
        logger.info("环境API兼容性检查通过")
        return True
    except Exception as e:
        logger.error(f"环境API兼容性检查失败: {str(e)}")
        return False

def test_min_hold_days_constraint():
    """测试最小持仓天数约束"""
    logger.info("测试最小持仓天数约束")
    
    # 创建测试数据
    data = create_test_data()
    
    # 创建交易环境
    env = TradingEnvironment(
        df_processed_data=data,
        initial_capital=100000,
        commission_rate=0.0003,
        min_hold_days=3,
        window_size=5
    )
    
    # 重置环境
    observation, info = env.reset()
    
    # 记录测试结果
    results = []
    
    # 测试场景：买入后立即尝试卖出
    logger.info("测试场景：买入后立即尝试卖出")
    
    # 步骤1：买入
    action = 1  # 假设1表示买入
    observation, reward, terminated, truncated, info = env.step(action)
    
    initial_shares = info['shares_held']
    initial_cash = info['cash']
    
    results.append({
        'step': 1,
        'action': action,
        'shares_held': info['shares_held'],
        'cash': info['cash'],
        'holding_days': info['holding_days'],
        'portfolio_value': info['portfolio_value']
    })
    
    logger.info(f"步骤1 - 买入: 持仓={info['shares_held']}, 现金={info['cash']}, 持仓天数={info['holding_days']}")
    
    # 步骤2：尝试卖出（应该被阻止）
    action = 2  # 假设2表示卖出
    observation, reward, terminated, truncated, info = env.step(action)
    
    results.append({
        'step': 2,
        'action': action,
        'shares_held': info['shares_held'],
        'cash': info['cash'],
        'holding_days': info['holding_days'],
        'portfolio_value': info['portfolio_value']
    })
    
    logger.info(f"步骤2 - 尝试卖出: 持仓={info['shares_held']}, 现金={info['cash']}, 持仓天数={info['holding_days']}")
    
    # 验证持仓没有变化（卖出被阻止）
    sell_blocked = info['shares_held'] == initial_shares and info['cash'] == initial_cash
    
    if sell_blocked:
        logger.info("最小持仓天数约束测试通过：卖出被正确阻止")
    else:
        logger.error("最小持仓天数约束测试失败：卖出未被阻止")
    
    # 步骤3-5：持有
    for i in range(3, 6):
        action = 0  # 假设0表示持有
        observation, reward, terminated, truncated, info = env.step(action)
        
        results.append({
            'step': i,
            'action': action,
            'shares_held': info['shares_held'],
            'cash': info['cash'],
            'holding_days': info['holding_days'],
            'portfolio_value': info['portfolio_value']
        })
        
        logger.info(f"步骤{i} - 持有: 持仓={info['shares_held']}, 现金={info['cash']}, 持仓天数={info['holding_days']}")
    
    # 步骤6：再次尝试卖出（应该允许）
    action = 2  # 假设2表示卖出
    observation, reward, terminated, truncated, info = env.step(action)
    
    results.append({
        'step': 6,
        'action': action,
        'shares_held': info['shares_held'],
        'cash': info['cash'],
        'holding_days': info['holding_days'],
        'portfolio_value': info['portfolio_value']
    })
    
    logger.info(f"步骤6 - 再次尝试卖出: 持仓={info['shares_held']}, 现金={info['cash']}, 持仓天数={info['holding_days']}")
    
    # 验证持仓已变化（卖出被允许）
    sell_allowed = info['shares_held'] < initial_shares and info['cash'] > initial_cash
    
    if sell_allowed:
        logger.info("最小持仓天数约束测试通过：满足最小持仓天数后卖出被允许")
    else:
        logger.error("最小持仓天数约束测试失败：满足最小持仓天数后卖出仍被阻止")
    
    # 保存测试结果
    results_df = pd.DataFrame(results)
    results_df.to_csv('tests/min_hold_days_constraint_results.csv', index=False)
    
    return sell_blocked and sell_allowed

def test_no_leverage_constraint():
    """测试无杠杆约束"""
    logger.info("测试无杠杆约束")
    
    # 创建测试数据
    data = create_test_data()
    
    # 创建交易环境，设置较小的初始资金以便测试
    env = TradingEnvironment(
        df_processed_data=data,
        initial_capital=5000,  # 较小的初始资金
        commission_rate=0.0003,
        min_hold_days=3,
        window_size=5
    )
    
    # 重置环境
    observation, info = env.reset()
    
    # 记录测试结果
    results = []
    
    # 获取当前价格
    current_price = info['current_price']
    initial_cash = info['cash']
    
    # 计算最大可买数量
    max_shares = int(initial_cash / current_price)
    
    logger.info(f"初始现金={initial_cash}, 当前价格={current_price}, 最大可买数量={max_shares}")
    
    # 步骤1：尝试买入超过最大可买数量的股票
    action = 1  # 假设1表示买入
    observation, reward, terminated, truncated, info = env.step(action)
    
    results.append({
        'step': 1,
        'action': action,
        'shares_held': info['shares_held'],
        'cash': info['cash'],
        'portfolio_value': info['portfolio_value']
    })
    
    logger.info(f"步骤1 - 尝试买入: 持仓={info['shares_held']}, 现金={info['cash']}")
    
    # 验证是否使用了全部或接近全部的现金（无杠杆）
    no_leverage = info['cash'] >= 0 and info['cash'] < current_price
    
    if no_leverage:
        logger.info("无杠杆约束测试通过：买入操作使用了可用现金但未使用杠杆")
    else:
        logger.error("无杠杆约束测试失败：买入操作可能使用了杠杆或未使用全部可用现金")
    
    # 保存测试结果
    results_df = pd.DataFrame(results)
    results_df.to_csv('tests/no_leverage_constraint_results.csv', index=False)
    
    return no_leverage

def test_closing_price_execution():
    """测试收盘价成交约束"""
    logger.info("测试收盘价成交约束")
    
    # 创建测试数据
    data = create_test_data()
    
    # 创建交易环境
    env = TradingEnvironment(
        df_processed_data=data,
        initial_capital=100000,
        commission_rate=0.0003,
        min_hold_days=3,
        window_size=5
    )
    
    # 重置环境
    observation, info = env.reset()
    
    # 记录测试结果
    results = []
    
    # 获取当前收盘价
    current_price = info['current_price']
    initial_cash = info['cash']
    
    logger.info(f"初始现金={initial_cash}, 当前收盘价={current_price}")
    
    # 步骤1：买入
    action = 1  # 假设1表示买入
    observation, reward, terminated, truncated, info = env.step(action)
    
    # 计算预期的现金变化和持仓变化
    expected_shares = int(initial_cash / current_price)
    expected_cash = initial_cash - expected_shares * current_price * (1 + env.commission_rate)
    
    results.append({
        'step': 1,
        'action': action,
        'shares_held': info['shares_held'],
        'cash': info['cash'],
        'expected_shares': expected_shares,
        'expected_cash': expected_cash,
        'current_price': current_price
    })
    
    logger.info(f"步骤1 - 买入: 持仓={info['shares_held']}, 现金={info['cash']}, 预期持仓={expected_shares}, 预期现金={expected_cash}")
    
    # 验证是否按收盘价成交
    closing_price_execution = np.isclose(info['shares_held'], expected_shares, rtol=1e-5) and np.isclose(info['cash'], expected_cash, rtol=1e-5)
    
    if closing_price_execution:
        logger.info("收盘价成交约束测试通过：交易按收盘价执行")
    else:
        logger.error("收盘价成交约束测试失败：交易可能未按收盘价执行")
    
    # 保存测试结果
    results_df = pd.DataFrame(results)
    results_df.to_csv('tests/closing_price_execution_results.csv', index=False)
    
    return closing_price_execution

def run_all_tests():
    """运行所有测试"""
    logger.info("开始运行所有交易环境约束测试")
    
    # 确保测试目录存在
    os.makedirs('tests', exist_ok=True)
    
    # 运行各项测试
    api_compatibility = test_env_api_compatibility()
    min_hold_days = test_min_hold_days_constraint()
    no_leverage = test_no_leverage_constraint()
    closing_price = test_closing_price_execution()
    
    # 汇总测试结果
    results = {
        'API兼容性': api_compatibility,
        '最小持仓天数约束': min_hold_days,
        '无杠杆约束': no_leverage,
        '收盘价成交约束': closing_price
    }
    
    all_passed = all(results.values())
    
    logger.info(f"所有测试完成，测试结果: {'全部通过' if all_passed else '部分失败'}")
    logger.info(f"测试结果详情: {results}")
    
    # 保存测试结果
    results_df = pd.DataFrame([results])
    results_df.to_csv('tests/trading_environment_constraints_results.csv', index=False)
    
    return all_passed

if __name__ == "__main__":
    run_all_tests()
