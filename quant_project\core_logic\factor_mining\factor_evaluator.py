"""
因子评估器模块
负责评估因子的预测能力和稳定性
"""

import logging
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.metrics import r2_score, mean_squared_error
from datetime import datetime, timedelta
import time

class FactorEvaluator:
    """因子评估器，评估因子的预测能力和稳定性"""
    
    def __init__(self, price_data):
        """
        初始化因子评估器
        
        参数:
            price_data (pandas.DataFrame): 价格数据，必须有close列
        """
        self.price_data = price_data
        self.logger = logging.getLogger('drl_trading')
        
    def calculate_ic(self, factor, forward_returns, method='pearson'):
        """
        计算因子与未来收益的信息系数(IC)
        
        参数:
            factor (pd.Series): 因子值
            forward_returns (pd.Series): 未来收益率
            method (str): 相关系数方法，'pearson'或'spearman'
            
        返回:
            float: 信息系数
        """
        # 删除任一序列中的NaN
        valid_data = pd.concat([factor, forward_returns], axis=1).dropna()
        
        if len(valid_data) <= 5:
            return 0.0
            
        if method == 'pearson':
            ic = valid_data.iloc[:, 0].corr(valid_data.iloc[:, 1], method='pearson')
        else:
            # 使用Spearman等级相关系数
            ic = valid_data.iloc[:, 0].corr(valid_data.iloc[:, 1], method='spearman')
        
        return ic if not np.isnan(ic) else 0.0
    
    def calculate_rank_ic(self, factor, forward_returns):
        """
        计算因子与未来收益的排名信息系数(Rank IC)
        
        参数:
            factor (pd.Series): 因子值
            forward_returns (pd.Series): 未来收益率
            
        返回:
            float: 排名信息系数
        """
        # 删除任一序列中的NaN
        valid_data = pd.concat([factor, forward_returns], axis=1).dropna()
        
        if len(valid_data) <= 5:
            return 0.0
            
        # 计算排名
        ranks = valid_data.rank()
        
        # 计算排名相关系数
        rank_ic = ranks.iloc[:, 0].corr(ranks.iloc[:, 1], method='pearson')
        
        return rank_ic if not np.isnan(rank_ic) else 0.0
    
    def calculate_quantile_returns(self, factor, forward_returns, n_quantiles=5):
        """
        计算因子分位数收益
        
        参数:
            factor (pd.Series): 因子值
            forward_returns (pd.Series): 未来收益率
            n_quantiles (int): 分位数数量
            
        返回:
            dict: 各分位数的平均收益率
        """
        # 删除任一序列中的NaN
        valid_data = pd.concat([factor, forward_returns], axis=1).dropna()
        valid_data.columns = ['factor', 'return']
        
        if len(valid_data) <= n_quantiles * 2:
            return {}
            
        # 按因子值分组
        valid_data['quantile'] = pd.qcut(valid_data['factor'], q=n_quantiles, labels=False)
        
        # 计算各组平均收益
        quantile_returns = valid_data.groupby('quantile')['return'].mean()
        
        return {f"Q{int(q+1)}": ret for q, ret in quantile_returns.items()}
    
    def calculate_ic_series(self, factor, periods=[1, 3, 5, 10, 20]):
        """
        计算因子在不同预测周期的IC值
        
        参数:
            factor (pd.Series): 因子值
            periods (list): 预测周期列表，单位为天
            
        返回:
            dict: 各预测周期的IC值
        """
        self.logger.info(f"计算因子 IC 序列...")
        
        # 确保价格数据索引与因子索引匹配
        if not isinstance(self.price_data.index, pd.DatetimeIndex):
            self.logger.warning("价格数据索引不是日期类型，尝试转换")
            self.price_data.index = pd.to_datetime(self.price_data.index)
        
        if not isinstance(factor.index, pd.DatetimeIndex):
            self.logger.warning("因子索引不是日期类型，尝试转换")
            factor.index = pd.to_datetime(factor.index)
        
        # 合并价格数据和因子数据
        close = self.price_data['close'] if 'close' in self.price_data.columns else None
        
        if close is None:
            # 尝试查找列名包含"收盘"或"close"的列
            close_columns = [col for col in self.price_data.columns if '收盘' in col.lower() or 'close' in col.lower()]
            if close_columns:
                close = self.price_data[close_columns[0]]
            else:
                self.logger.error("价格数据中找不到收盘价列")
                return {}
        
        # 重新采样到共同的日期索引
        common_index = sorted(set(factor.index).intersection(set(close.index)))
        if not common_index:
            self.logger.warning("因子和价格数据没有共同的索引")
            return {}
            
        factor = factor.loc[common_index]
        close = close.loc[common_index]
        
        # 计算不同期限的未来收益率
        ic_results = {}
        
        for period in periods:
            # 计算未来period天的收益率
            forward_returns = close.pct_change(period).shift(-period)
            
            # 计算IC值
            ic = self.calculate_ic(factor, forward_returns, method='pearson')
            rank_ic = self.calculate_rank_ic(factor, forward_returns)
            
            ic_results[period] = {
                'ic': ic,
                'rank_ic': rank_ic
            }
            
        return ic_results
    
    def calculate_return_prediction_score(self, factor, forward_returns):
        """
        计算因子对未来收益的预测分数
        
        参数:
            factor (pd.Series): 因子值
            forward_returns (pd.Series): 未来收益率
            
        返回:
            dict: 预测分数，包括R²和MSE
        """
        # 删除任一序列中的NaN
        valid_data = pd.concat([factor, forward_returns], axis=1).dropna()
        valid_data.columns = ['factor', 'return']
        
        if len(valid_data) <= 10:
            return {'r2': 0.0, 'mse': float('inf'), 'prediction_power': 0.0}
            
        # 标准化因子值和收益
        factor_norm = (valid_data['factor'] - valid_data['factor'].mean()) / valid_data['factor'].std()
        returns_norm = (valid_data['return'] - valid_data['return'].mean()) / valid_data['return'].std()
        
        # 计算预测分数
        r2 = r2_score(returns_norm, factor_norm)
        mse = mean_squared_error(returns_norm, factor_norm)
        
        # 计算预测能力得分 (0-100)
        prediction_power = max(0, min(100, (r2 * 50 + (1 / (1 + mse)) * 50)))
        
        return {
            'r2': r2,
            'mse': mse,
            'prediction_power': prediction_power
        }
    
    def calculate_factor_stability(self, factor, window_size=0.1):
        """
        计算因子稳定性
        
        参数:
            factor (pd.Series): 因子值
            window_size (float): 窗口大小，占总样本比例
            
        返回:
            dict: 稳定性指标
        """
        # 删除因子中的NaN
        factor = factor.dropna()
        
        if len(factor) <= 10:
            return {'stability_score': 0.0, 'stability_corr': 0.0, 'stability_direction': 0.0}
            
        # 计算窗口样本数
        window = max(5, int(len(factor) * window_size))
        
        # 将因子切分为多个窗口
        n_windows = len(factor) // window
        
        if n_windows <= 1:
            return {'stability_score': 0.0, 'stability_corr': 0.0, 'stability_direction': 0.0}
            
        windows = []
        for i in range(n_windows):
            start = i * window
            end = (i + 1) * window
            windows.append(factor.iloc[start:end])
        
        # 计算窗口间的相关性
        window_corrs = []
        window_directions = []
        
        for i in range(n_windows-1):
            # 阶层相关系数
            corr = stats.spearmanr(windows[i], windows[i+1])[0]
            window_corrs.append(corr if not np.isnan(corr) else 0)
            
            # 方向一致性
            direction_consistency = np.mean((np.sign(np.diff(windows[i])) == np.sign(np.diff(windows[i+1]))).astype(float))
            window_directions.append(direction_consistency if not np.isnan(direction_consistency) else 0.5)
        
        # 计算平均稳定性
        stability_corr = np.mean(window_corrs)
        stability_direction = np.mean(window_directions)
        
        # 计算总体稳定性得分 (0-100)
        stability_score = (stability_corr * 0.7 + stability_direction * 0.3) * 100
        stability_score = max(0, min(100, stability_score))
        
        return {
            'stability_score': stability_score,
            'stability_corr': stability_corr,
            'stability_direction': stability_direction
        }
    
    def evaluate_factors(self, factors, min_ic_abs=0.05):
        """
        评估一组因子的预测能力
        
        参数:
            factors (dict): 因子字典，键为因子名称，值为因子值
            min_ic_abs (float): 最小IC绝对值，低于此值的因子被过滤
            
        返回:
            dict: 评估结果
        """
        self.logger.info(f"开始评估 {len(factors)} 个因子...")
        
        # 获取收盘价
        close = self.price_data['close'] if 'close' in self.price_data.columns else None
        
        if close is None:
            # 尝试查找列名包含"收盘"或"close"的列
            close_columns = [col for col in self.price_data.columns if '收盘' in col.lower() or 'close' in col.lower()]
            if close_columns:
                close = self.price_data[close_columns[0]]
            else:
                self.logger.error("价格数据中找不到收盘价列")
                return {}
        
        # 计算多个周期的未来收益
        future_returns = {}
        for period in [1, 3, 5, 10, 20]:
            future_returns[period] = close.pct_change(period).shift(-period)
        
        # 评估因子
        evaluation_results = {}
        skipped_count = 0
        
        for factor_name, factor_value in factors.items():
            try:
                # 确保因子不全是NaN
                if factor_value.isnull().all():
                    skipped_count += 1
                    continue
                
                # 处理因子中的NaN和无穷大
                factor_value = factor_value.replace([np.inf, -np.inf], np.nan).dropna()
                
                if len(factor_value) == 0 or len(factor_value) < 20:
                    skipped_count += 1
                    continue
                
                # 对齐因子和价格数据的索引
                common_index = sorted(set(factor_value.index).intersection(set(close.index)))
                
                if len(common_index) < 20:
                    skipped_count += 1
                    continue
                    
                factor_value = factor_value.loc[common_index]
                
                # 多周期IC评估
                ic_results = {}
                max_abs_ic = 0.0
                best_period = 1
                sum_ic = 0.0
                count_significant = 0
                
                for period, returns in future_returns.items():
                    returns = returns.loc[common_index]
                    
                    ic = self.calculate_ic(factor_value, returns)
                    rank_ic = self.calculate_rank_ic(factor_value, returns)
                    
                    ic_results[period] = {
                        'ic': ic,
                        'rank_ic': rank_ic
                    }
                    
                    # 累积IC绝对值的信息
                    sum_ic += abs(ic)
                    if abs(ic) >= min_ic_abs:
                        count_significant += 1
                    
                    # 记录最大IC绝对值
                    if abs(ic) > max_abs_ic:
                        max_abs_ic = abs(ic)
                        best_period = period
                
                # 如果没有显著IC，跳过此因子
                if count_significant == 0:
                    skipped_count += 1
                    continue
                
                # 使用最佳周期计算更多指标
                best_returns = future_returns[best_period].loc[common_index]
                
                # 计算分位数收益
                quantile_returns = self.calculate_quantile_returns(factor_value, best_returns)
                
                # 计算预测分数
                prediction_scores = self.calculate_return_prediction_score(factor_value, best_returns)
                
                # 计算因子稳定性
                stability_metrics = self.calculate_factor_stability(factor_value)
                
                # 计算综合得分
                avg_abs_ic = sum_ic / len(future_returns)
                significant_ratio = count_significant / len(future_returns)
                
                # 最佳IC的方向（正/负）
                best_ic_sign = np.sign(ic_results[best_period]['ic'])
                
                # 首尾分位收益差异
                q_spread = 0.0
                if 'Q5' in quantile_returns and 'Q1' in quantile_returns:
                    q_spread = quantile_returns['Q5'] - quantile_returns['Q1']
                
                # 计算综合得分 (0-100)，权重可根据需要调整
                composite_score = (
                    max_abs_ic * 25 +                     # 最大IC绝对值
                    avg_abs_ic * 10 +                     # 平均IC绝对值
                    significant_ratio * 15 +              # 显著IC比例
                    stability_metrics['stability_score'] * 0.25 +  # 稳定性
                    prediction_scores['prediction_power'] * 0.25   # 预测能力
                )
                
                # 将综合得分限制在0-100范围内
                composite_score = max(0, min(100, composite_score))
                
                # 记录评估结果
                evaluation_results[factor_name] = {
                    'ic_results': ic_results,
                    'best_period': best_period,
                    'best_ic': ic_results[best_period]['ic'],
                    'max_abs_ic': max_abs_ic,
                    'avg_abs_ic': avg_abs_ic,
                    'significant_ratio': significant_ratio,
                    'best_ic_sign': best_ic_sign,
                    'quantile_returns': quantile_returns,
                    'q_spread': q_spread,
                    'prediction_scores': prediction_scores,
                    'stability_metrics': stability_metrics,
                    'composite_score': composite_score
                }
                
                # 进度日志
                if len(evaluation_results) % 10 == 0:
                    self.logger.info(f"已评估 {len(evaluation_results)} 个因子...")
                
            except Exception as e:
                self.logger.warning(f"评估因子 '{factor_name}' 时出错: {str(e)}")
                skipped_count += 1
        
        self.logger.info(f"因子评估完成。评估了 {len(evaluation_results)} 个因子，跳过了 {skipped_count} 个因子。")
        
        return evaluation_results
    
    def rank_factors(self, evaluation_results, metric='composite_score'):
        """
        根据指定指标对因子进行排序
        
        参数:
            evaluation_results (dict): 评估结果
            metric (str): 排序指标，默认为综合得分
            
        返回:
            dict: 排序后的因子列表
        """
        if not evaluation_results:
            return {}
            
        # 根据指定指标对因子进行排序
        if metric == 'composite_score':
            sorted_factors = sorted(evaluation_results.items(), 
                                 key=lambda x: x[1]['composite_score'], 
                                 reverse=True)
        elif metric == 'max_abs_ic':
            sorted_factors = sorted(evaluation_results.items(), 
                                 key=lambda x: x[1]['max_abs_ic'], 
                                 reverse=True)
        elif metric == 'avg_abs_ic':
            sorted_factors = sorted(evaluation_results.items(), 
                                 key=lambda x: x[1]['avg_abs_ic'], 
                                 reverse=True)
        else:
            self.logger.warning(f"不支持的排序指标: {metric}，使用默认指标")
            sorted_factors = sorted(evaluation_results.items(), 
                                 key=lambda x: x[1]['composite_score'], 
                                 reverse=True)
        
        return dict(sorted_factors)
    
    def plot_factor_evaluation(self, evaluation_results, top_n=20, metric='composite_score'):
        """
        绘制因子评估结果
        
        参数:
            evaluation_results (dict): 评估结果
            top_n (int): 显示前多少个因子
            metric (str): 排序指标
            
        返回:
            matplotlib.figure.Figure: 图形对象
        """
        if not evaluation_results:
            self.logger.warning("没有评估结果可供绘图")
            return None
            
        # 根据指标排序因子
        ranked_factors = self.rank_factors(evaluation_results, metric)
        
        # 选择前top_n个因子
        top_factors = list(ranked_factors.items())[:top_n]
        
        # 创建图形
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle(f'Top {top_n} Factors by {metric}', fontsize=16)
        
        # 绘制综合得分
        ax = axes[0, 0]
        factor_names = [f"{i+1}. {name}" for i, (name, _) in enumerate(top_factors)]
        scores = [data['composite_score'] for _, data in top_factors]
        
        ax.barh(factor_names, scores, color='skyblue')
        ax.set_xlabel('Composite Score')
        ax.set_title('Factor Composite Scores')
        ax.grid(axis='x', linestyle='--', alpha=0.7)
        
        # 绘制IC值
        ax = axes[0, 1]
        ic_values = [data['best_ic'] for _, data in top_factors]
        colors = ['green' if ic > 0 else 'red' for ic in ic_values]
        
        ax.barh(factor_names, ic_values, color=colors)
        ax.set_xlabel('Information Coefficient (IC)')
        ax.set_title('Best Period IC Values')
        ax.grid(axis='x', linestyle='--', alpha=0.7)
        
        # 绘制因子周期
        ax = axes[1, 0]
        periods = [data['best_period'] for _, data in top_factors]
        
        ax.barh(factor_names, periods, color='orange')
        ax.set_xlabel('Best Prediction Period (Days)')
        ax.set_title('Optimal Prediction Horizons')
        ax.grid(axis='x', linestyle='--', alpha=0.7)
        
        # 绘制稳定性得分
        ax = axes[1, 1]
        stability_scores = [data['stability_metrics']['stability_score'] for _, data in top_factors]
        
        ax.barh(factor_names, stability_scores, color='purple')
        ax.set_xlabel('Stability Score')
        ax.set_title('Factor Stability')
        ax.grid(axis='x', linestyle='--', alpha=0.7)
        
        # 调整布局
        plt.tight_layout()
        plt.subplots_adjust(top=0.92)
        
        return fig 