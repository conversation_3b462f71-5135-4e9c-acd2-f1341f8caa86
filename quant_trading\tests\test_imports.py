#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
导入测试
测试所有模块的导入是否正常
"""

import os
import sys
import unittest
import importlib
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join('logs', 'test_imports.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('test_imports')

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if project_root not in sys.path:
    sys.path.append(project_root)

class TestImports(unittest.TestCase):
    """测试导入"""

    def test_root_imports(self):
        """测试根模块导入"""
        import quant_trading
        self.assertIsNotNone(quant_trading)
        self.assertTrue(hasattr(quant_trading, '__version__'))

    def test_data_imports(self):
        """测试数据模块导入"""
        from quant_trading.data import (
            DataHandler, DataFetcher, DataCleaner, DataValidator,
            DataCache, EnhancedDataValidator, OptimizedDataHandler,
            OptimizedDataHandlerAdapter
        )
        self.assertIsNotNone(DataHandler)
        self.assertIsNotNone(DataFetcher)
        self.assertIsNotNone(DataCleaner)
        self.assertIsNotNone(DataValidator)
        self.assertIsNotNone(DataCache)
        self.assertIsNotNone(EnhancedDataValidator)
        self.assertIsNotNone(OptimizedDataHandler)
        self.assertIsNotNone(OptimizedDataHandlerAdapter)

    def test_features_imports(self):
        """测试特征模块导入"""
        from quant_trading.features import (
            FeatureEngineer, EnhancedFeatureEngineer, PriceFeatures,
            TechnicalIndicators, StatisticalFeatures, TimeFeatures,
            OptimizedFeatureEngineering, OptimizedFeatureEngineeringAdapter
        )
        self.assertIsNotNone(FeatureEngineer)
        self.assertIsNotNone(EnhancedFeatureEngineer)
        self.assertIsNotNone(PriceFeatures)
        self.assertIsNotNone(TechnicalIndicators)
        self.assertIsNotNone(StatisticalFeatures)
        self.assertIsNotNone(TimeFeatures)
        self.assertIsNotNone(OptimizedFeatureEngineering)
        self.assertIsNotNone(OptimizedFeatureEngineeringAdapter)

    def test_trading_imports(self):
        """测试交易模块导入"""
        from quant_trading.trading import (
            TradingEnvironment, EnhancedTradingEnvironment, RobustTradingEnvironment,
            Action, Observation, Reward, RenderMode, Renderer
        )
        self.assertIsNotNone(TradingEnvironment)
        self.assertIsNotNone(EnhancedTradingEnvironment)
        self.assertIsNotNone(RobustTradingEnvironment)
        self.assertIsNotNone(Action)
        self.assertIsNotNone(Observation)
        self.assertIsNotNone(Reward)
        self.assertIsNotNone(RenderMode)
        self.assertIsNotNone(Renderer)

    def test_agents_imports(self):
        """测试智能体模块导入"""
        from quant_trading.agents import (
            DRLAgent, RobustDRLAgent, EnhancedDRLAgent, EnsembleLearning
        )
        self.assertIsNotNone(DRLAgent)
        self.assertIsNotNone(RobustDRLAgent)
        self.assertIsNotNone(EnhancedDRLAgent)
        self.assertIsNotNone(EnsembleLearning)

    def test_evaluation_imports(self):
        """测试评估模块导入"""
        from quant_trading.evaluation import (
            PerformanceAnalyzer, EnhancedPerformanceAnalyzer, ModelEvaluator
        )
        self.assertIsNotNone(PerformanceAnalyzer)
        self.assertIsNotNone(EnhancedPerformanceAnalyzer)
        self.assertIsNotNone(ModelEvaluator)

    def test_risk_imports(self):
        """测试风险模块导入"""
        from quant_trading.risk import (
            RiskManager, StopLossType, PositionSizingMethod
        )
        self.assertIsNotNone(RiskManager)
        self.assertIsNotNone(StopLossType)
        self.assertIsNotNone(PositionSizingMethod)

    def test_validation_imports(self):
        """测试验证模块导入"""
        from quant_trading.validation import (
            TimeSeriesCV, CVMethod, MarketConditionCV, OverfittingDetector
        )
        self.assertIsNotNone(TimeSeriesCV)
        self.assertIsNotNone(CVMethod)
        self.assertIsNotNone(MarketConditionCV)
        self.assertIsNotNone(OverfittingDetector)

    def test_market_imports(self):
        """测试市场模块导入"""
        from quant_trading.market import (
            MarketConditionDetector, MarketCondition
        )
        self.assertIsNotNone(MarketConditionDetector)
        self.assertIsNotNone(MarketCondition)

    def test_utils_imports(self):
        """测试工具模块导入"""
        from quant_trading.utils import (
            setup_logger, is_gpu_available, install_gpu_support
        )
        self.assertIsNotNone(setup_logger)
        self.assertIsNotNone(is_gpu_available)
        self.assertIsNotNone(install_gpu_support)

    def test_cross_module_imports(self):
        """测试跨模块导入"""
        # 测试数据模块导入特征模块
        from quant_trading.data import DataHandler
        from quant_trading.features import FeatureEngineer

        # 测试特征模块导入交易模块
        from quant_trading.features import FeatureEngineer
        from quant_trading.trading import TradingEnvironment

        # 测试交易模块导入智能体模块
        from quant_trading.trading import TradingEnvironment
        from quant_trading.agents import DRLAgent

        # 测试智能体模块导入评估模块
        from quant_trading.agents import DRLAgent
        from quant_trading.evaluation import PerformanceAnalyzer

        # 测试评估模块导入风险模块
        from quant_trading.evaluation import PerformanceAnalyzer
        from quant_trading.risk import RiskManager

        # 测试风险模块导入验证模块
        from quant_trading.risk import RiskManager
        from quant_trading.validation import TimeSeriesCV

        # 测试验证模块导入市场模块
        from quant_trading.validation import MarketConditionCV
        from quant_trading.market import MarketConditionDetector

        # 测试市场模块导入工具模块
        from quant_trading.market import MarketConditionDetector
        from quant_trading.utils import setup_logger

        # 测试工具模块导入数据模块
        from quant_trading.utils import setup_logger
        from quant_trading.data import DataHandler

        # 所有导入都成功，测试通过
        self.assertTrue(True)

if __name__ == '__main__':
    unittest.main()
