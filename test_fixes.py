#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试修复脚本
用于测试对特征工程配置格式、性能分析器参数处理和GPU检测功能的修复
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import traceback
from pathlib import Path

# 设置日志
os.makedirs('logs', exist_ok=True)
log_file = os.path.join('logs', f'test_fixes_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('test_fixes')

# 添加项目根目录到系统路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_root)

# 添加quant_project目录到系统路径
quant_project_path = os.path.join(project_root, 'quant_project')
if os.path.exists(quant_project_path):
    sys.path.append(quant_project_path)

# 添加quant_trading目录到系统路径
quant_trading_path = os.path.join(project_root, 'quant_trading')
if os.path.exists(quant_trading_path):
    sys.path.append(quant_trading_path)

def test_gpu_detection():
    """测试GPU检测功能"""
    logger.info("测试GPU检测功能")
    
    try:
        # 导入GPU检测函数
        from install_gpu_support import detect_gpu
        
        # 调用GPU检测函数
        gpu_info = detect_gpu()
        
        logger.info(f"GPU检测结果: {gpu_info}")
        
        return {
            'success': True,
            'gpu_info': gpu_info
        }
    except Exception as e:
        logger.error(f"测试GPU检测功能时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            'success': False,
            'error': str(e)
        }

def test_feature_engineering_adapter():
    """测试特征工程适配器"""
    logger.info("测试特征工程适配器")
    
    try:
        # 导入特征工程适配器
        from quant_project.core_logic.feature_engineering_adapter import FeatureEngineeringAdapter
        
        # 创建适配器实例
        adapter = FeatureEngineeringAdapter()
        
        # 测试扁平格式配置
        flat_config = {
            'use_price': True,
            'use_volume': True,
            'use_technical': True,
            'sma_periods': [5, 10, 20, 30, 60],
            'ema_periods': [5, 10, 20, 30, 60],
            'rsi_periods': [14],
            'macd_params': {'fast': 12, 'slow': 26, 'signal': 9},
            'bb_params': {'window': 20, 'num_std': 2},
            'atr_periods': [14],
            'normalization': 'zscore'
        }
        
        # 转换为标准格式
        standard_config = adapter.adapt_config(flat_config)
        
        logger.info(f"扁平格式配置: {flat_config}")
        logger.info(f"转换后的标准格式配置: {standard_config}")
        
        # 测试标准格式配置
        standard_config_input = {
            'price_features': {'use': True},
            'volume': {'use': True, 'periods': [5, 10, 20]},
            'sma': {'use': True, 'periods': [5, 10, 20, 60]},
            'ema': {'use': True, 'periods': [5, 10, 20]},
            'rsi': {'use': True, 'periods': [6, 14, 21]},
            'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9},
            'bbands': {'use': True, 'period': 20, 'std': 2.0},
            'normalization': {'use': True, 'method': 'minmax'}
        }
        
        # 确认标准格式配置不变
        result_config = adapter.adapt_config(standard_config_input)
        
        logger.info(f"标准格式配置: {standard_config_input}")
        logger.info(f"适配后的配置: {result_config}")
        
        # 验证结果
        is_flat_converted = all(key in standard_config for key in ['sma', 'ema', 'rsi', 'macd', 'bbands'])
        is_standard_unchanged = result_config == standard_config_input
        
        return {
            'success': is_flat_converted and is_standard_unchanged,
            'flat_converted': is_flat_converted,
            'standard_unchanged': is_standard_unchanged
        }
    except Exception as e:
        logger.error(f"测试特征工程适配器时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            'success': False,
            'error': str(e)
        }

def test_performance_analyzer():
    """测试性能分析器参数处理"""
    logger.info("测试性能分析器参数处理")
    
    try:
        # 导入性能分析器
        from quant_project.core_logic.performance_analyzer import PerformanceAnalyzer
        
        # 创建性能分析器实例
        analyzer = PerformanceAnalyzer()
        
        # 创建模拟交易记录DataFrame
        trades_df = pd.DataFrame({
            'date': pd.date_range(start='2022-01-01', periods=10, freq='D'),
            'action': ['buy', 'sell', 'buy', 'sell', 'buy', 'sell', 'buy', 'sell', 'buy', 'sell'],
            'price': [100, 105, 98, 103, 101, 106, 99, 104, 102, 107],
            'quantity': [10, 10, 15, 15, 20, 20, 25, 25, 30, 30],
            'value': [1000, 1050, 1470, 1545, 2020, 2120, 2475, 2600, 3060, 3210],
            'commission': [1, 1.05, 1.47, 1.545, 2.02, 2.12, 2.475, 2.6, 3.06, 3.21]
        })
        
        # 创建模拟组合价值历史
        portfolio_values = pd.Series(
            [100000 * (1 + 0.001 * i) for i in range(100)],
            index=pd.date_range(start='2022-01-01', periods=100, freq='D')
        )
        
        # 测试直接传入DataFrame
        logger.info("测试直接传入DataFrame")
        metrics_df = analyzer.analyze(trades_df, portfolio_values)
        
        # 测试传入列表
        logger.info("测试传入列表")
        metrics_list = analyzer.analyze(trades_df.to_dict('records'), portfolio_values)
        
        # 验证结果
        logger.info(f"DataFrame参数结果: {metrics_df}")
        logger.info(f"列表参数结果: {metrics_list}")
        
        # 检查关键指标是否存在
        key_metrics = ['total_return', 'annualized_return', 'max_drawdown', 'sharpe_ratio']
        df_has_metrics = all(metric in metrics_df for metric in key_metrics)
        list_has_metrics = all(metric in metrics_list for metric in key_metrics)
        
        return {
            'success': df_has_metrics and list_has_metrics,
            'df_metrics': df_has_metrics,
            'list_metrics': list_has_metrics
        }
    except Exception as e:
        logger.error(f"测试性能分析器参数处理时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            'success': False,
            'error': str(e)
        }

def test_data_fetching():
    """测试数据获取功能"""
    logger.info("=== 测试数据获取功能 ===")
    
    from quant_project.core_logic.data_handling import DataHandler
    from quant_project.core_logic.data_handling.adapter import DataHandlerAdapter
    
    try:
        data_handler = DataHandlerAdapter()
        
        # 测试不同参数组合
        test_cases = [
            {
                "stock_code": "sh000001",
                "start_date": "2020-01-01",
                "end_date": "2020-12-31",
                "frequency": "日线",
                "data_source": "指数"
            },
            {
                "stock_code": "sh600000",
                "start_date": "2021-01-01",
                "end_date": "2021-06-30",
                "frequency": "周线",
                "data_source": "股票"
            }
        ]
        
        for case in test_cases:
            logger.info(f"测试数据获取: {case}")
            data = data_handler.get_stock_data(**case)
            
            # 验证数据
            if data is not None and len(data) > 0:
                logger.info(f"成功获取数据: {len(data)} 条记录")
                logger.info(f"数据范围: {data.index[0]} 至 {data.index[-1]}")
                logger.info(f"数据列: {data.columns.tolist()}")
            else:
                logger.error("数据获取失败: 返回空数据")
                return False
        
        logger.info("数据获取功能测试通过")
        return True
    
    except Exception as e:
        logger.error(f"数据获取功能测试失败: {str(e)}")
        return False

def test_factor_mining():
    """测试因子挖掘功能"""
    logger.info("=== 测试因子挖掘功能 ===")
    
    try:
        # 导入必要的模块
        from quant_project.core_logic.data_handling import DataHandler
        from quant_project.core_logic.data_handling.adapter import DataHandlerAdapter
        from quant_project.core_logic.factor_mining.factor_pipeline import AutoFactorPipeline
        
        # 获取测试数据
        data_handler = DataHandlerAdapter()
        data = data_handler.get_stock_data(
            stock_code="sh000001",
            start_date="2020-01-01",
            end_date="2020-12-31",
            frequency="日线",
            data_source="指数"
        )
        
        if data is None or len(data) == 0:
            logger.error("无法获取测试数据")
            return False
        
        # 创建因子挖掘流水线
        pipeline = AutoFactorPipeline(data_handler)
        
        # 配置流水线
        pipeline.configure(
            min_ic_abs=0.05,
            corr_threshold=0.7,
            top_n_factors=10,
            generate_technical_factors=True,
            generate_cross_factors=True,
            generate_time_factors=True,
            enable_parallel=False,
            verbose=True
        )
        
        # 创建进度回调函数
        def progress_callback(stage, progress, message):
            logger.info(f"因子挖掘进度: {stage} - {progress:.0%} - {message}")
        
        # 运行因子挖掘
        results = pipeline.run_with_data(
            data,
            min_ic_abs=0.05,
            corr_threshold=0.7,
            top_n_factors=10,
            progress_callback=progress_callback
        )
        
        # 验证结果
        if results and results.get('status') == 'success':
            best_factors = results.get('best_factors', {})
            logger.info(f"成功挖掘出 {len(best_factors)} 个因子")
            logger.info(f"因子列表: {list(best_factors.keys())}")
            logger.info("因子挖掘功能测试通过")
            return True
        else:
            logger.error(f"因子挖掘失败: {results.get('message', '未知错误')}")
            return False
    
    except Exception as e:
        logger.error(f"因子挖掘功能测试失败: {str(e)}")
        return False

def test_drl_agent():
    """测试DRL智能体功能"""
    logger.info("=== 测试DRL智能体功能 ===")
    
    try:
        # 导入必要的模块
        from quant_project.core_logic.drl_agent import DRLAgent
        
        # 创建DRL智能体
        agent = DRLAgent(algorithm="PPO")
        
        # 验证智能体属性和方法
        logger.info(f"DRL智能体算法: {agent.algorithm}")
        logger.info(f"智能体方法: {dir(agent)}")
        
        # 检查必要的方法
        required_methods = ['train', 'predict', 'save', 'load']
        for method in required_methods:
            if not hasattr(agent, method) or not callable(getattr(agent, method)):
                logger.error(f"DRL智能体缺少必要的方法: {method}")
                return False
        
        logger.info("DRL智能体功能测试通过")
        return True
    
    except Exception as e:
        logger.error(f"DRL智能体功能测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("=" * 50)
    logger.info("开始测试修复后的功能")
    logger.info("=" * 50)
    
    # 测试GPU检测功能
    gpu_result = test_gpu_detection()
    
    # 测试特征工程适配器
    feature_result = test_feature_engineering_adapter()
    
    # 测试性能分析器参数处理
    performance_result = test_performance_analyzer()
    
    # 测试数据获取功能
    data_fetching_result = test_data_fetching()
    
    # 测试因子挖掘功能
    factor_mining_result = test_factor_mining()
    
    # 测试DRL智能体功能
    drl_agent_result = test_drl_agent()
    
    # 输出测试结果
    logger.info("=" * 50)
    logger.info("测试结果:")
    logger.info(f"1. GPU检测功能: {'成功' if gpu_result['success'] else '失败'}")
    if not gpu_result['success']:
        logger.info(f"   错误: {gpu_result.get('error', '未知错误')}")
    
    logger.info(f"2. 特征工程适配器: {'成功' if feature_result['success'] else '失败'}")
    if not feature_result['success']:
        logger.info(f"   错误: {feature_result.get('error', '未知错误')}")
    
    logger.info(f"3. 性能分析器参数处理: {'成功' if performance_result['success'] else '失败'}")
    if not performance_result['success']:
        logger.info(f"   错误: {performance_result.get('error', '未知错误')}")
    
    logger.info(f"4. 数据获取功能: {'成功' if data_fetching_result else '失败'}")
    if not data_fetching_result:
        logger.info(f"   错误: {data_fetching_result}")
    
    logger.info(f"5. 因子挖掘功能: {'成功' if factor_mining_result else '失败'}")
    if not factor_mining_result:
        logger.info(f"   错误: {factor_mining_result}")
    
    logger.info(f"6. DRL智能体功能: {'成功' if drl_agent_result else '失败'}")
    if not drl_agent_result:
        logger.info(f"   错误: {drl_agent_result}")
    
    logger.info("=" * 50)
    
    # 总结
    all_success = gpu_result['success'] and feature_result['success'] and performance_result['success'] and data_fetching_result and factor_mining_result and drl_agent_result
    logger.info(f"修复测试总结: {'全部通过' if all_success else '部分失败'}")
    
    return all_success

if __name__ == "__main__":
    sys.exit(main())
