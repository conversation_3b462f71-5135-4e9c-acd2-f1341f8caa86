"""
数据字典模块
提供特征的详细说明、计算方法和解释
"""

import pandas as pd
import json
import os
import logging
from datetime import datetime

class DataDictionary:
    """
    数据字典类
    记录和管理特征的元数据
    """
    
    def __init__(self, dictionary_path='data_dictionary.json'):
        """
        初始化数据字典
        
        参数:
            dictionary_path (str): 数据字典文件路径
        """
        self.logger = logging.getLogger('drl_trading')
        self.dictionary_path = dictionary_path
        self.dictionary = self._load_dictionary()
        
    def _load_dictionary(self):
        """
        加载数据字典
        
        返回:
            dict: 数据字典
        """
        if os.path.exists(self.dictionary_path):
            try:
                with open(self.dictionary_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                self.logger.warning(f"加载数据字典失败: {str(e)}，将创建新的数据字典")
        
        # 创建默认数据字典
        return {
            'version': '1.0.0',
            'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'features': {}
        }
    
    def save_dictionary(self):
        """
        保存数据字典
        """
        try:
            # 更新时间戳
            self.dictionary['last_updated'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # 保存到文件
            with open(self.dictionary_path, 'w', encoding='utf-8') as f:
                json.dump(self.dictionary, f, ensure_ascii=False, indent=4)
                
            self.logger.info(f"数据字典已保存到 {self.dictionary_path}")
        except Exception as e:
            self.logger.error(f"保存数据字典失败: {str(e)}")
    
    def add_feature(self, feature_name, category, description, calculation_method, interpretation=None, source=None, tags=None):
        """
        添加特征到数据字典
        
        参数:
            feature_name (str): 特征名称
            category (str): 特征类别 (如 '价格特征', '技术指标', '统计特征' 等)
            description (str): 特征描述
            calculation_method (str): 计算方法
            interpretation (str, optional): 特征解释
            source (str, optional): 特征来源
            tags (list, optional): 特征标签
        """
        if 'features' not in self.dictionary:
            self.dictionary['features'] = {}
            
        self.dictionary['features'][feature_name] = {
            'category': category,
            'description': description,
            'calculation_method': calculation_method,
            'interpretation': interpretation or '',
            'source': source or 'internal',
            'tags': tags or [],
            'added_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        self.logger.info(f"特征 '{feature_name}' 已添加到数据字典")
    
    def get_feature_info(self, feature_name):
        """
        获取特征信息
        
        参数:
            feature_name (str): 特征名称
            
        返回:
            dict: 特征信息
        """
        if feature_name in self.dictionary.get('features', {}):
            return self.dictionary['features'][feature_name]
        else:
            self.logger.warning(f"特征 '{feature_name}' 不在数据字典中")
            return None
    
    def get_features_by_category(self, category):
        """
        按类别获取特征
        
        参数:
            category (str): 特征类别
            
        返回:
            dict: 该类别的所有特征
        """
        return {name: info for name, info in self.dictionary.get('features', {}).items() 
                if info.get('category') == category}
    
    def get_features_by_tag(self, tag):
        """
        按标签获取特征
        
        参数:
            tag (str): 特征标签
            
        返回:
            dict: 包含该标签的所有特征
        """
        return {name: info for name, info in self.dictionary.get('features', {}).items() 
                if tag in info.get('tags', [])}
    
    def generate_data_dictionary_report(self, output_path=None):
        """
        生成数据字典报告
        
        参数:
            output_path (str, optional): 输出文件路径
            
        返回:
            str: 报告内容
        """
        report = f"# 数据字典报告\n\n"
        report += f"版本: {self.dictionary.get('version', '未知')}\n"
        report += f"最后更新: {self.dictionary.get('last_updated', '未知')}\n\n"
        
        # 按类别组织特征
        categories = {}
        for name, info in self.dictionary.get('features', {}).items():
            category = info.get('category', '其他')
            if category not in categories:
                categories[category] = []
            categories[category].append((name, info))
        
        # 生成报告内容
        for category, features in categories.items():
            report += f"## {category}\n\n"
            
            for name, info in features:
                report += f"### {name}\n\n"
                report += f"**描述**: {info.get('description', '')}\n\n"
                report += f"**计算方法**: {info.get('calculation_method', '')}\n\n"
                
                if info.get('interpretation'):
                    report += f"**解释**: {info.get('interpretation', '')}\n\n"
                
                if info.get('tags'):
                    report += f"**标签**: {', '.join(info.get('tags', []))}\n\n"
                
                report += f"**添加日期**: {info.get('added_date', '')}\n\n"
                
                report += "---\n\n"
        
        # 保存报告
        if output_path:
            try:
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(report)
                self.logger.info(f"数据字典报告已保存到 {output_path}")
            except Exception as e:
                self.logger.error(f"保存数据字典报告失败: {str(e)}")
        
        return report
    
    def initialize_default_dictionary(self):
        """
        初始化默认数据字典，包含常用特征的说明
        """
        # 基础价格特征
        self.add_feature(
            '涨跌幅', '价格特征', 
            '当日收盘价相对前一日收盘价的变化百分比', 
            'df[\'涨跌幅\'] = df[\'收盘\'].pct_change()',
            '正值表示上涨，负值表示下跌，数值大小表示变化幅度'
        )
        
        self.add_feature(
            '对数收益率', '价格特征', 
            '当日收盘价与前一日收盘价的对数差', 
            'df[\'对数收益率\'] = np.log(df[\'收盘\'] / df[\'收盘\'].shift(1))',
            '对数收益率具有更好的统计特性，适合长期分析'
        )
        
        # 技术指标
        self.add_feature(
            'SMA_20', '技术指标', 
            '20日简单移动平均线', 
            'df[\'SMA_20\'] = ta.trend.sma_indicator(df[\'收盘\'], window=20)',
            '反映中期价格趋势，高于SMA为看涨信号，低于SMA为看跌信号',
            tags=['趋势指标', '移动平均线']
        )
        
        self.add_feature(
            'RSI_14', '技术指标', 
            '14日相对强弱指标', 
            'df[\'RSI_14\'] = ta.momentum.rsi(df[\'收盘\'], window=14)',
            'RSI > 70 表示超买，RSI < 30 表示超卖，可能出现反转',
            tags=['动量指标', '超买超卖']
        )
        
        # 统计特征
        self.add_feature(
            'Rolling_Volatility_20', '统计特征', 
            '20日滚动波动率', 
            'df[\'Rolling_Volatility_20\'] = df[\'涨跌幅\'].rolling(window=20).std()',
            '反映价格的不稳定性，高波动率表示市场不确定性增加',
            tags=['波动率', '风险指标']
        )
        
        # 保存数据字典
        self.save_dictionary()
