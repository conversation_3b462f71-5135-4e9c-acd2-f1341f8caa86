"""
性能分析模块
负责计算交易策略的性能指标
"""

import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

logger = logging.getLogger('drl_trading')

class PerformanceAnalyzer:
    """
    性能分析类
    负责计算交易策略的性能指标
    """

    def __init__(self, risk_free_rate=0.02):
        """
        初始化性能分析器
        
        参数:
            risk_free_rate: 无风险利率，默认为0.02（2%）
        """
        self.logger = logger
        self.risk_free_rate = risk_free_rate

    def analyze(self, trades, portfolio_values, risk_free_rate=None, benchmark_returns=None):
        """
        分析交易策略性能
        
        参数:
            trades: 交易记录，可以是DataFrame或列表
            portfolio_values: 组合价值历史，可以是Series或DataFrame
            risk_free_rate: 无风险利率，默认为None（使用实例的风险利率）
            benchmark_returns: 基准收益率，默认为None
            
        返回:
            dict: 性能指标
        """
        # 将trades转换为DataFrame
        trades_df = self._ensure_dataframe(trades)
        
        # 将portfolio_values转换为Series
        if isinstance(portfolio_values, pd.DataFrame):
            if 'portfolio_value' in portfolio_values.columns:
                portfolio_series = portfolio_values['portfolio_value']
            else:
                portfolio_series = portfolio_values.iloc[:, 0]
        else:
            portfolio_series = portfolio_values
        
        # 使用传入的风险利率，如果为None则使用实例的风险利率
        rf_rate = risk_free_rate if risk_free_rate is not None else self.risk_free_rate
        
        # 计算基本收益指标
        total_return = self._calculate_total_return(portfolio_series)
        annualized_return = self._calculate_annualized_return(portfolio_series)
        daily_returns = self._calculate_daily_returns(portfolio_series)
        
        # 计算风险指标
        volatility = self._calculate_volatility(daily_returns)
        max_drawdown_info = self._calculate_max_drawdown(portfolio_series)
        
        # 计算风险调整收益指标
        sharpe_ratio = self._calculate_sharpe_ratio(daily_returns, rf_rate)
        sortino_ratio = self._calculate_sortino_ratio(daily_returns, rf_rate)
        
        # 计算交易相关指标
        trade_metrics = self._calculate_trade_metrics(trades_df)
        
        # 计算基准对比指标
        benchmark_metrics = {}
        if benchmark_returns is not None:
            benchmark_metrics = self._calculate_benchmark_metrics(daily_returns, benchmark_returns)
        
        # 汇总所有指标
        metrics = {
            # 基本收益指标
            'total_return': total_return,
            'annualized_return': annualized_return,
            'daily_return_mean': daily_returns.mean(),
            'daily_return_std': daily_returns.std(),
            
            # 风险指标
            'volatility': volatility,
            'max_drawdown': max_drawdown_info['max_drawdown'],
            'max_drawdown_duration': max_drawdown_info['duration'],
            'max_drawdown_start': max_drawdown_info['start_date'],
            'max_drawdown_end': max_drawdown_info['end_date'],
            
            # 风险调整收益指标
            'sharpe_ratio': sharpe_ratio,
            'sortino_ratio': sortino_ratio,
            
            # 交易相关指标
            'trade_count': trade_metrics.get('trade_count', 0),
            'win_rate': trade_metrics.get('win_rate', 0),
            'profit_factor': trade_metrics.get('profit_factor', 0),
            'avg_profit': trade_metrics.get('avg_profit', 0),
            'avg_loss': trade_metrics.get('avg_loss', 0),
            'largest_profit': trade_metrics.get('largest_profit', 0),
            'largest_loss': trade_metrics.get('largest_loss', 0),
        }
        
        # 添加基准对比指标
        metrics.update(benchmark_metrics)
        
        return metrics

    def _ensure_dataframe(self, data):
        """
        确保数据为DataFrame格式
        
        参数:
            data: 输入数据，可以是DataFrame或列表
            
        返回:
            pandas.DataFrame: 转换后的DataFrame
        """
        if isinstance(data, pd.DataFrame):
            return data
        elif isinstance(data, list):
            return pd.DataFrame(data)
        elif data is None:
            return pd.DataFrame()
        else:
            self.logger.warning(f"无法识别的数据类型: {type(data)}，返回空DataFrame")
            return pd.DataFrame()

    def _calculate_total_return(self, portfolio_values):
        """
        计算总收益率
        
        参数:
            portfolio_values: 组合价值历史
            
        返回:
            float: 总收益率
        """
        if len(portfolio_values) < 2:
            return 0
        
        return (portfolio_values.iloc[-1] / portfolio_values.iloc[0]) - 1

    def _calculate_annualized_return(self, portfolio_values):
        """
        计算年化收益率
        
        参数:
            portfolio_values: 组合价值历史
            
        返回:
            float: 年化收益率
        """
        if len(portfolio_values) < 2:
            return 0
        
        total_return = self._calculate_total_return(portfolio_values)
        
        # 计算交易天数
        days = (portfolio_values.index[-1] - portfolio_values.index[0]).days
        
        if days <= 0:
            return 0
        
        # 计算年化收益率
        years = days / 365
        return (1 + total_return) ** (1 / years) - 1

    def _calculate_max_drawdown(self, portfolio_values):
        """
        计算最大回撤
        
        参数:
            portfolio_values: 组合价值历史
            
        返回:
            dict: 最大回撤信息
        """
        if len(portfolio_values) < 2:
            return {
                'max_drawdown': 0,
                'duration': 0,
                'start_date': None,
                'end_date': None
            }
        
        # 计算累计最大值
        running_max = portfolio_values.cummax()
        
        # 计算回撤
        drawdown = (portfolio_values - running_max) / running_max
        
        # 找到最大回撤及其位置
        max_drawdown = drawdown.min()
        end_idx = drawdown.idxmin()
        
        # 找到最大回撤的开始点
        start_idx = portfolio_values.loc[:end_idx].idxmax()
        
        # 计算回撤持续时间
        if isinstance(start_idx, pd.Timestamp) and isinstance(end_idx, pd.Timestamp):
            duration = (end_idx - start_idx).days
        else:
            duration = 0
        
        # 返回最大回撤信息
        return {
            'max_drawdown': max_drawdown,
            'duration': duration,
            'start_date': start_idx,
            'end_date': end_idx
        }

    def _calculate_sharpe_ratio(self, returns, risk_free_rate=None, periods_per_year=252):
        """
        计算夏普比率
        
        参数:
            returns: 收益率序列
            risk_free_rate: 无风险利率，默认为None（使用实例的风险利率）
            periods_per_year: 一年的期数，默认为252（交易日）
            
        返回:
            float: 夏普比率
        """
        if len(returns) < 2:
            return 0
        
        # 使用参数的无风险利率，如果为None则使用实例的无风险利率
        rf_rate = risk_free_rate if risk_free_rate is not None else self.risk_free_rate
        
        # 防止除以零
        if periods_per_year == 0:
            periods_per_year = 252
        
        # 计算超额收益
        daily_rf_rate = rf_rate / periods_per_year
        excess_returns = returns - daily_rf_rate
        
        # 计算夏普比率
        std = excess_returns.std()
        if std == 0:
            return 0  # 避免除以零
            
        sharpe_ratio = excess_returns.mean() / std
        
        # 年化夏普比率
        return sharpe_ratio * np.sqrt(periods_per_year)

    def _calculate_sortino_ratio(self, returns, risk_free_rate=None, periods_per_year=252):
        """
        计算索提诺比率
        
        参数:
            returns: 收益率序列
            risk_free_rate: 无风险利率，默认为None（使用实例的风险利率）
            periods_per_year: 一年的期数，默认为252（交易日）
            
        返回:
            float: 索提诺比率
        """
        if len(returns) < 2:
            return 0
        
        # 使用参数的无风险利率，如果为None则使用实例的无风险利率
        rf_rate = risk_free_rate if risk_free_rate is not None else self.risk_free_rate
        
        # 防止除以零
        if periods_per_year == 0:
            periods_per_year = 252
        
        # 计算超额收益
        daily_rf_rate = rf_rate / periods_per_year
        excess_returns = returns - daily_rf_rate
        
        # 计算下行波动率
        downside_returns = excess_returns[excess_returns < 0]
        
        if len(downside_returns) == 0:
            return np.inf  # 没有负收益，索提诺比率为无穷大
        
        downside_std = downside_returns.std()
        
        if downside_std == 0:
            return np.inf  # 避免除以零
        
        # 计算索提诺比率
        sortino_ratio = excess_returns.mean() / downside_std
        
        # 年化索提诺比率
        return sortino_ratio * np.sqrt(periods_per_year)

    def _calculate_calmar_ratio(self, annualized_return, max_drawdown):
        """
        计算卡玛比率

        参数:
            annualized_return (float): 年化收益率
            max_drawdown (float): 最大回撤

        返回:
            float: 卡玛比率
        """
        if max_drawdown >= 0:
            return 0.0

        return -annualized_return / max_drawdown

    def _calculate_trade_metrics(self, trades_df):
        """
        计算交易相关指标
        
        参数:
            trades_df: 交易记录DataFrame
            
        返回:
            dict: 交易相关指标
        """
        if trades_df.empty:
            return {
                'trade_count': 0,
                'win_rate': 0,
                'profit_factor': 0,
                'avg_profit': 0,
                'avg_loss': 0,
                'largest_profit': 0,
                'largest_loss': 0
            }
        
        # 整理交易记录，确保交易对已匹配
        trades_metrics = {}
        
        # 简单计算交易次数
        trades_metrics['trade_count'] = len(trades_df)
        
        # 如果有利润/亏损列
        if 'profit' in trades_df.columns:
            profits = trades_df[trades_df['profit'] > 0]['profit']
            losses = trades_df[trades_df['profit'] < 0]['profit']
            
            # 计算胜率
            if len(trades_df) > 0:
                trades_metrics['win_rate'] = len(profits) / len(trades_df)
            else:
                trades_metrics['win_rate'] = 0
            
            # 计算盈亏比
            if len(losses) > 0 and losses.sum() != 0:
                trades_metrics['profit_factor'] = abs(profits.sum() / losses.sum())
            else:
                trades_metrics['profit_factor'] = float('inf') if len(profits) > 0 else 0
            
            # 计算平均利润和平均亏损
            trades_metrics['avg_profit'] = profits.mean() if len(profits) > 0 else 0
            trades_metrics['avg_loss'] = losses.mean() if len(losses) > 0 else 0
            
            # 计算最大利润和最大亏损
            trades_metrics['largest_profit'] = profits.max() if len(profits) > 0 else 0
            trades_metrics['largest_loss'] = losses.min() if len(losses) > 0 else 0
        
        return trades_metrics

    def _calculate_period_returns(self, portfolio_values, period='M'):
        """
        计算周期收益率

        参数:
            portfolio_values (pandas.Series): 组合价值序列
            period (str): 周期，'M'表示月，'Y'表示年

        返回:
            pandas.Series: 周期收益率
        """
        if len(portfolio_values) < 2:
            return pd.Series()

        # 确保索引是日期类型
        if not isinstance(portfolio_values.index, pd.DatetimeIndex):
            portfolio_values.index = pd.to_datetime(portfolio_values.index)

        # 重采样到指定周期的最后一天
        period_end_values = portfolio_values.resample(period).last()

        # 计算周期收益率
        period_returns = period_end_values.pct_change().dropna()

        return period_returns

    def _calculate_alpha_beta(self, returns, benchmark_returns):
        """
        计算阿尔法和贝塔

        参数:
            returns (pandas.Series): 策略收益率序列
            benchmark_returns (pandas.Series): 基准收益率序列

        返回:
            dict: 阿尔法和贝塔
        """
        if len(returns) < 2 or len(benchmark_returns) < 2:
            return {'alpha': 0.0, 'beta': 0.0}

        # 对齐数据
        aligned_returns = pd.concat([returns, benchmark_returns], axis=1).dropna()
        if len(aligned_returns) < 2:
            return {'alpha': 0.0, 'beta': 0.0}

        strategy_returns = aligned_returns.iloc[:, 0]
        benchmark_returns = aligned_returns.iloc[:, 1]

        # 计算贝塔
        covariance = strategy_returns.cov(benchmark_returns)
        benchmark_variance = benchmark_returns.var()
        beta = covariance / benchmark_variance if benchmark_variance != 0 else 0.0

        # 计算阿尔法 (年化)
        alpha = (strategy_returns.mean() - self.risk_free_rate / 252 - beta * (benchmark_returns.mean() - self.risk_free_rate / 252)) * 252

        return {'alpha': alpha, 'beta': beta}

    def _calculate_information_ratio(self, returns, benchmark_returns):
        """
        计算信息比率

        参数:
            returns (pandas.Series): 策略收益率序列
            benchmark_returns (pandas.Series): 基准收益率序列

        返回:
            float: 信息比率
        """
        if len(returns) < 2 or len(benchmark_returns) < 2:
            return 0.0

        # 对齐数据
        aligned_returns = pd.concat([returns, benchmark_returns], axis=1).dropna()
        if len(aligned_returns) < 2:
            return 0.0

        strategy_returns = aligned_returns.iloc[:, 0]
        benchmark_returns = aligned_returns.iloc[:, 1]

        # 计算超额收益
        excess_returns = strategy_returns - benchmark_returns

        # 计算信息比率
        information_ratio = excess_returns.mean() / (excess_returns.std() + 1e-10) * np.sqrt(252)

        return information_ratio

    def plot_performance(self, portfolio_values, trades=None, benchmark=None):
        """
        绘制性能图表
        
        参数:
            portfolio_values: 组合价值历史
            trades: 交易记录，可选
            benchmark: 基准价值历史，可选
            
        返回:
            matplotlib.figure.Figure: 图表对象
        """
        # 创建图表
        fig, axes = plt.subplots(3, 1, figsize=(12, 15), gridspec_kw={'height_ratios': [3, 1, 1]})
        
        # 绘制组合价值
        portfolio_values.plot(ax=axes[0], label='策略')
        
        # 如果有基准，也绘制基准
        if benchmark is not None:
            benchmark.plot(ax=axes[0], label='基准', alpha=0.7)
        
        axes[0].set_title('组合价值')
        axes[0].set_ylabel('价值')
        axes[0].grid(True)
        axes[0].legend()
        
        # 如果有交易记录，绘制买卖点
        if trades is not None:
            trades_df = self._ensure_dataframe(trades)
            
            if not trades_df.empty:
                # 买入点
                buy_trades = trades_df[trades_df['action'] == 'buy']
                if not buy_trades.empty and 'date' in buy_trades.columns and 'price' in buy_trades.columns:
                    axes[0].scatter(buy_trades['date'], buy_trades['price'], color='green', marker='^', label='买入')
                
                # 卖出点
                sell_trades = trades_df[trades_df['action'] == 'sell']
                if not sell_trades.empty and 'date' in sell_trades.columns and 'price' in sell_trades.columns:
                    axes[0].scatter(sell_trades['date'], sell_trades['price'], color='red', marker='v', label='卖出')
                
                axes[0].legend()
        
        # 绘制收益率
        returns = portfolio_values.pct_change()
        cum_returns = (1 + returns).cumprod() - 1
        cum_returns.plot(ax=axes[1])
        axes[1].set_title('累计收益率')
        axes[1].set_ylabel('收益率')
        axes[1].grid(True)
        
        # 绘制回撤
        running_max = portfolio_values.cummax()
        drawdown = (portfolio_values - running_max) / running_max
        drawdown.plot(ax=axes[2], color='red')
        axes[2].set_title('回撤')
        axes[2].set_ylabel('回撤')
        axes[2].grid(True)
        
        plt.tight_layout()
        
        return fig

    def _calculate_daily_returns(self, portfolio_values):
        """
        计算日收益率
        
        参数:
            portfolio_values: 组合价值历史
            
        返回:
            pandas.Series: 日收益率序列
        """
        return portfolio_values.pct_change().dropna()

    def _calculate_volatility(self, daily_returns, annualized=True):
        """
        计算波动率
        
        参数:
            daily_returns: 日收益率序列
            annualized: 是否年化，默认为True
            
        返回:
            float: 波动率
        """
        if len(daily_returns) < 2:
            return 0
        
        # 计算标准差
        volatility = daily_returns.std()
        
        # 年化波动率
        if annualized:
            volatility = volatility * np.sqrt(252)
            
        return volatility

    def _calculate_benchmark_metrics(self, daily_returns, benchmark_returns):
        """
        计算基准对比指标
        
        参数:
            daily_returns: 策略日收益率序列
            benchmark_returns: 基准日收益率序列
            
        返回:
            dict: 基准对比指标
        """
        if len(daily_returns) < 2 or len(benchmark_returns) < 2:
            return {}
        
        # 确保基准收益率与策略收益率具有相同的索引
        common_index = daily_returns.index.intersection(benchmark_returns.index)
        
        if len(common_index) == 0:
            return {}
        
        strategy_returns = daily_returns.loc[common_index]
        benchmark = benchmark_returns.loc[common_index]
        
        # 计算相对基准的超额收益
        excess_returns = strategy_returns - benchmark
        
        # 计算信息比率
        info_ratio = 0
        if excess_returns.std() > 0:
            info_ratio = excess_returns.mean() / excess_returns.std() * np.sqrt(252)
        
        # 计算Beta
        covariance = np.cov(strategy_returns, benchmark)[0, 1]
        variance = np.var(benchmark)
        beta = covariance / variance if variance != 0 else 0
        
        # 计算Alpha (Jensen's Alpha)
        risk_free_rate = self.risk_free_rate / 252  # 日化无风险利率
        alpha = strategy_returns.mean() - (risk_free_rate + beta * (benchmark.mean() - risk_free_rate))
        alpha = alpha * 252  # 年化Alpha
        
        return {
            'alpha': alpha,
            'beta': beta,
            'information_ratio': info_ratio,
            'excess_return': excess_returns.mean() * 252,  # 年化超额收益
            'tracking_error': excess_returns.std() * np.sqrt(252)  # 年化跟踪误差
        }
