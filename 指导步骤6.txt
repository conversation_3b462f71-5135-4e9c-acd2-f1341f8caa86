块 6: 阶段三 (DRL智能体定义 - drl_agent.py 核心)

本块目标： 基于选定的DRL框架（如Stable-Baselines3），在 drl_agent.py 中定义DRL智能体结构，并封装其训练、预测、保存和加载的核心逻辑。

核心任务1：实现 core_logic/drl_agent.py – DRL智能体核心实现

此模块负责DRL智能体的所有生命周期管理。

类定义 DRLAgent (或针对特定算法的派生类)：

__init__(self, env_config, agent_config, hpo_config=None) 方法：
接收参数：
env_config: 字典或对象，包含实例化 TradingEnvironment 所需的配置。
agent_config: 字典或对象，包含DRL智能体的配置（来自UI或配置文件），例如：
algorithm: 字符串，指定DRL算法 (如 'PPO', 'A2C', 'DQN')。
policy_network: 字符串或字典，指定策略网络类型 (如 'MlpPolicy') 及其架构（如隐藏层节点数、激活函数）。
learning_rate: 学习率。
gamma: 折扣因子。
其他算法特定超参数。
hpo_config (可选): 超参数优化相关配置。
内部根据 env_config 实例化一个训练用的 TradingEnvironment。
根据 agent_config['algorithm'] 和DRL框架API，构建DRL模型实例（例如 model = PPO(...)）。GPU使用：若UI指定且可用，则在模型实例化时传递 device='cuda' (PyTorch) 或相应配置。
train(self, total_timesteps, callback_list=None, hpo_trial=None) 方法：

接收参数：
total_timesteps: 训练总步数。
callback_list (可选): DRL框架支持的回调函数列表，用于训练过程监控、模型定期保存等（UI实时更新将依赖此）。
hpo_trial (可选, 若使用Optuna等HPO库): Optuna的trial对象。
调用DRL框架模型的 .learn(total_timesteps=total_timesteps, callback=callback_list, ...) 方法启动训练。
训练结束后，保存最终模型（或在回调中定期保存最佳模型）。
返回训练结果或统计信息。
predict_action(self, observation, deterministic=True) 方法：

接收当前环境的 observation。
调用DRL框架模型的 .predict(observation, deterministic=deterministic) 方法获取动作。
返回预测的动作。(deterministic=True 用于评估和实际决策)。
save_model(self, save_path) 方法：

调用DRL框架提供的模型保存API，将训练好的智能体保存到指定的 save_path (应位于 saved_models/ 目录下)。
load_model(self, model_path, eval_env_config=None) 静态或类方法：

调用DRL框架提供的模型加载API，从 model_path 加载已保存的智能体。
eval_env_config (可选): 如果加载模型用于新的评估环境，可能需要此配置来设置评估环境。
返回加载的智能体模型实例。
文档与注释： DRLAgent 类及其所有方法必须有详尽的文档字符串。

模块化测试与验证要求 (drl_agent.py 初步封装后)：

单元测试 (在 tests/unit/test_drl_agent.py 中概念性完成或实际编写)：
实例化与配置测试： 验证 DRLAgent 能否根据不同 agent_config 正确实例化相应DRL模型。
最小化训练测试 (Smoke Test)： 使用最简化的 TradingEnvironment 实例（已通过 check_env()）和极小的 total_timesteps，确保训练过程能无报错地启动和完成，且模型参数有变化。
模型持久化测试： 测试 save_model() 和 load_model() 功能。确保模型能正确保存和加载，且加载后的模型能进行预测。
完成 drl_agent.py 的核心功能封装及上述测试思路后，接下来将进行UI部分的集成。

