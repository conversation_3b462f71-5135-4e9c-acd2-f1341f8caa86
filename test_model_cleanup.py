"""
测试模型清理功能
确保训练过程中只保留一个最佳模型
"""

import os
import logging
import numpy as np
import pandas as pd
import time
import shutil
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_model_cleanup')

# 导入核心模块
import sys
import os

# 添加项目根目录到Python路径
sys.path.append('.')

# 直接导入模块
from quant_project.core_logic.drl_agent import DRLAgent, BestModelCallback
from quant_project.core_logic.trading_environment import TradingEnvironment
from quant_project.core_logic.utils import is_gpu_available

def create_test_data():
    """创建测试数据"""
    # 创建一个简单的价格序列
    dates = pd.date_range(start='2020-01-01', periods=100, freq='D')
    prices = np.random.normal(100, 10, 100).cumsum() + 1000
    volumes = np.random.randint(1000, 10000, 100)

    # 创建DataFrame
    df = pd.DataFrame({
        '开盘': prices * 0.99,
        '最高': prices * 1.02,
        '最低': prices * 0.98,
        '收盘': prices,
        '成交量': volumes
    }, index=dates)

    # 添加一些技术指标作为特征
    df['ma5'] = df['收盘'].rolling(5).mean()
    df['ma10'] = df['收盘'].rolling(10).mean()
    df['rsi'] = 50 + np.random.normal(0, 10, 100)  # 简化的RSI

    # 填充NaN值
    df = df.fillna(method='bfill')

    return df

def count_models_with_timestamp(timestamp):
    """计算具有特定时间戳或标识符的模型文件数量"""
    models_dir = "saved_models"
    if not os.path.exists(models_dir):
        return 0, 0

    all_files = os.listdir(models_dir)
    model_files = [f for f in all_files if f.endswith(".zip")]

    # 计算带有时间戳或标识符的模型文件数量
    timestamp_models = [f for f in model_files if timestamp in f]

    # 计算带有时间戳或标识符和BEST标记的模型文件数量
    best_models = [f for f in timestamp_models if "BEST" in f]

    # 记录找到的文件
    logger.info(f"找到的模型文件: {timestamp_models}")
    logger.info(f"找到的最佳模型文件: {best_models}")

    return len(timestamp_models), len(best_models)

def main():
    """主函数"""
    logger.info("开始测试模型清理功能")

    # 准备测试目录
    models_dir = "saved_models"
    os.makedirs(models_dir, exist_ok=True)

    # 备份现有模型（如果有）
    backup_dir = "saved_models_backup"
    if os.path.exists(models_dir) and os.listdir(models_dir):
        logger.info("备份现有模型文件")
        os.makedirs(backup_dir, exist_ok=True)
        for item in os.listdir(models_dir):
            src = os.path.join(models_dir, item)
            dst = os.path.join(backup_dir, item)
            if os.path.isfile(src):
                shutil.copy2(src, dst)

    # 清空模型目录，确保测试环境干净
    logger.info("清空模型目录，确保测试环境干净")
    for item in os.listdir(models_dir):
        item_path = os.path.join(models_dir, item)
        if os.path.isfile(item_path):
            os.remove(item_path)

    try:
        # 创建测试数据
        df = create_test_data()
        logger.info(f"创建了测试数据，形状: {df.shape}")

        # 环境配置
        env_config = {
            'df_processed_data': df,
            'initial_capital': 10000,
            'commission_rate': 0.001,
            'window_size': 10
        }

        # 智能体配置
        agent_config = {
            'algorithm': 'PPO',
            'policy_network': 'MlpPolicy',
            'learning_rate': 0.0003,
            'gamma': 0.99,
            'n_steps': 64,
            'batch_size': 32,
            'n_epochs': 5,
            'use_gpu': is_gpu_available()
        }

        # 创建DRL智能体
        drl_agent = DRLAgent(env_config, agent_config)
        logger.info("创建了DRL智能体")

        # 创建评估环境
        eval_env = drl_agent._create_environment()

        # 训练模型
        logger.info("开始训练模型...")
        total_timesteps = 2000  # 短时间训练，但足够进行多次评估

        # 训练并记录时间戳
        training_stats = drl_agent.train(
            total_timesteps=total_timesteps,
            progress_bar=True,
            save_best_model=True
        )

        # 获取训练时间戳
        timestamp = None
        for file in os.listdir(models_dir):
            if file.endswith(".zip") and "BEST" in file:
                # 从文件名中提取时间戳 (格式: BEST_PPO_YYYYMMDD_HHMMSS...)
                parts = file.split("_")
                for i, part in enumerate(parts):
                    if part.startswith("20") and len(part) == 15:  # 时间戳格式: 20YYMMDD_HHMMSS
                        timestamp = part
                        logger.info(f"找到时间戳: {timestamp} 在文件: {file}")
                        break
                if timestamp:
                    break

        # 如果没有找到时间戳，尝试直接使用文件名的一部分
        if not timestamp and len(os.listdir(models_dir)) > 0:
            best_files = [f for f in os.listdir(models_dir) if f.endswith(".zip") and "BEST" in f]
            if best_files:
                # 使用文件名的一部分作为标识符
                file = best_files[0]
                logger.info(f"未找到标准时间戳，使用文件名作为标识: {file}")
                # 使用文件名的前20个字符作为标识符
                timestamp = file[:20]

        if not timestamp:
            logger.error("无法从模型文件名中提取时间戳")
            return False

        # 计算模型文件数量
        total_models, best_models = count_models_with_timestamp(timestamp)
        logger.info(f"训练后模型文件数量: 总计 {total_models}, 最佳模型 {best_models}")

        # 验证是否只有一个最佳模型（针对当前训练会话）
        # 注意：由于我们可能有来自之前测试运行的模型文件，我们只关注当前训练会话
        if best_models == 0:
            logger.error("测试失败: 没有找到最佳模型")
            return False

        # 获取当前训练会话的所有模型文件
        models_dir = "saved_models"
        all_files = os.listdir(models_dir)
        current_session_files = [f for f in all_files if timestamp in f]
        current_best_files = [f for f in current_session_files if "BEST" in f]
        current_non_best_files = [f for f in current_session_files if "BEST" not in f]

        logger.info(f"当前训练会话的模型文件: {current_session_files}")
        logger.info(f"当前训练会话的最佳模型文件: {current_best_files}")
        logger.info(f"当前训练会话的非最佳模型文件: {current_non_best_files}")

        # 验证当前训练会话是否只有一个最佳模型
        if len(current_best_files) != 1:
            logger.error(f"测试失败: 当前训练会话应该只有1个最佳模型，但找到了 {len(current_best_files)} 个")
            return False

        # 验证当前训练会话是否没有非最佳模型（应该被清理）
        if len(current_non_best_files) != 0:
            logger.error(f"测试失败: 当前训练会话应该没有非最佳模型，但找到了 {len(current_non_best_files)} 个")
            return False

        logger.info("测试成功: 只保留了一个最佳模型，清理了所有非最佳模型")
        return True

    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}", exc_info=True)
        return False

    finally:
        # 恢复备份的模型（如果有）
        if os.path.exists(backup_dir) and os.listdir(backup_dir):
            logger.info("恢复备份的模型文件")
            for item in os.listdir(backup_dir):
                src = os.path.join(backup_dir, item)
                dst = os.path.join(models_dir, item)
                if os.path.isfile(src):
                    shutil.copy2(src, dst)

            # 清理备份目录
            shutil.rmtree(backup_dir)

if __name__ == "__main__":
    success = main()
    if success:
        print("测试成功: 模型清理功能正常工作，只保留了一个最佳模型")
    else:
        print("测试失败: 模型清理功能未正常工作")
