"""
特征工程基类
定义特征工程的基本接口和通用功能
"""

import logging
import pandas as pd
import numpy as np
import warnings
import os

# 忽略特定警告
warnings.filterwarnings("ignore", category=RuntimeWarning, message=".*invalid value encountered.*")
warnings.filterwarnings("ignore", category=RuntimeWarning, message=".*divide by zero.*")

try:
    from ..utils import normalize_data
except ImportError:
    try:
        from core_logic.utils import normalize_data
    except ImportError:
        # Fallback: define a simple normalize_data function
        def normalize_data(data, method='minmax'):
            """Simple fallback normalization function"""
            import pandas as pd
            import numpy as np

            if isinstance(data, pd.DataFrame):
                data = data.copy()
                for col in data.columns:
                    try:
                        data.loc[:, col] = pd.to_numeric(data[col], errors='coerce')
                    except:
                        pass

            if method == 'minmax':
                if isinstance(data, pd.DataFrame):
                    min_vals = data.min()
                    max_vals = data.max()
                    return (data - min_vals) / (max_vals - min_vals + 1e-8)
                else:
                    return (data - np.nanmin(data)) / (np.nanmax(data) - np.nanmin(data) + 1e-8)
            elif method == 'zscore':
                if isinstance(data, pd.DataFrame):
                    return (data - data.mean()) / (data.std() + 1e-8)
                else:
                    return (data - np.nanmean(data)) / (np.nanstd(data) + 1e-8)
            else:
                raise ValueError(f"不支持的归一化方法: {method}")

class FeatureEngineer:
    """
    特征工程基类
    负责从原始行情数据计算技术指标和其他特征
    """

    def __init__(self, feature_config=None, data_dictionary_path='data_dictionary.json'):
        """
        初始化特征工程器

        参数:
            feature_config (dict): 特征配置字典，指定要计算的技术指标及其参数
            data_dictionary_path (str): 数据字典文件路径
        """
        self.logger = logging.getLogger('drl_trading')

        # 默认特征配置
        default_config = {
            # 基础价格特征
            'price_features': {'use': True},

            # 移动平均线
            'sma': {'use': True, 'periods': [5, 10, 20, 60, 120]},
            'ema': {'use': True, 'periods': [5, 10, 20, 60]},

            # 动量指标
            'rsi': {'use': True, 'periods': [6, 14, 21]},
            'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9},

            # 波动率指标
            'bbands': {'use': True, 'period': 20, 'std': 2.0},
            'atr': {'use': True, 'periods': [7, 14, 21]},

            # 成交量指标
            'volume': {'use': True, 'periods': [5, 10, 20]},

            # 统计特征
            'rolling_stats': {'use': True, 'windows': [5, 10, 20, 60]},

            # 高级特征
            'advanced_features': {'use': True},

            # 时间特征
            'time_features': {'use': True},

            # 特征选择
            'feature_selection': {'use': True, 'method': 'mutual_info', 'top_n': 30},

            # 特征归一化
            'normalization': {'use': True, 'method': 'minmax'}
        }

        # 如果提供了配置，则更新默认配置
        if feature_config:
            for category, config in feature_config.items():
                if category in default_config:
                    default_config[category].update(config)
                else:
                    default_config[category] = config

        self.feature_config = default_config

        # 保存生成的特征名称，用于特征选择
        self.feature_names = []

        # 初始化数据字典路径
        self.data_dictionary_path = data_dictionary_path

    def generate_features(self, data):
        """
        生成特征

        参数:
            data (pandas.DataFrame): 原始行情数据，必须包含OHLCV列
        返回:
            pandas.DataFrame: 添加了特征的数据
        """
        try:
            # 复制数据，避免修改原始数据
            df = data.copy()

            # 确保列名标准化
            df = self._ensure_column_names(df)

            # 重置特征名称列表
            self.feature_names = []

            # 计算基本价格特征
            if self.feature_config.get('price_features', {}).get('use', True):
                df = self._calculate_price_features(df)

            # 根据配置计算技术指标
            df = self._calculate_technical_indicators(df)

            # 计算统计特征
            if self.feature_config.get('rolling_stats', {}).get('use', True):
                df = self._calculate_statistical_features(df)

            # 计算高级特征
            if self.feature_config.get('advanced_features', {}).get('use', True):
                df = self._calculate_advanced_features(df)

            # 计算时间特征
            if self.feature_config.get('time_features', {}).get('use', True):
                df = self._calculate_time_features(df)

            # 特征选择
            if self.feature_config.get('feature_selection', {}).get('use', True):
                df = self._select_features(df)

            # 归一化特征
            if self.feature_config.get('normalization', {}).get('use', True):
                df = self._normalize_features(df)

            # 处理NaN值
            df = self._handle_missing_values(df)

            self.logger.info(f"特征生成完成，共 {len(df.columns)} 个特征")
            return df

        except Exception as e:
            self.logger.error(f"特征生成失败: {str(e)}")
            # 返回原始数据，确保流程不中断
            return data

    def _ensure_column_names(self, df):
        """
        确保列名标准化
        参数:
            df (pandas.DataFrame): 原始数据

        返回:
            pandas.DataFrame: 标准化列名后的数据
        """
        # 英文列名到中文列名的映射
        column_mapping = {
            'open': '开盘',
            'high': '最高',
            'low': '最低',
            'close': '收盘',
            'volume': '成交量'
        }

        # 检查并重命名列
        for eng, chn in column_mapping.items():
            if eng in df.columns and chn not in df.columns:
                df[chn] = df[eng]

        # 确保必要的列存在
        required_columns = ['开盘', '最高', '最低', '收盘', '成交量']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            self.logger.error(f"数据缺少必要的列: {missing_columns}")
            raise ValueError(f"数据缺少必要的列: {missing_columns}")

        return df

    def _calculate_price_features(self, df):
        """
        计算基本价格特征 - 将在子类中实现
        """
        return df

    def _calculate_technical_indicators(self, df):
        """
        计算技术指标 - 将在子类中实现
        """
        return df

    def _calculate_statistical_features(self, df):
        """
        计算统计特征 - 将在子类中实现
        """
        return df

    def _calculate_advanced_features(self, df):
        """
        计算高级特征 - 将在子类中实现
        """
        return df

    def _calculate_time_features(self, df):
        """
        计算时间特征 - 将在子类中实现
        """
        return df

    def _select_features(self, df):
        """
        特征选择 - 将在子类中实现
        """
        return df

    def _normalize_features(self, df):
        """
        归一化特征 - 将在子类中实现
        """
        return df

    def _handle_missing_values(self, df):
        """
        处理缺失值

        参数:
            df (pandas.DataFrame): 包含特征的数据框

        返回:
            pandas.DataFrame: 处理后的数据框
        """
        # 检查是否有NaN值
        nan_count = df.isna().sum().sum()
        if nan_count > 0:
            self.logger.warning(f"特征中存在 {nan_count} 个NaN值，使用前向填充和后向填充处理")
            # 使用前向填充和后向填充处理NaN值
            df = df.fillna(method='ffill').fillna(method='bfill')

            # 如果仍有NaN值（例如整列都是NaN），用0填充
            remaining_nan = df.isna().sum().sum()
            if remaining_nan > 0:
                self.logger.warning(f"前向和后向填充后仍有 {remaining_nan} 个NaN值，用0填充")
                df = df.fillna(0)

        # 检查是否有无穷大值
        inf_count = np.isinf(df.values).sum()
        if inf_count > 0:
            self.logger.warning(f"发现 {inf_count} 个无穷大值，替换为0")
            df = df.replace([np.inf, -np.inf], 0)

        return df
