"""
模型评估模块
实现在不同市场状态下的模型评估和鲁棒性测试
"""

import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Optional, Tuple, Union, Any, Callable
from ..market_analysis import MarketCondition, MarketConditionDetector
from ..validation import OverfittingDetector
from ..trading_env import RobustTradingEnvironment
import warnings

# 忽略特定警告
warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", category=RuntimeWarning)

class ModelEvaluator:
    """
    模型评估类
    实现在不同市场状态下的模型评估和鲁棒性测试
    """

    def __init__(self, logger=None):
        """
        初始化模型评估器

        参数:
            logger (logging.Logger): 日志记录器
        """
        self.logger = logger or logging.getLogger('drl_trading')
        self.market_detector = MarketConditionDetector(logger=self.logger)
        self.overfitting_detector = OverfittingDetector(logger=self.logger)
        self.evaluation_results = {}

    def evaluate_model_in_market_conditions(self, 
                                           model, 
                                           data: pd.DataFrame, 
                                           initial_capital: float = 100000, 
                                           commission_rate: float = 0.0003, 
                                           window_size: int = 20, 
                                           min_condition_samples: int = 60) -> Dict:
        """
        在不同市场状态下评估模型

        参数:
            model: 训练好的模型
            data (pandas.DataFrame): 测试数据
            initial_capital (float): 初始资金
            commission_rate (float): 手续费率
            window_size (int): 观测窗口大小
            min_condition_samples (int): 每种市场状态的最小样本数

        返回:
            dict: 评估结果
        """
        if data is None or data.empty:
            self.logger.error("测试数据为空")
            return {}

        # 检测数据中的市场状态
        market_conditions = self._detect_market_conditions(data, window_size)
        
        # 初始化结果字典
        results = {
            'overall': {},
            'market_conditions': {},
            'robustness_score': 0.0
        }
        
        # 在整个数据集上评估模型
        overall_result = self._evaluate_model(
            model=model,
            data=data,
            initial_capital=initial_capital,
            commission_rate=commission_rate,
            window_size=window_size
        )
        results['overall'] = overall_result
        
        # 在每种市场状态下评估模型
        for condition, condition_data in market_conditions.items():
            if len(condition_data) >= min_condition_samples:
                condition_result = self._evaluate_model(
                    model=model,
                    data=condition_data,
                    initial_capital=initial_capital,
                    commission_rate=commission_rate,
                    window_size=window_size
                )
                results['market_conditions'][condition.name] = condition_result
                self.logger.info(f"市场状态 {condition.name} 评估完成，样本数: {len(condition_data)}")
            else:
                self.logger.warning(f"市场状态 {condition.name} 样本数不足，跳过评估")
        
        # 计算鲁棒性得分
        robustness_score = self._calculate_robustness_score(results)
        results['robustness_score'] = robustness_score
        
        # 保存评估结果
        self.evaluation_results = results
        
        return results

    def plot_performance_by_market_condition(self, figsize=(15, 10)) -> plt.Figure:
        """
        绘制不同市场状态下的性能对比图

        参数:
            figsize (tuple): 图表大小

        返回:
            matplotlib.figure.Figure: 图表对象
        """
        if not self.evaluation_results:
            self.logger.error("没有评估结果可供绘图")
            return None

        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=figsize)
        axes = axes.flatten()
        
        # 提取数据
        conditions = list(self.evaluation_results['market_conditions'].keys())
        conditions.append('overall')
        
        # 准备绘图数据
        returns = []
        sharpe_ratios = []
        max_drawdowns = []
        win_rates = []
        
        for condition in conditions:
            if condition == 'overall':
                result = self.evaluation_results['overall']
            else:
                result = self.evaluation_results['market_conditions'].get(condition)
            
            if result:
                returns.append(result.get('annualized_return', 0) * 100)  # 转换为百分比
                sharpe_ratios.append(result.get('sharpe_ratio', 0))
                max_drawdowns.append(result.get('max_drawdown', 0) * 100)  # 转换为百分比
                win_rates.append(result.get('win_rate', 0) * 100)  # 转换为百分比
            else:
                returns.append(0)
                sharpe_ratios.append(0)
                max_drawdowns.append(0)
                win_rates.append(0)
        
        # 绘制年化收益率
        sns.barplot(x=conditions, y=returns, ax=axes[0])
        axes[0].set_title('年化收益率 (%)')
        axes[0].set_xticklabels(axes[0].get_xticklabels(), rotation=45)
        
        # 绘制夏普比率
        sns.barplot(x=conditions, y=sharpe_ratios, ax=axes[1])
        axes[1].set_title('夏普比率')
        axes[1].set_xticklabels(axes[1].get_xticklabels(), rotation=45)
        
        # 绘制最大回撤
        sns.barplot(x=conditions, y=max_drawdowns, ax=axes[2])
        axes[2].set_title('最大回撤 (%)')
        axes[2].set_xticklabels(axes[2].get_xticklabels(), rotation=45)
        
        # 绘制胜率
        sns.barplot(x=conditions, y=win_rates, ax=axes[3])
        axes[3].set_title('胜率 (%)')
        axes[3].set_xticklabels(axes[3].get_xticklabels(), rotation=45)
        
        # 调整布局
        plt.tight_layout()
        
        return fig

    def get_robustness_report(self) -> Dict:
        """
        获取模型鲁棒性报告

        返回:
            dict: 鲁棒性报告
        """
        if not self.evaluation_results:
            self.logger.error("没有评估结果可供生成报告")
            return {}

        # 初始化报告
        report = {
            'robustness_score': self.evaluation_results.get('robustness_score', 0),
            'market_condition_performance': {},
            'weaknesses': [],
            'strengths': [],
            'recommendations': []
        }
        
        # 分析不同市场状态下的表现
        overall_return = self.evaluation_results['overall'].get('annualized_return', 0)
        overall_sharpe = self.evaluation_results['overall'].get('sharpe_ratio', 0)
        overall_drawdown = self.evaluation_results['overall'].get('max_drawdown', 0)
        
        for condition, result in self.evaluation_results['market_conditions'].items():
            condition_return = result.get('annualized_return', 0)
            condition_sharpe = result.get('sharpe_ratio', 0)
            condition_drawdown = result.get('max_drawdown', 0)
            
            # 计算相对表现
            relative_return = condition_return / overall_return if overall_return != 0 else 0
            relative_sharpe = condition_sharpe / overall_sharpe if overall_sharpe != 0 else 0
            relative_drawdown = condition_drawdown / overall_drawdown if overall_drawdown != 0 else 0
            
            # 评估表现
            if relative_return >= 1.2:
                performance = "优秀"
            elif relative_return >= 0.8:
                performance = "良好"
            elif relative_return >= 0.5:
                performance = "一般"
            else:
                performance = "较差"
            
            # 记录表现
            report['market_condition_performance'][condition] = {
                'performance': performance,
                'relative_return': relative_return,
                'relative_sharpe': relative_sharpe,
                'relative_drawdown': relative_drawdown
            }
            
            # 识别优势和劣势
            if performance in ["优秀", "良好"]:
                report['strengths'].append(f"在{condition}市场中表现{performance}")
            elif performance in ["一般", "较差"]:
                report['weaknesses'].append(f"在{condition}市场中表现{performance}")
        
        # 生成建议
        if report['robustness_score'] < 0.6:
            report['recommendations'].append("模型鲁棒性较低，建议在更多样化的市场条件下进行训练")
        
        for weakness in report['weaknesses']:
            if "熊市" in weakness or "危机" in weakness:
                report['recommendations'].append("改进风险管理策略，特别是在熊市和危机市场中")
            elif "震荡" in weakness:
                report['recommendations'].append("优化震荡市场中的交易策略，考虑使用均值回归策略")
            elif "牛市" in weakness:
                report['recommendations'].append("优化牛市中的策略，考虑增加持仓时间或仓位大小")
        
        return report

    def _detect_market_conditions(self, data: pd.DataFrame, window_size: int) -> Dict[MarketCondition, pd.DataFrame]:
        """
        检测数据中的不同市场状态

        参数:
            data (pandas.DataFrame): 数据
            window_size (int): 窗口大小

        返回:
            dict: 不同市场状态的数据子集
        """
        # 初始化结果字典
        market_conditions = {condition: pd.DataFrame() for condition in MarketCondition}
        
        # 使用滑动窗口检测市场状态
        for i in range(window_size, len(data), window_size):
            end_idx = min(i + window_size, len(data))
            window_data = data.iloc[i-window_size:end_idx]
            
            if len(window_data) >= window_size:
                result = self.market_detector.detect_market_condition(window_data, window_size)
                condition = result['primary_condition']
                
                # 将窗口数据添加到对应的市场状态
                if market_conditions[condition].empty:
                    market_conditions[condition] = window_data
                else:
                    market_conditions[condition] = pd.concat([market_conditions[condition], window_data])
        
        return market_conditions

    def _evaluate_model(self, 
                       model, 
                       data: pd.DataFrame, 
                       initial_capital: float, 
                       commission_rate: float, 
                       window_size: int) -> Dict:
        """
        评估模型

        参数:
            model: 训练好的模型
            data (pandas.DataFrame): 测试数据
            initial_capital (float): 初始资金
            commission_rate (float): 手续费率
            window_size (int): 观测窗口大小

        返回:
            dict: 评估结果
        """
        # 创建鲁棒交易环境
        env = RobustTradingEnvironment(
            df_processed_data=data,
            initial_capital=initial_capital,
            commission_rate=commission_rate,
            window_size=window_size,
            market_regime_aware=True,
            adaptive_reward=True
        )
        
        # 重置环境
        observation, _ = env.reset()
        
        # 初始化结果
        done = False
        truncated = False
        portfolio_values = []
        returns = []
        actions = []
        
        # 运行模型
        while not done and not truncated:
            # 获取模型预测的动作
            action = model.predict(observation)[0]
            
            # 执行动作
            observation, reward, done, truncated, info = env.step(action)
            
            # 记录结果
            portfolio_values.append(info['portfolio_value'])
            if len(portfolio_values) > 1:
                returns.append(portfolio_values[-1] / portfolio_values[-2] - 1)
            actions.append(action)
        
        # 计算评估指标
        result = self._calculate_performance_metrics(
            portfolio_values=portfolio_values,
            returns=returns,
            actions=actions,
            initial_capital=initial_capital,
            trades=env.action_handler.get_trades()
        )
        
        return result

    def _calculate_performance_metrics(self, 
                                      portfolio_values: List[float], 
                                      returns: List[float], 
                                      actions: List[int], 
                                      initial_capital: float, 
                                      trades: List[Dict]) -> Dict:
        """
        计算性能指标

        参数:
            portfolio_values (list): 组合价值列表
            returns (list): 收益率列表
            actions (list): 动作列表
            initial_capital (float): 初始资金
            trades (list): 交易记录列表

        返回:
            dict: 性能指标
        """
        if not portfolio_values or len(portfolio_values) < 2:
            return {}
        
        # 计算总收益率
        total_return = (portfolio_values[-1] / initial_capital) - 1
        
        # 计算年化收益率（假设252个交易日）
        n_days = len(portfolio_values)
        annualized_return = (1 + total_return) ** (252 / n_days) - 1
        
        # 计算波动率
        if returns:
            volatility = np.std(returns) * np.sqrt(252)
        else:
            volatility = 0
        
        # 计算夏普比率
        risk_free_rate = 0.02  # 假设无风险利率为2%
        if volatility > 0:
            sharpe_ratio = (annualized_return - risk_free_rate) / volatility
        else:
            sharpe_ratio = 0
        
        # 计算最大回撤
        max_drawdown = 0
        peak = portfolio_values[0]
        for value in portfolio_values:
            if value > peak:
                peak = value
            drawdown = (peak - value) / peak
            max_drawdown = max(max_drawdown, drawdown)
        
        # 计算卡玛比率
        if max_drawdown > 0:
            calmar_ratio = annualized_return / max_drawdown
        else:
            calmar_ratio = 0
        
        # 计算胜率
        win_trades = 0
        total_trades = len(trades) // 2  # 买入和卖出算一笔交易
        for i in range(0, len(trades), 2):
            if i + 1 < len(trades):
                buy_price = trades[i]['price'] if trades[i]['action'] == 'buy' else trades[i + 1]['price']
                sell_price = trades[i + 1]['price'] if trades[i + 1]['action'] == 'sell' else trades[i]['price']
                
                if sell_price > buy_price:
                    win_trades += 1
        
        win_rate = win_trades / total_trades if total_trades > 0 else 0
        
        # 计算平均收益率
        avg_return = np.mean(returns) if returns else 0
        
        # 计算索提诺比率
        negative_returns = [r for r in returns if r < 0]
        downside_deviation = np.std(negative_returns) * np.sqrt(252) if negative_returns else 0
        sortino_ratio = (annualized_return - risk_free_rate) / downside_deviation if downside_deviation > 0 else 0
        
        # 汇总结果
        result = {
            'total_return': total_return,
            'annualized_return': annualized_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'calmar_ratio': calmar_ratio,
            'win_rate': win_rate,
            'total_trades': total_trades,
            'avg_return': avg_return,
            'sortino_ratio': sortino_ratio
        }
        
        return result

    def _calculate_robustness_score(self, results: Dict) -> float:
        """
        计算鲁棒性得分

        参数:
            results (dict): 评估结果

        返回:
            float: 鲁棒性得分
        """
        if not results or 'overall' not in results or 'market_conditions' not in results:
            return 0.0
        
        # 获取整体表现
        overall_return = results['overall'].get('annualized_return', 0)
        overall_sharpe = results['overall'].get('sharpe_ratio', 0)
        overall_drawdown = results['overall'].get('max_drawdown', 0)
        
        if overall_return == 0 or overall_sharpe == 0:
            return 0.0
        
        # 计算各市场状态下表现的标准差
        returns = []
        sharpes = []
        drawdowns = []
        
        for condition, result in results['market_conditions'].items():
            returns.append(result.get('annualized_return', 0))
            sharpes.append(result.get('sharpe_ratio', 0))
            drawdowns.append(result.get('max_drawdown', 0))
        
        # 计算变异系数（标准差/均值）
        return_cv = np.std(returns) / np.mean(returns) if np.mean(returns) != 0 else float('inf')
        sharpe_cv = np.std(sharpes) / np.mean(sharpes) if np.mean(sharpes) != 0 else float('inf')
        
        # 计算鲁棒性得分（1 - 归一化的变异系数）
        return_robustness = 1 / (1 + return_cv)
        sharpe_robustness = 1 / (1 + sharpe_cv)
        
        # 综合得分
        robustness_score = 0.6 * return_robustness + 0.4 * sharpe_robustness
        
        # 归一化到0-1范围
        robustness_score = max(0, min(1, robustness_score))
        
        return robustness_score
