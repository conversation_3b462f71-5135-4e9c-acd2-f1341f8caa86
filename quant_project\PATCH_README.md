# Python 3.13 + PyTorch + Streamlit 兼容性补丁

## 问题描述

在 Python 3.13 环境下运行 PyTorch 2.7.0 和 Streamlit 1.45.0 时，可能会遇到以下错误：

```
RuntimeError: no running event loop

RuntimeError: Tried to instantiate class '__path__._path', but it does not exist! Ensure that it is registered via torch::class_
```

这是由于 Python 3.13 中 asyncio 的变化以及 PyTorch 的自定义类注册机制与 Streamlit 的文件监视器不兼容导致的。

## 解决方案

我们提供了以下文件来解决这个问题：

1. `fix_asyncio_torch.py` - 包含修复 asyncio 和 PyTorch 兼容性问题的补丁
2. `run_with_patch.py` - 应用补丁并启动应用的脚本
3. `run_app.bat` - Windows 用户的便捷启动脚本

## 使用方法

### 方法 1：使用批处理文件（推荐，仅限 Windows）

双击 `run_app.bat` 文件即可启动应用。

### 方法 2：使用 Python 脚本

```bash
# 激活虚拟环境（如果有）
source test_env/bin/activate  # Linux/Mac
# 或
test_env\Scripts\activate.bat  # Windows

# 运行带补丁的应用
python run_with_patch.py
```

### 方法 3：直接运行 Streamlit 应用

```bash
# 激活虚拟环境（如果有）
source test_env/bin/activate  # Linux/Mac
# 或
test_env\Scripts\activate.bat  # Windows

# 运行应用
streamlit run main_app.py
```

注意：方法 3 依赖于我们已经在 `main_app.py` 中添加的补丁导入代码。

## 补丁说明

### 1. asyncio 事件循环补丁

修复 Python 3.13 中 `asyncio.get_running_loop()` 的行为变化，在没有运行中的事件循环时自动创建一个新的循环。

### 2. PyTorch 自定义类补丁

修复 PyTorch 的 `_classes` 模块中 `__path__._path` 相关的错误，通过拦截特定的属性访问并返回空列表而不是引发错误。

### 3. Streamlit 文件监视器补丁

修改 Streamlit 的 `local_sources_watcher` 模块，使其在处理 PyTorch 模块时不会尝试提取路径信息，从而避免错误。

## 兼容性

此补丁已在以下环境中测试：

- Python 3.13.2
- PyTorch 2.7.0+cu118
- Streamlit 1.45.0
- Windows 10/11

## 故障排除

如果仍然遇到问题，请尝试以下步骤：

1. 确保已安装所有必要的依赖：
   ```bash
   pip install -r requirements.txt
   ```

2. 检查 PyTorch 是否正确安装并支持 GPU：
   ```python
   import torch
   print(torch.__version__)
   print(f"CUDA available: {torch.cuda.is_available()}")
   ```

3. 尝试降级到 Python 3.11 或 3.12 版本，这些版本与 PyTorch 和 Streamlit 的兼容性更好。
