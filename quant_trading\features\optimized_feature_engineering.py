#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
优化版特征工程模块
专注于提高特征计算的性能
"""

import numpy as np
import pandas as pd
import talib
import logging
import time
import concurrent.futures
from functools import lru_cache
from typing import List, Dict, Any, Union, Tuple, Optional

class OptimizedFeatureEngineering:
    """
    优化版特征工程类
    专注于提高特征计算的性能
    """

    def __init__(self, parallel_processing=True, max_workers=4, use_cache=True):
        """
        初始化优化版特征工程器

        参数:
            parallel_processing (bool): 是否使用并行处理
            max_workers (int): 并行处理的最大工作线程数
            use_cache (bool): 是否使用缓存
        """
        self.logger = logging.getLogger('drl_trading')
        self.parallel_processing = parallel_processing
        self.max_workers = max_workers
        self.use_cache = use_cache

        # 特征计算缓存
        self.feature_cache = {}

        # 特征组
        self.feature_groups = {
            'price': ['开盘', '最高', '最低', '收盘'],
            'volume': ['成交量'],
            'basic': ['开盘', '最高', '最低', '收盘', '成交量'],
            'technical': ['RSI', 'MACD', 'MACD_Signal', 'MACD_Hist', 'MA5', 'MA10', 'MA20', 'MA60', 'BOLL_Upper', 'BOLL_Middle', 'BOLL_Lower'],
            'momentum': ['RSI', 'ROC', 'MOM', 'CCI'],
            'trend': ['MA5', 'MA10', 'MA20', 'MA60', 'ADX', 'PLUS_DI', 'MINUS_DI'],
            'volatility': ['ATR', 'NATR', 'BOLL_Upper', 'BOLL_Middle', 'BOLL_Lower', 'STD20'],
            'volume_indicators': ['OBV', 'AD', 'ADOSC'],
            'all': []  # 将在初始化时填充
        }

        # 填充'all'特征组
        all_features = set()
        for group, features in self.feature_groups.items():
            if group != 'all':
                all_features.update(features)
        self.feature_groups['all'] = list(all_features)

    def calculate_features(self, data: pd.DataFrame, feature_list: Union[List[str], str] = 'basic') -> pd.DataFrame:
        """
        计算特征

        参数:
            data (pandas.DataFrame): 原始数据，必须包含OHLCV列
            feature_list (list or str): 要计算的特征列表或特征组名称

        返回:
            pandas.DataFrame: 包含计算特征的数据框
        """
        start_time = time.time()

        # 检查数据
        if data is None or data.empty:
            self.logger.warning("输入数据为空，无法计算特征")
            return data

        # 确保数据有必要的列
        required_columns = ['开盘', '最高', '最低', '收盘', '成交量']
        missing_columns = [col for col in required_columns if col not in data.columns]
        if missing_columns:
            self.logger.warning(f"数据缺少必要的列: {missing_columns}")
            return data

        # 复制数据，避免修改原始数据
        df = data.copy()

        # 解析特征列表
        if isinstance(feature_list, str):
            if feature_list in self.feature_groups:
                features_to_calculate = self.feature_groups[feature_list]
            else:
                self.logger.warning(f"未知的特征组: {feature_list}，使用'basic'特征组")
                features_to_calculate = self.feature_groups['basic']
        else:
            features_to_calculate = feature_list

        # 缓存键
        cache_key = self._generate_cache_key(df, features_to_calculate)

        # 检查缓存
        if self.use_cache and cache_key in self.feature_cache:
            self.logger.info(f"从缓存加载特征，耗时: {time.time() - start_time:.4f}秒")
            return self.feature_cache[cache_key]

        # 并行计算特征
        if self.parallel_processing and len(features_to_calculate) > 1:
            df = self._parallel_calculate_features(df, features_to_calculate)
        else:
            # 串行计算特征
            for feature in features_to_calculate:
                if feature not in df.columns:  # 避免重复计算
                    try:
                        df = self._calculate_single_feature(df, feature)
                    except Exception as e:
                        self.logger.error(f"计算特征 {feature} 时出错: {str(e)}")

        # 缓存结果
        if self.use_cache:
            self.feature_cache[cache_key] = df

        self.logger.info(f"特征计算完成，共 {len(features_to_calculate)} 个特征，耗时: {time.time() - start_time:.4f}秒")
        return df

    def _parallel_calculate_features(self, df: pd.DataFrame, features: List[str]) -> pd.DataFrame:
        """
        并行计算特征

        参数:
            df (pandas.DataFrame): 原始数据
            features (list): 要计算的特征列表

        返回:
            pandas.DataFrame: 包含计算特征的数据框
        """
        # 过滤掉已经存在的特征
        features_to_calculate = [f for f in features if f not in df.columns]

        if not features_to_calculate:
            return df

        # 创建一个副本，避免并发修改问题
        result_df = df.copy()

        # 使用线程池并行计算特征
        with concurrent.futures.ThreadPoolExecutor(max_workers=min(self.max_workers, len(features_to_calculate))) as executor:
            # 提交任务
            future_to_feature = {executor.submit(self._calculate_single_feature_safe, df, feature): feature for feature in features_to_calculate}

            # 获取结果
            for future in concurrent.futures.as_completed(future_to_feature):
                feature = future_to_feature[future]
                try:
                    feature_df = future.result()
                    if feature_df is not None and feature in feature_df.columns:
                        result_df[feature] = feature_df[feature]
                except Exception as e:
                    self.logger.error(f"并行计算特征 {feature} 时出错: {str(e)}")

        return result_df

    def _calculate_single_feature_safe(self, df: pd.DataFrame, feature: str) -> Optional[pd.DataFrame]:
        """
        安全地计算单个特征（用于并行处理）

        参数:
            df (pandas.DataFrame): 原始数据
            feature (str): 要计算的特征

        返回:
            pandas.DataFrame or None: 包含计算特征的数据框，如果出错则返回None
        """
        try:
            return self._calculate_single_feature(df, feature)
        except Exception as e:
            self.logger.error(f"计算特征 {feature} 时出错: {str(e)}")
            return None

    def _calculate_single_feature(self, df: pd.DataFrame, feature: str) -> pd.DataFrame:
        """
        计算单个特征

        参数:
            df (pandas.DataFrame): 原始数据
            feature (str): 要计算的特征

        返回:
            pandas.DataFrame: 包含计算特征的数据框
        """
        # 复制数据，避免修改原始数据
        result_df = df.copy()

        # 提取OHLCV数据
        open_price = df['开盘'].values
        high_price = df['最高'].values
        low_price = df['最低'].values
        close_price = df['收盘'].values
        volume = df['成交量'].values if '成交量' in df.columns else None

        # 根据特征名称计算不同的特征
        if feature == 'RSI':
            # 相对强弱指标
            result_df['RSI'] = talib.RSI(close_price, timeperiod=14)
        elif feature == 'MACD':
            # 移动平均收敛/发散指标
            macd, signal, hist = talib.MACD(close_price, fastperiod=12, slowperiod=26, signalperiod=9)
            result_df['MACD'] = macd
            result_df['MACD_Signal'] = signal
            result_df['MACD_Hist'] = hist
        elif feature == 'MACD_Signal':
            # 如果已经计算过MACD，则跳过
            if 'MACD_Signal' not in result_df.columns:
                macd, signal, hist = talib.MACD(close_price, fastperiod=12, slowperiod=26, signalperiod=9)
                result_df['MACD'] = macd
                result_df['MACD_Signal'] = signal
                result_df['MACD_Hist'] = hist
        elif feature == 'MACD_Hist':
            # 如果已经计算过MACD，则跳过
            if 'MACD_Hist' not in result_df.columns:
                macd, signal, hist = talib.MACD(close_price, fastperiod=12, slowperiod=26, signalperiod=9)
                result_df['MACD'] = macd
                result_df['MACD_Signal'] = signal
                result_df['MACD_Hist'] = hist
        elif feature == 'MA5':
            # 5日移动平均线
            result_df['MA5'] = talib.SMA(close_price, timeperiod=5)
        elif feature == 'MA10':
            # 10日移动平均线
            result_df['MA10'] = talib.SMA(close_price, timeperiod=10)
        elif feature == 'MA20':
            # 20日移动平均线
            result_df['MA20'] = talib.SMA(close_price, timeperiod=20)
        elif feature == 'MA60':
            # 60日移动平均线
            result_df['MA60'] = talib.SMA(close_price, timeperiod=60)
        elif feature.startswith('BOLL_'):
            # 布林带
            upper, middle, lower = talib.BBANDS(close_price, timeperiod=20, nbdevup=2, nbdevdn=2, matype=0)
            result_df['BOLL_Upper'] = upper
            result_df['BOLL_Middle'] = middle
            result_df['BOLL_Lower'] = lower
        elif feature == 'ROC':
            # 变动率指标
            result_df['ROC'] = talib.ROC(close_price, timeperiod=10)
        elif feature == 'MOM':
            # 动量指标
            result_df['MOM'] = talib.MOM(close_price, timeperiod=10)
        elif feature == 'CCI':
            # 顺势指标
            result_df['CCI'] = talib.CCI(high_price, low_price, close_price, timeperiod=14)
        elif feature == 'ADX':
            # 平均趋向指数
            result_df['ADX'] = talib.ADX(high_price, low_price, close_price, timeperiod=14)
        elif feature == 'PLUS_DI':
            # 正向动向指标
            result_df['PLUS_DI'] = talib.PLUS_DI(high_price, low_price, close_price, timeperiod=14)
        elif feature == 'MINUS_DI':
            # 负向动向指标
            result_df['MINUS_DI'] = talib.MINUS_DI(high_price, low_price, close_price, timeperiod=14)
        elif feature == 'ATR':
            # 真实波动幅度均值
            result_df['ATR'] = talib.ATR(high_price, low_price, close_price, timeperiod=14)
        elif feature == 'NATR':
            # 归一化真实波动幅度均值
            result_df['NATR'] = talib.NATR(high_price, low_price, close_price, timeperiod=14)
        elif feature == 'STD20':
            # 20日标准差
            result_df['STD20'] = talib.STDDEV(close_price, timeperiod=20, nbdev=1)
        elif feature == 'OBV':
            # 能量潮指标
            if volume is not None:
                result_df['OBV'] = talib.OBV(close_price, volume)
        elif feature == 'AD':
            # 累积/派发线
            if volume is not None:
                result_df['AD'] = talib.AD(high_price, low_price, close_price, volume)
        elif feature == 'ADOSC':
            # 震荡指标
            if volume is not None:
                result_df['ADOSC'] = talib.ADOSC(high_price, low_price, close_price, volume, fastperiod=3, slowperiod=10)
        else:
            self.logger.warning(f"未知的特征: {feature}")

        return result_df

    def _generate_cache_key(self, df: pd.DataFrame, features: List[str]) -> str:
        """
        生成特征缓存键

        参数:
            df (pandas.DataFrame): 数据框
            features (list): 特征列表

        返回:
            str: 缓存键
        """
        # 使用数据的形状和最后一行的值作为缓存键的一部分
        data_shape = df.shape
        last_row = df.iloc[-1].values if not df.empty else []

        # 使用特征列表作为缓存键的另一部分
        features_str = ','.join(sorted(features))

        # 组合成缓存键
        cache_key = f"{data_shape}_{last_row.tobytes()}_{features_str}"

        return str(hash(cache_key))

    def calculate_returns(self, df: pd.DataFrame, periods: List[int] = [1, 5, 10, 20]) -> pd.DataFrame:
        """
        计算不同周期的收益率

        参数:
            df (pandas.DataFrame): 原始数据，必须包含'收盘'列
            periods (list): 周期列表，默认为[1, 5, 10, 20]

        返回:
            pandas.DataFrame: 包含收益率的数据框
        """
        if '收盘' not in df.columns:
            self.logger.warning("数据缺少'收盘'列，无法计算收益率")
            return df

        # 复制数据，避免修改原始数据
        result_df = df.copy()

        # 计算不同周期的收益率
        for period in periods:
            column_name = f'收益率_{period}日'
            result_df[column_name] = result_df['收盘'].pct_change(period)

        return result_df

    def calculate_volatility(self, df: pd.DataFrame, windows: List[int] = [5, 10, 20, 60]) -> pd.DataFrame:
        """
        计算不同窗口的波动率

        参数:
            df (pandas.DataFrame): 原始数据，必须包含'收盘'列
            windows (list): 窗口列表，默认为[5, 10, 20, 60]

        返回:
            pandas.DataFrame: 包含波动率的数据框
        """
        if '收盘' not in df.columns:
            self.logger.warning("数据缺少'收盘'列，无法计算波动率")
            return df

        # 复制数据，避免修改原始数据
        result_df = df.copy()

        # 计算日收益率
        daily_returns = result_df['收盘'].pct_change()

        # 计算不同窗口的波动率（标准差）
        for window in windows:
            column_name = f'波动率_{window}日'
            result_df[column_name] = daily_returns.rolling(window=window).std() * np.sqrt(252)  # 年化

        return result_df

    def calculate_price_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算价格相关特征

        参数:
            df (pandas.DataFrame): 原始数据，必须包含OHLC列

        返回:
            pandas.DataFrame: 包含价格特征的数据框
        """
        required_columns = ['开盘', '最高', '最低', '收盘']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            self.logger.warning(f"数据缺少必要的列: {missing_columns}")
            return df

        # 复制数据，避免修改原始数据
        result_df = df.copy()

        # 计算价格特征
        # 1. 当日振幅
        result_df['振幅'] = (result_df['最高'] - result_df['最低']) / result_df['收盘'].shift(1) * 100

        # 2. 当日涨跌幅
        result_df['涨跌幅'] = (result_df['收盘'] - result_df['收盘'].shift(1)) / result_df['收盘'].shift(1) * 100

        # 3. 当日收盘价相对开盘价变化
        result_df['开盘收盘比'] = (result_df['收盘'] - result_df['开盘']) / result_df['开盘'] * 100

        # 4. 价格位置指标（当日收盘价在当日最高最低价中的位置）
        result_df['价格位置'] = (result_df['收盘'] - result_df['最低']) / (result_df['最高'] - result_df['最低'])

        return result_df

    def calculate_volume_features(self, df: pd.DataFrame, windows: List[int] = [5, 10, 20]) -> pd.DataFrame:
        """
        计算成交量相关特征

        参数:
            df (pandas.DataFrame): 原始数据，必须包含'成交量'列
            windows (list): 窗口列表，默认为[5, 10, 20]

        返回:
            pandas.DataFrame: 包含成交量特征的数据框
        """
        if '成交量' not in df.columns:
            self.logger.warning("数据缺少'成交量'列，无法计算成交量特征")
            return df

        # 复制数据，避免修改原始数据
        result_df = df.copy()

        # 计算成交量特征
        # 1. 成交量变化率
        result_df['成交量变化率'] = result_df['成交量'].pct_change() * 100

        # 2. 不同窗口的成交量移动平均
        for window in windows:
            result_df[f'成交量MA{window}'] = result_df['成交量'].rolling(window=window).mean()

        # 3. 成交量相对于移动平均的比率
        for window in windows:
            result_df[f'成交量比率_{window}'] = result_df['成交量'] / result_df[f'成交量MA{window}']

        return result_df

    def calculate_all_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算所有特征

        参数:
            df (pandas.DataFrame): 原始数据，必须包含OHLCV列

        返回:
            pandas.DataFrame: 包含所有特征的数据框
        """
        # 计算技术指标
        result_df = self.calculate_features(df, 'all')

        # 计算收益率
        result_df = self.calculate_returns(result_df)

        # 计算波动率
        result_df = self.calculate_volatility(result_df)

        # 计算价格特征
        result_df = self.calculate_price_features(result_df)

        # 计算成交量特征
        result_df = self.calculate_volume_features(result_df)

        return result_df

    def normalize_features(self, df: pd.DataFrame, method: str = 'zscore', exclude_columns: List[str] = None) -> pd.DataFrame:
        """
        标准化特征

        参数:
            df (pandas.DataFrame): 原始数据
            method (str): 标准化方法，可选'zscore'(Z-score标准化),'minmax'(最小-最大标准化)
            exclude_columns (list): 排除的列，这些列不会被标准化

        返回:
            pandas.DataFrame: 标准化后的数据框
        """
        if df is None or df.empty:
            return df

        # 复制数据，避免修改原始数据
        result_df = df.copy()

        # 确定要标准化的列
        if exclude_columns is None:
            exclude_columns = []

        # 只标准化数值列
        numeric_columns = result_df.select_dtypes(include=['number']).columns
        columns_to_normalize = [col for col in numeric_columns if col not in exclude_columns]

        # 应用标准化
        if method == 'zscore':
            # Z-score标准化: (x - mean) / std
            for col in columns_to_normalize:
                mean = result_df[col].mean()
                std = result_df[col].std()
                if std != 0:  # 避免除以零
                    result_df[col] = (result_df[col] - mean) / std
        elif method == 'minmax':
            # 最小-最大标准化: (x - min) / (max - min)
            for col in columns_to_normalize:
                min_val = result_df[col].min()
                max_val = result_df[col].max()
                if max_val > min_val:  # 避免除以零
                    result_df[col] = (result_df[col] - min_val) / (max_val - min_val)
        else:
            self.logger.warning(f"未知的标准化方法: {method}，不进行标准化")

        return result_df

    def select_features(self, df: pd.DataFrame, feature_list: List[str], drop_na: bool = True) -> pd.DataFrame:
        """
        选择特征

        参数:
            df (pandas.DataFrame): 原始数据
            feature_list (list): 要选择的特征列表
            drop_na (bool): 是否删除包含NA的行

        返回:
            pandas.DataFrame: 选择特征后的数据框
        """
        if df is None or df.empty:
            return df

        # 检查特征是否存在
        missing_features = [f for f in feature_list if f not in df.columns]
        if missing_features:
            self.logger.warning(f"数据缺少以下特征: {missing_features}")
            # 只选择存在的特征
            feature_list = [f for f in feature_list if f in df.columns]

        # 选择特征
        result_df = df[feature_list].copy()

        # 删除包含NA的行
        if drop_na:
            result_df = result_df.dropna()

        return result_df

    def clear_cache(self):
        """清除特征计算缓存"""
        self.feature_cache.clear()
        self.logger.info("特征计算缓存已清除")