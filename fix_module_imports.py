#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
修复模块导入问题的脚本
"""

import os
import sys
import logging
import importlib
import traceback

# 设置日志
os.makedirs('logs', exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join('logs', 'fix_module_imports.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('fix_module_imports')

def check_module_imports():
    """检查模块导入"""
    logger.info("检查模块导入")
    
    # 添加项目根目录到Python路径
    project_root = os.path.abspath('.')
    quant_project_path = os.path.join(project_root, 'quant_project')
    
    logger.info(f"项目根目录: {project_root}")
    logger.info(f"quant_project路径: {quant_project_path}")
    
    if project_root not in sys.path:
        sys.path.append(project_root)
        logger.info(f"已添加项目根目录到Python路径: {project_root}")
    
    if quant_project_path not in sys.path:
        sys.path.append(quant_project_path)
        logger.info(f"已添加quant_project路径到Python路径: {quant_project_path}")
    
    # 检查Python路径
    logger.info("Python路径:")
    for path in sys.path:
        logger.info(f"  {path}")
    
    # 检查核心模块
    modules_to_check = [
        ('quant_project.core_logic.data_handler', 'DataHandler'),
        ('quant_project.core_logic.feature_engineer', 'FeatureEngineer'),
        ('quant_project.core_logic.trading_environment', 'TradingEnvironment'),
        ('quant_project.core_logic.drl_agent', 'DRLAgent'),
        ('quant_project.core_logic.performance_analyzer', 'PerformanceAnalyzer'),
        ('core_logic.data_handler', 'DataHandler'),
        ('core_logic.feature_engineer', 'FeatureEngineer'),
        ('core_logic.trading_environment', 'TradingEnvironment'),
        ('core_logic.drl_agent', 'DRLAgent'),
        ('core_logic.performance_analyzer', 'PerformanceAnalyzer')
    ]
    
    results = []
    
    for module_path, class_name in modules_to_check:
        try:
            module = importlib.import_module(module_path)
            class_obj = getattr(module, class_name)
            logger.info(f"成功导入 {module_path}.{class_name}")
            results.append({
                'module_path': module_path,
                'class_name': class_name,
                'success': True
            })
        except ImportError as e:
            logger.warning(f"导入 {module_path} 失败: {str(e)}")
            results.append({
                'module_path': module_path,
                'class_name': class_name,
                'success': False,
                'error': f"ImportError: {str(e)}"
            })
        except AttributeError as e:
            logger.warning(f"模块 {module_path} 中找不到类 {class_name}: {str(e)}")
            results.append({
                'module_path': module_path,
                'class_name': class_name,
                'success': False,
                'error': f"AttributeError: {str(e)}"
            })
        except Exception as e:
            logger.error(f"检查 {module_path}.{class_name} 时出错: {str(e)}")
            logger.error(traceback.format_exc())
            results.append({
                'module_path': module_path,
                'class_name': class_name,
                'success': False,
                'error': str(e)
            })
    
    return results

def fix_init_files():
    """修复__init__.py文件"""
    logger.info("修复__init__.py文件")
    
    init_files = [
        'quant_project/__init__.py',
        'quant_project/core_logic/__init__.py',
        'core_logic/__init__.py'
    ]
    
    for init_file in init_files:
        if os.path.exists(init_file):
            logger.info(f"检查 {init_file}")
            
            with open(init_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if not content.strip():
                logger.info(f"{init_file} 为空，添加导入语句")
                
                if init_file == 'quant_project/__init__.py':
                    new_content = '# quant_project package\n'
                elif init_file == 'quant_project/core_logic/__init__.py':
                    new_content = '''# quant_project.core_logic package
from quant_project.core_logic.data_handler import DataHandler
from quant_project.core_logic.feature_engineer import FeatureEngineer
from quant_project.core_logic.trading_environment import TradingEnvironment
from quant_project.core_logic.drl_agent import DRLAgent
from quant_project.core_logic.performance_analyzer import PerformanceAnalyzer
'''
                elif init_file == 'core_logic/__init__.py':
                    new_content = '''# core_logic package
from core_logic.data_handler import DataHandler
from core_logic.feature_engineer import FeatureEngineer
from core_logic.trading_environment import TradingEnvironment
from core_logic.drl_agent import DRLAgent
from core_logic.performance_analyzer import PerformanceAnalyzer
'''
                
                with open(init_file, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                logger.info(f"已更新 {init_file}")
            else:
                logger.info(f"{init_file} 已有内容，不做修改")
        else:
            logger.warning(f"{init_file} 不存在")
    
    return True

def create_import_helper():
    """创建导入辅助模块"""
    logger.info("创建导入辅助模块")
    
    helper_file = 'import_helper.py'
    
    content = '''#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
导入辅助模块
用于简化模块导入
"""

import os
import sys
import importlib

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
quant_project_path = os.path.join(project_root, 'quant_project')

if project_root not in sys.path:
    sys.path.append(project_root)

if quant_project_path not in sys.path:
    sys.path.append(quant_project_path)

def import_module(module_path, class_name=None):
    """导入模块或类"""
    try:
        module = importlib.import_module(module_path)
        if class_name:
            return getattr(module, class_name)
        return module
    except (ImportError, AttributeError) as e:
        print(f"导入 {module_path}{f'.{class_name}' if class_name else ''} 失败: {str(e)}")
        return None

# 核心模块
DataHandler = import_module('quant_project.core_logic.data_handler', 'DataHandler')
FeatureEngineer = import_module('quant_project.core_logic.feature_engineer', 'FeatureEngineer')
TradingEnvironment = import_module('quant_project.core_logic.trading_environment', 'TradingEnvironment')
DRLAgent = import_module('quant_project.core_logic.drl_agent', 'DRLAgent')
PerformanceAnalyzer = import_module('quant_project.core_logic.performance_analyzer', 'PerformanceAnalyzer')

# 如果上面的导入失败，尝试从根目录导入
if DataHandler is None:
    DataHandler = import_module('core_logic.data_handler', 'DataHandler')
if FeatureEngineer is None:
    FeatureEngineer = import_module('core_logic.feature_engineer', 'FeatureEngineer')
if TradingEnvironment is None:
    TradingEnvironment = import_module('core_logic.trading_environment', 'TradingEnvironment')
if DRLAgent is None:
    DRLAgent = import_module('core_logic.drl_agent', 'DRLAgent')
if PerformanceAnalyzer is None:
    PerformanceAnalyzer = import_module('core_logic.performance_analyzer', 'PerformanceAnalyzer')
'''
    
    with open(helper_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    logger.info(f"已创建导入辅助模块: {helper_file}")
    
    return True

def run():
    """运行修复脚本"""
    logger.info("开始修复模块导入问题")
    
    # 检查模块导入
    import_results = check_module_imports()
    
    # 统计导入成功的模块
    success_count = sum(1 for result in import_results if result['success'])
    total_count = len(import_results)
    
    logger.info(f"模块导入检查完成，成功率: {success_count}/{total_count}")
    
    # 修复__init__.py文件
    fix_init_files()
    
    # 创建导入辅助模块
    create_import_helper()
    
    logger.info("模块导入问题修复完成")
    
    return {
        'import_results': import_results,
        'success_rate': success_count / total_count if total_count > 0 else 0
    }

if __name__ == "__main__":
    run()
