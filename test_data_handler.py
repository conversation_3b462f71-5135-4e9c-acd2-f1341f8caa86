# -*- coding: utf-8 -*-
import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import traceback

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'logs/data_handler_test_{datetime.now().strftime("%Y-%m-%d")}.log')
    ]
)

# 确保logs目录存在
os.makedirs('logs', exist_ok=True)

# 导入数据处理模块
import sys
import os

# 添加项目路径到系统路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'quant_project'))

from quant_project.core_logic.data_handler import DataHandler

def test_stock_data():
    """测试股票数据获取功能"""

    print("\n===== 测试股票数据获取 =====")

    # 创建数据处理器实例
    handler = DataHandler(cache_dir='data_cache')

    # 设置测试日期范围
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')

    # 测试股票数据获取
    stocks = ['sh000001', 'sz000001']

    for stock in stocks:
        try:
            print(f"\n获取股票数据: {stock}")
            data = handler.get_stock_data(stock, start_date, end_date)

            if data is not None and not data.empty:
                print(f"成功获取 {stock} 数据, 行数: {len(data)}")
                print(f"数据日期范围: {data.index.min()} 到 {data.index.max()}")
                print(f"数据列: {data.columns.tolist()}")
                print(f"数据示例:\n{data.head(3)}")
            else:
                print(f"获取 {stock} 数据失败: 数据为空")
        except Exception as e:
            print(f"获取 {stock} 数据时出错: {str(e)}")
            print(traceback.format_exc())

def test_index_data():
    """测试指数数据获取功能"""

    print("\n===== 测试指数数据获取 =====")

    # 创建数据处理器实例
    handler = DataHandler(cache_dir='data_cache')

    # 设置测试日期范围
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')

    # 测试指数数据获取
    indices = ['index_000001', 'index_399001', 'index_000300']

    for idx in indices:
        try:
            print(f"\n获取指数数据: {idx}")
            data = handler.get_stock_data(idx, start_date, end_date)

            if data is not None and not data.empty:
                print(f"成功获取 {idx} 数据, 行数: {len(data)}")
                print(f"数据日期范围: {data.index.min()} 到 {data.index.max()}")
                print(f"数据列: {data.columns.tolist()}")
                print(f"数据示例:\n{data.head(3)}")
            else:
                print(f"获取 {idx} 数据失败: 数据为空")
        except Exception as e:
            print(f"获取 {idx} 数据时出错: {str(e)}")
            print(traceback.format_exc())

def test_crypto_data():
    """测试加密货币数据获取功能 - 已禁用"""

    print("\n===== 测试加密货币数据获取 - 已禁用 =====")
    print("加密货币数据提取功能已被禁用")
    print("根据要求，系统不再支持加密货币数据提取和模拟数据生成功能。")
    print("请使用其他类型的金融数据，如股票或指数。")
    print("请参考akshare官方文档获取正确的数据提取方法。")

    # 创建数据处理器实例
    handler = DataHandler(cache_dir='data_cache')

    # 设置测试日期范围
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=60)).strftime('%Y-%m-%d')

    # 测试加密货币数据获取 - 应该返回错误
    crypto = 'crypto_BTC'
    try:
        print(f"\n尝试获取加密货币数据: {crypto}")
        print(f"日期范围: {start_date} 至 {end_date}")
        print("预期结果: 功能已禁用错误")

        data = handler.get_stock_data(crypto, start_date, end_date)
        print("警告: 加密货币数据提取功能未被正确禁用")
    except ValueError as e:
        print(f"正确: 加密货币数据提取功能已被禁用: {str(e)}")

def test_different_frequencies():
    """测试不同频率数据获取功能"""

    print("\n===== 测试不同频率数据获取 =====")

    # 创建数据处理器实例
    handler = DataHandler(cache_dir='data_cache')

    # 设置测试日期范围
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')

    # 测试不同频率数据获取
    stock = 'sh000001'
    frequencies = ['日线', '周线', '月线']

    for freq in frequencies:
        try:
            print(f"\n获取 {stock} 的 {freq} 数据")
            data = handler.get_stock_data(stock, start_date, end_date, frequency=freq)

            if data is not None and not data.empty:
                print(f"成功获取 {freq} 数据, 行数: {len(data)}")
                print(f"数据日期范围: {data.index.min()} 到 {data.index.max()}")
                print(f"数据示例:\n{data.head(3)}")
            else:
                print(f"获取 {freq} 数据失败: 数据为空")
        except Exception as e:
            print(f"获取 {freq} 数据时出错: {str(e)}")
            print(traceback.format_exc())

def test_akshare_api():
    """测试AkShare API的可用性"""

    print("\n===== 测试AkShare API的可用性 =====")

    # 创建数据处理器实例
    handler = DataHandler(cache_dir='data_cache')

    try:
        # 测试获取股票数据
        print("\n测试获取股票数据API")
        import akshare as ak

        # 测试获取上证指数数据
        try:
            print("\n测试获取上证指数数据")
            sh_index_data = ak.stock_zh_index_daily(symbol="sh000001")

            if sh_index_data is not None and not sh_index_data.empty:
                print(f"成功获取上证指数数据，共 {len(sh_index_data)} 条记录")
                print(f"数据列: {sh_index_data.columns.tolist()}")
                print(f"数据示例:\n{sh_index_data.head(3)}")
            else:
                print("获取上证指数数据失败: 数据为空")
        except Exception as e:
            print(f"获取上证指数数据失败: {str(e)}")
            print(traceback.format_exc())

        # 测试获取个股数据
        try:
            print("\n测试获取个股数据")
            stock_data = ak.stock_zh_a_daily(symbol="600000", adjust="qfq")

            if stock_data is not None and not stock_data.empty:
                print(f"成功获取个股数据，共 {len(stock_data)} 条记录")
                print(f"数据列: {stock_data.columns.tolist()}")
                print(f"数据示例:\n{stock_data.head(3)}")
            else:
                print("获取个股数据失败: 数据为空")
        except Exception as e:
            print(f"获取个股数据失败: {str(e)}")
            print(traceback.format_exc())

        print("\n注意: 加密货币数据提取功能已被禁用")
        print("根据要求，系统不再支持加密货币数据提取和模拟数据生成功能。")

    except Exception as e:
        print(f"测试AkShare API时出错: {str(e)}")
        print(traceback.format_exc())

def run_all_tests():
    """运行所有测试"""
    try:
        test_akshare_api()  # 首先测试AkShare API的可用性
        test_stock_data()
        test_index_data()
        test_crypto_data()
        test_different_frequencies()
        print("\n===== 所有测试完成 =====")
        return True
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        print(traceback.format_exc())
        return False

if __name__ == '__main__':
    run_all_tests()