"""
数据验证模块
负责验证金融数据的完整性和有效性
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

class DataValidator:
    """
    数据验证类
    负责验证金融数据的完整性和有效性
    """

    def __init__(self, logger=None):
        """
        初始化数据验证器

        参数:
            logger (logging.Logger): 日志记录器
        """
        self.logger = logger or logging.getLogger('drl_trading')

    def validate_data(self, data):
        """
        验证数据

        参数:
            data (pandas.DataFrame): 金融数据

        返回:
            tuple: (bool, str) - 验证结果和错误信息
        """
        if data is None or data.empty:
            return False, "数据为空"

        # 验证数据结构
        structure_valid, structure_error = self._validate_structure(data)
        if not structure_valid:
            return False, structure_error

        # 验证数据完整性
        completeness_valid, completeness_error = self._validate_completeness(data)
        if not completeness_valid:
            return False, completeness_error

        # 验证数据有效性
        validity_valid, validity_error = self._validate_validity(data)
        if not validity_valid:
            return False, validity_error

        # 验证时间序列
        time_series_valid, time_series_error = self._validate_time_series(data)
        if not time_series_valid:
            return False, time_series_error

        return True, "数据验证通过"

    def _validate_structure(self, data):
        """
        验证数据结构

        参数:
            data (pandas.DataFrame): 金融数据

        返回:
            tuple: (bool, str) - 验证结果和错误信息
        """
        # 检查必要的列是否存在
        required_columns = ['开盘', '最高', '最低', '收盘', '成交量']
        missing_columns = [col for col in required_columns if col not in data.columns]
        
        if missing_columns:
            error_msg = f"数据缺少必要的列: {missing_columns}"
            self.logger.error(error_msg)
            return False, error_msg
        
        # 检查索引是否是日期类型
        if not isinstance(data.index, pd.DatetimeIndex):
            error_msg = "数据索引不是日期类型"
            self.logger.error(error_msg)
            return False, error_msg
        
        return True, "数据结构验证通过"

    def _validate_completeness(self, data):
        """
        验证数据完整性

        参数:
            data (pandas.DataFrame): 金融数据

        返回:
            tuple: (bool, str) - 验证结果和错误信息
        """
        # 检查是否有缺失值
        missing_values = data.isnull().sum()
        total_missing = missing_values.sum()
        
        if total_missing > 0:
            error_msg = f"数据中有 {total_missing} 个缺失值，按列分布: {missing_values[missing_values > 0]}"
            self.logger.warning(error_msg)
            # 不返回错误，因为缺失值可以在清洗阶段处理
        
        # 检查数据长度是否足够
        if len(data) < 20:  # 假设至少需要20个数据点
            error_msg = f"数据长度不足，只有 {len(data)} 个数据点，至少需要20个"
            self.logger.error(error_msg)
            return False, error_msg
        
        return True, "数据完整性验证通过"

    def _validate_validity(self, data):
        """
        验证数据有效性

        参数:
            data (pandas.DataFrame): 金融数据

        返回:
            tuple: (bool, str) - 验证结果和错误信息
        """
        # 检查价格是否为正
        price_columns = ['开盘', '最高', '最低', '收盘']
        for col in price_columns:
            if (data[col] <= 0).any():
                error_msg = f"列 {col} 中存在非正值"
                self.logger.error(error_msg)
                return False, error_msg
        
        # 检查最高价是否大于等于最低价
        if (data['最高'] < data['最低']).any():
            error_msg = "存在最高价小于最低价的记录"
            self.logger.error(error_msg)
            return False, error_msg
        
        # 检查开盘价和收盘价是否在最高价和最低价之间
        if ((data['开盘'] > data['最高']) | (data['开盘'] < data['最低'])).any():
            error_msg = "存在开盘价不在最高价和最低价之间的记录"
            self.logger.error(error_msg)
            return False, error_msg
        
        if ((data['收盘'] > data['最高']) | (data['收盘'] < data['最低'])).any():
            error_msg = "存在收盘价不在最高价和最低价之间的记录"
            self.logger.error(error_msg)
            return False, error_msg
        
        # 检查成交量是否为正
        if (data['成交量'] < 0).any():
            error_msg = "存在成交量为负的记录"
            self.logger.error(error_msg)
            return False, error_msg
        
        return True, "数据有效性验证通过"

    def _validate_time_series(self, data):
        """
        验证时间序列

        参数:
            data (pandas.DataFrame): 金融数据

        返回:
            tuple: (bool, str) - 验证结果和错误信息
        """
        # 检查索引是否有重复
        if data.index.duplicated().any():
            error_msg = "数据索引存在重复"
            self.logger.error(error_msg)
            return False, error_msg
        
        # 检查索引是否按时间排序
        if not data.index.is_monotonic_increasing:
            error_msg = "数据索引未按时间排序"
            self.logger.warning(error_msg)
            # 不返回错误，因为可以在清洗阶段排序
        
        # 检查时间间隔是否一致（对于日线数据）
        # 计算相邻日期之间的间隔
        date_diffs = data.index.to_series().diff().dropna()
        
        # 检查是否有异常大的间隔（例如超过5天）
        max_expected_diff = pd.Timedelta(days=5)
        large_gaps = date_diffs[date_diffs > max_expected_diff]
        
        if not large_gaps.empty:
            warning_msg = f"数据中存在 {len(large_gaps)} 个异常大的时间间隔，最大间隔为 {large_gaps.max()}"
            self.logger.warning(warning_msg)
            # 不返回错误，只是警告
        
        return True, "时间序列验证通过"

    def check_data_quality(self, data):
        """
        检查数据质量并返回质量评分

        参数:
            data (pandas.DataFrame): 金融数据

        返回:
            dict: 数据质量评分和详细信息
        """
        quality_score = {
            'completeness': 0,  # 完整性
            'validity': 0,      # 有效性
            'consistency': 0,   # 一致性
            'timeliness': 0,    # 时效性
            'overall': 0        # 总体评分
        }
        
        # 评估完整性 (0-100)
        missing_ratio = data.isnull().sum().sum() / (data.shape[0] * data.shape[1])
        quality_score['completeness'] = int(100 * (1 - missing_ratio))
        
        # 评估有效性 (0-100)
        # 检查价格和成交量是否合理
        price_columns = ['开盘', '最高', '最低', '收盘']
        price_validity = 0
        for col in price_columns:
            if col in data.columns:
                # 价格应该为正
                valid_ratio = (data[col] > 0).mean()
                price_validity += valid_ratio
        
        price_validity = price_validity / len(price_columns) if price_columns else 0
        volume_validity = (data['成交量'] >= 0).mean() if '成交量' in data.columns else 0
        
        quality_score['validity'] = int(100 * (price_validity * 0.7 + volume_validity * 0.3))
        
        # 评估一致性 (0-100)
        # 检查高低价关系是否一致
        high_low_consistent = ((data['最高'] >= data['最低']).mean() if '最高' in data.columns and '最低' in data.columns else 0)
        open_range_consistent = ((data['开盘'] <= data['最高']) & (data['开盘'] >= data['最低'])).mean() if '开盘' in data.columns else 0
        close_range_consistent = ((data['收盘'] <= data['最高']) & (data['收盘'] >= data['最低'])).mean() if '收盘' in data.columns else 0
        
        quality_score['consistency'] = int(100 * (high_low_consistent * 0.4 + open_range_consistent * 0.3 + close_range_consistent * 0.3))
        
        # 评估时效性 (0-100)
        # 检查数据的最新日期
        if isinstance(data.index, pd.DatetimeIndex) and len(data) > 0:
            latest_date = data.index.max()
            days_old = (datetime.now() - latest_date).days
            # 如果数据不超过30天，则时效性为100，否则按比例降低
            quality_score['timeliness'] = max(0, int(100 * (1 - days_old / 30))) if days_old <= 30 else 0
        else:
            quality_score['timeliness'] = 0
        
        # 计算总体评分
        weights = {
            'completeness': 0.3,
            'validity': 0.3,
            'consistency': 0.2,
            'timeliness': 0.2
        }
        
        quality_score['overall'] = int(
            quality_score['completeness'] * weights['completeness'] +
            quality_score['validity'] * weights['validity'] +
            quality_score['consistency'] * weights['consistency'] +
            quality_score['timeliness'] * weights['timeliness']
        )
        
        return quality_score
