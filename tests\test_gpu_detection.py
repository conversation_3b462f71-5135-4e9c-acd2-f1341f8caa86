"""
GPU检测与配置测试脚本
用于测试GPU检测、配置和诊断功能
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import time

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core_logic.utils import setup_logger, is_gpu_available, get_gpu_info, setup_gpu_environment, diagnose_gpu_issues

# 设置日志
logger = setup_logger(log_file='logs/test_gpu_detection.log')

def test_gpu_detection():
    """测试GPU检测功能"""
    logger.info("测试GPU检测功能")
    
    # 检测GPU是否可用
    gpu_available = is_gpu_available()
    logger.info(f"GPU可用: {gpu_available}")
    
    # 获取GPU详细信息
    gpu_info = get_gpu_info()
    logger.info(f"GPU信息: {gpu_info}")
    
    # 记录测试结果
    results = {
        'gpu_available': gpu_available,
        'gpu_count': gpu_info['count'],
        'gpu_framework': gpu_info['framework'],
        'detection_method': gpu_info['detection_method']
    }
    
    # 如果有GPU设备，记录设备信息
    if gpu_info['count'] > 0 and len(gpu_info['devices']) > 0:
        results['gpu_devices'] = gpu_info['devices']
    
    # 保存测试结果
    results_df = pd.DataFrame([results])
    results_df.to_csv('tests/gpu_detection_results.csv', index=False)
    
    return results

def test_gpu_environment_setup():
    """测试GPU环境配置功能"""
    logger.info("测试GPU环境配置功能")
    
    # 配置GPU环境
    setup_success = False
    try:
        setup_result = setup_gpu_environment()
        setup_success = setup_result['success']
        logger.info(f"GPU环境配置结果: {setup_result}")
    except Exception as e:
        logger.error(f"GPU环境配置失败: {str(e)}")
    
    # 记录测试结果
    results = {
        'setup_success': setup_success
    }
    
    # 保存测试结果
    results_df = pd.DataFrame([results])
    results_df.to_csv('tests/gpu_environment_setup_results.csv', index=False)
    
    return results

def test_gpu_diagnosis():
    """测试GPU诊断功能"""
    logger.info("测试GPU诊断功能")
    
    # 诊断GPU问题
    diagnosis = diagnose_gpu_issues()
    logger.info(f"GPU诊断结果: {diagnosis}")
    
    # 记录测试结果
    results = {
        'issues_detected': diagnosis['issues_detected'],
        'gpu_detected': diagnosis['gpu_detected'],
        'cuda_detected': diagnosis['cuda_detected'],
        'pytorch_gpu': diagnosis['pytorch_gpu'],
        'tensorflow_gpu': diagnosis['tensorflow_gpu']
    }
    
    # 如果检测到问题，记录问题和建议
    if diagnosis['issues_detected']:
        results['problems'] = diagnosis['problems']
        results['suggestions'] = diagnosis['suggestions']
    
    # 如果有驱动版本信息，记录版本信息
    if diagnosis['driver_version']:
        results['driver_version'] = diagnosis['driver_version']
    
    # 如果有CUDA版本信息，记录版本信息
    if diagnosis['cuda_version']:
        results['cuda_version'] = diagnosis['cuda_version']
    
    # 保存测试结果
    results_df = pd.DataFrame([results])
    results_df.to_csv('tests/gpu_diagnosis_results.csv', index=False)
    
    return results

def test_pytorch_gpu():
    """测试PyTorch GPU功能"""
    logger.info("测试PyTorch GPU功能")
    
    try:
        import torch
        import numpy as np
        
        # 检查CUDA是否可用
        cuda_available = torch.cuda.is_available()
        logger.info(f"PyTorch CUDA可用: {cuda_available}")
        
        if cuda_available:
            # 获取GPU设备数量
            device_count = torch.cuda.device_count()
            logger.info(f"PyTorch GPU设备数量: {device_count}")
            
            # 获取当前设备
            current_device = torch.cuda.current_device()
            logger.info(f"PyTorch当前GPU设备: {current_device}")
            
            # 获取设备名称
            device_name = torch.cuda.get_device_name(current_device)
            logger.info(f"PyTorch GPU设备名称: {device_name}")
            
            # 测试GPU性能
            # 创建大型矩阵
            size = 5000
            
            # CPU计算
            start_time = time.time()
            a_cpu = torch.randn(size, size)
            b_cpu = torch.randn(size, size)
            c_cpu = torch.matmul(a_cpu, b_cpu)
            cpu_time = time.time() - start_time
            logger.info(f"CPU矩阵乘法耗时: {cpu_time:.4f}秒")
            
            # GPU计算
            start_time = time.time()
            a_gpu = torch.randn(size, size, device='cuda')
            b_gpu = torch.randn(size, size, device='cuda')
            c_gpu = torch.matmul(a_gpu, b_gpu)
            torch.cuda.synchronize()  # 等待GPU操作完成
            gpu_time = time.time() - start_time
            logger.info(f"GPU矩阵乘法耗时: {gpu_time:.4f}秒")
            
            # 计算加速比
            speedup = cpu_time / gpu_time
            logger.info(f"GPU加速比: {speedup:.2f}x")
            
            # 记录测试结果
            results = {
                'cuda_available': cuda_available,
                'device_count': device_count,
                'device_name': device_name,
                'cpu_time': cpu_time,
                'gpu_time': gpu_time,
                'speedup': speedup
            }
        else:
            # 记录测试结果
            results = {
                'cuda_available': cuda_available
            }
    
    except ImportError:
        logger.error("未安装PyTorch")
        results = {
            'cuda_available': False,
            'error': 'PyTorch not installed'
        }
    except Exception as e:
        logger.error(f"PyTorch GPU测试失败: {str(e)}")
        results = {
            'cuda_available': False,
            'error': str(e)
        }
    
    # 保存测试结果
    results_df = pd.DataFrame([results])
    results_df.to_csv('tests/pytorch_gpu_results.csv', index=False)
    
    return results

def test_tensorflow_gpu():
    """测试TensorFlow GPU功能"""
    logger.info("测试TensorFlow GPU功能")
    
    try:
        import tensorflow as tf
        
        # 检查TensorFlow是否能够看到GPU
        gpus = tf.config.list_physical_devices('GPU')
        gpu_available = len(gpus) > 0
        logger.info(f"TensorFlow GPU可用: {gpu_available}, 设备数量: {len(gpus)}")
        
        if gpu_available:
            # 获取GPU设备信息
            gpu_details = []
            for gpu in gpus:
                gpu_details.append(str(gpu))
            logger.info(f"TensorFlow GPU设备: {gpu_details}")
            
            # 测试GPU性能
            # 创建大型矩阵
            size = 5000
            
            # CPU计算
            with tf.device('/cpu:0'):
                start_time = time.time()
                a_cpu = tf.random.normal((size, size))
                b_cpu = tf.random.normal((size, size))
                c_cpu = tf.matmul(a_cpu, b_cpu)
                cpu_time = time.time() - start_time
                logger.info(f"CPU矩阵乘法耗时: {cpu_time:.4f}秒")
            
            # GPU计算
            with tf.device('/gpu:0'):
                start_time = time.time()
                a_gpu = tf.random.normal((size, size))
                b_gpu = tf.random.normal((size, size))
                c_gpu = tf.matmul(a_gpu, b_gpu)
                gpu_time = time.time() - start_time
                logger.info(f"GPU矩阵乘法耗时: {gpu_time:.4f}秒")
            
            # 计算加速比
            speedup = cpu_time / gpu_time
            logger.info(f"GPU加速比: {speedup:.2f}x")
            
            # 记录测试结果
            results = {
                'gpu_available': gpu_available,
                'device_count': len(gpus),
                'devices': gpu_details,
                'cpu_time': cpu_time,
                'gpu_time': gpu_time,
                'speedup': speedup
            }
        else:
            # 记录测试结果
            results = {
                'gpu_available': gpu_available,
                'device_count': 0
            }
    
    except ImportError:
        logger.error("未安装TensorFlow")
        results = {
            'gpu_available': False,
            'error': 'TensorFlow not installed'
        }
    except Exception as e:
        logger.error(f"TensorFlow GPU测试失败: {str(e)}")
        results = {
            'gpu_available': False,
            'error': str(e)
        }
    
    # 保存测试结果
    results_df = pd.DataFrame([results])
    results_df.to_csv('tests/tensorflow_gpu_results.csv', index=False)
    
    return results

def run_all_tests():
    """运行所有测试"""
    logger.info("开始运行所有GPU检测与配置测试")
    
    # 确保测试目录存在
    os.makedirs('tests', exist_ok=True)
    
    # 运行各项测试
    detection_results = test_gpu_detection()
    setup_results = test_gpu_environment_setup()
    diagnosis_results = test_gpu_diagnosis()
    pytorch_results = test_pytorch_gpu()
    tensorflow_results = test_tensorflow_gpu()
    
    # 汇总测试结果
    gpu_available = detection_results['gpu_available']
    gpu_setup_success = setup_results['setup_success']
    
    pytorch_gpu_working = False
    tensorflow_gpu_working = False
    
    if 'cuda_available' in pytorch_results and pytorch_results['cuda_available']:
        pytorch_gpu_working = pytorch_results['speedup'] > 1.0
    
    if 'gpu_available' in tensorflow_results and tensorflow_results['gpu_available']:
        tensorflow_gpu_working = tensorflow_results['speedup'] > 1.0
    
    # 记录汇总结果
    summary = {
        'gpu_available': gpu_available,
        'gpu_setup_success': gpu_setup_success,
        'pytorch_gpu_working': pytorch_gpu_working,
        'tensorflow_gpu_working': tensorflow_gpu_working,
        'overall_success': gpu_available and gpu_setup_success and (pytorch_gpu_working or tensorflow_gpu_working)
    }
    
    logger.info(f"GPU检测与配置测试汇总结果: {summary}")
    
    # 保存汇总结果
    summary_df = pd.DataFrame([summary])
    summary_df.to_csv('tests/gpu_tests_summary.csv', index=False)
    
    return summary

if __name__ == "__main__":
    run_all_tests()
