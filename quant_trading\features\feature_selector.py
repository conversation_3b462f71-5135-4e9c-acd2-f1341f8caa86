"""
特征选择模块
负责选择最重要的特征
"""

import pandas as pd
import numpy as np
import logging
from sklearn.feature_selection import mutual_info_regression, SelectKBest, f_regression
from sklearn.ensemble import RandomForestRegressor
import matplotlib.pyplot as plt
import os

class FeatureSelector:
    """
    特征选择类
    负责选择最重要的特征
    """
    
    def __init__(self, method='mutual_info', top_n=30, target_col='涨跌幅', logger=None):
        """
        初始化特征选择器
        
        参数:
            method (str): 特征选择方法，可选 'mutual_info', 'f_regression', 'random_forest'
            top_n (int): 选择的特征数量
            target_col (str): 目标列名
            logger (logging.Logger): 日志记录器
        """
        self.logger = logger or logging.getLogger('drl_trading')
        self.method = method
        self.top_n = top_n
        self.target_col = target_col
        self.selected_features = []
        self.feature_importances = {}
        
    def select(self, df, feature_names=None):
        """
        选择特征
        
        参数:
            df (pandas.DataFrame): 包含特征和目标的数据框
            feature_names (list, optional): 要考虑的特征名称列表，如果为None，则使用除目标列外的所有列
            
        返回:
            tuple: (选择的特征列表, 特征重要性字典)
        """
        if self.target_col not in df.columns:
            self.logger.warning(f"目标列 '{self.target_col}' 不在数据框中，无法进行特征选择")
            return [], {}
        
        # 如果未提供特征名称，则使用除目标列外的所有列
        if feature_names is None:
            feature_names = [col for col in df.columns if col != self.target_col]
        
        # 确保所有特征都在数据框中
        valid_features = [f for f in feature_names if f in df.columns]
        if len(valid_features) < len(feature_names):
            missing_features = set(feature_names) - set(valid_features)
            self.logger.warning(f"以下特征不在数据框中: {missing_features}")
        
        # 如果没有有效特征，则返回空结果
        if not valid_features:
            self.logger.warning("没有有效特征可供选择")
            return [], {}
        
        # 准备特征矩阵和目标向量
        X = df[valid_features].copy()
        y = df[self.target_col].copy()
        
        # 处理缺失值
        X = X.fillna(0)
        y = y.fillna(0)
        
        # 根据方法选择特征
        if self.method == 'mutual_info':
            return self._select_mutual_info(X, y, valid_features)
        elif self.method == 'f_regression':
            return self._select_f_regression(X, y, valid_features)
        elif self.method == 'random_forest':
            return self._select_random_forest(X, y, valid_features)
        else:
            self.logger.warning(f"未知的特征选择方法: {self.method}，使用互信息法")
            return self._select_mutual_info(X, y, valid_features)
    
    def _select_mutual_info(self, X, y, feature_names):
        """使用互信息法选择特征"""
        try:
            # 计算互信息
            mi_scores = mutual_info_regression(X, y)
            
            # 创建特征重要性字典
            importances = dict(zip(feature_names, mi_scores))
            
            # 按重要性排序
            sorted_importances = {k: v for k, v in sorted(importances.items(), key=lambda item: item[1], reverse=True)}
            
            # 选择前top_n个特征
            selected = list(sorted_importances.keys())[:min(self.top_n, len(sorted_importances))]
            
            self.selected_features = selected
            self.feature_importances = sorted_importances
            
            self.logger.info(f"使用互信息法选择了 {len(selected)} 个特征")
            
            return selected, sorted_importances
        except Exception as e:
            self.logger.error(f"互信息特征选择失败: {str(e)}")
            return [], {}
    
    def _select_f_regression(self, X, y, feature_names):
        """使用F检验选择特征"""
        try:
            # 使用F检验
            selector = SelectKBest(f_regression, k=min(self.top_n, len(feature_names)))
            selector.fit(X, y)
            
            # 获取分数
            scores = selector.scores_
            
            # 创建特征重要性字典
            importances = dict(zip(feature_names, scores))
            
            # 按重要性排序
            sorted_importances = {k: v for k, v in sorted(importances.items(), key=lambda item: item[1], reverse=True)}
            
            # 选择前top_n个特征
            selected = list(sorted_importances.keys())[:min(self.top_n, len(sorted_importances))]
            
            self.selected_features = selected
            self.feature_importances = sorted_importances
            
            self.logger.info(f"使用F检验选择了 {len(selected)} 个特征")
            
            return selected, sorted_importances
        except Exception as e:
            self.logger.error(f"F检验特征选择失败: {str(e)}")
            return [], {}
    
    def _select_random_forest(self, X, y, feature_names):
        """使用随机森林选择特征"""
        try:
            # 使用随机森林
            rf = RandomForestRegressor(n_estimators=100, random_state=42)
            rf.fit(X, y)
            
            # 获取特征重要性
            importances = dict(zip(feature_names, rf.feature_importances_))
            
            # 按重要性排序
            sorted_importances = {k: v for k, v in sorted(importances.items(), key=lambda item: item[1], reverse=True)}
            
            # 选择前top_n个特征
            selected = list(sorted_importances.keys())[:min(self.top_n, len(sorted_importances))]
            
            self.selected_features = selected
            self.feature_importances = sorted_importances
            
            self.logger.info(f"使用随机森林选择了 {len(selected)} 个特征")
            
            return selected, sorted_importances
        except Exception as e:
            self.logger.error(f"随机森林特征选择失败: {str(e)}")
            return [], {}
    
    def plot_feature_importance(self, top_n=20, save_path=None):
        """
        绘制特征重要性图
        
        参数:
            top_n (int): 显示的特征数量
            save_path (str, optional): 保存图片的路径
        """
        if not self.feature_importances:
            self.logger.warning("没有特征重要性信息可供绘制")
            return
        
        # 获取前top_n个特征
        sorted_importances = {k: v for k, v in sorted(self.feature_importances.items(), key=lambda item: item[1], reverse=True)}
        top_features = list(sorted_importances.keys())[:min(top_n, len(sorted_importances))]
        top_importances = [sorted_importances[f] for f in top_features]
        
        # 创建图形
        plt.figure(figsize=(10, 8))
        plt.barh(range(len(top_features)), top_importances, align='center')
        plt.yticks(range(len(top_features)), top_features)
        plt.xlabel('特征重要性')
        plt.ylabel('特征')
        plt.title(f'前 {len(top_features)} 个重要特征 (方法: {self.method})')
        plt.tight_layout()
        
        # 保存图片
        if save_path:
            try:
                # 确保目录存在
                os.makedirs(os.path.dirname(save_path), exist_ok=True)
                plt.savefig(save_path)
                self.logger.info(f"特征重要性图已保存到 {save_path}")
            except Exception as e:
                self.logger.error(f"保存特征重要性图失败: {str(e)}")
        
        plt.close()

class FeatureImportance:
    """
    特征重要性类
    负责计算特征重要性
    """
    
    def __init__(self, logger=None):
        """
        初始化特征重要性
        
        参数:
            logger (logging.Logger): 日志记录器
        """
        self.logger = logger or logging.getLogger('drl_trading')
        self.feature_selector = FeatureSelector(logger=self.logger)
        
    def calculate(self, df, feature_names=None, target_col='涨跌幅', method='mutual_info'):
        """
        计算特征重要性
        
        参数:
            df (pandas.DataFrame): 包含特征和目标的数据框
            feature_names (list, optional): 要考虑的特征名称列表
            target_col (str): 目标列名
            method (str): 特征选择方法
            
        返回:
            dict: 特征重要性字典
        """
        self.feature_selector.method = method
        self.feature_selector.target_col = target_col
        _, importances = self.feature_selector.select(df, feature_names)
        return importances
