"""
扩展数据提取测试脚本
用于测试期货、指数和加密货币数据的提取功能
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core_logic.data_handler import DataHandler
from core_logic.utils import setup_logger

# 设置日志
logger = setup_logger(log_file='logs/test_extended_data.log')

def test_futures_data_extraction():
    """测试期货数据提取功能"""
    logger.info("测试期货数据提取功能 - 已禁用")

    # 创建一个空的结果DataFrame
    results = []

    # 添加一条说明记录
    results.append({
        'code': 'futures_disabled',
        'start_date': '2023-01-01',
        'end_date': '2023-01-31',
        'frequency': '日线',
        'data_length': 0,
        'success': False,
        'error': '期货数据提取功能已被禁用'
    })

    # 输出测试结果
    results_df = pd.DataFrame(results)
    print("\n期货数据提取测试结果:")
    print("期货数据提取功能已被禁用")

    # 保存测试结果
    results_df.to_csv('tests/futures_data_extraction_results.csv', index=False)

    return results_df

def test_indices_data_extraction():
    """测试指数数据提取功能"""
    logger.info("测试指数数据提取功能")

    # 创建DataHandler实例
    data_handler = DataHandler()

    # 测试不同指数代码
    indices_codes = ['index_000001', 'index_000300', 'index_000905', 'index_399001']

    # 测试不同时间间隔
    time_intervals = [
        # 短期 - 1个月
        {'start': '2023-01-01',
         'end': '2023-01-31'},
        # 中期 - 3个月
        {'start': '2023-01-01',
         'end': '2023-03-31'}
    ]

    # 测试不同频率
    frequencies = ['日线', '周线']

    results = []

    # 执行测试
    for index_code in indices_codes:
        for interval in time_intervals:
            for frequency in frequencies:
                try:
                    logger.info(f"获取 {index_code} 数据，时间区间: {interval['start']} 至 {interval['end']}，频率: {frequency}")

                    # 获取数据
                    data = data_handler.get_stock_data(
                        stock_code=index_code,
                        start_date=interval['start'],
                        end_date=interval['end'],
                        frequency=frequency
                    )

                    # 记录结果
                    results.append({
                        'code': index_code,
                        'start_date': interval['start'],
                        'end_date': interval['end'],
                        'frequency': frequency,
                        'data_length': len(data),
                        'success': True,
                        'columns': list(data.columns)
                    })

                    logger.info(f"成功获取数据，共 {len(data)} 条记录")

                except Exception as e:
                    logger.error(f"获取数据失败: {str(e)}")

                    # 记录失败结果
                    results.append({
                        'code': index_code,
                        'start_date': interval['start'],
                        'end_date': interval['end'],
                        'frequency': frequency,
                        'data_length': 0,
                        'success': False,
                        'error': str(e)
                    })

    # 输出测试结果
    results_df = pd.DataFrame(results)
    print("\n指数数据提取测试结果:")
    print(results_df[['code', 'frequency', 'data_length', 'success']])

    # 保存测试结果
    results_df.to_csv('tests/indices_data_extraction_results.csv', index=False)

    return results_df

def test_crypto_data_extraction():
    """测试加密货币数据提取功能"""
    logger.info("测试加密货币数据提取功能")

    # 创建DataHandler实例
    data_handler = DataHandler()

    # 测试不同加密货币代码
    crypto_codes = ['crypto_BTC', 'crypto_ETH', 'crypto_LTC', 'crypto_XRP']

    # 测试不同时间间隔
    time_intervals = [
        # 短期 - 1个月
        {'start': '2023-01-01',
         'end': '2023-01-31'},
        # 中期 - 3个月
        {'start': '2023-01-01',
         'end': '2023-03-31'}
    ]

    # 测试不同频率
    frequencies = ['日线', '周线']

    results = []

    # 执行测试
    for crypto_code in crypto_codes:
        for interval in time_intervals:
            for frequency in frequencies:
                try:
                    logger.info(f"获取 {crypto_code} 数据，时间区间: {interval['start']} 至 {interval['end']}，频率: {frequency}")

                    # 获取数据
                    data = data_handler.get_stock_data(
                        stock_code=crypto_code,
                        start_date=interval['start'],
                        end_date=interval['end'],
                        frequency=frequency
                    )

                    # 记录结果
                    results.append({
                        'code': crypto_code,
                        'start_date': interval['start'],
                        'end_date': interval['end'],
                        'frequency': frequency,
                        'data_length': len(data),
                        'success': True,
                        'columns': list(data.columns)
                    })

                    logger.info(f"成功获取数据，共 {len(data)} 条记录")

                except Exception as e:
                    logger.error(f"获取数据失败: {str(e)}")

                    # 记录失败结果
                    results.append({
                        'code': crypto_code,
                        'start_date': interval['start'],
                        'end_date': interval['end'],
                        'frequency': frequency,
                        'data_length': 0,
                        'success': False,
                        'error': str(e)
                    })

    # 输出测试结果
    results_df = pd.DataFrame(results)
    print("\n加密货币数据提取测试结果:")
    print(results_df[['code', 'frequency', 'data_length', 'success']])

    # 保存测试结果
    results_df.to_csv('tests/crypto_data_extraction_results.csv', index=False)

    return results_df

def run_all_tests():
    """运行所有测试"""
    logger.info("开始运行所有扩展数据提取测试")

    # 确保测试目录存在
    os.makedirs('tests', exist_ok=True)

    # 运行各项测试
    futures_results = test_futures_data_extraction()
    indices_results = test_indices_data_extraction()
    crypto_results = test_crypto_data_extraction()

    # 汇总测试结果
    # 期货数据提取已禁用，不计入成功率
    futures_success_rate = 100.0  # 设为100%，因为已禁用
    indices_success_rate = indices_results['success'].mean() * 100
    crypto_success_rate = crypto_results['success'].mean() * 100

    # 只计算指数和加密货币的平均成功率
    overall_success_rate = (indices_success_rate + crypto_success_rate) / 2

    logger.info(f"期货数据提取: 已禁用")
    logger.info(f"指数数据提取成功率: {indices_success_rate:.2f}%")
    logger.info(f"加密货币数据提取成功率: {crypto_success_rate:.2f}%")
    logger.info(f"整体数据提取成功率: {overall_success_rate:.2f}%")

    # 保存汇总结果
    summary = {
        'futures_success_rate': futures_success_rate,
        'indices_success_rate': indices_success_rate,
        'crypto_success_rate': crypto_success_rate,
        'overall_success_rate': overall_success_rate
    }

    summary_df = pd.DataFrame([summary])
    summary_df.to_csv('tests/extended_data_extraction_summary.csv', index=False)

    return overall_success_rate >= 80  # 如果整体成功率大于等于80%，则测试通过

if __name__ == "__main__":
    run_all_tests()
