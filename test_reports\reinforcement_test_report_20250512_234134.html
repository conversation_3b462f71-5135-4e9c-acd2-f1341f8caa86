
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>量化交易系统强化测试报告</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                h1, h2, h3 { color: #333; }
                table { border-collapse: collapse; width: 100%; margin-bottom: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
                tr:nth-child(even) { background-color: #f9f9f9; }
                .success { color: green; }
                .failure { color: red; }
                .warning { color: orange; }
                .summary { background-color: #f0f0f0; padding: 10px; margin-bottom: 20px; }
            </style>
        </head>
        <body>
            <h1>量化交易系统强化测试报告</h1>
            <p>生成时间: 2025-05-12 23:41:34</p>

            <div class="summary">
                <h2>测试摘要</h2>
                <table>
                    <tr>
                        <th>测试模块</th>
                        <th>状态</th>
                        <th>详情</th>
                    </tr>
        
                <tr>
                    <td>environment_setup</td>
                    <td class="failure">False</td>
                    <td>Python版本: 3.13.2 (tags/v3.13.2:4f8bb39, Feb  4 2025, 15:23:48) [MSC v.1942 64 bit (AMD64)]</td>
                </tr>
            
                <tr>
                    <td>data_processing</td>
                    <td class="success">True</td>
                    <td>股票数据: (260, 6)<br>指数数据: (260, 6)</td>
                </tr>
            
                <tr>
                    <td>feature_engineering</td>
                    <td class="failure">False</td>
                    <td>错误: dictionary update sequence element #0 has length 1; 2 is required</td>
                </tr>
            
                <tr>
                    <td>trading_environment</td>
                    <td class="failure">False</td>
                    <td>错误: dictionary update sequence element #0 has length 1; 2 is required</td>
                </tr>
            
                <tr>
                    <td>model_training</td>
                    <td class="failure">False</td>
                    <td>错误: dictionary update sequence element #0 has length 1; 2 is required</td>
                </tr>
            
                <tr>
                    <td>performance_analyzer</td>
                    <td class="failure">False</td>
                    <td>错误: cannot do slice indexing on DatetimeIndex with these indexers [portfolio_value   2022-01-01
dtype: datetime64[ns]] of type Series</td>
                </tr>
            
                <tr>
                    <td>ui</td>
                    <td class="success">True</td>
                    <td>应用文件: quant_project/main_app.py<br>后端导入: 34 个模块<br>has_forms: 否<br>has_buttons: 是<br>has_file_upload: 否<br>has_progress_bar: 是<br>has_charts: 是<br>has_session_state: 是<br>has_cache: 否<br>has_stop_button: 否<br>has_log_clear: 否</td>
                </tr>
            
                <tr>
                    <td><strong>总计</strong></td>
                    <td class="failure">
                        2/7 (28.57%)
                    </td>
                    <td></td>
                </tr>
            </table>
        </div>

        <h2>测试结论</h2>
        
            <p>系统测试通过率低，需要大量修复工作。</p>

            <h2>详细测试日志</h2>
            <p>详细测试日志请查看: logs\reinforcement_test_20250512_234124.log</p>

        </body>
        </html>
        