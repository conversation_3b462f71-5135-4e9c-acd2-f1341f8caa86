"""
风险管理模块
实现高级风险管理功能，包括动态止损、风险预算分配和波动率调整
"""

import numpy as np
import pandas as pd
import logging
from enum import Enum
from typing import Dict, List, Optional, Tuple, Union
from ..market_analysis import MarketCondition, MarketConditionDetector

class StopLossType(Enum):
    """止损类型枚举"""
    FIXED = 1       # 固定比例止损
    VOLATILITY = 2  # 基于波动率的止损
    ATR = 3         # 基于ATR的止损
    TRAILING = 4    # 追踪止损

class PositionSizingMethod(Enum):
    """仓位大小计算方法枚举"""
    FIXED = 1           # 固定比例
    VOLATILITY = 2      # 基于波动率
    KELLY = 3           # 凯利公式
    OPTIMAL_F = 4       # 最优f值
    RISK_PARITY = 5     # 风险平价

class RiskManager:
    """
    风险管理类
    实现高级风险管理功能
    """

    def __init__(self, 
                 stop_loss_config: Optional[Dict] = None,
                 position_sizing_config: Optional[Dict] = None,
                 market_condition_detector: Optional[MarketConditionDetector] = None,
                 logger: Optional[logging.Logger] = None):
        """
        初始化风险管理器

        参数:
            stop_loss_config (dict, optional): 止损配置
            position_sizing_config (dict, optional): 仓位大小配置
            market_condition_detector (MarketConditionDetector, optional): 市场状态检测器
            logger (logging.Logger, optional): 日志记录器
        """
        self.logger = logger or logging.getLogger('drl_trading')
        
        # 默认止损配置
        default_stop_loss = {
            'type': StopLossType.VOLATILITY,
            'threshold': 0.05,  # 5%止损
            'atr_multiplier': 3.0,  # ATR乘数
            'trailing_percent': 0.02,  # 2%追踪止损
            'max_loss': 0.1,  # 最大损失10%
            'time_stop': 10,  # 10天时间止损
        }
        
        # 默认仓位大小配置
        default_position_sizing = {
            'method': PositionSizingMethod.VOLATILITY,
            'max_position': 1.0,  # 最大仓位
            'risk_per_trade': 0.01,  # 每笔交易风险1%
            'target_volatility': 0.15,  # 目标年化波动率15%
            'kelly_fraction': 0.5,  # 凯利公式分数
        }
        
        # 使用提供的配置或默认配置
        self.stop_loss_config = stop_loss_config or default_stop_loss
        self.position_sizing_config = position_sizing_config or default_position_sizing
        
        # 市场状态检测器
        self.market_detector = market_condition_detector or MarketConditionDetector(logger=self.logger)
        
        # 初始化状态
        self.position = 0.0
        self.entry_price = 0.0
        self.highest_price = 0.0
        self.lowest_price = float('inf')
        self.position_start_time = 0
        self.current_time = 0
        self.market_condition = None
        self.stop_loss_triggered = False
        self.take_profit_triggered = False
        
        # 动态参数
        self.dynamic_stop_loss = self.stop_loss_config['threshold']
        self.dynamic_position_size = self.position_sizing_config['max_position']

    def update_market_condition(self, data: pd.DataFrame) -> MarketCondition:
        """
        更新市场状态

        参数:
            data (pandas.DataFrame): 市场数据

        返回:
            MarketCondition: 当前市场状态
        """
        result = self.market_detector.detect_market_condition(data)
        self.market_condition = result['primary_condition']
        
        # 根据市场状态调整风险参数
        self._adjust_risk_parameters()
        
        return self.market_condition

    def calculate_position_size(self, 
                               price: float, 
                               volatility: float, 
                               capital: float, 
                               win_rate: Optional[float] = None, 
                               avg_win: Optional[float] = None, 
                               avg_loss: Optional[float] = None) -> float:
        """
        计算仓位大小

        参数:
            price (float): 当前价格
            volatility (float): 波动率
            capital (float): 可用资金
            win_rate (float, optional): 胜率
            avg_win (float, optional): 平均盈利
            avg_loss (float, optional): 平均亏损

        返回:
            float: 仓位大小（0-1之间的比例）
        """
        method = self.position_sizing_config['method']
        
        if method == PositionSizingMethod.FIXED:
            # 固定比例
            position_size = self.dynamic_position_size
        
        elif method == PositionSizingMethod.VOLATILITY:
            # 基于波动率的仓位大小
            target_vol = self.position_sizing_config['target_volatility']
            if volatility > 0:
                position_size = target_vol / volatility
                # 限制范围
                position_size = min(self.dynamic_position_size, max(0.1, position_size))
            else:
                position_size = self.dynamic_position_size
        
        elif method == PositionSizingMethod.KELLY:
            # 凯利公式
            if win_rate is not None and avg_win is not None and avg_loss is not None and avg_loss > 0:
                kelly = win_rate - ((1 - win_rate) / (avg_win / avg_loss))
                # 使用凯利分数来降低风险
                kelly_fraction = self.position_sizing_config['kelly_fraction']
                position_size = max(0, kelly * kelly_fraction)
                # 限制范围
                position_size = min(self.dynamic_position_size, position_size)
            else:
                self.logger.warning("计算凯利公式需要胜率、平均盈利和平均亏损，使用默认仓位")
                position_size = self.dynamic_position_size * 0.5
        
        elif method == PositionSizingMethod.OPTIMAL_F:
            # 最优f值
            if avg_win is not None and avg_loss is not None and avg_loss > 0:
                optimal_f = avg_win / avg_loss
                # 使用凯利分数来降低风险
                kelly_fraction = self.position_sizing_config['kelly_fraction']
                position_size = optimal_f * kelly_fraction
                # 限制范围
                position_size = min(self.dynamic_position_size, max(0, position_size))
            else:
                self.logger.warning("计算最优f值需要平均盈利和平均亏损，使用默认仓位")
                position_size = self.dynamic_position_size * 0.5
        
        elif method == PositionSizingMethod.RISK_PARITY:
            # 风险平价
            risk_per_trade = self.position_sizing_config['risk_per_trade']
            if volatility > 0:
                # 计算每单位波动率的仓位
                position_size = risk_per_trade / volatility
                # 限制范围
                position_size = min(self.dynamic_position_size, max(0.1, position_size))
            else:
                position_size = self.dynamic_position_size * risk_per_trade
        
        else:
            self.logger.warning(f"不支持的仓位计算方法: {method}，使用默认仓位")
            position_size = self.dynamic_position_size
        
        # 根据市场状态进一步调整仓位
        position_size = self._adjust_position_by_market_condition(position_size)
        
        # 计算实际仓位数量
        position_value = capital * position_size
        shares = position_value / price if price > 0 else 0
        
        return shares

    def check_stop_loss(self, 
                       current_price: float, 
                       position: float, 
                       entry_price: float, 
                       atr: Optional[float] = None) -> bool:
        """
        检查是否触发止损

        参数:
            current_price (float): 当前价格
            position (float): 当前仓位
            entry_price (float): 入场价格
            atr (float, optional): ATR值

        返回:
            bool: 是否触发止损
        """
        if position == 0 or entry_price == 0:
            return False
        
        # 更新状态
        self.position = position
        self.entry_price = entry_price
        self.current_time += 1
        
        # 更新最高价和最低价
        if current_price > self.highest_price:
            self.highest_price = current_price
        if current_price < self.lowest_price:
            self.lowest_price = current_price
        
        # 计算当前盈亏比例
        if position > 0:  # 多头
            profit_ratio = (current_price - entry_price) / entry_price
        else:  # 空头
            profit_ratio = (entry_price - current_price) / entry_price
        
        # 根据止损类型检查是否触发止损
        stop_loss_type = self.stop_loss_config['type']
        
        if stop_loss_type == StopLossType.FIXED:
            # 固定比例止损
            if profit_ratio < -self.dynamic_stop_loss:
                self.stop_loss_triggered = True
                self.logger.info(f"触发固定比例止损: 亏损 {profit_ratio:.2%}, 阈值 {self.dynamic_stop_loss:.2%}")
                return True
        
        elif stop_loss_type == StopLossType.VOLATILITY:
            # 基于波动率的止损
            if profit_ratio < -self.dynamic_stop_loss:
                self.stop_loss_triggered = True
                self.logger.info(f"触发波动率止损: 亏损 {profit_ratio:.2%}, 阈值 {self.dynamic_stop_loss:.2%}")
                return True
        
        elif stop_loss_type == StopLossType.ATR:
            # 基于ATR的止损
            if atr is not None and atr > 0:
                atr_multiplier = self.stop_loss_config['atr_multiplier']
                atr_stop = atr * atr_multiplier
                if position > 0 and current_price < entry_price - atr_stop:
                    self.stop_loss_triggered = True
                    self.logger.info(f"触发ATR止损(多头): 价格 {current_price}, 止损价 {entry_price - atr_stop}")
                    return True
                elif position < 0 and current_price > entry_price + atr_stop:
                    self.stop_loss_triggered = True
                    self.logger.info(f"触发ATR止损(空头): 价格 {current_price}, 止损价 {entry_price + atr_stop}")
                    return True
            else:
                # 如果没有提供ATR，使用固定比例止损作为后备
                if profit_ratio < -self.dynamic_stop_loss:
                    self.stop_loss_triggered = True
                    self.logger.info(f"触发固定比例止损(ATR不可用): 亏损 {profit_ratio:.2%}, 阈值 {self.dynamic_stop_loss:.2%}")
                    return True
        
        elif stop_loss_type == StopLossType.TRAILING:
            # 追踪止损
            trailing_percent = self.stop_loss_config['trailing_percent']
            if position > 0:  # 多头
                # 计算追踪止损价格
                stop_price = self.highest_price * (1 - trailing_percent)
                if current_price < stop_price:
                    self.stop_loss_triggered = True
                    self.logger.info(f"触发追踪止损(多头): 价格 {current_price}, 止损价 {stop_price}, 最高价 {self.highest_price}")
                    return True
            else:  # 空头
                # 计算追踪止损价格
                stop_price = self.lowest_price * (1 + trailing_percent)
                if current_price > stop_price:
                    self.stop_loss_triggered = True
                    self.logger.info(f"触发追踪止损(空头): 价格 {current_price}, 止损价 {stop_price}, 最低价 {self.lowest_price}")
                    return True
        
        # 检查最大损失止损
        max_loss = self.stop_loss_config['max_loss']
        if profit_ratio < -max_loss:
            self.stop_loss_triggered = True
            self.logger.info(f"触发最大损失止损: 亏损 {profit_ratio:.2%}, 最大损失阈值 {max_loss:.2%}")
            return True
        
        # 检查时间止损
        time_stop = self.stop_loss_config['time_stop']
        if self.current_time - self.position_start_time > time_stop and profit_ratio < 0:
            self.stop_loss_triggered = True
            self.logger.info(f"触发时间止损: 持仓时间 {self.current_time - self.position_start_time}, 阈值 {time_stop}")
            return True
        
        return False

    def check_take_profit(self, current_price: float, position: float, entry_price: float) -> bool:
        """
        检查是否触发止盈

        参数:
            current_price (float): 当前价格
            position (float): 当前仓位
            entry_price (float): 入场价格

        返回:
            bool: 是否触发止盈
        """
        if position == 0 or entry_price == 0:
            return False
        
        # 计算当前盈利比例
        if position > 0:  # 多头
            profit_ratio = (current_price - entry_price) / entry_price
        else:  # 空头
            profit_ratio = (entry_price - current_price) / entry_price
        
        # 根据市场状态确定止盈阈值
        take_profit_threshold = self._get_take_profit_threshold()
        
        # 检查是否触发止盈
        if profit_ratio > take_profit_threshold:
            self.take_profit_triggered = True
            self.logger.info(f"触发止盈: 盈利 {profit_ratio:.2%}, 阈值 {take_profit_threshold:.2%}")
            return True
        
        return False

    def reset(self):
        """重置风险管理器状态"""
        self.position = 0.0
        self.entry_price = 0.0
        self.highest_price = 0.0
        self.lowest_price = float('inf')
        self.position_start_time = 0
        self.current_time = 0
        self.stop_loss_triggered = False
        self.take_profit_triggered = False
        self.dynamic_stop_loss = self.stop_loss_config['threshold']
        self.dynamic_position_size = self.position_sizing_config['max_position']

    def _adjust_risk_parameters(self):
        """根据市场状态调整风险参数"""
        if self.market_condition is None:
            return
        
        # 根据市场状态调整止损阈值
        if self.market_condition == MarketCondition.VOLATILE or self.market_condition == MarketCondition.CRISIS:
            # 高波动市场，收紧止损
            self.dynamic_stop_loss = self.stop_loss_config['threshold'] * 0.7
            self.logger.info(f"高波动市场，收紧止损阈值至 {self.dynamic_stop_loss:.2%}")
        elif self.market_condition == MarketCondition.CALM:
            # 低波动市场，放宽止损
            self.dynamic_stop_loss = self.stop_loss_config['threshold'] * 1.3
            self.logger.info(f"低波动市场，放宽止损阈值至 {self.dynamic_stop_loss:.2%}")
        else:
            # 其他市场，使用默认止损
            self.dynamic_stop_loss = self.stop_loss_config['threshold']
        
        # 根据市场状态调整仓位大小
        if self.market_condition == MarketCondition.BULL:
            # 牛市，增加仓位
            self.dynamic_position_size = min(1.0, self.position_sizing_config['max_position'] * 1.2)
            self.logger.info(f"牛市，增加最大仓位至 {self.dynamic_position_size:.2f}")
        elif self.market_condition == MarketCondition.BEAR or self.market_condition == MarketCondition.CRISIS:
            # 熊市或危机市场，减少仓位
            self.dynamic_position_size = self.position_sizing_config['max_position'] * 0.5
            self.logger.info(f"熊市或危机市场，减少最大仓位至 {self.dynamic_position_size:.2f}")
        else:
            # 其他市场，使用默认仓位
            self.dynamic_position_size = self.position_sizing_config['max_position']

    def _adjust_position_by_market_condition(self, position_size: float) -> float:
        """
        根据市场状态调整仓位大小

        参数:
            position_size (float): 原始仓位大小

        返回:
            float: 调整后的仓位大小
        """
        if self.market_condition is None:
            return position_size
        
        # 根据市场状态调整仓位
        if self.market_condition == MarketCondition.BULL:
            # 牛市，增加仓位
            return min(1.0, position_size * 1.2)
        elif self.market_condition == MarketCondition.BEAR:
            # 熊市，减少仓位
            return position_size * 0.5
        elif self.market_condition == MarketCondition.VOLATILE:
            # 高波动市场，减少仓位
            return position_size * 0.7
        elif self.market_condition == MarketCondition.CALM:
            # 低波动市场，增加仓位
            return min(1.0, position_size * 1.1)
        elif self.market_condition == MarketCondition.CRISIS:
            # 危机市场，大幅减少仓位
            return position_size * 0.3
        else:
            return position_size

    def _get_take_profit_threshold(self) -> float:
        """
        根据市场状态获取止盈阈值

        返回:
            float: 止盈阈值
        """
        # 默认止盈阈值
        default_threshold = 0.1  # 10%
        
        if self.market_condition is None:
            return default_threshold
        
        # 根据市场状态调整止盈阈值
        if self.market_condition == MarketCondition.BULL:
            # 牛市，放宽止盈
            return default_threshold * 1.5
        elif self.market_condition == MarketCondition.BEAR:
            # 熊市，收紧止盈
            return default_threshold * 0.7
        elif self.market_condition == MarketCondition.VOLATILE:
            # 高波动市场，收紧止盈
            return default_threshold * 0.8
        elif self.market_condition == MarketCondition.CALM:
            # 低波动市场，放宽止盈
            return default_threshold * 1.2
        elif self.market_condition == MarketCondition.CRISIS:
            # 危机市场，大幅收紧止盈
            return default_threshold * 0.5
        else:
            return default_threshold
