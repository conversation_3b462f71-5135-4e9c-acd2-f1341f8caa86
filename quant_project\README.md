# DRL量化交易系统

本项目是一个基于深度强化学习的量化交易系统，包含数据处理、特征工程、自动因子挖掘、模型训练和回测等功能。

## 功能特点

- 数据中心与环境配置：数据获取、预处理和环境参数配置
- DRL智能体训练：支持PPO、A2C、DQN等算法的模型训练
- 策略性能评估：策略回测和性能指标分析
- 实况信号决策：实时交易信号生成和决策辅助
- 自动因子挖掘：自动生成和评估交易因子，并应用于训练
- GPU加速支持：自动检测和利用GPU加速训练

## 系统要求

- Python 3.8+
- PyTorch 1.8+
- Stable-Baselines3
- Streamlit
- Pandas, NumPy, Matplotlib, Seaborn
- CUDA (可选，用于GPU加速)

## 快速开始

### 安装依赖

```bash
pip install -r requirements.txt
```

### 启动系统

使用以下命令启动完整版系统(包含自动因子挖掘功能)：

```bash
python run_project.py
```

系统将自动选择包含因子挖掘功能的版本(`main_app_with_factor_mining.py`)启动。
如果该文件不存在，则会退而使用标准版本(`main_app.py`)。

### 访问系统

系统启动后，在浏览器中访问以下地址：

```
http://localhost:8501
```

## 使用流程

1. **数据中心与环境配置**：加载和预处理交易数据
2. **自动因子挖掘**：生成和评估交易因子，选择最佳因子
3. **DRL智能体训练**：使用所选因子训练强化学习模型
4. **策略性能评估**：回测策略并分析性能指标
5. **实况信号决策**：应用模型生成实时交易信号

## 文件结构

```
quant_project/
├── core_logic/             # 核心逻辑模块
│   ├── auto_factor_mining.py  # 自动因子挖掘
│   ├── data_handler.py     # 数据处理
│   ├── drl_agent.py        # 强化学习代理
│   ├── feature_engineer.py # 特征工程
│   └── ...
├── data_cache/             # 数据缓存
├── logs/                   # 日志文件
├── saved_models/           # 保存的模型
├── plots/                  # 生成的图表
├── auto_factor_mining_page.py  # 因子挖掘页面
├── factor_training_utils.py    # 因子训练工具
├── main_app.py             # 主应用程序(标准版)
├── main_app_with_factor_mining.py  # 主应用程序(含因子挖掘)
├── run_project.py          # 项目启动脚本
└── README.md               # 项目说明
```

## 故障排除

如果在使用"自动因子挖掘"功能时页面无法显示，请确保：

1. 使用`run_project.py`启动系统
2. 确认`main_app_with_factor_mining.py`文件存在
3. 检查项目根目录下是否有`auto_factor_mining_page.py`文件
4. 查看日志文件了解详细错误信息

如有任何问题，请参考日志文件或提交issue。
