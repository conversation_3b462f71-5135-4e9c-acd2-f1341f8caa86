#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
量化交易系统
提供数据处理、特征工程、交易环境、DRL智能体、市场分析、风险管理、验证和评估等功能
"""

__version__ = '2.0.0'

# 导入子模块
from quant_trading.data import DataHandler, DataFetcher, DataCleaner, DataValidator, DataCache
from quant_trading.features import FeatureEngineer, FeatureSelector, FeatureImportance
from quant_trading.trading import TradingEnvironment, EnhancedTradingEnvironment, RobustTradingEnvironment
from quant_trading.agents import DRLAgent, RobustDRLAgent, EnhancedDRLAgent, EnsembleLearning
from quant_trading.evaluation import PerformanceAnalyzer, EnhancedPerformanceAnalyzer, ModelEvaluator
from quant_trading.risk import RiskManager, StopLossType, PositionSizingMethod
from quant_trading.validation import TimeSeriesCV, CVMethod, MarketConditionCV, OverfittingDetector
from quant_trading.utils import setup_logger, is_gpu_available, install_gpu_support

# 导出模块
__all__ = [
    # 数据处理
    'DataHandler', 'DataFetcher', 'DataCleaner', 'DataValidator', 'DataCache',
    # 特征工程
    'FeatureEngineer', 'FeatureSelector', 'FeatureImportance',
    # 交易环境
    'TradingEnvironment', 'EnhancedTradingEnvironment', 'RobustTradingEnvironment',
    # 智能体
    'DRLAgent', 'RobustDRLAgent', 'EnhancedDRLAgent', 'EnsembleLearning',
    # 评估
    'PerformanceAnalyzer', 'EnhancedPerformanceAnalyzer', 'ModelEvaluator',
    # 风险管理
    'RiskManager', 'StopLossType', 'PositionSizingMethod',
    # 验证
    'TimeSeriesCV', 'CVMethod', 'MarketConditionCV', 'OverfittingDetector',
    # 工具
    'setup_logger', 'is_gpu_available', 'install_gpu_support'
]
