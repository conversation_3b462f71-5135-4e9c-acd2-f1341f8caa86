"""
UI测试报告生成器

该脚本运行所有UI测试并生成综合测试报告。
"""

import os
import sys
import unittest
import HtmlTestRunner
import logging
import json
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(parent_dir, 'logs', 'ui_test_report.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('ui_test_report')

def run_tests():
    """运行UI测试并生成报告"""
    logger.info("开始运行UI测试")
    
    # 确保报告目录存在
    reports_dir = os.path.join(parent_dir, 'test_reports', 'ui_tests')
    os.makedirs(reports_dir, exist_ok=True)
    
    # 导入测试模块
    from comprehensive_ui_test import ComprehensiveUITest
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_suite.addTest(unittest.makeSuite(ComprehensiveUITest))
    
    # 运行测试并生成报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    runner = HtmlTestRunner.HTMLTestRunner(
        output=reports_dir,
        report_name=f"comprehensive_ui_test_report_{timestamp}",
        combine_reports=True,
        report_title="DRL量化交易系统全面UI测试报告"
    )
    
    result = runner.run(test_suite)
    
    # 输出测试结果摘要
    logger.info(f"测试完成: 运行 {result.testsRun} 个测试")
    logger.info(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    logger.info(f"失败: {len(result.failures)}")
    logger.info(f"错误: {len(result.errors)}")
    
    # 生成JSON格式的测试结果
    test_results = {
        "timestamp": timestamp,
        "total_tests": result.testsRun,
        "passed": result.testsRun - len(result.failures) - len(result.errors),
        "failed": len(result.failures),
        "errors": len(result.errors),
        "failures": [{"test": f[0].id(), "message": str(f[1])} for f in result.failures],
        "error_details": [{"test": e[0].id(), "message": str(e[1])} for e in result.errors]
    }
    
    # 保存测试结果到JSON文件
    json_report_path = os.path.join(reports_dir, f"test_results_{timestamp}.json")
    with open(json_report_path, 'w', encoding='utf-8') as f:
        json.dump(test_results, f, ensure_ascii=False, indent=4)
    
    logger.info(f"测试结果已保存到: {json_report_path}")
    
    # 返回测试结果
    return result, test_results

def generate_summary_report(test_results):
    """生成测试摘要报告"""
    logger.info("生成测试摘要报告")
    
    # 确保报告目录存在
    reports_dir = os.path.join(parent_dir, 'test_reports')
    os.makedirs(reports_dir, exist_ok=True)
    
    # 创建摘要报告
    summary_report = f"""
# DRL量化交易系统UI测试摘要报告

**测试时间**: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## 测试结果摘要

- **总测试数**: {test_results['total_tests']}
- **通过**: {test_results['passed']}
- **失败**: {test_results['failed']}
- **错误**: {test_results['errors']}

## 测试通过率

- **通过率**: {(test_results['passed'] / test_results['total_tests'] * 100):.2f}%

## 失败测试详情

{generate_failure_details(test_results)}

## 错误测试详情

{generate_error_details(test_results)}

## 测试环境

- **操作系统**: Windows
- **浏览器**: Chrome
- **测试框架**: Selenium + unittest

## 建议

{generate_suggestions(test_results)}

"""
    
    # 保存摘要报告
    summary_report_path = os.path.join(reports_dir, "ui_test_summary.md")
    with open(summary_report_path, 'w', encoding='utf-8') as f:
        f.write(summary_report)
    
    logger.info(f"测试摘要报告已保存到: {summary_report_path}")
    
    return summary_report_path

def generate_failure_details(test_results):
    """生成失败测试详情"""
    if not test_results['failures']:
        return "无失败测试"
    
    details = ""
    for i, failure in enumerate(test_results['failures']):
        details += f"{i+1}. **{failure['test']}**\n   - 错误信息: {failure['message']}\n\n"
    
    return details

def generate_error_details(test_results):
    """生成错误测试详情"""
    if not test_results['error_details']:
        return "无错误测试"
    
    details = ""
    for i, error in enumerate(test_results['error_details']):
        details += f"{i+1}. **{error['test']}**\n   - 错误信息: {error['message']}\n\n"
    
    return details

def generate_suggestions(test_results):
    """根据测试结果生成建议"""
    suggestions = []
    
    # 根据测试结果生成建议
    if test_results['passed'] == test_results['total_tests']:
        suggestions.append("所有测试都已通过，UI功能正常工作。")
    else:
        if test_results['failed'] > 0:
            suggestions.append("修复失败的测试用例，特别关注UI元素交互和参数传递问题。")
        
        if test_results['errors'] > 0:
            suggestions.append("解决测试过程中出现的错误，可能是由于UI元素定位问题或页面加载超时导致。")
    
    # 添加一般性建议
    suggestions.append("定期运行UI测试，确保系统功能稳定。")
    suggestions.append("考虑增加更多边界条件和异常输入的测试用例。")
    suggestions.append("优化页面加载和响应时间，提高用户体验。")
    
    return "\n".join([f"- {suggestion}" for suggestion in suggestions])

def main():
    """主函数"""
    logger.info("=" * 80)
    logger.info("UI测试报告生成器")
    logger.info("=" * 80)
    
    # 运行测试
    result, test_results = run_tests()
    
    # 生成摘要报告
    summary_report_path = generate_summary_report(test_results)
    
    # 输出报告路径
    logger.info(f"测试报告已生成: {summary_report_path}")
    
    # 根据测试结果设置退出代码
    if result.wasSuccessful():
        logger.info("所有测试通过")
        sys.exit(0)
    else:
        logger.error("测试失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
