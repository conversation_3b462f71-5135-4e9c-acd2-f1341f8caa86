"""
因子训练工具模块 - 用于连接因子挖掘和模型训练
"""

import streamlit as st
import pandas as pd
import numpy as np
import logging
import time

logger = logging.getLogger('drl_trading')

def setup_auto_training(auto_params):
    """
    根据自动训练参数配置训练环境
    
    参数:
        auto_params (dict): 自动训练参数
        
    返回:
        tuple: (agent_config, env_config, total_timesteps, algorithm)
    """
    # 获取当前配置
    agent_config = st.session_state.get('agent_config', {})
    env_config = st.session_state.get('env_config', {})
    
    # 默认值
    total_timesteps = 100000
    algorithm = "PPO"
    
    # 设置训练步数（基于轮数）
    if 'epochs' in auto_params:
        total_timesteps = auto_params.get('epochs', 100) * 1000  # 根据轮数计算总步数
        logger.info(f"自动训练设置总步数: {total_timesteps}")
    
    # 设置算法
    if 'algorithm' in auto_params:
        algorithm = auto_params.get('algorithm', 'PPO')
        logger.info(f"自动训练设置算法: {algorithm}")
    
    # 设置GPU使用
    if 'use_gpu' in auto_params:
        use_gpu = auto_params.get('use_gpu', True)
        agent_config['use_gpu'] = use_gpu
        logger.info(f"自动训练设置GPU使用: {use_gpu}")
    
    # 设置集成学习
    if 'use_ensemble' in auto_params:
        use_ensemble = auto_params.get('use_ensemble', False)
        if use_ensemble:
            # 确保ensemble配置存在
            if 'ensemble' not in agent_config:
                agent_config['ensemble'] = {}
            
            agent_config['ensemble']['use'] = True
            agent_config['ensemble']['n_models'] = 5  # 默认使用5个模型
            agent_config['ensemble']['voting_method'] = 'weighted'  # 默认使用加权投票
            logger.info("自动训练启用集成学习")
        else:
            # 禁用集成学习
            if 'ensemble' in agent_config:
                agent_config['ensemble']['use'] = False
            logger.info("自动训练禁用集成学习")
    
    # 应用因子
    if 'use_factors' in auto_params and auto_params['use_factors']:
        # 确保环境配置支持这些因子
        # 确保有feature_columns字段
        if 'feature_columns' not in env_config:
            env_config['feature_columns'] = []
        
        # 添加新因子
        for factor in auto_params['use_factors']:
            if factor not in env_config['feature_columns']:
                env_config['feature_columns'].append(factor)
                logger.info(f"自动训练添加因子: {factor}")
        
        logger.info(f"自动训练使用因子数量: {len(auto_params['use_factors'])}")
    
    return agent_config, env_config, total_timesteps, algorithm

def check_auto_train_factors():
    """
    检查是否设置了自动训练与因子
    
    返回:
        bool: 是否需要自动开始训练
    """
    # 防止重复执行
    execution_key = "factor_check_execution_" + str(int(time.time() / 10))  # 创建时间相关的执行键，每10秒刷新一次
    if execution_key in st.session_state:
        logger.debug(f"跳过重复执行检查: {execution_key}")
        return False
    
    # 设置执行标记
    st.session_state[execution_key] = True
    
    auto_start = False
    
    if 'auto_train_with_factors' in st.session_state and st.session_state.auto_train_with_factors and not st.session_state.get('training_in_progress', False):
        # 防止重复执行自动训练
        if 'auto_train_action_executed' in st.session_state and st.session_state.auto_train_action_executed:
            logger.info("自动训练动作已执行过，跳过")
            st.session_state.auto_train_with_factors = False
            return False
        
        logger.info("检测到自动训练标志，准备开始训练")
        auto_start = True
        
        # 标记已执行
        st.session_state.auto_train_action_executed = True
        
        # 获取自动训练参数
        if 'auto_train_params' in st.session_state:
            auto_params = st.session_state.auto_train_params
            auto_algorithm = auto_params.get('algorithm', 'PPO')
            auto_epochs = auto_params.get('epochs', 100)
            auto_factors = auto_params.get('use_factors', [])
            
            logger.info(f"自动训练配置：算法={auto_algorithm}, 训练轮数={auto_epochs}, 因子数量={len(auto_factors)}")
            
            # 检查并应用因子
            if auto_factors and len(auto_factors) > 0:
                # 确保环境配置支持这些因子
                if 'env_config' in st.session_state:
                    env_config = st.session_state.env_config.copy()
                    # 确保有feature_columns字段
                    if 'feature_columns' not in env_config:
                        env_config['feature_columns'] = []
                    
                    # 添加新因子
                    for factor in auto_factors:
                        if factor not in env_config['feature_columns']:
                            env_config['feature_columns'].append(factor)
                            logger.info(f"添加因子到环境配置: {factor}")
                    
                    # 更新环境配置
                    st.session_state.env_config = env_config
                    logger.info(f"更新环境配置，当前特征列数量: {len(env_config['feature_columns'])}")
                
                # 检查因子是否存在于处理后的数据中
                if 'processed_data' in st.session_state and st.session_state.processed_data is not None:
                    processed_data = st.session_state.processed_data
                    missing_factors = [f for f in auto_factors if f not in processed_data.columns]
                    
                    if missing_factors:
                        logger.warning(f"以下因子在处理后的数据中缺失: {missing_factors}")
                        
                        # 尝试重新处理数据，确保包含所有因子
                        if 'data' in st.session_state and st.session_state.data is not None:
                            try:
                                from quant_project.core_logic.enhanced_feature_engineer import EnhancedFeatureEngineer
                                feature_config = st.session_state.get('feature_config', {})
                                feature_engineer = EnhancedFeatureEngineer(feature_config)
                                
                                logger.info("重新处理数据以包含所有因子")
                                # 确保生成所有的因子，不应用特征选择
                                new_processed_data = feature_engineer.generate_features(
                                    st.session_state.data,
                                    apply_selection=False
                                )
                                
                                # 更新处理后的数据
                                st.session_state.processed_data = new_processed_data
                                logger.info(f"数据重新处理完成，形状: {new_processed_data.shape}")
                                
                                # 再次检查因子是否存在
                                still_missing = [f for f in auto_factors if f not in new_processed_data.columns]
                                if still_missing:
                                    logger.warning(f"重新处理后仍有因子缺失: {still_missing}")
                                else:
                                    logger.info("所有因子已成功包含在处理后的数据中")
                            except Exception as e:
                                logger.error(f"重新处理数据时出错: {str(e)}")
            
            # 设置训练参数
            st.session_state.algorithm = auto_algorithm
            st.session_state.total_timesteps = auto_epochs * 1000  # 根据轮数设置总步数
            logger.info(f"设置算法为 {auto_algorithm}, 总步数为 {auto_epochs * 1000}")
            
            # 设置GPU和集成学习选项
            if 'agent_config' in st.session_state:
                agent_config = st.session_state.agent_config
                
                # 设置GPU使用
                use_gpu = auto_params.get('use_gpu', True)
                agent_config['use_gpu'] = use_gpu
                logger.info(f"设置GPU使用: {use_gpu}")
                
                # 设置集成学习
                use_ensemble = auto_params.get('use_ensemble', False)
                if use_ensemble:
                    # 确保ensemble配置存在
                    if 'ensemble' not in agent_config:
                        agent_config['ensemble'] = {}
                    
                    agent_config['ensemble']['use'] = True
                    agent_config['ensemble']['n_models'] = 5  # 默认使用5个模型
                    agent_config['ensemble']['voting_method'] = 'weighted'  # 默认使用加权投票
                    logger.info("启用集成学习")
                else:
                    # 禁用集成学习
                    if 'ensemble' in agent_config:
                        agent_config['ensemble']['use'] = False
                    logger.info("禁用集成学习")
                
                # 更新智能体配置
                st.session_state.agent_config = agent_config
            
            # 设置自动开始训练标志
            st.session_state.auto_start_training = True
            logger.info("设置自动开始训练标志")
    
    return auto_start 