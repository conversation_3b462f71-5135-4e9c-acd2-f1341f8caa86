#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
修复相对导入脚本
用于修复剩余的相对导入
"""

import os
import re
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fix_relative_imports.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('fix_relative_imports')

# 项目根目录
PROJECT_ROOT = Path(os.getcwd())
# 新项目目录
PROJECT_DIR = PROJECT_ROOT / 'quant_trading'

# 需要修复的文件和对应的导入映射
FILES_TO_FIX = {
    'quant_trading/agents/robust_drl_agent.py': {
        'from .ensemble_learning': 'from quant_trading.agents.ensemble_learning'
    },
    'quant_trading/data/optimized_data_handler_adapter.py': {
        'from .optimized_data_handler': 'from quant_trading.data.optimized_data_handler'
    },
    'quant_trading/features/optimized_feature_engineering_adapter.py': {
        'from .optimized_feature_engineering': 'from quant_trading.features.optimized_feature_engineering'
    },
    'quant_trading/trading/robust_trading_environment.py': {
        'from .observation': 'from quant_trading.trading.observation',
        'from .reward': 'from quant_trading.trading.reward',
        'from .action': 'from quant_trading.trading.action',
        'from .rendering': 'from quant_trading.trading.rendering'
    },
    'quant_trading/validation/market_condition_cv.py': {
        'from .time_series_cv': 'from quant_trading.validation.time_series_cv'
    },
    'quant_trading/validation/overfitting_detector.py': {
        'from .time_series_cv': 'from quant_trading.validation.time_series_cv'
    }
}

def fix_file(file_path, import_mapping):
    """修复文件中的相对导入"""
    logger.info(f"修复文件中的相对导入: {file_path}")
    
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 备份原始内容
        original_content = content
        
        # 应用导入映射
        for old_import, new_import in import_mapping.items():
            content = content.replace(old_import, new_import)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"已修复文件: {file_path}")
            return True
        else:
            logger.info(f"文件无需修复: {file_path}")
            return False
    
    except Exception as e:
        logger.error(f"修复文件 {file_path} 时出错: {str(e)}")
        return False

def run():
    """运行修复相对导入脚本"""
    logger.info("开始修复相对导入")
    
    # 修复每个文件中的相对导入
    fixed_count = 0
    for file_rel_path, import_mapping in FILES_TO_FIX.items():
        file_path = PROJECT_ROOT / file_rel_path
        if file_path.exists():
            if fix_file(file_path, import_mapping):
                fixed_count += 1
        else:
            logger.warning(f"文件不存在: {file_path}")
    
    logger.info(f"相对导入修复完成，共修复 {fixed_count}/{len(FILES_TO_FIX)} 个文件")
    
    return True

if __name__ == "__main__":
    run()
