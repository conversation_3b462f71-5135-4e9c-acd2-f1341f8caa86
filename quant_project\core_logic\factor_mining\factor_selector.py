"""
因子选择器模块
负责选择最优因子集合，去除冗余因子
"""

import logging
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import re

class FactorSelector:
    """因子选择器，选择最优因子集合，去除冗余因子"""
    
    def __init__(self):
        """初始化因子选择器"""
        self.logger = logging.getLogger('drl_trading')
    
    def remove_highly_correlated(self, factors, threshold=0.7, method='keep_highest_ic'):
        """
        移除高度相关的因子
        
        参数:
            factors (dict): 因子字典
            threshold (float): 相关性阈值
            method (str): 冲突解决方法，可选 'keep_highest_ic', 'keep_first', 'keep_random'
            
        返回:
            dict: 过滤后的因子字典
        """
        self.logger.info(f"开始移除高度相关因子 (阈值: {threshold}, 方法: {method})...")
        
        if len(factors) <= 1:
            return factors
            
        # 将因子转换为DataFrame
        factor_df = pd.DataFrame(factors)
        
        # 计算因子间相关性
        corr_matrix = factor_df.corr()
        
        # 创建上三角矩阵的掩码
        mask = np.triu(np.ones(corr_matrix.shape), k=1).astype(bool)
        
        # 找出高度相关的因子对
        high_corr_pairs = []
        for i in range(len(corr_matrix.columns)):
            for j in range(i+1, len(corr_matrix.columns)):
                if abs(corr_matrix.iloc[i, j]) > threshold:
                    factor_i = corr_matrix.columns[i]
                    factor_j = corr_matrix.columns[j]
                    corr_value = corr_matrix.iloc[i, j]
                    high_corr_pairs.append((factor_i, factor_j, abs(corr_value)))
        
        self.logger.info(f"发现 {len(high_corr_pairs)} 对高度相关因子对")
        
        if not high_corr_pairs:
            return factors
        
        # 排序相关对，从高到低
        high_corr_pairs.sort(key=lambda x: x[2], reverse=True)
        
        # 根据选择方法决定保留哪个因子
        to_remove = set()
        
        for factor_i, factor_j, corr_value in high_corr_pairs:
            # 如果两个因子都已经在移除列表中，跳过
            if factor_i in to_remove and factor_j in to_remove:
                continue
            
            # 如果其中一个因子已经在移除列表中，保留另一个
            if factor_i in to_remove:
                continue
            if factor_j in to_remove:
                continue
            
            # 根据方法选择保留哪个因子
            if method == 'keep_first':
                to_remove.add(factor_j)
            elif method == 'keep_random':
                import random
                to_remove.add(random.choice([factor_i, factor_j]))
            elif method == 'keep_highest_ic' and hasattr(self, 'evaluation_results'):
                # 获取评估结果
                eval_results = getattr(self, 'evaluation_results', {})
                
                # 如果有评估结果，保留IC绝对值较高的因子
                ic_i = eval_results.get(factor_i, {}).get('max_abs_ic', 0)
                ic_j = eval_results.get(factor_j, {}).get('max_abs_ic', 0)
                
                if ic_i >= ic_j:
                    to_remove.add(factor_j)
                    self.logger.debug(f"保留 '{factor_i}' (IC: {ic_i:.3f}), 移除 '{factor_j}' (IC: {ic_j:.3f})")
                else:
                    to_remove.add(factor_i)
                    self.logger.debug(f"保留 '{factor_j}' (IC: {ic_j:.3f}), 移除 '{factor_i}' (IC: {ic_i:.3f})")
            else:
                # 默认保留第一个因子
                to_remove.add(factor_j)
        
        # 移除冗余因子
        filtered_factors = {k: v for k, v in factors.items() if k not in to_remove}
        
        self.logger.info(f"移除了 {len(to_remove)} 个冗余因子，剩余 {len(filtered_factors)} 个因子")
        
        # 打印被移除的因子
        self.logger.debug(f"被移除的因子: {list(to_remove)}")
        
        return filtered_factors
    
    def multi_period_filter(self, factors, evaluation_results, min_periods=3, min_periods_ratio=0.6):
        """
        多周期过滤，保留在多个周期都有预测能力的因子
        
        参数:
            factors (dict): 因子字典
            evaluation_results (dict): 评估结果
            min_periods (int): 最少有效周期数
            min_periods_ratio (float): 最少有效周期比例
            
        返回:
            dict: 过滤后的因子字典
        """
        self.logger.info(f"开始多周期过滤 (最少有效周期: {min_periods}, 有效周期比例: {min_periods_ratio})...")
        
        filtered_factors = {}
        min_ic_threshold = 0.05  # 最小IC阈值
        
        for factor_name, factor_value in factors.items():
            if factor_name not in evaluation_results:
                continue
                
            eval_data = evaluation_results[factor_name]
            
            # 检查因子在多个周期是否有预测能力
            significant_periods = 0
            all_periods = len(eval_data['ic_results'])
            
            for period, period_data in eval_data['ic_results'].items():
                if abs(period_data['ic']) >= min_ic_threshold:
                    significant_periods += 1
            
            # 计算有效周期比例
            period_ratio = significant_periods / all_periods if all_periods > 0 else 0
            
            # 如果满足条件，加入过滤后的因子集
            if significant_periods >= min_periods and period_ratio >= min_periods_ratio:
                filtered_factors[factor_name] = factor_value
        
        self.logger.info(f"多周期过滤后剩余 {len(filtered_factors)} 个因子")
        
        return filtered_factors
    
    def select_best_factors(self, factors, evaluation_results, top_n=20, use_composite_score=True):
        """
        选择最佳因子
        
        参数:
            factors (dict): 因子字典
            evaluation_results (dict): 评估结果
            top_n (int): 选择的顶级因子数量
            use_composite_score (bool): 是否使用综合得分，否则使用最大IC绝对值
            
        返回:
            dict: 选择的最佳因子
        """
        self.logger.info(f"选择最佳因子 (top {top_n}, 使用综合得分: {use_composite_score})...")
        
        # 保存评估结果以供其他方法使用
        self.evaluation_results = evaluation_results
        
        # 筛选出在评估结果中有记录的因子
        valid_factors = {k: v for k, v in factors.items() if k in evaluation_results}
        
        if not valid_factors:
            self.logger.warning("没有有效的因子评估结果")
            return {}
        
        # 根据评分对因子排序
        if use_composite_score:
            sorted_factors = sorted(
                valid_factors.items(),
                key=lambda x: evaluation_results[x[0]].get('composite_score', 0),
                reverse=True
            )
        else:
            sorted_factors = sorted(
                valid_factors.items(),
                key=lambda x: evaluation_results[x[0]].get('max_abs_ic', 0),
                reverse=True
            )
        
        # 选择前top_n个因子
        best_factors = dict(sorted_factors[:top_n])
        
        self.logger.info(f"选择了 {len(best_factors)} 个最佳因子")
        
        return best_factors
    
    def combine_factors(self, factors, evaluation_results=None, method='adaptive_weight'):
        """
        组合多个因子为一个因子
        
        参数:
            factors (dict): 因子字典
            evaluation_results (dict): 评估结果
            method (str): 组合方法，可选 'adaptive_weight', 'equal_weight', 'ic_weight'
            
        返回:
            pd.Series: 组合因子
        """
        self.logger.info(f"组合因子 (方法: {method})...")
        
        if not factors:
            self.logger.warning("没有可组合的因子")
            return None
            
        # 获取共同的索引
        all_indices = [f.index for f in factors.values()]
        common_index = all_indices[0].intersection(*all_indices[1:]) if len(all_indices) > 1 else all_indices[0]
        
        # 如果没有共同索引，返回None
        if len(common_index) == 0:
            self.logger.warning("因子没有共同的索引，无法组合")
            return None
        
        # 将所有因子对齐到相同的索引
        aligned_factors = {name: factor.loc[common_index] for name, factor in factors.items()}
        
        # 各因子权重
        weights = {}
        
        # 根据方法设置权重
        if method == 'equal_weight':
            # 等权重
            for name in aligned_factors:
                weights[name] = 1.0 / len(aligned_factors)
                
        elif method == 'ic_weight' and evaluation_results:
            # 根据IC绝对值加权
            total_ic = 0
            for name in aligned_factors:
                if name in evaluation_results:
                    ic = abs(evaluation_results[name].get('best_ic', 0))
                    weights[name] = ic
                    total_ic += ic
                else:
                    weights[name] = 0
            
            # 归一化权重
            if total_ic > 0:
                for name in weights:
                    weights[name] /= total_ic
            else:
                # 如果总IC为0，使用等权重
                for name in aligned_factors:
                    weights[name] = 1.0 / len(aligned_factors)
        
        elif method == 'adaptive_weight' and evaluation_results:
            # 自适应权重，综合考虑IC、稳定性和预测能力
            
            # 提取各项指标
            metrics = {}
            for name in aligned_factors:
                if name in evaluation_results:
                    eval_data = evaluation_results[name]
                    
                    ic = abs(eval_data.get('best_ic', 0))
                    stability = eval_data.get('stability_metrics', {}).get('stability_score', 50) / 100
                    prediction = eval_data.get('prediction_scores', {}).get('prediction_power', 50) / 100
                    
                    # 综合评分
                    score = ic * 0.5 + stability * 0.25 + prediction * 0.25
                    metrics[name] = max(score, 0.01)  # 确保最小权重
                else:
                    metrics[name] = 0.01  # 默认小权重
            
            # 计算总分
            total_score = sum(metrics.values())
            
            # 归一化权重
            for name in aligned_factors:
                weights[name] = metrics[name] / total_score
        
        else:
            # 默认等权重
            for name in aligned_factors:
                weights[name] = 1.0 / len(aligned_factors)
        
        # 记录权重
        self.logger.info(f"因子权重: {weights}")
        
        # 标准化所有因子
        standardized_factors = {}
        for name, factor in aligned_factors.items():
            # 处理可能的NaN和无穷值
            factor = factor.replace([np.inf, -np.inf], np.nan)
            
            # 获取非NaN值
            valid_values = factor.dropna()
            
            if len(valid_values) == 0:
                standardized_factors[name] = pd.Series(0, index=common_index)
                continue
            
            # Z-score标准化
            mean = valid_values.mean()
            std = valid_values.std()
            
            if std == 0:
                standardized = (factor - mean)
            else:
                standardized = (factor - mean) / std
            
            standardized_factors[name] = standardized.fillna(0)
        
        # 根据权重组合因子
        combined_factor = pd.Series(0, index=common_index)
        
        for name, factor in standardized_factors.items():
            combined_factor += factor * weights[name]
        
        # 重新进行标准化
        final_factor = (combined_factor - combined_factor.mean()) / combined_factor.std()
        
        # 返回组合因子
        return final_factor
    
    def select_diverse_factors(self, factors, evaluation_results, categories=None, top_n_per_category=3, top_n_total=15):
        """
        选择多样化的因子集合
        
        参数:
            factors (dict): 因子字典
            evaluation_results (dict): 评估结果
            categories (dict, optional): 因子类别字典，如果为None则自动分类
            top_n_per_category (int): 每个类别选择的顶级因子数量
            top_n_total (int): 总共选择的因子数量
            
        返回:
            dict: 选择的多样化因子集合
        """
        self.logger.info("选择多样化因子集合...")
        
        # 如果没有提供类别信息，自动分类
        if categories is None:
            categories = self._auto_categorize_factors(factors.keys())
        
        # 确定每个类别的有效因子
        valid_factors_by_category = {}
        for category, factor_names in categories.items():
            # 过滤有评估结果的因子
            valid_factors = {name: factors[name] for name in factor_names if name in factors and name in evaluation_results}
            valid_factors_by_category[category] = valid_factors
        
        # 从每个类别选择top_n_per_category个因子
        selected_factors = {}
        for category, valid_factors in valid_factors_by_category.items():
            if not valid_factors:
                continue
                
            # 根据评分排序
            sorted_factors = sorted(
                valid_factors.items(),
                key=lambda x: evaluation_results[x[0]].get('composite_score', 0),
                reverse=True
            )
            
            # 选择顶级因子
            for name, factor in sorted_factors[:top_n_per_category]:
                selected_factors[name] = factor
                
            self.logger.info(f"从类别 '{category}' 选择了 {min(top_n_per_category, len(sorted_factors))} 个因子")
        
        # 如果选择的因子总数超过top_n_total，进一步筛选
        if len(selected_factors) > top_n_total:
            # 根据综合评分再次排序
            sorted_all = sorted(
                selected_factors.items(),
                key=lambda x: evaluation_results[x[0]].get('composite_score', 0),
                reverse=True
            )
            
            # 限制总数
            selected_factors = dict(sorted_all[:top_n_total])
        
        self.logger.info(f"最终选择了 {len(selected_factors)} 个多样化因子")
        
        return selected_factors
    
    def _auto_categorize_factors(self, factor_names):
        """
        自动对因子进行分类
        
        参数:
            factor_names (list): 因子名称列表
            
        返回:
            dict: 分类结果
        """
        categories = {
            'price_based': [],        # 基于价格的因子
            'volume_based': [],       # 基于成交量的因子
            'momentum': [],           # 动量类因子
            'trend': [],              # 趋势类因子
            'volatility': [],         # 波动性因子
            'mean_reversion': [],     # 均值回归类因子
            'technical': [],          # 技术指标类因子
            'candlestick': [],        # 蜡烛图形态类因子
            'cross_factor': [],       # 交叉因子
            'time_series': [],        # 时间序列因子
            'other': []               # 其他因子
        }
        
        # 定义分类关键词
        keywords = {
            'price_based': ['close', 'open', 'high', 'low', 'price'],
            'volume_based': ['volume', 'vol', 'obv'],
            'momentum': ['momentum', 'roc', 'rsi', 'stoch', 'cci', 'williams'],
            'trend': ['sma', 'ema', 'wma', 'vwap', 'slope', 'trend'],
            'volatility': ['volatility', 'atr', 'tr', 'std', 'bb_width'],
            'mean_reversion': ['bb_position', 'z_score'],
            'technical': ['macd', 'kama', 'kdj', 'cci', 'psar'],
            'candlestick': ['shadow', 'body', 'bullish', 'bearish'],
            'cross_factor': ['to_', 'minus_', 'ratio'],
            'time_series': ['lag_', 'diff_', 'rolling_']
        }
        
        # 分类因子
        for factor_name in factor_names:
            categorized = False
            
            # 检查每个类别
            for category, words in keywords.items():
                if any(word in factor_name.lower() for word in words):
                    categories[category].append(factor_name)
                    categorized = True
                    break
            
            # 如果无法分类，归入其他类别
            if not categorized:
                categories['other'].append(factor_name)
        
        # 删除空类别
        categories = {k: v for k, v in categories.items() if v}
        
        self.logger.info(f"自动分类完成，共 {len(categories)} 个类别")
        for category, factors in categories.items():
            self.logger.info(f"类别 '{category}': {len(factors)} 个因子")
        
        return categories 