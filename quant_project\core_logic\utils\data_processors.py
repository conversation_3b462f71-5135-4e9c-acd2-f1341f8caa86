"""
数据处理工具模块
提供异常值处理、特征工程和数据转换功能
"""

import numpy as np
import pandas as pd
from scipy import stats
from typing import Dict, List, Union, Tuple, Optional, Callable
import logging

logger = logging.getLogger('drl_trading')

class DataProcessor:
    """数据处理工具类，提供多种数据处理和特征工程方法"""
    
    @staticmethod
    def handle_outliers(data: pd.DataFrame, 
                        method: str = 'winsorize', 
                        params: Dict = {'limits': [0.01, 0.01]},
                        columns: Optional[List[str]] = None) -> pd.DataFrame:
        """
        处理异常值
        
        参数:
            data: DataFrame 需要处理的数据
            method: str 处理方法 ('winsorize', 'clip', 'remove')
            params: dict 处理参数
            columns: 要处理的列，默认为None表示处理所有数值列
            
        返回:
            处理后的数据框
        """
        processed_data = data.copy()
        
        # 选择要处理的列
        if columns is None:
            # 默认处理所有数值列
            numeric_columns = processed_data.select_dtypes(include=['float64', 'int64']).columns
        else:
            # 只处理指定的列
            numeric_columns = [col for col in columns if col in processed_data.columns 
                               and processed_data[col].dtype in ['float64', 'int64']]
        
        for col in numeric_columns:
            series = processed_data[col]
            
            if method == 'winsorize':
                limits = params.get('limits', [0.01, 0.01])
                processed_data[col] = pd.Series(
                    stats.mstats.winsorize(series.values, limits=limits),
                    index=series.index
                )
                
            elif method == 'clip':
                q_low = series.quantile(params.get('low', 0.01))
                q_high = series.quantile(params.get('high', 0.99))
                processed_data[col] = series.clip(q_low, q_high)
                
            elif method == 'remove':
                q_low = series.quantile(params.get('low', 0.01))
                q_high = series.quantile(params.get('high', 0.99))
                mask = (series >= q_low) & (series <= q_high)
                processed_data.loc[~mask, col] = np.nan
                
            elif method == 'zscore':
                threshold = params.get('threshold', 3)
                z_scores = np.abs((series - series.mean()) / series.std())
                outliers = z_scores > threshold
                if params.get('replace_with_nan', True):
                    processed_data.loc[outliers, col] = np.nan
                else:
                    # 替换为临界值
                    processed_data.loc[outliers & (series > series.mean()), col] = \
                        series.mean() + threshold * series.std()
                    processed_data.loc[outliers & (series < series.mean()), col] = \
                        series.mean() - threshold * series.std()
        
        return processed_data
    
    @staticmethod
    def normalize_features(data: pd.DataFrame, 
                          method: str = 'zscore', 
                          columns: Optional[List[str]] = None,
                          params: Dict = {}) -> pd.DataFrame:
        """
        特征标准化
        
        参数:
            data: DataFrame 需要标准化的数据
            method: str 标准化方法 ('zscore', 'minmax', 'robust')
            columns: 要标准化的列，默认为None表示处理所有数值列
            params: dict 标准化参数
            
        返回:
            标准化后的数据框
        """
        normalized_data = data.copy()
        
        # 选择要处理的列
        if columns is None:
            # 默认处理所有数值列
            numeric_columns = normalized_data.select_dtypes(include=['float64', 'int64']).columns
        else:
            # 只处理指定的列
            numeric_columns = [col for col in columns if col in normalized_data.columns 
                               and normalized_data[col].dtype in ['float64', 'int64']]
        
        for col in numeric_columns:
            series = normalized_data[col]
            
            if method == 'zscore':
                # Z-score标准化 (减均值除以标准差)
                normalized_data[col] = (series - series.mean()) / (series.std() + 1e-8)
                
            elif method == 'minmax':
                # Min-Max标准化 (缩放到[0,1]区间)
                min_val = series.min()
                max_val = series.max()
                if max_val > min_val:
                    normalized_data[col] = (series - min_val) / (max_val - min_val)
                else:
                    normalized_data[col] = 0  # 处理常量列
                    
            elif method == 'robust':
                # 鲁棒标准化 (使用四分位数)
                q1 = series.quantile(0.25)
                q3 = series.quantile(0.75)
                iqr = q3 - q1
                if iqr > 0:
                    normalized_data[col] = (series - series.median()) / (iqr + 1e-8)
                else:
                    normalized_data[col] = (series - series.median())
                    
            elif method == 'decimal':
                # 小数定标标准化
                max_abs = np.max(np.abs(series))
                if max_abs > 0:
                    scale = 10 ** np.ceil(np.log10(max_abs))
                    normalized_data[col] = series / scale
        
        return normalized_data
    
    @staticmethod
    def fill_missing_values(data: pd.DataFrame, 
                           method: str = 'ffill', 
                           columns: Optional[List[str]] = None,
                           params: Dict = {}) -> pd.DataFrame:
        """
        填充缺失值
        
        参数:
            data: DataFrame 需要填充的数据
            method: str 填充方法 ('ffill', 'bfill', 'mean', 'median', 'zero', 'interpolate')
            columns: 要填充的列，默认为None表示处理所有列
            params: dict 填充参数
            
        返回:
            填充后的数据框
        """
        filled_data = data.copy()
        
        # 选择要处理的列
        if columns is None:
            # 默认处理所有列
            target_columns = filled_data.columns
        else:
            # 只处理指定的列
            target_columns = [col for col in columns if col in filled_data.columns]
        
        for col in target_columns:
            series = filled_data[col]
            
            # 检查是否有缺失值
            if not series.isna().any():
                continue
                
            if method == 'ffill':
                # 前向填充
                filled_data[col] = series.ffill()
                # 处理开头的NA值
                if filled_data[col].isna().any():
                    filled_data[col] = filled_data[col].bfill()
                    
            elif method == 'bfill':
                # 后向填充
                filled_data[col] = series.bfill()
                # 处理结尾的NA值
                if filled_data[col].isna().any():
                    filled_data[col] = filled_data[col].ffill()
                    
            elif method == 'mean':
                # 均值填充
                if pd.api.types.is_numeric_dtype(series):
                    filled_data[col] = series.fillna(series.mean())
                else:
                    # 非数值列使用众数填充
                    filled_data[col] = series.fillna(series.mode()[0] if not series.mode().empty else np.nan)
                    
            elif method == 'median':
                # 中位数填充
                if pd.api.types.is_numeric_dtype(series):
                    filled_data[col] = series.fillna(series.median())
                else:
                    # 非数值列使用众数填充
                    filled_data[col] = series.fillna(series.mode()[0] if not series.mode().empty else np.nan)
                    
            elif method == 'zero':
                # 零值填充
                filled_data[col] = series.fillna(0)
                
            elif method == 'interpolate':
                # 插值填充
                method_param = params.get('interp_method', 'linear')
                filled_data[col] = series.interpolate(method=method_param)
                # 处理边界NA值
                filled_data[col] = filled_data[col].ffill().bfill()
                
            elif method == 'constant':
                # 常数填充
                value = params.get('value', 0)
                filled_data[col] = series.fillna(value)
        
        return filled_data
    
    @staticmethod
    def create_time_features(data: pd.DataFrame) -> pd.DataFrame:
        """
        从日期索引创建时间特征
        
        参数:
            data: DataFrame 带有DatetimeIndex的数据框
            
        返回:
            添加了时间特征的数据框
        """
        if not isinstance(data.index, pd.DatetimeIndex):
            logger.warning("数据框的索引不是DatetimeIndex类型，无法创建时间特征")
            return data
            
        result = data.copy()
        
        # 提取基本时间特征
        result['day_of_week'] = data.index.dayofweek
        result['day_of_month'] = data.index.day
        result['day_of_year'] = data.index.dayofyear
        result['month'] = data.index.month
        result['quarter'] = data.index.quarter
        result['year'] = data.index.year
        result['is_month_start'] = data.index.is_month_start.astype(int)
        result['is_month_end'] = data.index.is_month_end.astype(int)
        result['is_quarter_start'] = data.index.is_quarter_start.astype(int)
        result['is_quarter_end'] = data.index.is_quarter_end.astype(int)
        result['is_year_start'] = data.index.is_year_start.astype(int)
        result['is_year_end'] = data.index.is_year_end.astype(int)
        
        # 创建周期性特征 (使用正弦和余弦变换以保留周期性)
        # 对于星期几
        result['day_of_week_sin'] = np.sin(2 * np.pi * data.index.dayofweek / 7)
        result['day_of_week_cos'] = np.cos(2 * np.pi * data.index.dayofweek / 7)
        
        # 对于月份
        result['month_sin'] = np.sin(2 * np.pi * data.index.month / 12)
        result['month_cos'] = np.cos(2 * np.pi * data.index.month / 12)
        
        # 对于季度
        result['quarter_sin'] = np.sin(2 * np.pi * data.index.quarter / 4)
        result['quarter_cos'] = np.cos(2 * np.pi * data.index.quarter / 4)
        
        return result
    
    @staticmethod
    def calculate_returns(data: pd.DataFrame, 
                         price_col: str = '收盘',
                         periods: List[int] = [1, 5, 10, 20],
                         log_returns: bool = False) -> pd.DataFrame:
        """
        计算多种周期的收益率
        
        参数:
            data: DataFrame 价格数据
            price_col: str 价格列名
            periods: List[int] 需要计算的周期列表
            log_returns: bool 是否计算对数收益率
            
        返回:
            添加了收益率的数据框
        """
        result = data.copy()
        
        if price_col not in result.columns:
            logger.warning(f"价格列 {price_col} 不在数据框中")
            return result
            
        # 计算不同周期的收益率
        for period in periods:
            if log_returns:
                # 对数收益率 log(pt/pt-n)
                result[f'return_{period}d'] = np.log(result[price_col] / result[price_col].shift(period))
            else:
                # 简单收益率 (pt/pt-n) - 1
                result[f'return_{period}d'] = (result[price_col] / result[price_col].shift(period)) - 1
                
        return result
    
    @staticmethod
    def convert_frequency(data: pd.DataFrame, 
                         target_freq: str = 'W', 
                         price_cols: List[str] = ['开盘', '最高', '最低', '收盘'],
                         volume_col: str = '成交量',
                         agg_dict: Optional[Dict] = None) -> pd.DataFrame:
        """
        转换数据频率（例如日线转周线、月线）
        
        参数:
            data: DataFrame 原始数据
            target_freq: str 目标频率 ('W'周线, 'M'月线, 'Q'季线)
            price_cols: List[str] 价格列名列表
            volume_col: str 成交量列名
            agg_dict: Dict 自定义聚合字典
            
        返回:
            转换频率后的数据框
        """
        if not isinstance(data.index, pd.DatetimeIndex):
            logger.warning("数据框的索引不是DatetimeIndex类型，无法转换频率")
            return data
            
        # 默认的聚合方法
        if agg_dict is None:
            agg_dict = {}
            
            # 价格列的默认聚合方法
            for col in price_cols:
                if col in data.columns:
                    if '开盘' in col or col == 'open':
                        agg_dict[col] = 'first'  # 开盘价使用周期第一个值
                    elif '最高' in col or col == 'high':
                        agg_dict[col] = 'max'    # 最高价使用周期最大值
                    elif '最低' in col or col == 'low':
                        agg_dict[col] = 'min'    # 最低价使用周期最小值
                    elif '收盘' in col or col == 'close':
                        agg_dict[col] = 'last'   # 收盘价使用周期最后一个值
                    else:
                        agg_dict[col] = 'mean'   # 其他价格列默认使用平均值
            
            # 成交量列的默认聚合方法
            if volume_col in data.columns:
                agg_dict[volume_col] = 'sum'     # 成交量使用周期总和
        
        # 使用resample进行频率转换
        resampled = data.resample(target_freq)
        
        # 应用聚合方法
        result = resampled.agg(agg_dict)
        
        # 处理可能的边界问题
        # 确保第一行和最后一行数据完整
        first_date = result.index[0]
        last_date = result.index[-1]
        
        # 检查第一个周期是否完整
        first_period_data = data[(data.index >= first_date) & (data.index < first_date + pd.Timedelta(target_freq))]
        if len(first_period_data) < 3:  # 假设至少需要3个数据点才算完整
            result = result.iloc[1:]
            
        # 检查最后一个周期是否完整
        if target_freq == 'W':
            last_period_start = last_date - pd.Timedelta(days=6)
        elif target_freq == 'M':
            last_period_start = last_date.replace(day=1)
        elif target_freq == 'Q':
            quarter_month = (last_date.month - 1) // 3 * 3 + 1
            last_period_start = last_date.replace(month=quarter_month, day=1)
        else:
            last_period_start = last_date  # 对于其他频率，不做特殊处理
            
        last_period_data = data[(data.index >= last_period_start) & (data.index <= last_date)]
        if len(last_period_data) < 3:  # 假设至少需要3个数据点才算完整
            result = result.iloc[:-1]
            
        return result
    
    @staticmethod
    def add_technical_indicators(data: pd.DataFrame,
                               price_col: str = '收盘',
                               volume_col: Optional[str] = '成交量',
                               indicators: List[str] = ['SMA', 'EMA', 'RSI', 'MACD', 'BBANDS']) -> pd.DataFrame:
        """
        添加技术指标
        
        参数:
            data: DataFrame 价格数据
            price_col: str 价格列名
            volume_col: str 成交量列名，如不使用可设为None
            indicators: List[str] 需要添加的指标列表
            
        返回:
            添加了技术指标的数据框
        """
        try:
            # 尝试导入talib库
            import talib
            has_talib = True
        except ImportError:
            has_talib = False
            logger.warning("未安装TA-Lib库，将使用自定义实现计算技术指标")
            
        result = data.copy()
        
        if price_col not in result.columns:
            logger.warning(f"价格列 {price_col} 不在数据框中")
            return result
        
        # 确保价格数据是浮点型
        close_prices = result[price_col].astype(float).values
        
        for indicator in indicators:
            # 使用TA-Lib或自定义实现计算指标
            if indicator == 'SMA':
                # 简单移动平均线
                periods = [5, 10, 20, 60]
                for period in periods:
                    if has_talib:
                        result[f'SMA_{period}'] = talib.SMA(close_prices, timeperiod=period)
                    else:
                        result[f'SMA_{period}'] = result[price_col].rolling(window=period).mean()
                
            elif indicator == 'EMA':
                # 指数移动平均线
                periods = [5, 10, 20, 60]
                for period in periods:
                    if has_talib:
                        result[f'EMA_{period}'] = talib.EMA(close_prices, timeperiod=period)
                    else:
                        result[f'EMA_{period}'] = result[price_col].ewm(span=period, adjust=False).mean()
            
            elif indicator == 'RSI':
                # 相对强弱指数
                periods = [6, 14, 21]
                for period in periods:
                    if has_talib:
                        result[f'RSI_{period}'] = talib.RSI(close_prices, timeperiod=period)
                    else:
                        # 简化版RSI计算
                        delta = result[price_col].diff()
                        gain = delta.where(delta > 0, 0)
                        loss = -delta.where(delta < 0, 0)
                        avg_gain = gain.rolling(window=period).mean()
                        avg_loss = loss.rolling(window=period).mean()
                        rs = avg_gain / avg_loss.replace(0, np.nan)  # 避免除零
                        result[f'RSI_{period}'] = 100 - (100 / (1 + rs))
            
            elif indicator == 'MACD':
                # 移动平均收敛散度
                if has_talib:
                    macd, signal, hist = talib.MACD(close_prices, fastperiod=12, slowperiod=26, signalperiod=9)
                    result['MACD'] = macd
                    result['MACD_signal'] = signal
                    result['MACD_hist'] = hist
                else:
                    # 简化版MACD计算
                    ema12 = result[price_col].ewm(span=12, adjust=False).mean()
                    ema26 = result[price_col].ewm(span=26, adjust=False).mean()
                    result['MACD'] = ema12 - ema26
                    result['MACD_signal'] = result['MACD'].ewm(span=9, adjust=False).mean()
                    result['MACD_hist'] = result['MACD'] - result['MACD_signal']
            
            elif indicator == 'BBANDS':
                # 布林带
                if has_talib:
                    upper, middle, lower = talib.BBANDS(close_prices, timeperiod=20, nbdevup=2, nbdevdn=2)
                    result['BB_upper'] = upper
                    result['BB_middle'] = middle
                    result['BB_lower'] = lower
                else:
                    # 简化版布林带计算
                    result['BB_middle'] = result[price_col].rolling(window=20).mean()
                    result['BB_std'] = result[price_col].rolling(window=20).std()
                    result['BB_upper'] = result['BB_middle'] + 2 * result['BB_std']
                    result['BB_lower'] = result['BB_middle'] - 2 * result['BB_std']
            
            elif indicator == 'ATR':
                # 平均真实范围
                if has_talib and '最高' in result.columns and '最低' in result.columns:
                    result['ATR'] = talib.ATR(result['最高'].values, result['最低'].values, close_prices, timeperiod=14)
                elif '最高' in result.columns and '最低' in result.columns:
                    # 简化版ATR计算
                    high = result['最高']
                    low = result['最低']
                    close = result[price_col]
                    tr1 = high - low
                    tr2 = abs(high - close.shift())
                    tr3 = abs(low - close.shift())
                    tr = pd.DataFrame({'tr1': tr1, 'tr2': tr2, 'tr3': tr3}).max(axis=1)
                    result['ATR'] = tr.rolling(window=14).mean()
            
            elif indicator == 'ADX':
                # 平均趋向指数
                if has_talib and '最高' in result.columns and '最低' in result.columns:
                    result['ADX'] = talib.ADX(result['最高'].values, result['最低'].values, close_prices, timeperiod=14)
            
            elif indicator == 'OBV':
                # 能量潮指标
                if volume_col is not None and volume_col in result.columns:
                    if has_talib:
                        result['OBV'] = talib.OBV(close_prices, result[volume_col].values)
                    else:
                        # 简化版OBV计算
                        obv = [0]
                        for i in range(1, len(result)):
                            if result[price_col].iloc[i] > result[price_col].iloc[i-1]:
                                obv.append(obv[-1] + result[volume_col].iloc[i])
                            elif result[price_col].iloc[i] < result[price_col].iloc[i-1]:
                                obv.append(obv[-1] - result[volume_col].iloc[i])
                            else:
                                obv.append(obv[-1])
                        result['OBV'] = obv
        
        return result 