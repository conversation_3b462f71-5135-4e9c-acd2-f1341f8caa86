"""
特征工程模块
负责从原始行情数据计算技术指标和其他特征
实现了顶尖量化基金使用的高级特征工程技术
"""

import logging
import pandas as pd
import numpy as np
import ta
# import talib - disabled due to compatibility issues
from scipy import stats
from sklearn.feature_selection import mutual_info_regression
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from datetime import datetime, timedelta
import warnings
import os

# 忽略特定警告
warnings.filterwarnings("ignore", category=RuntimeWarning, message=".*invalid value encountered.*")
warnings.filterwarnings("ignore", category=RuntimeWarning, message=".*divide by zero.*")

from quant_trading.utils.common import normalize_data
from quant_trading.data.data_dictionary import DataDictionary

class FeatureEngineer:
    """
    特征工程类
    负责从原始行情数据计算技术指标和其他特征
    """

    def __init__(self, feature_config=None, data_dictionary_path='data_dictionary.json'):
        """
        初始化特征工程器

        参数:
            feature_config (dict): 特征配置字典，指定要计算的技术指标及其参数
            data_dictionary_path (str): 数据字典文件路径
        """
        self.logger = logging.getLogger('drl_trading')

        # 默认特征配置
        default_config = {
            # 基础价格特征
            'price_features': {'use': True},

            # 移动平均线
            'sma': {'use': True, 'periods': [5, 10, 20, 60, 120]},
            'ema': {'use': True, 'periods': [5, 10, 20, 60]},
            'wma': {'use': True, 'periods': [10, 20]},  # 加权移动平均
            'kama': {'use': True, 'periods': [10, 20]},  # 考夫曼自适应移动平均

            # 动量指标
            'rsi': {'use': True, 'periods': [6, 14, 21]},
            'stoch': {'use': True, 'k_period': 14, 'd_period': 3, 'slowing': 3},
            'cci': {'use': True, 'periods': [14, 20]},  # 商品通道指数
            'adx': {'use': True, 'period': 14},  # 平均方向指数
            'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9},
            'mom': {'use': True, 'periods': [10, 20]},  # 动量
            'roc': {'use': True, 'periods': [10, 20]},  # 变动率

            # 波动率指标
            'bbands': {'use': True, 'period': 20, 'std': 2.0},
            'atr': {'use': True, 'periods': [7, 14, 21]},
            'natr': {'use': True, 'period': 14},  # 归一化ATR

            # 成交量指标
            'volume': {'use': True, 'periods': [5, 10, 20]},
            'obv': {'use': True},  # 能量潮
            'ad': {'use': True},   # 累积/派发线
            'adosc': {'use': True, 'fast_period': 3, 'slow_period': 10},  # 震荡指标
            'mfi': {'use': True, 'period': 14},  # 资金流量指标

            # 统计特征
            'rolling_stats': {'use': True, 'windows': [5, 10, 20, 60]},

            # 高级特征
            'fractals': {'use': True},  # 分形指标
            'ichimoku': {'use': True},  # 一目均衡表
            'zigzag': {'use': True, 'deviation': 5},  # 之字形态

            # 市场微观结构特征
            'microstructure': {'use': True},

            # 时间特征
            'time_features': {'use': True},

            # 价格形态特征
            'pattern_recognition': {'use': True},

            # 特征选择
            'feature_selection': {'use': True, 'method': 'mutual_info', 'top_n': 30},

            # 特征归一化
            'normalization': {'use': True, 'method': 'minmax'}
        }

        # 如果提供了配置，则更新默认配置
        if feature_config:
            for category, config in feature_config.items():
                if category in default_config:
                    default_config[category].update(config)
                else:
                    default_config[category] = config

        self.feature_config = default_config

        # 保存生成的特征名称，用于特征选择
        self.feature_names = []

        # 初始化数据字典
        self.data_dictionary = DataDictionary(dictionary_path=data_dictionary_path)

        # 如果数据字典为空，初始化默认数据字典
        if not self.data_dictionary.dictionary.get('features'):
            self.logger.info("初始化默认数据字典")
            self.data_dictionary.initialize_default_dictionary()

    def generate_features(self, data):
        """
        生成特征

        参数:
            data (pandas.DataFrame): 原始行情数据，必须包含OHLCV列
        返回:
            pandas.DataFrame: 添加了特征的数据
        """
        try:
            # 复制数据，避免修改原始数据
            df = data.copy()

            # 确保列名标准化
            df = self._ensure_column_names(df)

            # 重置特征名称列表
            self.feature_names = []

            # 计算基本价格特征
            if self.feature_config.get('price_features', {}).get('use', True):
                df = self._calculate_price_features(df)

            # 根据配置计算技术指标
            df = self._calculate_technical_indicators(df)

            # 计算统计特征
            if self.feature_config.get('rolling_stats', {}).get('use', True):
                df = self._calculate_statistical_features(df)

            # 计算高级特征
            df = self._calculate_advanced_features(df)

            # 计算市场微观结构特征
            df = self._calculate_microstructure_features(df)

            # 计算时间特征
            df = self._calculate_time_features(df)

            # 计算价格形态特征
            df = self._calculate_pattern_features(df)

            # 确保所有特征的长度与索引一致
            columns_to_drop = []
            for col in df.columns:
                if isinstance(df[col], pd.Series):
                    if len(df[col]) != len(df.index):
                        try:
                            self.logger.warning(f"列 {col} 的长度 ({len(df[col])}) 与索引长度 ({len(df.index)}) 不一致，将重新对齐")
                            # 如果列长度小于索引长度，则填充缺失值
                            if len(df[col]) < len(df.index):
                                # 创建一个新的Series，使用原始值并填充缺失值
                                new_series = pd.Series(index=df.index, dtype=df[col].dtype)
                                new_series.loc[df[col].index] = df[col]
                                df[col] = new_series.fillna(method='ffill').fillna(method='bfill')
                            # 如果列长度大于索引长度，则截断
                            else:
                                df[col] = pd.Series(df[col].values[:len(df.index)], index=df.index)
                        except Exception as e:
                            self.logger.error(f"无法对齐列 {col}: {str(e)}，将删除该列")
                            columns_to_drop.append(col)
                else:
                    self.logger.warning(f"列 {col} 不是Series类型，将删除该列")
                    columns_to_drop.append(col)

            # 删除无法对齐的列
            if columns_to_drop:
                self.logger.warning(f"删除 {len(columns_to_drop)} 个无法对齐的列: {columns_to_drop}")
                df = df.drop(columns=columns_to_drop)
                # 从特征名称列表中也删除这些列
                self.feature_names = [f for f in self.feature_names if f not in columns_to_drop]

            # 特征选择
            if self.feature_config.get('feature_selection', {}).get('use', True):
                df = self._select_features(df)

            # 在归一化前，检查并处理无穷大值和极端值
            self.logger.info("检查并处理无穷大值和极端值...")
            for col in df.columns:
                if pd.api.types.is_numeric_dtype(df[col]):
                    # 替换无穷大值为NaN
                    inf_count = np.isinf(df[col]).sum()
                    if inf_count > 0:
                        self.logger.warning(f"列 {col} 中发现 {inf_count} 个无穷大值，已替换为NaN")
                        df[col] = df[col].replace([np.inf, -np.inf], np.nan)

                    # 检查是否有极大值
                    if not df[col].isna().all():
                        mean = df[col].mean()
                        std = df[col].std()
                        if not np.isnan(mean) and not np.isnan(std) and std > 0:
                            # 将超过均值±10倍标准差的值视为异常值，替换为NaN
                            extreme_high = (df[col] > mean + 10*std)
                            extreme_low = (df[col] < mean - 10*std)
                            extreme_count = extreme_high.sum() + extreme_low.sum()
                            if extreme_count > 0:
                                self.logger.warning(f"列 {col} 中发现 {extreme_count} 个极端值，已替换为NaN")
                                df.loc[extreme_high | extreme_low, col] = np.nan

            # 归一化特征
            if self.feature_config.get('normalization', {}).get('use', True):
                try:
                    df = self._normalize_features(df)
                except Exception as e:
                    self.logger.error(f"归一化特征失败: {str(e)}，跳过归一化步骤")
                    # 如果归一化失败，至少确保数据类型正确
                    for col in df.columns:
                        if pd.api.types.is_numeric_dtype(df[col]):
                            df[col] = pd.to_numeric(df[col], errors='coerce')

            # 处理NaN值
            nan_count = df.isna().sum().sum()
            if nan_count > 0:
                self.logger.warning(f"特征中存在 {nan_count} 个NaN值，使用前向填充和后向填充处理")
                # 使用前向填充和后向填充处理NaN值
                df = df.fillna(method='ffill').fillna(method='bfill')

                # 如果仍有NaN值（例如整列都是NaN），用0填充
                remaining_nan = df.isna().sum().sum()
                if remaining_nan > 0:
                    self.logger.warning(f"前向和后向填充后仍有 {remaining_nan} 个NaN值，用0填充")
                    df = df.fillna(0)

            # 最后检查一次是否还有无穷大值
            inf_count = np.isinf(df.values).sum()
            if inf_count > 0:
                self.logger.warning(f"最终检查发现 {inf_count} 个无穷大值，替换为0")
                df = df.replace([np.inf, -np.inf], 0)

            # 更新数据字典
            self._update_data_dictionary()

            # 生成数据字典报告
            try:
                report_path = os.path.join(os.path.dirname(self.data_dictionary.dictionary_path), 'feature_dictionary_report.md')
                self.data_dictionary.generate_data_dictionary_report(output_path=report_path)
                self.logger.info(f"特征字典报告已生成: {report_path}")
            except Exception as e:
                self.logger.warning(f"生成特征字典报告失败: {str(e)}")

            self.logger.info(f"特征生成完成，共 {len(df.columns)} 个特征")
            return df

        except Exception as e:
            self.logger.error(f"特征生成失败: {str(e)}")
            # 返回原始数据，确保流程不中断
            return data

    def _update_data_dictionary(self):
        """
        更新数据字典，添加新生成的特征
        """
        try:
            # 添加基本价格特征
            if '涨跌幅' in self.feature_names:
                self.data_dictionary.add_feature(
                    '涨跌幅', '价格特征',
                    '当日收盘价相对前一日收盘价的变化百分比',
                    'df[\'涨跌幅\'] = df[\'收盘\'].pct_change()',
                    '正值表示上涨，负值表示下跌，数值大小表示变化幅度',
                    tags=['基础特征', '收益率']
                )

            if '对数收益率' in self.feature_names:
                self.data_dictionary.add_feature(
                    '对数收益率', '价格特征',
                    '当日收盘价与前一日收盘价的对数差',
                    'df[\'对数收益率\'] = np.log(df[\'收盘\'] / df[\'收盘\'].shift(1))',
                    '对数收益率具有更好的统计特性，适合长期分析',
                    tags=['基础特征', '收益率']
                )

            # 添加技术指标
            for period in [5, 10, 20, 60, 120]:
                feature_name = f'SMA_{period}'
                if feature_name in self.feature_names:
                    self.data_dictionary.add_feature(
                        feature_name, '技术指标',
                        f'{period}日简单移动平均线',
                        f'df[\'{feature_name}\'] = ta.trend.sma_indicator(df[\'收盘\'], window={period})',
                        f'反映{period}日的价格趋势，高于SMA为看涨信号，低于SMA为看跌信号',
                        tags=['趋势指标', '移动平均线']
                    )

            # 添加RSI指标
            for period in [6, 14, 21]:
                feature_name = f'RSI_{period}'
                if feature_name in self.feature_names:
                    self.data_dictionary.add_feature(
                        feature_name, '技术指标',
                        f'{period}日相对强弱指标',
                        f'df[\'{feature_name}\'] = ta.momentum.rsi(df[\'收盘\'], window={period})',
                        'RSI > 70 表示超买，RSI < 30 表示超卖，可能出现反转',
                        tags=['动量指标', '超买超卖']
                    )

            # 添加MACD指标
            if 'MACD_12_26' in self.feature_names:
                self.data_dictionary.add_feature(
                    'MACD_12_26', '技术指标',
                    'MACD线 (12日EMA - 26日EMA)',
                    'df[\'MACD_12_26\'] = ta.trend.macd(df[\'收盘\'], window_slow=26, window_fast=12)',
                    'MACD > 0 为看涨信号，MACD < 0 为看跌信号，MACD穿越0线为买卖信号',
                    tags=['趋势指标', '动量指标']
                )

            # 添加布林带指标
            if 'BBands_Upper_20' in self.feature_names:
                self.data_dictionary.add_feature(
                    'BBands_Upper_20', '技术指标',
                    '20日布林带上轨 (20日SMA + 2倍标准差)',
                    'df[\'BBands_Upper_20\'] = ta.volatility.bollinger_hband(df[\'收盘\'], window=20, window_dev=2.0)',
                    '价格接近上轨表示可能超买，价格突破上轨表示强势',
                    tags=['波动率指标', '通道指标']
                )

            # 添加ATR指标
            for period in [7, 14, 21]:
                feature_name = f'ATR_{period}'
                if feature_name in self.feature_names:
                    self.data_dictionary.add_feature(
                        feature_name, '技术指标',
                        f'{period}日平均真实波动幅度',
                        f'df[\'{feature_name}\'] = ta.volatility.average_true_range(df[\'最高\'], df[\'最低\'], df[\'收盘\'], window={period})',
                        '反映市场波动性，ATR值越大表示波动越大',
                        tags=['波动率指标', '风险指标']
                    )

            # 添加统计特征
            for window in [5, 10, 20, 60]:
                feature_name = f'Rolling_Volatility_{window}'
                if feature_name in self.feature_names:
                    self.data_dictionary.add_feature(
                        feature_name, '统计特征',
                        f'{window}日滚动波动率',
                        f'df[\'{feature_name}\'] = df[\'涨跌幅\'].rolling(window={window}).std()',
                        '反映价格的不稳定性，高波动率表示市场不确定性增加',
                        tags=['波动率', '风险指标']
                    )

            # 添加高级特征
            if '上分形' in self.feature_names:
                self.data_dictionary.add_feature(
                    '上分形', '高级特征',
                    '价格形成上分形，当前高点高于前后两个周期的高点',
                    'df[\'上分形\'] = ((df[\'最高\'] > df[\'最高\'].shift(1)) & (df[\'最高\'] > df[\'最高\'].shift(2)) & (df[\'最高\'] > df[\'最高\'].shift(-1)) & (df[\'最高\'] > df[\'最高\'].shift(-2))).astype(int)',
                    '上分形表示可能的顶部形成，是潜在的卖出信号',
                    tags=['价格形态', '分形理论']
                )

            # 添加一目均衡表特征
            if '转换线' in self.feature_names:
                self.data_dictionary.add_feature(
                    '转换线', '高级特征',
                    '一目均衡表转换线 (9日高点 + 9日低点) / 2',
                    'df[\'转换线\'] = (df[\'最高\'].rolling(window=9).max() + df[\'最低\'].rolling(window=9).min()) / 2',
                    '转换线上穿基准线为买入信号，下穿为卖出信号',
                    tags=['趋势指标', '一目均衡表']
                )

            # 添加市场微观结构特征
            if '价格波动率比率' in self.feature_names:
                self.data_dictionary.add_feature(
                    '价格波动率比率', '市场微观结构',
                    '高低价差与开盘价的比率',
                    'df[\'价格波动率比率\'] = (df[\'最高\'] - df[\'最低\']) / df[\'开盘\']',
                    '反映日内波动性，高值表示波动剧烈',
                    tags=['波动率', '微观结构']
                )

            # 添加时间特征
            if '月' in self.feature_names:
                self.data_dictionary.add_feature(
                    '月', '时间特征',
                    '月份 (1-12)',
                    'df[\'月\'] = df.index.month',
                    '用于捕捉月度季节性效应',
                    tags=['日历特征', '季节性']
                )

            # 添加价格形态特征
            if '锤子线' in self.feature_names:
                self.data_dictionary.add_feature(
                    '锤子线', '价格形态',
                    '锤子线形态，下影线长，上影线短或没有，实体小',
                    'df[\'锤子线\'] = ((body > 0) & (lower_shadow > 2 * body_abs) & (upper_shadow < 0.1 * body_abs)).astype(int)',
                    '在下跌趋势中出现锤子线是潜在的反转信号',
                    tags=['蜡烛图形态', '反转信号']
                )

            # 保存数据字典
            self.data_dictionary.save_dictionary()
            self.logger.info("数据字典已更新")

        except Exception as e:
            self.logger.warning(f"更新数据字典时出错: {str(e)}")

    def _ensure_column_names(self, df):
        """
        确保列名标准化
        参数:
            df (pandas.DataFrame): 原始数据

        返回:
            pandas.DataFrame: 标准化列名后的数据
        """
        # 英文列名到中文列名的映射
        column_mapping = {
            'open': '开盘',
            'high': '最高',
            'low': '最低',
            'close': '收盘',
            'volume': '成交量'
        }

        # 检查并重命名列
        for eng, chn in column_mapping.items():
            if eng in df.columns and chn not in df.columns:
                df[chn] = df[eng]

        # 确保必要的列存在
        required_columns = ['开盘', '最高', '最低', '收盘', '成交量']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            self.logger.error(f"数据缺少必要的列: {missing_columns}")
            raise ValueError(f"数据缺少必要的列: {missing_columns}")

        return df

    def _calculate_price_features(self, df):
        """
        计算基本价格特征

        参数:
            df (pandas.DataFrame): 原始数据

        返回:
            pandas.DataFrame: 添加了价格特征的数据
        """
        # 确保价格列是数值类型
        price_columns = ['开盘', '最高', '最低', '收盘', '成交量']
        for col in price_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # 计算涨跌幅
        if '涨跌幅' not in df.columns:
            try:
                df['涨跌幅'] = df['收盘'].pct_change()
                # 限制涨跌幅的范围，避免极端值
                df['涨跌幅'] = df['涨跌幅'].replace([np.inf, -np.inf], np.nan).clip(-0.5, 0.5)
                self.feature_names.append('涨跌幅')
            except Exception as e:
                self.logger.warning(f"计算涨跌幅失败: {str(e)}")
                df['涨跌幅'] = 0
                self.feature_names.append('涨跌幅')

        # 计算对数收益率
        try:
            # 确保收盘价为正值
            positive_close = df['收盘'].replace(0, np.nan)
            shifted_close = positive_close.shift(1).replace(0, np.nan)
            # 计算对数收益率，并处理可能的无穷大值
            log_returns = np.log(positive_close / shifted_close)
            df['对数收益率'] = log_returns.replace([np.inf, -np.inf], np.nan).clip(-0.5, 0.5)
            self.feature_names.append('对数收益率')
        except Exception as e:
            self.logger.warning(f"计算对数收益率失败: {str(e)}")
            df['对数收益率'] = 0
            self.feature_names.append('对数收益率')

        # 计算真实波动幅度 (True Range)
        try:
            df['TR'] = ta.volatility.average_true_range(df['最高'], df['最低'], df['收盘'], window=1, fillna=True)
            # 处理可能的无穷大值
            df['TR'] = df['TR'].replace([np.inf, -np.inf], np.nan)
            self.feature_names.append('TR')
        except Exception as e:
            self.logger.warning(f"计算TR失败: {str(e)}，使用简单方法计算")
            try:
                # 使用简单方法计算TR
                high_low = df['最高'] - df['最低']
                high_close = (df['最高'] - df['收盘'].shift(1)).abs()
                low_close = (df['最低'] - df['收盘'].shift(1)).abs()
                df['TR'] = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
                # 处理可能的无穷大值
                df['TR'] = df['TR'].replace([np.inf, -np.inf], np.nan)
                self.feature_names.append('TR')
            except Exception as e2:
                self.logger.warning(f"简单方法计算TR也失败: {str(e2)}")
                df['TR'] = df['最高'] - df['最低']  # 最简单的替代方案
                self.feature_names.append('TR')

        # 计算收盘价相对开盘价的变化
        try:
            # 确保开盘价不为0
            non_zero_open = df['开盘'].replace(0, np.nan)
            df['日内涨跌幅'] = (df['收盘'] - df['开盘']) / non_zero_open
            # 限制范围，避免极端值
            df['日内涨跌幅'] = df['日内涨跌幅'].replace([np.inf, -np.inf], np.nan).clip(-0.5, 0.5)
            self.feature_names.append('日内涨跌幅')
        except Exception as e:
            self.logger.warning(f"计算日内涨跌幅失败: {str(e)}")
            df['日内涨跌幅'] = 0
            self.feature_names.append('日内涨跌幅')

        # 计算高低价差占开盘价的比例
        try:
            # 确保开盘价不为0
            non_zero_open = df['开盘'].replace(0, np.nan)
            df['日内波动率'] = (df['最高'] - df['最低']) / non_zero_open
            # 限制范围，避免极端值
            df['日内波动率'] = df['日内波动率'].replace([np.inf, -np.inf], np.nan).clip(0, 0.5)
            self.feature_names.append('日内波动率')
        except Exception as e:
            self.logger.warning(f"计算日内波动率失败: {str(e)}")
            df['日内波动率'] = 0
            self.feature_names.append('日内波动率')

        # 计算价格相对位置 (当前价格在当日最高最低价之间的位置)
        df['价格相对位置'] = (df['收盘'] - df['最低']) / (df['最高'] - df['最低'] + 1e-10)
        self.feature_names.append('价格相对位置')

        # 计算价格动量 (当前价格与N日前价格的差)
        for period in [1, 3, 5, 10, 20]:
            df[f'价格动量_{period}'] = df['收盘'] - df['收盘'].shift(period)
            self.feature_names.append(f'价格动量_{period}')

        # 计算价格加速度 (当前动量与前一日动量的差)
        for period in [1, 3, 5, 10]:
            momentum_col = f'价格动量_{period}'
            df[f'价格加速度_{period}'] = df[momentum_col] - df[momentum_col].shift(1)
            self.feature_names.append(f'价格加速度_{period}')

        # 计算价格波动率 (过去N日收益率的标准差)
        for period in [5, 10, 20, 60]:
            df[f'价格波动率_{period}'] = df['涨跌幅'].rolling(window=period).std()
            self.feature_names.append(f'价格波动率_{period}')

        # 计算价格趋势强度 (过去N日的价格方向一致性)
        for period in [5, 10, 20]:
            df[f'趋势强度_{period}'] = df['涨跌幅'].rolling(window=period).apply(
                lambda x: np.sum(np.sign(x)) / period, raw=True
            )
            self.feature_names.append(f'趋势强度_{period}')

        # 计算价格突破特征
        for period in [5, 10, 20, 60]:
            # 计算过去N日的最高价和最低价
            df[f'过去{period}日最高'] = df['最高'].rolling(window=period).max()
            df[f'过去{period}日最低'] = df['最低'].rolling(window=period).min()

            # 计算价格相对于过去N日最高价和最低价的位置
            df[f'相对高点_{period}'] = df['收盘'] / df[f'过去{period}日最高'] - 1
            df[f'相对低点_{period}'] = df['收盘'] / df[f'过去{period}日最低'] - 1

            self.feature_names.extend([f'过去{period}日最高', f'过去{period}日最低',
                                      f'相对高点_{period}', f'相对低点_{period}'])

            # 计算突破指标 (当前价格是否突破过去N日的最高价或最低价)
            df[f'突破高点_{period}'] = (df['收盘'] > df[f'过去{period}日最高'].shift(1)).astype(int)
            df[f'突破低点_{period}'] = (df['收盘'] < df[f'过去{period}日最低'].shift(1)).astype(int)

            self.feature_names.extend([f'突破高点_{period}', f'突破低点_{period}'])

        # 计算成交量特征
        df['成交量变化率'] = df['成交量'].pct_change()
        df['成交量相对强度'] = df['成交量'] / df['成交量'].rolling(window=20).mean()

        self.feature_names.extend(['成交量变化率', '成交量相对强度'])

        # 计算价量相关性
        for period in [5, 10, 20]:
            df[f'价量相关_{period}'] = df['涨跌幅'].rolling(window=period).corr(df['成交量变化率'])
            self.feature_names.append(f'价量相关_{period}')

        return df

    def _calculate_technical_indicators(self, df):
        """
        计算技术指标
        参数:
            df (pandas.DataFrame): 原始数据

        返回:
            pandas.DataFrame: 添加了技术指标的数据
        """
        # 简单移动平均线 (SMA)
        if self.feature_config.get('sma', {}).get('use', True):
            periods = self.feature_config.get('sma', {}).get('periods', [5, 20, 60])
            for period in periods:
                df[f'SMA_{period}'] = ta.trend.sma_indicator(df['收盘'], window=period, fillna=True)
                # 计算价格相对SMA的位置
                df[f'收盘/SMA_{period}'] = df['收盘'] / df[f'SMA_{period}']

        # 指数移动平均线(EMA)
        if self.feature_config.get('ema', {}).get('use', True):
            periods = self.feature_config.get('ema', {}).get('periods', [5, 20])
            for period in periods:
                df[f'EMA_{period}'] = ta.trend.ema_indicator(df['收盘'], window=period, fillna=True)

        # 相对强弱指标 (RSI)
        if self.feature_config.get('rsi', {}).get('use', True):
            period = self.feature_config.get('rsi', {}).get('period', 14)
            df[f'RSI_{period}'] = ta.momentum.rsi(df['收盘'], window=period, fillna=True)

        # MACD
        if self.feature_config.get('macd', {}).get('use', True):
            fast = self.feature_config.get('macd', {}).get('fast', 12)
            slow = self.feature_config.get('macd', {}).get('slow', 26)
            signal = self.feature_config.get('macd', {}).get('signal', 9)

            df[f'MACD_{fast}_{slow}'] = ta.trend.macd(df['收盘'], window_slow=slow, window_fast=fast, fillna=True)
            df[f'MACD_Signal_{signal}'] = ta.trend.macd_signal(df['收盘'], window_slow=slow, window_fast=fast, window_sign=signal, fillna=True)
            df[f'MACD_Hist'] = ta.trend.macd_diff(df['收盘'], window_slow=slow, window_fast=fast, window_sign=signal, fillna=True)

        # 布林带
        if self.feature_config.get('bbands', {}).get('use', True):
            period = self.feature_config.get('bbands', {}).get('period', 20)
            std = self.feature_config.get('bbands', {}).get('std', 2.0)

            df[f'BBands_Upper_{period}'] = ta.volatility.bollinger_hband(df['收盘'], window=period, window_dev=std, fillna=True)
            df[f'BBands_Middle_{period}'] = ta.volatility.bollinger_mavg(df['收盘'], window=period, fillna=True)
            df[f'BBands_Lower_{period}'] = ta.volatility.bollinger_lband(df['收盘'], window=period, window_dev=std, fillna=True)

            # 计算价格在布林带中的位置 (0-1)
            upper = df[f'BBands_Upper_{period}']
            lower = df[f'BBands_Lower_{period}']
            df[f'BBands_Position_{period}'] = (df['收盘'] - lower) / (upper - lower + 1e-10)

        # 平均真实波动幅度 (ATR)
        if self.feature_config.get('atr', {}).get('use', True):
            period = self.feature_config.get('atr', {}).get('period', 14)
            df[f'ATR_{period}'] = ta.volatility.average_true_range(
                df['最高'],
                df['最低'],
                df['收盘'],
                window=period,
                fillna=True
            )
            # 计算ATR占收盘价的比例
            df[f'ATR_{period}_Pct'] = df[f'ATR_{period}'] / df['收盘']

        # 随机指标 (Stochastic)
        if self.feature_config.get('stoch', {}).get('use', False):
            k_period = self.feature_config.get('stoch', {}).get('k_period', 14)
            d_period = self.feature_config.get('stoch', {}).get('d_period', 3)
            slowing = self.feature_config.get('stoch', {}).get('slowing', 3)

            df[f'Stoch_K_{k_period}'] = ta.momentum.stoch(df['最高'], df['最低'], df['收盘'], window=k_period, smooth_window=slowing, fillna=True)
            df[f'Stoch_D_{d_period}'] = ta.momentum.stoch_signal(df['最高'], df['最低'], df['收盘'], window=k_period, smooth_window=slowing, fillna=True)

        # 成交量指标
        if self.feature_config.get('volume', {}).get('use', False):
            # 成交量移动平均
            periods = self.feature_config.get('volume', {}).get('periods', [5, 20])
            for period in periods:
                df[f'Volume_SMA_{period}'] = ta.trend.sma_indicator(df['成交量'], window=period, fillna=True)
                # 计算成交量相对其移动平均的比例
                df[f'Volume/SMA_{period}'] = df['成交量'] / df[f'Volume_SMA_{period}']

            # 成交量变化率
            df['Volume_Change'] = df['成交量'].pct_change()

            # 能量潮指标(OBV)
            df['OBV'] = ta.volume.on_balance_volume(df['收盘'], df['成交量'], fillna=True)

        return df

    def _calculate_statistical_features(self, df):
        """
        计算统计特征

        参数:
            df (pandas.DataFrame): 原始数据

        返回:
            pandas.DataFrame: 添加了统计特征的数据
        """
        # 收益率的滚动统计特征
        if self.feature_config.get('rolling_stats', {}).get('use', True):
            windows = self.feature_config.get('rolling_stats', {}).get('windows', [5, 10, 20, 60])

            # 创建一个空的DataFrame来存储所有特征，避免DataFrame碎片化
            all_features = {}
            new_feature_names = []

            for window in windows:
                # 滚动波动率 (标准差)
                all_features[f'Rolling_Volatility_{window}'] = df['涨跌幅'].rolling(window=window).std()
                new_feature_names.append(f'Rolling_Volatility_{window}')

                # 滚动偏度
                all_features[f'Rolling_Skew_{window}'] = df['涨跌幅'].rolling(window=window).skew()
                new_feature_names.append(f'Rolling_Skew_{window}')

                # 滚动峰度
                all_features[f'Rolling_Kurt_{window}'] = df['涨跌幅'].rolling(window=window).kurt()
                new_feature_names.append(f'Rolling_Kurt_{window}')

                # 滚动最大值和最小值
                all_features[f'Rolling_Max_{window}'] = df['收盘'].rolling(window=window).max()
                all_features[f'Rolling_Min_{window}'] = df['收盘'].rolling(window=window).min()
                new_feature_names.extend([f'Rolling_Max_{window}', f'Rolling_Min_{window}'])

                # 滚动平均绝对偏差
                all_features[f'Rolling_MAD_{window}'] = df['涨跌幅'].rolling(window=window).apply(
                    lambda x: np.mean(np.abs(x - np.mean(x))), raw=True
                )
                new_feature_names.append(f'Rolling_MAD_{window}')

                # 滚动Z-score (当前价格相对于滚动窗口的标准化得分)
                rolling_mean = df['收盘'].rolling(window=window).mean()
                rolling_std = df['收盘'].rolling(window=window).std()
                all_features[f'Rolling_ZScore_{window}'] = (df['收盘'] - rolling_mean) / rolling_std
                new_feature_names.append(f'Rolling_ZScore_{window}')

                # 滚动自相关系数
                all_features[f'Rolling_Autocorr_{window}'] = df['涨跌幅'].rolling(window=window).apply(
                    lambda x: x.autocorr(1) if len(x) > 1 else np.nan, raw=False
                )
                new_feature_names.append(f'Rolling_Autocorr_{window}')

                # 滚动收益率的正负比例
                all_features[f'Rolling_PosRatio_{window}'] = df['涨跌幅'].rolling(window=window).apply(
                    lambda x: np.sum(x > 0) / len(x), raw=True
                )
                new_feature_names.append(f'Rolling_PosRatio_{window}')

                # 滚动收益率的上升/下降趋势
                all_features[f'Rolling_Trend_{window}'] = df['涨跌幅'].rolling(window=window).apply(
                    lambda x: np.corrcoef(x, np.arange(len(x)))[0, 1] if len(x) > 1 else np.nan, raw=False
                )
                new_feature_names.append(f'Rolling_Trend_{window}')

                # 滚动夏普比率 (假设无风险利率为0)
                all_features[f'Rolling_Sharpe_{window}'] = df['涨跌幅'].rolling(window=window).mean() / df['涨跌幅'].rolling(window=window).std() * np.sqrt(252)
                new_feature_names.append(f'Rolling_Sharpe_{window}')

                # 滚动最大回撤
                all_features[f'Rolling_MaxDrawdown_{window}'] = df['收盘'].rolling(window=window).apply(
                    lambda x: (x / np.maximum.accumulate(x) - 1).min(), raw=True
                )
                new_feature_names.append(f'Rolling_MaxDrawdown_{window}')

                # 滚动收益率的分位数
                for q in [0.25, 0.75]:
                    all_features[f'Rolling_Quantile_{window}_{int(q*100)}'] = df['涨跌幅'].rolling(window=window).quantile(q)
                    new_feature_names.append(f'Rolling_Quantile_{window}_{int(q*100)}')

            # 一次性创建所有特征的DataFrame
            features_df = pd.DataFrame(all_features, index=df.index)

            # 计算IQR (需要先有分位数特征)
            for window in windows:
                features_df[f'Rolling_IQR_{window}'] = features_df[f'Rolling_Quantile_{window}_75'] - features_df[f'Rolling_Quantile_{window}_25']
                new_feature_names.append(f'Rolling_IQR_{window}')

            # 将所有特征合并到原始DataFrame
            df = pd.concat([df, features_df], axis=1)

            # 更新特征名称列表
            self.feature_names.extend(new_feature_names)

            self.logger.info(f"计算了 {len(new_feature_names)} 个统计特征")

        return df

    def _calculate_advanced_features(self, df):
        """
        计算高级特征，包括分形指标、一目均衡表和之字形态

        参数:
            df (pandas.DataFrame): 原始数据

        返回:
            pandas.DataFrame: 添加了高级特征的数据
        """
        # 创建一个字典来存储所有特征，避免DataFrame碎片化
        all_features = {}
        new_feature_names = []

        # 分形指标 (Fractals)
        if self.feature_config.get('fractals', {}).get('use', True):
            # 上分形: 当前高点高于前后两个周期的高点
            all_features['上分形'] = ((df['最高'] > df['最高'].shift(1)) &
                        (df['最高'] > df['最高'].shift(2)) &
                        (df['最高'] > df['最高'].shift(-1)) &
                        (df['最高'] > df['最高'].shift(-2))).astype(int)
            new_feature_names.append('上分形')

            # 下分形: 当前低点低于前后两个周期的低点
            all_features['下分形'] = ((df['最低'] < df['最低'].shift(1)) &
                        (df['最低'] < df['最低'].shift(2)) &
                        (df['最低'] < df['最低'].shift(-1)) &
                        (df['最低'] < df['最低'].shift(-2))).astype(int)
            new_feature_names.append('下分形')

            # 创建临时DataFrame来计算依赖于上分形和下分形的特征
            temp_df = pd.DataFrame({
                '上分形': all_features['上分形'],
                '下分形': all_features['下分形']
            }, index=df.index)

            # 计算分形间的距离
            all_features['上分形距离'] = temp_df['上分形'].rolling(window=20).apply(
                lambda x: np.argmax(x) if np.sum(x) > 0 else np.nan, raw=True
            )
            new_feature_names.append('上分形距离')

            all_features['下分形距离'] = temp_df['下分形'].rolling(window=20).apply(
                lambda x: np.argmax(x) if np.sum(x) > 0 else np.nan, raw=True
            )
            new_feature_names.append('下分形距离')

        # 一目均衡表 (Ichimoku Cloud)
        if self.feature_config.get('ichimoku', {}).get('use', True):
            # 转换线 (Conversion Line): (9日高点 + 9日低点) / 2
            all_features['转换线'] = (df['最高'].rolling(window=9).max() + df['最低'].rolling(window=9).min()) / 2
            new_feature_names.append('转换线')

            # 基准线 (Base Line): (26日高点 + 26日低点) / 2
            all_features['基准线'] = (df['最高'].rolling(window=26).max() + df['最低'].rolling(window=26).min()) / 2
            new_feature_names.append('基准线')

            # 创建临时DataFrame来计算依赖于转换线和基准线的特征
            temp_df = pd.DataFrame({
                '转换线': all_features['转换线'],
                '基准线': all_features['基准线']
            }, index=df.index)

            # 先行带A (Leading Span A): (转换线 + 基准线) / 2，向前推26天
            all_features['先行带A'] = ((temp_df['转换线'] + temp_df['基准线']) / 2).shift(26)
            new_feature_names.append('先行带A')

            # 先行带B (Leading Span B): (52日高点 + 52日低点) / 2，向前推26天
            all_features['先行带B'] = ((df['最高'].rolling(window=52).max() + df['最低'].rolling(window=52).min()) / 2).shift(26)
            new_feature_names.append('先行带B')

            # 注意：原始的延迟线 (Lagging Span) 需要未来数据 (收盘价向后推26天)，这会引入前视偏差
            # 为避免前视偏差，我们不计算延迟线

            # 计算云层信号
            all_features['云层信号'] = ((df['收盘'] > all_features['先行带A']) &
                                    (df['收盘'] > all_features['先行带B'])).astype(int) - \
                                   ((df['收盘'] < all_features['先行带A']) &
                                    (df['收盘'] < all_features['先行带B'])).astype(int)
            new_feature_names.append('云层信号')

            # 计算转换线与基准线的交叉信号
            all_features['转基交叉'] = ((temp_df['转换线'] > temp_df['基准线']) &
                                    (temp_df['转换线'].shift(1) <= temp_df['基准线'].shift(1))).astype(int) - \
                                   ((temp_df['转换线'] < temp_df['基准线']) &
                                    (temp_df['转换线'].shift(1) >= temp_df['基准线'].shift(1))).astype(int)
            new_feature_names.append('转基交叉')

        # 之字形态 (ZigZag)
        if self.feature_config.get('zigzag', {}).get('use', True):
            deviation = self.feature_config.get('zigzag', {}).get('deviation', 5)

            # 计算价格变化的百分比
            price_changes = df['收盘'].pct_change().abs() * 100

            # 标记转折点 (价格变化超过偏差阈值)
            all_features['转折点'] = (price_changes > deviation).astype(int)
            new_feature_names.append('转折点')

            # 计算自上次转折点以来的价格变化
            all_features['自转折点变化'] = df['收盘'] / df['收盘'].shift(1).where(all_features['转折点'] == 1).ffill() - 1
            new_feature_names.append('自转折点变化')

            # 计算转折点之间的距离
            all_features['转折点距离'] = pd.Series(all_features['转折点']).rolling(window=20).apply(
                lambda x: np.argmax(x) if np.sum(x) > 0 else np.nan, raw=True
            )
            new_feature_names.append('转折点距离')

        # 一次性创建所有特征的DataFrame
        if all_features:
            features_df = pd.DataFrame(all_features, index=df.index)

            # 将所有特征合并到原始DataFrame
            df = pd.concat([df, features_df], axis=1)

            # 更新特征名称列表
            self.feature_names.extend(new_feature_names)

            self.logger.info(f"计算了 {len(new_feature_names)} 个高级特征")

        return df

    def _calculate_microstructure_features(self, df):
        """
        计算市场微观结构特征

        参数:
            df (pandas.DataFrame): 原始数据

        返回:
            pandas.DataFrame: 添加了市场微观结构特征的数据
        """
        if self.feature_config.get('microstructure', {}).get('use', True):
            # 创建一个字典来存储所有特征，避免DataFrame碎片化
            all_features = {}
            new_feature_names = []

            # 价格波动率比率 (高低价差/开盘价)
            all_features['价格波动率比率'] = (df['最高'] - df['最低']) / df['开盘']
            new_feature_names.append('价格波动率比率')

            # 收盘价相对高低价的位置 (0-1)
            all_features['收盘相对位置'] = (df['收盘'] - df['最低']) / (df['最高'] - df['最低'] + 1e-10)
            new_feature_names.append('收盘相对位置')

            # 开盘价相对前日收盘价的缺口
            all_features['开盘缺口'] = (df['开盘'] / df['收盘'].shift(1) - 1) * 100
            new_feature_names.append('开盘缺口')

            # 收盘价相对开盘价的变化
            all_features['日内价格变化'] = (df['收盘'] / df['开盘'] - 1) * 100
            new_feature_names.append('日内价格变化')

            # 成交量相对价格变化的比率 (成交量/价格变化的绝对值)
            all_features['量价比'] = df['成交量'] / (np.abs(df['收盘'] - df['开盘']) + 1e-10)
            new_feature_names.append('量价比')

            # 日内价格方向变化的估计 (基于开盘、最高、最低、收盘价的相对位置)
            all_features['日内方向变化'] = ((df['收盘'] > df['开盘']).astype(int) *
                               (df['最高'] - df['收盘'] > df['开盘'] - df['最低']).astype(int) +
                               (df['收盘'] < df['开盘']).astype(int) *
                               (df['最高'] - df['开盘'] > df['收盘'] - df['最低']).astype(int))
            new_feature_names.append('日内方向变化')

            # 价格跳跃指标 (基于收盘价与前一日收盘价的差异)
            all_features['价格跳跃'] = (np.abs(df['收盘'] - df['收盘'].shift(1)) >
                              2 * df['收盘'].rolling(window=20).std()).astype(int)
            new_feature_names.append('价格跳跃')

            # 成交量跳跃指标 (基于成交量与前一日成交量的差异)
            all_features['成交量跳跃'] = (np.abs(df['成交量'] - df['成交量'].shift(1)) >
                               2 * df['成交量'].rolling(window=20).std()).astype(int)
            new_feature_names.append('成交量跳跃')

            # 计算滚动窗口内的微观结构特征
            for window in [5, 10, 20]:
                # 价格加速度 (价格变化率的变化率)
                all_features[f'价格加速度_{window}'] = df['涨跌幅'].diff().rolling(window=window).mean()
                new_feature_names.append(f'价格加速度_{window}')

                # 成交量加速度 (成交量变化率的变化率)
                all_features[f'成交量加速度_{window}'] = df['成交量'].pct_change().diff().rolling(window=window).mean()
                new_feature_names.append(f'成交量加速度_{window}')

                # 价格波动聚集性 (价格波动率的自相关)
                all_features[f'价格波动聚集_{window}'] = df['涨跌幅'].abs().rolling(window=window).apply(
                    lambda x: x.autocorr(1) if len(x) > 1 else np.nan, raw=False
                )
                new_feature_names.append(f'价格波动聚集_{window}')

                # 成交量波动聚集性 (成交量波动率的自相关)
                all_features[f'成交量波动聚集_{window}'] = df['成交量'].pct_change().abs().rolling(window=window).apply(
                    lambda x: x.autocorr(1) if len(x) > 1 else np.nan, raw=False
                )
                new_feature_names.append(f'成交量波动聚集_{window}')

            # 一次性创建所有特征的DataFrame
            features_df = pd.DataFrame(all_features, index=df.index)

            # 将所有特征合并到原始DataFrame
            df = pd.concat([df, features_df], axis=1)

            # 更新特征名称列表
            self.feature_names.extend(new_feature_names)

            self.logger.info(f"计算了 {len(new_feature_names)} 个微观结构特征")

        return df

    def _calculate_time_features(self, df):
        """
        计算时间特征

        参数:
            df (pandas.DataFrame): 原始数据

        返回:
            pandas.DataFrame: 添加了时间特征的数据
        """
        if self.feature_config.get('time_features', {}).get('use', True):
            # 确保索引是日期类型
            if not isinstance(df.index, pd.DatetimeIndex):
                self.logger.warning("数据索引不是日期类型，无法计算时间特征")
                return df

            # 创建一个字典来存储所有特征，避免DataFrame碎片化
            all_features = {}
            new_feature_names = []

            # 提取日期组件
            all_features['年'] = df.index.year
            new_feature_names.append('年')

            all_features['月'] = df.index.month
            new_feature_names.append('月')

            all_features['日'] = df.index.day
            new_feature_names.append('日')

            all_features['周几'] = df.index.dayofweek  # 0=周一, 6=周日
            new_feature_names.append('周几')

            all_features['季度'] = df.index.quarter
            new_feature_names.append('季度')

            all_features['月初'] = (df.index.day <= 5).astype(int)
            new_feature_names.append('月初')

            all_features['月中'] = ((df.index.day > 5) & (df.index.day <= 25)).astype(int)
            new_feature_names.append('月中')

            all_features['月末'] = (df.index.day > 25).astype(int)
            new_feature_names.append('月末')

            all_features['季初'] = ((df.index.month % 3 == 1) & (df.index.day <= 10)).astype(int)
            new_feature_names.append('季初')

            all_features['季末'] = ((df.index.month % 3 == 0) & (df.index.day >= 20)).astype(int)
            new_feature_names.append('季末')

            all_features['年初'] = ((df.index.month == 1) & (df.index.day <= 15)).astype(int)
            new_feature_names.append('年初')

            all_features['年末'] = ((df.index.month == 12) & (df.index.day >= 15)).astype(int)
            new_feature_names.append('年末')

            # 是否为交易日前后
            all_features['周一'] = (df.index.dayofweek == 0).astype(int)
            new_feature_names.append('周一')

            all_features['周五'] = (df.index.dayofweek == 4).astype(int)
            new_feature_names.append('周五')

            # 计算节假日相关特征 (简化版，实际应用中可以使用更完整的节假日日历)
            # 这里仅作为示例，使用周末作为节假日的近似
            all_features['节假日前'] = all_features['周五']
            new_feature_names.append('节假日前')

            all_features['节假日后'] = all_features['周一']
            new_feature_names.append('节假日后')

            # 计算时间趋势特征
            time_trend = np.arange(len(df))
            all_features['时间趋势'] = (time_trend - time_trend.min()) / (time_trend.max() - time_trend.min() + 1e-10)
            new_feature_names.append('时间趋势')

            # 计算周期性特征 (使用正弦和余弦变换)
            # 年度周期
            all_features['年周期_sin'] = np.sin(2 * np.pi * df.index.dayofyear / 365.25)
            new_feature_names.append('年周期_sin')

            all_features['年周期_cos'] = np.cos(2 * np.pi * df.index.dayofyear / 365.25)
            new_feature_names.append('年周期_cos')

            # 季度周期
            all_features['季周期_sin'] = np.sin(2 * np.pi * (df.index.dayofyear % 91) / 91)
            new_feature_names.append('季周期_sin')

            all_features['季周期_cos'] = np.cos(2 * np.pi * (df.index.dayofyear % 91) / 91)
            new_feature_names.append('季周期_cos')

            # 月度周期
            all_features['月周期_sin'] = np.sin(2 * np.pi * df.index.day / 30.4375)
            new_feature_names.append('月周期_sin')

            all_features['月周期_cos'] = np.cos(2 * np.pi * df.index.day / 30.4375)
            new_feature_names.append('月周期_cos')

            # 周度周期
            all_features['周周期_sin'] = np.sin(2 * np.pi * df.index.dayofweek / 5)
            new_feature_names.append('周周期_sin')

            all_features['周周期_cos'] = np.cos(2 * np.pi * df.index.dayofweek / 5)
            new_feature_names.append('周周期_cos')

            # 一次性创建所有特征的DataFrame
            features_df = pd.DataFrame(all_features, index=df.index)

            # 将所有特征合并到原始DataFrame
            df = pd.concat([df, features_df], axis=1)

            # 更新特征名称列表
            self.feature_names.extend(new_feature_names)

            self.logger.info(f"计算了 {len(new_feature_names)} 个时间特征")

        return df

    def _calculate_pattern_features(self, df):
        """
        计算价格形态特征

        参数:
            df (pandas.DataFrame): 原始数据

        返回:
            pandas.DataFrame: 添加了价格形态特征的数据
        """
        if self.feature_config.get('pattern_recognition', {}).get('use', True):
            try:
                # 创建一个字典来存储所有特征，避免DataFrame碎片化
                all_features = {}
                new_feature_names = []

                # 使用自定义函数识别蜡烛图形态
                # 由于talib不可用，我们使用简化的方法来识别一些基本形态

                # 计算实体和影线
                body = df['收盘'] - df['开盘']
                body_abs = abs(body)
                upper_shadow = df['最高'] - df[['开盘', '收盘']].max(axis=1)
                lower_shadow = df[['开盘', '收盘']].min(axis=1) - df['最低']

                # 计算前一天的数据
                prev_body = body.shift(1)
                prev_body_abs = body_abs.shift(1)
                prev_close = df['收盘'].shift(1)
                prev_open = df['开盘'].shift(1)

                # 看涨形态 - 锤子线 (小实体，长下影线，几乎没有上影线)
                all_features['锤子线'] = ((body > 0) &
                                    (lower_shadow > 2 * body_abs) &
                                    (upper_shadow < 0.1 * body_abs)).astype(int)

                # 看涨吞没 (前一天看跌，当天看涨且完全吞没前一天)
                all_features['看涨吞没'] = ((prev_body < 0) &
                                     (body > 0) &
                                     (df['开盘'] < prev_close) &
                                     (df['收盘'] > prev_open)).astype(int)

                # 简化版早晨之星 (三天形态：看跌，十字星，看涨)
                morning_star_day1 = (prev_body.shift(1) < 0) & (prev_body_abs.shift(1) > df['收盘'].rolling(window=10).std())
                morning_star_day2 = (prev_body_abs < 0.3 * prev_body_abs.shift(1))
                morning_star_day3 = (body > 0) & (body_abs > 0.6 * prev_body_abs.shift(1))
                all_features['早晨之星'] = (morning_star_day1 & morning_star_day2 & morning_star_day3).astype(int)

                # 刺透形态 (前一天看跌，当天看涨且开盘低于前一天收盘，收盘高于前一天中点)
                all_features['刺透形态'] = ((prev_body < 0) &
                                     (body > 0) &
                                     (df['开盘'] < prev_close) &
                                     (df['收盘'] > (prev_open + prev_close) / 2)).astype(int)

                # 看涨三线 (三连阳且每天收盘价都更高)
                three_white_day1 = (body.shift(2) > 0)
                three_white_day2 = (prev_body > 0) & (df['收盘'].shift(1) > df['收盘'].shift(2))
                three_white_day3 = (body > 0) & (df['收盘'] > df['收盘'].shift(1))
                all_features['看涨三线'] = (three_white_day1 & three_white_day2 & three_white_day3).astype(int)

                # 看跌形态 - 乌云盖顶 (前一天看涨，当天开盘高于前一天收盘，但收盘低于前一天中点)
                all_features['乌云盖顶'] = ((prev_body > 0) &
                                     (body < 0) &
                                     (df['开盘'] > prev_close) &
                                     (df['收盘'] < (prev_open + prev_close) / 2)).astype(int)

                # 看跌吞没 (前一天看涨，当天看跌且完全吞没前一天)
                all_features['看跌吞没'] = ((prev_body > 0) &
                                     (body < 0) &
                                     (df['开盘'] > prev_close) &
                                     (df['收盘'] < prev_open)).astype(int)

                # 简化版黄昏之星 (三天形态：看涨，十字星，看跌)
                evening_star_day1 = (prev_body.shift(1) > 0) & (prev_body_abs.shift(1) > df['收盘'].rolling(window=10).std())
                evening_star_day2 = (prev_body_abs < 0.3 * prev_body_abs.shift(1))
                evening_star_day3 = (body < 0) & (body_abs > 0.6 * prev_body_abs.shift(1))
                all_features['黄昏之星'] = (evening_star_day1 & evening_star_day2 & evening_star_day3).astype(int)

                # 看跌三线 (三连阴且每天收盘价都更低)
                three_black_day1 = (body.shift(2) < 0)
                three_black_day2 = (prev_body < 0) & (df['收盘'].shift(1) < df['收盘'].shift(2))
                three_black_day3 = (body < 0) & (df['收盘'] < df['收盘'].shift(1))
                all_features['看跌三线'] = (three_black_day1 & three_black_day2 & three_black_day3).astype(int)

                # 上吊线 (小实体，长下影线，几乎没有上影线，但出现在上升趋势中)
                uptrend = df['收盘'].rolling(window=10).mean() > df['收盘'].rolling(window=20).mean()
                all_features['上吊线'] = ((body < 0) &
                                    (lower_shadow > 2 * body_abs) &
                                    (upper_shadow < 0.1 * body_abs) &
                                    uptrend).astype(int)

                # 十字星 (开盘价和收盘价几乎相同)
                all_features['十字星'] = (body_abs < 0.1 * (df['最高'] - df['最低'])).astype(int)

                # 启明星 (早晨之星的变种，中间日为十字星)
                morning_doji_day1 = (prev_body.shift(1) < 0) & (prev_body_abs.shift(1) > df['收盘'].rolling(window=10).std())
                morning_doji_day2 = (prev_body_abs < 0.1 * (df['最高'].shift(1) - df['最低'].shift(1)))
                morning_doji_day3 = (body > 0) & (body_abs > 0.6 * prev_body_abs.shift(1))
                all_features['启明星'] = (morning_doji_day1 & morning_doji_day2 & morning_doji_day3).astype(int)

                # 黄昏星 (黄昏之星的变种，中间日为十字星)
                evening_doji_day1 = (prev_body.shift(1) > 0) & (prev_body_abs.shift(1) > df['收盘'].rolling(window=10).std())
                evening_doji_day2 = (prev_body_abs < 0.1 * (df['最高'].shift(1) - df['最低'].shift(1)))
                evening_doji_day3 = (body < 0) & (body_abs > 0.6 * prev_body_abs.shift(1))
                all_features['黄昏星'] = (evening_doji_day1 & evening_doji_day2 & evening_doji_day3).astype(int)

                # 添加到特征名称列表
                pattern_features = ['锤子线', '看涨吞没', '早晨之星', '刺透形态', '看涨三线',
                                   '乌云盖顶', '看跌吞没', '黄昏之星', '看跌三线', '上吊线',
                                   '十字星', '启明星', '黄昏星']
                new_feature_names.extend(pattern_features)

                # 将形态信号标准化为 -1 (看跌), 0 (中性), 1 (看涨)
                for col in pattern_features:
                    all_features[col] = np.sign(all_features[col])

                # 创建临时DataFrame来计算依赖于形态特征的特征
                temp_df = pd.DataFrame(all_features, index=df.index)

                # 计算综合形态信号
                bullish_patterns = ['锤子线', '看涨吞没', '早晨之星', '刺透形态', '看涨三线', '启明星']
                bearish_patterns = ['乌云盖顶', '看跌吞没', '黄昏之星', '看跌三线', '上吊线', '黄昏星']

                all_features['看涨形态数'] = temp_df[bullish_patterns].apply(lambda x: np.sum(x > 0), axis=1)
                new_feature_names.append('看涨形态数')

                all_features['看跌形态数'] = temp_df[bearish_patterns].apply(lambda x: np.sum(x < 0), axis=1)
                new_feature_names.append('看跌形态数')

                all_features['形态信号'] = all_features['看涨形态数'] - all_features['看跌形态数']
                new_feature_names.append('形态信号')

                # 创建临时DataFrame来计算持续性特征
                temp_df2 = pd.DataFrame({
                    '看涨形态数': all_features['看涨形态数'],
                    '看跌形态数': all_features['看跌形态数'],
                    '形态信号': all_features['形态信号']
                }, index=df.index)

                # 计算形态的持续性 (过去N天内出现的形态数量)
                for window in [3, 5, 10]:
                    all_features[f'看涨形态持续_{window}'] = temp_df2['看涨形态数'].rolling(window=window).sum()
                    new_feature_names.append(f'看涨形态持续_{window}')

                    all_features[f'看跌形态持续_{window}'] = temp_df2['看跌形态数'].rolling(window=window).sum()
                    new_feature_names.append(f'看跌形态持续_{window}')

                    all_features[f'形态信号持续_{window}'] = temp_df2['形态信号'].rolling(window=window).sum()
                    new_feature_names.append(f'形态信号持续_{window}')

                # 一次性创建所有特征的DataFrame
                features_df = pd.DataFrame(all_features, index=df.index)

                # 将所有特征合并到原始DataFrame
                df = pd.concat([df, features_df], axis=1)

                # 更新特征名称列表
                self.feature_names.extend(new_feature_names)

                self.logger.info(f"计算了 {len(new_feature_names)} 个形态特征")

            except Exception as e:
                self.logger.warning(f"计算价格形态特征时出错: {str(e)}")
                self.logger.info("如果是TA-Lib相关错误，请确保正确安装了TA-Lib库")

        return df

    def _select_features(self, df):
        """
        特征选择

        参数:
            df (pandas.DataFrame): 原始数据

        返回:
            pandas.DataFrame: 特征选择后的数据
        """
        try:
            if self.feature_config.get('feature_selection', {}).get('use', True):
                method = self.feature_config.get('feature_selection', {}).get('method', 'mutual_info')
                top_n = self.feature_config.get('feature_selection', {}).get('top_n', 30)

                # 保留原始价格数据
                price_columns = ['开盘', '最高', '最低', '收盘', '成交量']
                if '成交额' in df.columns:
                    price_columns.append('成交额')

                # 只保留存在的列
                existing_price_columns = [col for col in price_columns if col in df.columns]

                # 获取特征列 - 确保特征名称在self.feature_names中且在df.columns中
                feature_columns = []
                if hasattr(self, 'feature_names') and self.feature_names:
                    feature_columns = [col for col in df.columns if col not in existing_price_columns and col in self.feature_names]
                else:
                    # 如果没有feature_names，使用所有非价格列作为特征
                    feature_columns = [col for col in df.columns if col not in existing_price_columns]

                if len(feature_columns) <= top_n:
                    self.logger.info(f"特征数量 ({len(feature_columns)}) 小于或等于目标数量 ({top_n})，跳过特征选择")
                    return df

                # 准备特征矩阵和目标变量
                X = df[feature_columns].copy()

                # 确保所有特征列的长度一致
                for col in feature_columns:
                    if len(X[col]) != len(df):
                        self.logger.warning(f"列 {col} 的长度 ({len(X[col])}) 与数据框长度 ({len(df)}) 不一致，将重新对齐")
                        X[col] = pd.Series(X[col].values[:len(df)], index=df.index)

                # 使用下一期收益率作为目标变量
                y = df['收盘'].pct_change().shift(-1)

                # 处理缺失值
                X = X.fillna(0)
                y = y.fillna(0)

                # 确保X和y有相同的长度和索引
                X = X.loc[df.index]
                y = y.loc[df.index]

                # 确保没有无穷值
                X = X.replace([np.inf, -np.inf], np.nan).fillna(0)
                y = y.replace([np.inf, -np.inf], np.nan).fillna(0)

                # 特征重要性评分
                if method == 'mutual_info':
                    try:
                        # 检查X和y的长度是否一致
                        if len(X) != len(y):
                            self.logger.warning(f"X和y的长度不一致: X={len(X)}, y={len(y)}，跳过特征选择")
                            return df

                        # 使用互信息计算特征重要性
                        importance = mutual_info_regression(X.values, y.values)

                        # 创建特征重要性数据框
                        feature_importance = pd.DataFrame({
                            'feature': feature_columns,
                            'importance': importance
                        })

                        # 按重要性排序
                        feature_importance = feature_importance.sort_values('importance', ascending=False)

                        # 选择前top_n个特征
                        selected_features = feature_importance['feature'].iloc[:top_n].tolist()

                        self.logger.info(f"特征选择: 从 {len(feature_columns)} 个特征中选择了 {len(selected_features)} 个特征")

                        # 记录特征重要性
                        for i, row in feature_importance.head(10).iterrows():
                            self.logger.info(f"特征 '{row['feature']}' 重要性: {row['importance']:.6f}")

                        # 创建新的数据框，只包含选定的特征和原始价格数据
                        selected_columns = existing_price_columns + selected_features
                        df_selected = df[selected_columns].copy()

                        return df_selected

                    except Exception as e:
                        self.logger.warning(f"特征选择时出错: {str(e)}")
                        self.logger.info("跳过特征选择，返回原始数据")
                        return df
                else:
                    self.logger.warning(f"不支持的特征选择方法: {method}")
                    return df
        except Exception as e:
            self.logger.warning(f"特征选择时出错: {str(e)}")
            self.logger.info("跳过特征选择，返回原始数据")

        return df

    def _normalize_features(self, df):
        """
        归一化特征
        参数:
            df (pandas.DataFrame): 原始数据

        返回:
            pandas.DataFrame: 归一化后的数据
        """
        # 保留原始价格数据
        price_columns = ['开盘', '最高', '最低', '收盘', '成交量']
        # 如果有成交额列，也加入
        if '成交额' in df.columns:
            price_columns.append('成交额')

        # 只保留存在的列
        existing_price_columns = [col for col in price_columns if col in df.columns]
        original_prices = df[existing_price_columns].copy()

        # 归一化其他特征列
        feature_columns = [col for col in df.columns if col not in price_columns]
        if feature_columns:
            # 获取归一化方法
            method = self.feature_config.get('normalization', {}).get('method', 'minmax')
            df[feature_columns] = normalize_data(df[feature_columns], method=method)

            self.logger.info(f"特征归一化完成，使用方法: {method}")

        # 将原始价格数据放回
        for col in existing_price_columns:
            df[col] = original_prices[col]

        return df
