"""
性能分析器测试脚本
用于测试性能分析器的各项指标计算功能
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core_logic.performance_analyzer import PerformanceAnalyzer
from core_logic.utils import setup_logger

# 设置日志
logger = setup_logger(log_file='logs/test_performance_analyzer.log')

def create_test_data():
    """创建测试数据"""
    logger.info("创建测试数据")
    
    # 创建日期范围
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    dates = dates[dates.dayofweek < 5]  # 过滤掉周末
    
    # 创建价格数据
    np.random.seed(42)  # 设置随机种子，确保结果可重现
    
    # 创建基准指数数据（例如，大盘指数）
    benchmark_returns = np.random.normal(0.0005, 0.01, len(dates))  # 日收益率，均值0.05%，标准差1%
    benchmark_values = 1000 * np.cumprod(1 + benchmark_returns)
    
    # 创建策略净值数据
    # 假设策略比基准表现稍好
    strategy_returns = np.random.normal(0.0007, 0.012, len(dates))  # 日收益率，均值0.07%，标准差1.2%
    strategy_values = 1000 * np.cumprod(1 + strategy_returns)
    
    # 创建交易记录
    trades = []
    position = 0
    
    for i in range(len(dates)):
        # 随机生成交易信号
        if i > 0 and np.random.random() < 0.05:  # 5%的概率产生交易
            if position == 0:  # 当前无持仓，买入
                price = benchmark_values[i]
                shares = 10
                position = shares
                trades.append({
                    'date': dates[i],
                    'action': 'buy',
                    'price': price,
                    'shares': shares,
                    'value': price * shares,
                    'commission': price * shares * 0.0003
                })
            else:  # 当前有持仓，卖出
                price = benchmark_values[i]
                shares = position
                position = 0
                trades.append({
                    'date': dates[i],
                    'action': 'sell',
                    'price': price,
                    'shares': shares,
                    'value': price * shares,
                    'commission': price * shares * 0.0003
                })
    
    # 创建DataFrame
    benchmark_df = pd.DataFrame({
        'date': dates,
        'value': benchmark_values
    })
    benchmark_df.set_index('date', inplace=True)
    
    strategy_df = pd.DataFrame({
        'date': dates,
        'value': strategy_values
    })
    strategy_df.set_index('date', inplace=True)
    
    trades_df = pd.DataFrame(trades)
    
    logger.info(f"测试数据创建完成，基准数据: {len(benchmark_df)}条，策略数据: {len(strategy_df)}条，交易记录: {len(trades_df)}条")
    
    return benchmark_df, strategy_df, trades_df

def test_performance_metrics():
    """测试性能指标计算"""
    logger.info("测试性能指标计算")
    
    # 创建测试数据
    benchmark_df, strategy_df, trades_df = create_test_data()
    
    # 创建性能分析器
    analyzer = PerformanceAnalyzer()
    
    # 计算性能指标
    metrics = analyzer.calculate_metrics(
        strategy_values=strategy_df['value'],
        benchmark_values=benchmark_df['value'],
        trades=trades_df,
        risk_free_rate=0.02  # 假设无风险利率为2%
    )
    
    logger.info(f"性能指标计算结果: {metrics}")
    
    # 验证指标是否合理
    validation_results = {}
    
    # 总收益率应该大于-100%
    validation_results['total_return_valid'] = metrics['total_return'] > -1.0
    
    # 年化收益率应该在合理范围内
    validation_results['annual_return_valid'] = -0.5 < metrics['annual_return'] < 0.5
    
    # 夏普比率应该在合理范围内
    validation_results['sharpe_ratio_valid'] = -3.0 < metrics['sharpe_ratio'] < 3.0
    
    # 最大回撤应该在0到100%之间
    validation_results['max_drawdown_valid'] = 0.0 <= metrics['max_drawdown'] <= 1.0
    
    # 胜率应该在0到100%之间
    validation_results['win_rate_valid'] = 0.0 <= metrics['win_rate'] <= 1.0
    
    # 验证结果
    all_valid = all(validation_results.values())
    
    if all_valid:
        logger.info("所有性能指标验证通过")
    else:
        invalid_metrics = [k for k, v in validation_results.items() if not v]
        logger.error(f"以下性能指标验证失败: {invalid_metrics}")
    
    # 保存指标结果
    metrics_df = pd.DataFrame([metrics])
    metrics_df.to_csv('tests/performance_metrics_results.csv', index=False)
    
    # 保存验证结果
    validation_df = pd.DataFrame([validation_results])
    validation_df.to_csv('tests/performance_metrics_validation.csv', index=False)
    
    return metrics, validation_results, all_valid

def test_drawdown_calculation():
    """测试回撤计算"""
    logger.info("测试回撤计算")
    
    # 创建测试数据
    _, strategy_df, _ = create_test_data()
    
    # 创建性能分析器
    analyzer = PerformanceAnalyzer()
    
    # 计算回撤
    drawdowns = analyzer.calculate_drawdowns(strategy_df['value'])
    
    logger.info(f"回撤计算结果: 最大回撤 {drawdowns['max_drawdown']:.2%}, 平均回撤 {drawdowns['avg_drawdown']:.2%}")
    
    # 验证回撤是否合理
    validation_results = {}
    
    # 最大回撤应该在0到100%之间
    validation_results['max_drawdown_valid'] = 0.0 <= drawdowns['max_drawdown'] <= 1.0
    
    # 平均回撤应该在0到最大回撤之间
    validation_results['avg_drawdown_valid'] = 0.0 <= drawdowns['avg_drawdown'] <= drawdowns['max_drawdown']
    
    # 验证结果
    all_valid = all(validation_results.values())
    
    if all_valid:
        logger.info("回撤计算验证通过")
    else:
        invalid_metrics = [k for k, v in validation_results.items() if not v]
        logger.error(f"以下回撤指标验证失败: {invalid_metrics}")
    
    # 保存回撤结果
    drawdowns_df = pd.DataFrame([drawdowns])
    drawdowns_df.to_csv('tests/drawdown_results.csv', index=False)
    
    # 保存验证结果
    validation_df = pd.DataFrame([validation_results])
    validation_df.to_csv('tests/drawdown_validation.csv', index=False)
    
    return drawdowns, validation_results, all_valid

def test_trade_analysis():
    """测试交易分析"""
    logger.info("测试交易分析")
    
    # 创建测试数据
    _, _, trades_df = create_test_data()
    
    # 创建性能分析器
    analyzer = PerformanceAnalyzer()
    
    # 分析交易
    trade_metrics = analyzer.analyze_trades(trades_df)
    
    logger.info(f"交易分析结果: {trade_metrics}")
    
    # 验证交易指标是否合理
    validation_results = {}
    
    # 总交易次数应该大于0
    validation_results['total_trades_valid'] = trade_metrics['total_trades'] > 0
    
    # 胜率应该在0到100%之间
    validation_results['win_rate_valid'] = 0.0 <= trade_metrics['win_rate'] <= 1.0
    
    # 平均持仓时间应该大于0
    validation_results['avg_holding_days_valid'] = trade_metrics['avg_holding_days'] > 0
    
    # 验证结果
    all_valid = all(validation_results.values())
    
    if all_valid:
        logger.info("交易分析验证通过")
    else:
        invalid_metrics = [k for k, v in validation_results.items() if not v]
        logger.error(f"以下交易指标验证失败: {invalid_metrics}")
    
    # 保存交易分析结果
    trade_metrics_df = pd.DataFrame([trade_metrics])
    trade_metrics_df.to_csv('tests/trade_analysis_results.csv', index=False)
    
    # 保存验证结果
    validation_df = pd.DataFrame([validation_results])
    validation_df.to_csv('tests/trade_analysis_validation.csv', index=False)
    
    return trade_metrics, validation_results, all_valid

def run_all_tests():
    """运行所有测试"""
    logger.info("开始运行所有性能分析器测试")
    
    # 确保测试目录存在
    os.makedirs('tests', exist_ok=True)
    
    # 运行各项测试
    metrics, metrics_validation, metrics_valid = test_performance_metrics()
    drawdowns, drawdowns_validation, drawdowns_valid = test_drawdown_calculation()
    trade_metrics, trade_validation, trade_valid = test_trade_analysis()
    
    # 汇总测试结果
    all_valid = metrics_valid and drawdowns_valid and trade_valid
    
    logger.info(f"所有测试完成，测试结果: {'全部通过' if all_valid else '部分失败'}")
    
    # 保存汇总结果
    summary = {
        '性能指标测试': metrics_valid,
        '回撤计算测试': drawdowns_valid,
        '交易分析测试': trade_valid,
        '总体结果': all_valid
    }
    
    summary_df = pd.DataFrame([summary])
    summary_df.to_csv('tests/performance_analyzer_summary.csv', index=False)
    
    return all_valid

if __name__ == "__main__":
    run_all_tests()
