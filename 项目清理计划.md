# 量化交易项目清理计划

本文档概述了清理项目结构和移除冗余代码的计划，以提高代码质量和可维护性。

## 已完成工作

### 1. 重构因子挖掘模块

已将`auto_factor_mining.py`模块拆分为以下文件:

- `factor_mining/factor_generator.py` - 因子生成器
- `factor_mining/factor_evaluator.py` - 因子评估器
- `factor_mining/factor_selector.py` - 因子选择器
- `factor_mining/factor_pipeline.py` - 因子挖掘流水线

### 2. 重构DRL智能体模块

已将DRL智能体相关模块(`drl_agent.py`, `enhanced_drl_agent.py`, `robust_drl_agent.py`)重构为以下文件:

- `drl_agent/agent_base.py` - DRL智能体基类
- `drl_agent/ppo_agent.py` - PPO算法实现
- `drl_agent/a2c_agent.py` - A2C算法实现
- `drl_agent/dqn_agent.py` - DQN算法实现
- `drl_agent/callbacks.py` - 训练回调函数

同时为保持向后兼容性，原始文件现在作为导入文件，保留了相同的接口。

### 3. 重构特征工程模块

已将特征工程相关模块重构为以下文件:

- `feature_engineering/base.py` - 特征工程基类
- `feature_engineering/enhanced.py` - 增强特征工程
- `feature_engineering/price_features.py` - 价格相关特征
- `feature_engineering/technical_indicators.py` - 技术指标特征
- `feature_engineering/statistical_features.py` - 统计特征
- `feature_engineering/time_features.py` - 时间特征
- `feature_engineering/utils.py` - 特征工程工具函数
- `feature_engineering/adapter.py` - 向后兼容适配器

## 待完成工作

### 1. 重构回测模块

将`backtest.py`模块重构为以下文件:

- `backtest/backtest_engine.py` - 回测引擎
- `backtest/performance_metrics.py` - 性能指标计算
- `backtest/visualization.py` - 回测结果可视化
- `backtest/optimization.py` - 策略优化
- `backtest/report_generator.py` - 报告生成

### 2. 整合运行文件

将多个运行文件合并为一个统一的运行文件，支持通过命令行参数选择不同的模式。

## 1. 删除冗余运行文件

保留一个主要运行文件，删除其余重复或过时的运行文件：

- 保留：`quant_project/run_project.py` - 作为主要入口点
- 删除：
  - 根目录下的 `run_project.py` (与 quant_project 目录下的重复)
  - `run_project_fixed.py`
  - `run_with_factor_mining.py`
  - `run_with_patch.py`
  - `simple_run.py`

将 `start_system.py` 中的有用功能合并到保留的运行文件中。

## 2. 整合主应用程序文件

保留一个主应用程序文件，合并功能：

- 保留：`quant_project/main_app.py` - 作为主要应用程序
- 删除：
  - `main_app_with_factor_mining.py`
  - `fixed_main_app.py`

将因子挖掘功能集成到保留的主应用程序文件中。

## 3. 重构过长的核心模块文件

将过长的模块文件拆分为更小的组件：

### `auto_factor_mining.py` (2526行)
拆分为：
- `factor_generator.py` - 因子生成器类
- `factor_evaluator.py` - 因子评估器类
- `factor_selector.py` - 因子选择器类
- `factor_pipeline.py` - 自动因子流水线类

### `drl_agent.py` 系列
整合三个版本 (`drl_agent.py`, `enhanced_drl_agent.py`, `robust_drl_agent.py`) 为单一版本：
- `drl_agent/agent_base.py` - 智能体基类
- `drl_agent/ppo_agent.py` - PPO实现
- `drl_agent/a2c_agent.py` - A2C实现
- `drl_agent/dqn_agent.py` - DQN实现
- `drl_agent/callbacks.py` - 回调函数

### `feature_engineer.py` 系列
整合多个版本为单一结构：
- `feature_engineering/base_features.py` - 基础特征工程
- `feature_engineering/technical_indicators.py` - 技术指标
- `feature_engineering/statistical_features.py` - 统计特征
- `feature_engineering/advanced_features.py` - 高级特征

## 4. 精简测试代码

整合所有测试到测试目录并移除重复的测试：

- 保留：`quant_project/tests/` 目录中的测试
- 删除：根目录和其他位置的重复测试文件
- 整合测试报告到单一位置

## 5. 删除多余的修复和辅助脚本

所有修复功能都应该集成到主代码中，然后删除独立的修复脚本：

- 删除所有 `fix_*.py` 文件
- 删除所有调试文件（`debug_*.py`）
- 合并任何有用的功能到主代码

## 6. 整理文档

整合多个分散的指南和文档：

- 创建统一的 `docs/` 目录
- 将所有指南合并到结构化文档中
- 移除重复的 README 文件
- 删除指导步骤 1-12，将有用内容整合到主文档中

## 7. 重新规划项目结构

最终项目结构应该是：

```
quant_project/
├── README.md                 # 项目主文档
├── run_project.py            # 项目主入口
├── main_app.py               # Streamlit 主应用
├── requirements.txt          # 项目依赖
├── core_logic/               # 核心业务逻辑
│   ├── __init__.py
│   ├── trading_env/          # 交易环境模块
│   ├── data_handling/        # 数据处理模块
│   ├── drl_agent/            # 强化学习智能体
│   ├── feature_engineering/  # 特征工程
│   ├── factor_mining/        # 因子挖掘
│   ├── performance_analyzer/ # 性能分析
│   └── utils.py              # 工具函数
├── tests/                    # 测试代码
├── ui_components/            # UI组件
├── configs/                  # 配置文件
└── docs/                     # 项目文档
```

## 8. 实施优先级

1. 首先构建核心模块的重构计划
2. 创建文件映射和依赖关系图
3. 重构核心模块
4. 删除重复文件
5. 整合和精简测试代码
6. 最后整理文档 