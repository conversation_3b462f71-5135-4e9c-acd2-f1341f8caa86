#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
优化数据处理适配器模块
提供与原始数据处理类兼容的接口
"""

import os
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from quant_trading.data.optimized_data_handler import OptimizedDataHandler

class OptimizedDataHandlerAdapter:
    """
    优化数据处理适配器类
    提供与原始数据处理类兼容的接口
    """

    def __init__(self, cache_dir='data_cache', timezone='Asia/Shanghai',
                 missing_value_strategy='ffill', outlier_detection=True,
                 data_validation=True, adjust_price=True):
        """
        初始化优化数据处理适配器

        参数:
            cache_dir (str): 缓存目录路径
            timezone (str): 时区设置，默认为'Asia/Shanghai'
            missing_value_strategy (str): 缺失值处理策略，可选'ffill'(前向填充),'bfill'(后向填充),'mean'(均值填充),'median'(中位数填充),'none'(不处理)
            outlier_detection (bool): 是否进行异常值检测
            data_validation (bool): 是否进行数据验证
            adjust_price (bool): 是否使用复权价格
        """
        self.logger = logging.getLogger('drl_trading')

        # 创建优化版数据处理器
        self.optimized_handler = OptimizedDataHandler(
            cache_dir=cache_dir,
            timezone=timezone,
            missing_value_strategy=missing_value_strategy,
            outlier_detection=outlier_detection,
            data_validation=data_validation,
            adjust_price=adjust_price,
            use_memory_cache=True,
            parallel_processing=True
        )

        # 保存原始数据处理类的属性
        self.cache_dir = cache_dir
        self.timezone = timezone
        self.missing_value_strategy = missing_value_strategy
        self.outlier_detection = outlier_detection
        self.data_validation = data_validation
        self.adjust_price = adjust_price

        # 保存原始类中的指数映射表
        self.index_map = getattr(self.optimized_handler, 'index_map', {})

    def get_stock_data(self, stock_code, start_date, end_date, frequency='日线', use_cache=True):
        """
        获取金融数据（股票、指数）

        参数:
            stock_code (str): 金融产品代码，格式如下：
                - 股票: 'sh000001' 或 'sz399001'
                - 指数: 'index_000300'，如 'index_000300'
            start_date (str): 开始日期，格式为 'YYYY-MM-DD'
            end_date (str): 结束日期，格式为 'YYYY-MM-DD'
            frequency (str): 数据频率，可选 '日线', '周线', '月线', '小时线', '分钟线'
            use_cache (bool): 是否使用缓存

        返回:
            pandas.DataFrame: 金融数据
        """
        return self.optimized_handler.get_stock_data(stock_code, start_date, end_date, frequency, use_cache)

    def get_data(self, code, start_date, end_date, interval='日线', use_cache=True):
        """
        获取数据（兼容方法）

        参数:
            code (str): 金融产品代码
            start_date (str): 开始日期
            end_date (str): 结束日期
            interval (str): 数据频率
            use_cache (bool): 是否使用缓存

        返回:
            pandas.DataFrame: 金融数据
        """
        return self.get_stock_data(code, start_date, end_date, interval, use_cache)

    def _clean_data(self, data):
        """
        清洗数据

        参数:
            data (pandas.DataFrame): 原始数据

        返回:
            pandas.DataFrame: 清洗后的数据
        """
        return self.optimized_handler._clean_data(data)

    def _resample_data(self, data, freq):
        """
        重采样数据

        参数:
            data (pandas.DataFrame): 原始数据
            freq (str): 重采样频率，'W'表示周，'M'表示月

        返回:
            pandas.DataFrame: 重采样后的数据
        """
        return self.optimized_handler._resample_data(data, freq)

    def get_data_quality_report(self, data):
        """
        获取数据质量报告

        参数:
            data (pandas.DataFrame): 金融数据

        返回:
            dict: 数据质量报告
        """
        if hasattr(self.optimized_handler, 'get_data_quality_report'):
            return self.optimized_handler.get_data_quality_report(data)
        else:
            # 简单实现
            report = {
                'missing_values': data.isnull().sum().to_dict(),
                'data_points': len(data),
                'start_date': data.index[0].strftime('%Y-%m-%d') if len(data) > 0 else None,
                'end_date': data.index[-1].strftime('%Y-%m-%d') if len(data) > 0 else None
            }
            return report

    def get_cache_stats(self):
        """
        获取缓存统计信息

        返回:
            dict: 缓存统计信息
        """
        if hasattr(self.optimized_handler, 'get_cache_stats'):
            return self.optimized_handler.get_cache_stats()
        else:
            # 简单实现
            cache_files = [f for f in os.listdir(self.cache_dir) if f.endswith('.csv')]
            return {
                'cache_files': len(cache_files),
                'cache_size_mb': sum(os.path.getsize(os.path.join(self.cache_dir, f)) for f in cache_files) / (1024 * 1024)
            }

    def clear_cache(self):
        """
        清理过期缓存

        返回:
            int: 清理的缓存文件数量
        """
        if hasattr(self.optimized_handler, 'clear_cache'):
            return self.optimized_handler.clear_cache()
        else:
            # 简单实现
            count = 0
            for f in os.listdir(self.cache_dir):
                if f.endswith('.csv'):
                    os.remove(os.path.join(self.cache_dir, f))
                    count += 1
            return count

    def get_latest_trading_date(self):
        """
        获取最新交易日期

        返回:
            str: 最新交易日期，格式为 'YYYY-MM-DD'
        """
        if hasattr(self.optimized_handler, 'get_latest_trading_date'):
            return self.optimized_handler.get_latest_trading_date()
        else:
            # 简单实现，返回当前日期
            return datetime.now().strftime('%Y-%m-%d')
