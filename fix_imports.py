#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
修复导入语句脚本
用于统一导入方式，避免循环导入
"""

import os
import re
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fix_imports.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('fix_imports')

# 项目根目录
PROJECT_ROOT = Path(os.getcwd())
# 新项目目录
PROJECT_DIR = PROJECT_ROOT / 'quant_trading'

# 导入映射关系
IMPORT_MAPPING = {
    # 旧导入 -> 新导入
    'from core_logic.utils': 'from quant_trading.utils.common',
    'from core_logic.data_handler': 'from quant_trading.data.data_handler',
    'from core_logic.feature_engineer': 'from quant_trading.features.feature_engineer',
    'from core_logic.trading_environment': 'from quant_trading.trading.trading_environment',
    'from core_logic.drl_agent': 'from quant_trading.agents.drl_agent',
    'from core_logic.performance_analyzer': 'from quant_trading.evaluation.performance_analyzer',
    'from core_logic.enhanced_feature_engineer': 'from quant_trading.features.enhanced_feature_engineer',
    'from core_logic.enhanced_trading_environment': 'from quant_trading.trading.enhanced_trading_environment',
    'from core_logic.enhanced_drl_agent': 'from quant_trading.agents.enhanced_drl_agent',
    'from core_logic.enhanced_performance_analyzer': 'from quant_trading.evaluation.enhanced_performance_analyzer',
    'from core_logic.robust_drl_agent': 'from quant_trading.agents.robust_drl_agent',
    'from core_logic.ensemble_learning': 'from quant_trading.agents.ensemble_learning',
    'from core_logic.optimized_data_handler': 'from quant_trading.data.optimized_data_handler',
    'from core_logic.optimized_feature_engineering': 'from quant_trading.features.optimized_feature_engineering',
    'from core_logic.data_handling.': 'from quant_trading.data.',
    'from core_logic.feature_engineering.': 'from quant_trading.features.',
    'from core_logic.trading_env.': 'from quant_trading.trading.',
    'from core_logic.risk_management.': 'from quant_trading.risk.',
    'from core_logic.validation.': 'from quant_trading.validation.',
    'from core_logic.market_analysis.': 'from quant_trading.market.',
    'from core_logic.evaluation.': 'from quant_trading.evaluation.',
    'import core_logic.utils': 'import quant_trading.utils.common',
    'import core_logic.data_handler': 'import quant_trading.data.data_handler',
    'import core_logic.feature_engineer': 'import quant_trading.features.feature_engineer',
    'import core_logic.trading_environment': 'import quant_trading.trading.trading_environment',
    'import core_logic.drl_agent': 'import quant_trading.agents.drl_agent',
    'import core_logic.performance_analyzer': 'import quant_trading.evaluation.performance_analyzer',
    'from .data_handling': 'from quant_trading.data',
    'from .feature_engineering': 'from quant_trading.features',
    'from .trading_env': 'from quant_trading.trading',
    'from .risk_management': 'from quant_trading.risk',
    'from .validation': 'from quant_trading.validation',
    'from .market_analysis': 'from quant_trading.market',
    'from .evaluation': 'from quant_trading.evaluation',
    'from .utils': 'from quant_trading.utils.common',
    'from . import': 'from quant_trading import',
    'from ..': 'from quant_trading',
}

# 相对导入模式
RELATIVE_IMPORT_PATTERNS = [
    r'from \.\w+',
    r'from \.\.\w+',
    r'from \.\.\.\w+',
    r'from \. import',
    r'from \.\. import',
    r'from \.\.\. import',
]

def fix_imports_in_file(file_path):
    """修复文件中的导入语句"""
    logger.info(f"修复文件中的导入语句: {file_path}")
    
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 备份原始内容
        original_content = content
        
        # 应用导入映射
        for old_import, new_import in IMPORT_MAPPING.items():
            content = content.replace(old_import, new_import)
        
        # 检查是否有相对导入
        for pattern in RELATIVE_IMPORT_PATTERNS:
            matches = re.findall(pattern, content)
            if matches:
                logger.warning(f"文件 {file_path} 中仍有相对导入: {matches}")
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            logger.info(f"已修复文件: {file_path}")
            return True
        else:
            logger.info(f"文件无需修复: {file_path}")
            return False
    
    except Exception as e:
        logger.error(f"修复文件 {file_path} 时出错: {str(e)}")
        return False

def fix_all_imports():
    """修复所有文件中的导入语句"""
    logger.info("修复所有文件中的导入语句")
    
    # 获取所有Python文件
    python_files = list(PROJECT_DIR.glob('**/*.py'))
    
    # 修复每个文件中的导入语句
    fixed_count = 0
    for file_path in python_files:
        if fix_imports_in_file(file_path):
            fixed_count += 1
    
    logger.info(f"共修复 {fixed_count}/{len(python_files)} 个文件")
    
    return fixed_count

def run():
    """运行修复导入语句脚本"""
    logger.info("开始修复导入语句")
    
    # 修复所有导入语句
    fixed_count = fix_all_imports()
    
    logger.info(f"导入语句修复完成，共修复 {fixed_count} 个文件")
    
    return True

if __name__ == "__main__":
    run()
