{"timestamp": "2025-05-30T23:51:49.585952", "summary": {"total_tests": 5, "successful_tests": 0, "failed_tests": 5, "success_rate": 0.0}, "successful_tests": {}, "issues_found": [{"test": "数据获取参数验证", "description": "测试模块导入或初始化失败", "error": "No module named 'quant_project.core_logic.drl_agent'", "timestamp": "2025-05-30T23:51:49.583658"}, {"test": "特征工程", "description": "没有可用的测试数据", "error": null, "timestamp": "2025-05-30T23:51:49.584285"}, {"test": "交易环境", "description": "没有可用的特征数据", "error": null, "timestamp": "2025-05-30T23:51:49.584552"}, {"test": "模型训练", "description": "没有可用的交易环境", "error": null, "timestamp": "2025-05-30T23:51:49.584794"}, {"test": "回测功能", "description": "没有可用的训练模型", "error": null, "timestamp": "2025-05-30T23:51:49.585063"}]}