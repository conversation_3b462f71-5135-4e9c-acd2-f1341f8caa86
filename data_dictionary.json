{"version": "1.0.0", "last_updated": "2025-05-13 07:30:20", "features": {"涨跌幅": {"category": "价格特征", "description": "当日收盘价相对前一日收盘价的变化百分比", "calculation_method": "df['涨跌幅'] = df['收盘'].pct_change()", "interpretation": "正值表示上涨，负值表示下跌，数值大小表示变化幅度", "source": "internal", "tags": ["基础特征", "收益率"], "added_date": "2025-05-13 07:30:20"}, "对数收益率": {"category": "价格特征", "description": "当日收盘价与前一日收盘价的对数差", "calculation_method": "df['对数收益率'] = np.log(df['收盘'] / df['收盘'].shift(1))", "interpretation": "对数收益率具有更好的统计特性，适合长期分析", "source": "internal", "tags": ["基础特征", "收益率"], "added_date": "2025-05-13 07:30:20"}, "SMA_20": {"category": "技术指标", "description": "20日简单移动平均线", "calculation_method": "df['SMA_20'] = ta.trend.sma_indicator(df['收盘'], window=20)", "interpretation": "反映中期价格趋势，高于SMA为看涨信号，低于SMA为看跌信号", "source": "internal", "tags": ["趋势指标", "移动平均线"], "added_date": "2025-05-11 16:36:45"}, "RSI_14": {"category": "技术指标", "description": "14日相对强弱指标", "calculation_method": "df['RSI_14'] = ta.momentum.rsi(df['收盘'], window=14)", "interpretation": "RSI > 70 表示超买，RSI < 30 表示超卖，可能出现反转", "source": "internal", "tags": ["动量指标", "超买超卖"], "added_date": "2025-05-11 16:36:45"}, "Rolling_Volatility_20": {"category": "统计特征", "description": "20日滚动波动率", "calculation_method": "df['Rolling_Volatility_20'] = df['涨跌幅'].rolling(window=20).std()", "interpretation": "反映价格的不稳定性，高波动率表示市场不确定性增加", "source": "internal", "tags": ["波动率", "风险指标"], "added_date": "2025-05-13 00:34:56"}, "Rolling_Volatility_5": {"category": "统计特征", "description": "5日滚动波动率", "calculation_method": "df['Rolling_Volatility_5'] = df['涨跌幅'].rolling(window=5).std()", "interpretation": "反映价格的不稳定性，高波动率表示市场不确定性增加", "source": "internal", "tags": ["波动率", "风险指标"], "added_date": "2025-05-13 00:34:56"}, "Rolling_Volatility_10": {"category": "统计特征", "description": "10日滚动波动率", "calculation_method": "df['Rolling_Volatility_10'] = df['涨跌幅'].rolling(window=10).std()", "interpretation": "反映价格的不稳定性，高波动率表示市场不确定性增加", "source": "internal", "tags": ["波动率", "风险指标"], "added_date": "2025-05-13 00:34:56"}, "Rolling_Volatility_60": {"category": "统计特征", "description": "60日滚动波动率", "calculation_method": "df['Rolling_Volatility_60'] = df['涨跌幅'].rolling(window=60).std()", "interpretation": "反映价格的不稳定性，高波动率表示市场不确定性增加", "source": "internal", "tags": ["波动率", "风险指标"], "added_date": "2025-05-13 00:34:56"}, "上分形": {"category": "高级特征", "description": "价格形成上分形，当前高点高于前后两个周期的高点", "calculation_method": "df['上分形'] = ((df['最高'] > df['最高'].shift(1)) & (df['最高'] > df['最高'].shift(2)) & (df['最高'] > df['最高'].shift(-1)) & (df['最高'] > df['最高'].shift(-2))).astype(int)", "interpretation": "上分形表示可能的顶部形成，是潜在的卖出信号", "source": "internal", "tags": ["价格形态", "分形理论"], "added_date": "2025-05-13 07:30:20"}, "转换线": {"category": "高级特征", "description": "一目均衡表转换线 (9日高点 + 9日低点) / 2", "calculation_method": "df['转换线'] = (df['最高'].rolling(window=9).max() + df['最低'].rolling(window=9).min()) / 2", "interpretation": "转换线上穿基准线为买入信号，下穿为卖出信号", "source": "internal", "tags": ["趋势指标", "一目均衡表"], "added_date": "2025-05-13 07:30:20"}, "价格波动率比率": {"category": "市场微观结构", "description": "高低价差与开盘价的比率", "calculation_method": "df['价格波动率比率'] = (df['最高'] - df['最低']) / df['开盘']", "interpretation": "反映日内波动性，高值表示波动剧烈", "source": "internal", "tags": ["波动率", "微观结构"], "added_date": "2025-05-13 07:30:20"}, "月": {"category": "时间特征", "description": "月份 (1-12)", "calculation_method": "df['月'] = df.index.month", "interpretation": "用于捕捉月度季节性效应", "source": "internal", "tags": ["日历特征", "季节性"], "added_date": "2025-05-13 07:30:20"}, "锤子线": {"category": "价格形态", "description": "锤子线形态，下影线长，上影线短或没有，实体小", "calculation_method": "df['锤子线'] = ((body > 0) & (lower_shadow > 2 * body_abs) & (upper_shadow < 0.1 * body_abs)).astype(int)", "interpretation": "在下跌趋势中出现锤子线是潜在的反转信号", "source": "internal", "tags": ["蜡烛图形态", "反转信号"], "added_date": "2025-05-13 07:30:20"}}}