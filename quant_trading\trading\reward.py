"""
奖励计算模块
负责计算交易环境的奖励函数
"""

import logging
import numpy as np
import pandas as pd
from enum import Enum

class Reward:
    """奖励类，用于表示交易环境的奖励"""

    def __init__(self, value=0.0, components=None):
        """
        初始化奖励对象

        参数:
            value (float): 奖励值
            components (dict): 奖励组成部分
        """
        self.value = value
        self.components = components or {}

class RewardCalculator:
    """
    奖励计算类
    负责计算交易环境的奖励函数
    """

    def __init__(self, reward_config=None, logger=None):
        """
        初始化奖励计算器

        参数:
            reward_config (dict): 奖励函数配置
            logger (logging.Logger): 日志记录器
        """
        self.logger = logger or logging.getLogger('drl_trading')

        # 奖励函数配置
        self.reward_config = reward_config or {
            'portfolio_return': 1.0,      # 组合收益权重
            'volatility_penalty': 0.1,    # 波动率惩罚权重
            'drawdown_penalty': 0.2,      # 回撤惩罚权重
            'holding_penalty': 0.05,      # 持仓成本惩罚权重
            'trade_penalty': 0.01         # 交易成本惩罚权重
        }

        # 记录历史奖励
        self.reward_history = []

        # 记录历史组合价值
        self.portfolio_values = []

    def calculate_reward(self, prev_portfolio_value, current_portfolio_value,
                         holding_days=0, trade_cost=0, is_trade=False):
        """
        计算奖励

        参数:
            prev_portfolio_value (float): 上一步的组合价值
            current_portfolio_value (float): 当前组合价值
            holding_days (int): 持仓天数
            trade_cost (float): 交易成本
            is_trade (bool): 是否进行了交易

        返回:
            float: 奖励值
        """
        # 计算组合收益率
        portfolio_return = (current_portfolio_value / prev_portfolio_value) - 1

        # 更新历史组合价值
        self.portfolio_values.append(current_portfolio_value)

        # 计算波动率惩罚
        volatility_penalty = 0
        if len(self.portfolio_values) > 10:
            # 计算最近10个时间步的收益率
            # 修复数组形状不匹配问题：np.diff(array)会返回比原数组少一个元素的数组
            # 使用正确的索引确保两个数组长度相同
            values = np.array(self.portfolio_values[-10:])
            prev_values = np.array(self.portfolio_values[-11:-1])
            returns = (values[1:] - values[:-1]) / prev_values[-9:]
            # 计算波动率
            volatility = np.std(returns)
            # 波动率惩罚
            volatility_penalty = volatility

        # 计算回撤惩罚
        drawdown_penalty = 0
        if len(self.portfolio_values) > 1:
            # 计算当前回撤
            peak = max(self.portfolio_values)
            drawdown = (peak - current_portfolio_value) / peak
            # 回撤惩罚
            drawdown_penalty = drawdown

        # 计算持仓成本惩罚
        holding_penalty = 0
        if holding_days > 0:
            # 持仓成本惩罚随持仓天数增加而增加
            holding_penalty = holding_days / 30  # 假设最长持仓30天

        # 计算交易成本惩罚
        trade_penalty = 0
        if is_trade:
            # 交易成本惩罚
            trade_penalty = trade_cost / current_portfolio_value

        # 计算总奖励
        reward = (
            self.reward_config['portfolio_return'] * portfolio_return -
            self.reward_config['volatility_penalty'] * volatility_penalty -
            self.reward_config['drawdown_penalty'] * drawdown_penalty -
            self.reward_config['holding_penalty'] * holding_penalty -
            self.reward_config['trade_penalty'] * trade_penalty
        )

        # 记录奖励
        self.reward_history.append(reward)

        # 记录奖励组成
        reward_components = {
            'portfolio_return': portfolio_return,
            'volatility_penalty': volatility_penalty,
            'drawdown_penalty': drawdown_penalty,
            'holding_penalty': holding_penalty,
            'trade_penalty': trade_penalty,
            'total_reward': reward
        }

        return reward, reward_components

    def calculate_sharpe_ratio(self, risk_free_rate=0.0):
        """
        计算夏普比率

        参数:
            risk_free_rate (float): 无风险利率

        返回:
            float: 夏普比率
        """
        if len(self.portfolio_values) < 2:
            return 0

        # 计算收益率
        returns = np.diff(self.portfolio_values) / self.portfolio_values[:-1]

        # 计算年化收益率
        annual_return = np.mean(returns) * 252

        # 计算年化波动率
        annual_volatility = np.std(returns) * np.sqrt(252)

        # 计算夏普比率
        if annual_volatility == 0:
            return 0

        sharpe_ratio = (annual_return - risk_free_rate) / annual_volatility

        return sharpe_ratio

    def calculate_sortino_ratio(self, risk_free_rate=0.0):
        """
        计算索提诺比率

        参数:
            risk_free_rate (float): 无风险利率

        返回:
            float: 索提诺比率
        """
        if len(self.portfolio_values) < 2:
            return 0

        # 计算收益率
        returns = np.diff(self.portfolio_values) / self.portfolio_values[:-1]

        # 计算年化收益率
        annual_return = np.mean(returns) * 252

        # 计算下行波动率
        negative_returns = returns[returns < 0]
        if len(negative_returns) == 0:
            return float('inf')  # 如果没有负收益，索提诺比率为无穷大

        downside_deviation = np.std(negative_returns) * np.sqrt(252)

        # 计算索提诺比率
        if downside_deviation == 0:
            return 0

        sortino_ratio = (annual_return - risk_free_rate) / downside_deviation

        return sortino_ratio

    def calculate_max_drawdown(self):
        """
        计算最大回撤

        返回:
            float: 最大回撤
        """
        if len(self.portfolio_values) < 2:
            return 0

        # 计算累计最大值
        peak = np.maximum.accumulate(self.portfolio_values)

        # 计算回撤
        drawdown = (peak - self.portfolio_values) / peak

        # 计算最大回撤
        max_drawdown = np.max(drawdown)

        return max_drawdown

    def calculate_calmar_ratio(self):
        """
        计算卡玛比率

        返回:
            float: 卡玛比率
        """
        if len(self.portfolio_values) < 2:
            return 0

        # 计算收益率
        returns = np.diff(self.portfolio_values) / self.portfolio_values[:-1]

        # 计算年化收益率
        annual_return = np.mean(returns) * 252

        # 计算最大回撤
        max_drawdown = self.calculate_max_drawdown()

        # 计算卡玛比率
        if max_drawdown == 0:
            return 0

        calmar_ratio = annual_return / max_drawdown

        return calmar_ratio

    def reset(self):
        """
        重置奖励计算器
        """
        self.reward_history = []
        self.portfolio_values = []
