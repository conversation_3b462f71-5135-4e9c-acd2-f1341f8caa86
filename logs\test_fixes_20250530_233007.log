2025-05-30 23:30:07,013 - test_fixes - INFO - ==================================================
2025-05-30 23:30:07,013 - test_fixes - INFO - 开始测试修复后的功能
2025-05-30 23:30:07,013 - test_fixes - INFO - ==================================================
2025-05-30 23:30:07,014 - test_fixes - INFO - 测试GPU检测功能
2025-05-30 23:30:07,014 - gpu_support - INFO - 检测GPU支持...
2025-05-30 23:30:07,076 - gpu_support - INFO - 操作系统: Windows
2025-05-30 23:30:07,076 - gpu_support - INFO - 在Windows系统上检测GPU...
2025-05-30 23:30:07,135 - gpu_support - INFO - 检测PyTorch GPU支持...
2025-05-30 23:30:08,683 - gpu_support - INFO - PyTorch可访问的GPU数量: 1
2025-05-30 23:30:08,685 - gpu_support - INFO - PyTorch GPU 0: NVIDIA GeForce RTX 3070 Ti
2025-05-30 23:30:08,685 - gpu_support - INFO - 检测TensorFlow GPU支持...
2025-05-30 23:30:08,685 - gpu_support - INFO - 未安装TensorFlow或导入错误
2025-05-30 23:30:08,685 - gpu_support - INFO - GPU可用: True
2025-05-30 23:30:08,685 - gpu_support - INFO - 设备列表: [{'name': 'NVIDIA GeForce RTX 3070 Ti', 'memory_total': '8192 MiB', 'memory_free': '5018 MiB', 'compute_capability': '8.6'}]
2025-05-30 23:30:08,686 - gpu_support - INFO - CUDA版本: 11.8
2025-05-30 23:30:08,686 - gpu_support - INFO - 驱动版本: 560.94
2025-05-30 23:30:08,686 - gpu_support - INFO - PyTorch GPU支持: True
2025-05-30 23:30:08,686 - gpu_support - INFO - TensorFlow GPU支持: False
2025-05-30 23:30:08,686 - test_fixes - INFO - GPU检测结果: {'available': True, 'devices': [{'name': 'NVIDIA GeForce RTX 3070 Ti', 'memory_total': '8192 MiB', 'memory_free': '5018 MiB', 'compute_capability': '8.6'}], 'cuda_version': '11.8', 'driver_version': '560.94', 'pytorch_gpu': True, 'tensorflow_gpu': False}
2025-05-30 23:30:08,686 - test_fixes - INFO - 测试特征工程适配器
2025-05-30 23:30:10,568 - test_fixes - INFO - 扁平格式配置: {'use_price': True, 'use_volume': True, 'use_technical': True, 'sma_periods': [5, 10, 20, 30, 60], 'ema_periods': [5, 10, 20, 30, 60], 'rsi_periods': [14], 'macd_params': {'fast': 12, 'slow': 26, 'signal': 9}, 'bb_params': {'window': 20, 'num_std': 2}, 'atr_periods': [14], 'normalization': 'zscore'}
2025-05-30 23:30:10,569 - test_fixes - INFO - 转换后的标准格式配置: {'price_features': {'use': True}, 'volume': {'use': True, 'periods': [5, 10, 20]}, 'sma': {'use': True, 'periods': [5, 10, 20, 30, 60]}, 'ema': {'use': True, 'periods': [5, 10, 20, 30, 60]}, 'rsi': {'use': True, 'periods': [14]}, 'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9}, 'bbands': {'use': True, 'period': 20, 'std': 2}, 'atr': {'use': True, 'periods': [14]}, 'normalization': {'use': True, 'method': 'zscore'}, 'technical_indicators': {'use': True}}
2025-05-30 23:30:10,569 - test_fixes - INFO - 标准格式配置: {'price_features': {'use': True}, 'volume': {'use': True, 'periods': [5, 10, 20]}, 'sma': {'use': True, 'periods': [5, 10, 20, 60]}, 'ema': {'use': True, 'periods': [5, 10, 20]}, 'rsi': {'use': True, 'periods': [6, 14, 21]}, 'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9}, 'bbands': {'use': True, 'period': 20, 'std': 2.0}, 'normalization': {'use': True, 'method': 'minmax'}}
2025-05-30 23:30:10,569 - test_fixes - INFO - 适配后的配置: {'price_features': {'use': True}, 'volume': {'use': True, 'periods': [5, 10, 20]}, 'sma': {'use': True, 'periods': [5, 10, 20, 60]}, 'ema': {'use': True, 'periods': [5, 10, 20]}, 'rsi': {'use': True, 'periods': [6, 14, 21]}, 'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9}, 'bbands': {'use': True, 'period': 20, 'std': 2.0}, 'normalization': {'use': True, 'method': 'minmax'}}
2025-05-30 23:30:10,569 - test_fixes - INFO - 测试性能分析器参数处理
2025-05-30 23:30:10,580 - test_fixes - INFO - 测试直接传入DataFrame
2025-05-30 23:30:10,582 - test_fixes - ERROR - 测试性能分析器参数处理时出错: float division by zero
2025-05-30 23:30:10,583 - test_fixes - ERROR - Traceback (most recent call last):
  File "C:\cursor\量化\test_fixes.py", line 165, in test_performance_analyzer
    metrics_df = analyzer.analyze(trades_df, portfolio_values)
  File "C:\cursor\量化\quant_project\core_logic\performance_analyzer.py", line 79, in analyze
    metrics['sharpe_ratio'] = self._calculate_sharpe_ratio(returns, self.risk_free_rate)
                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\cursor\量化\quant_project\core_logic\performance_analyzer.py", line 238, in _calculate_sharpe_ratio
    excess_returns = returns - self.risk_free_rate / periods_per_year
                               ~~~~~~~~~~~~~~~~~~~~^~~~~~~~~~~~~~~~~~
ZeroDivisionError: float division by zero

2025-05-30 23:30:10,583 - test_fixes - INFO - === 测试数据获取功能 ===
2025-05-30 23:30:10,625 - test_fixes - INFO - 测试数据获取: {'stock_code': 'sh000001', 'start_date': '2020-01-01', 'end_date': '2020-12-31', 'frequency': '日线', 'data_source': '指数'}
2025-05-30 23:30:10,626 - drl_trading - INFO - 获取数据: code=sh000001, freq=D, source=指数
2025-05-30 23:30:10,626 - drl_trading - INFO - 缓存文件不存在: data_cache\c15b165ee622bcd2366ea8205db43b32.csv
2025-05-30 23:30:10,626 - drl_trading - INFO - 从数据源获取数据: sh000001, 2020-01-01 至 2020-12-31, 频率: D
2025-05-30 23:30:10,626 - drl_trading - ERROR - 获取数据失败: 不支持的数据频率: D，目前仅支持 '日线', '周线', '月线'
2025-05-30 23:30:10,627 - drl_trading - WARNING - 使用内部频率 D 获取数据失败: 获取数据失败: 不支持的数据频率: D，目前仅支持 '日线', '周线', '月线'
股票代码格式说明:
- 上海证券交易所股票: sh + 6位数字，如 sh600000
- 深圳证券交易所股票: sz + 6位数字，如 sz000001
，尝试使用原始频率: 日线
2025-05-30 23:30:10,627 - drl_trading - INFO - 从缓存加载数据: data_cache\a8c3892a8f5a2d9bcf50c6153734f9f1.csv
2025-05-30 23:30:10,640 - drl_trading - INFO - 从缓存加载数据成功，共 8409 条记录
2025-05-30 23:30:10,640 - test_fixes - INFO - 成功获取数据: 8409 条记录
2025-05-30 23:30:10,640 - test_fixes - INFO - 数据范围: 1990-12-19 00:00:00 至 2025-05-30 00:00:00
2025-05-30 23:30:10,641 - test_fixes - INFO - 数据列: ['开盘', '收盘', '最高', '最低', '成交量', '成交额']
2025-05-30 23:30:10,641 - test_fixes - INFO - 测试数据获取: {'stock_code': 'sh600000', 'start_date': '2021-01-01', 'end_date': '2021-06-30', 'frequency': '周线', 'data_source': '股票'}
2025-05-30 23:30:10,641 - drl_trading - INFO - 获取数据: code=sh600000, freq=W, source=股票
2025-05-30 23:30:10,641 - drl_trading - INFO - 缓存文件不存在: data_cache\286bf51681d0b8e6c2c23716c10f3f3f.csv
2025-05-30 23:30:10,642 - drl_trading - INFO - 从数据源获取数据: sh600000, 2021-01-01 至 2021-06-30, 频率: W
2025-05-30 23:30:10,642 - drl_trading - ERROR - 获取数据失败: 不支持的数据频率: W，目前仅支持 '日线', '周线', '月线'
2025-05-30 23:30:10,642 - drl_trading - WARNING - 使用内部频率 W 获取数据失败: 获取数据失败: 不支持的数据频率: W，目前仅支持 '日线', '周线', '月线'
股票代码格式说明:
- 上海证券交易所股票: sh + 6位数字，如 sh600000
- 深圳证券交易所股票: sz + 6位数字，如 sz000001
，尝试使用原始频率: 周线
2025-05-30 23:30:10,642 - drl_trading - INFO - 从缓存加载数据: data_cache\a29b0c2df38fe6158765b1683d3b6bbd.csv
2025-05-30 23:30:10,646 - drl_trading - INFO - 从缓存加载数据成功，共 1283 条记录
2025-05-30 23:30:10,646 - test_fixes - INFO - 成功获取数据: 1283 条记录
2025-05-30 23:30:10,646 - test_fixes - INFO - 数据范围: 1999-11-14 00:00:00 至 2025-06-01 00:00:00
2025-05-30 23:30:10,647 - test_fixes - INFO - 数据列: ['开盘', '最高', '最低', '收盘', '成交量', '成交额']
2025-05-30 23:30:10,647 - test_fixes - INFO - 数据获取功能测试通过
2025-05-30 23:30:10,647 - test_fixes - INFO - === 测试因子挖掘功能 ===
2025-05-30 23:30:10,651 - drl_trading - INFO - 获取数据: code=sh000001, freq=D, source=指数
2025-05-30 23:30:10,652 - drl_trading - INFO - 缓存文件不存在: data_cache\c15b165ee622bcd2366ea8205db43b32.csv
2025-05-30 23:30:10,652 - drl_trading - INFO - 从数据源获取数据: sh000001, 2020-01-01 至 2020-12-31, 频率: D
2025-05-30 23:30:10,652 - drl_trading - ERROR - 获取数据失败: 不支持的数据频率: D，目前仅支持 '日线', '周线', '月线'
2025-05-30 23:30:10,652 - drl_trading - WARNING - 使用内部频率 D 获取数据失败: 获取数据失败: 不支持的数据频率: D，目前仅支持 '日线', '周线', '月线'
股票代码格式说明:
- 上海证券交易所股票: sh + 6位数字，如 sh600000
- 深圳证券交易所股票: sz + 6位数字，如 sz000001
，尝试使用原始频率: 日线
2025-05-30 23:30:10,652 - drl_trading - INFO - 从缓存加载数据: data_cache\a8c3892a8f5a2d9bcf50c6153734f9f1.csv
2025-05-30 23:30:10,665 - drl_trading - INFO - 从缓存加载数据成功，共 8409 条记录
2025-05-30 23:30:10,665 - drl_trading - INFO - 开始运行自动因子挖掘流水线...
2025-05-30 23:30:10,665 - test_fixes - INFO - 因子挖掘进度: 初始化 - 0% - 开始因子挖掘流程
2025-05-30 23:30:10,666 - drl_trading - INFO - 初始化 - 0% - 开始因子挖掘流程
2025-05-30 23:30:10,666 - test_fixes - INFO - 因子挖掘进度: 因子生成 - 10% - 开始生成因子
2025-05-30 23:30:10,666 - drl_trading - INFO - 因子生成 - 10% - 开始生成因子
2025-05-30 23:30:10,666 - test_fixes - INFO - 因子挖掘进度: 因子生成 - 15% - 生成技术指标因子
2025-05-30 23:30:10,666 - drl_trading - INFO - 因子生成 - 15% - 生成技术指标因子
2025-05-30 23:30:10,666 - drl_trading - INFO - 生成技术指标因子...
2025-05-30 23:30:10,666 - drl_trading - INFO - 输入数据形状: (8409, 6), 列名: ['开盘', '收盘', '最高', '最低', '成交量', '成交额']
2025-05-30 23:30:10,666 - drl_trading - INFO - 找到列 '开盘' 映射到标准名称 'open'
2025-05-30 23:30:10,667 - drl_trading - INFO - 找到列 '最高' 映射到标准名称 'high'
2025-05-30 23:30:10,667 - drl_trading - INFO - 找到列 '最低' 映射到标准名称 'low'
2025-05-30 23:30:10,667 - drl_trading - INFO - 找到列 '收盘' 映射到标准名称 'close'
2025-05-30 23:30:10,667 - drl_trading - INFO - 找到列 '成交量' 映射到标准名称 'volume'
2025-05-30 23:30:10,667 - drl_trading - INFO - 最终列映射: {'open': '开盘', 'high': '最高', 'low': '最低', 'close': '收盘', 'volume': '成交量'}
2025-05-30 23:30:19,984 - drl_trading - INFO - 成功生成 177 个技术指标因子
2025-05-30 23:30:19,985 - test_fixes - INFO - 因子挖掘进度: 因子生成 - 20% - 生成了 177 个技术指标因子
2025-05-30 23:30:19,985 - drl_trading - INFO - 因子生成 - 20% - 生成了 177 个技术指标因子
2025-05-30 23:30:19,985 - test_fixes - INFO - 因子挖掘进度: 因子生成 - 25% - 生成交叉因子
2025-05-30 23:30:19,985 - drl_trading - INFO - 因子生成 - 25% - 生成交叉因子
2025-05-30 23:30:19,988 - drl_trading - INFO - 成功生成 30 个交叉因子
2025-05-30 23:30:19,989 - test_fixes - INFO - 因子挖掘进度: 因子生成 - 30% - 生成了 30 个交叉因子
2025-05-30 23:30:19,989 - drl_trading - INFO - 因子生成 - 30% - 生成了 30 个交叉因子
2025-05-30 23:30:19,989 - test_fixes - INFO - 因子挖掘进度: 因子生成 - 35% - 生成时间序列因子
2025-05-30 23:30:19,989 - drl_trading - INFO - 因子生成 - 35% - 生成时间序列因子
2025-05-30 23:30:20,012 - drl_trading - INFO - 成功生成 100 个时间序列因子
2025-05-30 23:30:20,013 - test_fixes - INFO - 因子挖掘进度: 因子生成 - 40% - 生成了 100 个时间序列因子
2025-05-30 23:30:20,013 - drl_trading - INFO - 因子生成 - 40% - 生成了 100 个时间序列因子
2025-05-30 23:30:20,013 - test_fixes - INFO - 因子挖掘进度: 因子生成 - 40% - 因子生成完成，共 307 个因子，耗时 9.35 秒
2025-05-30 23:30:20,013 - drl_trading - INFO - 因子生成 - 40% - 因子生成完成，共 307 个因子，耗时 9.35 秒
2025-05-30 23:30:20,014 - test_fixes - INFO - 因子挖掘进度: 因子评估 - 45% - 开始评估因子
2025-05-30 23:30:20,014 - drl_trading - INFO - 因子评估 - 45% - 开始评估因子
2025-05-30 23:30:20,014 - test_fixes - INFO - 因子挖掘进度: 因子评估 - 50% - 评估 307 个因子
2025-05-30 23:30:20,014 - drl_trading - INFO - 因子评估 - 50% - 评估 307 个因子
2025-05-30 23:30:20,014 - drl_trading - INFO - 开始评估 307 个因子...
2025-05-30 23:30:21,056 - drl_trading - INFO - 已评估 10 个因子...
2025-05-30 23:30:22,448 - drl_trading - INFO - 已评估 20 个因子...
2025-05-30 23:30:23,863 - drl_trading - INFO - 已评估 30 个因子...
2025-05-30 23:30:25,292 - drl_trading - INFO - 已评估 40 个因子...
2025-05-30 23:30:25,332 - drl_trading - WARNING - 评估因子 'kama_20' 时出错: Bin edges must be unique: Index([0.0, 0.0, 129.74134483681013, 131.57702224858568, 131.9191473266997,
       134.25],
      dtype='float64', name='factor').
You can drop duplicate edges by setting the 'duplicates' kwarg
2025-05-30 23:30:26,161 - drl_trading - INFO - 已评估 50 个因子...
2025-05-30 23:30:27,139 - drl_trading - INFO - 已评估 60 个因子...
2025-05-30 23:30:28,045 - drl_trading - INFO - 已评估 70 个因子...
2025-05-30 23:30:29,639 - drl_trading - INFO - 已评估 80 个因子...
2025-05-30 23:30:30,397 - drl_trading - INFO - 已评估 90 个因子...
2025-05-30 23:30:31,279 - drl_trading - INFO - 已评估 100 个因子...
2025-05-30 23:30:32,962 - drl_trading - INFO - 已评估 110 个因子...
2025-05-30 23:30:33,810 - drl_trading - INFO - 已评估 120 个因子...
2025-05-30 23:30:34,750 - drl_trading - INFO - 已评估 130 个因子...
2025-05-30 23:30:35,892 - drl_trading - INFO - 已评估 140 个因子...
2025-05-30 23:30:37,020 - drl_trading - INFO - 已评估 150 个因子...
2025-05-30 23:30:38,819 - drl_trading - INFO - 已评估 160 个因子...
2025-05-30 23:30:40,690 - drl_trading - INFO - 已评估 170 个因子...
2025-05-30 23:30:41,899 - drl_trading - INFO - 已评估 180 个因子...
2025-05-30 23:30:43,589 - drl_trading - INFO - 已评估 190 个因子...
2025-05-30 23:30:45,209 - drl_trading - INFO - 已评估 200 个因子...
2025-05-30 23:30:46,713 - drl_trading - INFO - 因子评估完成。评估了 204 个因子，跳过了 103 个因子。
2025-05-30 23:30:46,714 - test_fixes - INFO - 因子挖掘进度: 因子评估 - 60% - 因子评估完成，共评估了 204 个因子，耗时 26.70 秒
2025-05-30 23:30:46,714 - drl_trading - INFO - 因子评估 - 60% - 因子评估完成，共评估了 204 个因子，耗时 26.70 秒
2025-05-30 23:30:46,715 - test_fixes - INFO - 因子挖掘进度: 因子筛选 - 65% - 开始筛选因子
2025-05-30 23:30:46,715 - drl_trading - INFO - 因子筛选 - 65% - 开始筛选因子
2025-05-30 23:30:46,715 - test_fixes - INFO - 因子挖掘进度: 因子筛选 - 70% - 多周期过滤
2025-05-30 23:30:46,715 - drl_trading - INFO - 因子筛选 - 70% - 多周期过滤
2025-05-30 23:30:46,715 - drl_trading - INFO - 开始多周期过滤 (最少有效周期: 3, 有效周期比例: 0.6)...
2025-05-30 23:30:46,716 - drl_trading - INFO - 多周期过滤后剩余 164 个因子
2025-05-30 23:30:46,716 - test_fixes - INFO - 因子挖掘进度: 因子筛选 - 75% - 相关性过滤
2025-05-30 23:30:46,716 - drl_trading - INFO - 因子筛选 - 75% - 相关性过滤
2025-05-30 23:30:46,716 - drl_trading - INFO - 开始移除高度相关因子 (阈值: 0.7, 方法: keep_highest_ic)...
2025-05-30 23:30:47,188 - drl_trading - INFO - 发现 3521 对高度相关因子对
2025-05-30 23:30:47,189 - drl_trading - INFO - 移除了 151 个冗余因子，剩余 13 个因子
2025-05-30 23:30:47,190 - test_fixes - INFO - 因子挖掘进度: 因子筛选 - 80% - 选择最终因子
2025-05-30 23:30:47,190 - drl_trading - INFO - 因子筛选 - 80% - 选择最终因子
2025-05-30 23:30:47,190 - drl_trading - INFO - 选择最佳因子 (top 10, 使用综合得分: True)...
2025-05-30 23:30:47,190 - drl_trading - INFO - 选择了 10 个最佳因子
2025-05-30 23:30:47,191 - test_fixes - INFO - 因子挖掘进度: 因子筛选 - 85% - 因子筛选完成，选择了 10 个最佳因子，耗时 0.48 秒
2025-05-30 23:30:47,191 - drl_trading - INFO - 因子筛选 - 85% - 因子筛选完成，选择了 10 个最佳因子，耗时 0.48 秒
2025-05-30 23:30:47,191 - test_fixes - INFO - 因子挖掘进度: 结果处理 - 90% - 绘制结果
2025-05-30 23:30:47,191 - drl_trading - INFO - 结果处理 - 90% - 绘制结果
2025-05-30 23:30:47,862 - test_fixes - INFO - 因子挖掘进度: 结果处理 - 95% - 保存评估图表到 factor_results\factor_evaluation_20250530_233047.png
2025-05-30 23:30:47,862 - drl_trading - INFO - 结果处理 - 95% - 保存评估图表到 factor_results\factor_evaluation_20250530_233047.png
2025-05-30 23:30:47,863 - test_fixes - INFO - 因子挖掘进度: 完成 - 100% - 因子挖掘成功完成
2025-05-30 23:30:47,863 - drl_trading - INFO - 完成 - 100% - 因子挖掘成功完成
2025-05-30 23:30:47,863 - drl_trading - INFO - 保存因子挖掘结果...
2025-05-30 23:30:47,926 - drl_trading - INFO - 保存因子数据到 factor_results\best_factors_20250530_233047.csv
2025-05-30 23:30:47,928 - drl_trading - INFO - 保存评估摘要到 factor_results\factor_evaluation_20250530_233047.csv
2025-05-30 23:30:47,928 - drl_trading - INFO - 自动因子挖掘流水线完成，共耗时 37.26 秒
2025-05-30 23:30:47,930 - test_fixes - INFO - 成功挖掘出 10 个因子
2025-05-30 23:30:47,930 - test_fixes - INFO - 因子列表: ['kama_10', 'close_sma5_ratio', 'sma_5', 'volume_ratio_20', 'sma_5_to_rsi_14', 'bb_width_20_10', 'rsi_6', 'sma_5_to_volatility_20', 'close_sma60_ratio', 'atr_7']
2025-05-30 23:30:47,930 - test_fixes - INFO - 因子挖掘功能测试通过
2025-05-30 23:30:47,931 - test_fixes - INFO - === 测试DRL智能体功能 ===
2025-05-30 23:30:47,931 - drl_trading - INFO - 初始化DRL智能体
2025-05-30 23:30:47,931 - drl_trading - INFO - 环境配置: None
2025-05-30 23:30:47,931 - drl_trading - INFO - 智能体配置: dict_keys(['algorithm'])
2025-05-30 23:30:47,931 - drl_trading - INFO - 超参数优化配置: None
2025-05-30 23:30:47,931 - drl_trading - WARNING - 创建DRL智能体时出错: 'NoneType' object has no attribute 'columns'，将使用空智能体
2025-05-30 23:30:47,931 - drl_trading - WARNING - 创建空智能体 (PPO)，仅用于兼容性测试
2025-05-30 23:30:47,932 - test_fixes - INFO - DRL智能体算法: PPO
2025-05-30 23:30:47,932 - test_fixes - INFO - 智能体方法: ['__class__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__firstlineno__', '__format__', '__ge__', '__getattribute__', '__getstate__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__setattr__', '__sizeof__', '__static_attributes__', '__str__', '__subclasshook__', '__weakref__', '_create_agent', 'agent', 'agent_config', 'algorithm', 'env_config', 'load', 'predict', 'save', 'train']
2025-05-30 23:30:47,932 - test_fixes - INFO - DRL智能体功能测试通过
2025-05-30 23:30:47,932 - test_fixes - INFO - ==================================================
2025-05-30 23:30:47,933 - test_fixes - INFO - 测试结果:
2025-05-30 23:30:47,933 - test_fixes - INFO - 1. GPU检测功能: 成功
2025-05-30 23:30:47,933 - test_fixes - INFO - 2. 特征工程适配器: 成功
2025-05-30 23:30:47,933 - test_fixes - INFO - 3. 性能分析器参数处理: 失败
2025-05-30 23:30:47,933 - test_fixes - INFO -    错误: float division by zero
2025-05-30 23:30:47,934 - test_fixes - INFO - 4. 数据获取功能: 成功
2025-05-30 23:30:47,934 - test_fixes - INFO - 5. 因子挖掘功能: 成功
2025-05-30 23:30:47,934 - test_fixes - INFO - 6. DRL智能体功能: 成功
2025-05-30 23:30:47,934 - test_fixes - INFO - ==================================================
2025-05-30 23:30:47,934 - test_fixes - INFO - 修复测试总结: 部分失败
