"""
量化交易系统启动脚本 - 修复版
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# 确保logs目录存在
os.makedirs('logs', exist_ok=True)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/run_fixed.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('run_fixed')

def main():
    """主函数，启动系统"""
    logger.info("开始启动量化交易系统")
    
    # 检查项目结构
    project_dir = Path(__file__).parent
    app_path = project_dir / "quant_project" / "main_app.py"
    
    if not app_path.exists():
        logger.error(f"主应用程序文件不存在: {app_path}")
        print(f"错误: 找不到主应用程序文件 {app_path}")
        return 1
    
    # 启动streamlit应用
    logger.info("启动Streamlit应用")
    cmd = ["python", "-m", "streamlit", "run", str(app_path), "--server.port=8501"]
    
    try:
        logger.info(f"运行命令: {' '.join(cmd)}")
        process = subprocess.Popen(cmd)
        print(f"已启动Streamlit应用，请访问 http://localhost:8501")
        
        # 等待进程结束
        process.wait()
        return process.returncode
    except KeyboardInterrupt:
        logger.info("接收到用户中断，停止应用")
        print("停止应用...")
        return 0
    except Exception as e:
        logger.error(f"启动应用时出错: {str(e)}")
        print(f"错误: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main()) 