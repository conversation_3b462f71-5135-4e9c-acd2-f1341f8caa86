"""
数据清洗模块
负责清洗和预处理金融数据
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from scipy import stats

class DataCleaner:
    """
    数据清洗类
    负责清洗和预处理金融数据，包括缺失值处理、异常值检测和时间序列对齐
    """

    def __init__(self, missing_value_strategy='ffill', outlier_detection=True, logger=None):
        """
        初始化数据清洗器

        参数:
            missing_value_strategy (str): 缺失值处理策略，可选'ffill'(前向填充),'bfill'(后向填充),'mean'(均值填充),'median'(中位数填充),'none'(不处理)
            outlier_detection (bool): 是否进行异常值检测
            logger (logging.Logger): 日志记录器
        """
        self.missing_value_strategy = missing_value_strategy
        self.outlier_detection = outlier_detection
        self.logger = logger or logging.getLogger('drl_trading')

    def clean_data(self, data):
        """
        清洗数据

        参数:
            data (pandas.DataFrame): 原始金融数据

        返回:
            pandas.DataFrame: 清洗后的数据
        """
        if data is None or data.empty:
            self.logger.warning("输入数据为空，无法进行清洗")
            return data

        # 复制数据，避免修改原始数据
        df = data.copy()

        # 标准化列名
        df = self._standardize_column_names(df)

        # 确保日期索引
        df = self._ensure_date_index(df)

        # 处理缺失值
        df = self._handle_missing_values(df)

        # 检测并处理异常值
        if self.outlier_detection:
            df = self._detect_and_handle_outliers(df)

        # 确保数据类型正确
        df = self._ensure_data_types(df)

        # 排序数据
        df = df.sort_index()

        self.logger.info(f"数据清洗完成，处理后数据形状: {df.shape}")
        return df

    def _standardize_column_names(self, df):
        """
        标准化列名

        参数:
            df (pandas.DataFrame): 原始数据

        返回:
            pandas.DataFrame: 标准化列名后的数据
        """
        # 列名映射字典
        column_mapping = {
            # 英文到中文的映射
            'open': '开盘',
            'high': '最高',
            'low': '最低',
            'close': '收盘',
            'volume': '成交量',
            'amount': '成交额',
            'date': '日期',
            'trade_date': '日期',
            # 中文变体的映射
            '开盘价': '开盘',
            '最高价': '最高',
            '最低价': '最低',
            '收盘价': '收盘',
            '成交量(手)': '成交量',
            '成交金额': '成交额',
            '交易日期': '日期'
        }

        # 检查并重命名列
        renamed_columns = {}
        for col in df.columns:
            if col.lower() in column_mapping:
                renamed_columns[col] = column_mapping[col.lower()]
            elif col in column_mapping:
                renamed_columns[col] = column_mapping[col]

        if renamed_columns:
            df = df.rename(columns=renamed_columns)
            self.logger.info(f"标准化了以下列名: {renamed_columns}")

        return df

    def _ensure_date_index(self, df):
        """
        确保数据框使用日期作为索引

        参数:
            df (pandas.DataFrame): 原始数据

        返回:
            pandas.DataFrame: 使用日期索引的数据
        """
        # 检查是否已经有日期索引
        if isinstance(df.index, pd.DatetimeIndex):
            self.logger.info("数据已经使用日期索引")
            return df

        # 检查日期列
        date_column_candidates = ['日期', 'date', 'trade_date', '交易日期', '日期时间']
        date_column = None

        for col in date_column_candidates:
            if col in df.columns:
                date_column = col
                self.logger.info(f"找到日期列: {col}")
                break

        if date_column is None:
            self.logger.warning("未找到日期列，将创建一个日期索引")
            # 创建一个日期索引
            df.index = pd.date_range(start=datetime.now() - timedelta(days=len(df)), periods=len(df), freq='D')
            return df

        # 将日期列转换为日期时间类型
        try:
            df[date_column] = pd.to_datetime(df[date_column])
            # 设置日期列为索引
            df = df.set_index(date_column)
            self.logger.info(f"将 {date_column} 设置为索引")
        except Exception as e:
            self.logger.error(f"将 {date_column} 转换为日期时间类型失败: {str(e)}")
            # 创建一个日期索引
            df.index = pd.date_range(start=datetime.now() - timedelta(days=len(df)), periods=len(df), freq='D')

        return df

    def _handle_missing_values(self, df):
        """
        处理缺失值

        参数:
            df (pandas.DataFrame): 原始数据

        返回:
            pandas.DataFrame: 处理缺失值后的数据
        """
        # 检查是否有缺失值
        missing_values = df.isnull().sum()
        total_missing = missing_values.sum()

        if total_missing > 0:
            self.logger.info(f"检测到 {total_missing} 个缺失值，按列分布: {missing_values[missing_values > 0]}")

            # 根据策略处理缺失值
            if self.missing_value_strategy == 'ffill':
                df = df.fillna(method='ffill')
                # 如果第一行有缺失值，使用后向填充
                df = df.fillna(method='bfill')
                self.logger.info("使用前向填充和后向填充处理缺失值")
            elif self.missing_value_strategy == 'bfill':
                df = df.fillna(method='bfill')
                # 如果最后一行有缺失值，使用前向填充
                df = df.fillna(method='ffill')
                self.logger.info("使用后向填充和前向填充处理缺失值")
            elif self.missing_value_strategy == 'mean':
                # 对于数值列使用均值填充
                numeric_cols = df.select_dtypes(include=['number']).columns
                for col in numeric_cols:
                    df[col] = df[col].fillna(df[col].mean())
                self.logger.info("使用均值填充处理数值列的缺失值")
            elif self.missing_value_strategy == 'median':
                # 对于数值列使用中位数填充
                numeric_cols = df.select_dtypes(include=['number']).columns
                for col in numeric_cols:
                    df[col] = df[col].fillna(df[col].median())
                self.logger.info("使用中位数填充处理数值列的缺失值")
            elif self.missing_value_strategy == 'none':
                self.logger.info("不处理缺失值")
            else:
                self.logger.warning(f"未知的缺失值处理策略: {self.missing_value_strategy}，将使用前向填充")
                df = df.fillna(method='ffill')
                # 如果第一行有缺失值，使用后向填充
                df = df.fillna(method='bfill')

            # 检查是否还有缺失值
            remaining_missing = df.isnull().sum().sum()
            if remaining_missing > 0:
                self.logger.warning(f"处理后仍有 {remaining_missing} 个缺失值，将使用0填充")
                df = df.fillna(0)

        return df

    def _detect_and_handle_outliers(self, df):
        """
        检测并处理异常值

        参数:
            df (pandas.DataFrame): 原始数据

        返回:
            pandas.DataFrame: 处理异常值后的数据
        """
        # 只处理数值列
        numeric_cols = df.select_dtypes(include=['number']).columns

        for col in numeric_cols:
            # 使用Z-score方法检测异常值
            z_scores = stats.zscore(df[col], nan_policy='omit')
            abs_z_scores = np.abs(z_scores)
            outliers = (abs_z_scores > 3).sum()  # 3个标准差以外的值视为异常值

            if outliers > 0:
                self.logger.info(f"列 {col} 中检测到 {outliers} 个异常值")
                
                # 使用Winsorization方法处理异常值（将超出范围的值设置为边界值）
                lower_bound = df[col].quantile(0.01)  # 1%分位数
                upper_bound = df[col].quantile(0.99)  # 99%分位数
                
                # 记录原始值
                original_values = df[col].copy()
                
                # 应用边界
                df[col] = df[col].clip(lower=lower_bound, upper=upper_bound)
                
                # 计算修改的值的数量
                modified = (original_values != df[col]).sum()
                self.logger.info(f"列 {col} 中修改了 {modified} 个异常值")

        return df

    def _ensure_data_types(self, df):
        """
        确保数据类型正确

        参数:
            df (pandas.DataFrame): 原始数据

        返回:
            pandas.DataFrame: 数据类型正确的数据
        """
        # OHLCV列应该是数值类型
        ohlcv_columns = ['开盘', '最高', '最低', '收盘', '成交量', '成交额']
        
        for col in ohlcv_columns:
            if col in df.columns:
                try:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                    # 如果转换后有NaN值，使用前向填充
                    if df[col].isnull().any():
                        df[col] = df[col].fillna(method='ffill').fillna(method='bfill')
                except Exception as e:
                    self.logger.error(f"将列 {col} 转换为数值类型失败: {str(e)}")
        
        return df
