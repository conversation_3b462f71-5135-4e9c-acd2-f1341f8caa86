"""
UI测试与修复主脚本

该脚本运行UI测试，生成报告，并修复发现的问题。
"""

import os
import sys
import subprocess
import logging
import time
from datetime import datetime

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/ui_test_main.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('ui_test_main')

def ensure_dependencies():
    """确保所有依赖已安装"""
    logger.info("检查并安装依赖")
    
    dependencies = [
        "selenium",
        "webdriver_manager",
        "html-testRunner"
    ]
    
    for dep in dependencies:
        try:
            __import__(dep.replace("-", "_"))
            logger.info(f"依赖已安装: {dep}")
        except ImportError:
            logger.info(f"安装依赖: {dep}")
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
    
    logger.info("所有依赖已安装")

def run_ui_tests():
    """运行UI测试"""
    logger.info("运行UI测试")
    
    # 运行测试并生成报告
    test_script = os.path.join("tests", "generate_ui_test_report.py")
    
    try:
        subprocess.check_call([sys.executable, test_script])
        logger.info("UI测试完成")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"UI测试失败: {str(e)}")
        return False

def fix_ui_issues():
    """修复UI问题"""
    logger.info("修复UI问题")
    
    # 运行修复脚本
    fix_script = os.path.join("tests", "fix_ui_issues.py")
    
    try:
        subprocess.check_call([sys.executable, fix_script])
        logger.info("UI问题修复完成")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"UI问题修复失败: {str(e)}")
        return False

def verify_fixes():
    """验证修复效果"""
    logger.info("验证修复效果")
    
    # 再次运行测试验证修复效果
    logger.info("再次运行UI测试验证修复效果")
    
    # 等待一段时间，确保之前的进程已完全退出
    time.sleep(5)
    
    test_script = os.path.join("tests", "generate_ui_test_report.py")
    
    try:
        subprocess.check_call([sys.executable, test_script])
        logger.info("验证测试完成")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"验证测试失败: {str(e)}")
        return False

def generate_final_report():
    """生成最终报告"""
    logger.info("生成最终报告")
    
    # 确保报告目录存在
    reports_dir = "test_reports"
    os.makedirs(reports_dir, exist_ok=True)
    
    # 创建最终报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_content = f"""
# UI测试与修复最终报告

**生成时间**: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## 测试与修复流程

1. 运行UI测试
2. 分析测试结果
3. 修复发现的问题
4. 验证修复效果

## 测试结果

详细测试结果请查看 `test_reports/ui_tests` 目录下的HTML报告。

## 修复结果

详细修复结果请查看 `test_reports/ui_fix_report_*.md` 文件。

## 建议

1. 定期运行UI测试，确保系统功能稳定
2. 在开发新功能时添加相应的UI测试用例
3. 关注用户反馈，及时修复UI问题

"""
    
    # 保存最终报告
    report_path = os.path.join(reports_dir, f"ui_test_final_report_{timestamp}.md")
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    logger.info(f"最终报告已保存到: {report_path}")
    return report_path

def main():
    """主函数"""
    logger.info("=" * 80)
    logger.info("UI测试与修复主脚本")
    logger.info("=" * 80)
    
    # 确保日志目录存在
    os.makedirs("logs", exist_ok=True)
    
    # 确保依赖已安装
    ensure_dependencies()
    
    # 运行UI测试
    test_success = run_ui_tests()
    
    # 修复UI问题
    if test_success:
        logger.info("UI测试成功，检查是否需要修复问题")
        fix_success = fix_ui_issues()
    else:
        logger.warning("UI测试失败，尝试修复问题")
        fix_success = fix_ui_issues()
    
    # 验证修复效果
    if fix_success:
        verify_success = verify_fixes()
    else:
        logger.error("UI问题修复失败，跳过验证")
        verify_success = False
    
    # 生成最终报告
    final_report_path = generate_final_report()
    logger.info(f"最终报告已生成: {final_report_path}")
    
    # 输出总结
    logger.info("=" * 80)
    logger.info("UI测试与修复总结")
    logger.info("=" * 80)
    logger.info(f"测试结果: {'成功' if test_success else '失败'}")
    logger.info(f"修复结果: {'成功' if fix_success else '失败'}")
    logger.info(f"验证结果: {'成功' if verify_success else '失败'}")
    logger.info("=" * 80)
    
    # 根据结果设置退出代码
    if test_success and (not fix_success or verify_success):
        logger.info("测试与修复流程成功完成")
        sys.exit(0)
    else:
        logger.error("测试与修复流程未完全成功")
        sys.exit(1)

if __name__ == "__main__":
    main()
