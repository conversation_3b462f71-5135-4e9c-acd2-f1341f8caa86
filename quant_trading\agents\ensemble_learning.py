"""
集成学习模块
实现多种集成学习策略，包括投票、加权投票、堆叠等
"""

import os
import logging
import numpy as np
import pandas as pd
from typing import List, Dict, Any, Tuple, Union, Optional
from stable_baselines3.common.base_class import BaseAlgorithm
from stable_baselines3.common.vec_env import VecEnv
from stable_baselines3.common.callbacks import BaseCallback
from stable_baselines3.common.evaluation import evaluate_policy
from stable_baselines3.common.utils import set_random_seed
from stable_baselines3 import PPO, A2C, DQN, SAC

logger = logging.getLogger('drl_trading')

class EnsembleModel:
    """
    集成模型类
    负责管理多个模型的集成预测
    """

    def __init__(self, models=None, voting_method='weighted'):
        """
        初始化集成模型

        参数:
            models (list): 模型列表
            voting_method (str): 投票方法，可选 'majority', 'weighted', 'stacking'
        """
        self.models = models or []
        self.voting_method = voting_method
        self.logger = logging.getLogger('drl_trading')

    def predict(self, observation, deterministic=True):
        """
        集成预测

        参数:
            observation: 观察
            deterministic (bool): 是否确定性预测

        返回:
            tuple: (动作, 状态)
        """
        if not self.models:
            raise ValueError("集成模型中没有模型")

        # 收集所有模型的预测
        predictions = []
        for model in self.models:
            action, _ = model.predict(observation, deterministic=deterministic)
            predictions.append(action)

        # 根据投票方法进行集成
        if self.voting_method == 'majority':
            # 多数投票
            final_action = self._majority_vote(predictions)
        elif self.voting_method == 'weighted':
            # 加权投票
            final_action = self._weighted_vote(predictions)
        elif self.voting_method == 'stacking':
            # 堆叠
            final_action = self._stacking(predictions, observation)
        else:
            # 默认使用多数投票
            self.logger.warning(f"未知的投票方法: {self.voting_method}，使用多数投票")
            final_action = self._majority_vote(predictions)

        return final_action, None

    def _majority_vote(self, predictions):
        """多数投票"""
        # 转换为numpy数组
        predictions = np.array(predictions)

        # 如果是离散动作空间
        if len(predictions.shape) == 1:
            # 计算每个动作的出现次数
            unique, counts = np.unique(predictions, return_counts=True)
            # 返回出现次数最多的动作
            return unique[np.argmax(counts)]
        else:
            # 如果是连续动作空间，取平均值
            return np.mean(predictions, axis=0)

    def _weighted_vote(self, predictions):
        """加权投票"""
        # 目前简单实现为平均值
        # 未来可以根据模型性能分配权重
        return np.mean(predictions, axis=0)

    def _stacking(self, predictions, observation):
        """堆叠方法"""
        # 目前简单实现为平均值
        # 未来可以训练元模型
        return np.mean(predictions, axis=0)

    def save(self, path):
        """
        保存集成模型

        参数:
            path (str): 保存路径

        返回:
            str: 保存路径
        """
        # 创建目录
        os.makedirs(os.path.dirname(path), exist_ok=True)

        # 保存每个模型
        model_paths = []
        for i, model in enumerate(self.models):
            model_path = f"{path}_model_{i}.zip"
            model.save(model_path)
            model_paths.append(model_path)

        # 保存集成模型元数据
        metadata = {
            'voting_method': self.voting_method,
            'model_paths': model_paths
        }

        # 保存元数据
        metadata_path = f"{path}_metadata.json"
        with open(metadata_path, 'w') as f:
            import json
            json.dump(metadata, f)

        self.logger.info(f"集成模型已保存至: {path}")

        return path

    @classmethod
    def load(cls, path):
        """
        加载集成模型

        参数:
            path (str): 加载路径

        返回:
            EnsembleModel: 加载的集成模型
        """
        # 加载元数据
        metadata_path = f"{path}_metadata.json"
        with open(metadata_path, 'r') as f:
            import json
            metadata = json.load(f)

        # 加载每个模型
        models = []
        for model_path in metadata['model_paths']:
            # 根据文件名判断模型类型
            if 'PPO' in model_path:
                model = PPO.load(model_path)
            elif 'A2C' in model_path:
                model = A2C.load(model_path)
            elif 'DQN' in model_path:
                model = DQN.load(model_path)
            elif 'SAC' in model_path:
                model = SAC.load(model_path)
            else:
                # 默认使用PPO
                model = PPO.load(model_path)

            models.append(model)

        # 创建集成模型
        ensemble_model = cls(
            models=models,
            voting_method=metadata['voting_method']
        )

        return ensemble_model

class EnsembleLearning:
    """
    集成学习类
    负责训练和管理多个模型的集成
    """

    def __init__(self,
                 env,
                 model_types: List[str] = ['PPO', 'A2C', 'SAC'],
                 n_models: int = 3,
                 voting_method: str = 'weighted',
                 seed: int = 42,
                 logger=None):
        """
        初始化集成学习

        参数:
            env: 训练环境
            model_types (List[str]): 模型类型列表
            n_models (int): 每种类型的模型数量
            voting_method (str): 投票方法
            seed (int): 随机种子
            logger (logging.Logger): 日志记录器
        """
        self.env = env
        self.model_types = model_types
        self.n_models = n_models
        self.voting_method = voting_method
        self.seed = seed
        self.logger = logger or logging.getLogger('drl_trading')

        # 模型映射
        self.model_mapping = {
            'PPO': PPO,
            'A2C': A2C,
            'DQN': DQN,
            'SAC': SAC
        }

        # 初始化模型列表
        self.models = []
        self.ensemble_model = None

    def train(self,
              total_timesteps: int,
              policy: str = 'MlpPolicy',
              policy_kwargs: Dict = None,
              learning_rate: float = 0.0003,
              gamma: float = 0.99,
              verbose: int = 1,
              callback = None):
        """
        训练集成模型

        参数:
            total_timesteps (int): 总训练步数
            policy (str): 策略类型
            policy_kwargs (Dict): 策略参数
            learning_rate (float): 学习率
            gamma (float): 折扣因子
            verbose (int): 详细程度
            callback: 回调函数

        返回:
            EnsembleModel: 训练好的集成模型
        """
        # 设置随机种子
        set_random_seed(self.seed)

        # 训练每种类型的模型
        for model_type in self.model_types:
            if model_type not in self.model_mapping:
                self.logger.warning(f"不支持的模型类型: {model_type}，跳过")
                continue

            model_class = self.model_mapping[model_type]

            # 训练多个同类型模型
            for i in range(self.n_models):
                # 创建模型
                model = model_class(
                    policy=policy,
                    env=self.env,
                    policy_kwargs=policy_kwargs,
                    learning_rate=learning_rate,
                    gamma=gamma,
                    verbose=verbose,
                    seed=self.seed + i  # 使用不同的种子
                )

                # 训练模型
                self.logger.info(f"开始训练 {model_type} 模型 {i+1}/{self.n_models}")
                model.learn(
                    total_timesteps=total_timesteps // (len(self.model_types) * self.n_models),
                    callback=callback
                )

                # 添加到模型列表
                self.models.append(model)
                self.logger.info(f"{model_type} 模型 {i+1}/{self.n_models} 训练完成")

        # 创建集成模型
        self.ensemble_model = EnsembleModel(
            models=self.models,
            voting_method=self.voting_method
        )

        self.logger.info(f"集成学习训练完成，共 {len(self.models)}/{len(self.model_types) * self.n_models} 个模型")

        return self.ensemble_model

    def save(self, path: str) -> str:
        """
        保存集成模型

        参数:
            path (str): 保存路径

        返回:
            str: 保存路径
        """
        if self.ensemble_model is None:
            raise ValueError("集成模型尚未训练")

        return self.ensemble_model.save(path)

    @classmethod
    def load(cls, path: str, env) -> Tuple['EnsembleLearning', EnsembleModel]:
        """
        加载集成模型

        参数:
            path (str): 加载路径
            env: 环境

        返回:
            Tuple[EnsembleLearning, EnsembleModel]: 集成学习实例和集成模型
        """
        # 加载集成模型
        ensemble_model = EnsembleModel.load(path)

        # 创建集成学习实例
        ensemble_learning = cls(env=env)
        ensemble_learning.ensemble_model = ensemble_model
        ensemble_learning.models = ensemble_model.models

        return ensemble_learning, ensemble_model

class EnsembleModel:
    """
    集成学习模型类
    实现多种集成学习策略，包括投票、加权投票、堆叠等
    """

    def __init__(self, models: List[BaseAlgorithm], voting_method: str = 'majority', weights: Optional[List[float]] = None):
        """
        初始化集成学习模型

        参数:
            models (List[BaseAlgorithm]): 模型列表
            voting_method (str): 投票方法，可选值：'majority'（多数投票）, 'weighted'（加权投票）, 'soft'（软投票）
            weights (List[float], optional): 模型权重，仅在voting_method='weighted'时使用
        """
        self.models = models
        self.voting_method = voting_method

        # 检查模型列表是否为空
        if not models:
            raise ValueError("模型列表不能为空")

        # 检查所有模型是否为同一类型
        model_types = set(type(model) for model in models)
        if len(model_types) > 1:
            logger.warning("集成学习中包含不同类型的模型，可能导致预测结果不一致")

        # 设置权重
        if weights is None:
            self.weights = [1.0 / len(models)] * len(models)
        else:
            if len(weights) != len(models):
                raise ValueError(f"权重数量 ({len(weights)}) 与模型数量 ({len(models)}) 不匹配")
            # 归一化权重
            total_weight = sum(weights)
            self.weights = [w / total_weight for w in weights]

        logger.info(f"创建集成学习模型，共 {len(models)} 个模型，投票方法: {voting_method}")

    def predict(self, observation, deterministic: bool = True) -> Tuple[np.ndarray, Optional[np.ndarray]]:
        """
        使用集成模型进行预测

        参数:
            observation: 环境观测
            deterministic (bool): 是否使用确定性策略

        返回:
            Tuple[np.ndarray, Optional[np.ndarray]]: 预测的动作和状态
        """
        actions = []
        states = []

        # 收集所有模型的预测结果
        for model in self.models:
            action, state = model.predict(observation, deterministic=deterministic)
            actions.append(action)
            if state is not None:
                states.append(state)

        # 根据投票方法合并预测结果
        if self.voting_method == 'majority':
            # 多数投票（适用于离散动作空间）
            final_action = self._majority_vote(actions)
        elif self.voting_method == 'weighted':
            # 加权投票
            final_action = self._weighted_vote(actions)
        elif self.voting_method == 'soft':
            # 软投票（取平均值，适用于连续动作空间）
            final_action = self._soft_vote(actions)
        else:
            raise ValueError(f"不支持的投票方法: {self.voting_method}")

        # 合并状态（如果有）
        final_state = np.mean(states, axis=0) if states else None

        return final_action, final_state

    def _majority_vote(self, actions: List[np.ndarray]) -> np.ndarray:
        """
        多数投票（适用于离散动作空间）

        参数:
            actions (List[np.ndarray]): 动作列表

        返回:
            np.ndarray: 投票结果
        """
        # 将动作转换为一维数组
        actions_1d = [a.item() if a.size == 1 else a for a in actions]

        # 计算每个动作的出现次数
        unique_actions, counts = np.unique(actions_1d, return_counts=True)

        # 选择出现次数最多的动作
        majority_action = unique_actions[np.argmax(counts)]

        return np.array([majority_action])

    def _weighted_vote(self, actions: List[np.ndarray]) -> np.ndarray:
        """
        加权投票

        参数:
            actions (List[np.ndarray]): 动作列表

        返回:
            np.ndarray: 投票结果
        """
        # 对于离散动作空间
        if actions[0].size == 1:
            # 将动作转换为一维数组
            actions_1d = [a.item() for a in actions]

            # 计算每个动作的加权得分
            action_scores = {}
            for action, weight in zip(actions_1d, self.weights):
                action_scores[action] = action_scores.get(action, 0) + weight

            # 选择得分最高的动作
            weighted_action = max(action_scores.items(), key=lambda x: x[1])[0]

            return np.array([weighted_action])

        # 对于连续动作空间，使用加权平均
        else:
            weighted_action = np.zeros_like(actions[0])
            for action, weight in zip(actions, self.weights):
                weighted_action += action * weight

            return weighted_action

    def _soft_vote(self, actions: List[np.ndarray]) -> np.ndarray:
        """
        软投票（取平均值，适用于连续动作空间）

        参数:
            actions (List[np.ndarray]): 动作列表

        返回:
            np.ndarray: 投票结果
        """
        return np.mean(actions, axis=0)

    def save(self, path: str) -> List[str]:
        """
        保存集成模型

        参数:
            path (str): 保存路径

        返回:
            List[str]: 保存的模型路径列表
        """
        # 创建保存目录
        os.makedirs(path, exist_ok=True)

        # 保存每个模型
        model_paths = []
        for i, model in enumerate(self.models):
            model_path = os.path.join(path, f"model_{i}.zip")
            model.save(model_path)
            model_paths.append(model_path)

        # 保存集成配置
        config = {
            'voting_method': self.voting_method,
            'weights': self.weights,
            'model_paths': model_paths
        }

        # 保存配置到JSON文件
        import json
        config_path = os.path.join(path, "ensemble_config.json")
        with open(config_path, 'w') as f:
            json.dump(config, f)

        logger.info(f"集成模型已保存到 {path}")

        return model_paths

    @classmethod
    def load(cls, path: str) -> 'EnsembleModel':
        """
        加载集成模型

        参数:
            path (str): 加载路径

        返回:
            EnsembleModel: 加载的集成模型
        """
        # 加载配置
        import json
        config_path = os.path.join(path, "ensemble_config.json")
        with open(config_path, 'r') as f:
            config = json.load(f)

        # 加载每个模型
        models = []
        for model_path in config['model_paths']:
            # 根据文件名判断模型类型
            if 'PPO' in model_path:
                model = PPO.load(model_path)
            elif 'A2C' in model_path:
                model = A2C.load(model_path)
            elif 'DQN' in model_path:
                model = DQN.load(model_path)
            elif 'SAC' in model_path:
                model = SAC.load(model_path)
            else:
                # 默认使用PPO
                model = PPO.load(model_path)

            models.append(model)

        # 创建集成模型
        ensemble = cls(
            models=models,
            voting_method=config['voting_method'],
            weights=config['weights']
        )

        logger.info(f"集成模型已从 {path} 加载")

        return ensemble
