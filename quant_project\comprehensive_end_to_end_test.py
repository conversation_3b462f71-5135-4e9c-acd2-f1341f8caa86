#!/usr/bin/env python3
"""
综合端到端测试脚本
模拟用户完整工作流程，检测所有潜在问题
"""

import sys
import os
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import traceback
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('comprehensive_test')

class ComprehensiveTestSuite:
    """综合测试套件"""

    def __init__(self):
        self.test_results = {}
        self.issues_found = []
        self.test_data = None

    def log_issue(self, test_name, issue_description, error=None):
        """记录发现的问题"""
        issue = {
            'test': test_name,
            'description': issue_description,
            'error': str(error) if error else None,
            'timestamp': datetime.now().isoformat()
        }
        self.issues_found.append(issue)
        logger.error(f"❌ {test_name}: {issue_description}")
        if error:
            logger.error(f"   错误详情: {str(error)}")

    def log_success(self, test_name, message):
        """记录成功的测试"""
        logger.info(f"✅ {test_name}: {message}")
        self.test_results[test_name] = {'status': 'success', 'message': message}

    def test_data_acquisition_parameter_validation(self):
        """测试数据获取参数验证"""
        logger.info("=" * 60)
        logger.info("测试1: 数据获取参数验证")
        logger.info("=" * 60)

        try:
            # 尝试不同的导入路径
            try:
                from core_logic.data_handling.adapter import DataHandlerAdapter
            except ImportError:
                try:
                    from core_logic.data_handling import DataHandler as DataHandlerAdapter
                except ImportError:
                    from core_logic.data_handler import DataHandler as DataHandlerAdapter

            adapter = DataHandlerAdapter()

            # 测试用例：不同的参数组合
            test_cases = [
                {
                    'name': '上证指数_日线_2024年1月',
                    'params': {
                        'stock_code': 'sh000001',
                        'start_date': '2024-01-01',
                        'end_date': '2024-01-31',
                        'frequency': '日线',
                        'data_source': '股票'
                    }
                },
                {
                    'name': '深证成指_日线_2024年2月',
                    'params': {
                        'stock_code': 'index_399001',
                        'start_date': '2024-02-01',
                        'end_date': '2024-02-29',
                        'frequency': '日线',
                        'data_source': '指数'
                    }
                },
                {
                    'name': '上证指数_英文频率_2024年3月',
                    'params': {
                        'stock_code': 'sh000001',
                        'start_date': '2024-03-01',
                        'end_date': '2024-03-31',
                        'frequency': 'D',  # 英文格式
                        'data_source': '股票'
                    }
                }
            ]

            results = {}
            for test_case in test_cases:
                try:
                    logger.info(f"测试用例: {test_case['name']}")
                    data = adapter.get_stock_data(**test_case['params'])

                    if data is not None and not data.empty:
                        # 验证数据时间范围
                        data_start = data.index.min().strftime('%Y-%m-%d')
                        data_end = data.index.max().strftime('%Y-%m-%d')
                        expected_start = test_case['params']['start_date']
                        expected_end = test_case['params']['end_date']

                        # 检查时间范围是否正确
                        if data_start <= expected_start and data_end >= expected_start:
                            results[test_case['name']] = {
                                'status': 'success',
                                'records': len(data),
                                'date_range': f"{data_start} 至 {data_end}",
                                'columns': list(data.columns)
                            }
                            logger.info(f"  ✅ 成功: {len(data)}条记录, 时间范围: {data_start} 至 {data_end}")
                        else:
                            self.log_issue(
                                test_case['name'],
                                f"数据时间范围不符合预期。期望: {expected_start} 至 {expected_end}, 实际: {data_start} 至 {data_end}"
                            )
                    else:
                        self.log_issue(test_case['name'], "返回的数据为空")

                except Exception as e:
                    self.log_issue(test_case['name'], "数据获取失败", e)

            # 保存第一个成功的数据用于后续测试
            for test_case in test_cases:
                if test_case['name'] in results and results[test_case['name']]['status'] == 'success':
                    self.test_data = adapter.get_stock_data(**test_case['params'])
                    logger.info(f"保存测试数据: {test_case['name']}")
                    break

            if results:
                self.log_success("数据获取参数验证", f"完成 {len(results)} 个测试用例")
            else:
                self.log_issue("数据获取参数验证", "所有测试用例都失败")

        except Exception as e:
            self.log_issue("数据获取参数验证", "测试模块导入或初始化失败", e)

    def test_feature_engineering(self):
        """测试特征工程"""
        logger.info("\n" + "=" * 60)
        logger.info("测试2: 特征工程")
        logger.info("=" * 60)

        if self.test_data is None:
            self.log_issue("特征工程", "没有可用的测试数据")
            return

        try:
            from core_logic.feature_engineering.adapter import FeatureEngineeringAdapter
            fe_adapter = FeatureEngineeringAdapter()

            # 测试特征工程
            features = fe_adapter.engineer_features(self.test_data)

            if features is not None and not features.empty:
                logger.info(f"  ✅ 特征工程成功: {features.shape[0]}行 x {features.shape[1]}列")
                logger.info(f"  特征列: {list(features.columns)}")

                # 检查是否有缺失值
                missing_values = features.isnull().sum().sum()
                if missing_values > 0:
                    self.log_issue("特征工程", f"生成的特征包含 {missing_values} 个缺失值")
                else:
                    self.log_success("特征工程", f"成功生成 {features.shape[1]} 个特征，无缺失值")

                # 保存特征数据用于后续测试
                self.test_features = features
            else:
                self.log_issue("特征工程", "特征工程返回空数据")

        except Exception as e:
            self.log_issue("特征工程", "特征工程失败", e)

    def test_trading_environment(self):
        """测试交易环境"""
        logger.info("\n" + "=" * 60)
        logger.info("测试3: 交易环境")
        logger.info("=" * 60)

        if not hasattr(self, 'test_features') or self.test_features is None:
            self.log_issue("交易环境", "没有可用的特征数据")
            return

        try:
            from core_logic.trading_env.adapter import TradingEnvironmentAdapter
            env_adapter = TradingEnvironmentAdapter()

            # 创建交易环境
            env = env_adapter.create_environment(self.test_features)

            if env is not None:
                # 测试环境重置
                obs, info = env.reset()
                logger.info(f"  ✅ 环境重置成功，观察空间形状: {obs.shape}")

                # 测试几步交易
                for step in range(5):
                    action = env.action_space.sample()  # 随机动作
                    obs, reward, terminated, truncated, info = env.step(action)
                    logger.info(f"  步骤 {step+1}: 动作={action}, 奖励={reward:.4f}, 结束={terminated}")

                    if terminated or truncated:
                        break

                self.log_success("交易环境", "环境创建和基本操作测试通过")
                self.test_env = env
            else:
                self.log_issue("交易环境", "环境创建失败")

        except Exception as e:
            self.log_issue("交易环境", "交易环境测试失败", e)

    def test_model_training(self):
        """测试模型训练"""
        logger.info("\n" + "=" * 60)
        logger.info("测试4: 模型训练")
        logger.info("=" * 60)

        if not hasattr(self, 'test_env') or self.test_env is None:
            self.log_issue("模型训练", "没有可用的交易环境")
            return

        try:
            from core_logic.drl_agent.ppo_agent import PPOAgent

            # 创建PPO智能体
            agent = PPOAgent(self.test_env)

            # 短时间训练测试
            logger.info("  开始短时间训练测试...")
            agent.train(total_timesteps=1000)  # 很短的训练时间

            # 测试预测
            obs, _ = self.test_env.reset()
            action = agent.predict_action(obs)
            logger.info(f"  ✅ 训练完成，预测动作: {action}")

            self.log_success("模型训练", "PPO智能体训练和预测测试通过")
            self.test_agent = agent

        except Exception as e:
            self.log_issue("模型训练", "模型训练失败", e)

    def test_backtesting(self):
        """测试回测功能"""
        logger.info("\n" + "=" * 60)
        logger.info("测试5: 回测功能")
        logger.info("=" * 60)

        if not hasattr(self, 'test_agent') or self.test_agent is None:
            self.log_issue("回测功能", "没有可用的训练模型")
            return

        try:
            from core_logic.backtest.backtest_engine import BacktestEngine

            # 创建回测引擎
            backtest_engine = BacktestEngine()

            # 运行回测
            backtest_results = backtest_engine.run_backtest(
                data=self.test_data,
                strategy=self.test_agent
            )

            if backtest_results and 'status' not in backtest_results:
                logger.info("  ✅ 回测执行成功")

                # 检查回测结果
                if 'portfolio_values' in backtest_results:
                    portfolio_values = backtest_results['portfolio_values']
                    logger.info(f"  组合价值序列长度: {len(portfolio_values)}")

                if 'trades' in backtest_results:
                    trades = backtest_results['trades']
                    logger.info(f"  交易记录数量: {len(trades)}")

                self.log_success("回测功能", "回测执行成功并返回结果")
            else:
                error_msg = backtest_results.get('message', '未知错误') if backtest_results else '回测返回空结果'
                self.log_issue("回测功能", f"回测失败: {error_msg}")

        except Exception as e:
            self.log_issue("回测功能", "回测执行异常", e)

    def generate_test_report(self):
        """生成测试报告"""
        logger.info("\n" + "=" * 60)
        logger.info("测试报告")
        logger.info("=" * 60)

        total_tests = len(self.test_results) + len(self.issues_found)
        successful_tests = len(self.test_results)
        failed_tests = len(self.issues_found)

        logger.info(f"总测试数: {total_tests}")
        logger.info(f"成功测试: {successful_tests}")
        logger.info(f"失败测试: {failed_tests}")
        logger.info(f"成功率: {successful_tests/total_tests*100:.1f}%" if total_tests > 0 else "0%")

        if self.issues_found:
            logger.info("\n发现的问题:")
            for i, issue in enumerate(self.issues_found, 1):
                logger.info(f"{i}. {issue['test']}: {issue['description']}")

        # 保存详细报告
        report = {
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total_tests': total_tests,
                'successful_tests': successful_tests,
                'failed_tests': failed_tests,
                'success_rate': successful_tests/total_tests*100 if total_tests > 0 else 0
            },
            'successful_tests': self.test_results,
            'issues_found': self.issues_found
        }

        with open('comprehensive_test_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        logger.info(f"\n详细报告已保存到: comprehensive_test_report.json")

        return report

def main():
    """主测试函数"""
    logger.info("开始综合端到端测试...")
    logger.info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    test_suite = ComprehensiveTestSuite()

    # 执行所有测试
    test_suite.test_data_acquisition_parameter_validation()
    test_suite.test_feature_engineering()
    test_suite.test_trading_environment()
    test_suite.test_model_training()
    test_suite.test_backtesting()

    # 生成报告
    report = test_suite.generate_test_report()

    return report

if __name__ == "__main__":
    main()
