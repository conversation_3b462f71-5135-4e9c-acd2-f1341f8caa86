# 自动因子挖掘系统使用指南

本指南详细介绍如何使用DRL量化交易系统中的自动因子挖掘功能，包括功能概述、基本用法和高级技巧。

## 1. 功能概述

自动因子挖掘系统是DRL量化交易系统的强大扩展，它可以：

- **自动生成交易因子**：基于技术指标、交叉特征和时序特征
- **评估因子有效性**：通过IC值和IR值等指标
- **选择最佳因子组合**：去除冗余因子，选取预测能力最强的因子
- **将因子应用于DRL模型**：无缝集成到模型训练流程

## 2. 快速开始

### 2.1 启动系统

```bash
# 方法1：使用启动脚本
python quant_project/start_system.py
# 然后选择选项2: "启动带自动因子挖掘的系统"

# 方法2：直接指定模式
python quant_project/start_system.py --mode factor_mining

# 方法3：直接运行主应用
python -m streamlit run quant_project/main_app_with_factor_mining.py --server.headless true
```

> 注意：添加 `--server.headless true` 参数可以在服务器上运行时阻止自动打开浏览器。在本地开发时可以省略此参数。

### 2.2 基本工作流程

1. 在导航栏中选择"自动因子挖掘"标签页
2. 设置股票代码、时间范围和参数
3. 点击"开始挖掘因子"按钮
4. 查看评估结果和可视化
5. 在"因子应用"标签页应用因子进行模型训练

## 3. 详细使用说明

### 3.1 因子挖掘配置

在"因子挖掘配置"标签页中：

| 参数 | 说明 | 推荐值 |
|------|------|--------|
| 股票/指数代码 | 用于挖掘因子的标的代码 | sh000001（上证指数）|
| 起始日期 | 数据开始日期 | 至少1年以上数据 |
| 结束日期 | 数据结束日期 | 通常为当前日期 |
| 最小IC绝对值阈值 | IC值低于此阈值的因子会被过滤 | 0.05 |
| 相关性阈值 | 高于此相关性的因子会被合并 | 0.7 |
| 选择因子数量 | 最终选择的因子数量 | 20 |

高级设置：
- **评估周期**：计算IC值的未来收益周期
- **因子类型**：要生成的因子类型（技术指标、交叉特征、时序特征）

### 3.2 因子评估结果

挖掘完成后，系统会显示：

- **数量统计**：初始因子数量、有效因子数量、去相关后数量、最终选择数量
- **评估指标表**：每个因子的IC均值、IC标准差、IR值、不同周期的IC值

关键指标解释：
- **IC均值**：因子与未来收益的相关性平均值，越高越好
- **IR值**：信息比率，IC均值除以IC标准差，衡量因子稳定性
- **IC(1日/5日/20日)**：因子与不同周期未来收益的相关性

### 3.3 因子可视化

可视化功能包括：

- **因子值变化趋势图**：显示选定因子随时间的变化
- **因子IC热力图**：展示不同因子在不同周期的IC值
- **因子相关性矩阵**：展示因子之间的相关关系

使用技巧：
- 选择2-3个IC值最高的因子进行对比
- 关注IC值在不同周期的稳定性
- 检查高IC值因子之间的相关性，避免冗余

### 3.4 因子应用

在"因子应用"标签页中：

1. **选择要应用的因子**：从挖掘出的因子中选择要使用的因子
2. **选择因子应用方式**：平等权重或基于IC值加权
3. **配置训练参数**：
   - 股票代码和日期范围
   - DRL算法（PPO、A2C、DDPG等）
   - 学习率、批量大小等高级参数
4. **点击"应用因子并开始训练模型"**：系统会使用选定的因子训练DRL模型
5. **查看回测结果**：训练完成后，系统会自动执行回测并展示性能指标
6. **保存模型和因子**：可以保存训练好的模型和使用的因子，以便后续使用

## 4. 高级用法

### 4.1 通过代码使用因子挖掘系统

可以通过Python代码直接使用因子挖掘系统：

```python
from core_logic.data_handler import DataHandler
from core_logic.auto_factor_mining import AutoFactorPipeline, AdaptiveFactorSystem

# 初始化数据处理器
data_handler = DataHandler(cache_dir='data_cache')

# 使用流水线挖掘因子
pipeline = AutoFactorPipeline(data_handler)
best_factors, evaluation_results, stats = pipeline.run(
    stock_code='sh000001',
    start_date='2020-01-01',
    end_date='2023-01-01',
    min_ic_abs=0.05,
    corr_threshold=0.7,
    top_n_factors=20
)

# 使用自适应系统训练模型
adaptive_system = AdaptiveFactorSystem(data_handler)
model, factors, train_stats = adaptive_system.train_with_best_factors(
    stock_code='sh000001',
    env_config={...},
    agent_config={...}
)
```

### 4.2 导出和导入因子

导出因子以便后续使用：

```python
from core_logic.auto_factor_mining import AdaptiveFactorSystem

# 初始化系统
system = AdaptiveFactorSystem(data_handler)

# 导出因子
system.export_factors(best_factors, "factors/my_best_factors.pkl")

# 导入因子
imported_factors = system.import_factors("factors/my_best_factors.pkl")
```

### 4.3 自定义因子生成

高级用户可以扩展FactorGenerator类，添加自定义因子：

```python
from core_logic.auto_factor_mining import FactorGenerator

class MyFactorGenerator(FactorGenerator):
    def generate_custom_factors(self, df):
        """生成自定义因子"""
        custom_factors = {}
        # 添加自定义因子逻辑，例如：
        custom_factors['my_factor'] = df['close'] / df['close'].rolling(window=5).mean()
        return custom_factors
```

## 5. 常见问题与解决方案

### 5.1 因子挖掘失败

**可能原因**：
- 数据量不足
- 股票代码错误
- IC阈值设置过高

**解决方案**：
- 确保选择的时间范围至少包含100个交易日
- 检查股票代码格式（如sh000001）
- 尝试降低IC阈值（如从0.05降至0.03）

### 5.2 挖掘的因子质量不佳

**可能原因**：
- 数据质量问题
- 选择的股票波动性低
- 时间范围过短

**解决方案**：
- 尝试不同的股票或指数
- 延长历史数据时间范围
- 尝试不同类型的因子组合

### 5.3 模型训练问题

**可能原因**：
- 因子数量过多导致维度灾难
- 训练步数不足
- 学习率设置不当

**解决方案**：
- 减少使用的因子数量（10-20个为宜）
- 增加训练步数（至少10万步）
- 调整学习率（通常在0.0001-0.001之间）

## 6. 性能优化建议

1. **数据预处理**：确保数据已正确清洗，无异常值和缺失值
2. **因子选择**：优先选择IR值高的因子，它们通常更稳定
3. **去相关**：将相关性阈值设为0.6-0.7，有效减少冗余
4. **训练配置**：
   - PPO算法通常表现较好
   - 批量大小设为64-128
   - 学习率设为0.0003
   - 训练步数至少10万步
5. **定期更新**：每隔1-3个月重新挖掘一次因子，保持模型适应性

## 7. 系统限制

- 当前版本主要支持股票和指数数据
- 因子生成过程在数据量大时可能耗时较长
- 高频数据支持有限
- GPU加速仅适用于模型训练，不适用于因子挖掘过程

## 8. 后续开发计划

未来版本将添加以下功能：

- 基于机器学习的自动特征选择
- 高频数据支持
- 商品期货和加密货币市场支持
- 因子组合优化算法
- 分布式计算支持，加速大规模因子挖掘 