"""
DRL智能体回调模块
提供训练过程中的回调函数，用于记录指标、保存最佳模型等
"""

import os
import logging
import numpy as np
import pandas as pd
from stable_baselines3.common.callbacks import BaseCallback, EvalCallback
from stable_baselines3.common.vec_env import VecEnv
from stable_baselines3.common.evaluation import evaluate_policy

class MetricsCallback(BaseCallback):
    """
    指标回调
    记录训练过程中的各种指标
    """
    
    def __init__(self, verbose=0):
        """
        初始化指标回调
        
        参数:
            verbose (int): 详细程度，0为无输出，1为简单输出，2为详细输出
        """
        super(MetricsCallback, self).__init__(verbose)
        self.metrics = {
            'rewards': [],
            'episode_lengths': [],
            'learning_rates': [],
            'losses': [],
            'steps': []
        }
        self.logger = logging.getLogger('drl_trading')
    
    def _on_step(self):
        """
        每一步调用的方法
        记录当前步骤的指标
        
        返回:
            bool: 是否继续训练
        """
        # 记录步数
        self.metrics['steps'].append(self.num_timesteps)
        
        # 记录奖励
        if len(self.model.ep_info_buffer) > 0:
            self.metrics['rewards'].append(
                np.mean([ep_info['r'] for ep_info in self.model.ep_info_buffer])
            )
            self.metrics['episode_lengths'].append(
                np.mean([ep_info['l'] for ep_info in self.model.ep_info_buffer])
            )
        
        # 记录学习率
        if hasattr(self.model, 'learning_rate'):
            if callable(self.model.learning_rate):
                lr = self.model.learning_rate(1.0)  # 假设进度为1.0
            else:
                lr = self.model.learning_rate
            self.metrics['learning_rates'].append(lr)
        
        # 记录损失
        if hasattr(self.model, 'logger') and hasattr(self.model.logger, 'name_to_value'):
            for key, value in self.model.logger.name_to_value.items():
                if 'loss' in key.lower():
                    if 'losses' not in self.metrics:
                        self.metrics['losses'] = []
                    self.metrics['losses'].append(value)
        
        return True
    
    def _on_rollout_end(self):
        """
        每次rollout结束时调用的方法
        记录rollout结束时的指标
        """
        # 如果有详细输出，记录当前的平均奖励
        if self.verbose > 0 and len(self.metrics['rewards']) > 0:
            self.logger.info(f"Steps: {self.num_timesteps}, "
                             f"Mean reward: {self.metrics['rewards'][-1]:.2f}, "
                             f"Mean episode length: {self.metrics['episode_lengths'][-1]:.2f}")
    
    def get_metrics(self):
        """
        获取记录的指标
        
        返回:
            dict: 指标字典
        """
        return self.metrics

class BestModelCallback(BaseCallback):
    """
    最佳模型回调
    定期评估模型并保存最佳模型
    """
    
    def __init__(self, eval_env, eval_freq=10000, n_eval_episodes=5, save_path="saved_models/best_model",
                 save_vec_normalize=True, agent_config=None, verbose=1):
        """
        初始化最佳模型回调
        
        参数:
            eval_env: 评估环境
            eval_freq (int): 评估频率（步数）
            n_eval_episodes (int): 评估的episode数量
            save_path (str): 保存路径
            save_vec_normalize (bool): 是否保存向量化环境
            agent_config (dict): 智能体配置
            verbose (int): 详细程度
        """
        super(BestModelCallback, self).__init__(verbose)
        self.eval_env = eval_env
        self.eval_freq = eval_freq
        self.n_eval_episodes = n_eval_episodes
        self.save_path = save_path
        self.save_vec_normalize = save_vec_normalize
        self.agent_config = agent_config or {}
        
        self.best_mean_reward = -np.inf
        self.best_model_path = None
        self.last_evaluation_step = 0
        self.logger = logging.getLogger('drl_trading')
        
        # 确保目录存在
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
    
    def _on_step(self):
        """
        每一步调用的方法
        定期评估模型并保存最佳模型
        
        返回:
            bool: 是否继续训练
        """
        # 如果达到评估频率
        if self.num_timesteps - self.last_evaluation_step >= self.eval_freq:
            self.last_evaluation_step = self.num_timesteps
            
            # 评估模型
            mean_reward, std_reward = evaluate_policy(
                self.model,
                self.eval_env,
                n_eval_episodes=self.n_eval_episodes,
                deterministic=True
            )
            
            # 如果有详细输出，记录评估结果
            if self.verbose > 0:
                self.logger.info(f"评估 - 步数: {self.num_timesteps}, "
                                 f"平均奖励: {mean_reward:.2f}±{std_reward:.2f}")
            
            # 如果是新的最佳模型，保存它
            if mean_reward > self.best_mean_reward:
                self.best_mean_reward = mean_reward
                
                # 生成保存路径
                algorithm = self.agent_config.get('algorithm', 'unknown')
                save_path = f"{self.save_path}_{algorithm}_{self.num_timesteps}_steps"
                
                # 保存模型
                self.model.save(save_path)
                
                # 如果需要保存向量化环境
                if self.save_vec_normalize and isinstance(self.eval_env, VecEnv):
                    if hasattr(self.eval_env, 'save'):
                        self.eval_env.save(f"{save_path}_vecnormalize.pkl")
                
                self.best_model_path = save_path
                
                if self.verbose > 0:
                    self.logger.info(f"新的最佳模型 - 奖励: {mean_reward:.2f}, 保存到: {save_path}")
        
        return True

class RobustMetricsCallback(BaseCallback):
    """
    鲁棒指标回调
    记录训练过程中的指标，包括市场状态和风险指标
    """

    def __init__(self, agent=None, verbose=0):
        """
        初始化鲁棒指标回调
        
        参数:
            agent: 智能体实例，用于访问市场检测器和过拟合检测器
            verbose (int): 详细程度
        """
        super(RobustMetricsCallback, self).__init__(verbose)
        self.agent = agent
        
        # 初始化指标字典
        self.metrics = {
            'rewards': [],
            'episode_lengths': [],
            'learning_rates': [],
            'steps': [],
            'market_conditions': [],
            'overfitting_degrees': [],
            'risk_metrics': []
        }
        
        self.logger = logging.getLogger('drl_trading')
        
        # 检查是否有市场检测器
        self.market_detector = getattr(agent, 'market_detector', None)
        self.overfitting_detector = getattr(agent, 'overfitting_detector', None)
        self.risk_manager = getattr(agent, 'risk_manager', None)

    def _on_step(self):
        """
        每一步调用的方法
        记录当前步骤的指标
        
        返回:
            bool: 是否继续训练
        """
        # 记录步数
        self.metrics['steps'].append(self.num_timesteps)
        
        # 记录奖励
        if len(self.model.ep_info_buffer) > 0:
            rewards = [ep_info['r'] for ep_info in self.model.ep_info_buffer]
            mean_reward = np.mean(rewards)
            self.metrics['rewards'].append(mean_reward)
            self.metrics['episode_lengths'].append(
                np.mean([ep_info['l'] for ep_info in self.model.ep_info_buffer])
            )

            # 记录市场状态（如果可用）
            if self.market_detector is not None:
                try:
                    # 调用市场检测器的检测方法（假设有这样的接口）
                    market_condition = self.market_detector.detect_market_condition()
                    self.metrics['market_conditions'].append(market_condition)
                except Exception as e:
                    self.logger.warning(f"记录市场状态时出错: {str(e)}")
            
            # 检查过拟合（如果可用）
            if self.overfitting_detector is not None and len(self.metrics['rewards']) > 10:
                try:
                    # 使用最近的10个奖励作为"训练集"和"测试集"
                    train_rewards = self.metrics['rewards'][-10:-5]
                    test_rewards = self.metrics['rewards'][-5:]
                    
                    # 检测过拟合
                    is_overfitting, overfitting_degree, _ = self.overfitting_detector.detect_overfitting(
                        train_scores=train_rewards,
                        test_scores=test_rewards,
                        threshold=0.2
                    )
                    
                    # 记录过拟合程度
                    self.metrics['overfitting_degrees'].append(overfitting_degree)
                    
                    # 如果检测到过拟合，记录警告
                    if is_overfitting and self.verbose > 0:
                        self.logger.warning(f"检测到过拟合，程度: {overfitting_degree:.4f}")
                except Exception as e:
                    self.logger.warning(f"检测过拟合时出错: {str(e)}")
            
            # 记录风险指标（如果可用）
            if self.risk_manager is not None:
                try:
                    # 计算风险指标（假设有这样的接口）
                    risk_metrics = self.risk_manager.calculate_risk_metrics(rewards)
                    self.metrics['risk_metrics'].append(risk_metrics)
                except Exception as e:
                    self.logger.warning(f"记录风险指标时出错: {str(e)}")
        
        # 记录学习率
        if hasattr(self.model, 'learning_rate'):
            if callable(self.model.learning_rate):
                lr = self.model.learning_rate(1.0)  # 假设进度为1.0
            else:
                lr = self.model.learning_rate
            self.metrics['learning_rates'].append(lr)
        
        return True 