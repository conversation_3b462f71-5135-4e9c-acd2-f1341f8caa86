"""
测试模型保存和加载功能
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('model_test')

# 导入核心逻辑模块
try:
    from quant_project.core_logic.data_handler import DataHandler
    from quant_project.core_logic.feature_engineer import FeatureEngineer
    from quant_project.core_logic.trading_environment import TradingEnvironment
    from quant_project.core_logic.drl_agent import DRLAgent, BestModelCallback
    from quant_project.core_logic.utils import is_gpu_available
except ImportError:
    try:
        from core_logic.data_handler import DataHandler
        from core_logic.feature_engineer import FeatureEngineer
        from core_logic.trading_environment import TradingEnvironment
        from core_logic.drl_agent import DRLAgent, BestModelCallback
        from core_logic.utils import is_gpu_available
    except ImportError as e:
        logger.error(f"导入模块失败: {str(e)}")
        sys.exit(1)

def test_model_save_load():
    """测试模型保存和加载功能"""
    logger.info("开始测试模型保存和加载功能")
    
    # 步骤1: 创建模拟数据
    logger.info("创建模拟数据...")
    dates = pd.date_range(start='2020-01-01', end='2021-01-01')
    data = pd.DataFrame({
        '开盘': np.random.normal(100, 10, len(dates)),
        '最高': np.random.normal(105, 10, len(dates)),
        '最低': np.random.normal(95, 10, len(dates)),
        '收盘': np.random.normal(100, 10, len(dates)),
        '成交量': np.random.normal(1000000, 500000, len(dates)),
    }, index=dates)
    
    # 确保价格合理
    for i in range(len(data)):
        data.iloc[i, 1] = max(data.iloc[i, [0, 1, 2, 3]])  # 最高价是最高的
        data.iloc[i, 2] = min(data.iloc[i, [0, 1, 2, 3]])  # 最低价是最低的
    
    logger.info(f"模拟数据创建完成，形状: {data.shape}")
    
    # 步骤2: 生成特征
    logger.info("生成特征...")
    feature_config = {
        'sma': {'use': True, 'periods': [5, 20]},
        'ema': {'use': True, 'periods': [5, 20]},
        'rsi': {'use': True, 'period': 14},
        'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9},
        'bbands': {'use': True, 'period': 20, 'std': 2.0},
        'atr': {'use': True, 'period': 14}
    }
    
    feature_engineer = FeatureEngineer(feature_config)
    processed_data = feature_engineer.generate_features(data)
    processed_data = processed_data.dropna()
    logger.info(f"特征生成完成，处理后数据形状: {processed_data.shape}")
    
    # 步骤3: 创建环境和智能体
    logger.info("创建环境和智能体...")
    env_config = {
        'df_processed_data': processed_data,
        'initial_capital': 100000,
        'commission_rate': 0.0003,
        'min_hold_days': 3,
        'window_size': 20
    }
    
    agent_config = {
        'algorithm': 'PPO',
        'policy_network': 'MlpPolicy',
        'learning_rate': 0.0003,
        'gamma': 0.99,
        'n_steps': 64,
        'batch_size': 32,
        'n_epochs': 5,
        'use_gpu': is_gpu_available()
    }
    
    drl_agent = DRLAgent(env_config, agent_config)
    logger.info("环境和智能体创建完成")
    
    # 步骤4: 训练模型（短时间）
    logger.info("开始训练模型（短时间）...")
    total_timesteps = 1000  # 短时间训练
    
    # 创建评估环境
    eval_env = drl_agent._create_environment()
    
    # 创建最佳模型回调
    best_model_callback = BestModelCallback(
        eval_env=eval_env,
        eval_freq=200,
        n_eval_episodes=2,
        save_path="saved_models/test_best_model.zip",
        verbose=1
    )
    
    # 训练模型
    drl_agent.train(
        total_timesteps=total_timesteps,
        callback_list=[best_model_callback],
        progress_bar=True,
        save_best_model=True
    )
    logger.info("模型训练完成")
    
    # 步骤5: 保存模型
    logger.info("保存模型...")
    model_path = drl_agent.save_model("saved_models/test_model.zip")
    logger.info(f"模型已保存到: {model_path}")
    
    # 步骤6: 加载模型
    logger.info("加载模型...")
    loaded_agent = DRLAgent.load_model(model_path, env_config)
    logger.info("模型加载成功")
    
    # 步骤7: 加载最佳模型
    best_model_path = "saved_models/test_best_model.zip"
    if os.path.exists(best_model_path):
        logger.info("加载最佳模型...")
        best_agent = DRLAgent.load_model(best_model_path, env_config)
        logger.info("最佳模型加载成功")
    else:
        logger.warning("最佳模型文件不存在，跳过加载")
    
    # 步骤8: 测试预测功能
    logger.info("测试预测功能...")
    observation, _ = eval_env.reset()
    action = loaded_agent.predict_action(observation)
    logger.info(f"预测动作: {action}")
    
    logger.info("测试完成")
    return True

if __name__ == "__main__":
    try:
        success = test_model_save_load()
        if success:
            logger.info("所有测试通过")
        else:
            logger.error("测试失败")
    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}", exc_info=True)
