"""
集成学习模块
实现多种集成学习策略，包括投票、加权投票、堆叠等
"""

import os
import logging
import numpy as np
import pandas as pd
from typing import List, Dict, Any, Tuple, Union, Optional
from stable_baselines3.common.base_class import BaseAlgorithm
from stable_baselines3.common.vec_env import VecEnv
from stable_baselines3.common.callbacks import BaseCallback
from stable_baselines3.common.evaluation import evaluate_policy
from stable_baselines3.common.utils import set_random_seed
from stable_baselines3 import PPO, A2C, DQN, SAC

logger = logging.getLogger('drl_trading')

class EnsembleModel:
    """
    集成学习模型类
    实现多种集成学习策略，包括投票、加权投票、堆叠等
    """

    def __init__(self, models: List[BaseAlgorithm], voting_method: str = 'majority', weights: Optional[List[float]] = None):
        """
        初始化集成学习模型

        参数:
            models (List[BaseAlgorithm]): 模型列表
            voting_method (str): 投票方法，可选值：'majority'（多数投票）, 'weighted'（加权投票）, 'soft'（软投票）
            weights (List[float], optional): 模型权重，仅在voting_method='weighted'时使用
        """
        self.models = models
        self.voting_method = voting_method
        
        # 检查模型列表是否为空
        if not models:
            raise ValueError("模型列表不能为空")
        
        # 检查所有模型是否为同一类型
        model_types = set(type(model) for model in models)
        if len(model_types) > 1:
            logger.warning("集成学习中包含不同类型的模型，可能导致预测结果不一致")
        
        # 设置权重
        if weights is None:
            self.weights = [1.0 / len(models)] * len(models)
        else:
            if len(weights) != len(models):
                raise ValueError(f"权重数量 ({len(weights)}) 与模型数量 ({len(models)}) 不匹配")
            # 归一化权重
            total_weight = sum(weights)
            self.weights = [w / total_weight for w in weights]
        
        logger.info(f"创建集成学习模型，共 {len(models)} 个模型，投票方法: {voting_method}")

    def predict(self, observation, deterministic: bool = True) -> Tuple[np.ndarray, Optional[np.ndarray]]:
        """
        使用集成模型进行预测

        参数:
            observation: 环境观测
            deterministic (bool): 是否使用确定性策略

        返回:
            Tuple[np.ndarray, Optional[np.ndarray]]: 预测的动作和状态
        """
        actions = []
        states = []
        
        # 收集所有模型的预测结果
        for model in self.models:
            action, state = model.predict(observation, deterministic=deterministic)
            actions.append(action)
            if state is not None:
                states.append(state)
        
        # 根据投票方法合并预测结果
        if self.voting_method == 'majority':
            # 多数投票（适用于离散动作空间）
            final_action = self._majority_vote(actions)
        elif self.voting_method == 'weighted':
            # 加权投票
            final_action = self._weighted_vote(actions)
        elif self.voting_method == 'soft':
            # 软投票（取平均值，适用于连续动作空间）
            final_action = self._soft_vote(actions)
        else:
            raise ValueError(f"不支持的投票方法: {self.voting_method}")
        
        # 合并状态（如果有）
        final_state = np.mean(states, axis=0) if states else None
        
        return final_action, final_state

    def _majority_vote(self, actions: List[np.ndarray]) -> np.ndarray:
        """
        多数投票（适用于离散动作空间）

        参数:
            actions (List[np.ndarray]): 动作列表

        返回:
            np.ndarray: 投票结果
        """
        # 将动作转换为一维数组
        actions_1d = [a.item() if a.size == 1 else a for a in actions]
        
        # 计算每个动作的出现次数
        unique_actions, counts = np.unique(actions_1d, return_counts=True)
        
        # 选择出现次数最多的动作
        majority_action = unique_actions[np.argmax(counts)]
        
        return np.array([majority_action])

    def _weighted_vote(self, actions: List[np.ndarray]) -> np.ndarray:
        """
        加权投票

        参数:
            actions (List[np.ndarray]): 动作列表

        返回:
            np.ndarray: 投票结果
        """
        # 对于离散动作空间
        if actions[0].size == 1:
            # 将动作转换为一维数组
            actions_1d = [a.item() for a in actions]
            
            # 计算每个动作的加权得分
            action_scores = {}
            for action, weight in zip(actions_1d, self.weights):
                action_scores[action] = action_scores.get(action, 0) + weight
            
            # 选择得分最高的动作
            weighted_action = max(action_scores.items(), key=lambda x: x[1])[0]
            
            return np.array([weighted_action])
        
        # 对于连续动作空间，使用加权平均
        else:
            weighted_action = np.zeros_like(actions[0])
            for action, weight in zip(actions, self.weights):
                weighted_action += action * weight
            
            return weighted_action

    def _soft_vote(self, actions: List[np.ndarray]) -> np.ndarray:
        """
        软投票（取平均值，适用于连续动作空间）

        参数:
            actions (List[np.ndarray]): 动作列表

        返回:
            np.ndarray: 投票结果
        """
        return np.mean(actions, axis=0)

    def save(self, path: str) -> List[str]:
        """
        保存集成模型

        参数:
            path (str): 保存路径

        返回:
            List[str]: 保存的模型路径列表
        """
        # 创建保存目录
        os.makedirs(path, exist_ok=True)
        
        # 保存每个模型
        model_paths = []
        for i, model in enumerate(self.models):
            model_path = os.path.join(path, f"model_{i}.zip")
            model.save(model_path)
            model_paths.append(model_path)
        
        # 保存集成配置
        config = {
            'voting_method': self.voting_method,
            'weights': self.weights,
            'model_paths': model_paths
        }
        
        # 保存配置到JSON文件
        import json
        config_path = os.path.join(path, "ensemble_config.json")
        with open(config_path, 'w') as f:
            json.dump(config, f)
        
        logger.info(f"集成模型已保存到 {path}")
        
        return model_paths

    @classmethod
    def load(cls, path: str) -> 'EnsembleModel':
        """
        加载集成模型

        参数:
            path (str): 加载路径

        返回:
            EnsembleModel: 加载的集成模型
        """
        # 加载配置
        import json
        config_path = os.path.join(path, "ensemble_config.json")
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # 加载每个模型
        models = []
        for model_path in config['model_paths']:
            # 根据文件名判断模型类型
            if 'PPO' in model_path:
                model = PPO.load(model_path)
            elif 'A2C' in model_path:
                model = A2C.load(model_path)
            elif 'DQN' in model_path:
                model = DQN.load(model_path)
            elif 'SAC' in model_path:
                model = SAC.load(model_path)
            else:
                # 默认使用PPO
                model = PPO.load(model_path)
            
            models.append(model)
        
        # 创建集成模型
        ensemble = cls(
            models=models,
            voting_method=config['voting_method'],
            weights=config['weights']
        )
        
        logger.info(f"集成模型已从 {path} 加载")
        
        return ensemble
