#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试特征工程模块
"""

import os
import sys
import unittest
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core_logic.data_handler import DataHandler
from core_logic.feature_engineer import FeatureEngineer
import logging

# 设置日志
logger = logging.getLogger('drl_trading')
logger.setLevel(logging.INFO)
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)
logger.addHandler(console_handler)

class TestFeatureEngineering(unittest.TestCase):
    """测试特征工程功能"""

    @classmethod
    def setUpClass(cls):
        """测试前的准备工作"""
        # 获取测试数据
        cls.data_handler = DataHandler()
        start_date = '2023-01-01'
        end_date = '2023-03-31'
        cls.stock_data = cls.data_handler.get_stock_data('sh000001', start_date, end_date, '日线')

        # 初始化特征工程器
        # 配置基本特征
        feature_config = {
            'price_features': {'use': True},
            'sma': {'use': True, 'periods': [5, 10, 20]},
            'rsi': {'use': True, 'periods': [14]},
            'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9},
            'bbands': {'use': True, 'period': 20, 'std': 2.0},
            'rolling_stats': {'use': True, 'windows': [5, 10, 20]},
            'normalization': {'use': True, 'method': 'minmax'}
        }
        cls.feature_engineer = FeatureEngineer(feature_config=feature_config)

        # 确保数据存在
        if cls.stock_data is None or len(cls.stock_data) == 0:
            raise ValueError("无法获取测试数据")

        logger.info(f"测试数据准备完成，共 {len(cls.stock_data)} 条记录")

    def test_basic_features(self):
        """测试基本特征生成"""
        logger.info("测试基本特征生成")

        # 生成特征
        df_features = self.feature_engineer.generate_features(self.stock_data)

        # 检查特征是否生成
        self.assertIsNotNone(df_features)
        self.assertGreater(len(df_features.columns), len(self.stock_data.columns))

        # 检查是否生成了足够的特征
        self.assertGreater(len(df_features.columns), 20)

        # 检查一些常见特征类型是否存在
        feature_patterns = ['Rolling', 'trend', 'volatility', '相对', '波动率']
        found_patterns = False
        for pattern in feature_patterns:
            if any(pattern in col for col in df_features.columns):
                found_patterns = True
                break
        self.assertTrue(found_patterns, "未找到任何预期的特征模式")

        # 检查特征值是否合理
        # 检查收盘价是否为正值
        self.assertTrue(df_features['收盘'].iloc[-1] > 0)

        # 检查归一化特征是否在0-1范围内
        normalized_features = [col for col in df_features.columns if 'Rolling' in col]
        if normalized_features:
            sample_feature = normalized_features[0]
            self.assertTrue(0 <= df_features[sample_feature].iloc[-1] <= 1.1)  # 允许轻微超出1的情况

        logger.info(f"基本特征生成成功，特征数量: {len(df_features.columns)}")
        return df_features

    def test_advanced_features(self):
        """测试高级特征生成"""
        logger.info("测试高级特征生成")

        # 使用初始化时配置的特征
        # 生成特征
        df_features = self.feature_engineer.generate_features(self.stock_data)

        # 检查特征是否生成
        self.assertIsNotNone(df_features)
        self.assertGreater(len(df_features.columns), 30)  # 应该有很多特征

        # 检查是否生成了足够的高级特征
        self.assertGreater(len(df_features.columns), 30)

        # 检查一些高级特征类型是否存在
        advanced_patterns = ['Rolling_Kurt', 'Rolling_Skew', 'Rolling_Quantile', '分形', '趋势', '波动']
        found_advanced = False
        for pattern in advanced_patterns:
            if any(pattern in col for col in df_features.columns):
                found_advanced = True
                break
        self.assertTrue(found_advanced, "未找到任何高级特征模式")

        logger.info(f"高级特征生成成功，特征数量: {len(df_features.columns)}")
        return df_features

    def test_feature_selection(self):
        """测试特征选择"""
        logger.info("测试特征选择")

        # 先生成大量特征
        df_features = self.test_advanced_features()

        # 测试特征选择
        # 创建一个简单的目标变量用于特征选择
        target = df_features['收盘'].pct_change().fillna(0)

        # 使用互信息方法选择特征
        from sklearn.feature_selection import mutual_info_regression

        # 准备特征矩阵，排除非数值列
        X = df_features.select_dtypes(include=['float64', 'int64'])

        # 计算互信息
        mi_scores = mutual_info_regression(X, target)
        mi_scores = pd.Series(mi_scores, index=X.columns)

        # 选择前10个特征
        selected_features = mi_scores.sort_values(ascending=False).head(10).index.tolist()

        # 检查是否选择了特征
        self.assertIsNotNone(selected_features)
        self.assertLess(len(selected_features), len(df_features.columns))

        logger.info(f"特征选择成功，从 {len(df_features.columns)} 个特征中选择了 {len(selected_features)} 个特征")

        # 可视化特征相关性
        if len(selected_features) > 0:
            corr_matrix = df_features[selected_features].corr()
            plt.figure(figsize=(12, 10))
            plt.matshow(corr_matrix, fignum=1)
            plt.title('Selected Features Correlation Matrix')
            plt.colorbar()
            plt.savefig('tests/feature_correlation.png')
            logger.info("特征相关性矩阵已保存到 tests/feature_correlation.png")

    def test_feature_importance(self):
        """测试特征重要性"""
        logger.info("测试特征重要性")

        # 先生成特征
        df_features = self.test_basic_features()

        # 准备目标变量（这里使用未来5天的收益率作为目标）
        df_features['target'] = df_features['收盘'].shift(-5) / df_features['收盘'] - 1
        df_features.dropna(inplace=True)

        # 计算特征重要性
        from sklearn.ensemble import RandomForestRegressor

        # 准备特征矩阵，排除非数值列和目标变量
        X = df_features.select_dtypes(include=['float64', 'int64']).drop('target', axis=1, errors='ignore')
        y = df_features['target']

        # 训练随机森林模型
        model = RandomForestRegressor(n_estimators=50, random_state=42)
        model.fit(X, y)

        # 获取特征重要性
        feature_importance = pd.Series(model.feature_importances_, index=X.columns)

        # 检查特征重要性
        self.assertIsNotNone(feature_importance)
        self.assertGreater(len(feature_importance), 0)

        logger.info(f"特征重要性计算成功，共 {len(feature_importance)} 个特征")

        # 可视化特征重要性
        if len(feature_importance) > 0:
            plt.figure(figsize=(10, 6))
            feature_importance.sort_values().plot(kind='barh')
            plt.title('Feature Importance')
            plt.tight_layout()
            plt.savefig('tests/feature_importance.png')
            logger.info("特征重要性图已保存到 tests/feature_importance.png")

    def test_feature_stability(self):
        """测试特征稳定性"""
        logger.info("测试特征稳定性")

        # 先生成特征
        df_features = self.test_basic_features()

        # 将数据分为两部分
        mid_point = len(df_features) // 2
        df_part1 = df_features.iloc[:mid_point]
        df_part2 = df_features.iloc[mid_point:]

        # 计算特征稳定性（使用分布相似度）
        from scipy.stats import ks_2samp

        # 只考虑数值型特征
        numeric_cols = df_features.select_dtypes(include=['float64', 'int64']).columns

        # 计算每个特征的KS统计量
        stability_scores = {}
        for col in numeric_cols:
            # 跳过全为常数的列
            if df_part1[col].nunique() <= 1 or df_part2[col].nunique() <= 1:
                continue

            # 计算KS统计量
            ks_stat, p_value = ks_2samp(df_part1[col].dropna(), df_part2[col].dropna())
            # 稳定性得分 = 1 - KS统计量，越接近1表示越稳定
            stability_scores[col] = 1 - ks_stat

        stability_scores = pd.Series(stability_scores)

        # 检查稳定性分数
        self.assertIsNotNone(stability_scores)
        self.assertGreater(len(stability_scores), 0)

        logger.info(f"特征稳定性计算成功，共 {len(stability_scores)} 个特征")

        # 可视化特征稳定性
        if len(stability_scores) > 0:
            plt.figure(figsize=(10, 6))
            stability_scores.sort_values().plot(kind='barh')
            plt.title('Feature Stability')
            plt.tight_layout()
            plt.savefig('tests/feature_stability.png')
            logger.info("特征稳定性图已保存到 tests/feature_stability.png")

def run_tests():
    """运行所有测试"""
    logger.info("开始运行特征工程测试")
    unittest.main(argv=['first-arg-is-ignored'], exit=False)
    logger.info("特征工程测试完成")

if __name__ == '__main__':
    run_tests()
