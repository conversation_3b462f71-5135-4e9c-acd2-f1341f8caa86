"""
回测引擎模块
提供用于回测交易策略的核心功能
"""

import logging
import numpy as np
import pandas as pd
from datetime import datetime
import matplotlib.pyplot as plt
from typing import Dict, List, Union, Tuple, Optional, Any

class BacktestEngine:
    """
    回测引擎类
    用于回测交易策略，支持多种交易机制和回测参数
    """

    def __init__(self,
                 initial_capital: float = 100000.0,
                 commission_rate: float = 0.0003,
                 slippage: float = 0.0,
                 min_order_size: float = 100,
                 max_position_size: float = 1.0,
                 allow_short: bool = False,
                 allow_partial_liquidate: bool = True,
                 risk_free_rate: float = 0.0):
        """
        初始化回测引擎

        参数:
            initial_capital (float): 初始资金
            commission_rate (float): 交易佣金率
            slippage (float): 滑点率
            min_order_size (float): 最小订单规模
            max_position_size (float): 最大仓位比例 (0.0-1.0)
            allow_short (bool): 是否允许做空
            allow_partial_liquidate (bool): 是否允许部分平仓
            risk_free_rate (float): 无风险利率，年化
        """
        self.logger = logging.getLogger('drl_trading')

        # 回测参数
        self.initial_capital = initial_capital
        self.commission_rate = commission_rate
        self.slippage = slippage
        self.min_order_size = min_order_size
        self.max_position_size = max_position_size
        self.allow_short = allow_short
        self.allow_partial_liquidate = allow_partial_liquidate
        self.risk_free_rate = risk_free_rate

        # 回测状态
        self.reset()

        self.logger.info(f"初始化回测引擎: 初始资金={initial_capital}, 佣金率={commission_rate}, "
                         f"最大仓位={max_position_size}, 允许做空={allow_short}")

    def reset(self):
        """
        重置回测状态
        """
        self.capital = self.initial_capital  # 可用资金
        self.positions = {}  # 持仓 {symbol: quantity}
        self.position_values = {}  # 持仓价值 {symbol: value}
        self.portfolio_value = self.initial_capital  # 组合总价值
        self.trades = []  # 交易记录
        self.portfolio_values = pd.Series()  # 组合价值序列
        self.cash_values = pd.Series()  # 现金价值序列
        self.position_history = {}  # 持仓历史 {symbol: Series}

        self.logger.info("回测状态已重置")

    def run_backtest(self, data: pd.DataFrame, strategy: Any,
                    benchmark_symbol: Optional[str] = None) -> Dict[str, Any]:
        """
        运行回测

        参数:
            data (pandas.DataFrame): 回测数据，必须包含日期索引和OHLCV列
            strategy: 交易策略对象，必须实现predict_action方法
            benchmark_symbol (str, optional): 基准标的代码

        返回:
            dict: 回测结果，包括交易记录、组合价值序列和性能指标
        """
        self.reset()

        # 确保数据索引是日期类型并已排序
        if not isinstance(data.index, pd.DatetimeIndex):
            self.logger.warning("数据索引不是日期类型，尝试转换...")
            try:
                data.index = pd.to_datetime(data.index)
            except Exception as e:
                self.logger.error(f"无法转换索引为日期类型: {str(e)}")
                return {"status": "error", "message": "数据索引必须是日期类型"}

        data = data.sort_index()

        # 初始化结果变量
        dates = data.index
        portfolio_values = []
        cash_values = []

        # **关键修复**: 检查数据结构，支持单一标的或多标的
        if isinstance(data.columns, pd.MultiIndex):
            # 多标的格式: (symbol, field)
            symbols = list(set(data.columns.get_level_values(0)))
            self.logger.info(f"检测到多标的数据，标的数量: {len(symbols)}")
            position_quantities = {symbol: [] for symbol in symbols}
        else:
            # 单一标的格式: 假设只有一个标的
            symbols = ['default']
            # 创建MultiIndex列结构
            data.columns = pd.MultiIndex.from_product([symbols, data.columns])
            self.logger.info("检测到单一标的数据，已转换为MultiIndex格式")
            position_quantities = {symbols[0]: []}

        # 初始化持仓历史
        for symbol in symbols:
            self.position_history[symbol] = pd.Series(index=dates, data=0.0)

        # 遍历每个交易日
        prev_date = None
        for i, date in enumerate(dates):
            # 当前状态
            current_state = self._prepare_state(data, date, i)

            # 如果不是第一个交易日，执行策略
            if prev_date is not None:
                # 调用策略预测动作
                actions = strategy.predict_action(current_state)

                # 执行交易
                self._execute_trades(data, date, actions, symbols)

            # 更新组合价值
            self._update_portfolio_value(data, date)

            # 记录状态
            portfolio_values.append(self.portfolio_value)
            cash_values.append(self.capital)

            for symbol in symbols:
                if symbol in self.positions:
                    self.position_history[symbol][date] = self.positions[symbol]

            prev_date = date

        # 整理结果
        self.portfolio_values = pd.Series(index=dates, data=portfolio_values)
        self.cash_values = pd.Series(index=dates, data=cash_values)

        # 整理持仓历史
        for symbol in symbols:
            self.position_history[symbol] = self.position_history[symbol].fillna(0)

        # 计算基准价值序列
        benchmark_values = None
        if benchmark_symbol:
            if benchmark_symbol in symbols:
                # 使用收盘价作为基准
                close_prices = data.loc[:, (benchmark_symbol, '收盘')]
                benchmark_values = close_prices / close_prices.iloc[0] * self.initial_capital
            else:
                self.logger.warning(f"基准标的 {benchmark_symbol} 不在数据中")

        # 构建结果
        result = {
            "status": "success",
            "trades": pd.DataFrame(self.trades) if self.trades else pd.DataFrame(),
            "portfolio_values": self.portfolio_values,
            "cash_values": self.cash_values,
            "position_history": self.position_history,
            "benchmark_values": benchmark_values
        }

        self.logger.info(f"回测完成: 交易次数={len(self.trades)}, 最终组合价值={portfolio_values[-1]:.2f}")
        return result

    def _prepare_state(self, data: pd.DataFrame, date: pd.Timestamp, index: int) -> Dict[str, Any]:
        """
        准备当前状态

        参数:
            data (pandas.DataFrame): 回测数据
            date (pandas.Timestamp): 当前日期
            index (int): 当前索引

        返回:
            dict: 当前状态
        """
        # 构建状态字典，包含当前价格、指标和持仓信息
        state = {
            "date": date,
            "index": index,
            "data": data.loc[date],
            "capital": self.capital,
            "positions": self.positions.copy(),
            "portfolio_value": self.portfolio_value
        }
        return state

    def _execute_trades(self, data: pd.DataFrame, date: pd.Timestamp,
                        actions: Dict[str, float], symbols: List[str]) -> None:
        """
        执行交易

        参数:
            data (pandas.DataFrame): 回测数据
            date (pandas.Timestamp): 当前日期
            actions (dict): 交易动作 {symbol: position_size}，position_size为目标仓位比例
            symbols (list): 交易标的列表
        """
        # 检查actions格式
        if not isinstance(actions, dict):
            if isinstance(actions, (int, float, np.integer, np.floating)):
                # 单一标的情况，只有一个动作
                actions = {symbols[0]: float(actions)}
            else:
                self.logger.warning(f"不支持的动作格式: {type(actions)}")
                return

        # 遍历每个标的
        for symbol in symbols:
            if symbol not in actions:
                continue

            # 获取当前价格
            if (symbol, '收盘') in data.loc[date]:
                current_price = data.loc[date, (symbol, '收盘')]
            else:
                self.logger.warning(f"无法获取标的 {symbol} 的价格数据")
                continue

            # 计算目标仓位
            target_position = actions[symbol] * self.portfolio_value / current_price

            # 限制仓位大小
            max_position = self.max_position_size * self.portfolio_value / current_price
            target_position = min(target_position, max_position) if target_position > 0 else max(target_position, -max_position)

            # 获取当前持仓
            current_position = self.positions.get(symbol, 0)

            # 计算交易数量
            quantity = target_position - current_position

            # 执行交易
            if abs(quantity) >= self.min_order_size / current_price:
                self._place_order(symbol, quantity, current_price, date)

    def _place_order(self, symbol: str, quantity: float, price: float, date: pd.Timestamp) -> bool:
        """
        下单

        参数:
            symbol (str): 交易标的
            quantity (float): 交易数量，正数为买入，负数为卖出
            price (float): 交易价格
            date (pandas.Timestamp): 交易日期

        返回:
            bool: 交易是否成功
        """
        # 计算交易成本
        transaction_cost = abs(quantity * price * self.commission_rate)

        # 计算实际交易价格（考虑滑点）
        actual_price = price * (1 + self.slippage) if quantity > 0 else price * (1 - self.slippage)

        # 计算交易金额
        amount = quantity * actual_price

        # 检查资金是否足够
        if quantity > 0 and amount + transaction_cost > self.capital:
            # 资金不足，尝试调整交易数量
            max_quantity = (self.capital - transaction_cost) / actual_price
            if max_quantity <= 0:
                self.logger.warning(f"资金不足，无法买入 {symbol}")
                return False

            quantity = max_quantity
            amount = quantity * actual_price
            transaction_cost = abs(quantity * price * self.commission_rate)

            self.logger.info(f"资金不足，调整买入数量为 {quantity}")

        # 检查卖出限制
        if quantity < 0:
            current_position = self.positions.get(symbol, 0)

            if abs(quantity) > abs(current_position):
                if not self.allow_short:
                    # 不允许做空，调整卖出数量
                    quantity = -abs(current_position)
                    amount = quantity * actual_price
                    transaction_cost = abs(quantity * price * self.commission_rate)

                    if quantity == 0:
                        self.logger.warning(f"无持仓，无法卖出 {symbol}")
                        return False

                    self.logger.info(f"调整卖出数量为 {abs(quantity)}")

        # 更新资金和持仓
        self.capital -= amount + transaction_cost

        # 更新持仓
        if symbol not in self.positions:
            self.positions[symbol] = 0
        self.positions[symbol] += quantity

        # 如果持仓为零，移除该标的
        if abs(self.positions[symbol]) < 1e-10:
            del self.positions[symbol]

        # 记录交易
        trade = {
            "date": date,
            "symbol": symbol,
            "quantity": quantity,
            "price": actual_price,
            "amount": amount,
            "commission": transaction_cost,
            "type": "buy" if quantity > 0 else "sell"
        }
        self.trades.append(trade)

        self.logger.debug(f"交易执行: {trade['type']} {symbol} {abs(quantity):.2f} @ {actual_price:.2f}")
        return True

    def _update_portfolio_value(self, data: pd.DataFrame, date: pd.Timestamp) -> None:
        """
        更新组合价值

        参数:
            data (pandas.DataFrame): 回测数据
            date (pandas.Timestamp): 当前日期
        """
        # 重置组合价值
        self.portfolio_value = self.capital
        self.position_values = {}

        # 计算所有持仓的价值
        for symbol, quantity in self.positions.items():
            if (symbol, '收盘') in data.loc[date]:
                price = data.loc[date, (symbol, '收盘')]
                position_value = quantity * price
                self.position_values[symbol] = position_value
                self.portfolio_value += position_value
            else:
                self.logger.warning(f"无法获取标的 {symbol} 的价格数据，使用前一日价值")

    def get_performance_summary(self) -> Dict[str, Any]:
        """
        获取回测性能摘要

        返回:
            dict: 性能指标摘要
        """
        if len(self.portfolio_values) == 0:
            return {"status": "error", "message": "未执行回测"}

        # 计算收益率序列
        returns = self.portfolio_values.pct_change().dropna()

        # 计算基本指标
        total_return = (self.portfolio_values.iloc[-1] / self.portfolio_values.iloc[0]) - 1

        # 计算年化收益率
        days = (self.portfolio_values.index[-1] - self.portfolio_values.index[0]).days
        years = days / 365
        annual_return = (1 + total_return) ** (1 / max(years, 1e-10)) - 1

        # 计算最大回撤
        peak = self.portfolio_values.expanding().max()
        drawdown = (self.portfolio_values - peak) / peak
        max_drawdown = drawdown.min()

        # 计算夏普比率
        daily_risk_free = (1 + self.risk_free_rate) ** (1/252) - 1
        excess_returns = returns - daily_risk_free
        sharpe_ratio = excess_returns.mean() / excess_returns.std() * np.sqrt(252) if excess_returns.std() > 0 else 0

        # 计算交易统计
        trade_df = pd.DataFrame(self.trades) if self.trades else pd.DataFrame()
        total_trades = len(trade_df)

        winning_trades = 0
        losing_trades = 0
        profit_amount = 0
        loss_amount = 0

        if not trade_df.empty and 'amount' in trade_df.columns:
            # 按日期排序
            trade_df = trade_df.sort_values('date')

            # 计算盈亏
            for symbol in set(trade_df['symbol'].values):
                symbol_trades = trade_df[trade_df['symbol'] == symbol]

                # 计算每笔交易的盈亏
                for i, trade in symbol_trades.iterrows():
                    trade_type = trade['type']
                    amount = trade['amount']

                    if trade_type == 'buy':
                        profit_amount += amount
                    else:
                        loss_amount += abs(amount)

                winning_trades = sum(profit_amount > 0 for _ in symbol_trades)
                losing_trades = sum(loss_amount > 0 for _ in symbol_trades)

        win_rate = winning_trades / max(total_trades, 1)

        return {
            "total_return": total_return,
            "annual_return": annual_return,
            "max_drawdown": max_drawdown,
            "sharpe_ratio": sharpe_ratio,
            "total_trades": total_trades,
            "win_rate": win_rate,
            "profit_amount": profit_amount,
            "loss_amount": loss_amount
        }