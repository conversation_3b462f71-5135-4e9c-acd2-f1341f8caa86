"""
市场状态交叉验证模块
实现在不同市场状态下的模型评估
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Optional, Tuple, Union, Iterator, Callable
from ..market_analysis import MarketConditionDetector, MarketCondition
from .time_series_cv import TimeSeriesCV, CVMethod

class MarketConditionCV:
    """
    市场状态交叉验证类
    实现在不同市场状态下的模型评估
    """

    def __init__(self, 
                 market_detector: Optional[MarketConditionDetector] = None,
                 cv_method: CVMethod = CVMethod.ROLLING_WINDOW,
                 n_splits: int = 5,
                 train_size: Optional[int] = None,
                 test_size: Optional[int] = None,
                 gap: int = 0,
                 embargo: int = 0,
                 window_size: int = 60,
                 logger: Optional[logging.Logger] = None):
        """
        初始化市场状态交叉验证器

        参数:
            market_detector (MarketConditionDetector, optional): 市场状态检测器
            cv_method (CVMethod): 交叉验证方法
            n_splits (int): 分割数量
            train_size (int, optional): 训练集大小，如果为None则自动计算
            test_size (int, optional): 测试集大小，如果为None则自动计算
            gap (int): 训练集和测试集之间的间隔
            embargo (int): 禁运期大小
            window_size (int): 用于检测市场状态的窗口大小
            logger (logging.Logger, optional): 日志记录器
        """
        self.logger = logger or logging.getLogger('drl_trading')
        self.market_detector = market_detector or MarketConditionDetector(logger=self.logger)
        self.cv = TimeSeriesCV(
            method=cv_method,
            n_splits=n_splits,
            train_size=train_size,
            test_size=test_size,
            gap=gap,
            embargo=embargo,
            logger=self.logger
        )
        self.window_size = window_size

    def split_by_market_condition(self, 
                                 X: pd.DataFrame, 
                                 y: Optional[pd.Series] = None, 
                                 target_condition: Optional[MarketCondition] = None) -> Iterator[Tuple[np.ndarray, np.ndarray]]:
        """
        根据市场状态生成训练集和测试集的索引

        参数:
            X (pandas.DataFrame): 特征数据，必须包含'收盘'列
            y (pandas.Series, optional): 目标数据
            target_condition (MarketCondition, optional): 目标市场状态，如果为None则不筛选

        返回:
            iterator: 生成(train_index, test_index)元组的迭代器
        """
        if '收盘' not in X.columns:
            self.logger.error("数据中缺少'收盘'列，无法检测市场状态")
            return

        # 获取基础的交叉验证分割
        for train_index, test_index in self.cv.split(X, y):
            # 如果没有指定目标市场状态，直接返回分割
            if target_condition is None:
                yield train_index, test_index
                continue

            # 获取测试集数据
            test_data = X.iloc[test_index]
            
            # 检测测试集的市场状态
            if len(test_data) >= self.window_size:
                result = self.market_detector.detect_market_condition(test_data, self.window_size)
                condition = result['primary_condition']
                
                # 如果测试集的市场状态与目标状态匹配，返回分割
                if condition == target_condition:
                    self.logger.info(f"找到目标市场状态: {condition.name}，测试集大小: {len(test_index)}")
                    yield train_index, test_index
            else:
                self.logger.warning(f"测试集大小 ({len(test_data)}) 小于窗口大小 ({self.window_size})，无法检测市场状态")

    def evaluate_in_market_conditions(self, 
                                     X: pd.DataFrame, 
                                     y: pd.Series, 
                                     model_fn: Callable, 
                                     scoring_fn: Callable) -> Dict[MarketCondition, List[float]]:
        """
        在不同市场状态下评估模型

        参数:
            X (pandas.DataFrame): 特征数据，必须包含'收盘'列
            y (pandas.Series): 目标数据
            model_fn (callable): 模型训练函数，接受(X_train, y_train)并返回模型
            scoring_fn (callable): 评分函数，接受(model, X_test, y_test)并返回分数

        返回:
            dict: 不同市场状态下的评分列表
        """
        if '收盘' not in X.columns:
            self.logger.error("数据中缺少'收盘'列，无法检测市场状态")
            return {}

        # 初始化结果字典
        results = {condition: [] for condition in MarketCondition}
        
        # 获取基础的交叉验证分割
        for train_index, test_index in self.cv.split(X, y):
            # 训练模型
            X_train, y_train = X.iloc[train_index], y.iloc[train_index]
            model = model_fn(X_train, y_train)
            
            # 获取测试集数据
            X_test, y_test = X.iloc[test_index], y.iloc[test_index]
            
            # 检测测试集的市场状态
            if len(X_test) >= self.window_size:
                result = self.market_detector.detect_market_condition(X_test, self.window_size)
                condition = result['primary_condition']
                
                # 评估模型
                score = scoring_fn(model, X_test, y_test)
                
                # 记录结果
                results[condition].append(score)
                self.logger.info(f"市场状态: {condition.name}, 评分: {score:.4f}")
            else:
                self.logger.warning(f"测试集大小 ({len(X_test)}) 小于窗口大小 ({self.window_size})，无法检测市场状态")
        
        return results

    def get_market_condition_distribution(self, X: pd.DataFrame, window_size: int = 60) -> Dict[MarketCondition, float]:
        """
        获取数据中不同市场状态的分布

        参数:
            X (pandas.DataFrame): 特征数据，必须包含'收盘'列
            window_size (int): 用于检测市场状态的窗口大小

        返回:
            dict: 不同市场状态的比例
        """
        if '收盘' not in X.columns:
            self.logger.error("数据中缺少'收盘'列，无法检测市场状态")
            return {}

        # 初始化结果字典
        condition_counts = {condition: 0 for condition in MarketCondition}
        total_windows = 0
        
        # 使用滑动窗口检测市场状态
        for i in range(window_size, len(X), window_size // 2):  # 使用50%的重叠
            end_idx = min(i + window_size, len(X))
            window_data = X.iloc[i-window_size:end_idx]
            
            if len(window_data) >= window_size:
                result = self.market_detector.detect_market_condition(window_data, window_size)
                condition = result['primary_condition']
                
                # 计数
                condition_counts[condition] += 1
                total_windows += 1
        
        # 计算比例
        if total_windows > 0:
            condition_distribution = {condition: count / total_windows for condition, count in condition_counts.items()}
        else:
            condition_distribution = {condition: 0 for condition in MarketCondition}
        
        return condition_distribution

    def get_market_condition_timeline(self, X: pd.DataFrame, window_size: int = 60) -> pd.DataFrame:
        """
        获取数据中市场状态的时间线

        参数:
            X (pandas.DataFrame): 特征数据，必须包含'收盘'列
            window_size (int): 用于检测市场状态的窗口大小

        返回:
            pandas.DataFrame: 包含时间和市场状态的数据框
        """
        if '收盘' not in X.columns:
            self.logger.error("数据中缺少'收盘'列，无法检测市场状态")
            return pd.DataFrame()

        # 初始化结果列表
        timeline = []
        
        # 使用滑动窗口检测市场状态
        for i in range(window_size, len(X), window_size // 2):  # 使用50%的重叠
            end_idx = min(i + window_size, len(X))
            window_data = X.iloc[i-window_size:end_idx]
            
            if len(window_data) >= window_size:
                result = self.market_detector.detect_market_condition(window_data, window_size)
                condition = result['primary_condition']
                
                # 记录时间和市场状态
                timeline.append({
                    'start_date': X.index[i-window_size],
                    'end_date': X.index[end_idx-1],
                    'market_condition': condition,
                    'market_condition_name': condition.name
                })
        
        return pd.DataFrame(timeline)
