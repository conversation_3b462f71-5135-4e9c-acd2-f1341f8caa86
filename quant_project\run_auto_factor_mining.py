"""
DRL量化交易系统 - 自动因子挖掘独立运行脚本
单独运行自动因子挖掘模块，避免加载整个系统导致的问题
"""

import os
import sys
import time
import logging
import traceback

# 确保日志目录存在
os.makedirs('logs', exist_ok=True)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/auto_factor_mining.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('auto_factor_mining_app')

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(current_dir)
sys.path.append(parent_dir)

logger.info(f"启动自动因子挖掘应用程序")
logger.info(f"当前目录: {current_dir}")
logger.info(f"Python路径: {sys.path}")

try:
    import streamlit as st
    import pandas as pd
    import numpy as np
    from datetime import datetime, timedelta
    import matplotlib.pyplot as plt
    logger.info("成功导入基础库")
except Exception as e:
    logger.error(f"导入基础库失败: {str(e)}")
    traceback.print_exc(file=open("logs/auto_factor_mining.log", "a"))
    sys.exit(1)

# 初始化Streamlit页面配置
try:
    st.set_page_config(
        page_title="DRL量化交易系统 - 自动因子挖掘",
        page_icon="📈",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    logger.info("Streamlit页面配置设置完成")
except Exception as e:
    logger.error(f"设置Streamlit页面配置时出错: {str(e)}")
    traceback.print_exc(file=open("logs/auto_factor_mining.log", "a"))

# 预加载自动因子挖掘模块
try:
    from core_logic.auto_factor_mining import (
        FactorGenerator, 
        FactorEvaluator, 
        FactorSelector, 
        AutoFactorPipeline,
        AdaptiveFactorSystem
    )
    logger.info("成功导入自动因子挖掘模块")
except Exception as e:
    logger.error(f"导入自动因子挖掘模块失败: {str(e)}")
    traceback.print_exc(file=open("logs/auto_factor_mining.log", "a"))

# 预加载自动因子挖掘页面
try:
    from auto_factor_mining_page import display_auto_factor_mining_page
    logger.info("成功导入自动因子挖掘页面")
except Exception as e:
    logger.error(f"导入自动因子挖掘页面失败: {str(e)}")
    traceback.print_exc(file=open("logs/auto_factor_mining.log", "a"))

# 添加自定义CSS
st.markdown("""
<style>
    .main-header {
        color: #1E88E5;
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        text-align: center;
        padding: 1rem;
        border-bottom: 2px solid #f0f2f6;
    }
    .loading-spinner {
        text-align: center;
        padding: 2rem;
    }
</style>
""", unsafe_allow_html=True)

def main():
    """主应用程序函数"""
    try:
        logger.info("执行main函数")
        
        # 标题
        st.markdown('<div class="main-header">DRL量化交易系统 - 自动因子挖掘</div>', unsafe_allow_html=True)
        
        # 显示加载中提示
        loading_placeholder = st.empty()
        loading_placeholder.markdown('<div class="loading-spinner">正在加载自动因子挖掘页面，请稍候...</div>', unsafe_allow_html=True)
        
        try:
            # 导入并运行自动因子挖掘页面
            from auto_factor_mining_page import display_auto_factor_mining_page
            
            # 添加短暂延迟确保UI反映加载状态
            time.sleep(0.5)
            
            # 移除加载提示并显示页面
            loading_placeholder.empty()
            display_auto_factor_mining_page()
            logger.info("自动因子挖掘页面加载成功")
        except Exception as e:
            # 如果出错，清除加载提示并显示错误信息
            loading_placeholder.empty()
            logger.error(f"显示自动因子挖掘页面时出错: {str(e)}")
            traceback.print_exc(file=open("logs/auto_factor_mining.log", "a"))
            
            st.error(f"加载自动因子挖掘页面时出错: {str(e)}")
            
            # 在展开框中显示堆栈跟踪
            with st.expander("错误详情"):
                st.code(traceback.format_exc())
            
    except Exception as e:
        logger.error(f"main函数中出错: {str(e)}")
        traceback.print_exc(file=open("logs/auto_factor_mining.log", "a"))
        st.error(f"应用程序错误: {str(e)}")

if __name__ == "__main__":
    main() 