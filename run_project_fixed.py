#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
量化交易系统启动脚本
此脚本用于启动量化交易系统，包括数据获取、特征工程、模型训练和回测
"""

import os
import sys
import argparse
import logging
import yaml
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入项目模块
from quant_project.core_logic.data_handler import DataHandler
from quant_project.core_logic.feature_engineer import FeatureEngineer
from quant_project.core_logic.trading_environment import TradingEnvironment
from quant_project.core_logic.drl_agent import DRLAgent, MetricsCallback
from quant_project.core_logic.performance_analyzer import PerformanceAnalyzer
from quant_project.core_logic.utils import setup_logger, detect_gpu

# 配置日志
log_dir = "logs"
if not os.path.exists(log_dir):
    os.makedirs(log_dir)

log_file = os.path.join(log_dir, f"run_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
logger = setup_logger("quant_system", log_file)

def load_config(config_path):
    """加载配置文件"""
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        logger.error(f"加载配置文件失败: {str(e)}")
        sys.exit(1)

def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description="量化交易系统")
    
    parser.add_argument("--mode", type=str, default="train", choices=["train", "backtest", "optimize"],
                        help="运行模式: train(训练), backtest(回测), optimize(优化)")
    
    parser.add_argument("--stock_code", type=str, default="sh000001",
                        help="股票/指数/加密货币代码，例如: sh000001, index_000300, crypto_BTC")
    
    parser.add_argument("--start_date", type=str, default="2020-01-01",
                        help="开始日期，格式: YYYY-MM-DD")
    
    parser.add_argument("--end_date", type=str, default="2022-12-31",
                        help="结束日期，格式: YYYY-MM-DD")
    
    parser.add_argument("--frequency", type=str, default="日线", choices=["日线", "周线", "月线"],
                        help="数据频率: 日线, 周线, 月线")
    
    parser.add_argument("--algorithm", type=str, default="PPO", choices=["PPO", "A2C", "DQN"],
                        help="DRL算法: PPO, A2C, DQN")
    
    parser.add_argument("--timesteps", type=int, default=10000,
                        help="训练步数")
    
    parser.add_argument("--model_path", type=str, default="",
                        help="模型路径，用于回测模式")
    
    parser.add_argument("--config", type=str, default="quant_project/configs/env_config.yaml",
                        help="配置文件路径")
    
    parser.add_argument("--use_gpu", action="store_true",
                        help="是否使用GPU")
    
    return parser.parse_args()

def train_model(args, config):
    """训练模型"""
    logger.info("开始训练模型")
    
    # 检查GPU
    if args.use_gpu:
        gpu_info = detect_gpu()
        if gpu_info['available']:
            logger.info(f"使用GPU: {gpu_info['devices']}")
        else:
            logger.warning("未检测到GPU，将使用CPU进行训练")
    
    # 获取数据
    logger.info(f"获取数据: {args.stock_code}, {args.start_date} 至 {args.end_date}")
    data_handler = DataHandler()
    data = data_handler.get_stock_data(
        stock_code=args.stock_code,
        start_date=args.start_date,
        end_date=args.end_date,
        frequency=args.frequency
    )
    
    if data is None or data.empty:
        logger.error("获取数据失败")
        return
    
    logger.info(f"获取到 {len(data)} 条数据")
    
    # 特征工程
    logger.info("生成特征")
    feature_engineer = FeatureEngineer(config['feature_config'])
    processed_data = feature_engineer.generate_features(data)
    
    # 数据分割
    split_config = config['data_split']
    train_size = int(len(processed_data) * split_config['train_ratio'])
    val_size = int(len(processed_data) * split_config['val_ratio'])
    
    train_data = processed_data.iloc[:train_size]
    val_data = processed_data.iloc[train_size:train_size+val_size]
    test_data = processed_data.iloc[train_size+val_size:]
    
    logger.info(f"数据分割: 训练集={len(train_data)}, 验证集={len(val_data)}, 测试集={len(test_data)}")
    
    # 创建环境
    env_config = config.copy()
    env_config['df_processed_data'] = train_data
    
    # 创建DRL智能体
    agent_config = {}
    agent_config['algorithm'] = args.algorithm
    agent_config['use_gpu'] = args.use_gpu
    
    drl_agent = DRLAgent(env_config, agent_config)
    
    # 创建回调
    metrics_callback = MetricsCallback()
    
    # 训练模型
    logger.info(f"开始训练: 算法={args.algorithm}, 步数={args.timesteps}")
    training_stats = drl_agent.train(
        total_timesteps=args.timesteps,
        callback_list=[metrics_callback],
        progress_bar=True
    )
    
    # 保存模型
    save_dir = "saved_models"
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    model_path = os.path.join(save_dir, f"{args.algorithm}_{args.stock_code}_{timestamp}.zip")
    drl_agent.save_model(save_path=model_path)
    logger.info(f"模型已保存到: {model_path}")
    
    # 可视化训练过程
    plt.figure(figsize=(10, 6))
    plt.plot(metrics_callback.rewards)
    plt.title("训练过程中的奖励")
    plt.xlabel("步数")
    plt.ylabel("奖励")
    plt.grid(True)
    
    plots_dir = "plots"
    if not os.path.exists(plots_dir):
        os.makedirs(plots_dir)
    
    plt.savefig(os.path.join(plots_dir, f"training_rewards_{timestamp}.png"))
    logger.info(f"训练奖励可视化已保存到 plots/training_rewards_{timestamp}.png")
    
    # 在验证集上评估
    logger.info("在验证集上评估模型")
    backtest(drl_agent, val_data, config, "validation")
    
    return model_path

def backtest(agent, data, config, dataset_name="test"):
    """回测模型"""
    logger.info(f"在{dataset_name}集上回测模型")
    
    # 创建回测环境
    env_config = config.copy()
    env_config['df_processed_data'] = data
    
    test_env = TradingEnvironment(**env_config)
    
    # 执行回测
    observation, info = test_env.reset()
    done = False
    
    while not done:
        action = agent.predict_action(observation)
        observation, reward, terminated, truncated, info = test_env.step(action)
        done = terminated or truncated
    
    # 获取交易记录和组合价值历史
    trades = test_env.get_trades_history()
    portfolio_values = test_env.get_portfolio_history()
    
    # 性能分析
    analyzer = PerformanceAnalyzer()
    metrics = analyzer.analyze(trades, portfolio_values)
    
    # 打印性能指标
    logger.info(f"{dataset_name}集性能指标:")
    logger.info(f"总收益率: {metrics['total_return']:.2%}")
    logger.info(f"年化收益率: {metrics['annualized_return']:.2%}")
    logger.info(f"最大回撤: {metrics['max_drawdown']:.2%}")
    logger.info(f"夏普比率: {metrics['sharpe_ratio']:.2f}")
    
    # 可视化回测结果
    plt.figure(figsize=(12, 8))
    
    # 绘制组合价值
    plt.subplot(2, 1, 1)
    plt.plot(portfolio_values.index, portfolio_values['portfolio_value'])
    plt.title(f"{dataset_name}集回测 - 组合价值")
    plt.xlabel("日期")
    plt.ylabel("价值")
    plt.grid(True)
    
    # 绘制交易点
    if not trades.empty:
        buy_trades = trades[trades['action'] == 'buy']
        sell_trades = trades[trades['action'] == 'sell']
        
        if not buy_trades.empty:
            plt.scatter(buy_trades['date'], buy_trades['price'], color='green', marker='^', label='买入')
        if not sell_trades.empty:
            plt.scatter(sell_trades['date'], sell_trades['price'], color='red', marker='v', label='卖出')
        
        plt.legend()
    
    # 绘制收益率
    plt.subplot(2, 1, 2)
    returns = portfolio_values['portfolio_value'].pct_change()
    plt.plot(returns.index, returns.cumsum())
    plt.title(f"{dataset_name}集回测 - 累计收益率")
    plt.xlabel("日期")
    plt.ylabel("累计收益率")
    plt.grid(True)
    
    plt.tight_layout()
    
    plots_dir = "plots"
    if not os.path.exists(plots_dir):
        os.makedirs(plots_dir)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    plt.savefig(os.path.join(plots_dir, f"backtest_{dataset_name}_{timestamp}.png"))
    logger.info(f"回测结果可视化已保存到 plots/backtest_{dataset_name}_{timestamp}.png")
    
    return metrics

def main():
    """主函数"""
    # 解析命令行参数
    args = parse_args()
    
    # 加载配置
    config = load_config(args.config)
    
    logger.info("=" * 50)
    logger.info("量化交易系统启动")
    logger.info(f"运行模式: {args.mode}")
    logger.info(f"股票代码: {args.stock_code}")
    logger.info(f"日期范围: {args.start_date} 至 {args.end_date}")
    logger.info(f"数据频率: {args.frequency}")
    logger.info(f"DRL算法: {args.algorithm}")
    logger.info("=" * 50)
    
    if args.mode == "train":
        # 训练模式
        model_path = train_model(args, config)
        
        # 在测试集上评估
        if model_path:
            logger.info("在测试集上评估模型")
            
            # 获取数据
            data_handler = DataHandler()
            data = data_handler.get_stock_data(
                stock_code=args.stock_code,
                start_date=args.start_date,
                end_date=args.end_date,
                frequency=args.frequency
            )
            
            # 特征工程
            feature_engineer = FeatureEngineer(config['feature_config'])
            processed_data = feature_engineer.generate_features(data)
            
            # 数据分割
            split_config = config['data_split']
            train_size = int(len(processed_data) * split_config['train_ratio'])
            val_size = int(len(processed_data) * split_config['val_ratio'])
            test_data = processed_data.iloc[train_size+val_size:]
            
            # 加载模型
            loaded_agent = DRLAgent.load_model(model_path)
            
            # 回测
            backtest(loaded_agent, test_data, config, "test")
    
    elif args.mode == "backtest":
        # 回测模式
        if not args.model_path:
            logger.error("回测模式需要指定模型路径 (--model_path)")
            return
        
        # 获取数据
        data_handler = DataHandler()
        data = data_handler.get_stock_data(
            stock_code=args.stock_code,
            start_date=args.start_date,
            end_date=args.end_date,
            frequency=args.frequency
        )
        
        # 特征工程
        feature_engineer = FeatureEngineer(config['feature_config'])
        processed_data = feature_engineer.generate_features(data)
        
        # 加载模型
        loaded_agent = DRLAgent.load_model(args.model_path)
        
        # 回测
        backtest(loaded_agent, processed_data, config, "backtest")
    
    elif args.mode == "optimize":
        # 优化模式
        logger.info("优化模式尚未实现")
        # TODO: 实现超参数优化
    
    logger.info("=" * 50)
    logger.info("量化交易系统运行完成")
    logger.info("=" * 50)

if __name__ == "__main__":
    main()
