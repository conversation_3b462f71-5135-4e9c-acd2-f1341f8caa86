# 量化交易系统用户文档

## 项目概述

本量化交易系统是一个基于深度强化学习（DRL）的量化交易平台，支持多种金融产品（股票、指数）的数据获取、特征工程、模型训练和回测。系统采用最先进的DRL算法，通过学习市场模式来优化交易决策，并提供全面的性能分析和可视化功能。

## 系统要求

- Python 3.8+
- 依赖库：详见 `requirements.txt`
- 推荐：NVIDIA GPU（用于加速模型训练）

## 安装指南

### 1. 创建虚拟环境

```bash
# 创建虚拟环境
python -m venv quant_env

# 激活虚拟环境
# Windows
quant_env\Scripts\activate
# Linux/Mac
source quant_env/bin/activate
```

### 2. 安装依赖

```bash
# 安装依赖库
pip install -r requirements.txt
```

### 3. GPU支持（可选但推荐）

系统会自动检测GPU硬件并在虚拟环境中安装适合的GPU版本的深度学习框架。您也可以手动安装：

#### 自动安装GPU支持

系统在启动时会自动检测GPU并尝试安装GPU支持。您也可以通过以下方式手动触发安装：

1. 在应用程序中，导航到"GPU状态与诊断"页面
2. 点击"运行完整GPU支持安装脚本"按钮
3. 等待安装完成后重启应用程序

#### 手动安装PyTorch GPU版本

如果您的系统有NVIDIA GPU，可以手动安装GPU版本的PyTorch来加速训练：

```bash
# 安装支持CUDA的PyTorch
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

#### 手动安装TensorFlow GPU版本

您也可以安装GPU版本的TensorFlow：

```bash
# 安装TensorFlow（会自动支持GPU）
pip install tensorflow
```

#### 验证GPU支持

安装完成后，您可以通过以下方式验证GPU是否可用：

```python
# 验证PyTorch GPU支持
import torch
print(f"PyTorch版本: {torch.__version__}")
print(f"CUDA是否可用: {torch.cuda.is_available()}")
if torch.cuda.is_available():
    print(f"GPU设备数量: {torch.cuda.device_count()}")
    print(f"GPU设备名称: {torch.cuda.get_device_name(0)}")

# 验证TensorFlow GPU支持
import tensorflow as tf
print(f"TensorFlow版本: {tf.__version__}")
gpus = tf.config.list_physical_devices('GPU')
print(f"GPU设备数量: {len(gpus)}")
for gpu in gpus:
    print(f"GPU设备: {gpu.name}")
```

## 项目结构

```
quant_project/
├── core_logic/              # 核心逻辑模块
│   ├── data_handler.py      # 数据处理模块
│   ├── feature_engineer.py  # 特征工程模块
│   ├── trading_environment.py # 交易环境模块
│   ├── drl_agent.py         # DRL智能体模块
│   ├── performance_analyzer.py # 性能分析模块
│   └── utils.py             # 工具函数
├── configs/                 # 配置文件
│   ├── env_config.yaml      # 环境配置
│   └── drl_agent_config.yaml # DRL智能体配置
├── data_cache/              # 数据缓存目录
├── logs/                    # 日志目录
├── saved_models/            # 保存的模型
├── tests/                   # 测试脚本
└── app.py                   # 主应用程序
```

## 使用指南

### 1. 数据获取

系统支持多种金融产品的数据获取：

- **股票**：使用格式 `sh000001` 或 `sz399001`
- **指数**：使用格式 `index_000300`（沪深300）

注意：加密货币数据提取功能已被禁用。

示例代码：

```python
from core_logic.data_handler import DataHandler

# 创建数据处理器
data_handler = DataHandler()

# 获取上证指数数据
sh_index_data = data_handler.get_stock_data(
    stock_code='sh000001',
    start_date='2022-01-01',
    end_date='2022-12-31',
    frequency='日线'
)

# 获取沪深300指数数据
hs300_data = data_handler.get_stock_data(
    stock_code='index_000300',
    start_date='2022-01-01',
    end_date='2022-12-31',
    frequency='日线'
)

# 注意：加密货币数据提取功能已被禁用
# 以下代码将会引发错误：
# btc_data = data_handler.get_stock_data(
#     stock_code='crypto_BTC',
#     start_date='2022-01-01',
#     end_date='2022-12-31',
#     frequency='日线'
# )
```

### 2. 特征工程

系统支持多种技术指标的生成：

```python
from core_logic.feature_engineer import FeatureEngineer

# 创建特征工程器
feature_config = {
    'sma': {'use': True, 'periods': [5, 20, 60]},
    'ema': {'use': True, 'periods': [5, 20]},
    'rsi': {'use': True, 'period': 14},
    'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9},
    'bbands': {'use': True, 'period': 20, 'std': 2.0},
    'atr': {'use': True, 'period': 14}
}

feature_engineer = FeatureEngineer(feature_config)

# 生成特征
processed_data = feature_engineer.generate_features(data)
```

### 3. 创建交易环境

```python
from core_logic.trading_environment import TradingEnvironment

# 创建交易环境
env = TradingEnvironment(
    df_processed_data=processed_data,
    initial_capital=100000,
    commission_rate=0.0003,
    min_hold_days=3,
    window_size=20
)
```

### 4. 训练DRL智能体

```python
from core_logic.drl_agent import DRLAgent, MetricsCallback

# 环境配置
env_config = {
    'df_processed_data': processed_data,
    'initial_capital': 100000,
    'commission_rate': 0.0003,
    'min_hold_days': 3,
    'window_size': 20
}

# 智能体配置
agent_config = {
    'algorithm': 'PPO',  # 可选: 'PPO', 'A2C', 'DQN'
    'policy_network': 'MlpPolicy',
    'learning_rate': 0.0003,
    'gamma': 0.99
}

# 创建DRL智能体
drl_agent = DRLAgent(env_config, agent_config)

# 创建回调
metrics_callback = MetricsCallback()

# 训练模型
training_stats = drl_agent.train(
    total_timesteps=10000,
    callback_list=[metrics_callback]
)

# 保存模型
model_path = drl_agent.save_model(save_path="saved_models/my_model.zip")
```

### 5. 回测与性能分析

```python
from core_logic.performance_analyzer import PerformanceAnalyzer

# 加载保存的模型
loaded_agent = DRLAgent.load_model("saved_models/my_model.zip")

# 创建回测环境
test_env_config = {
    'df_processed_data': test_data,
    'initial_capital': 100000,
    'commission_rate': 0.0003,
    'min_hold_days': 3,
    'window_size': 20
}

test_env = TradingEnvironment(**test_env_config)

# 执行回测
observation, info = test_env.reset()
done = False

while not done:
    action = loaded_agent.predict_action(observation)
    observation, reward, terminated, truncated, info = test_env.step(action)
    done = terminated or truncated

# 获取交易记录和组合价值历史
trades = test_env.get_trades_history()
portfolio_values = test_env.get_portfolio_history()

# 性能分析
analyzer = PerformanceAnalyzer()
metrics = analyzer.analyze(trades, portfolio_values)

# 打印性能指标
print(f"总收益率: {metrics['total_return']:.2%}")
print(f"年化收益率: {metrics['annualized_return']:.2%}")
print(f"最大回撤: {metrics['max_drawdown']:.2%}")
print(f"夏普比率: {metrics['sharpe_ratio']:.2f}")
```

## 运行完整测试

系统提供了全面的测试脚本，可以验证各个模块的功能：

```bash
# 运行所有测试
python run_all_tests.py

# 运行特定测试
python tests/test_data_extraction.py
python tests/test_drl_agent.py
```

## 常见问题解答

### 1. 数据获取失败

如果遇到数据获取失败的情况，请检查：
- 网络连接是否正常
- 股票/指数代码是否正确
- 日期格式是否为 'YYYY-MM-DD'

### 2. GPU不可用

如果系统无法检测到GPU：

- 确认您的系统有支持CUDA的NVIDIA GPU
- 确认已安装最新的NVIDIA驱动
- 确认已安装CUDA工具包
- 确认已安装GPU版本的PyTorch或TensorFlow

您可以通过以下步骤解决GPU问题：

1. **自动诊断**：在应用程序中，导航到"GPU状态与诊断"页面，点击"运行诊断"按钮
2. **自动安装**：在同一页面点击"运行完整GPU支持安装脚本"按钮
3. **手动安装**：按照以下命令安装GPU支持

```bash
# 安装PyTorch GPU版本
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# 安装TensorFlow GPU版本
pip install tensorflow
```

4. **检查NVIDIA驱动**：确保已安装最新的NVIDIA驱动
5. **检查CUDA版本**：使用 `nvcc --version` 命令检查CUDA版本，确保与安装的深度学习框架兼容

### 3. 训练速度慢

提高训练速度的建议：
- 使用GPU进行训练
- 减少训练数据的时间范围
- 简化特征配置
- 调整批处理大小和学习率

## 联系与支持

如有任何问题或建议，请联系项目维护者。

---

祝您交易顺利！
