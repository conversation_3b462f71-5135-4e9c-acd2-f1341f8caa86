{"startup": {"success": true, "app_file": "quant_project/main_app.py"}, "components": {"success": false, "error": "UI组件目录 quant_project/ui_components 中没有Python文件"}, "interaction": {"success": true, "app_file": "quant_project/main_app.py", "backend_imports": ["optimized_data_handler", "optimized_data_handler_adapter", "data_handling", "data_handler", "optimized_feature_engineering", "optimized_feature_engineering_adapter", "feature_engineering", "feature_engineer", "trading_env", "trading_environment", "drl_agent", "performance_analyzer", "utils", "enhanced_feature_engineer", "enhanced_drl_agent", "enhanced_drl_agent", "enhanced_drl_agent", "optimized_feature_engineering_adapter", "enhanced_trading_environment", "enhanced_drl_agent", "enhanced_feature_engineer", "enhanced_feature_engineer"], "ui_features": {"has_forms": false, "has_buttons": true, "has_file_upload": false, "has_progress_bar": true, "has_charts": true, "has_session_state": true, "has_cache": false, "has_stop_button": true, "has_log_clear": false}}}