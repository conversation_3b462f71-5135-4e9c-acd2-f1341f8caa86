# 数据字典报告

版本: 1.0.0
最后更新: 2025-05-13 07:30:20

## 价格特征

### 涨跌幅

**描述**: 当日收盘价相对前一日收盘价的变化百分比

**计算方法**: df['涨跌幅'] = df['收盘'].pct_change()

**解释**: 正值表示上涨，负值表示下跌，数值大小表示变化幅度

**标签**: 基础特征, 收益率

**添加日期**: 2025-05-13 07:30:20

---

### 对数收益率

**描述**: 当日收盘价与前一日收盘价的对数差

**计算方法**: df['对数收益率'] = np.log(df['收盘'] / df['收盘'].shift(1))

**解释**: 对数收益率具有更好的统计特性，适合长期分析

**标签**: 基础特征, 收益率

**添加日期**: 2025-05-13 07:30:20

---

## 技术指标

### SMA_20

**描述**: 20日简单移动平均线

**计算方法**: df['SMA_20'] = ta.trend.sma_indicator(df['收盘'], window=20)

**解释**: 反映中期价格趋势，高于SMA为看涨信号，低于SMA为看跌信号

**标签**: 趋势指标, 移动平均线

**添加日期**: 2025-05-11 16:36:45

---

### RSI_14

**描述**: 14日相对强弱指标

**计算方法**: df['RSI_14'] = ta.momentum.rsi(df['收盘'], window=14)

**解释**: RSI > 70 表示超买，RSI < 30 表示超卖，可能出现反转

**标签**: 动量指标, 超买超卖

**添加日期**: 2025-05-11 16:36:45

---

## 统计特征

### Rolling_Volatility_20

**描述**: 20日滚动波动率

**计算方法**: df['Rolling_Volatility_20'] = df['涨跌幅'].rolling(window=20).std()

**解释**: 反映价格的不稳定性，高波动率表示市场不确定性增加

**标签**: 波动率, 风险指标

**添加日期**: 2025-05-13 00:34:56

---

### Rolling_Volatility_5

**描述**: 5日滚动波动率

**计算方法**: df['Rolling_Volatility_5'] = df['涨跌幅'].rolling(window=5).std()

**解释**: 反映价格的不稳定性，高波动率表示市场不确定性增加

**标签**: 波动率, 风险指标

**添加日期**: 2025-05-13 00:34:56

---

### Rolling_Volatility_10

**描述**: 10日滚动波动率

**计算方法**: df['Rolling_Volatility_10'] = df['涨跌幅'].rolling(window=10).std()

**解释**: 反映价格的不稳定性，高波动率表示市场不确定性增加

**标签**: 波动率, 风险指标

**添加日期**: 2025-05-13 00:34:56

---

### Rolling_Volatility_60

**描述**: 60日滚动波动率

**计算方法**: df['Rolling_Volatility_60'] = df['涨跌幅'].rolling(window=60).std()

**解释**: 反映价格的不稳定性，高波动率表示市场不确定性增加

**标签**: 波动率, 风险指标

**添加日期**: 2025-05-13 00:34:56

---

## 高级特征

### 上分形

**描述**: 价格形成上分形，当前高点高于前后两个周期的高点

**计算方法**: df['上分形'] = ((df['最高'] > df['最高'].shift(1)) & (df['最高'] > df['最高'].shift(2)) & (df['最高'] > df['最高'].shift(-1)) & (df['最高'] > df['最高'].shift(-2))).astype(int)

**解释**: 上分形表示可能的顶部形成，是潜在的卖出信号

**标签**: 价格形态, 分形理论

**添加日期**: 2025-05-13 07:30:20

---

### 转换线

**描述**: 一目均衡表转换线 (9日高点 + 9日低点) / 2

**计算方法**: df['转换线'] = (df['最高'].rolling(window=9).max() + df['最低'].rolling(window=9).min()) / 2

**解释**: 转换线上穿基准线为买入信号，下穿为卖出信号

**标签**: 趋势指标, 一目均衡表

**添加日期**: 2025-05-13 07:30:20

---

## 市场微观结构

### 价格波动率比率

**描述**: 高低价差与开盘价的比率

**计算方法**: df['价格波动率比率'] = (df['最高'] - df['最低']) / df['开盘']

**解释**: 反映日内波动性，高值表示波动剧烈

**标签**: 波动率, 微观结构

**添加日期**: 2025-05-13 07:30:20

---

## 时间特征

### 月

**描述**: 月份 (1-12)

**计算方法**: df['月'] = df.index.month

**解释**: 用于捕捉月度季节性效应

**标签**: 日历特征, 季节性

**添加日期**: 2025-05-13 07:30:20

---

## 价格形态

### 锤子线

**描述**: 锤子线形态，下影线长，上影线短或没有，实体小

**计算方法**: df['锤子线'] = ((body > 0) & (lower_shadow > 2 * body_abs) & (upper_shadow < 0.1 * body_abs)).astype(int)

**解释**: 在下跌趋势中出现锤子线是潜在的反转信号

**标签**: 蜡烛图形态, 反转信号

**添加日期**: 2025-05-13 07:30:20

---

