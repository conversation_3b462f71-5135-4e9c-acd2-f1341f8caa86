"""
测试特征工程模块
"""

import os
import sys
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('test_feature_engineering')

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入原始特征工程类和重构后的特征工程类
from core_logic.feature_engineer import FeatureEngineer as OldFeatureEngineer
from core_logic.feature_engineering import FeatureEngineer as NewFeatureEngineer
from core_logic.feature_engineering import EnhancedFeatureEngineer
from core_logic.feature_engineering.adapter import FeatureEngineerAdapter

def create_test_data(n_samples=100):
    """
    创建测试数据
    
    参数:
        n_samples (int): 样本数量
        
    返回:
        pandas.DataFrame: 测试数据
    """
    # 创建日期索引
    dates = pd.date_range(start='2020-01-01', periods=n_samples, freq='D')
    
    # 创建价格数据
    np.random.seed(42)
    close = np.random.normal(loc=100, scale=10, size=n_samples).cumsum()
    open_price = close * np.random.normal(loc=1, scale=0.01, size=n_samples)
    high = np.maximum(close, open_price) * np.random.normal(loc=1.02, scale=0.01, size=n_samples)
    low = np.minimum(close, open_price) * np.random.normal(loc=0.98, scale=0.01, size=n_samples)
    volume = np.random.normal(loc=1000000, scale=200000, size=n_samples)
    
    # 创建数据框
    df = pd.DataFrame({
        '开盘': open_price,
        '最高': high,
        '最低': low,
        '收盘': close,
        '成交量': volume
    }, index=dates)
    
    return df

def test_feature_engineering():
    """
    测试特征工程模块
    """
    logger.info("创建测试数据...")
    df = create_test_data(n_samples=100)
    
    # 测试原始特征工程类
    logger.info("测试原始特征工程类...")
    try:
        old_fe = OldFeatureEngineer()
        df_old = old_fe.generate_features(df)
        logger.info(f"原始特征工程类生成了 {len(df_old.columns) - 5} 个特征")
    except Exception as e:
        logger.error(f"测试原始特征工程类失败: {str(e)}")
        df_old = None
    
    # 测试新的特征工程类
    logger.info("测试新的特征工程类...")
    try:
        new_fe = NewFeatureEngineer()
        df_new = new_fe.generate_features(df)
        logger.info(f"新的特征工程类生成了 {len(df_new.columns) - 5} 个特征")
    except Exception as e:
        logger.error(f"测试新的特征工程类失败: {str(e)}")
        df_new = None
    
    # 测试增强版特征工程类
    logger.info("测试增强版特征工程类...")
    try:
        enhanced_fe = EnhancedFeatureEngineer()
        df_enhanced = enhanced_fe.generate_features(df)
        logger.info(f"增强版特征工程类生成了 {len(df_enhanced.columns) - 5} 个特征")
    except Exception as e:
        logger.error(f"测试增强版特征工程类失败: {str(e)}")
        df_enhanced = None
    
    # 测试适配器
    logger.info("测试特征工程适配器...")
    try:
        adapter_fe = FeatureEngineerAdapter(use_enhanced=False)
        df_adapter = adapter_fe.generate_features(df)
        logger.info(f"特征工程适配器生成了 {len(df_adapter.columns) - 5} 个特征")
    except Exception as e:
        logger.error(f"测试特征工程适配器失败: {str(e)}")
        df_adapter = None
    
    # 测试增强版适配器
    logger.info("测试增强版特征工程适配器...")
    try:
        enhanced_adapter_fe = FeatureEngineerAdapter(use_enhanced=True)
        df_enhanced_adapter = enhanced_adapter_fe.generate_features(df)
        logger.info(f"增强版特征工程适配器生成了 {len(df_enhanced_adapter.columns) - 5} 个特征")
    except Exception as e:
        logger.error(f"测试增强版特征工程适配器失败: {str(e)}")
        df_enhanced_adapter = None
    
    # 比较结果
    logger.info("比较结果...")
    
    if df_old is not None and df_new is not None:
        # 比较特征数量
        old_features = len(df_old.columns) - 5
        new_features = len(df_new.columns) - 5
        logger.info(f"原始特征工程类: {old_features} 个特征, 新的特征工程类: {new_features} 个特征")
        
        # 比较共同特征的值
        common_features = set(df_old.columns).intersection(set(df_new.columns)) - {'开盘', '最高', '最低', '收盘', '成交量'}
        logger.info(f"共同特征数量: {len(common_features)}")
        
        for feature in list(common_features)[:5]:  # 只比较前5个共同特征
            correlation = df_old[feature].corr(df_new[feature])
            logger.info(f"特征 '{feature}' 的相关性: {correlation:.4f}")
    
    # 输出结果摘要
    logger.info("测试结果摘要:")
    logger.info(f"原始特征工程类: {'成功' if df_old is not None else '失败'}")
    logger.info(f"新的特征工程类: {'成功' if df_new is not None else '失败'}")
    logger.info(f"增强版特征工程类: {'成功' if df_enhanced is not None else '失败'}")
    logger.info(f"特征工程适配器: {'成功' if df_adapter is not None else '失败'}")
    logger.info(f"增强版特征工程适配器: {'成功' if df_enhanced_adapter is not None else '失败'}")

if __name__ == "__main__":
    test_feature_engineering()
