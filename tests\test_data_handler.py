import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import traceback

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'logs/data_handler_test_{datetime.now().strftime("%Y-%m-%d")}.log')
    ]
)

# 确保logs目录存在
os.makedirs('logs', exist_ok=True)

# 导入数据处理模块
from core_logic.data_handler import DataHandler

def test_stock_data():
    """测试股票数据获取功能"""
    
    print("\n===== 测试股票数据获取 =====")
    
    # 创建数据处理器实例
    handler = DataHandler(cache_dir='data_cache')
    
    # 设置测试日期范围
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    
    # 测试股票数据获取
    stocks = ['sh000001', 'sz000001']
    
    for stock in stocks:
        try:
            print(f"\n获取股票数据: {stock}")
            data = handler.get_stock_data(stock, start_date, end_date)
            
            if data is not None and not data.empty:
                print(f"成功获取 {stock} 数据, 行数: {len(data)}")
                print(f"数据日期范围: {data.index.min()} 到 {data.index.max()}")
                print(f"数据列: {data.columns.tolist()}")
                print(f"数据示例:\n{data.head(3)}")
            else:
                print(f"获取 {stock} 数据失败: 数据为空")
        except Exception as e:
            print(f"获取 {stock} 数据时出错: {str(e)}")
            print(traceback.format_exc())

def test_index_data():
    """测试指数数据获取功能"""
    
    print("\n===== 测试指数数据获取 =====")
    
    # 创建数据处理器实例
    handler = DataHandler(cache_dir='data_cache')
    
    # 设置测试日期范围
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    
    # 测试指数数据获取
    indices = ['index_000001', 'index_399001', 'index_000300']
    
    for idx in indices:
        try:
            print(f"\n获取指数数据: {idx}")
            data = handler.get_stock_data(idx, start_date, end_date)
            
            if data is not None and not data.empty:
                print(f"成功获取 {idx} 数据, 行数: {len(data)}")
                print(f"数据日期范围: {data.index.min()} 到 {data.index.max()}")
                print(f"数据列: {data.columns.tolist()}")
                print(f"数据示例:\n{data.head(3)}")
            else:
                print(f"获取 {idx} 数据失败: 数据为空")
        except Exception as e:
            print(f"获取 {idx} 数据时出错: {str(e)}")
            print(traceback.format_exc())

def test_crypto_data():
    """测试加密货币数据获取功能"""
    
    print("\n===== 测试加密货币数据获取 =====")
    
    # 创建数据处理器实例
    handler = DataHandler(cache_dir='data_cache')
    
    # 设置测试日期范围
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    
    # 测试加密货币数据获取
    cryptos = ['crypto_BTC', 'crypto_ETH']
    
    for crypto in cryptos:
        try:
            print(f"\n获取加密货币数据: {crypto}")
            data = handler.get_stock_data(crypto, start_date, end_date)
            
            if data is not None and not data.empty:
                print(f"成功获取 {crypto} 数据, 行数: {len(data)}")
                print(f"数据日期范围: {data.index.min()} 到 {data.index.max()}")
                print(f"数据列: {data.columns.tolist()}")
                print(f"数据示例:\n{data.head(3)}")
            else:
                print(f"获取 {crypto} 数据失败: 数据为空")
        except Exception as e:
            print(f"获取 {crypto} 数据时出错: {str(e)}")
            print(traceback.format_exc())

def test_different_frequencies():
    """测试不同频率数据获取功能"""
    
    print("\n===== 测试不同频率数据获取 =====")
    
    # 创建数据处理器实例
    handler = DataHandler(cache_dir='data_cache')
    
    # 设置测试日期范围
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
    
    # 测试不同频率数据获取
    stock = 'sh000001'
    frequencies = ['日线', '周线', '月线']
    
    for freq in frequencies:
        try:
            print(f"\n获取 {stock} 的 {freq} 数据")
            data = handler.get_stock_data(stock, start_date, end_date, frequency=freq)
            
            if data is not None and not data.empty:
                print(f"成功获取 {freq} 数据, 行数: {len(data)}")
                print(f"数据日期范围: {data.index.min()} 到 {data.index.max()}")
                print(f"数据示例:\n{data.head(3)}")
            else:
                print(f"获取 {freq} 数据失败: 数据为空")
        except Exception as e:
            print(f"获取 {freq} 数据时出错: {str(e)}")
            print(traceback.format_exc())

def test_cache_functionality():
    """测试缓存功能"""
    
    print("\n===== 测试缓存功能 =====")
    
    # 创建数据处理器实例
    handler = DataHandler(cache_dir='data_cache')
    
    # 设置测试日期范围
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    
    stock = 'sh000001'
    
    # 第一次获取数据，不使用缓存
    print(f"\n第一次获取数据: {stock}")
    try:
        import time
        start_time = time.time()
        data1 = handler.get_stock_data(stock, start_date, end_date, use_cache=False)
        time1 = time.time() - start_time
        
        print(f"第一次获取耗时: {time1:.2f} 秒")
        print(f"数据行数: {len(data1)}")
        
        # 第二次获取数据，使用缓存
        print(f"\n第二次获取数据: {stock} (使用缓存)")
        start_time = time.time()
        data2 = handler.get_stock_data(stock, start_date, end_date, use_cache=True)
        time2 = time.time() - start_time
        
        print(f"第二次获取耗时: {time2:.2f} 秒")
        print(f"数据行数: {len(data2)}")
        
        # 验证两次获取的数据是否相同
        is_same = data1.equals(data2)
        print(f"两次获取的数据是否相同: {is_same}")
        print(f"加速比: {time1 / time2 if time2 > 0 else 'N/A'}")
        
    except Exception as e:
        print(f"测试缓存功能时出错: {str(e)}")
        print(traceback.format_exc())

def test_simulated_data():
    """测试模拟数据生成功能"""
    
    print("\n===== 测试模拟数据生成功能 =====")
    
    # 创建数据处理器实例
    handler = DataHandler(cache_dir='data_cache')
    
    # 设置测试日期范围
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
    
    # 通过私有方法测试模拟数据生成 (仅用于测试)
    try:
        print("\n生成模拟股票数据")
        stock_data = handler._generate_simulated_data('test_stock', start_date, end_date, 'stock')
        print(f"生成的模拟股票数据行数: {len(stock_data)}")
        print(f"模拟股票数据示例:\n{stock_data.head(3)}")
        
        print("\n生成模拟指数数据")
        index_data = handler._generate_simulated_data('test_index', start_date, end_date, 'index')
        print(f"生成的模拟指数数据行数: {len(index_data)}")
        print(f"模拟指数数据示例:\n{index_data.head(3)}")
        
        print("\n生成模拟加密货币数据")
        crypto_data = handler._generate_simulated_data('BTC', start_date, end_date, 'crypto')
        print(f"生成的模拟加密货币数据行数: {len(crypto_data)}")
        print(f"模拟加密货币数据示例:\n{crypto_data.head(3)}")
        
    except Exception as e:
        print(f"测试模拟数据生成功能时出错: {str(e)}")
        print(traceback.format_exc())

def run_all_tests():
    """运行所有测试"""
    try:
        test_stock_data()
        test_index_data()
        test_crypto_data()
        test_different_frequencies()
        test_cache_functionality()
        test_simulated_data()
        print("\n===== 所有测试完成 =====")
        return True
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        print(traceback.format_exc())
        return False

if __name__ == '__main__':
    run_all_tests() 