"""
修复集成学习模型检测问题

该脚本修复集成学习训练完成后，策略性能评估和实况信号决策模块无法找到最佳模型的问题。
"""

import os
import sys
import logging
import re

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(parent_dir, 'logs', 'fix_ensemble_model.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('fix_ensemble_model')

def fix_drl_agent_module():
    """修复DRL智能体模块中的模型保存和加载逻辑"""
    logger.info("修复DRL智能体模块中的模型保存和加载逻辑")
    
    # 定位DRL智能体模块文件
    drl_agent_path = os.path.join(parent_dir, 'core_logic', 'drl_agent.py')
    
    if not os.path.exists(drl_agent_path):
        logger.error(f"找不到DRL智能体模块文件: {drl_agent_path}")
        return False
    
    # 读取文件内容
    try:
        with open(drl_agent_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        logger.error(f"读取DRL智能体模块文件失败: {str(e)}")
        return False
    
    # 检查是否存在集成学习模型保存问题
    if "集成学习训练完成" in content and "self.save_model" in content:
        logger.info("检测到集成学习模型保存代码")
        
        # 查找集成学习训练完成后的模型保存逻辑
        ensemble_training_pattern = r'(def\s+train_ensemble_models.*?集成学习训练完成.*?)(\s+return\s+)'
        ensemble_training_match = re.search(ensemble_training_pattern, content, re.DOTALL)
        
        if ensemble_training_match:
            logger.info("找到集成学习训练完成代码段")
            
            # 检查是否缺少保存最佳模型的代码
            if "self.save_best_model" not in ensemble_training_match.group(1):
                logger.info("集成学习训练完成后缺少保存最佳模型的代码")
                
                # 构建修复后的代码
                fixed_code = ensemble_training_match.group(1)
                fixed_code += "\n        # 保存最佳集成模型\n"
                fixed_code += "        logger.info('保存集成学习最佳模型')\n"
                fixed_code += "        self.save_best_model(ensemble_dir, 'ensemble')\n"
                fixed_code += "        # 更新当前训练模型列表\n"
                fixed_code += "        if hasattr(st.session_state, 'current_training_models') and ensemble_dir not in st.session_state.current_training_models:\n"
                fixed_code += "            st.session_state.current_training_models.append(ensemble_dir)\n"
                fixed_code += "            logger.info(f'将集成目录添加到当前训练模型列表: {ensemble_dir}')\n"
                fixed_code += ensemble_training_match.group(2)
                
                # 替换原始代码
                new_content = content.replace(ensemble_training_match.group(0), fixed_code)
                
                # 保存修改后的文件
                try:
                    with open(drl_agent_path, 'w', encoding='utf-8') as f:
                        f.write(new_content)
                    logger.info("成功修复DRL智能体模块中的集成学习模型保存逻辑")
                    return True
                except Exception as e:
                    logger.error(f"保存修改后的DRL智能体模块文件失败: {str(e)}")
                    return False
            else:
                logger.info("集成学习训练完成后已有保存最佳模型的代码，无需修复")
        else:
            logger.warning("未找到集成学习训练完成代码段")
    else:
        logger.warning("未检测到集成学习模型保存代码")
    
    return False

def fix_performance_analyzer_module():
    """修复性能分析器模块中的模型加载逻辑"""
    logger.info("修复性能分析器模块中的模型加载逻辑")
    
    # 定位性能分析器模块文件
    performance_analyzer_path = os.path.join(parent_dir, 'core_logic', 'performance_analyzer.py')
    
    if not os.path.exists(performance_analyzer_path):
        logger.error(f"找不到性能分析器模块文件: {performance_analyzer_path}")
        return False
    
    # 读取文件内容
    try:
        with open(performance_analyzer_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        logger.error(f"读取性能分析器模块文件失败: {str(e)}")
        return False
    
    # 检查是否存在集成学习模型加载问题
    if "load_model" in content and "model_path" in content:
        logger.info("检测到模型加载代码")
        
        # 查找模型加载逻辑
        load_model_pattern = r'(def\s+load_model.*?)(\s+return\s+model)'
        load_model_match = re.search(load_model_pattern, content, re.DOTALL)
        
        if load_model_match:
            logger.info("找到模型加载代码段")
            
            # 检查是否缺少检查集成学习目录的代码
            if "ensemble" not in load_model_match.group(1) or "_ensemble" not in load_model_match.group(1):
                logger.info("模型加载逻辑中缺少检查集成学习目录的代码")
                
                # 构建修复后的代码
                fixed_code = load_model_match.group(1)
                
                # 在return语句前添加集成学习目录检查代码
                ensemble_check_code = """
        # 检查是否是集成学习目录
        if os.path.isdir(model_path) and model_path.endswith('_ensemble'):
            logger.info(f"检测到集成学习目录: {model_path}")
            # 加载集成学习模型
            try:
                from core_logic.ensemble_learning import EnsembleLearning
                ensemble = EnsembleLearning()
                model = ensemble.load_ensemble_model(model_path)
                logger.info(f"成功加载集成学习模型: {model_path}")
                return model
            except Exception as e:
                logger.error(f"加载集成学习模型失败: {str(e)}")
                raise ValueError(f"加载集成学习模型失败: {str(e)}")
        """
                
                # 插入集成学习目录检查代码
                fixed_code += ensemble_check_code
                fixed_code += load_model_match.group(2)
                
                # 替换原始代码
                new_content = content.replace(load_model_match.group(0), fixed_code)
                
                # 保存修改后的文件
                try:
                    with open(performance_analyzer_path, 'w', encoding='utf-8') as f:
                        f.write(new_content)
                    logger.info("成功修复性能分析器模块中的模型加载逻辑")
                    return True
                except Exception as e:
                    logger.error(f"保存修改后的性能分析器模块文件失败: {str(e)}")
                    return False
            else:
                logger.info("模型加载逻辑中已有检查集成学习目录的代码，无需修复")
        else:
            logger.warning("未找到模型加载代码段")
    else:
        logger.warning("未检测到模型加载代码")
    
    return False

def fix_main_app_module():
    """修复主应用程序模块中的模型检测逻辑"""
    logger.info("修复主应用程序模块中的模型检测逻辑")
    
    # 定位主应用程序模块文件
    main_app_path = os.path.join(parent_dir, 'main_app.py')
    
    if not os.path.exists(main_app_path):
        logger.error(f"找不到主应用程序模块文件: {main_app_path}")
        return False
    
    # 读取文件内容
    try:
        with open(main_app_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        logger.error(f"读取主应用程序模块文件失败: {str(e)}")
        return False
    
    # 检查是否存在模型检测问题
    if "current_training_models" in content and "No model detected" in content:
        logger.info("检测到模型检测代码")
        
        # 查找模型检测逻辑
        model_detection_pattern = r'(if\s+not\s+st\.session_state\.current_training_models:.*?st\.warning\(["\']No model detected)'
        model_detection_match = re.search(model_detection_pattern, content, re.DOTALL)
        
        if model_detection_match:
            logger.info("找到模型检测代码段")
            
            # 构建修复后的代码
            fixed_code = """if not st.session_state.current_training_models:
            st.warning("未检测到训练模型。请先在'DRL智能体训练'页面训练模型。")
        else:
            # 检查当前训练模型列表中是否包含有效模型
            valid_models = []
            for model_path in st.session_state.current_training_models:
                if os.path.exists(model_path):
                    if os.path.isfile(model_path) and model_path.endswith('.zip'):
                        valid_models.append(model_path)
                    elif os.path.isdir(model_path) and model_path.endswith('_ensemble'):
                        valid_models.append(model_path)
            
            if not valid_models:
                st.warning("No model detected"""
            
            # 替换原始代码
            new_content = content.replace(model_detection_match.group(0), fixed_code)
            
            # 保存修改后的文件
            try:
                with open(main_app_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                logger.info("成功修复主应用程序模块中的模型检测逻辑")
                return True
            except Exception as e:
                logger.error(f"保存修改后的主应用程序模块文件失败: {str(e)}")
                return False
        else:
            logger.warning("未找到模型检测代码段")
    else:
        logger.warning("未检测到模型检测代码")
    
    return False

def main():
    """主函数"""
    logger.info("=" * 80)
    logger.info("修复集成学习模型检测问题")
    logger.info("=" * 80)
    
    # 修复DRL智能体模块
    drl_agent_fixed = fix_drl_agent_module()
    
    # 修复性能分析器模块
    performance_analyzer_fixed = fix_performance_analyzer_module()
    
    # 修复主应用程序模块
    main_app_fixed = fix_main_app_module()
    
    # 输出修复结果
    logger.info("=" * 80)
    logger.info("修复结果")
    logger.info("=" * 80)
    logger.info(f"DRL智能体模块: {'已修复' if drl_agent_fixed else '无需修复或修复失败'}")
    logger.info(f"性能分析器模块: {'已修复' if performance_analyzer_fixed else '无需修复或修复失败'}")
    logger.info(f"主应用程序模块: {'已修复' if main_app_fixed else '无需修复或修复失败'}")
    
    # 根据修复结果设置退出代码
    if drl_agent_fixed or performance_analyzer_fixed or main_app_fixed:
        logger.info("至少有一个模块已修复")
        sys.exit(0)
    else:
        logger.warning("所有模块均无需修复或修复失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
