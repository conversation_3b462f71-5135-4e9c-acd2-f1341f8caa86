"""
数据处理模块
负责协调数据获取、清洗、验证和缓存
"""

import os
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

from .data_fetcher import DataFetcher
from .data_cleaner import DataCleaner
from .data_validator import DataValidator
from .data_cache import DataCache

class DataHandler:
    """
    数据处理类
    负责协调数据获取、清洗、验证和缓存，提供统一的数据处理接口
    """

    def __init__(self, cache_dir='data_cache', timezone='Asia/Shanghai',
                 missing_value_strategy='ffill', outlier_detection=True,
                 data_validation=True, adjust_price=True):
        """
        初始化数据处理器

        参数:
            cache_dir (str): 缓存目录路径
            timezone (str): 时区设置，默认为'Asia/Shanghai'
            missing_value_strategy (str): 缺失值处理策略，可选'ffill'(前向填充),'bfill'(后向填充),'mean'(均值填充),'median'(中位数填充),'none'(不处理)
            outlier_detection (bool): 是否进行异常值检测
            data_validation (bool): 是否进行数据验证
            adjust_price (bool): 是否使用复权价格
        """
        self.logger = logging.getLogger('drl_trading')
        self.cache_dir = cache_dir
        self.timezone = timezone
        self.missing_value_strategy = missing_value_strategy
        self.outlier_detection = outlier_detection
        self.data_validation = data_validation
        self.adjust_price = adjust_price

        # 初始化组件
        self.fetcher = DataFetcher(logger=self.logger, timezone=timezone)
        self.cleaner = DataCleaner(missing_value_strategy=missing_value_strategy,
                                  outlier_detection=outlier_detection,
                                  logger=self.logger)
        self.validator = DataValidator(logger=self.logger)
        self.cache = DataCache(cache_dir=cache_dir, logger=self.logger)

        # 确保缓存目录存在
        os.makedirs(self.cache_dir, exist_ok=True)

    def get_stock_data(self, stock_code, start_date, end_date, frequency='日线', use_cache=True):
        """
        获取金融数据（股票、指数）

        参数:
            stock_code (str): 金融产品代码，格式如下：
                - 股票: 'sh000001' 或 'sz399001'
                - 指数: 'index_000300'，如 'index_000300'
            start_date (str): 开始日期，格式为 'YYYY-MM-DD'
            end_date (str): 结束日期，格式为 'YYYY-MM-DD'
            frequency (str): 数据频率，可选 '日线', '周线', '月线', '小时线', '分钟线'
            use_cache (bool): 是否使用缓存

        返回:
            pandas.DataFrame: 金融数据
        """
        # 首先检查是否是加密货币代码，如果是则直接禁止
        if stock_code.startswith('crypto_'):
            self.logger.warning("加密货币数据提取功能已被禁用")
            error_message = "加密货币数据提取功能已被禁用，请使用其他类型的金融数据。\n"
            error_message += "请参考akshare官方文档获取正确的数据提取方法。\n"
            error_message += "根据要求，系统不再支持加密货币数据提取和模拟数据生成功能。"
            raise ValueError(error_message)

        # 检查期货代码，如果是则直接禁止
        if stock_code.startswith('futures_'):
            self.logger.warning("期货数据提取功能已被禁用")
            error_message = "期货数据提取功能已被禁用，请使用其他类型的金融数据。\n"
            error_message += "期货代码格式说明:\n"
            error_message += "- 中金所股指期货: IF, IC, IH\n"
            error_message += "- 上海期货交易所: CU(铜), AL(铝), ZN(锌), AU(黄金), AG(白银)等\n"
            error_message += "- 大连商品交易所: A(豆一), M(豆粕), C(玉米), Y(豆油)等\n"
            error_message += "- 郑州商品交易所: SR(白糖), CF(棉花), TA(PTA), MA(甲醇)等\n"
            raise ValueError(error_message)

        # 如果使用缓存且缓存存在，则从缓存加载
        if use_cache:
            cached_data = self.cache.get_cached_data(stock_code, start_date, end_date, frequency)
            if cached_data is not None:
                self.logger.info(f"从缓存加载数据成功，共 {len(cached_data)} 条记录")

                # **关键修复**: 即使是缓存数据也要应用日期过滤
                if not cached_data.empty and isinstance(cached_data.index, pd.DatetimeIndex):
                    original_length = len(cached_data)
                    start_dt = pd.to_datetime(start_date)
                    end_dt = pd.to_datetime(end_date)

                    # 过滤缓存数据到请求的日期范围
                    filtered_data = cached_data[(cached_data.index >= start_dt) & (cached_data.index <= end_dt)]

                    self.logger.info(f"缓存数据日期过滤: 原始 {original_length} 条 -> 过滤后 {len(filtered_data)} 条")
                    self.logger.info(f"请求日期范围: {start_date} 至 {end_date}")

                    if not filtered_data.empty:
                        actual_start = filtered_data.index.min().strftime('%Y-%m-%d')
                        actual_end = filtered_data.index.max().strftime('%Y-%m-%d')
                        self.logger.info(f"实际数据范围: {actual_start} 至 {actual_end}")
                        return filtered_data
                    else:
                        self.logger.warning("缓存数据日期过滤后为空，可能请求的日期范围内没有交易数据")
                        return pd.DataFrame()  # 返回空DataFrame而不是None

                return cached_data

        # 从数据源获取数据
        try:
            self.logger.info(f"从数据源获取数据: {stock_code}, {start_date} 至 {end_date}, 频率: {frequency}")

            # 频率参数标准化：支持中文和英文格式
            freq_map = {
                'D': '日线', 'daily': '日线',
                'W': '周线', 'weekly': '周线',
                'M': '月线', 'monthly': '月线',
                'H': '小时线', 'hourly': '小时线',
                'min': '分钟线', 'minute': '分钟线'
            }

            # 如果是英文格式，转换为中文
            if frequency in freq_map:
                normalized_freq = freq_map[frequency]
                self.logger.info(f"频率参数标准化: '{frequency}' -> '{normalized_freq}'")
            else:
                normalized_freq = frequency

            # 根据代码类型选择不同的获取方法
            if normalized_freq == '日线':
                if stock_code.startswith('index_'):
                    # 指数数据
                    data = self.fetcher.fetch_index_data(stock_code, start_date, end_date, normalized_freq)
                else:
                    # 股票数据
                    data = self.fetcher.fetch_stock_data(stock_code, start_date, end_date, normalized_freq)
            elif normalized_freq == '周线':
                # 使用日线数据重采样为周线
                daily_data = self.get_stock_data(stock_code, start_date, end_date, '日线', use_cache=False)
                data = self._resample_data(daily_data, 'W')
            elif normalized_freq == '月线':
                # 使用日线数据重采样为月线
                daily_data = self.get_stock_data(stock_code, start_date, end_date, '日线', use_cache=False)
                data = self._resample_data(daily_data, 'M')
            else:
                supported_freqs = "'日线', '周线', '月线' 或对应的英文格式 'D', 'W', 'M', 'daily', 'weekly', 'monthly'"
                raise ValueError(f"不支持的数据频率: {frequency}，目前仅支持 {supported_freqs}")

            # 清洗数据
            data = self.cleaner.clean_data(data)

            # **关键修复**: 根据请求的日期范围过滤数据
            if data is not None and not data.empty:
                original_length = len(data)

                # 确保索引是日期类型
                if not isinstance(data.index, pd.DatetimeIndex):
                    self.logger.warning("数据索引不是日期类型，无法进行日期过滤")
                else:
                    # 转换日期字符串为日期对象
                    start_dt = pd.to_datetime(start_date)
                    end_dt = pd.to_datetime(end_date)

                    # 过滤数据到请求的日期范围
                    data = data[(data.index >= start_dt) & (data.index <= end_dt)]
                    filtered_length = len(data)

                    self.logger.info(f"日期过滤: 原始数据 {original_length} 条 -> 过滤后 {filtered_length} 条")
                    self.logger.info(f"请求日期范围: {start_date} 至 {end_date}")
                    if not data.empty:
                        actual_start = data.index.min().strftime('%Y-%m-%d')
                        actual_end = data.index.max().strftime('%Y-%m-%d')
                        self.logger.info(f"实际数据范围: {actual_start} 至 {actual_end}")
                    else:
                        self.logger.warning("日期过滤后数据为空，可能请求的日期范围内没有交易数据")

            # 验证数据
            if self.data_validation:
                is_valid, error_msg = self.validator.validate_data(data)
                if not is_valid:
                    self.logger.warning(f"数据验证失败: {error_msg}")
                    # 不中断流程，继续使用数据

            # 缓存数据
            if use_cache:
                self.cache.cache_data(data, stock_code, start_date, end_date, frequency)

            self.logger.info(f"数据处理完成，共 {len(data)} 条记录")
            return data

        except Exception as e:
            self.logger.error(f"获取数据失败: {str(e)}")

            # 提供详细的错误信息和建议
            error_message = f"获取数据失败: {str(e)}\n"

            if stock_code.startswith('sh') or stock_code.startswith('sz'):
                error_message += "股票代码格式说明:\n"
                error_message += "- 上海证券交易所股票: sh + 6位数字，如 sh600000\n"
                error_message += "- 深圳证券交易所股票: sz + 6位数字，如 sz000001\n"
            elif stock_code.startswith('index_'):
                error_message += "指数代码格式说明:\n"
                error_message += "- 上证指数: index_000001, index_000300(沪深300), index_000016(上证50)等\n"
                error_message += "- 深证指数: index_399001(深证成指), index_399006(创业板指)等\n"

            raise ValueError(error_message)

    def _resample_data(self, data, freq):
        """
        重采样数据

        参数:
            data (pandas.DataFrame): 原始数据
            freq (str): 重采样频率，'W'表示周，'M'表示月

        返回:
            pandas.DataFrame: 重采样后的数据
        """
        if data is None or data.empty:
            self.logger.warning("输入数据为空，无法进行重采样")
            return data

        # 确保索引是日期类型
        if not isinstance(data.index, pd.DatetimeIndex):
            self.logger.warning("数据索引不是日期类型，无法进行重采样")
            return data

        # 确保数据按日期排序
        data = data.sort_index()

        # 重采样
        try:
            resampled = data.resample(freq).agg({
                '开盘': 'first',
                '最高': 'max',
                '最低': 'min',
                '收盘': 'last',
                '成交量': 'sum'
            })

            # 如果有成交额列，也进行重采样
            if '成交额' in data.columns:
                volume_data = data['成交额'].resample(freq).sum()
                resampled['成交额'] = volume_data

            # 删除缺失值
            resampled = resampled.dropna()

            self.logger.info(f"数据重采样完成，从 {len(data)} 条记录重采样为 {len(resampled)} 条记录")
            return resampled
        except Exception as e:
            self.logger.error(f"数据重采样失败: {str(e)}")
            return data

    def get_data_quality_report(self, data):
        """
        获取数据质量报告

        参数:
            data (pandas.DataFrame): 金融数据

        返回:
            dict: 数据质量报告
        """
        if data is None or data.empty:
            return {"error": "数据为空，无法生成质量报告"}

        # 获取数据质量评分
        quality_score = self.validator.check_data_quality(data)

        # 生成数据统计信息
        stats = {
            "rows": len(data),
            "columns": len(data.columns),
            "date_range": f"{data.index.min().strftime('%Y-%m-%d')} 至 {data.index.max().strftime('%Y-%m-%d')}",
            "missing_values": data.isnull().sum().sum(),
            "missing_percentage": round(data.isnull().sum().sum() / (data.shape[0] * data.shape[1]) * 100, 2),
            "column_stats": {}
        }

        # 计算每列的统计信息
        for col in data.columns:
            if pd.api.types.is_numeric_dtype(data[col]):
                stats["column_stats"][col] = {
                    "min": data[col].min(),
                    "max": data[col].max(),
                    "mean": data[col].mean(),
                    "median": data[col].median(),
                    "std": data[col].std(),
                    "missing": data[col].isnull().sum(),
                    "missing_percentage": round(data[col].isnull().sum() / len(data) * 100, 2)
                }

        # 合并质量评分和统计信息
        report = {
            "quality_score": quality_score,
            "statistics": stats
        }

        return report

    def get_cache_stats(self):
        """
        获取缓存统计信息

        返回:
            dict: 缓存统计信息
        """
        return self.cache.get_cache_stats()

    def clear_cache(self):
        """
        清理过期缓存

        返回:
            int: 清理的缓存文件数量
        """
        return self.cache.clear_expired_cache()

    def get_latest_trading_date(self):
        """
        获取最新交易日期

        返回:
            str: 最新交易日期，格式为 'YYYY-MM-DD'
        """
        try:
            # 获取上证指数最近10天的数据，用于确定最新交易日
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=10)).strftime('%Y-%m-%d')

            # 获取上证指数数据
            index_data = self.fetcher.fetch_index_data('index_000001', start_date, end_date, '日线')

            if index_data is not None and not index_data.empty:
                # 返回最新的交易日期
                latest_date = index_data.index.max()
                return latest_date.strftime('%Y-%m-%d')
            else:
                # 如果无法获取数据，返回当前日期
                self.logger.warning("无法获取最新交易日数据，返回当前日期")
                return datetime.now().strftime('%Y-%m-%d')
        except Exception as e:
            self.logger.error(f"获取最新交易日期失败: {str(e)}")
            # 出错时返回当前日期
            return datetime.now().strftime('%Y-%m-%d')
