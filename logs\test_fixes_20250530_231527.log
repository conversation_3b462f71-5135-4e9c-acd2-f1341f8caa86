2025-05-30 23:15:27,201 - test_fixes - INFO - ==================================================
2025-05-30 23:15:27,201 - test_fixes - INFO - 开始测试修复后的功能
2025-05-30 23:15:27,201 - test_fixes - INFO - ==================================================
2025-05-30 23:15:27,201 - test_fixes - INFO - 测试GPU检测功能
2025-05-30 23:15:27,210 - gpu_installer - INFO - 检测系统GPU...
2025-05-30 23:15:27,308 - gpu_installer - INFO - 检测到NVIDIA GPU
2025-05-30 23:15:27,308 - gpu_installer - WARNING - 检测GPU时出错: argument of type 'NoneType' is not iterable
2025-05-30 23:15:27,309 - gpu_installer - WARNING - 检测CUDA时出错: [WinError 2] 系统找不到指定的文件。
2025-05-30 23:15:29,104 - gpu_installer - WARNING - TensorFlow GPU检测失败
2025-05-30 23:15:29,104 - test_fixes - INFO - GPU检测结果: {'has_gpu': True, 'gpu_count': 1, 'gpu_names': [], 'cuda_version': None, 'pytorch_gpu': True, 'tensorflow_gpu': False}
2025-05-30 23:15:29,104 - test_fixes - INFO - 测试特征工程适配器
2025-05-30 23:15:32,588 - drl_trading - INFO - 检测到扁平格式配置，转换为标准格式
2025-05-30 23:15:32,588 - test_fixes - INFO - 扁平格式配置: {'use_price': True, 'use_volume': True, 'use_technical': True, 'sma_periods': [5, 10, 20, 30, 60], 'ema_periods': [5, 10, 20, 30, 60], 'rsi_periods': [14], 'macd_params': {'fast': 12, 'slow': 26, 'signal': 9}, 'bb_params': {'window': 20, 'num_std': 2}, 'atr_periods': [14], 'normalization': 'zscore'}
2025-05-30 23:15:32,588 - test_fixes - INFO - 转换后的标准格式配置: {'price_features': {'use': True}, 'volume': {'use': True}, 'sma': {'use': True, 'periods': [5, 10, 20, 30, 60]}, 'ema': {'use': True, 'periods': [5, 10, 20, 30, 60]}, 'rsi': {'use': True, 'periods': [14]}, 'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9}, 'bbands': {'use': True, 'period': 20, 'std': 2}, 'atr': {'use': True, 'periods': [14]}, 'normalization': {'use': True, 'method': 'zscore'}}
2025-05-30 23:15:32,588 - drl_trading - INFO - 检测到标准格式配置，进行验证和填充默认值
2025-05-30 23:15:32,588 - test_fixes - INFO - 标准格式配置: {'price_features': {'use': True}, 'volume': {'use': True, 'periods': [5, 10, 20]}, 'sma': {'use': True, 'periods': [5, 10, 20, 60]}, 'ema': {'use': True, 'periods': [5, 10, 20]}, 'rsi': {'use': True, 'periods': [6, 14, 21]}, 'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9}, 'bbands': {'use': True, 'period': 20, 'std': 2.0}, 'normalization': {'use': True, 'method': 'minmax'}}
2025-05-30 23:15:32,589 - test_fixes - INFO - 适配后的配置: {'price_features': {'use': True}, 'volume': {'use': True, 'periods': [5, 10, 20]}, 'sma': {'use': True, 'periods': [5, 10, 20, 60]}, 'ema': {'use': True, 'periods': [5, 10, 20]}, 'rsi': {'use': True, 'periods': [6, 14, 21]}, 'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9}, 'bbands': {'use': True, 'period': 20, 'std': 2.0}, 'atr': {'use': True, 'periods': [14]}, 'normalization': {'use': True, 'method': 'minmax'}}
2025-05-30 23:15:32,589 - test_fixes - INFO - 测试性能分析器参数处理
2025-05-30 23:15:32,591 - test_fixes - INFO - 测试直接传入DataFrame
2025-05-30 23:15:32,593 - drl_trading - INFO - 交易记录中没有profit列，尝试从交易记录计算
2025-05-30 23:15:32,594 - test_fixes - ERROR - 测试性能分析器参数处理时出错: 'shares'
2025-05-30 23:15:32,597 - test_fixes - ERROR - Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pandas\core\indexes\base.py", line 3805, in get_loc
    return self._engine.get_loc(casted_key)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^
  File "index.pyx", line 167, in pandas._libs.index.IndexEngine.get_loc
  File "index.pyx", line 196, in pandas._libs.index.IndexEngine.get_loc
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7081, in pandas._libs.hashtable.PyObjectHashTable.get_item
  File "pandas\\_libs\\hashtable_class_helper.pxi", line 7089, in pandas._libs.hashtable.PyObjectHashTable.get_item
KeyError: 'shares'

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\cursor\量化\test_fixes.py", line 165, in test_performance_analyzer
    metrics_df = analyzer.analyze(trades_df, portfolio_values)
  File "C:\cursor\量化\quant_project\core_logic\performance_analyzer.py", line 87, in analyze
    trade_stats = self.calculate_trade_statistics(trades_df)
  File "C:\cursor\量化\quant_project\core_logic\performance_analyzer.py", line 370, in calculate_trade_statistics
    shares = buy_trade['shares']  # 假设买入和卖出的股数相同
             ~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pandas\core\series.py", line 1121, in __getitem__
    return self._get_value(key)
           ~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pandas\core\series.py", line 1237, in _get_value
    loc = self.index.get_loc(label)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\pandas\core\indexes\base.py", line 3812, in get_loc
    raise KeyError(key) from err
KeyError: 'shares'

2025-05-30 23:15:32,598 - test_fixes - INFO - === 测试数据获取功能 ===
2025-05-30 23:15:32,623 - test_fixes - INFO - 测试数据获取: {'stock_code': 'sh000001', 'start_date': '2020-01-01', 'end_date': '2020-12-31', 'frequency': 'D', 'data_source': '指数'}
2025-05-30 23:15:32,623 - drl_trading - INFO - 缓存文件不存在: data_cache\c15b165ee622bcd2366ea8205db43b32.csv
2025-05-30 23:15:32,623 - drl_trading - INFO - 从数据源获取数据: sh000001, 2020-01-01 至 2020-12-31, 频率: D
2025-05-30 23:15:32,623 - drl_trading - ERROR - 获取数据失败: 不支持的数据频率: D，目前仅支持 '日线', '周线', '月线'
2025-05-30 23:15:32,623 - test_fixes - ERROR - 数据获取功能测试失败: 获取数据失败: 不支持的数据频率: D，目前仅支持 '日线', '周线', '月线'
股票代码格式说明:
- 上海证券交易所股票: sh + 6位数字，如 sh600000
- 深圳证券交易所股票: sz + 6位数字，如 sz000001

2025-05-30 23:15:32,623 - test_fixes - INFO - === 测试因子挖掘功能 ===
2025-05-30 23:15:32,627 - drl_trading - INFO - 缓存文件不存在: data_cache\c15b165ee622bcd2366ea8205db43b32.csv
2025-05-30 23:15:32,627 - drl_trading - INFO - 从数据源获取数据: sh000001, 2020-01-01 至 2020-12-31, 频率: D
2025-05-30 23:15:32,627 - drl_trading - ERROR - 获取数据失败: 不支持的数据频率: D，目前仅支持 '日线', '周线', '月线'
2025-05-30 23:15:32,627 - test_fixes - ERROR - 因子挖掘功能测试失败: 获取数据失败: 不支持的数据频率: D，目前仅支持 '日线', '周线', '月线'
股票代码格式说明:
- 上海证券交易所股票: sh + 6位数字，如 sh600000
- 深圳证券交易所股票: sz + 6位数字，如 sz000001

2025-05-30 23:15:32,627 - test_fixes - INFO - === 测试DRL智能体功能 ===
2025-05-30 23:15:32,628 - drl_trading - INFO - 初始化DRL智能体
2025-05-30 23:15:32,628 - drl_trading - INFO - 环境配置: None
2025-05-30 23:15:32,628 - drl_trading - INFO - 智能体配置: dict_keys(['algorithm'])
2025-05-30 23:15:32,628 - drl_trading - INFO - 超参数优化配置: None
2025-05-30 23:15:32,628 - drl_trading - WARNING - 创建DRL智能体时出错: 'NoneType' object has no attribute 'columns'，将使用空智能体
2025-05-30 23:15:32,628 - drl_trading - WARNING - 创建空智能体 (PPO)，仅用于兼容性测试
2025-05-30 23:15:32,629 - test_fixes - INFO - DRL智能体算法: PPO
2025-05-30 23:15:32,629 - test_fixes - INFO - 智能体方法: ['__class__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__firstlineno__', '__format__', '__ge__', '__getattribute__', '__getstate__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__setattr__', '__sizeof__', '__static_attributes__', '__str__', '__subclasshook__', '__weakref__', '_create_agent', 'agent', 'agent_config', 'algorithm', 'env_config', 'load', 'predict', 'save', 'train']
2025-05-30 23:15:32,629 - test_fixes - INFO - DRL智能体功能测试通过
2025-05-30 23:15:32,629 - test_fixes - INFO - ==================================================
2025-05-30 23:15:32,629 - test_fixes - INFO - 测试结果:
2025-05-30 23:15:32,629 - test_fixes - INFO - 1. GPU检测功能: 成功
2025-05-30 23:15:32,630 - test_fixes - INFO - 2. 特征工程适配器: 失败
2025-05-30 23:15:32,630 - test_fixes - INFO -    错误: 未知错误
2025-05-30 23:15:32,630 - test_fixes - INFO - 3. 性能分析器参数处理: 失败
2025-05-30 23:15:32,630 - test_fixes - INFO -    错误: 'shares'
2025-05-30 23:15:32,630 - test_fixes - INFO - 4. 数据获取功能: 失败
2025-05-30 23:15:32,630 - test_fixes - INFO -    错误: False
2025-05-30 23:15:32,630 - test_fixes - INFO - 5. 因子挖掘功能: 失败
2025-05-30 23:15:32,631 - test_fixes - INFO -    错误: False
2025-05-30 23:15:32,631 - test_fixes - INFO - 6. DRL智能体功能: 成功
2025-05-30 23:15:32,631 - test_fixes - INFO - ==================================================
2025-05-30 23:15:32,631 - test_fixes - INFO - 修复测试总结: 部分失败
