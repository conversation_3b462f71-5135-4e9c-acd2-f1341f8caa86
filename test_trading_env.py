#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
交易环境测试脚本
测试交易环境的API兼容性、交易约束和奖励函数
"""

import os
import sys
import pandas as pd
import numpy as np
import logging
import traceback
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import gymnasium as gym
from gymnasium.utils import env_checker

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'logs/trading_env_test_{datetime.now().strftime("%Y-%m-%d")}.log')
    ]
)

# 确保logs目录存在
os.makedirs('logs', exist_ok=True)

# 添加项目路径到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入相关模块
from quant_trading.data.data_handler import DataHandler
from quant_trading.features.feature_engineer import FeatureEngineer
from quant_trading.trading.trading_environment import TradingEnvironment
from quant_trading.utils.common import load_config

def test_env_api_compatibility():
    """测试交易环境API兼容性"""
    print("\n===== 测试交易环境API兼容性 =====")
    
    # 获取测试数据
    handler = DataHandler(cache_dir='data_cache')
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=120)).strftime('%Y-%m-%d')
    stock = 'sh000001'
    
    try:
        # 获取原始数据
        data = handler.get_stock_data(stock, start_date, end_date)
        
        if data is None or data.empty:
            print("获取测试数据失败，无法进行环境测试")
            return
        
        # 加载环境配置
        env_config = load_config("quant_project/configs/env_config.yaml")
        
        # 生成特征
        feature_engineer = FeatureEngineer(env_config.get('feature_config', {}))
        processed_data = feature_engineer.generate_features(data)
        
        # 创建交易环境
        env_params = {
            'df_processed_data': processed_data,
            'initial_capital': env_config.get('initial_capital', 100000),
            'commission_rate': env_config.get('commission_rate', 0.0003),
            'min_hold_days': env_config.get('min_hold_days', 3),
            'allow_short': env_config.get('allow_short', False),
            'max_position': env_config.get('max_position', 1.0),
            'window_size': env_config.get('window_size', 20),
            'reward_config': env_config.get('reward_config', {})
        }
        
        env = TradingEnvironment(**env_params)
        
        # 使用Gymnasium的环境检查器验证环境
        print("\n使用Gymnasium环境检查器验证环境...")
        try:
            env_checker.check_env(env)
            print("验证通过: 环境符合Gymnasium API规范")
        except Exception as e:
            print(f"验证失败: {str(e)}")
            print(traceback.format_exc())
        
        # 测试核心方法
        print("\n测试环境核心方法...")
        
        # 测试reset方法
        print("测试reset方法...")
        observation, info = env.reset()
        print(f"观测空间形状: {observation.shape}")
        print(f"信息字典: {info.keys()}")
        
        # 测试step方法
        print("\n测试step方法...")
        action = 1  # 买入动作
        observation, reward, terminated, truncated, info = env.step(action)
        print(f"执行动作 {action} 后的奖励: {reward}")
        print(f"执行动作 {action} 后的信息: {info}")
        
        # 测试render方法
        print("\n测试render方法...")
        try:
            render_result = env.render()
            print("渲染成功")
        except Exception as e:
            print(f"渲染失败: {str(e)}")
        
        # 测试close方法
        print("\n测试close方法...")
        env.close()
        print("环境关闭成功")
        
    except Exception as e:
        print(f"测试环境API兼容性时出错: {str(e)}")
        print(traceback.format_exc())

def test_trading_constraints():
    """测试交易约束"""
    print("\n===== 测试交易约束 =====")
    
    # 获取测试数据
    handler = DataHandler(cache_dir='data_cache')
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=120)).strftime('%Y-%m-%d')
    stock = 'sh000001'
    
    try:
        # 获取原始数据
        data = handler.get_stock_data(stock, start_date, end_date)
        
        if data is None or data.empty:
            print("获取测试数据失败，无法进行环境测试")
            return
        
        # 加载环境配置
        env_config = load_config("quant_project/configs/env_config.yaml")
        
        # 生成特征
        feature_engineer = FeatureEngineer(env_config.get('feature_config', {}))
        processed_data = feature_engineer.generate_features(data)
        
        # 创建交易环境
        env_params = {
            'df_processed_data': processed_data,
            'initial_capital': env_config.get('initial_capital', 100000),
            'commission_rate': env_config.get('commission_rate', 0.0003),
            'min_hold_days': env_config.get('min_hold_days', 3),
            'allow_short': env_config.get('allow_short', False),
            'max_position': env_config.get('max_position', 1.0),
            'window_size': env_config.get('window_size', 20),
            'reward_config': env_config.get('reward_config', {})
        }
        
        env = TradingEnvironment(**env_params)
        
        # 测试最小持仓天数约束
        print("\n测试最小持仓天数约束...")
        observation, info = env.reset()
        
        # 执行买入操作
        action = 1  # 买入
        observation, reward, terminated, truncated, info = env.step(action)
        print(f"执行买入操作，持仓: {info.get('position', 0)}")
        
        # 尝试立即卖出（应该被约束）
        action = 2  # 卖出
        observation, reward, terminated, truncated, info = env.step(action)
        print(f"尝试立即卖出，持仓: {info.get('position', 0)}")
        
        # 等待几天
        for i in range(env_params['min_hold_days']):
            action = 0  # 持有
            observation, reward, terminated, truncated, info = env.step(action)
            print(f"等待第 {i+1} 天，持仓: {info.get('position', 0)}")
        
        # 现在应该可以卖出
        action = 2  # 卖出
        observation, reward, terminated, truncated, info = env.step(action)
        print(f"最小持仓天数后卖出，持仓: {info.get('position', 0)}")
        
        # 测试最大仓位约束
        print("\n测试最大仓位约束...")
        env.reset()
        
        # 设置最大仓位为0.5
        env.max_position = 0.5
        
        # 执行买入操作
        action = 1  # 买入
        observation, reward, terminated, truncated, info = env.step(action)
        print(f"最大仓位为0.5时买入，持仓: {info.get('position', 0)}")
        
        # 尝试再次买入（应该被约束）
        action = 1  # 买入
        observation, reward, terminated, truncated, info = env.step(action)
        print(f"尝试再次买入，持仓: {info.get('position', 0)}")
        
    except Exception as e:
        print(f"测试交易约束时出错: {str(e)}")
        print(traceback.format_exc())

def test_reward_function():
    """测试奖励函数"""
    print("\n===== 测试奖励函数 =====")
    
    # 获取测试数据
    handler = DataHandler(cache_dir='data_cache')
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=120)).strftime('%Y-%m-%d')
    stock = 'sh000001'
    
    try:
        # 获取原始数据
        data = handler.get_stock_data(stock, start_date, end_date)
        
        if data is None or data.empty:
            print("获取测试数据失败，无法进行环境测试")
            return
        
        # 加载环境配置
        env_config = load_config("quant_project/configs/env_config.yaml")
        
        # 生成特征
        feature_engineer = FeatureEngineer(env_config.get('feature_config', {}))
        processed_data = feature_engineer.generate_features(data)
        
        # 创建交易环境
        env_params = {
            'df_processed_data': processed_data,
            'initial_capital': env_config.get('initial_capital', 100000),
            'commission_rate': env_config.get('commission_rate', 0.0003),
            'min_hold_days': env_config.get('min_hold_days', 3),
            'allow_short': env_config.get('allow_short', False),
            'max_position': env_config.get('max_position', 1.0),
            'window_size': env_config.get('window_size', 20),
            'reward_config': env_config.get('reward_config', {})
        }
        
        # 测试不同奖励配置
        reward_configs = [
            # 默认配置
            env_config.get('reward_config', {}),
            # 只关注收益
            {'portfolio_return': 1.0, 'volatility_penalty': 0.0, 'drawdown_penalty': 0.0, 'holding_penalty': 0.0, 'trade_penalty': 0.0},
            # 关注风险
            {'portfolio_return': 0.5, 'volatility_penalty': 0.3, 'drawdown_penalty': 0.2, 'holding_penalty': 0.0, 'trade_penalty': 0.0},
            # 关注交易成本
            {'portfolio_return': 0.7, 'volatility_penalty': 0.0, 'drawdown_penalty': 0.0, 'holding_penalty': 0.1, 'trade_penalty': 0.2}
        ]
        
        for i, reward_config in enumerate(reward_configs):
            print(f"\n测试奖励配置 {i+1}: {reward_config}")
            
            env_params['reward_config'] = reward_config
            env = TradingEnvironment(**env_params)
            
            # 执行简单的交易序列
            observation, info = env.reset()
            
            # 买入
            action = 1
            observation, reward, terminated, truncated, info = env.step(action)
            print(f"买入操作的奖励: {reward}")
            
            # 持有几天
            hold_rewards = []
            for i in range(5):
                action = 0
                observation, reward, terminated, truncated, info = env.step(action)
                hold_rewards.append(reward)
            
            print(f"持有操作的奖励: {hold_rewards}")
            
            # 卖出
            action = 2
            observation, reward, terminated, truncated, info = env.step(action)
            print(f"卖出操作的奖励: {reward}")
            
    except Exception as e:
        print(f"测试奖励函数时出错: {str(e)}")
        print(traceback.format_exc())

def run_all_tests():
    """运行所有交易环境测试"""
    try:
        test_env_api_compatibility()
        test_trading_constraints()
        test_reward_function()
        print("\n===== 所有交易环境测试完成 =====")
        return True
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        print(traceback.format_exc())
        return False

if __name__ == '__main__':
    run_all_tests()
