"""
增强特征工程模块
实现高级特征工程，包括因子分析、特征选择和特征重要性评估
"""

import logging
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler, PowerTransformer
from sklearn.decomposition import PCA
from sklearn.feature_selection import SelectKBest, f_regression, mutual_info_regression, RFE
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import Lasso, Ridge
import warnings

from .base import FeatureEngineer
from .price_features import PriceFeatureCalculator
from .technical_indicators import TechnicalIndicatorCalculator
from .statistical_features import StatisticalFeatureCalculator
from .time_features import TimeFeatureCalculator
from .utils import normalize_features, select_features, check_stationarity

# 忽略特定警告
warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", category=RuntimeWarning)

class EnhancedFeatureEngineer(FeatureEngineer):
    """
    增强特征工程类
    实现高级特征工程，包括因子分析、特征选择和特征重要性评估
    严格避免前视偏差和数据泄露，确保特征的可解释性和预测能力
    """

    def __init__(self, feature_config=None, avoid_lookahead=True, check_stationarity=True,
                 feature_selection_method='combined', normalization_method='robust',
                 rolling_window_type='expanding', max_features=50):
        """
        初始化特征工程器

        参数:
            feature_config (dict): 特征配置字典，指定要计算的技术指标及其参数
            avoid_lookahead (bool): 是否严格避免前视偏差
            check_stationarity (bool): 是否检查特征平稳性
            feature_selection_method (str): 特征选择方法，可选'filter'(过滤法),'wrapper'(包裹法),'embedded'(嵌入法),'combined'(组合法)
            normalization_method (str): 归一化方法，可选'minmax','standard','robust','yeo-johnson'
            rolling_window_type (str): 滚动窗口类型，可选'fixed'(固定窗口),'expanding'(扩展窗口)
            max_features (int): 最大特征数量
        """
        super(EnhancedFeatureEngineer, self).__init__(feature_config)
        
        self.avoid_lookahead = avoid_lookahead
        self.check_stationarity = check_stationarity
        self.feature_selection_method = feature_selection_method
        self.normalization_method = normalization_method
        self.rolling_window_type = rolling_window_type
        self.max_features = max_features
        
        # 特征相关属性
        self.feature_importance = {}
        self.selected_features = []
        self.feature_stationarity = {}
        self.scaler = None
        self.pca_model = None
        
        # 特征组分类
        self.feature_groups = {
            'price': [],           # 价格相关特征
            'volume': [],          # 成交量相关特征
            'momentum': [],        # 动量类特征
            'volatility': [],      # 波动率类特征
            'trend': [],           # 趋势类特征
            'oscillator': [],      # 震荡指标类特征
            'pattern': [],         # 形态类特征
            'fundamental': [],     # 基本面特征
            'cross_asset': [],     # 跨资产特征
            'time': [],            # 时间特征
            'statistical': []      # 统计特征
        }
        
        # 初始化特征计算器
        self.price_calculator = PriceFeatureCalculator(logger=self.logger)
        self.technical_calculator = TechnicalIndicatorCalculator(
            config=self.feature_config, logger=self.logger)
        self.statistical_calculator = StatisticalFeatureCalculator(
            config=self.feature_config.get('rolling_stats', {}), logger=self.logger)
        self.time_calculator = TimeFeatureCalculator(logger=self.logger)

    def generate_features(self, data, apply_selection=False):
        """
        生成特征

        参数:
            data (pandas.DataFrame): 原始行情数据，必须包含OHLCV列
            apply_selection (bool): 是否应用特征选择

        返回:
            pandas.DataFrame: 添加了特征的数据
        """
        # 复制数据，避免修改原始数据
        df = data.copy()

        # 确保列名标准化
        df = self._ensure_column_names(df)

        # 重置特征组
        for group in self.feature_groups:
            self.feature_groups[group] = []

        # 计算基本价格特征
        self.logger.info("计算基本价格特征...")
        df, price_features = self.price_calculator.calculate(df)
        self.feature_names.extend(price_features)

        # 将价格特征添加到价格组
        price_features_group = [col for col in price_features if '价格' in col or '涨跌幅' in col or '波动率' in col]
        self.feature_groups['price'].extend(price_features_group)

        # 根据配置计算技术指标
        self.logger.info("计算技术指标...")
        df, tech_features = self.technical_calculator.calculate(df)
        self.feature_names.extend(tech_features)

        # 将技术指标分类到不同组
        for feature in tech_features:
            if 'SMA' in feature or 'EMA' in feature or 'MACD' in feature or 'BBands' in feature:
                self.feature_groups['trend'].append(feature)
            elif 'RSI' in feature or 'Stoch' in feature:
                self.feature_groups['oscillator'].append(feature)
            elif 'ATR' in feature or 'Volatility' in feature:
                self.feature_groups['volatility'].append(feature)
            elif 'Volume' in feature or 'OBV' in feature or 'VWAP' in feature:
                self.feature_groups['volume'].append(feature)
            elif '动量' in feature or 'Momentum' in feature:
                self.feature_groups['momentum'].append(feature)

        # 计算统计特征
        self.logger.info("计算统计特征...")
        df, stat_features = self.statistical_calculator.calculate(df)
        self.feature_names.extend(stat_features)

        # 将统计特征添加到统计组
        stat_features_group = [col for col in stat_features if 'Rolling' in col or 'ZScore' in col or 'Autocorr' in col]
        self.feature_groups['statistical'].extend(stat_features_group)

        # 计算时间特征
        if self.feature_config.get('time_features', {}).get('use', True):
            self.logger.info("计算时间特征...")
            df, time_features = self.time_calculator.calculate(df)
            self.feature_names.extend(time_features)
            
            # 将时间特征添加到时间组
            time_features_group = [col for col in time_features if '星期' in col or '月' in col or '季' in col or '年' in col or '周期' in col]
            self.feature_groups['time'].extend(time_features_group)

        # 计算高级特征
        if self.feature_config.get('advanced_features', {}).get('use', True):
            self.logger.info("计算高级特征...")
            df = self._calculate_advanced_features(df)

        # 检查特征平稳性并进行必要的转换
        if self.check_stationarity:
            self.logger.info("检查特征平稳性...")
            self.feature_stationarity = check_stationarity(df, columns=self.feature_names, logger=self.logger)
            
            # 记录平稳性检查结果
            stationary_count = sum(1 for result in self.feature_stationarity.values() if result.get('stationary', False))
            non_stationary_count = len(self.feature_stationarity) - stationary_count
            self.logger.info(f"平稳性检查结果: {stationary_count} 个平稳特征, {non_stationary_count} 个非平稳特征")

        # 特征选择（如果启用）
        if apply_selection and self.feature_config.get('feature_selection', {}).get('use', False):
            self.logger.info("应用特征选择...")
            df = self._apply_feature_selection(df)

        # 归一化特征
        self.logger.info("归一化特征...")
        df = normalize_features(df, method=self.normalization_method, columns=self.feature_names, logger=self.logger)

        # 处理NaN值和无穷大值
        df = self._handle_missing_values(df)

        self.logger.info(f"特征生成完成，共 {len(self.feature_names)} 个特征")
        return df

    def _calculate_advanced_features(self, df):
        """
        计算高级特征
        
        参数:
            df (pandas.DataFrame): 原始数据
            
        返回:
            pandas.DataFrame: 添加了高级特征的数据
        """
        # 这里可以实现更复杂的高级特征计算
        # 例如Alpha因子、市场微观结构特征等
        
        # 示例：计算Alpha因子
        if self.feature_config.get('alpha_factors', {}).get('use', True):
            try:
                # Alpha1: (收盘价-开盘价)/振幅
                df['Alpha1'] = (df['收盘'] - df['开盘']) / ((df['最高'] - df['最低']) + 1e-10)
                self.feature_names.append('Alpha1')
                self.feature_groups['fundamental'].append('Alpha1')
                
                # Alpha2: 收盘价相对于过去10天的排名
                window = 10
                df['Alpha2'] = df['收盘'].rolling(window).apply(
                    lambda x: pd.Series(x).rank().iloc[-1] / window, raw=False)
                self.feature_names.append('Alpha2')
                self.feature_groups['fundamental'].append('Alpha2')
                
                # Alpha3: 短期动量相对于长期动量
                df['Alpha3'] = df['收盘'].pct_change(5) - df['收盘'].pct_change(20)
                self.feature_names.append('Alpha3')
                self.feature_groups['fundamental'].append('Alpha3')
            except Exception as e:
                self.logger.error(f"计算Alpha因子时出错: {str(e)}")
        
        return df

    def _apply_feature_selection(self, df):
        """
        应用特征选择
        
        参数:
            df (pandas.DataFrame): 包含特征的数据框
            
        返回:
            pandas.DataFrame: 只包含选定特征的数据框
        """
        # 使用涨跌幅作为目标变量
        target_col = '涨跌幅'
        
        # 应用特征选择
        df_selected, feature_importance = select_features(
            df, target_col, n_features=self.max_features, 
            method=self.feature_selection_method, logger=self.logger)
        
        # 保存特征重要性
        self.feature_importance = feature_importance
        
        # 更新选定的特征
        self.selected_features = [col for col in df_selected.columns 
                                 if col not in ['开盘', '最高', '最低', '收盘', '成交量', target_col]]
        
        # 更新特征名称列表
        self.feature_names = self.selected_features
        
        return df_selected
