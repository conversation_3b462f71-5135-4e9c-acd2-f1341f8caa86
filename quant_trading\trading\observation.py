"""
观察处理模块
负责处理交易环境的观察空间和观察向量
"""

import logging
import numpy as np
import pandas as pd
from gymnasium import spaces
from enum import Enum

class Observation:
    """观察类，用于表示交易环境的观察"""

    def __init__(self, features=None, account_state=None):
        """
        初始化观察对象

        参数:
            features (numpy.ndarray): 特征数据
            account_state (numpy.ndarray): 账户状态数据
        """
        self.features = features if features is not None else np.array([])
        self.account_state = account_state if account_state is not None else np.array([])

    def to_array(self):
        """
        将观察转换为数组

        返回:
            numpy.ndarray: 观察数组
        """
        return np.concatenate([self.features, self.account_state])

class ObservationHandler:
    """
    观察处理类
    负责处理交易环境的观察空间和观察向量
    """

    def __init__(self, df, window_size=20, logger=None):
        """
        初始化观察处理器

        参数:
            df (pandas.DataFrame): 处理好的行情数据和特征
            window_size (int): 观测窗口大小
            logger (logging.Logger): 日志记录器
        """
        self.logger = logger or logging.getLogger('drl_trading')
        self.df = df
        self.window_size = window_size

        # 计算特征数量
        self.feature_columns = [col for col in self.df.columns if col not in ['开盘', '最高', '最低', '收盘', '成交量']]
        self.n_features = len(self.feature_columns)
        self.logger.info(f"初始化观察处理器，特征数量: {self.n_features}")

        # 计算观察空间维度
        self.obs_dim = self.window_size * self.n_features + 4  # 4个账户状态特征
        self.logger.info(f"观察空间维度: {self.obs_dim}")

        # 创建观察空间
        self.observation_space = spaces.Box(
            low=-10.0,
            high=10.0,
            shape=(self.obs_dim,),
            dtype=np.float32
        )

    def get_observation(self, current_step, cash, shares_held, current_price, cost_basis, holding_days):
        """
        获取当前观测

        参数:
            current_step (int): 当前时间步
            cash (float): 当前现金
            shares_held (int): 当前持有股票数量
            current_price (float): 当前股票价格
            cost_basis (float): 买入成本
            holding_days (int): 持仓天数

        返回:
            numpy.ndarray: 观测向量
        """
        # 获取窗口内的特征数据
        start_idx = current_step - self.window_size + 1
        end_idx = current_step + 1

        # 获取特征窗口
        try:
            # 尝试获取特征窗口
            features_window = self.df[self.feature_columns].iloc[start_idx:end_idx].values

            # 检查特征窗口的形状
            if features_window.shape[0] != self.window_size:
                self.logger.warning(f"特征窗口大小 ({features_window.shape[0]}) 与预期 ({self.window_size}) 不一致，将调整")
                # 如果窗口大小不一致，调整为正确的大小
                if features_window.shape[0] < self.window_size:
                    # 如果窗口太小，用0填充
                    padding = np.zeros((self.window_size - features_window.shape[0], features_window.shape[1]))
                    features_window = np.vstack([padding, features_window])
                else:
                    # 如果窗口太大，截取最近的window_size个
                    features_window = features_window[-self.window_size:]

            # 展平特征窗口
            features_window = features_window.flatten()

            # 检查特征窗口的维度
            expected_dim = self.window_size * len(self.feature_columns)
            if len(features_window) != expected_dim:
                self.logger.warning(f"特征窗口维度 ({len(features_window)}) 与预期 ({expected_dim}) 不一致，将调整")
                # 如果维度不一致，调整为正确的维度
                if len(features_window) < expected_dim:
                    # 如果维度太小，用0填充
                    features_window = np.pad(features_window, (0, expected_dim - len(features_window)))
                else:
                    # 如果维度太大，截取前expected_dim个
                    features_window = features_window[:expected_dim]
        except Exception as e:
            self.logger.error(f"获取特征窗口时出错: {str(e)}")
            # 如果出错，创建一个全零的特征窗口
            features_window = np.zeros(self.window_size * self.n_features)

        # 计算账户状态特征
        portfolio_value = cash + shares_held * current_price
        cash_ratio = cash / portfolio_value if portfolio_value > 0 else 0
        position_ratio = (shares_held * current_price) / portfolio_value if portfolio_value > 0 else 0
        unrealized_pnl_ratio = 0
        if shares_held > 0 and cost_basis > 0:
            unrealized_pnl_ratio = ((current_price - cost_basis) / cost_basis)

        # 归一化持仓天数
        normalized_holding_days = holding_days / 30  # 假设最长持仓30天

        # 组合账户状态
        account_state = np.array([
            cash_ratio,
            position_ratio,
            normalized_holding_days,
            unrealized_pnl_ratio
        ], dtype=np.float32)

        # 确保特征窗口和账户状态的数据类型一致
        features_window = features_window.astype(np.float32)

        # 组合观测向量
        observation = np.concatenate([features_window, account_state])

        # 确保观测值在观测空间的范围内
        observation = np.clip(observation, -10.0, 10.0)

        # 确保观测向量的维度与观测空间一致
        expected_obs_dim = self.observation_space.shape[0]

        # 检查观测向量维度是否与观测空间一致
        if len(observation) != expected_obs_dim:
            # 记录详细的维度信息，帮助调试
            self.logger.warning(
                f"观测向量维度 ({len(observation)}) 与观测空间 ({expected_obs_dim}) 不一致，将调整\n"
                f"特征窗口维度: {len(features_window)}, 账户状态维度: {len(account_state)}\n"
                f"特征列数量: {len(self.feature_columns)}, 窗口大小: {self.window_size}"
            )

            # 如果维度不一致，尝试调整为正确的维度
            if len(observation) < expected_obs_dim:
                # 如果维度太小，用0填充
                padding_size = expected_obs_dim - len(observation)
                self.logger.info(f"观测向量维度太小，添加 {padding_size} 个零填充")
                observation = np.pad(observation, (0, padding_size))
            else:
                # 如果维度太大，截取前expected_obs_dim个
                self.logger.info(f"观测向量维度太大，截取前 {expected_obs_dim} 个元素")
                observation = observation[:expected_obs_dim]

            # 再次检查维度是否正确
            if len(observation) != expected_obs_dim:
                self.logger.error(f"调整后观测向量维度 ({len(observation)}) 仍与观测空间 ({expected_obs_dim}) 不一致")
                # 如果仍然不一致，创建一个全零的观测向量
                observation = np.zeros(expected_obs_dim, dtype=np.float32)

        return observation.astype(np.float32)

    def update_observation_space(self, new_n_features=None):
        """
        更新观察空间以适应新的特征数量

        参数:
            new_n_features (int): 新的特征数量，如果为None则使用当前特征数量
        """
        if new_n_features is not None:
            # 保存原始特征数量
            old_n_features = self.n_features

            # 更新特征数量
            self.n_features = new_n_features
            self.logger.info(f"特征数量从 {old_n_features} 变为 {new_n_features}")

        # 计算新的观察空间维度
        new_obs_dim = self.window_size * self.n_features + 4

        # 记录变化
        self.logger.info(f"观察空间维度从 {self.obs_dim} 变为 {new_obs_dim}")

        # 更新观察空间
        self.observation_space = spaces.Box(
            low=-10.0,
            high=10.0,
            shape=(new_obs_dim,),
            dtype=np.float32
        )

        # 更新观察空间维度
        self.obs_dim = new_obs_dim

    def force_observation_space(self, target_dim):
        """
        强制设置观察空间维度为指定值

        参数:
            target_dim (int): 目标观察空间维度
        """
        # 记录变化
        self.logger.warning(f"强制设置观察空间维度: {self.obs_dim} -> {target_dim}")

        # 计算所需的特征数量
        required_features = (target_dim - 4) // self.window_size

        # 更新特征数量
        self.n_features = required_features

        # 更新观察空间
        self.observation_space = spaces.Box(
            low=-10.0,
            high=10.0,
            shape=(target_dim,),
            dtype=np.float32
        )

        # 更新观察空间维度
        self.obs_dim = target_dim

        self.logger.info(f"观察空间已强制设置为 {target_dim}，对应特征数量: {required_features}")

    def adjust_observation_space(self, target_dim):
        """
        调整观察空间维度以匹配目标维度

        这个方法用于解决模型加载时观察空间维度不匹配的问题。
        它会调整环境的观察空间维度，使其与加载的模型匹配。

        参数:
            target_dim (int): 目标观察空间维度
        """
        # 记录当前维度和目标维度
        current_dim = self.observation_space.shape[0]
        self.logger.warning(f"调整观察空间维度: {current_dim} -> {target_dim}")

        if current_dim == target_dim:
            self.logger.info("观察空间维度已经匹配，无需调整")
            return

        # 使用force_observation_space方法强制设置观察空间维度
        self.force_observation_space(target_dim)

        # 记录调整后的信息
        self.logger.info(f"观察空间已调整为 {target_dim} 维度")
        self.logger.info(f"特征数量: {self.n_features}, 窗口大小: {self.window_size}, 账户状态: 4")
