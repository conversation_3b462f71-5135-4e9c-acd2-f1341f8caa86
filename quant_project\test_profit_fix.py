"""
测试profit修复脚本
用于测试修复后的性能分析器
"""

import logging
import pandas as pd
import numpy as np
from core_logic.performance_analyzer import PerformanceAnalyzer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_profit_fix')

def test_performance_analyzer():
    """测试性能分析器修复"""
    logger.info("测试性能分析器修复")
    
    # 创建测试数据
    portfolio_values = pd.Series(
        np.linspace(100000, 120000, 100),
        index=pd.date_range(start='2020-01-01', periods=100, freq='D')
    )
    
    # 创建交易记录，但不包含profit列
    trades = []
    for i in range(10):
        buy_date = pd.Timestamp('2020-01-01') + pd.Timedelta(days=i*5)
        sell_date = buy_date + pd.Timedelta(days=3)
        trades.append({
            'symbol': 'TEST',
            'buy_date': buy_date,
            'sell_date': sell_date,
            'buy_price': 100 + i,
            'sell_price': 105 + i,
            'quantity': 100,
            'commission': 10
        })
    
    # 创建性能分析器
    analyzer = PerformanceAnalyzer()
    
    try:
        # 分析性能
        metrics = analyzer.analyze(trades, portfolio_values)
        logger.info(f"性能分析成功，指标: {metrics}")
        return True
    except Exception as e:
        logger.error(f"性能分析失败: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("开始测试修复")
    
    # 测试性能分析器
    pa_result = test_performance_analyzer()
    logger.info(f"性能分析器测试结果: {'成功' if pa_result else '失败'}")
    
    logger.info("测试完成")
