"""
测试交易环境模块
"""

import os
import sys
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('test_trading_env')

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入原始交易环境类和重构后的交易环境类
from core_logic.trading_environment import TradingEnvironment as OldTradingEnvironment
from core_logic.trading_env.trading_environment import TradingEnvironment as NewTradingEnvironment
from core_logic.trading_env.adapter import TradingEnvironmentAdapter

def create_test_data(n_samples=100):
    """
    创建测试数据
    
    参数:
        n_samples (int): 样本数量
        
    返回:
        pandas.DataFrame: 测试数据
    """
    # 创建日期索引
    dates = pd.date_range(start='2020-01-01', periods=n_samples, freq='D')
    
    # 创建价格数据
    np.random.seed(42)
    close = np.random.normal(loc=100, scale=10, size=n_samples).cumsum()
    open_price = close * np.random.normal(loc=1, scale=0.01, size=n_samples)
    high = np.maximum(close, open_price) * np.random.normal(loc=1.02, scale=0.01, size=n_samples)
    low = np.minimum(close, open_price) * np.random.normal(loc=0.98, scale=0.01, size=n_samples)
    volume = np.random.normal(loc=1000000, scale=200000, size=n_samples)
    
    # 创建特征数据
    features = {}
    for i in range(10):
        features[f'feature_{i}'] = np.random.normal(size=n_samples)
    
    # 创建数据框
    df = pd.DataFrame({
        '开盘': open_price,
        '最高': high,
        '最低': low,
        '收盘': close,
        '成交量': volume,
        **features
    }, index=dates)
    
    return df

def test_trading_env():
    """
    测试交易环境模块
    """
    logger.info("创建测试数据...")
    df = create_test_data(n_samples=100)
    
    # 测试原始交易环境类
    logger.info("测试原始交易环境类...")
    try:
        old_env = OldTradingEnvironment(
            df_processed_data=df,
            initial_capital=100000,
            commission_rate=0.0003,
            min_hold_days=3,
            window_size=10
        )
        
        # 重置环境
        obs, info = old_env.reset()
        logger.info(f"原始交易环境类重置成功，观测维度: {obs.shape}")
        
        # 执行一些动作
        for _ in range(10):
            action = np.random.randint(0, 3)  # 随机选择动作
            obs, reward, terminated, truncated, info = old_env.step(action)
            logger.info(f"原始交易环境类执行动作 {action}，奖励: {reward:.4f}")
            
            if terminated:
                break
        
        logger.info(f"原始交易环境类测试完成，最终组合价值: {info['portfolio_value']:.2f}")
    except Exception as e:
        logger.error(f"测试原始交易环境类失败: {str(e)}")
        old_env = None
    
    # 测试新的交易环境类
    logger.info("测试新的交易环境类...")
    try:
        new_env = NewTradingEnvironment(
            df_processed_data=df,
            initial_capital=100000,
            commission_rate=0.0003,
            min_hold_days=3,
            window_size=10
        )
        
        # 重置环境
        obs, info = new_env.reset()
        logger.info(f"新的交易环境类重置成功，观测维度: {obs.shape}")
        
        # 执行一些动作
        for _ in range(10):
            action = np.random.randint(0, 3)  # 随机选择动作
            obs, reward, terminated, truncated, info = new_env.step(action)
            logger.info(f"新的交易环境类执行动作 {action}，奖励: {reward:.4f}")
            
            if terminated:
                break
        
        logger.info(f"新的交易环境类测试完成，最终组合价值: {info['portfolio_value']:.2f}")
        
        # 测试性能指标
        metrics = new_env.get_performance_metrics()
        logger.info(f"性能指标: {metrics}")
        
        # 测试保存性能图表
        new_env.save_performance_plot('test_performance.png')
        logger.info("性能图表已保存")
    except Exception as e:
        logger.error(f"测试新的交易环境类失败: {str(e)}")
        new_env = None
    
    # 测试适配器
    logger.info("测试交易环境适配器...")
    try:
        adapter_env = TradingEnvironmentAdapter(
            df_processed_data=df,
            initial_capital=100000,
            commission_rate=0.0003,
            min_hold_days=3,
            window_size=10
        )
        
        # 重置环境
        obs, info = adapter_env.reset()
        logger.info(f"交易环境适配器重置成功，观测维度: {obs.shape}")
        
        # 执行一些动作
        for _ in range(10):
            action = np.random.randint(0, 3)  # 随机选择动作
            obs, reward, terminated, truncated, info = adapter_env.step(action)
            logger.info(f"交易环境适配器执行动作 {action}，奖励: {reward:.4f}")
            
            if terminated:
                break
        
        logger.info(f"交易环境适配器测试完成，最终组合价值: {info['portfolio_value']:.2f}")
    except Exception as e:
        logger.error(f"测试交易环境适配器失败: {str(e)}")
        adapter_env = None
    
    # 比较结果
    logger.info("比较结果...")
    
    if old_env is not None and new_env is not None:
        # 比较观测空间
        old_obs_shape = old_env.observation_space.shape
        new_obs_shape = new_env.observation_space.shape
        logger.info(f"原始交易环境类观测空间: {old_obs_shape}, 新的交易环境类观测空间: {new_obs_shape}")
        
        # 比较动作空间
        old_action_space = old_env.action_space
        new_action_space = new_env.action_space
        logger.info(f"原始交易环境类动作空间: {old_action_space}, 新的交易环境类动作空间: {new_action_space}")
    
    # 输出结果摘要
    logger.info("测试结果摘要:")
    logger.info(f"原始交易环境类: {'成功' if old_env is not None else '失败'}")
    logger.info(f"新的交易环境类: {'成功' if new_env is not None else '失败'}")
    logger.info(f"交易环境适配器: {'成功' if adapter_env is not None else '失败'}")

if __name__ == "__main__":
    test_trading_env()
