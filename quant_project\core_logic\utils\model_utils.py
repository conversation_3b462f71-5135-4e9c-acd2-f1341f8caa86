"""
模型工具模块
提供模型训练、评估和优化相关的工具函数
"""

import os
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
from typing import Dict, List, Union, Tuple, Optional, Callable, Any
import matplotlib.pyplot as plt
import logging
from datetime import datetime
import json

logger = logging.getLogger('drl_trading')

class ModelTrainer:
    """模型训练和评估工具类"""
    
    def __init__(self, 
                model: nn.Module, 
                optimizer: optim.Optimizer,
                criterion: Callable,
                device: torch.device = None,
                scheduler: Optional[Any] = None,
                checkpoint_dir: str = './checkpoints',
                experiment_name: str = None):
        """
        初始化模型训练器
        
        参数:
            model: 神经网络模型
            optimizer: 优化器
            criterion: 损失函数
            device: 训练设备 (GPU/CPU)
            scheduler: 学习率调度器 (可选)
            checkpoint_dir: 检查点保存目录
            experiment_name: 实验名称 (用于保存检查点)
        """
        self.model = model
        self.optimizer = optimizer
        self.criterion = criterion
        self.scheduler = scheduler
        
        # 设置设备
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = device
            
        # 将模型移动到设备
        self.model.to(self.device)
        
        # 设置检查点目录
        self.checkpoint_dir = checkpoint_dir
        if not os.path.exists(checkpoint_dir):
            os.makedirs(checkpoint_dir)
            
        # 设置实验名称
        if experiment_name is None:
            self.experiment_name = datetime.now().strftime("%Y%m%d_%H%M%S")
        else:
            self.experiment_name = experiment_name
            
        # 初始化训练记录
        self.train_losses = []
        self.val_losses = []
        self.metrics_history = {}
        self.best_val_loss = float('inf')
        self.best_val_metric = float('-inf')
        self.current_epoch = 0
        self.early_stop_counter = 0
        
    def train_epoch(self, train_loader: DataLoader) -> float:
        """
        训练一个完整的epoch
        
        参数:
            train_loader: 训练数据加载器
            
        返回:
            平均训练损失
        """
        self.model.train()
        epoch_loss = 0.0
        
        for batch_idx, (inputs, targets) in enumerate(train_loader):
            # 数据移动到设备
            inputs = inputs.to(self.device)
            targets = targets.to(self.device)
            
            # 梯度清零
            self.optimizer.zero_grad()
            
            # 前向传播
            outputs = self.model(inputs)
            
            # 计算损失
            loss = self.criterion(outputs, targets)
            
            # 反向传播
            loss.backward()
            
            # 梯度裁剪，防止梯度爆炸
            nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            # 参数更新
            self.optimizer.step()
            
            # 累计损失
            epoch_loss += loss.item()
            
        # 学习率调度器更新
        if self.scheduler is not None:
            if isinstance(self.scheduler, optim.lr_scheduler.ReduceLROnPlateau):
                pass  # 这种调度器在验证阶段更新
            else:
                self.scheduler.step()
                
        # 计算平均损失
        avg_loss = epoch_loss / len(train_loader)
        self.train_losses.append(avg_loss)
        
        return avg_loss
    
    def validate(self, val_loader: DataLoader, metrics: Dict[str, Callable] = None) -> Tuple[float, Dict[str, float]]:
        """
        在验证集上评估模型
        
        参数:
            val_loader: 验证数据加载器
            metrics: 评估指标字典 {指标名称: 指标计算函数}
            
        返回:
            (平均验证损失, 指标字典)
        """
        self.model.eval()
        val_loss = 0.0
        all_outputs = []
        all_targets = []
        
        with torch.no_grad():
            for inputs, targets in val_loader:
                # 数据移动到设备
                inputs = inputs.to(self.device)
                targets = targets.to(self.device)
                
                # 前向传播
                outputs = self.model(inputs)
                
                # 计算损失
                loss = self.criterion(outputs, targets)
                
                # 累计损失
                val_loss += loss.item()
                
                # 收集预测和目标，用于计算指标
                all_outputs.append(outputs.cpu())
                all_targets.append(targets.cpu())
                
        # 计算平均损失
        avg_loss = val_loss / len(val_loader)
        self.val_losses.append(avg_loss)
        
        # 学习率调度器更新
        if self.scheduler is not None and isinstance(self.scheduler, optim.lr_scheduler.ReduceLROnPlateau):
            self.scheduler.step(avg_loss)
        
        # 计算评估指标
        metrics_values = {}
        if metrics is not None:
            # 合并所有批次的预测和目标
            outputs_tensor = torch.cat(all_outputs)
            targets_tensor = torch.cat(all_targets)
            
            for metric_name, metric_fn in metrics.items():
                metric_value = metric_fn(outputs_tensor, targets_tensor)
                metrics_values[metric_name] = metric_value
                
                # 记录指标历史
                if metric_name not in self.metrics_history:
                    self.metrics_history[metric_name] = []
                self.metrics_history[metric_name].append(metric_value)
                
        return avg_loss, metrics_values
    
    def train(self, 
              train_loader: DataLoader, 
              val_loader: DataLoader,
              epochs: int = 100,
              metrics: Dict[str, Callable] = None,
              early_stopping: int = 10,
              save_best: bool = True,
              save_best_metric: str = None,
              verbose: bool = True,
              log_interval: int = 1) -> Dict[str, List[float]]:
        """
        完整的训练流程
        
        参数:
            train_loader: 训练数据加载器
            val_loader: 验证数据加载器
            epochs: 训练轮数
            metrics: 评估指标字典 {指标名称: 指标计算函数}
            early_stopping: 早停轮数 (验证损失不再下降)
            save_best: 是否保存最佳模型
            save_best_metric: 根据哪个指标保存最佳模型 (如不指定则根据验证损失)
            verbose: 是否打印训练过程
            log_interval: 日志记录间隔
            
        返回:
            训练历史记录
        """
        # 恢复训练状态 (如果是继续训练)
        start_epoch = self.current_epoch
        
        for epoch in range(start_epoch, start_epoch + epochs):
            self.current_epoch = epoch
            
            # 训练一个epoch
            train_loss = self.train_epoch(train_loader)
            
            # 验证
            val_loss, metrics_values = self.validate(val_loader, metrics)
            
            # 打印训练进度
            if verbose and (epoch % log_interval == 0 or epoch == epochs - 1):
                log_msg = f"Epoch {epoch+1}/{epochs} - train_loss: {train_loss:.4f}, val_loss: {val_loss:.4f}"
                for metric_name, metric_value in metrics_values.items():
                    log_msg += f", {metric_name}: {metric_value:.4f}"
                logger.info(log_msg)
                
            # 判断是否保存最佳模型
            if save_best:
                improved = False
                
                if save_best_metric is not None and metrics is not None and save_best_metric in metrics_values:
                    # 根据指定指标保存最佳模型
                    current_metric = metrics_values[save_best_metric]
                    if current_metric > self.best_val_metric:
                        self.best_val_metric = current_metric
                        improved = True
                else:
                    # 根据验证损失保存最佳模型
                    if val_loss < self.best_val_loss:
                        self.best_val_loss = val_loss
                        improved = True
                
                if improved:
                    self.early_stop_counter = 0
                    self.save_checkpoint(is_best=True)
                    if verbose:
                        logger.info(f"Model improved, saved checkpoint at epoch {epoch+1}")
                else:
                    self.early_stop_counter += 1
            
            # 定期保存检查点
            if (epoch + 1) % 10 == 0:
                self.save_checkpoint(is_best=False)
                
            # 早停检查
            if early_stopping > 0 and self.early_stop_counter >= early_stopping:
                if verbose:
                    logger.info(f"Early stopping triggered after {epoch+1} epochs")
                break
                
        # 返回训练历史
        history = {
            'train_loss': self.train_losses,
            'val_loss': self.val_losses
        }
        
        # 添加指标历史
        for metric_name, metric_values in self.metrics_history.items():
            history[metric_name] = metric_values
            
        return history
    
    def save_checkpoint(self, is_best: bool = False) -> str:
        """
        保存训练检查点
        
        参数:
            is_best: 是否为最佳模型
            
        返回:
            检查点文件路径
        """
        checkpoint = {
            'epoch': self.current_epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'metrics_history': self.metrics_history,
            'best_val_loss': self.best_val_loss,
            'best_val_metric': self.best_val_metric
        }
        
        if self.scheduler is not None:
            checkpoint['scheduler_state_dict'] = self.scheduler.state_dict()
        
        # 创建文件名
        if is_best:
            filename = os.path.join(self.checkpoint_dir, f"{self.experiment_name}_best.pth")
        else:
            filename = os.path.join(self.checkpoint_dir, f"{self.experiment_name}_epoch{self.current_epoch}.pth")
        
        # 保存检查点
        torch.save(checkpoint, filename)
        
        # 同时保存训练状态和配置为JSON（方便查看）
        config_filename = os.path.join(self.checkpoint_dir, f"{self.experiment_name}_config.json")
        
        config = {
            'epoch': self.current_epoch,
            'best_val_loss': float(self.best_val_loss),
            'best_val_metric': float(self.best_val_metric),
            'train_losses': [float(loss) for loss in self.train_losses[-10:]],  # 只保存最近10个
            'val_losses': [float(loss) for loss in self.val_losses[-10:]],
            'timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        with open(config_filename, 'w') as f:
            json.dump(config, f, indent=4)
        
        return filename
    
    def load_checkpoint(self, checkpoint_path: str) -> None:
        """
        加载训练检查点
        
        参数:
            checkpoint_path: 检查点文件路径
        """
        # 检查文件是否存在
        if not os.path.exists(checkpoint_path):
            logger.error(f"Checkpoint file not found: {checkpoint_path}")
            return
            
        # 加载检查点
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        
        # 恢复模型状态
        self.model.load_state_dict(checkpoint['model_state_dict'])
        
        # 恢复优化器状态
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        
        # 恢复学习率调度器状态
        if self.scheduler is not None and 'scheduler_state_dict' in checkpoint:
            self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
            
        # 恢复训练记录
        self.current_epoch = checkpoint['epoch']
        self.train_losses = checkpoint['train_losses']
        self.val_losses = checkpoint['val_losses']
        
        if 'metrics_history' in checkpoint:
            self.metrics_history = checkpoint['metrics_history']
            
        if 'best_val_loss' in checkpoint:
            self.best_val_loss = checkpoint['best_val_loss']
            
        if 'best_val_metric' in checkpoint:
            self.best_val_metric = checkpoint['best_val_metric']
            
        logger.info(f"Loaded checkpoint from epoch {self.current_epoch}")
    
    def find_best_lr(self, 
                   train_loader: DataLoader, 
                   start_lr: float = 1e-7,
                   end_lr: float = 1,
                   num_steps: int = 100,
                   plot: bool = True) -> Tuple[List[float], List[float]]:
        """
        学习率范围测试，找到最佳学习率
        
        参数:
            train_loader: 训练数据加载器
            start_lr: 起始学习率
            end_lr: 结束学习率
            num_steps: 测试步数
            plot: 是否绘制学习率-损失曲线
            
        返回:
            (学习率列表, 损失列表)
        """
        # 保存当前模型和优化器状态
        model_state = {k: v.clone() for k, v in self.model.state_dict().items()}
        optimizer_state = self.optimizer.state_dict()
        
        # 设置初始学习率
        for param_group in self.optimizer.param_groups:
            param_group['lr'] = start_lr
            
        # 计算每一步的学习率倍增因子
        gamma = (end_lr / start_lr) ** (1 / num_steps)
        
        # 初始化记录列表
        lr_values = []
        loss_values = []
        best_loss = float('inf')
        
        # 运行学习率测试
        self.model.train()
        for batch_idx, (inputs, targets) in enumerate(train_loader):
            if batch_idx >= num_steps:
                break
                
            # 获取当前学习率
            current_lr = self.optimizer.param_groups[0]['lr']
            lr_values.append(current_lr)
            
            # 数据移动到设备
            inputs = inputs.to(self.device)
            targets = targets.to(self.device)
            
            # 梯度清零
            self.optimizer.zero_grad()
            
            # 前向传播
            outputs = self.model(inputs)
            
            # 计算损失
            loss = self.criterion(outputs, targets)
            
            # 检查损失是否有效
            if not torch.isfinite(loss):
                logger.warning(f"Loss is not finite at lr={current_lr}. Stopping the test.")
                break
                
            # 反向传播
            loss.backward()
            
            # 更新参数
            self.optimizer.step()
            
            # 记录损失
            loss_value = loss.item()
            loss_values.append(loss_value)
            
            # 更新学习率
            for param_group in self.optimizer.param_groups:
                param_group['lr'] = current_lr * gamma
                
            # 更新最佳损失
            if loss_value < best_loss:
                best_loss = loss_value
                
            # 如果损失突然增大，提前结束测试
            if loss_value > 4 * best_loss:
                logger.info(f"Loss spiked to {loss_value} at lr={current_lr}. Stopping the test.")
                break
                
        # 恢复模型和优化器状态
        self.model.load_state_dict(model_state)
        self.optimizer.load_state_dict(optimizer_state)
        
        # 绘制学习率-损失曲线
        if plot:
            plt.figure(figsize=(10, 6))
            plt.plot(lr_values, loss_values)
            plt.xscale('log')
            plt.xlabel('Learning Rate')
            plt.ylabel('Loss')
            plt.title('Learning Rate Finder')
            plt.savefig(os.path.join(self.checkpoint_dir, f"{self.experiment_name}_lr_finder.png"))
            plt.close()
            
        return lr_values, loss_values
    
    def get_current_lr(self) -> float:
        """获取当前学习率"""
        return self.optimizer.param_groups[0]['lr']
    
    def set_learning_rate(self, new_lr: float) -> None:
        """设置新的学习率"""
        for param_group in self.optimizer.param_groups:
            param_group['lr'] = new_lr
            
    def plot_training_history(self, save_path: Optional[str] = None) -> None:
        """
        绘制训练历史
        
        参数:
            save_path: 保存路径，如不指定则显示图表
        """
        epochs = range(1, len(self.train_losses) + 1)
        
        plt.figure(figsize=(12, 8))
        
        # 损失曲线
        plt.subplot(2, 1, 1)
        plt.plot(epochs, self.train_losses, 'b-', label='Training Loss')
        plt.plot(epochs, self.val_losses, 'r-', label='Validation Loss')
        plt.title('Training and Validation Loss')
        plt.xlabel('Epochs')
        plt.ylabel('Loss')
        plt.legend()
        
        # 指标曲线
        if self.metrics_history:
            plt.subplot(2, 1, 2)
            for metric_name, metric_values in self.metrics_history.items():
                plt.plot(epochs, metric_values, label=metric_name)
            plt.title('Training Metrics')
            plt.xlabel('Epochs')
            plt.ylabel('Value')
            plt.legend()
            
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path)
            plt.close()
        else:
            plt.show()


def create_sequence_dataset(features: np.ndarray, 
                           targets: np.ndarray, 
                           sequence_length: int,
                           batch_size: int = 32,
                           train_ratio: float = 0.8) -> Tuple[DataLoader, DataLoader]:
    """
    创建序列数据集，用于时间序列预测或强化学习
    
    参数:
        features: 特征矩阵 (样本数, 特征数)
        targets: 目标值 (样本数,) 或 (样本数, 目标维度)
        sequence_length: 序列长度
        batch_size: 批次大小
        train_ratio: 训练集比例
        
    返回:
        (训练数据加载器, 验证数据加载器)
    """
    # 确保输入数据类型为NumPy数组
    if isinstance(features, pd.DataFrame):
        features = features.values
    if isinstance(targets, pd.Series):
        targets = targets.values
        
    # 获取维度信息
    n_samples, n_features = features.shape
    
    # 检查targets的维度
    if targets.ndim == 1:
        targets = targets.reshape(-1, 1)
    n_targets = targets.shape[1]
    
    # 创建序列样本
    X_sequences = []
    y_sequences = []
    
    for i in range(n_samples - sequence_length):
        X_sequences.append(features[i:i+sequence_length])
        y_sequences.append(targets[i+sequence_length])
        
    # 转换为NumPy数组
    X_sequences = np.array(X_sequences)
    y_sequences = np.array(y_sequences)
    
    # 计算训练集大小
    train_size = int(len(X_sequences) * train_ratio)
    
    # 分割训练集和验证集
    X_train, X_val = X_sequences[:train_size], X_sequences[train_size:]
    y_train, y_val = y_sequences[:train_size], y_sequences[train_size:]
    
    # 转换为PyTorch张量
    X_train_tensor = torch.FloatTensor(X_train)
    y_train_tensor = torch.FloatTensor(y_train)
    X_val_tensor = torch.FloatTensor(X_val)
    y_val_tensor = torch.FloatTensor(y_val)
    
    # 创建数据集
    train_dataset = TensorDataset(X_train_tensor, y_train_tensor)
    val_dataset = TensorDataset(X_val_tensor, y_val_tensor)
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=batch_size)
    
    return train_loader, val_loader


def calculate_metrics(predictions: torch.Tensor, targets: torch.Tensor) -> Dict[str, float]:
    """
    计算常用评估指标
    
    参数:
        predictions: 预测值
        targets: 目标值
        
    返回:
        指标字典
    """
    # 确保输入是NumPy数组
    if isinstance(predictions, torch.Tensor):
        predictions = predictions.detach().cpu().numpy()
    if isinstance(targets, torch.Tensor):
        targets = targets.detach().cpu().numpy()
        
    # 计算均方误差 (MSE)
    mse = np.mean((predictions - targets) ** 2)
    
    # 计算均方根误差 (RMSE)
    rmse = np.sqrt(mse)
    
    # 计算平均绝对误差 (MAE)
    mae = np.mean(np.abs(predictions - targets))
    
    # 计算平均绝对百分比误差 (MAPE)
    # 避免除以零
    mask = targets != 0
    mape = np.mean(np.abs((targets[mask] - predictions[mask]) / targets[mask])) * 100
    
    # 计算决定系数 (R^2)
    if np.var(targets) != 0:
        r2 = 1 - (np.sum((targets - predictions) ** 2) / np.sum((targets - np.mean(targets)) ** 2))
    else:
        r2 = 0
        
    # 计算预测方向准确率 (对于收益率预测)
    direction_accuracy = np.mean((predictions > 0) == (targets > 0))
    
    # 返回所有指标
    return {
        'MSE': mse,
        'RMSE': rmse,
        'MAE': mae,
        'MAPE': mape,
        'R2': r2,
        'Direction_Accuracy': direction_accuracy
    }


def create_recurrent_model(input_size: int, 
                          hidden_size: int, 
                          output_size: int,
                          num_layers: int = 1,
                          dropout: float = 0.0,
                          model_type: str = 'LSTM') -> nn.Module:
    """
    创建循环神经网络模型
    
    参数:
        input_size: 输入特征维度
        hidden_size: 隐藏层大小
        output_size: 输出维度
        num_layers: RNN层数
        dropout: Dropout比例
        model_type: 模型类型 ('LSTM', 'GRU', 'RNN')
        
    返回:
        循环神经网络模型
    """
    class RecurrentModel(nn.Module):
        def __init__(self):
            super(RecurrentModel, self).__init__()
            
            # 选择RNN类型
            if model_type == 'LSTM':
                self.rnn = nn.LSTM(input_size, hidden_size, num_layers, 
                                   batch_first=True, dropout=dropout if num_layers > 1 else 0)
            elif model_type == 'GRU':
                self.rnn = nn.GRU(input_size, hidden_size, num_layers, 
                                  batch_first=True, dropout=dropout if num_layers > 1 else 0)
            else:  # 默认基础RNN
                self.rnn = nn.RNN(input_size, hidden_size, num_layers, 
                                 batch_first=True, dropout=dropout if num_layers > 1 else 0)
                
            # 输出层
            self.fc = nn.Linear(hidden_size, output_size)
            
            # Dropout层
            self.dropout = nn.Dropout(dropout)
            
        def forward(self, x):
            # x shape: (batch_size, sequence_length, input_size)
            
            # RNN输出
            # output shape: (batch_size, sequence_length, hidden_size)
            # h_n shape: (num_layers, batch_size, hidden_size)
            if model_type == 'LSTM':
                output, (hidden, cell) = self.rnn(x)
            else:
                output, hidden = self.rnn(x)
                
            # 只使用最后一个时间步的隐藏状态
            # (batch_size, hidden_size)
            last_hidden = output[:, -1, :]
            
            # 应用dropout
            last_hidden = self.dropout(last_hidden)
            
            # 通过全连接层生成预测
            # (batch_size, output_size)
            predictions = self.fc(last_hidden)
            
            return predictions
            
    return RecurrentModel()


def create_feed_forward_model(input_size: int, 
                             hidden_sizes: List[int], 
                             output_size: int,
                             dropout: float = 0.0,
                             activation: nn.Module = nn.ReLU()) -> nn.Module:
    """
    创建前馈神经网络模型
    
    参数:
        input_size: 输入特征维度
        hidden_sizes: 隐藏层大小列表
        output_size: 输出维度
        dropout: Dropout比例
        activation: 激活函数
        
    返回:
        前馈神经网络模型
    """
    class FeedForwardModel(nn.Module):
        def __init__(self):
            super(FeedForwardModel, self).__init__()
            
            # 构建网络层
            layers = []
            
            # 输入层 -> 第一个隐藏层
            layers.append(nn.Linear(input_size, hidden_sizes[0]))
            layers.append(activation)
            layers.append(nn.Dropout(dropout))
            
            # 隐藏层之间
            for i in range(len(hidden_sizes) - 1):
                layers.append(nn.Linear(hidden_sizes[i], hidden_sizes[i+1]))
                layers.append(activation)
                layers.append(nn.Dropout(dropout))
                
            # 最后一个隐藏层 -> 输出层
            layers.append(nn.Linear(hidden_sizes[-1], output_size))
            
            # 将所有层组合为序列模型
            self.model = nn.Sequential(*layers)
            
        def forward(self, x):
            # 如果输入是3D张量 (batch_size, sequence_length, input_size)
            # 则将其转换为2D张量 (batch_size, input_size)
            if x.dim() == 3:
                x = x[:, -1, :]  # 只使用最后一个时间步
                
            return self.model(x)
            
    return FeedForwardModel()


def create_cnn_model(input_channels: int,
                    sequence_length: int,
                    feature_maps: List[int] = [64, 128, 256],
                    kernel_sizes: List[int] = [3, 3, 3],
                    output_size: int = 1,
                    dropout: float = 0.3) -> nn.Module:
    """
    创建一维CNN模型，适用于时间序列数据
    
    参数:
        input_channels: 输入通道数 (特征数)
        sequence_length: 序列长度
        feature_maps: 每层卷积的特征图数量
        kernel_sizes: 每层的卷积核大小
        output_size: 输出维度
        dropout: Dropout比例
        
    返回:
        一维CNN模型
    """
    class CNNModel(nn.Module):
        def __init__(self):
            super(CNNModel, self).__init__()
            
            self.conv_layers = nn.ModuleList()
            self.bn_layers = nn.ModuleList()
            self.pool_layers = nn.ModuleList()
            
            # 输入大小跟踪
            current_length = sequence_length
            current_channels = input_channels
            
            # 构建卷积层
            for i, (num_filters, kernel_size) in enumerate(zip(feature_maps, kernel_sizes)):
                # 添加卷积层
                self.conv_layers.append(
                    nn.Conv1d(in_channels=current_channels, 
                             out_channels=num_filters, 
                             kernel_size=kernel_size,
                             padding=kernel_size//2)  # 保持序列长度不变
                )
                
                # 添加批标准化层
                self.bn_layers.append(nn.BatchNorm1d(num_filters))
                
                # 添加池化层 (每两层做一次池化)
                if i % 2 == 1 or i == len(feature_maps) - 1:
                    self.pool_layers.append(nn.MaxPool1d(kernel_size=2, stride=2))
                    current_length = current_length // 2  # 更新长度
                else:
                    self.pool_layers.append(nn.Identity())  # 不做池化
                
                # 更新通道数
                current_channels = num_filters
            
            # 计算展平后的特征数
            self.flattened_size = current_channels * current_length
            
            # 全连接层
            self.fc1 = nn.Linear(self.flattened_size, 128)
            self.fc2 = nn.Linear(128, output_size)
            
            # Dropout层
            self.dropout = nn.Dropout(dropout)
            
        def forward(self, x):
            # 输入形状: (batch_size, sequence_length, input_channels)
            # 转换为CNN期望的形状: (batch_size, input_channels, sequence_length)
            x = x.permute(0, 2, 1)
            
            # 应用卷积层
            for conv, bn, pool in zip(self.conv_layers, self.bn_layers, self.pool_layers):
                x = conv(x)
                x = bn(x)
                x = nn.functional.relu(x)
                x = pool(x)
            
            # 展平
            x = x.view(x.size(0), -1)
            
            # 全连接层
            x = self.fc1(x)
            x = nn.functional.relu(x)
            x = self.dropout(x)
            x = self.fc2(x)
            
            return x
            
    return CNNModel() 