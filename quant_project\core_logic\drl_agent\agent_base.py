"""
DRL智能体基类
提供所有智能体共享的基础功能
"""

import os
import logging
import time
import datetime
import numpy as np
import pandas as pd
import gymnasium as gym
from gymnasium.utils.env_checker import check_env
from stable_baselines3.common.callbacks import BaseCallback, Eva<PERSON><PERSON><PERSON>back, CallbackList
from stable_baselines3.common.monitor import Monitor
from stable_baselines3.common.vec_env import DummyVecEnv, VecNormalize
from stable_baselines3.common.evaluation import evaluate_policy
import torch

from quant_project.core_logic.trading_environment import TradingEnvironment
from quant_project.core_logic.utils import is_gpu_available

class DRLAgentBase:
    """
    DRL智能体基类
    提供所有智能体共享的基础功能，如环境创建、模型保存、加载等
    """

    def __init__(self, env_config, agent_config, hpo_config=None):
        """
        初始化DRL智能体基类

        参数:
            env_config (dict): 交易环境配置
            agent_config (dict): 智能体配置
            hpo_config (dict, optional): 超参数优化配置
        """
        self.logger = logging.getLogger('drl_trading')
        self.env_config = env_config
        self.agent_config = agent_config
        self.hpo_config = hpo_config

        # 记录初始化参数
        self.logger.info("初始化DRL智能体")
        self.logger.info(f"环境配置: {env_config.keys() if env_config else None}")
        self.logger.info(f"智能体配置: {agent_config.keys() if agent_config else None}")
        self.logger.info(f"超参数优化配置: {hpo_config.keys() if hpo_config else None}")

        # 设置模型保存目录
        self.models_dir = 'saved_models'
        os.makedirs(self.models_dir, exist_ok=True)

        # 初始化最佳模型路径
        self.best_model_path = None

        # 训练指标
        self.training_stats = {
            'rewards': [],
            'losses': [],
            'steps': [],
            'episodes': [],
            'learning_rates': []
        }

        # 创建训练环境
        self.env = self._create_environment()

        # 检查环境是否符合Gymnasium API
        try:
            check_env(self.env)
            self.logger.info("环境检查通过")
        except Exception as e:
            self.logger.error(f"环境检查失败: {str(e)}")
            raise

        # 创建DRL模型 - 子类需要实现
        self.model = None

    def _create_environment(self):
        """
        创建交易环境

        返回:
            gym.Env: 交易环境实例
        """
        # 从环境配置中提取参数
        df_processed_data = self.env_config.get('df_processed_data')
        initial_capital = self.env_config.get('initial_capital', 100000)
        commission_rate = self.env_config.get('commission_rate', 0.0003)
        min_hold_days = self.env_config.get('min_hold_days', 3)
        allow_short = self.env_config.get('allow_short', False)
        max_position = self.env_config.get('max_position', 1.0)
        reward_config = self.env_config.get('reward_config', None)
        window_size = self.env_config.get('window_size', 20)

        # 创建环境
        env = TradingEnvironment(
            df_processed_data=df_processed_data,
            initial_capital=initial_capital,
            commission_rate=commission_rate,
            min_hold_days=min_hold_days,
            allow_short=allow_short,
            max_position=max_position,
            reward_config=reward_config,
            window_size=window_size
        )

        # 包装环境以记录指标
        env = Monitor(env)

        # 是否使用向量化环境
        if self.agent_config.get('use_vec_env', False):
            # 创建向量化环境
            env = DummyVecEnv([lambda: env])

            # 是否使用归一化环境
            if self.agent_config.get('use_vec_normalize', False):
                env = VecNormalize(
                    env,
                    norm_obs=True,
                    norm_reward=True,
                    clip_obs=10.0,
                    clip_reward=10.0,
                    gamma=self.agent_config.get('gamma', 0.99),
                    epsilon=1e-8
                )

        return env

    def train(self, total_timesteps, callback_list=None, hpo_trial=None, progress_bar=False, save_best_model=True):
        """
        训练DRL智能体

        参数:
            total_timesteps (int): 训练总步数
            callback_list (list, optional): 回调函数列表
            hpo_trial (optuna.Trial, optional): Optuna试验对象
            progress_bar (bool): 是否显示进度条，需要安装tqdm和rich
            save_best_model (bool): 是否保存训练过程中的最佳模型

        返回:
            dict: 训练结果统计信息
        """
        # 参数验证
        if total_timesteps <= 0:
            self.logger.warning(f"无效的训练步数: {total_timesteps}，已调整为默认值10000")
            total_timesteps = 10000

        if self.model is None:
            self.logger.error("模型未初始化，请先调用_create_model方法")
            return {'status': 'error', 'message': '模型未初始化'}

        start_time = time.time()
        self.logger.info(f"开始训练，算法: {self.agent_config.get('algorithm')}, 总步数: {total_timesteps}")

        try:
            # 执行训练
            self.model.learn(
                total_timesteps=total_timesteps,
                callback=callback_list,
                progress_bar=progress_bar
            )

            # 训练结束后评估模型
            mean_reward, std_reward = evaluate_policy(
                self.model,
                self.env,
                n_eval_episodes=10,
                deterministic=True
            )

            # 记录训练时间
            training_time = time.time() - start_time
            self.logger.info(f"训练完成，耗时: {training_time:.2f}秒，最终平均奖励: {mean_reward:.2f}±{std_reward:.2f}")

            # 训练结果
            result = {
                'status': 'success',
                'mean_reward': mean_reward,
                'std_reward': std_reward,
                'training_time': training_time,
                'total_timesteps': total_timesteps,
                'algorithm': self.agent_config.get('algorithm')
            }

            return result

        except Exception as e:
            self.logger.error(f"训练过程中出错: {str(e)}")
            return {'status': 'error', 'message': str(e)}

    def predict_action(self, observation, deterministic=True):
        """
        预测给定状态的动作

        参数:
            observation (np.array): 观察状态
            deterministic (bool): 是否使用确定性策略

        返回:
            int: 预测的动作
        """
        if self.model is None:
            self.logger.error("模型未初始化，无法预测")
            return None

        try:
            action, _states = self.model.predict(observation, deterministic=deterministic)
            return action
        except Exception as e:
            self.logger.error(f"预测动作时出错: {str(e)}")
            return None

    def save_model(self, save_path=None, stock_code=None, performance_metrics=None, clean_old_models=False):
        """
        保存模型

        参数:
            save_path (str, optional): 保存路径，如果为None则自动生成
            stock_code (str, optional): 股票代码，用于生成保存路径
            performance_metrics (dict, optional): 性能指标，用于生成保存路径
            clean_old_models (bool): 是否清理旧模型

        返回:
            str: 保存路径
        """
        if self.model is None:
            self.logger.error("模型未初始化，无法保存")
            return None

        try:
            # 生成保存路径
            if save_path is None:
                timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
                algorithm = self.agent_config.get('algorithm', 'unknown')
                
                if stock_code:
                    save_path = f"{self.models_dir}/{algorithm}_{stock_code}_{timestamp}"
                else:
                    save_path = f"{self.models_dir}/{algorithm}_{timestamp}"

            # 确保目录存在
            os.makedirs(os.path.dirname(save_path), exist_ok=True)

            # 保存模型
            self.model.save(save_path)
            self.logger.info(f"模型已保存到: {save_path}")

            # 如果需要，清理旧模型
            if clean_old_models and os.path.exists(self.models_dir):
                self._clean_non_best_models(timestamp)

            return save_path

        except Exception as e:
            self.logger.error(f"保存模型时出错: {str(e)}")
            return None

    @classmethod
    def load_model(cls, model_path, eval_env_config=None):
        """
        加载模型

        参数:
            model_path (str): 模型路径
            eval_env_config (dict, optional): 评估环境配置

        返回:
            DRLAgentBase: 加载的智能体实例
        """
        raise NotImplementedError("子类必须实现load_model方法")

    def _clean_non_best_models(self, current_timestamp):
        """
        清理非最佳模型

        参数:
            current_timestamp (str): 当前时间戳，用于标识最新模型
        """
        try:
            # 获取模型目录中的所有文件
            files = os.listdir(self.models_dir)
            algorithm = self.agent_config.get('algorithm', 'unknown')
            
            # 筛选出相同算法的模型文件
            model_files = [f for f in files if f.startswith(algorithm) and f.endswith('.zip')]
            
            # 如果有最佳模型路径，保留该模型
            if self.best_model_path and os.path.exists(self.best_model_path):
                best_model_filename = os.path.basename(self.best_model_path)
                model_files = [f for f in model_files if f != best_model_filename]
            
            # 保留当前模型和最近的3个模型
            current_model = [f for f in model_files if current_timestamp in f]
            other_models = [f for f in model_files if current_timestamp not in f]
            
            # 按修改时间排序
            other_models.sort(key=lambda x: os.path.getmtime(os.path.join(self.models_dir, x)), reverse=True)
            
            # 保留最近的3个模型
            models_to_keep = current_model + other_models[:3]
            models_to_delete = [f for f in model_files if f not in models_to_keep]
            
            # 删除旧模型
            for model_file in models_to_delete:
                file_path = os.path.join(self.models_dir, model_file)
                os.remove(file_path)
                self.logger.info(f"已删除旧模型: {file_path}")
                
        except Exception as e:
            self.logger.error(f"清理旧模型时出错: {str(e)}") 