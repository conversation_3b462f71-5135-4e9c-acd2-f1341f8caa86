"""
测试加密货币数据提取功能是否已被禁用
"""

from quant_project.core_logic.data_handler import DataHandler

def test_crypto_disabled():
    """测试加密货币数据提取功能是否已被禁用"""
    
    print("\n===== 测试加密货币数据提取功能是否已被禁用 =====")
    
    # 创建数据处理器实例
    handler = DataHandler()
    
    # 测试加密货币数据获取 - 应该返回错误
    crypto = 'crypto_BTC'
    try:
        print(f"\n尝试获取加密货币数据: {crypto}")
        print("预期结果: 功能已禁用错误")
        
        data = handler.get_stock_data(crypto, '2023-01-01', '2023-12-31')
        print("警告: 加密货币数据提取功能未被正确禁用")
    except ValueError as e:
        print(f"正确: 加密货币数据提取功能已被禁用")
        print(f"错误信息: {str(e)}")
    
    # 测试模拟数据生成功能是否已被禁用
    print("\n===== 测试模拟数据生成功能是否已被禁用 =====")
    
    # 检查错误信息中是否包含关于模拟数据的内容
    try:
        handler.get_stock_data(crypto, '2023-01-01', '2023-12-31')
    except ValueError as e:
        error_message = str(e)
        if "模拟数据" in error_message:
            print("警告: 错误信息中仍然包含关于模拟数据的内容")
        else:
            print("正确: 错误信息中不包含关于模拟数据的内容")
            print(f"错误信息: {error_message}")

if __name__ == "__main__":
    test_crypto_disabled()
