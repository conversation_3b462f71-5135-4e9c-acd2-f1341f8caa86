#!/usr/bin/env python3
"""
测试数据获取功能的脚本
验证频率参数映射和数据获取是否正常工作
"""

import sys
import os
import logging
from datetime import datetime, timedelta

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('test_data_acquisition')

def test_data_handler_adapter():
    """测试数据处理适配器"""
    print("=" * 60)
    print("测试数据处理适配器")
    print("=" * 60)
    
    try:
        from core_logic.data_handling.adapter import DataHandlerAdapter
        
        # 创建适配器实例
        adapter = DataHandlerAdapter()
        
        # 测试参数
        test_cases = [
            {
                'stock_code': 'sh000001',
                'start_date': '2024-01-01',
                'end_date': '2024-01-31',
                'frequency': '日线',
                'data_source': '股票'
            },
            {
                'stock_code': 'index_000001',
                'start_date': '2024-01-01',
                'end_date': '2024-01-31',
                'frequency': '日线',
                'data_source': '指数'
            },
            {
                'stock_code': 'sh000001',
                'start_date': '2024-01-01',
                'end_date': '2024-01-31',
                'frequency': 'D',  # 英文格式
                'data_source': '股票'
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n测试用例 {i}: {test_case}")
            try:
                data = adapter.get_stock_data(**test_case)
                if data is not None and not data.empty:
                    print(f"✅ 成功获取数据: {len(data)} 条记录")
                    print(f"   数据列: {list(data.columns)}")
                    print(f"   时间范围: {data.index.min()} 至 {data.index.max()}")
                else:
                    print("❌ 获取的数据为空")
            except Exception as e:
                print(f"❌ 获取数据失败: {str(e)}")
                
    except ImportError as e:
        print(f"❌ 导入模块失败: {str(e)}")
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

def test_data_handler():
    """测试原始数据处理器"""
    print("\n" + "=" * 60)
    print("测试原始数据处理器")
    print("=" * 60)
    
    try:
        from core_logic.data_handling.data_handler import DataHandler
        
        # 创建数据处理器实例
        handler = DataHandler()
        
        # 测试参数
        test_cases = [
            {
                'stock_code': 'sh000001',
                'start_date': '2024-01-01',
                'end_date': '2024-01-31',
                'frequency': '日线'
            },
            {
                'stock_code': 'sh000001',
                'start_date': '2024-01-01',
                'end_date': '2024-01-31',
                'frequency': 'D'  # 英文格式
            },
            {
                'stock_code': 'index_000001',
                'start_date': '2024-01-01',
                'end_date': '2024-01-31',
                'frequency': '日线'
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n测试用例 {i}: {test_case}")
            try:
                data = handler.get_stock_data(**test_case)
                if data is not None and not data.empty:
                    print(f"✅ 成功获取数据: {len(data)} 条记录")
                    print(f"   数据列: {list(data.columns)}")
                    print(f"   时间范围: {data.index.min()} 至 {data.index.max()}")
                else:
                    print("❌ 获取的数据为空")
            except Exception as e:
                print(f"❌ 获取数据失败: {str(e)}")
                
    except ImportError as e:
        print(f"❌ 导入模块失败: {str(e)}")
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")

def test_frequency_mapping():
    """测试频率参数映射"""
    print("\n" + "=" * 60)
    print("测试频率参数映射")
    print("=" * 60)
    
    # 测试频率映射
    freq_map_to_chinese = {
        'D': '日线',
        'W': '周线', 
        'M': '月线',
        'H': '小时线',
        'min': '分钟线',
        'daily': '日线',
        'weekly': '周线',
        'monthly': '月线',
        'hourly': '小时线',
        'minute': '分钟线'
    }
    
    print("英文到中文频率映射:")
    for eng, chn in freq_map_to_chinese.items():
        print(f"  {eng} -> {chn}")
    
    # 测试中文频率参数
    chinese_freqs = ['日线', '周线', '月线', '小时线', '分钟线']
    print(f"\n支持的中文频率参数: {chinese_freqs}")

def main():
    """主测试函数"""
    print("开始测试数据获取功能...")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试频率映射
    test_frequency_mapping()
    
    # 测试数据处理适配器
    test_data_handler_adapter()
    
    # 测试原始数据处理器
    test_data_handler()
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    main()
