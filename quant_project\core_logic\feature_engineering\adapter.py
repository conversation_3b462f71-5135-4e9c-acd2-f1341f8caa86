"""
特征工程适配器模块
提供与原始特征工程类兼容的接口
"""

import logging
import pandas as pd
import numpy as np
import warnings

from .base import FeatureEngineer
from .enhanced import EnhancedFeatureEngineer

class FeatureEngineerAdapter:
    """
    特征工程适配器类
    提供与原始特征工程类兼容的接口
    """

    def __init__(self, feature_config=None, data_dictionary_path='data_dictionary.json', use_enhanced=False):
        """
        初始化特征工程适配器

        参数:
            feature_config (dict): 特征配置字典，指定要计算的技术指标及其参数
            data_dictionary_path (str): 数据字典文件路径
            use_enhanced (bool): 是否使用增强版特征工程
        """
        self.logger = logging.getLogger('drl_trading')
        self.use_enhanced = use_enhanced

        # 创建特征工程器
        if use_enhanced:
            self.feature_engineer = EnhancedFeatureEngineer(
                feature_config=feature_config
            )
        else:
            self.feature_engineer = FeatureEngineer(
                feature_config=feature_config,
                data_dictionary_path=data_dictionary_path
            )

        # 保存原始特征工程类的属性
        self.feature_config = self.feature_engineer.feature_config
        self.feature_names = self.feature_engineer.feature_names
        self.data_dictionary_path = data_dictionary_path

    def generate_features(self, data):
        """
        生成特征

        参数:
            data (pandas.DataFrame): 原始行情数据，必须包含OHLCV列

        返回:
            pandas.DataFrame: 添加了特征的数据
        """
        # 调用特征工程器生成特征
        df = self.feature_engineer.generate_features(data)

        # 更新特征名称
        self.feature_names = self.feature_engineer.feature_names

        return df

    def engineer_features(self, data):
        """
        工程特征 - 与generate_features方法相同，提供兼容性

        参数:
            data (pandas.DataFrame): 原始行情数据，必须包含OHLCV列

        返回:
            pandas.DataFrame: 添加了特征的数据
        """
        return self.generate_features(data)

    def _ensure_column_names(self, df):
        """
        确保列名标准化

        参数:
            df (pandas.DataFrame): 原始数据

        返回:
            pandas.DataFrame: 标准化列名后的数据
        """
        return self.feature_engineer._ensure_column_names(df)

    def _calculate_price_features(self, df):
        """
        计算基本价格特征

        参数:
            df (pandas.DataFrame): 原始数据

        返回:
            pandas.DataFrame: 添加了价格特征的数据
        """
        if hasattr(self.feature_engineer, '_calculate_price_features'):
            return self.feature_engineer._calculate_price_features(df)
        else:
            from .price_features import PriceFeatureCalculator
            calculator = PriceFeatureCalculator(logger=self.logger)
            df, _ = calculator.calculate(df)
            return df

    def _calculate_technical_indicators(self, df):
        """
        计算技术指标

        参数:
            df (pandas.DataFrame): 原始数据

        返回:
            pandas.DataFrame: 添加了技术指标的数据
        """
        if hasattr(self.feature_engineer, '_calculate_technical_indicators'):
            return self.feature_engineer._calculate_technical_indicators(df)
        else:
            from .technical_indicators import TechnicalIndicatorCalculator
            calculator = TechnicalIndicatorCalculator(config=self.feature_config, logger=self.logger)
            df, _ = calculator.calculate(df)
            return df

    def _calculate_statistical_features(self, df):
        """
        计算统计特征

        参数:
            df (pandas.DataFrame): 原始数据

        返回:
            pandas.DataFrame: 添加了统计特征的数据
        """
        if hasattr(self.feature_engineer, '_calculate_statistical_features'):
            return self.feature_engineer._calculate_statistical_features(df)
        else:
            from .statistical_features import StatisticalFeatureCalculator
            calculator = StatisticalFeatureCalculator(
                config=self.feature_config.get('rolling_stats', {}), logger=self.logger)
            df, _ = calculator.calculate(df)
            return df

    def _calculate_advanced_features(self, df):
        """
        计算高级特征

        参数:
            df (pandas.DataFrame): 原始数据

        返回:
            pandas.DataFrame: 添加了高级特征的数据
        """
        if hasattr(self.feature_engineer, '_calculate_advanced_features'):
            return self.feature_engineer._calculate_advanced_features(df)
        else:
            # 简单实现，实际应根据需要扩展
            return df

    def _calculate_time_features(self, df):
        """
        计算时间特征

        参数:
            df (pandas.DataFrame): 原始数据

        返回:
            pandas.DataFrame: 添加了时间特征的数据
        """
        if hasattr(self.feature_engineer, '_calculate_time_features'):
            return self.feature_engineer._calculate_time_features(df)
        else:
            from .time_features import TimeFeatureCalculator
            calculator = TimeFeatureCalculator(logger=self.logger)
            df, _ = calculator.calculate(df)
            return df

    def _select_features(self, df):
        """
        特征选择

        参数:
            df (pandas.DataFrame): 包含特征的数据框

        返回:
            pandas.DataFrame: 只包含选定特征的数据框
        """
        if hasattr(self.feature_engineer, '_select_features'):
            return self.feature_engineer._select_features(df)
        else:
            from .utils import select_features
            df_selected, _ = select_features(
                df, '涨跌幅', n_features=30, method='mutual_info', logger=self.logger)
            return df_selected

    def _normalize_features(self, df):
        """
        归一化特征

        参数:
            df (pandas.DataFrame): 包含特征的数据框

        返回:
            pandas.DataFrame: 归一化后的数据框
        """
        if hasattr(self.feature_engineer, '_normalize_features'):
            return self.feature_engineer._normalize_features(df)
        else:
            from .utils import normalize_features
            return normalize_features(df, method='minmax', logger=self.logger)

# Alias for backward compatibility
FeatureEngineeringAdapter = FeatureEngineerAdapter
