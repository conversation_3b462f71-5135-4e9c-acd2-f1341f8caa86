#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
导入辅助模块
用于简化模块导入
"""

import os
import sys
import importlib

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
quant_project_path = os.path.join(project_root, 'quant_project')

if project_root not in sys.path:
    sys.path.append(project_root)

if quant_project_path not in sys.path:
    sys.path.append(quant_project_path)

def import_module(module_path, class_name=None):
    """导入模块或类"""
    try:
        module = importlib.import_module(module_path)
        if class_name:
            return getattr(module, class_name)
        return module
    except (ImportError, AttributeError) as e:
        print(f"导入 {module_path}{f'.{class_name}' if class_name else ''} 失败: {str(e)}")
        return None

# 核心模块
DataHandler = import_module('quant_project.core_logic.data_handler', 'DataHandler')
FeatureEngineer = import_module('quant_project.core_logic.feature_engineer', 'FeatureEngineer')
TradingEnvironment = import_module('quant_project.core_logic.trading_environment', 'TradingEnvironment')
DRLAgent = import_module('quant_project.core_logic.drl_agent', 'DRLAgent')
PerformanceAnalyzer = import_module('quant_project.core_logic.performance_analyzer', 'PerformanceAnalyzer')

# 如果上面的导入失败，尝试从根目录导入
if DataHandler is None:
    DataHandler = import_module('core_logic.data_handler', 'DataHandler')
if FeatureEngineer is None:
    FeatureEngineer = import_module('core_logic.feature_engineer', 'FeatureEngineer')
if TradingEnvironment is None:
    TradingEnvironment = import_module('core_logic.trading_environment', 'TradingEnvironment')
if DRLAgent is None:
    DRLAgent = import_module('core_logic.drl_agent', 'DRLAgent')
if PerformanceAnalyzer is None:
    PerformanceAnalyzer = import_module('core_logic.performance_analyzer', 'PerformanceAnalyzer')
