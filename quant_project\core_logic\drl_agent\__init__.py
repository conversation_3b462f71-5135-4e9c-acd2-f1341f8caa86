"""
DRL智能体模块
提供多种强化学习算法的实现和训练工具
"""

from quant_project.core_logic.drl_agent.agent_base import DRLAgentBase
from quant_project.core_logic.drl_agent.ppo_agent import PPOAgent
from quant_project.core_logic.drl_agent.a2c_agent import A2CAgent
from quant_project.core_logic.drl_agent.dqn_agent import DQNAgent
from quant_project.core_logic.drl_agent.callbacks import (
    MetricsCallback, 
    BestModelCallback, 
    RobustMetricsCallback
)
from quant_project.core_logic.drl_agent.dummy_agent import DummyAgent

# 用于向后兼容的DRLAgent类
class DRLAgent:
    """
    DRL智能体类，用于向后兼容，提供统一的接口
    """
    def __init__(self, algorithm="PPO", **kwargs):
        self.algorithm = algorithm
        self.agent = None
        self.env_config = kwargs.get('env_config', {})
        self.agent_config = kwargs.get('agent_config', {})
        self._create_agent(**kwargs)
    
    def _create_agent(self, **kwargs):
        """根据指定的算法创建具体的智能体"""
        try:
            if self.algorithm == "PPO":
                self.agent = PPOAgent(
                    env_config=self.env_config, 
                    agent_config=self.agent_config, 
                    **kwargs
                )
            elif self.algorithm == "A2C":
                self.agent = A2CAgent(
                    env_config=self.env_config, 
                    agent_config=self.agent_config, 
                    **kwargs
                )
            elif self.algorithm == "DQN":
                self.agent = DQNAgent(
                    env_config=self.env_config, 
                    agent_config=self.agent_config, 
                    **kwargs
                )
            else:
                raise ValueError(f"不支持的算法: {self.algorithm}")
        except Exception as e:
            # 创建简单的空智能体以保持兼容性
            import logging
            logger = logging.getLogger('drl_trading')
            logger.warning(f"创建DRL智能体时出错: {str(e)}，将使用空智能体")
            self.agent = DummyAgent(algorithm=self.algorithm)
    
    def train(self, env, total_timesteps, **kwargs):
        """训练智能体"""
        if self.agent is None:
            raise ValueError("智能体未初始化")
        return self.agent.train(env, total_timesteps, **kwargs)
    
    def predict(self, observation, **kwargs):
        """预测动作"""
        if self.agent is None:
            raise ValueError("智能体未初始化")
        return self.agent.predict(observation, **kwargs)
    
    def save(self, path):
        """保存模型"""
        if self.agent is None:
            raise ValueError("智能体未初始化")
        return self.agent.save(path)
    
    def load(self, path):
        """加载模型"""
        if self.agent is None:
            raise ValueError("智能体未初始化")
        return self.agent.load(path)

# 向后兼容导出
__all__ = [
    'DRLAgentBase',
    'PPOAgent',
    'A2CAgent',
    'DQNAgent',
    'MetricsCallback',
    'BestModelCallback',
    'RobustMetricsCallback',
    'DRLAgent',
    'DummyAgent'
] 