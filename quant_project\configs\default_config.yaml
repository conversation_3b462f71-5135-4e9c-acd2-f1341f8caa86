# 默认配置文件

# 环境配置
env_config:
  # 基本参数
  initial_capital: 100000  # 初始资金
  commission_rate: 0.0003  # 手续费率（单边）
  min_hold_days: 3  # 最小持仓天数
  allow_short: false  # 是否允许做空
  max_position: 1.0  # 最大仓位比例
  
  # 观测窗口
  window_size: 20  # 观测窗口大小
  
  # 奖励函数配置
  reward_config:
    portfolio_return: 1.0  # 组合收益权重
    volatility_penalty: 0.1  # 波动率惩罚权重
    drawdown_penalty: 0.2  # 回撤惩罚权重
    holding_penalty: 0.05  # 持仓成本惩罚权重
    trade_penalty: 0.01  # 交易成本惩罚权重
  
  # 特征工程配置
  feature_config:
    # 简单移动平均线
    sma:
      use: true
      periods: [5, 20, 60]
    
    # 指数移动平均线
    ema:
      use: true
      periods: [5, 20]
    
    # 相对强弱指标
    rsi:
      use: true
      period: 14
    
    # MACD
    macd:
      use: true
      fast: 12
      slow: 26
      signal: 9
    
    # 布林带
    bbands:
      use: true
      period: 20
      std: 2.0
    
    # 平均真实波动幅度
    atr:
      use: true
      period: 14
    
    # 随机指标
    stoch:
      use: false
      k_period: 14
      d_period: 3
      slowing: 3
    
    # 成交量指标
    volume:
      use: false
      periods: [5, 20]
    
    # 滚动统计特征
    rolling_stats:
      use: false
      window: 20
  
  # 数据分割
  data_split:
    train_ratio: 0.7  # 训练集比例
    val_ratio: 0.15  # 验证集比例
    test_ratio: 0.15  # 测试集比例

# 智能体配置
agent_config:
  # 算法配置
  algorithm: PPO  # 可选: PPO, A2C, DQN
  policy_network: MlpPolicy  # 策略网络类型
  
  # 通用超参数
  learning_rate: 0.0003  # 学习率
  gamma: 0.99  # 折扣因子
  use_gpu: true  # 是否使用GPU
  
  # PPO特定参数
  ppo:
    n_steps: 2048  # 每次更新前收集的步数
    batch_size: 64  # 每次优化的小批量大小
    n_epochs: 10  # 每次更新时的优化轮数
    gae_lambda: 0.95  # GAE参数
    clip_range: 0.2  # PPO裁剪参数
    ent_coef: 0.01  # 熵系数
    vf_coef: 0.5  # 价值函数系数
    max_grad_norm: 0.5  # 梯度裁剪
  
  # A2C特定参数
  a2c:
    n_steps: 5  # 每次更新前收集的步数
    ent_coef: 0.01  # 熵系数
    vf_coef: 0.5  # 价值函数系数
    max_grad_norm: 0.5  # 梯度裁剪
  
  # DQN特定参数
  dqn:
    buffer_size: 10000  # 经验回放缓冲区大小
    learning_starts: 1000  # 开始学习前收集的步数
    batch_size: 32  # 每次优化的小批量大小
    tau: 1.0  # 目标网络更新率
    train_freq: 4  # 训练频率
    gradient_steps: 1  # 每次更新的梯度步数
    target_update_interval: 1000  # 目标网络更新间隔
    exploration_fraction: 0.1  # 探索率衰减的比例
    exploration_initial_eps: 1.0  # 初始探索率
    exploration_final_eps: 0.05  # 最终探索率
    max_grad_norm: 10  # 梯度裁剪
  
  # 网络结构
  network:
    net_arch:
      pi: [64, 64]  # 策略网络隐藏层
      vf: [64, 64]  # 价值网络隐藏层
    activation_fn: relu  # 激活函数
