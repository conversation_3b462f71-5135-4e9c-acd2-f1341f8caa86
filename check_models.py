"""
检查模型文件
"""

import os
import sys

print("脚本开始执行")

# 检查saved_models目录
saved_models_dir = "saved_models"
if not os.path.exists(saved_models_dir):
    os.makedirs(saved_models_dir)
    print(f"创建{saved_models_dir}目录")
else:
    print(f"{saved_models_dir}目录已存在")

# 列出现有模型
model_files = [f for f in os.listdir(saved_models_dir) if f.endswith(".zip")]
print(f"现有模型文件: {model_files}")

# 检查是否有最佳模型
best_models = [f for f in model_files if 'best_' in f]
print(f"最佳模型文件: {best_models}")

# 检查是否有对应的VecNormalize参数文件
vec_normalize_files = [f for f in os.listdir(saved_models_dir) if f.endswith("_vecnormalize.pkl")]
print(f"VecNormalize参数文件: {vec_normalize_files}")

# 创建一个测试模型文件
print("创建测试模型文件...")
# 创建最佳模型文件
with open(os.path.join(saved_models_dir, "best_PPO.zip"), "wb") as f:
    f.write(b"best_test")
print("测试模型文件创建完成")

# 再次列出模型文件
model_files = [f for f in os.listdir(saved_models_dir) if f.endswith(".zip")]
print(f"更新后的模型文件列表: {model_files}")

print("脚本执行完毕")
