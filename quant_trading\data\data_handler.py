"""
数据处理模块
负责从AkShare获取金融数据，进行数据验证、清洗和缓存
遵循顶尖量化基金的最佳实践标准
"""

import os
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import hashlib
import json
import time
import pytz
from scipy import stats

try:
    import akshare as ak
except ImportError:
    logging.error("未安装AkShare库，请使用 pip install akshare 安装")

class DataHandler:
    """
    数据处理类
    负责从AkShare获取金融数据，进行数据验证、清洗和缓存
    实现了高质量数据处理流程，包括缺失值处理、异常值检测和时间序列对齐
    """

    def __init__(self, cache_dir='data_cache', timezone='Asia/Shanghai',
                 missing_value_strategy='ffill', outlier_detection=True,
                 data_validation=True, adjust_price=True):
        """
        初始化数据处理器

        参数:
            cache_dir (str): 缓存目录路径
            timezone (str): 时区设置，默认为'Asia/Shanghai'
            missing_value_strategy (str): 缺失值处理策略，可选'ffill'(前向填充),'bfill'(后向填充),'mean'(均值填充),'median'(中位数填充),'none'(不处理)
            outlier_detection (bool): 是否进行异常值检测
            data_validation (bool): 是否进行数据验证
            adjust_price (bool): 是否使用复权价格
        """
        self.cache_dir = cache_dir
        self.logger = logging.getLogger('drl_trading')
        self.timezone = pytz.timezone(timezone)
        self.missing_value_strategy = missing_value_strategy
        self.outlier_detection = outlier_detection
        self.data_validation = data_validation
        self.adjust_price = adjust_price

        # 指数代码映射表
        self.index_map = {
            # 上证系列指数
            '000001': {'name': '上证指数', 'prefix': 'sh'},
            '000016': {'name': '上证50', 'prefix': 'sh'},
            '000300': {'name': '沪深300', 'prefix': 'sh'},
            '000905': {'name': '中证500', 'prefix': 'sh'},
            '000852': {'name': '中证1000', 'prefix': 'sh'},
            # 深证系列指数
            '399001': {'name': '深证成指', 'prefix': 'sz'},
            '399006': {'name': '创业板指', 'prefix': 'sz'},
            '399673': {'name': '创业板50', 'prefix': 'sz'},
            # 其他重要指数
            '000688': {'name': '科创50', 'prefix': 'sh'},
            '399324': {'name': '深证红利', 'prefix': 'sz'}
        }

        # 确保缓存目录存在
        os.makedirs(self.cache_dir, exist_ok=True)

    def get_stock_data(self, stock_code, start_date, end_date, frequency='日线', use_cache=True):
        """
        获取金融数据（股票、期货、指数）

        参数:
            stock_code (str): 金融产品代码，格式如下：
                - 股票: 'sh000001' 或 'sz399001'
                - 期货: 'futures_名称'，如 'futures_IF'
                - 指数: 'index_名称'，如 'index_000300'
            start_date (str): 开始日期，格式为 'YYYY-MM-DD'
            end_date (str): 结束日期，格式为 'YYYY-MM-DD'
            frequency (str): 数据频率，可选 '日线', '周线', '月线', '小时线', '分钟线'
            use_cache (bool): 是否使用缓存

        返回:
            pandas.DataFrame: 金融数据
        """
        # 首先检查是否是加密货币代码，如果是则直接禁止
        if stock_code.startswith('crypto_'):
            self.logger.warning("加密货币数据提取功能已被禁用")
            error_message = "加密货币数据提取功能已被禁用，请使用其他类型的金融数据。\n"
            error_message += "请参考akshare官方文档获取正确的数据提取方法。\n"
            error_message += "根据要求，系统不再支持加密货币数据提取和模拟数据生成功能。"
            raise ValueError(error_message)
        # 生成缓存文件名
        cache_key = self._generate_cache_key(stock_code, start_date, end_date, frequency)
        cache_file = os.path.join(self.cache_dir, f"{cache_key}.csv")

        # 如果使用缓存且缓存文件存在，则从缓存加载
        if use_cache and os.path.exists(cache_file):
            self.logger.info(f"从缓存加载数据: {cache_file}")
            try:
                data = pd.read_csv(cache_file, index_col=0, parse_dates=True)
                return data
            except Exception as e:
                self.logger.warning(f"从缓存加载数据失败: {str(e)}，将重新获取数据")

        # 从AkShare获取数据
        try:
            self.logger.info(f"从AkShare获取数据: {stock_code}, {start_date} 至 {end_date}, 频率: {frequency}")

            # 解析代码类型和具体代码
            if stock_code.startswith('sh') or stock_code.startswith('sz'):
                # 股票数据
                market = stock_code[:2]
                code = stock_code[2:]
                data_type = 'stock'
            elif stock_code.startswith('futures_'):
                # 期货数据
                code = stock_code[9:]
                data_type = 'futures'
            elif stock_code.startswith('index_'):
                # 指数数据
                code = stock_code[6:]
                data_type = 'index'
            elif stock_code.startswith('crypto_'):
                # 加密货币数据已被禁用
                self.logger.warning("加密货币数据提取功能已被禁用")
                error_message = "加密货币数据提取功能已被禁用，请使用其他类型的金融数据。\n"
                error_message += "请参考akshare官方文档获取正确的数据提取方法。\n"
                error_message += "根据要求，系统不再支持加密货币数据提取和模拟数据生成功能。"
                raise ValueError(error_message)
            else:
                # 默认当作股票处理
                market = 'sh' if stock_code.startswith('6') else 'sz'
                code = stock_code
                data_type = 'stock'
                self.logger.warning(f"未识别的代码格式: {stock_code}，将当作股票处理")

            # 根据数据类型和频率选择不同的API
            if frequency == '日线':
                try:
                    if data_type == 'stock':
                        # 股票数据提取
                        if market == 'sh':
                            # 上海证券交易所股票
                            self.logger.info(f"获取上海证券交易所股票: {code}")
                            try:
                                try:
                                    # 首先尝试使用stock_zh_index_daily_em，这个API不依赖PyMiniRacer
                                    self.logger.info(f"尝试使用stock_zh_index_daily_em获取数据: sh{code}")
                                    data = ak.stock_zh_index_daily_em(symbol=f"sh{code}")
                                    self.logger.info(f"使用stock_zh_index_daily_em获取到的数据列名: {list(data.columns)}")
                                except Exception as inner_e:
                                    self.logger.error(f"使用stock_zh_index_daily_em获取数据失败: {str(inner_e)}")
                                    try:
                                        # 如果失败，尝试使用stock_zh_a_daily
                                        self.logger.info(f"尝试使用stock_zh_a_daily获取数据: {code}")
                                        data = ak.stock_zh_a_daily(symbol=f"{code}", start_date=start_date, end_date=end_date, adjust="qfq")
                                        self.logger.info(f"获取到的数据列名: {list(data.columns)}")
                                        self.logger.info(f"获取到的数据前5行: \n{data.head()}")
                                    except Exception as e2:
                                        self.logger.error(f"使用stock_zh_a_daily获取数据失败: {str(e2)}")
                                        # 尝试使用其他API
                                        self.logger.info(f"尝试使用stock_zh_index_daily获取数据: sh{code}")
                                        data = ak.stock_zh_index_daily(symbol=f"sh{code}")
                                        self.logger.info(f"使用stock_zh_index_daily获取到的数据列名: {list(data.columns)}")
                            except Exception as e:
                                self.logger.error(f"获取上海证券交易所股票数据失败: {str(e)}")
                                # 尝试使用其他API
                                self.logger.info(f"尝试使用其他API获取上海证券交易所股票: {code}")
                                data = ak.stock_zh_index_daily_em(symbol=f"sh{code}")
                                self.logger.info(f"使用其他API获取到的数据列名: {list(data.columns)}")
                        elif market == 'sz':
                            # 深圳证券交易所股票
                            self.logger.info(f"获取深圳证券交易所股票: {code}")
                            try:
                                try:
                                    # 首先尝试使用stock_zh_index_daily_em，这个API不依赖PyMiniRacer
                                    self.logger.info(f"尝试使用stock_zh_index_daily_em获取数据: sz{code}")
                                    data = ak.stock_zh_index_daily_em(symbol=f"sz{code}")
                                    self.logger.info(f"使用stock_zh_index_daily_em获取到的数据列名: {list(data.columns)}")
                                except Exception as inner_e:
                                    self.logger.error(f"使用stock_zh_index_daily_em获取数据失败: {str(inner_e)}")
                                    try:
                                        # 如果失败，尝试使用stock_zh_a_daily
                                        self.logger.info(f"尝试使用stock_zh_a_daily获取数据: {code}")
                                        data = ak.stock_zh_a_daily(symbol=f"{code}", start_date=start_date, end_date=end_date, adjust="qfq")
                                        self.logger.info(f"获取到的数据列名: {list(data.columns)}")
                                        self.logger.info(f"获取到的数据前5行: \n{data.head()}")
                                    except Exception as e2:
                                        self.logger.error(f"使用stock_zh_a_daily获取数据失败: {str(e2)}")
                                        # 尝试使用其他API
                                        self.logger.info(f"尝试使用stock_zh_index_daily获取数据: sz{code}")
                                        data = ak.stock_zh_index_daily(symbol=f"sz{code}")
                                        self.logger.info(f"使用stock_zh_index_daily获取到的数据列名: {list(data.columns)}")
                            except Exception as e:
                                self.logger.error(f"获取深圳证券交易所股票数据失败: {str(e)}")
                                # 尝试使用其他API
                                self.logger.info(f"尝试使用其他API获取深圳证券交易所股票: {code}")
                                data = ak.stock_zh_index_daily_em(symbol=f"sz{code}")
                                self.logger.info(f"使用其他API获取到的数据列名: {list(data.columns)}")
                        else:
                            raise ValueError(f"不支持的市场代码: {market}，股票代码应以'sh'或'sz'开头")
                    elif data_type == 'futures':
                        # 期货数据提取已被禁用
                        self.logger.warning("期货数据提取功能已被禁用")
                        error_message = "期货数据提取功能已被禁用，请使用其他类型的金融数据。\n"
                        error_message += "期货代码格式说明:\n"
                        error_message += "- 中金所股指期货: IF, IC, IH\n"
                        error_message += "- 上海期货交易所: CU(铜), AL(铝), ZN(锌), AU(黄金), AG(白银)等\n"
                        error_message += "- 大连商品交易所: A(豆一), M(豆粕), C(玉米), Y(豆油)等\n"
                        error_message += "- 郑州商品交易所: SR(白糖), CF(棉花), TA(PTA), MA(甲醇)等\n"
                        raise ValueError(error_message)


                    elif data_type == 'index':
                        # 指数数据提取
                        self.logger.info(f"获取指数数据: {code}")

                        # 检查是否是已知的指数代码
                        if code in self.index_map:
                            index_info = self.index_map[code]
                            prefix = index_info['prefix']
                            index_name = index_info['name']
                            self.logger.info(f"识别到已知指数: {index_name} ({prefix}{code})")
                            full_code = f"{prefix}{code}"
                        else:
                            # 尝试根据代码前缀判断
                            if code.startswith('000'):
                                prefix = 'sh'
                                full_code = f"{prefix}{code}"
                                self.logger.info(f"根据前缀判断为上证系列指数: {full_code}")
                            elif code.startswith('399'):
                                prefix = 'sz'
                                full_code = f"{prefix}{code}"
                                self.logger.info(f"根据前缀判断为深证系列指数: {full_code}")
                            elif code.startswith('sh') or code.startswith('sz'):
                                # 已经包含前缀
                                full_code = code
                                self.logger.info(f"指数代码已包含前缀: {full_code}")
                            else:
                                # 无法判断，尝试直接使用
                                full_code = code
                                self.logger.info(f"无法识别的指数代码格式，将直接使用: {full_code}")

                        # 系统性尝试多种接口获取指数数据
                        data = None
                        error_messages = []

                        # 方法1: 使用东方财富指数行情接口
                        try:
                            self.logger.info(f"尝试使用东方财富指数行情接口获取: {full_code}")
                            data = ak.stock_zh_index_daily_em(symbol=full_code)

                            if data is not None and not data.empty:
                                self.logger.info(f"成功使用东方财富指数行情接口获取数据，共 {len(data)} 条记录")
                            else:
                                error_messages.append("东方财富指数行情接口返回空数据")
                                data = None
                        except Exception as e:
                            error_msg = f"使用东方财富指数行情接口失败: {str(e)}"
                            self.logger.warning(error_msg)
                            error_messages.append(error_msg)

                        # 方法2: 如果方法1失败，尝试使用新浪财经指数行情接口
                        if data is None or data.empty:
                            try:
                                self.logger.info(f"尝试使用新浪财经指数行情接口获取: {code}")
                                data = ak.stock_zh_index_daily(symbol=full_code)

                                if data is not None and not data.empty:
                                    self.logger.info(f"成功使用新浪财经指数行情接口获取数据，共 {len(data)} 条记录")
                                else:
                                    error_messages.append("新浪财经指数行情接口返回空数据")
                                    data = None
                            except Exception as e:
                                error_msg = f"使用新浪财经指数行情接口失败: {str(e)}"
                                self.logger.warning(error_msg)
                                error_messages.append(error_msg)

                        # 方法3: 如果方法2失败，尝试使用A股指数历史行情接口
                        if data is None or data.empty:
                            try:
                                # 移除可能的前缀
                                clean_code = code
                                if clean_code.startswith('sh') or clean_code.startswith('sz'):
                                    clean_code = clean_code[2:]

                                self.logger.info(f"尝试使用A股指数历史行情接口获取: {clean_code}")
                                # 转换日期格式为YYYYMMDD
                                start_date_fmt = start_date.replace('-', '')
                                end_date_fmt = end_date.replace('-', '')

                                data = ak.index_zh_a_hist(symbol=clean_code, period="daily",
                                                         start_date=start_date_fmt, end_date=end_date_fmt)

                                if data is not None and not data.empty:
                                    self.logger.info(f"成功使用A股指数历史行情接口获取数据，共 {len(data)} 条记录")
                                else:
                                    error_messages.append("A股指数历史行情接口返回空数据")
                                    data = None
                            except Exception as e:
                                error_msg = f"使用A股指数历史行情接口失败: {str(e)}"
                                self.logger.warning(error_msg)
                                error_messages.append(error_msg)

                        # 方法4: 如果方法3失败，尝试使用全球指数数据接口
                        if data is None or data.empty:
                            try:
                                self.logger.info(f"尝试使用全球指数数据接口获取: {code}")
                                data = ak.index_investing_global_daily(symbol=code, period="daily")

                                if data is not None and not data.empty:
                                    self.logger.info(f"成功使用全球指数数据接口获取数据，共 {len(data)} 条记录")
                                else:
                                    error_messages.append("全球指数数据接口返回空数据")
                                    data = None
                            except Exception as e:
                                error_msg = f"使用全球指数数据接口失败: {str(e)}"
                                self.logger.warning(error_msg)
                                error_messages.append(error_msg)

                        # 方法5: 如果方法4失败，尝试使用分钟级别数据并转换为日线
                        if data is None or data.empty:
                            try:
                                self.logger.info(f"尝试使用分钟级别数据并转换为日线: {full_code}")
                                min_data = ak.index_zh_a_hist_min_em(symbol=full_code, period="1",
                                                                    start_date=start_date, end_date=end_date)

                                if min_data is not None and not min_data.empty:
                                    # 确保索引是日期时间类型
                                    if '时间' in min_data.columns:
                                        min_data['时间'] = pd.to_datetime(min_data['时间'])
                                        min_data.set_index('时间', inplace=True)
                                    elif '日期' in min_data.columns:
                                        min_data['日期'] = pd.to_datetime(min_data['日期'])
                                        min_data.set_index('日期', inplace=True)

                                    # 转换为日线数据
                                    data = min_data.resample('D').agg({
                                        'open': 'first',
                                        'high': 'max',
                                        'low': 'min',
                                        'close': 'last',
                                        'volume': 'sum'
                                    }).dropna()

                                    self.logger.info(f"成功使用分钟级别数据转换为日线数据，共 {len(data)} 条记录")
                                else:
                                    error_messages.append("分钟级别数据接口返回空数据")
                                    data = None
                            except Exception as e:
                                error_msg = f"使用分钟级别数据并转换为日线失败: {str(e)}"
                                self.logger.warning(error_msg)
                                error_messages.append(error_msg)

                        # 如果所有方法都失败，尝试使用上证指数作为替代
                        if data is None or data.empty:
                            error_detail = "\n".join(error_messages)
                            error_message = f"未能获取到指数 {code} 的数据。尝试了以下方法但均失败:\n{error_detail}\n"
                            error_message += "请检查指数代码是否正确，或尝试以下格式:\n"
                            error_message += "- 上证系列指数: sh000001(上证指数), sh000300(沪深300), sh000016(上证50), sh000905(中证500)\n"
                            error_message += "- 深证系列指数: sz399001(深证成指), sz399006(创业板指), sz399673(创业板50)\n"
                            error_message += "- 科创板指数: sh000688(科创50)\n"

                            self.logger.warning(error_message)
                            self.logger.info("尝试使用上证指数(sh000001)作为替代")

                            try:
                                # 尝试获取上证指数数据作为替代
                                data = ak.stock_zh_index_daily_em(symbol="sh000001")
                                if data is not None and not data.empty:
                                    self.logger.info(f"成功获取上证指数数据作为替代，共 {len(data)} 条记录")
                                else:
                                    raise ValueError("获取上证指数数据失败")
                            except Exception as fallback_e:
                                self.logger.error(f"获取上证指数数据失败: {str(fallback_e)}")
                                raise ValueError(error_message)
                    elif data_type == 'crypto':
                        # 加密货币数据提取功能已被禁用
                        self.logger.warning("加密货币数据提取功能已被禁用")
                        error_message = "加密货币数据提取功能已被禁用，请使用其他类型的金融数据。\n"
                        error_message += "根据要求，系统不再支持加密货币数据提取和模拟数据生成功能。\n"
                        error_message += "请参考akshare官方文档获取正确的数据提取方法。"
                        raise ValueError(error_message)

                    # 缓存数据
                    if use_cache and data is not None and not data.empty:
                        self._cache_data(data, stock_code, start_date, end_date, frequency)

                    # 检查数据结构
                    if data is None or data.empty:
                        raise ValueError(f"获取的数据为空，请检查输入的代码是否正确: {stock_code}")

                    # 打印列名，帮助调试
                    self.logger.info(f"API返回的数据列名: {list(data.columns)}")
                    self.logger.info(f"API返回的数据前5行: \n{data.head()}")

                    # 检查日期列，AkShare可能返回不同名称的日期列
                    date_column_candidates = ['date', '日期', 'trade_date', '交易日期', '日期时间']
                    date_column = None
                    for col in date_column_candidates:
                        if col in data.columns:
                            date_column = col
                            self.logger.info(f"找到日期列: {col}")
                            break

                    if date_column is None:
                        # 如果没有找到日期列，但数据有索引，尝试重置索引
                        if isinstance(data.index, pd.DatetimeIndex):
                            self.logger.info(f"数据索引是DatetimeIndex，重置索引")
                            data = data.reset_index()
                            if 'index' in data.columns:
                                data.rename(columns={'index': 'date'}, inplace=True)
                                date_column = 'date'
                                self.logger.info(f"将索引转换为date列")

                        # 如果仍然没有日期列，尝试创建一个日期列
                        if date_column is None:
                            self.logger.info(f"未找到日期列，尝试创建一个日期列")
                            # 创建一个日期列
                            data['date'] = pd.date_range(start=pd.to_datetime(start_date), periods=len(data), freq='D')
                            date_column = 'date'
                            self.logger.info(f"创建了日期列: {date_column}")

                except Exception as e:
                    self.logger.error(f"从AkShare获取数据失败: {str(e)}")

                    # 如果是'date'列的问题，尝试修复
                    if "'date'" in str(e):
                        self.logger.info("检测到'date'列问题，尝试修复")
                        try:
                            # 创建一个日期列
                            if data is not None and not data.empty:
                                self.logger.info(f"数据不为空，尝试添加日期列。数据列: {list(data.columns)}")
                                # 创建日期范围
                                date_range = pd.date_range(start=pd.to_datetime(start_date), end=pd.to_datetime(end_date), freq='D')
                                # 过滤掉周末
                                date_range = date_range[date_range.dayofweek < 5]
                                # 确保日期范围长度与数据长度一致
                                if len(date_range) > len(data):
                                    date_range = date_range[:len(data)]
                                elif len(date_range) < len(data):
                                    # 如果日期范围不够长，扩展它
                                    extra_days = len(data) - len(date_range)
                                    end_date_dt = pd.to_datetime(end_date)
                                    extended_range = pd.date_range(start=end_date_dt + pd.Timedelta(days=1), periods=extra_days, freq='D')
                                    # 过滤掉周末
                                    extended_range = extended_range[extended_range.dayofweek < 5]
                                    date_range = date_range.append(extended_range)

                                # 添加日期列
                                data['date'] = date_range
                                self.logger.info(f"成功添加日期列，现在的列: {list(data.columns)}")
                                return data
                        except Exception as inner_e:
                            self.logger.error(f"尝试修复'date'列问题失败: {str(inner_e)}")

                    # 提供详细的错误信息和建议
                    error_message = f"获取数据失败: {str(e)}\n"

                    if data_type == 'stock':
                        error_message += "股票代码格式说明:\n"
                        error_message += "- 上海证券交易所股票: sh + 6位数字，如 sh600000\n"
                        error_message += "- 深圳证券交易所股票: sz + 6位数字，如 sz000001\n"
                    elif data_type == 'futures':
                        error_message += "期货代码格式说明:\n"
                        error_message += "- 中金所股指期货: IF, IC, IH\n"
                        error_message += "- 上海期货交易所: CU(铜), AL(铝), ZN(锌), AU(黄金), AG(白银)等\n"
                        error_message += "- 大连商品交易所: A(豆一), M(豆粕), C(玉米), Y(豆油)等\n"
                        error_message += "- 郑州商品交易所: SR(白糖), CF(棉花), TA(PTA), MA(甲醇)等\n"
                    elif data_type == 'index':
                        error_message += "指数代码格式说明:\n"
                        error_message += "- 上证指数: sh000001, sh000300(沪深300), sh000016(上证50)等\n"
                        error_message += "- 深证指数: sz399001(深证成指), sz399006(创业板指)等\n"
                    elif data_type == 'crypto':
                        error_message += "加密货币数据提取功能已被禁用\n"
                        error_message += "根据要求，系统不再支持加密货币数据提取和模拟数据生成功能。\n"
                        error_message += "请使用其他类型的金融数据，如股票或指数。\n"
                        error_message += "请参考akshare官方文档获取正确的数据提取方法。"

                    raise ValueError(error_message)
            elif frequency == '周线':
                # 检查是否是加密货币
                if stock_code.startswith('crypto_'):
                    # 加密货币数据已被禁用
                    self.logger.warning("加密货币数据提取功能已被禁用")
                    error_message = "加密货币数据提取功能已被禁用，请使用其他类型的金融数据。\n"
                    error_message += "请参考akshare官方文档获取正确的数据提取方法。\n"
                    error_message += "根据要求，系统不再支持加密货币数据提取和模拟数据生成功能。"
                    raise ValueError(error_message)
                # 使用日线数据重采样为周线
                daily_data = self.get_stock_data(stock_code, start_date, end_date, '日线', use_cache=False)
                data = self._resample_data(daily_data, 'W')
            elif frequency == '月线':
                # 检查是否是加密货币
                if stock_code.startswith('crypto_'):
                    # 加密货币数据已被禁用
                    self.logger.warning("加密货币数据提取功能已被禁用")
                    error_message = "加密货币数据提取功能已被禁用，请使用其他类型的金融数据。\n"
                    error_message += "请参考akshare官方文档获取正确的数据提取方法。\n"
                    error_message += "根据要求，系统不再支持加密货币数据提取和模拟数据生成功能。"
                    raise ValueError(error_message)
                # 使用日线数据重采样为月线
                daily_data = self.get_stock_data(stock_code, start_date, end_date, '日线', use_cache=False)
                data = self._resample_data(daily_data, 'M')
            elif frequency == '小时线' or frequency == '分钟线':
                raise NotImplementedError(f"暂不支持 {frequency} 频率")
            else:
                raise ValueError(f"不支持的频率: {frequency}")

            # 验证数据
            if not self._validate_data(data):
                raise ValueError("获取的数据无效")

            # 设置日期索引
            # 检查日期列，AkShare可能返回不同名称的日期列
            date_column_candidates = ['date', '日期', 'trade_date', '交易日期', '日期时间']
            date_column = None
            for col in date_column_candidates:
                if col in data.columns:
                    date_column = col
                    self.logger.info(f"找到日期列用于设置索引: {col}")
                    break

            if date_column is not None:
                # 将日期列转换为datetime类型
                try:
                    data[date_column] = pd.to_datetime(data[date_column])
                    # 设置日期索引
                    data.set_index(date_column, inplace=True)
                    self.logger.info(f"成功将 {date_column} 设置为索引")
                except Exception as e:
                    self.logger.error(f"将 {date_column} 设置为索引时出错: {str(e)}")
                    # 创建一个新的日期列
                    data['date'] = pd.date_range(start=pd.to_datetime(start_date), periods=len(data), freq='D')
                    data.set_index('date', inplace=True)
                    self.logger.info("创建了新的日期索引")
            elif isinstance(data.index, pd.DatetimeIndex):
                # 如果索引已经是DatetimeIndex，不需要额外处理
                self.logger.info("数据索引已经是DatetimeIndex")
            else:
                # 如果没有找到日期列，创建一个日期索引
                self.logger.warning(f"未找到日期列，创建一个基于开始日期的索引")
                data.index = pd.date_range(start=pd.to_datetime(start_date), periods=len(data), freq='D')
                self.logger.info("创建了新的日期索引")

            # 重命名列名为中文
            data = self._standardize_column_names(data)

            # 应用数据清洗
            self.logger.info("开始数据清洗过程...")
            data = self._clean_data(data)
            self.logger.info("数据清洗完成")

            # 时间序列对齐
            self.logger.info("开始时间序列对齐...")
            freq = 'D'  # 日频数据
            if frequency == '周线':
                freq = 'W'
            elif frequency == '月线':
                freq = 'M'
            data = self._align_time_series(data, start_date, end_date, freq=freq)
            self.logger.info("时间序列对齐完成")

            # 设置时区
            if isinstance(data.index, pd.DatetimeIndex) and data.index.tz is None:
                try:
                    data.index = data.index.tz_localize(self.timezone)
                    self.logger.info(f"成功设置时区为 {self.timezone}")
                except Exception as e:
                    self.logger.warning(f"设置时区失败: {str(e)}")

            # 缓存数据
            if use_cache:
                data.to_csv(cache_file)
                self.logger.info(f"数据已缓存: {cache_file}")

            return data

        except Exception as e:
            self.logger.error(f"获取数据失败: {str(e)}")
            raise

    def get_latest_trading_date(self):
        """
        获取最新交易日期

        返回:
            str: 最新交易日期，格式为 'YYYY-MM-DD'
        """
        try:
            # 使用上证指数获取最新交易日
            latest_data = ak.stock_zh_index_daily(symbol="sh000001")
            latest_date = latest_data.iloc[-1]['date']

            # 确保返回的是字符串格式
            if isinstance(latest_date, str):
                # 如果已经是字符串，检查格式是否正确
                try:
                    # 尝试解析日期字符串，然后重新格式化以确保格式一致
                    parsed_date = datetime.strptime(latest_date, '%Y-%m-%d')
                    return parsed_date.strftime('%Y-%m-%d')
                except ValueError:
                    # 如果格式不是 'YYYY-MM-DD'，尝试其他常见格式
                    try:
                        # 尝试解析为 'YYYYMMDD' 格式
                        parsed_date = datetime.strptime(latest_date, '%Y%m%d')
                        return parsed_date.strftime('%Y-%m-%d')
                    except ValueError:
                        # 如果仍然失败，记录错误并返回当前日期
                        self.logger.error(f"无法解析日期字符串: {latest_date}")
                        return datetime.now().strftime('%Y-%m-%d')
            elif isinstance(latest_date, datetime):
                # 如果是 datetime 对象，直接格式化
                return latest_date.strftime('%Y-%m-%d')
            elif isinstance(latest_date, pd.Timestamp):
                # 如果是 pandas Timestamp 对象，转换为字符串
                return latest_date.strftime('%Y-%m-%d')
            elif hasattr(latest_date, 'strftime'):
                # 如果是其他具有 strftime 方法的日期对象
                return latest_date.strftime('%Y-%m-%d')
            else:
                # 如果是其他类型，尝试转换为字符串
                self.logger.warning(f"未知的日期类型: {type(latest_date)}，尝试转换为字符串")
                return str(latest_date)
        except Exception as e:
            self.logger.error(f"获取最新交易日期失败: {str(e)}")
            # 如果失败，返回当前日期
            return datetime.now().strftime('%Y-%m-%d')

    def get_benchmark_data(self, benchmark_code='sh000001', start_date=None, end_date=None):
        """
        获取基准数据（如上证指数）

        参数:
            benchmark_code (str): 基准代码，默认为上证指数 'sh000001'
            start_date (str): 开始日期，格式为 'YYYY-MM-DD'
            end_date (str): 结束日期，格式为 'YYYY-MM-DD'

        返回:
            pandas.DataFrame: 基准数据
        """
        return self.get_stock_data(benchmark_code, start_date, end_date)

    def _generate_cache_key(self, stock_code, start_date, end_date, frequency):
        """
        生成缓存键

        参数:
            stock_code (str): 股票代码
            start_date (str): 开始日期
            end_date (str): 结束日期
            frequency (str): 数据频率

        返回:
            str: 缓存键
        """
        # 创建一个包含所有参数的字符串
        params_str = f"{stock_code}_{start_date}_{end_date}_{frequency}"
        # 使用MD5生成一个固定长度的哈希值
        return hashlib.md5(params_str.encode()).hexdigest()

    def _validate_data(self, data):
        """
        验证数据有效性

        参数:
            data (pandas.DataFrame): 要验证的数据

        返回:
            bool: 数据是否有效
        """
        # 检查数据是否为空
        if data is None or data.empty:
            self.logger.warning("数据为空")
            return False

        # 检查是否包含必要的列
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        # 检查英文列名
        english_columns_present = all(col in data.columns for col in required_columns)
        # 检查中文列名
        chinese_columns = ['开盘', '最高', '最低', '收盘', '成交量']
        chinese_columns_present = all(col in data.columns for col in chinese_columns)

        if not (english_columns_present or chinese_columns_present):
            missing_cols = [col for col in required_columns if col not in data.columns]
            self.logger.warning(f"数据缺少必要的列: {missing_cols}")
            return False

        # 如果启用了数据验证，进行更严格的检查
        if self.data_validation:
            # 使用中文列名或英文列名
            cols = chinese_columns if chinese_columns_present else required_columns

            # 检查价格数据的有效性
            price_data = data[cols[:4]]  # 开盘、最高、最低、收盘

            # 检查是否有负值
            if (price_data < 0).any().any():
                self.logger.warning("数据包含负的价格值")
                return False

            # 检查价格关系是否合理: 最高 >= 开盘, 最高 >= 最低, 最高 >= 收盘, 最低 <= 开盘, 最低 <= 收盘
            high_col = cols[1]  # 最高
            low_col = cols[2]   # 最低
            open_col = cols[0]  # 开盘
            close_col = cols[3] # 收盘

            invalid_high = (data[high_col] < data[open_col]) | (data[high_col] < data[low_col]) | (data[high_col] < data[close_col])
            invalid_low = (data[low_col] > data[open_col]) | (data[low_col] > data[close_col])

            if invalid_high.any() or invalid_low.any():
                invalid_count = invalid_high.sum() + invalid_low.sum()
                total_count = len(data)
                invalid_percent = (invalid_count / total_count) * 100

                # 如果无效数据比例超过5%，则认为数据无效
                if invalid_percent > 5:
                    self.logger.warning(f"数据包含不合理的价格关系，无效数据比例: {invalid_percent:.2f}%")
                    return False
                else:
                    self.logger.warning(f"数据包含少量不合理的价格关系，无效数据比例: {invalid_percent:.2f}%，将在清洗阶段修复")

            # 检查成交量是否为负
            volume_col = cols[4]  # 成交量
            if (data[volume_col] < 0).any():
                self.logger.warning("数据包含负的成交量值")
                return False

        return True

    def _clean_data(self, data):
        """
        清洗数据，处理缺失值和异常值

        参数:
            data (pandas.DataFrame): 原始数据

        返回:
            pandas.DataFrame: 清洗后的数据
        """
        if data is None or data.empty:
            return data

        # 复制数据，避免修改原始数据
        df = data.copy()

        # 确保所有数值列都是浮点数类型
        numeric_columns = df.select_dtypes(include=['number']).columns
        for col in numeric_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')

        # 处理缺失值
        if self.missing_value_strategy != 'none':
            # 记录缺失值数量
            missing_before = df.isna().sum().sum()

            # 应用缺失值处理策略
            if self.missing_value_strategy == 'ffill':
                # 前向填充
                df = df.fillna(method='ffill')
                # 如果仍有缺失值（例如序列开始处），使用后向填充
                df = df.fillna(method='bfill')
            elif self.missing_value_strategy == 'bfill':
                # 后向填充
                df = df.fillna(method='bfill')
                # 如果仍有缺失值（例如序列结束处），使用前向填充
                df = df.fillna(method='ffill')
            elif self.missing_value_strategy == 'mean':
                # 均值填充
                for col in numeric_columns:
                    df[col] = df[col].fillna(df[col].mean())
            elif self.missing_value_strategy == 'median':
                # 中位数填充
                for col in numeric_columns:
                    df[col] = df[col].fillna(df[col].median())

            # 记录处理后的缺失值数量
            missing_after = df.isna().sum().sum()
            if missing_before > 0:
                self.logger.info(f"缺失值处理: 从 {missing_before} 减少到 {missing_after}")

        # 处理异常值
        if self.outlier_detection:
            # 获取价格和成交量列
            price_volume_cols = []
            for col in ['开盘', '最高', '最低', '收盘', '成交量']:
                if col in df.columns:
                    price_volume_cols.append(col)

            # 使用多种方法检测异常值
            for col in price_volume_cols:
                # 初始化异常值标记
                outliers_combined = np.zeros(len(df), dtype=bool)

                # 1. Z-score方法
                try:
                    z_scores = stats.zscore(df[col], nan_policy='omit')
                    outliers_zscore = (abs(z_scores) > 3) & ~np.isnan(z_scores)
                    outliers_combined = outliers_combined | outliers_zscore
                    self.logger.debug(f"Z-score方法在 {col} 列中检测到 {outliers_zscore.sum()} 个异常值")
                except Exception as e:
                    self.logger.warning(f"Z-score异常值检测失败: {str(e)}")

                # 2. IQR方法 (四分位距)
                try:
                    Q1 = df[col].quantile(0.25)
                    Q3 = df[col].quantile(0.75)
                    IQR = Q3 - Q1
                    lower_bound = Q1 - 1.5 * IQR
                    upper_bound = Q3 + 1.5 * IQR
                    outliers_iqr = ((df[col] < lower_bound) | (df[col] > upper_bound)) & ~df[col].isna()
                    outliers_combined = outliers_combined | outliers_iqr
                    self.logger.debug(f"IQR方法在 {col} 列中检测到 {outliers_iqr.sum()} 个异常值")
                except Exception as e:
                    self.logger.warning(f"IQR异常值检测失败: {str(e)}")

                # 3. 移动窗口方法 (局部异常值)
                try:
                    window_size = 20
                    rolling_mean = df[col].rolling(window=window_size, center=True, min_periods=5).mean()
                    rolling_std = df[col].rolling(window=window_size, center=True, min_periods=5).std()
                    outliers_rolling = (abs(df[col] - rolling_mean) > 3 * rolling_std) & ~df[col].isna() & ~rolling_std.isna()
                    outliers_combined = outliers_combined | outliers_rolling
                    self.logger.debug(f"移动窗口方法在 {col} 列中检测到 {outliers_rolling.sum()} 个异常值")
                except Exception as e:
                    self.logger.warning(f"移动窗口异常值检测失败: {str(e)}")

                # 4. 百分位数方法 (极端值)
                try:
                    lower_percentile = df[col].quantile(0.001)  # 0.1%分位数
                    upper_percentile = df[col].quantile(0.999)  # 99.9%分位数
                    outliers_percentile = ((df[col] < lower_percentile) | (df[col] > upper_percentile)) & ~df[col].isna()
                    outliers_combined = outliers_combined | outliers_percentile
                    self.logger.debug(f"百分位数方法在 {col} 列中检测到 {outliers_percentile.sum()} 个异常值")
                except Exception as e:
                    self.logger.warning(f"百分位数异常值检测失败: {str(e)}")

                # 统计异常值总数
                outlier_count = outliers_combined.sum()

                if outlier_count > 0:
                    self.logger.info(f"列 {col} 中检测到 {outlier_count} 个异常值 (占比 {outlier_count/len(df)*100:.2f}%)")

                    # 对于价格列，使用前后值的中位数替换异常值
                    if col in ['开盘', '最高', '最低', '收盘']:
                        # 创建一个滑动窗口，计算局部中位数
                        window_size = 5
                        rolling_median = df[col].rolling(window=window_size, center=True, min_periods=1).median()

                        # 替换异常值
                        df.loc[outliers_combined, col] = rolling_median[outliers_combined]
                        self.logger.info(f"已使用局部中位数替换 {col} 列中的异常值")

                    # 对于成交量列，使用局部平均值替换异常值
                    elif col == '成交量':
                        # 创建一个滑动窗口，计算局部平均值
                        window_size = 5
                        rolling_mean = df[col].rolling(window=window_size, center=True, min_periods=1).mean()

                        # 替换异常值
                        df.loc[outliers_combined, col] = rolling_mean[outliers_combined]
                        self.logger.info(f"已使用局部平均值替换 {col} 列中的异常值")

        # 确保价格关系合理
        if '最高' in df.columns and '最低' in df.columns and '开盘' in df.columns and '收盘' in df.columns:
            # 修复最高价
            df['最高'] = df[['最高', '开盘', '收盘']].max(axis=1)
            # 修复最低价
            df['最低'] = df[['最低', '开盘', '收盘']].min(axis=1)

        # 确保成交量非负
        if '成交量' in df.columns:
            df.loc[df['成交量'] < 0, '成交量'] = 0

        return df

    def _align_time_series(self, data, start_date, end_date, freq='D'):
        """
        确保时间序列在指定的日期范围内连续，处理交易日历问题

        参数:
            data (pandas.DataFrame): 原始数据
            start_date (str): 开始日期
            end_date (str): 结束日期
            freq (str): 频率，'D'表示日频

        返回:
            pandas.DataFrame: 对齐后的数据
        """
        if data is None or data.empty:
            return data

        # 确保数据有日期索引
        if not isinstance(data.index, pd.DatetimeIndex):
            self.logger.warning("数据没有日期索引，无法进行时间序列对齐")
            return data

        # 转换日期字符串为datetime对象
        start_dt = pd.to_datetime(start_date)
        end_dt = pd.to_datetime(end_date)

        # 创建完整的日期范围
        full_date_range = pd.date_range(start=start_dt, end=end_dt, freq=freq)

        # 如果是交易日数据，过滤掉周末
        if freq == 'D':
            # 只保留周一至周五
            full_date_range = full_date_range[full_date_range.dayofweek < 5]

        # 重新索引数据，填充缺失值
        reindexed_data = data.reindex(full_date_range)

        # 应用缺失值处理策略
        if self.missing_value_strategy != 'none':
            if self.missing_value_strategy == 'ffill':
                reindexed_data = reindexed_data.fillna(method='ffill')
                reindexed_data = reindexed_data.fillna(method='bfill')  # 处理开始部分的缺失值
            elif self.missing_value_strategy == 'bfill':
                reindexed_data = reindexed_data.fillna(method='bfill')
                reindexed_data = reindexed_data.fillna(method='ffill')  # 处理结束部分的缺失值
            elif self.missing_value_strategy == 'mean':
                # 对每列应用均值填充
                for col in reindexed_data.columns:
                    if pd.api.types.is_numeric_dtype(reindexed_data[col]):
                        col_mean = data[col].mean()
                        reindexed_data[col] = reindexed_data[col].fillna(col_mean)
            elif self.missing_value_strategy == 'median':
                # 对每列应用中位数填充
                for col in reindexed_data.columns:
                    if pd.api.types.is_numeric_dtype(reindexed_data[col]):
                        col_median = data[col].median()
                        reindexed_data[col] = reindexed_data[col].fillna(col_median)

        return reindexed_data

    def _standardize_column_names(self, data):
        """
        标准化列名为中文

        参数:
            data (pandas.DataFrame): 要标准化的数据

        返回:
            pandas.DataFrame: 标准化后的数据
        """
        # 英文列名到中文列名的映射
        column_mapping = {
            'open': '开盘',
            'high': '最高',
            'low': '最低',
            'close': '收盘',
            'volume': '成交量',
            'amount': '成交额',
            'turn': '换手率',
            'pct_chg': '涨跌幅',
            'date': '日期'
        }

        # 重命名列
        renamed_columns = {}
        for col in data.columns:
            if col in column_mapping:
                renamed_columns[col] = column_mapping[col]

        if renamed_columns:
            data = data.rename(columns=renamed_columns)

        return data

    def _cache_data(self, data, stock_code, start_date, end_date, frequency):
        """
        缓存数据

        参数:
            data (pandas.DataFrame): 要缓存的数据
            stock_code (str): 股票代码
            start_date (str): 开始日期
            end_date (str): 结束日期
            frequency (str): 数据频率
        """
        try:
            # 生成缓存文件名
            cache_key = self._generate_cache_key(stock_code, start_date, end_date, frequency)
            cache_file = os.path.join(self.cache_dir, f"{cache_key}.csv")

            # 保存数据
            data.to_csv(cache_file)
            self.logger.info(f"数据已缓存: {cache_file}")
        except Exception as e:
            self.logger.warning(f"缓存数据失败: {str(e)}")

    def _resample_data(self, data, freq):
        """
        重采样数据到指定频率

        参数:
            data (pandas.DataFrame): 要重采样的数据
            freq (str): 频率，'W'表示周，'M'表示月

        返回:
            pandas.DataFrame: 重采样后的数据
        """
        # 确保数据有日期索引
        if not isinstance(data.index, pd.DatetimeIndex):
            if '日期' in data.columns:
                data['日期'] = pd.to_datetime(data['日期'])
                data.set_index('日期', inplace=True)
            else:
                raise ValueError("数据没有日期列或索引")

        # 定义OHLC聚合方法
        ohlc_dict = {
            '开盘': 'first',
            '最高': 'max',
            '最低': 'min',
            '收盘': 'last',
            '成交量': 'sum'
        }

        # 如果有成交额列，也进行聚合
        if '成交额' in data.columns:
            ohlc_dict['成交额'] = 'sum'

        # 重采样
        resampled = data.resample(freq).agg(ohlc_dict)

        return resampled
