"""
DummyAgent模块 - 用于兼容性测试和错误处理
"""

import logging
import numpy as np

logger = logging.getLogger('drl_trading')

class DummyAgent:
    """
    空智能体类，用于兼容性测试和错误处理
    """
    
    def __init__(self, algorithm="Dummy", **kwargs):
        """初始化空智能体"""
        self.algorithm = algorithm
        self.logger = logger
        self.logger.warning(f"创建空智能体 ({algorithm})，仅用于兼容性测试")
    
    def train(self, env, total_timesteps, **kwargs):
        """模拟训练过程"""
        self.logger.warning("空智能体不支持实际训练，仅返回占位符结果")
        
        # 创建回调函数和调用它
        if 'callback' in kwargs and kwargs['callback'] is not None:
            callback = kwargs['callback']
            
            # 模拟训练步骤
            for i in range(min(100, total_timesteps)):
                if hasattr(callback, 'update'):
                    callback.update({'rewards': [0.1]}, {})
        
        return self
    
    def predict(self, observation, **kwargs):
        """模拟预测动作"""
        self.logger.warning("空智能体不支持实际预测，仅返回随机动作")
        
        # 返回随机动作和状态
        return np.array([0]), None
    
    def save(self, path):
        """模拟保存模型"""
        self.logger.warning(f"空智能体不支持实际保存，路径: {path}")
        return True
    
    def load(self, path):
        """模拟加载模型"""
        self.logger.warning(f"空智能体不支持实际加载，路径: {path}")
        return self 