@echo off
echo ========================================
echo DRL量化交易系统 - UI测试与修复工具
echo ========================================
echo.

REM 激活虚拟环境 (如果存在)
if exist "test_env\Scripts\activate.bat" (
    echo 正在激活虚拟环境...
    call test_env\Scripts\activate.bat
)

echo 正在运行UI测试与修复脚本...
echo.

REM 运行UI测试与修复脚本
python run_all_ui_tests_and_fixes.py

REM 检查运行结果
if %ERRORLEVEL% EQU 0 (
    echo.
    echo ========================================
    echo UI测试与修复成功完成！
    echo 请查看test_reports目录下的报告文件。
    echo ========================================
) else (
    echo.
    echo ========================================
    echo UI测试与修复过程中出现问题。
    echo 请查看logs目录下的日志文件了解详情。
    echo ========================================
)

REM 如果虚拟环境已激活，则退出
if defined VIRTUAL_ENV (
    deactivate
)

echo.
echo 按任意键退出...
pause > nul
