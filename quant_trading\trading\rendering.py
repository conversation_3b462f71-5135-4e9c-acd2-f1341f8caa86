"""
渲染模块
负责渲染交易环境的状态
"""

import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.figure import Figure
from matplotlib.backends.backend_agg import FigureCanvasAgg as FigureCanvas
from enum import Enum

class RenderMode(Enum):
    """渲染模式枚举"""
    HUMAN = 'human'       # 人类可视化模式
    RGB_ARRAY = 'rgb_array'  # RGB数组模式

class Renderer:
    """
    渲染类
    负责渲染交易环境的状态
    """

    def __init__(self, df, logger=None):
        """
        初始化渲染器

        参数:
            df (pandas.DataFrame): 处理好的行情数据和特征
            logger (logging.Logger): 日志记录器
        """
        self.logger = logger or logging.getLogger('drl_trading')
        self.df = df
        self.portfolio_values = []
        self.trades = []

    def render(self, current_step, mode='human'):
        """
        渲染环境状态

        参数:
            current_step (int): 当前时间步
            mode (str): 渲染模式，可选 'human', 'rgb_array'

        返回:
            numpy.ndarray: 如果mode='rgb_array'，返回RGB数组
        """
        if mode not in ['human', 'rgb_array']:
            raise ValueError(f"不支持的渲染模式: {mode}")

        # 创建图表
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8), sharex=True)

        # 绘制价格和交易点
        ax1.set_title('价格和交易点')
        ax1.set_ylabel('价格')

        # 获取可见的数据范围
        start_idx = max(0, current_step - 100)
        end_idx = current_step + 1

        # 绘制价格
        ax1.plot(self.df.index[start_idx:end_idx], self.df['收盘'].iloc[start_idx:end_idx], label='收盘价')

        # 标记交易点
        for trade in self.trades:
            if start_idx <= trade['step'] <= end_idx:
                if trade['action'] == 'buy':
                    ax1.scatter(self.df.index[trade['step']], trade['price'], color='green', marker='^', s=100)
                elif trade['action'] == 'sell':
                    ax1.scatter(self.df.index[trade['step']], trade['price'], color='red', marker='v', s=100)

        # 绘制组合价值
        ax2.set_title('组合价值')
        ax2.set_ylabel('价值')
        ax2.set_xlabel('日期')

        # 确保portfolio_values的长度与绘图范围匹配
        if len(self.portfolio_values) > 0:
            # 计算起始索引
            portfolio_start_idx = max(0, len(self.portfolio_values) - (end_idx - start_idx))
            # 绘制组合价值
            ax2.plot(self.df.index[start_idx:end_idx], self.portfolio_values[portfolio_start_idx:], label='组合价值')

        # 添加图例
        ax1.legend()
        ax2.legend()

        # 调整布局
        plt.tight_layout()

        if mode == 'human':
            # 显示图表
            plt.show()
            return None
        elif mode == 'rgb_array':
            # 将图表转换为RGB数组
            canvas = FigureCanvas(fig)
            canvas.draw()
            width, height = fig.get_size_inches() * fig.get_dpi()
            image = np.frombuffer(canvas.tostring_rgb(), dtype='uint8')
            image = image.reshape(int(height), int(width), 3)
            plt.close(fig)
            return image

    def update_data(self, portfolio_values, trades):
        """
        更新渲染数据

        参数:
            portfolio_values (list): 组合价值历史
            trades (list): 交易记录
        """
        self.portfolio_values = portfolio_values
        self.trades = trades

    def plot_performance(self, portfolio_values, trades, benchmark=None):
        """
        绘制性能图表

        参数:
            portfolio_values (list): 组合价值历史
            trades (list): 交易记录
            benchmark (pandas.Series): 基准收益率，如果为None则不绘制

        返回:
            matplotlib.figure.Figure: 图表对象
        """
        # 创建图表
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 12), gridspec_kw={'height_ratios': [3, 1, 1]})

        # 计算日期范围
        start_idx = self.df.index[0]
        end_idx = self.df.index[-1]

        # 绘制价格和交易点
        ax1.set_title('价格和交易点')
        ax1.set_ylabel('价格')
        ax1.plot(self.df.index, self.df['收盘'], label='收盘价')

        # 标记交易点
        for trade in trades:
            if trade['action'] == 'buy':
                ax1.scatter(self.df.index[trade['step']], trade['price'], color='green', marker='^', s=100)
            elif trade['action'] == 'sell':
                ax1.scatter(self.df.index[trade['step']], trade['price'], color='red', marker='v', s=100)

        # 绘制组合价值
        ax2.set_title('组合价值')
        ax2.set_ylabel('价值')

        # 确保portfolio_values的长度与绘图范围匹配
        if len(portfolio_values) > 0:
            # 绘制组合价值
            ax2.plot(self.df.index[len(self.df) - len(portfolio_values):], portfolio_values, label='组合价值')

        # 计算收益率
        if len(portfolio_values) > 1:
            returns = np.diff(portfolio_values) / portfolio_values[:-1]
            cumulative_returns = np.cumprod(1 + returns) - 1

            # 绘制累计收益率
            ax3.set_title('累计收益率')
            ax3.set_ylabel('收益率')
            ax3.set_xlabel('日期')
            ax3.plot(self.df.index[len(self.df) - len(cumulative_returns):], cumulative_returns, label='策略收益率')

            # 绘制基准收益率
            if benchmark is not None:
                # 确保基准收益率的长度与绘图范围匹配
                if len(benchmark) >= len(cumulative_returns):
                    benchmark_returns = benchmark.pct_change().dropna()
                    benchmark_cumulative_returns = np.cumprod(1 + benchmark_returns) - 1
                    ax3.plot(benchmark_returns.index, benchmark_cumulative_returns, label='基准收益率')

        # 添加图例
        ax1.legend()
        ax2.legend()
        ax3.legend()

        # 调整布局
        plt.tight_layout()

        return fig

    def save_performance_plot(self, portfolio_values, trades, filename, benchmark=None):
        """
        保存性能图表

        参数:
            portfolio_values (list): 组合价值历史
            trades (list): 交易记录
            filename (str): 文件名
            benchmark (pandas.Series): 基准收益率，如果为None则不绘制

        返回:
            bool: 是否成功保存
        """
        try:
            # 绘制性能图表
            fig = self.plot_performance(portfolio_values, trades, benchmark)

            # 保存图表
            fig.savefig(filename, dpi=300, bbox_inches='tight')
            plt.close(fig)

            self.logger.info(f"性能图表已保存到 {filename}")
            return True
        except Exception as e:
            self.logger.error(f"保存性能图表失败: {str(e)}")
            return False
