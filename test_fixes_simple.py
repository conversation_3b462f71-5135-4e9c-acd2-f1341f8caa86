#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
简化版测试修复脚本
用于测试对特征工程配置格式、性能分析器参数处理和GPU检测功能的修复
不依赖项目的导入结构
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import traceback

# 设置日志
os.makedirs('logs', exist_ok=True)
log_file = os.path.join('logs', f'test_fixes_simple_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log')
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('test_fixes_simple')

# 添加项目根目录到系统路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.append(project_root)

def test_gpu_detection():
    """测试GPU检测功能"""
    logger.info("测试GPU检测功能")
    
    try:
        # 直接导入GPU检测函数
        from install_gpu_support import detect_gpu
        
        # 调用GPU检测函数
        gpu_info = detect_gpu()
        
        logger.info(f"GPU检测结果: {gpu_info}")
        
        return {
            'success': True,
            'gpu_info': gpu_info
        }
    except Exception as e:
        logger.error(f"测试GPU检测功能时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            'success': False,
            'error': str(e)
        }

def test_performance_analyzer_direct():
    """直接测试性能分析器参数处理"""
    logger.info("直接测试性能分析器参数处理")
    
    try:
        # 创建模拟交易记录DataFrame
        trades_df = pd.DataFrame({
            'date': pd.date_range(start='2022-01-01', periods=10, freq='D'),
            'action': ['buy', 'sell', 'buy', 'sell', 'buy', 'sell', 'buy', 'sell', 'buy', 'sell'],
            'price': [100, 105, 98, 103, 101, 106, 99, 104, 102, 107],
            'quantity': [10, 10, 15, 15, 20, 20, 25, 25, 30, 30],
            'value': [1000, 1050, 1470, 1545, 2020, 2120, 2475, 2600, 3060, 3210],
            'commission': [1, 1.05, 1.47, 1.545, 2.02, 2.12, 2.475, 2.6, 3.06, 3.21]
        })
        
        # 创建模拟组合价值历史
        portfolio_values = pd.Series(
            [100000 * (1 + 0.001 * i) for i in range(100)],
            index=pd.date_range(start='2022-01-01', periods=100, freq='D')
        )
        
        # 创建简单的性能分析器类
        class SimplePerformanceAnalyzer:
            def analyze(self, trades, portfolio_values):
                # 检查trades是否为DataFrame
                is_dataframe = isinstance(trades, pd.DataFrame)
                logger.info(f"trades是DataFrame: {is_dataframe}")
                
                # 如果是DataFrame，直接使用
                if is_dataframe:
                    trades_df = trades
                else:
                    # 否则转换为DataFrame
                    trades_df = pd.DataFrame(trades) if trades else pd.DataFrame()
                
                # 返回简单的指标
                return {
                    'total_return': (portfolio_values.iloc[-1] / portfolio_values.iloc[0]) - 1,
                    'trade_count': len(trades_df),
                    'is_dataframe_input': is_dataframe
                }
        
        # 创建分析器实例
        analyzer = SimplePerformanceAnalyzer()
        
        # 测试直接传入DataFrame
        logger.info("测试直接传入DataFrame")
        metrics_df = analyzer.analyze(trades_df, portfolio_values)
        
        # 测试传入列表
        logger.info("测试传入列表")
        metrics_list = analyzer.analyze(trades_df.to_dict('records'), portfolio_values)
        
        # 验证结果
        logger.info(f"DataFrame参数结果: {metrics_df}")
        logger.info(f"列表参数结果: {metrics_list}")
        
        # 检查是否正确处理了DataFrame输入
        df_handled_correctly = metrics_df.get('is_dataframe_input', False)
        
        return {
            'success': df_handled_correctly,
            'metrics_df': metrics_df,
            'metrics_list': metrics_list
        }
    except Exception as e:
        logger.error(f"直接测试性能分析器参数处理时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            'success': False,
            'error': str(e)
        }

def main():
    """主函数"""
    logger.info("=" * 50)
    logger.info("开始简化测试修复")
    logger.info("=" * 50)
    
    # 测试GPU检测功能
    gpu_result = test_gpu_detection()
    
    # 直接测试性能分析器参数处理
    performance_result = test_performance_analyzer_direct()
    
    # 输出测试结果
    logger.info("=" * 50)
    logger.info("测试结果:")
    logger.info(f"1. GPU检测功能: {'成功' if gpu_result['success'] else '失败'}")
    if not gpu_result['success']:
        logger.info(f"   错误: {gpu_result.get('error', '未知错误')}")
    
    logger.info(f"2. 性能分析器参数处理: {'成功' if performance_result['success'] else '失败'}")
    if not performance_result['success']:
        logger.info(f"   错误: {performance_result.get('error', '未知错误')}")
    
    logger.info("=" * 50)
    
    # 总结
    all_success = gpu_result['success'] and performance_result['success']
    logger.info(f"修复测试总结: {'全部通过' if all_success else '部分失败'}")
    
    return all_success

if __name__ == "__main__":
    main()
