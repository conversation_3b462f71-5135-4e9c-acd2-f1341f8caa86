2025-05-30 23:29:01,044 - test_fixes - INFO - ==================================================
2025-05-30 23:29:01,044 - test_fixes - INFO - 开始测试修复后的功能
2025-05-30 23:29:01,045 - test_fixes - INFO - ==================================================
2025-05-30 23:29:01,045 - test_fixes - INFO - 测试GPU检测功能
2025-05-30 23:29:01,057 - gpu_support - INFO - 检测GPU支持...
2025-05-30 23:29:01,170 - gpu_support - INFO - 操作系统: Windows
2025-05-30 23:29:01,170 - gpu_support - INFO - 在Windows系统上检测GPU...
2025-05-30 23:29:01,236 - gpu_support - INFO - 检测PyTorch GPU支持...
2025-05-30 23:29:03,869 - gpu_support - INFO - PyTorch可访问的GPU数量: 1
2025-05-30 23:29:03,872 - gpu_support - INFO - PyTorch GPU 0: NVIDIA GeForce RTX 3070 Ti
2025-05-30 23:29:03,872 - gpu_support - INFO - 检测TensorFlow GPU支持...
2025-05-30 23:29:03,873 - gpu_support - INFO - 未安装TensorFlow或导入错误
2025-05-30 23:29:03,873 - gpu_support - INFO - GPU可用: True
2025-05-30 23:29:03,873 - gpu_support - INFO - 设备列表: [{'name': 'NVIDIA GeForce RTX 3070 Ti', 'memory_total': '8192 MiB', 'memory_free': '4983 MiB', 'compute_capability': '8.6'}]
2025-05-30 23:29:03,873 - gpu_support - INFO - CUDA版本: 11.8
2025-05-30 23:29:03,873 - gpu_support - INFO - 驱动版本: 560.94
2025-05-30 23:29:03,874 - gpu_support - INFO - PyTorch GPU支持: True
2025-05-30 23:29:03,874 - gpu_support - INFO - TensorFlow GPU支持: False
2025-05-30 23:29:03,874 - test_fixes - INFO - GPU检测结果: {'available': True, 'devices': [{'name': 'NVIDIA GeForce RTX 3070 Ti', 'memory_total': '8192 MiB', 'memory_free': '4983 MiB', 'compute_capability': '8.6'}], 'cuda_version': '11.8', 'driver_version': '560.94', 'pytorch_gpu': True, 'tensorflow_gpu': False}
2025-05-30 23:29:03,874 - test_fixes - INFO - 测试特征工程适配器
2025-05-30 23:29:06,306 - test_fixes - INFO - 扁平格式配置: {'use_price': True, 'use_volume': True, 'use_technical': True, 'sma_periods': [5, 10, 20, 30, 60], 'ema_periods': [5, 10, 20, 30, 60], 'rsi_periods': [14], 'macd_params': {'fast': 12, 'slow': 26, 'signal': 9}, 'bb_params': {'window': 20, 'num_std': 2}, 'atr_periods': [14], 'normalization': 'zscore'}
2025-05-30 23:29:06,306 - test_fixes - INFO - 转换后的标准格式配置: {'price_features': {'use': True}, 'volume': {'use': True, 'periods': [5, 10, 20]}, 'sma': {'use': True, 'periods': [5, 10, 20, 30, 60]}, 'ema': {'use': True, 'periods': [5, 10, 20, 30, 60]}, 'rsi': {'use': True, 'periods': [14]}, 'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9}, 'bbands': {'use': True, 'period': 20, 'std': 2}, 'atr': {'use': True, 'periods': [14]}, 'normalization': {'use': True, 'method': 'zscore'}, 'technical_indicators': {'use': True}}
2025-05-30 23:29:06,307 - test_fixes - INFO - 标准格式配置: {'price_features': {'use': True}, 'volume': {'use': True, 'periods': [5, 10, 20]}, 'sma': {'use': True, 'periods': [5, 10, 20, 60]}, 'ema': {'use': True, 'periods': [5, 10, 20]}, 'rsi': {'use': True, 'periods': [6, 14, 21]}, 'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9}, 'bbands': {'use': True, 'period': 20, 'std': 2.0}, 'normalization': {'use': True, 'method': 'minmax'}}
2025-05-30 23:29:06,307 - test_fixes - INFO - 适配后的配置: {'price_features': {'use': True}, 'volume': {'use': True, 'periods': [5, 10, 20]}, 'sma': {'use': True, 'periods': [5, 10, 20, 60]}, 'ema': {'use': True, 'periods': [5, 10, 20]}, 'rsi': {'use': True, 'periods': [6, 14, 21]}, 'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9}, 'bbands': {'use': True, 'period': 20, 'std': 2.0}, 'normalization': {'use': True, 'method': 'minmax'}}
2025-05-30 23:29:06,307 - test_fixes - INFO - 测试性能分析器参数处理
2025-05-30 23:29:06,320 - test_fixes - INFO - 测试直接传入DataFrame
2025-05-30 23:29:06,322 - test_fixes - ERROR - 测试性能分析器参数处理时出错: 'float' object is not subscriptable
2025-05-30 23:29:06,324 - test_fixes - ERROR - Traceback (most recent call last):
  File "C:\cursor\量化\test_fixes.py", line 165, in test_performance_analyzer
    metrics_df = analyzer.analyze(trades_df, portfolio_values)
  File "C:\cursor\量化\quant_project\core_logic\performance_analyzer.py", line 73, in analyze
    metrics['max_drawdown'] = drawdown_info['max_drawdown']
                              ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^
TypeError: 'float' object is not subscriptable

2025-05-30 23:29:06,324 - test_fixes - INFO - === 测试数据获取功能 ===
2025-05-30 23:29:06,389 - test_fixes - INFO - 测试数据获取: {'stock_code': 'sh000001', 'start_date': '2020-01-01', 'end_date': '2020-12-31', 'frequency': '日线', 'data_source': '指数'}
2025-05-30 23:29:06,389 - drl_trading - INFO - 获取数据: code=sh000001, freq=D, source=指数
2025-05-30 23:29:06,389 - drl_trading - INFO - 缓存文件不存在: data_cache\c15b165ee622bcd2366ea8205db43b32.csv
2025-05-30 23:29:06,390 - drl_trading - INFO - 从数据源获取数据: sh000001, 2020-01-01 至 2020-12-31, 频率: D
2025-05-30 23:29:06,390 - drl_trading - ERROR - 获取数据失败: 不支持的数据频率: D，目前仅支持 '日线', '周线', '月线'
2025-05-30 23:29:06,390 - drl_trading - WARNING - 使用内部频率 D 获取数据失败: 获取数据失败: 不支持的数据频率: D，目前仅支持 '日线', '周线', '月线'
股票代码格式说明:
- 上海证券交易所股票: sh + 6位数字，如 sh600000
- 深圳证券交易所股票: sz + 6位数字，如 sz000001
，尝试使用原始频率: 日线
2025-05-30 23:29:06,390 - drl_trading - INFO - 从缓存加载数据: data_cache\a8c3892a8f5a2d9bcf50c6153734f9f1.csv
2025-05-30 23:29:06,408 - drl_trading - INFO - 从缓存加载数据成功，共 8409 条记录
2025-05-30 23:29:06,408 - test_fixes - INFO - 成功获取数据: 8409 条记录
2025-05-30 23:29:06,409 - test_fixes - INFO - 数据范围: 1990-12-19 00:00:00 至 2025-05-30 00:00:00
2025-05-30 23:29:06,409 - test_fixes - INFO - 数据列: ['开盘', '收盘', '最高', '最低', '成交量', '成交额']
2025-05-30 23:29:06,409 - test_fixes - INFO - 测试数据获取: {'stock_code': 'sh600000', 'start_date': '2021-01-01', 'end_date': '2021-06-30', 'frequency': '周线', 'data_source': '股票'}
2025-05-30 23:29:06,409 - drl_trading - INFO - 获取数据: code=sh600000, freq=W, source=股票
2025-05-30 23:29:06,410 - drl_trading - INFO - 缓存文件不存在: data_cache\286bf51681d0b8e6c2c23716c10f3f3f.csv
2025-05-30 23:29:06,410 - drl_trading - INFO - 从数据源获取数据: sh600000, 2021-01-01 至 2021-06-30, 频率: W
2025-05-30 23:29:06,410 - drl_trading - ERROR - 获取数据失败: 不支持的数据频率: W，目前仅支持 '日线', '周线', '月线'
2025-05-30 23:29:06,410 - drl_trading - WARNING - 使用内部频率 W 获取数据失败: 获取数据失败: 不支持的数据频率: W，目前仅支持 '日线', '周线', '月线'
股票代码格式说明:
- 上海证券交易所股票: sh + 6位数字，如 sh600000
- 深圳证券交易所股票: sz + 6位数字，如 sz000001
，尝试使用原始频率: 周线
2025-05-30 23:29:06,410 - drl_trading - INFO - 从缓存加载数据: data_cache\a29b0c2df38fe6158765b1683d3b6bbd.csv
2025-05-30 23:29:06,415 - drl_trading - INFO - 从缓存加载数据成功，共 1283 条记录
2025-05-30 23:29:06,415 - test_fixes - INFO - 成功获取数据: 1283 条记录
2025-05-30 23:29:06,415 - test_fixes - INFO - 数据范围: 1999-11-14 00:00:00 至 2025-06-01 00:00:00
2025-05-30 23:29:06,415 - test_fixes - INFO - 数据列: ['开盘', '最高', '最低', '收盘', '成交量', '成交额']
2025-05-30 23:29:06,416 - test_fixes - INFO - 数据获取功能测试通过
2025-05-30 23:29:06,416 - test_fixes - INFO - === 测试因子挖掘功能 ===
2025-05-30 23:29:06,427 - drl_trading - INFO - 获取数据: code=sh000001, freq=D, source=指数
2025-05-30 23:29:06,427 - drl_trading - INFO - 缓存文件不存在: data_cache\c15b165ee622bcd2366ea8205db43b32.csv
2025-05-30 23:29:06,427 - drl_trading - INFO - 从数据源获取数据: sh000001, 2020-01-01 至 2020-12-31, 频率: D
2025-05-30 23:29:06,428 - drl_trading - ERROR - 获取数据失败: 不支持的数据频率: D，目前仅支持 '日线', '周线', '月线'
2025-05-30 23:29:06,428 - drl_trading - WARNING - 使用内部频率 D 获取数据失败: 获取数据失败: 不支持的数据频率: D，目前仅支持 '日线', '周线', '月线'
股票代码格式说明:
- 上海证券交易所股票: sh + 6位数字，如 sh600000
- 深圳证券交易所股票: sz + 6位数字，如 sz000001
，尝试使用原始频率: 日线
2025-05-30 23:29:06,428 - drl_trading - INFO - 从缓存加载数据: data_cache\a8c3892a8f5a2d9bcf50c6153734f9f1.csv
2025-05-30 23:29:06,442 - drl_trading - INFO - 从缓存加载数据成功，共 8409 条记录
2025-05-30 23:29:06,443 - drl_trading - INFO - 开始运行自动因子挖掘流水线...
2025-05-30 23:29:06,443 - test_fixes - INFO - 因子挖掘进度: 初始化 - 0% - 开始因子挖掘流程
2025-05-30 23:29:06,443 - drl_trading - INFO - 初始化 - 0% - 开始因子挖掘流程
2025-05-30 23:29:06,443 - test_fixes - INFO - 因子挖掘进度: 因子生成 - 10% - 开始生成因子
2025-05-30 23:29:06,444 - drl_trading - INFO - 因子生成 - 10% - 开始生成因子
2025-05-30 23:29:06,444 - test_fixes - INFO - 因子挖掘进度: 因子生成 - 15% - 生成技术指标因子
2025-05-30 23:29:06,444 - drl_trading - INFO - 因子生成 - 15% - 生成技术指标因子
2025-05-30 23:29:06,444 - drl_trading - INFO - 生成技术指标因子...
2025-05-30 23:29:06,444 - drl_trading - INFO - 输入数据形状: (8409, 6), 列名: ['开盘', '收盘', '最高', '最低', '成交量', '成交额']
2025-05-30 23:29:06,445 - drl_trading - INFO - 找到列 '开盘' 映射到标准名称 'open'
2025-05-30 23:29:06,445 - drl_trading - INFO - 找到列 '最高' 映射到标准名称 'high'
2025-05-30 23:29:06,445 - drl_trading - INFO - 找到列 '最低' 映射到标准名称 'low'
2025-05-30 23:29:06,445 - drl_trading - INFO - 找到列 '收盘' 映射到标准名称 'close'
2025-05-30 23:29:06,446 - drl_trading - INFO - 找到列 '成交量' 映射到标准名称 'volume'
2025-05-30 23:29:06,446 - drl_trading - INFO - 最终列映射: {'open': '开盘', 'high': '最高', 'low': '最低', 'close': '收盘', 'volume': '成交量'}
2025-05-30 23:29:16,362 - drl_trading - INFO - 成功生成 177 个技术指标因子
2025-05-30 23:29:16,362 - test_fixes - INFO - 因子挖掘进度: 因子生成 - 20% - 生成了 177 个技术指标因子
2025-05-30 23:29:16,362 - drl_trading - INFO - 因子生成 - 20% - 生成了 177 个技术指标因子
2025-05-30 23:29:16,362 - test_fixes - INFO - 因子挖掘进度: 因子生成 - 25% - 生成交叉因子
2025-05-30 23:29:16,363 - drl_trading - INFO - 因子生成 - 25% - 生成交叉因子
2025-05-30 23:29:16,364 - drl_trading - INFO - 成功生成 30 个交叉因子
2025-05-30 23:29:16,364 - test_fixes - INFO - 因子挖掘进度: 因子生成 - 30% - 生成了 30 个交叉因子
2025-05-30 23:29:16,364 - drl_trading - INFO - 因子生成 - 30% - 生成了 30 个交叉因子
2025-05-30 23:29:16,364 - test_fixes - INFO - 因子挖掘进度: 因子生成 - 35% - 生成时间序列因子
2025-05-30 23:29:16,365 - drl_trading - INFO - 因子生成 - 35% - 生成时间序列因子
2025-05-30 23:29:16,382 - drl_trading - INFO - 成功生成 100 个时间序列因子
2025-05-30 23:29:16,382 - test_fixes - INFO - 因子挖掘进度: 因子生成 - 40% - 生成了 100 个时间序列因子
2025-05-30 23:29:16,382 - drl_trading - INFO - 因子生成 - 40% - 生成了 100 个时间序列因子
2025-05-30 23:29:16,383 - test_fixes - INFO - 因子挖掘进度: 因子生成 - 40% - 因子生成完成，共 307 个因子，耗时 9.94 秒
2025-05-30 23:29:16,383 - drl_trading - INFO - 因子生成 - 40% - 因子生成完成，共 307 个因子，耗时 9.94 秒
2025-05-30 23:29:16,383 - test_fixes - INFO - 因子挖掘进度: 因子评估 - 45% - 开始评估因子
2025-05-30 23:29:16,383 - drl_trading - INFO - 因子评估 - 45% - 开始评估因子
2025-05-30 23:29:16,383 - test_fixes - INFO - 因子挖掘进度: 因子评估 - 50% - 评估 307 个因子
2025-05-30 23:29:16,383 - drl_trading - INFO - 因子评估 - 50% - 评估 307 个因子
2025-05-30 23:29:16,383 - drl_trading - INFO - 开始评估 307 个因子...
2025-05-30 23:29:17,192 - drl_trading - INFO - 已评估 10 个因子...
2025-05-30 23:29:18,209 - drl_trading - INFO - 已评估 20 个因子...
2025-05-30 23:29:19,244 - drl_trading - INFO - 已评估 30 个因子...
2025-05-30 23:29:20,286 - drl_trading - INFO - 已评估 40 个因子...
2025-05-30 23:29:20,325 - drl_trading - WARNING - 评估因子 'kama_20' 时出错: Bin edges must be unique: Index([0.0, 0.0, 129.74134483681013, 131.57702224858568, 131.9191473266997,
       134.25],
      dtype='float64', name='factor').
You can drop duplicate edges by setting the 'duplicates' kwarg
2025-05-30 23:29:21,050 - drl_trading - INFO - 已评估 50 个因子...
2025-05-30 23:29:21,824 - drl_trading - INFO - 已评估 60 个因子...
2025-05-30 23:29:22,590 - drl_trading - INFO - 已评估 70 个因子...
2025-05-30 23:29:23,916 - drl_trading - INFO - 已评估 80 个因子...
2025-05-30 23:29:24,716 - drl_trading - INFO - 已评估 90 个因子...
2025-05-30 23:29:25,543 - drl_trading - INFO - 已评估 100 个因子...
2025-05-30 23:29:27,265 - drl_trading - INFO - 已评估 110 个因子...
2025-05-30 23:29:28,093 - drl_trading - INFO - 已评估 120 个因子...
2025-05-30 23:29:29,050 - drl_trading - INFO - 已评估 130 个因子...
2025-05-30 23:29:30,339 - drl_trading - INFO - 已评估 140 个因子...
2025-05-30 23:29:31,490 - drl_trading - INFO - 已评估 150 个因子...
2025-05-30 23:29:33,328 - drl_trading - INFO - 已评估 160 个因子...
2025-05-30 23:29:34,799 - drl_trading - INFO - 已评估 170 个因子...
2025-05-30 23:29:35,626 - drl_trading - INFO - 已评估 180 个因子...
2025-05-30 23:29:37,030 - drl_trading - INFO - 已评估 190 个因子...
2025-05-30 23:29:38,729 - drl_trading - INFO - 已评估 200 个因子...
2025-05-30 23:29:40,451 - drl_trading - INFO - 因子评估完成。评估了 204 个因子，跳过了 103 个因子。
2025-05-30 23:29:40,452 - test_fixes - INFO - 因子挖掘进度: 因子评估 - 60% - 因子评估完成，共评估了 204 个因子，耗时 24.07 秒
2025-05-30 23:29:40,452 - drl_trading - INFO - 因子评估 - 60% - 因子评估完成，共评估了 204 个因子，耗时 24.07 秒
2025-05-30 23:29:40,452 - test_fixes - INFO - 因子挖掘进度: 因子筛选 - 65% - 开始筛选因子
2025-05-30 23:29:40,452 - drl_trading - INFO - 因子筛选 - 65% - 开始筛选因子
2025-05-30 23:29:40,453 - test_fixes - INFO - 因子挖掘进度: 因子筛选 - 70% - 多周期过滤
2025-05-30 23:29:40,453 - drl_trading - INFO - 因子筛选 - 70% - 多周期过滤
2025-05-30 23:29:40,453 - drl_trading - INFO - 开始多周期过滤 (最少有效周期: 3, 有效周期比例: 0.6)...
2025-05-30 23:29:40,453 - drl_trading - INFO - 多周期过滤后剩余 164 个因子
2025-05-30 23:29:40,453 - test_fixes - INFO - 因子挖掘进度: 因子筛选 - 75% - 相关性过滤
2025-05-30 23:29:40,454 - drl_trading - INFO - 因子筛选 - 75% - 相关性过滤
2025-05-30 23:29:40,454 - drl_trading - INFO - 开始移除高度相关因子 (阈值: 0.7, 方法: keep_highest_ic)...
2025-05-30 23:29:40,946 - drl_trading - INFO - 发现 3521 对高度相关因子对
2025-05-30 23:29:40,947 - drl_trading - INFO - 移除了 151 个冗余因子，剩余 13 个因子
2025-05-30 23:29:40,948 - test_fixes - INFO - 因子挖掘进度: 因子筛选 - 80% - 选择最终因子
2025-05-30 23:29:40,948 - drl_trading - INFO - 因子筛选 - 80% - 选择最终因子
2025-05-30 23:29:40,948 - drl_trading - INFO - 选择最佳因子 (top 10, 使用综合得分: True)...
2025-05-30 23:29:40,949 - drl_trading - INFO - 选择了 10 个最佳因子
2025-05-30 23:29:40,949 - test_fixes - INFO - 因子挖掘进度: 因子筛选 - 85% - 因子筛选完成，选择了 10 个最佳因子，耗时 0.50 秒
2025-05-30 23:29:40,949 - drl_trading - INFO - 因子筛选 - 85% - 因子筛选完成，选择了 10 个最佳因子，耗时 0.50 秒
2025-05-30 23:29:40,949 - test_fixes - INFO - 因子挖掘进度: 结果处理 - 90% - 绘制结果
2025-05-30 23:29:40,949 - drl_trading - INFO - 结果处理 - 90% - 绘制结果
2025-05-30 23:29:41,683 - test_fixes - INFO - 因子挖掘进度: 结果处理 - 95% - 保存评估图表到 factor_results\factor_evaluation_20250530_232941.png
2025-05-30 23:29:41,683 - drl_trading - INFO - 结果处理 - 95% - 保存评估图表到 factor_results\factor_evaluation_20250530_232941.png
2025-05-30 23:29:41,683 - test_fixes - INFO - 因子挖掘进度: 完成 - 100% - 因子挖掘成功完成
2025-05-30 23:29:41,684 - drl_trading - INFO - 完成 - 100% - 因子挖掘成功完成
2025-05-30 23:29:41,684 - drl_trading - INFO - 保存因子挖掘结果...
2025-05-30 23:29:41,777 - drl_trading - INFO - 保存因子数据到 factor_results\best_factors_20250530_232941.csv
2025-05-30 23:29:41,779 - drl_trading - INFO - 保存评估摘要到 factor_results\factor_evaluation_20250530_232941.csv
2025-05-30 23:29:41,779 - drl_trading - INFO - 自动因子挖掘流水线完成，共耗时 35.34 秒
2025-05-30 23:29:41,781 - test_fixes - INFO - 成功挖掘出 10 个因子
2025-05-30 23:29:41,782 - test_fixes - INFO - 因子列表: ['kama_10', 'close_sma5_ratio', 'sma_5', 'volume_ratio_20', 'sma_5_to_rsi_14', 'bb_width_20_10', 'rsi_6', 'sma_5_to_volatility_20', 'close_sma60_ratio', 'atr_7']
2025-05-30 23:29:41,782 - test_fixes - INFO - 因子挖掘功能测试通过
2025-05-30 23:29:41,782 - test_fixes - INFO - === 测试DRL智能体功能 ===
2025-05-30 23:29:41,783 - drl_trading - INFO - 初始化DRL智能体
2025-05-30 23:29:41,783 - drl_trading - INFO - 环境配置: None
2025-05-30 23:29:41,783 - drl_trading - INFO - 智能体配置: dict_keys(['algorithm'])
2025-05-30 23:29:41,783 - drl_trading - INFO - 超参数优化配置: None
2025-05-30 23:29:41,783 - drl_trading - WARNING - 创建DRL智能体时出错: 'NoneType' object has no attribute 'columns'，将使用空智能体
2025-05-30 23:29:41,784 - drl_trading - WARNING - 创建空智能体 (PPO)，仅用于兼容性测试
2025-05-30 23:29:41,784 - test_fixes - INFO - DRL智能体算法: PPO
2025-05-30 23:29:41,784 - test_fixes - INFO - 智能体方法: ['__class__', '__delattr__', '__dict__', '__dir__', '__doc__', '__eq__', '__firstlineno__', '__format__', '__ge__', '__getattribute__', '__getstate__', '__gt__', '__hash__', '__init__', '__init_subclass__', '__le__', '__lt__', '__module__', '__ne__', '__new__', '__reduce__', '__reduce_ex__', '__repr__', '__setattr__', '__sizeof__', '__static_attributes__', '__str__', '__subclasshook__', '__weakref__', '_create_agent', 'agent', 'agent_config', 'algorithm', 'env_config', 'load', 'predict', 'save', 'train']
2025-05-30 23:29:41,784 - test_fixes - INFO - DRL智能体功能测试通过
2025-05-30 23:29:41,784 - test_fixes - INFO - ==================================================
2025-05-30 23:29:41,784 - test_fixes - INFO - 测试结果:
2025-05-30 23:29:41,785 - test_fixes - INFO - 1. GPU检测功能: 成功
2025-05-30 23:29:41,785 - test_fixes - INFO - 2. 特征工程适配器: 成功
2025-05-30 23:29:41,785 - test_fixes - INFO - 3. 性能分析器参数处理: 失败
2025-05-30 23:29:41,785 - test_fixes - INFO -    错误: 'float' object is not subscriptable
2025-05-30 23:29:41,785 - test_fixes - INFO - 4. 数据获取功能: 成功
2025-05-30 23:29:41,785 - test_fixes - INFO - 5. 因子挖掘功能: 成功
2025-05-30 23:29:41,785 - test_fixes - INFO - 6. DRL智能体功能: 成功
2025-05-30 23:29:41,785 - test_fixes - INFO - ==================================================
2025-05-30 23:29:41,786 - test_fixes - INFO - 修复测试总结: 部分失败
