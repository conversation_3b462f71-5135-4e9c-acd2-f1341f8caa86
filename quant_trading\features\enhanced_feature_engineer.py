"""
增强特征工程模块
实现高级特征工程，包括因子分析、特征选择和特征重要性评估
符合顶尖量化基金的最佳实践，确保特征的可解释性和预测能力
严格避免前视偏差和数据泄露
"""

import logging
import pandas as pd
import numpy as np
import ta
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler, PowerTransformer
from sklearn.decomposition import PCA
from sklearn.feature_selection import SelectKBest, f_regression, mutual_info_regression, RFE
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import Lasso, Ridge
import statsmodels.api as sm
from scipy import stats
from statsmodels.tsa.stattools import adfuller, kpss
import warnings
from quant_trading.utils.common import normalize_data

# 忽略特定警告
warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", category=RuntimeWarning)

class EnhancedFeatureEngineer:
    """
    增强特征工程类
    实现高级特征工程，包括因子分析、特征选择和特征重要性评估
    严格避免前视偏差和数据泄露，确保特征的可解释性和预测能力
    """

    def __init__(self, feature_config=None, avoid_lookahead=True, check_stationarity=True,
                 feature_selection_method='combined', normalization_method='robust',
                 rolling_window_type='expanding', max_features=50):
        """
        初始化特征工程器

        参数:
            feature_config (dict): 特征配置字典，指定要计算的技术指标及其参数
            avoid_lookahead (bool): 是否严格避免前视偏差
            check_stationarity (bool): 是否检查特征平稳性
            feature_selection_method (str): 特征选择方法，可选'filter'(过滤法),'wrapper'(包裹法),'embedded'(嵌入法),'combined'(组合法)
            normalization_method (str): 归一化方法，可选'minmax','standard','robust','yeo-johnson'
            rolling_window_type (str): 滚动窗口类型，可选'fixed'(固定窗口),'expanding'(扩展窗口)
            max_features (int): 最大特征数量
        """
        self.logger = logging.getLogger('drl_trading')
        self.feature_config = feature_config or {}
        self.avoid_lookahead = avoid_lookahead
        self.check_stationarity = check_stationarity
        self.feature_selection_method = feature_selection_method
        self.normalization_method = normalization_method
        self.rolling_window_type = rolling_window_type
        self.max_features = max_features

        # 特征相关属性
        self.feature_importance = {}
        self.selected_features = []
        self.feature_stationarity = {}
        self.scaler = None
        self.pca_model = None
        self.feature_groups = {
            'price': [],           # 价格相关特征
            'volume': [],          # 成交量相关特征
            'momentum': [],        # 动量类特征
            'volatility': [],      # 波动率类特征
            'trend': [],           # 趋势类特征
            'oscillator': [],      # 震荡指标类特征
            'pattern': [],         # 形态类特征
            'fundamental': [],     # 基本面特征
            'cross_asset': [],     # 跨资产特征
            'time': [],            # 时间特征
            'statistical': []      # 统计特征
        }

    def generate_features(self, data, apply_selection=False):
        """
        生成特征

        参数:
            data (pandas.DataFrame): 原始行情数据，必须包含OHLCV列
            apply_selection (bool): 是否应用特征选择

        返回:
            pandas.DataFrame: 添加了特征的数据
        """
        # 复制数据，避免修改原始数据
        df = data.copy()

        # 确保列名标准化
        df = self._ensure_column_names(df)

        # 重置特征组
        for group in self.feature_groups:
            self.feature_groups[group] = []

        # 计算基本价格特征
        self.logger.info("计算基本价格特征...")
        df = self._calculate_price_features(df)

        # 将价格特征添加到价格组
        price_features = [col for col in df.columns if col.startswith('价格') or '涨跌幅' in col or '波动率' in col]
        self.feature_groups['price'].extend(price_features)

        # 根据配置计算技术指标
        self.logger.info("计算技术指标...")
        df = self._calculate_technical_indicators(df)

        # 将技术指标分类到不同组
        for col in df.columns:
            if col in ['开盘', '最高', '最低', '收盘', '成交量', '成交额']:
                continue

            if 'SMA' in col or 'EMA' in col or 'MACD' in col or 'BBands' in col:
                self.feature_groups['trend'].append(col)
            elif 'RSI' in col or 'Stoch' in col:
                self.feature_groups['oscillator'].append(col)
            elif 'ATR' in col or 'Volatility' in col:
                self.feature_groups['volatility'].append(col)
            elif 'Volume' in col or 'OBV' in col or 'VWAP' in col:
                self.feature_groups['volume'].append(col)
            elif '动量' in col or 'Momentum' in col:
                self.feature_groups['momentum'].append(col)

        # 计算统计特征
        self.logger.info("计算统计特征...")
        df = self._calculate_statistical_features(df)

        # 将统计特征添加到统计组
        stat_features = [col for col in df.columns if 'Rolling' in col or 'ZScore' in col or 'Autocorr' in col or 'Hurst' in col]
        self.feature_groups['statistical'].extend(stat_features)

        # 计算高级特征
        self.logger.info("计算高级特征...")
        df = self._calculate_advanced_features(df)

        # 将高级特征分类到不同组
        for col in df.columns:
            if col.startswith('Alpha'):
                self.feature_groups['fundamental'].append(col)
            elif '高低价比率' in col or '日内价格范围' in col or '收盘价位置' in col:
                self.feature_groups['pattern'].append(col)
            elif '星期几' in col or '月份' in col or '月初' in col or '月末' in col:
                self.feature_groups['time'].append(col)

        # 计算跨资产特征（如果配置中提供了其他资产数据）
        if self.feature_config.get('cross_asset', {}).get('use', False):
            self.logger.info("计算跨资产特征...")
            df = self._calculate_cross_asset_features(df)

            # 将跨资产特征添加到跨资产组
            cross_features = [col for col in df.columns if '相关性' in col or '相对强度' in col or '价差' in col]
            self.feature_groups['cross_asset'].extend(cross_features)

        # 检查特征平稳性并进行必要的转换
        if self.check_stationarity:
            self.logger.info("检查特征平稳性...")
            df, stationarity_results = self._check_stationarity(df)

            # 记录平稳性检查结果
            stationary_count = sum(1 for result in stationarity_results.values() if result.get('stationary', False))
            non_stationary_count = len(stationarity_results) - stationary_count
            self.logger.info(f"平稳性检查结果: {stationary_count} 个平稳特征, {non_stationary_count} 个非平稳特征")

        # 对特征进行变换，使其更符合正态分布
        if self.feature_config.get('transform_features', {}).get('use', False):
            self.logger.info("对特征进行变换...")
            df = self._transform_features(df)

        # 特征选择（如果启用）
        if apply_selection and (self.feature_config.get('feature_selection', {}).get('use', False) or self.feature_selection_method != 'none'):
            self.logger.info("应用特征选择...")
            df = self._apply_feature_selection(df)

        # 在归一化前，确保没有无穷大值
        self.logger.info("检查并处理无穷大值...")
        for col in df.columns:
            if pd.api.types.is_numeric_dtype(df[col]):
                # 替换无穷大值为NaN
                inf_count = np.isinf(df[col]).sum()
                if inf_count > 0:
                    self.logger.warning(f"列 {col} 中发现 {inf_count} 个无穷大值，已替换为NaN")
                    df[col] = df[col].replace([np.inf, -np.inf], np.nan)

                # 检查是否有极大值
                if not df[col].isna().all():
                    mean = df[col].mean()
                    std = df[col].std()
                    if not np.isnan(mean) and not np.isnan(std) and std > 0:
                        # 将超过均值±10倍标准差的值视为异常值，替换为NaN
                        extreme_high = (df[col] > mean + 10*std)
                        extreme_low = (df[col] < mean - 10*std)
                        extreme_count = extreme_high.sum() + extreme_low.sum()
                        if extreme_count > 0:
                            self.logger.warning(f"列 {col} 中发现 {extreme_count} 个极端值，已替换为NaN")
                            df.loc[extreme_high | extreme_low, col] = np.nan

        # 归一化特征
        self.logger.info("归一化特征...")
        try:
            df = self._normalize_features(df)
        except Exception as e:
            self.logger.error(f"归一化特征失败: {str(e)}，跳过归一化步骤")
            # 如果归一化失败，至少确保数据类型正确
            for col in df.columns:
                if pd.api.types.is_numeric_dtype(df[col]):
                    df[col] = pd.to_numeric(df[col], errors='coerce')

        # 检查是否有NaN值，并进行处理
        nan_count = df.isna().sum().sum()
        if nan_count > 0:
            self.logger.warning(f"特征中存在 {nan_count} 个NaN值，使用前向填充处理")
            # 使用前向填充和后向填充处理NaN值
            df = df.fillna(method='ffill').fillna(method='bfill')

            # 如果仍有NaN值（例如整列都是NaN），用0填充
            remaining_nan = df.isna().sum().sum()
            if remaining_nan > 0:
                self.logger.warning(f"前向和后向填充后仍有 {remaining_nan} 个NaN值，用0填充")
                df = df.fillna(0)

        # 最后检查一次是否还有无穷大值
        inf_count = np.isinf(df.values).sum()
        if inf_count > 0:
            self.logger.warning(f"最终检查发现 {inf_count} 个无穷大值，替换为0")
            df = df.replace([np.inf, -np.inf], 0)

        self.logger.info(f"特征生成完成，共 {len(df.columns)} 个特征")
        return df

    def _ensure_column_names(self, df):
        """确保列名标准化"""
        # 英文列名到中文列名的映射
        column_mapping = {
            'open': '开盘',
            'high': '最高',
            'low': '最低',
            'close': '收盘',
            'volume': '成交量'
        }

        # 检查并重命名列
        for eng, chn in column_mapping.items():
            if eng in df.columns and chn not in df.columns:
                df[chn] = df[eng]

        # 确保必要的列存在
        required_columns = ['开盘', '最高', '最低', '收盘', '成交量']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            self.logger.error(f"数据缺少必要的列: {missing_columns}")
            raise ValueError(f"数据缺少必要的列: {missing_columns}")

        return df

    def _calculate_price_features(self, df):
        """计算基本价格特征"""
        # 确保价格列是数值类型
        price_columns = ['开盘', '最高', '最低', '收盘', '成交量']
        for col in price_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # 检查是否有无穷大或极大值
        for col in price_columns:
            if col in df.columns:
                # 替换无穷大值为NaN
                df[col] = df[col].replace([np.inf, -np.inf], np.nan)
                # 检查是否有极大值
                mean = df[col].mean()
                std = df[col].std()
                if not np.isnan(mean) and not np.isnan(std):
                    # 将超过均值±10倍标准差的值视为异常值，替换为NaN
                    df.loc[df[col] > mean + 10*std, col] = np.nan
                    df.loc[df[col] < mean - 10*std, col] = np.nan

        # 计算涨跌幅
        if '涨跌幅' not in df.columns:
            try:
                df['涨跌幅'] = df['收盘'].pct_change()
                # 限制涨跌幅的范围，避免极端值
                df['涨跌幅'] = df['涨跌幅'].clip(-0.5, 0.5)
            except Exception as e:
                self.logger.warning(f"计算涨跌幅失败: {str(e)}")
                df['涨跌幅'] = 0

        # 计算对数收益率（更符合正态分布）
        try:
            # 确保收盘价为正值
            positive_close = df['收盘'].replace(0, np.nan)
            shifted_close = positive_close.shift(1).replace(0, np.nan)
            # 计算对数收益率，并处理可能的无穷大值
            log_returns = np.log(positive_close / shifted_close)
            df['对数收益率'] = log_returns.replace([np.inf, -np.inf], np.nan).clip(-0.5, 0.5)
        except Exception as e:
            self.logger.warning(f"计算对数收益率失败: {str(e)}")
            df['对数收益率'] = 0

        # 计算真实波动幅度 (True Range)
        try:
            # 使用ta库计算TR
            df['TR'] = ta.volatility.average_true_range(df['最高'], df['最低'], df['收盘'], window=1, fillna=True)
        except Exception as e:
            self.logger.warning(f"计算TR失败: {str(e)}，使用简单方法计算")
            try:
                # 使用简单方法计算TR
                high_low = df['最高'] - df['最低']
                high_close = (df['最高'] - df['收盘'].shift(1)).abs()
                low_close = (df['最低'] - df['收盘'].shift(1)).abs()
                df['TR'] = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
                # 处理可能的无穷大值
                df['TR'] = df['TR'].replace([np.inf, -np.inf], np.nan)
            except Exception as e2:
                self.logger.warning(f"简单方法计算TR也失败: {str(e2)}")
                df['TR'] = df['最高'] - df['最低']  # 最简单的替代方案

        # 计算收盘价相对开盘价的变化
        try:
            # 确保开盘价不为0
            non_zero_open = df['开盘'].replace(0, np.nan)
            df['日内涨跌幅'] = (df['收盘'] - df['开盘']) / non_zero_open
            # 限制范围，避免极端值
            df['日内涨跌幅'] = df['日内涨跌幅'].replace([np.inf, -np.inf], np.nan).clip(-0.5, 0.5)
        except Exception as e:
            self.logger.warning(f"计算日内涨跌幅失败: {str(e)}")
            df['日内涨跌幅'] = 0

        # 计算高低价差占开盘价的比例
        try:
            # 确保开盘价不为0
            non_zero_open = df['开盘'].replace(0, np.nan)
            df['日内波动率'] = (df['最高'] - df['最低']) / non_zero_open
            # 限制范围，避免极端值
            df['日内波动率'] = df['日内波动率'].replace([np.inf, -np.inf], np.nan).clip(0, 0.5)
        except Exception as e:
            self.logger.warning(f"计算日内波动率失败: {str(e)}")
            df['日内波动率'] = 0

        # 计算价格动量
        for period in [1, 3, 5, 10, 20]:
            try:
                df[f'价格动量_{period}'] = df['收盘'].pct_change(period)
                # 限制范围，避免极端值
                df[f'价格动量_{period}'] = df[f'价格动量_{period}'].replace([np.inf, -np.inf], np.nan).clip(-0.5, 0.5)
            except Exception as e:
                self.logger.warning(f"计算价格动量_{period}失败: {str(e)}")
                df[f'价格动量_{period}'] = 0

        # 计算价格加速度（动量的变化率）
        for period in [5, 10]:
            try:
                df[f'价格加速度_{period}'] = df[f'价格动量_{period}'].diff()
                # 限制范围，避免极端值
                df[f'价格加速度_{period}'] = df[f'价格加速度_{period}'].replace([np.inf, -np.inf], np.nan).clip(-0.2, 0.2)
            except Exception as e:
                self.logger.warning(f"计算价格加速度_{period}失败: {str(e)}")
                df[f'价格加速度_{period}'] = 0

        return df

    def _calculate_technical_indicators(self, df):
        """计算技术指标"""
        # 简单移动平均线 (SMA)
        if self.feature_config.get('sma', {}).get('use', True):
            periods = self.feature_config.get('sma', {}).get('periods', [5, 10, 20, 60, 120])
            for period in periods:
                try:
                    df[f'SMA_{period}'] = ta.trend.sma_indicator(df['收盘'], window=period, fillna=True)
                    # 处理可能的无穷大值
                    df[f'SMA_{period}'] = df[f'SMA_{period}'].replace([np.inf, -np.inf], np.nan)

                    # 计算价格相对SMA的位置
                    try:
                        # 确保SMA不为0
                        non_zero_sma = df[f'SMA_{period}'].replace(0, np.nan)
                        df[f'收盘/SMA_{period}'] = df['收盘'] / non_zero_sma
                        # 限制范围，避免极端值
                        df[f'收盘/SMA_{period}'] = df[f'收盘/SMA_{period}'].replace([np.inf, -np.inf], np.nan).clip(0.5, 1.5)
                    except Exception as e:
                        self.logger.warning(f"计算收盘/SMA_{period}失败: {str(e)}")
                        df[f'收盘/SMA_{period}'] = 1.0

                    # 计算SMA斜率
                    try:
                        df[f'SMA_{period}_斜率'] = df[f'SMA_{period}'].pct_change(5)
                        # 限制范围，避免极端值
                        df[f'SMA_{period}_斜率'] = df[f'SMA_{period}_斜率'].replace([np.inf, -np.inf], np.nan).clip(-0.2, 0.2)
                    except Exception as e:
                        self.logger.warning(f"计算SMA_{period}_斜率失败: {str(e)}")
                        df[f'SMA_{period}_斜率'] = 0
                except Exception as e:
                    self.logger.warning(f"计算SMA_{period}失败: {str(e)}")
                    # 使用简单的移动平均作为替代
                    df[f'SMA_{period}'] = df['收盘'].rolling(window=period, min_periods=1).mean()
                    df[f'收盘/SMA_{period}'] = 1.0
                    df[f'SMA_{period}_斜率'] = 0

        # 指数移动平均线 (EMA)
        if self.feature_config.get('ema', {}).get('use', True):
            periods = self.feature_config.get('ema', {}).get('periods', [5, 10, 20, 60])
            for period in periods:
                try:
                    df[f'EMA_{period}'] = ta.trend.ema_indicator(df['收盘'], window=period, fillna=True)
                    # 处理可能的无穷大值
                    df[f'EMA_{period}'] = df[f'EMA_{period}'].replace([np.inf, -np.inf], np.nan)

                    # 计算EMA交叉信号
                    if period > 5 and f'EMA_5' in df.columns:
                        try:
                            # 确保EMA不为0
                            non_zero_ema = df[f'EMA_{period}'].replace(0, np.nan)
                            df[f'EMA_5_{period}_交叉'] = (df['EMA_5'] - df[f'EMA_{period}']) / non_zero_ema
                            # 限制范围，避免极端值
                            df[f'EMA_5_{period}_交叉'] = df[f'EMA_5_{period}_交叉'].replace([np.inf, -np.inf], np.nan).clip(-0.2, 0.2)
                        except Exception as e:
                            self.logger.warning(f"计算EMA_5_{period}_交叉失败: {str(e)}")
                            df[f'EMA_5_{period}_交叉'] = 0
                except Exception as e:
                    self.logger.warning(f"计算EMA_{period}失败: {str(e)}")
                    # 使用简单的移动平均作为替代
                    df[f'EMA_{period}'] = df['收盘'].ewm(span=period, min_periods=1).mean()
                    if period > 5:
                        df[f'EMA_5_{period}_交叉'] = 0

        # 相对强弱指标 (RSI)
        if self.feature_config.get('rsi', {}).get('use', True):
            periods = self.feature_config.get('rsi', {}).get('periods', [6, 14, 21])
            for period in periods:
                try:
                    df[f'RSI_{period}'] = ta.momentum.rsi(df['收盘'], window=period, fillna=True)
                    # 处理可能的无穷大值和极端值
                    df[f'RSI_{period}'] = df[f'RSI_{period}'].replace([np.inf, -np.inf], np.nan).clip(0, 100)

                    # RSI动量
                    try:
                        df[f'RSI_{period}_动量'] = df[f'RSI_{period}'].diff(3)
                        # 限制范围，避免极端值
                        df[f'RSI_{period}_动量'] = df[f'RSI_{period}_动量'].replace([np.inf, -np.inf], np.nan).clip(-30, 30)
                    except Exception as e:
                        self.logger.warning(f"计算RSI_{period}_动量失败: {str(e)}")
                        df[f'RSI_{period}_动量'] = 0
                except Exception as e:
                    self.logger.warning(f"计算RSI_{period}失败: {str(e)}")
                    # 使用默认值作为替代
                    df[f'RSI_{period}'] = 50
                    df[f'RSI_{period}_动量'] = 0

        # MACD
        if self.feature_config.get('macd', {}).get('use', True):
            fast = self.feature_config.get('macd', {}).get('fast', 12)
            slow = self.feature_config.get('macd', {}).get('slow', 26)
            signal = self.feature_config.get('macd', {}).get('signal', 9)

            try:
                # 使用ta库计算MACD
                df[f'MACD_{fast}_{slow}'] = ta.trend.macd(df['收盘'], window_slow=slow, window_fast=fast, fillna=True)
                df[f'MACD_Signal_{signal}'] = ta.trend.macd_signal(df['收盘'], window_slow=slow, window_fast=fast, window_sign=signal, fillna=True)
                df[f'MACD_Hist'] = ta.trend.macd_diff(df['收盘'], window_slow=slow, window_fast=fast, window_sign=signal, fillna=True)

                # 处理可能的无穷大值
                df[f'MACD_{fast}_{slow}'] = df[f'MACD_{fast}_{slow}'].replace([np.inf, -np.inf], np.nan)
                df[f'MACD_Signal_{signal}'] = df[f'MACD_Signal_{signal}'].replace([np.inf, -np.inf], np.nan)
                df[f'MACD_Hist'] = df[f'MACD_Hist'].replace([np.inf, -np.inf], np.nan)

                # 限制范围，避免极端值
                macd_std = df[f'MACD_{fast}_{slow}'].std()
                if not np.isnan(macd_std) and macd_std > 0:
                    df[f'MACD_{fast}_{slow}'] = df[f'MACD_{fast}_{slow}'].clip(-5*macd_std, 5*macd_std)
                    df[f'MACD_Signal_{signal}'] = df[f'MACD_Signal_{signal}'].clip(-5*macd_std, 5*macd_std)
                    df[f'MACD_Hist'] = df[f'MACD_Hist'].clip(-5*macd_std, 5*macd_std)

                # MACD柱状图变化率
                try:
                    df['MACD_Hist_变化率'] = df['MACD_Hist'].pct_change()
                    # 限制范围，避免极端值
                    df['MACD_Hist_变化率'] = df['MACD_Hist_变化率'].replace([np.inf, -np.inf], np.nan).clip(-5, 5)
                except Exception as e:
                    self.logger.warning(f"计算MACD_Hist_变化率失败: {str(e)}")
                    df['MACD_Hist_变化率'] = 0
            except Exception as e:
                self.logger.warning(f"计算MACD失败: {str(e)}")
                # 使用默认值作为替代
                df[f'MACD_{fast}_{slow}'] = 0
                df[f'MACD_Signal_{signal}'] = 0
                df[f'MACD_Hist'] = 0
                df['MACD_Hist_变化率'] = 0

        # 布林带
        if self.feature_config.get('bbands', {}).get('use', True):
            periods = self.feature_config.get('bbands', {}).get('periods', [20, 40])
            stds = self.feature_config.get('bbands', {}).get('stds', [2.0, 3.0])
            for period in periods:
                for std in stds:
                    try:
                        # 使用ta库计算布林带
                        df[f'BBands_Upper_{period}_{std}'] = ta.volatility.bollinger_hband(df['收盘'], window=period, window_dev=std, fillna=True)
                        df[f'BBands_Middle_{period}_{std}'] = ta.volatility.bollinger_mavg(df['收盘'], window=period, fillna=True)
                        df[f'BBands_Lower_{period}_{std}'] = ta.volatility.bollinger_lband(df['收盘'], window=period, window_dev=std, fillna=True)

                        # 处理可能的无穷大值
                        df[f'BBands_Upper_{period}_{std}'] = df[f'BBands_Upper_{period}_{std}'].replace([np.inf, -np.inf], np.nan)
                        df[f'BBands_Middle_{period}_{std}'] = df[f'BBands_Middle_{period}_{std}'].replace([np.inf, -np.inf], np.nan)
                        df[f'BBands_Lower_{period}_{std}'] = df[f'BBands_Lower_{period}_{std}'].replace([np.inf, -np.inf], np.nan)

                        # 计算价格在布林带中的位置 (0-1)
                        try:
                            upper = df[f'BBands_Upper_{period}_{std}']
                            lower = df[f'BBands_Lower_{period}_{std}']
                            # 确保分母不为0
                            band_width = (upper - lower).replace(0, np.nan)
                            df[f'BBands_Position_{period}_{std}'] = (df['收盘'] - lower) / (band_width + 1e-10)
                            # 限制范围，避免极端值
                            df[f'BBands_Position_{period}_{std}'] = df[f'BBands_Position_{period}_{std}'].replace([np.inf, -np.inf], np.nan).clip(0, 1)
                        except Exception as e:
                            self.logger.warning(f"计算BBands_Position_{period}_{std}失败: {str(e)}")
                            df[f'BBands_Position_{period}_{std}'] = 0.5

                        # 布林带宽度
                        try:
                            middle = df[f'BBands_Middle_{period}_{std}']
                            # 确保分母不为0
                            non_zero_middle = middle.replace(0, np.nan)
                            df[f'BBands_Width_{period}_{std}'] = (upper - lower) / non_zero_middle
                            # 限制范围，避免极端值
                            df[f'BBands_Width_{period}_{std}'] = df[f'BBands_Width_{period}_{std}'].replace([np.inf, -np.inf], np.nan).clip(0, 0.5)
                        except Exception as e:
                            self.logger.warning(f"计算BBands_Width_{period}_{std}失败: {str(e)}")
                            df[f'BBands_Width_{period}_{std}'] = 0.1
                    except Exception as e:
                        self.logger.warning(f"计算布林带 period={period}, std={std} 失败: {str(e)}")
                        # 使用简单的移动平均和标准差作为替代
                        ma = df['收盘'].rolling(window=period, min_periods=1).mean()
                        std_dev = df['收盘'].rolling(window=period, min_periods=1).std()
                        df[f'BBands_Middle_{period}_{std}'] = ma
                        df[f'BBands_Upper_{period}_{std}'] = ma + std * std_dev
                        df[f'BBands_Lower_{period}_{std}'] = ma - std * std_dev
                        df[f'BBands_Position_{period}_{std}'] = 0.5
                        df[f'BBands_Width_{period}_{std}'] = 0.1

        # 平均真实波动幅度 (ATR)
        if self.feature_config.get('atr', {}).get('use', True):
            periods = self.feature_config.get('atr', {}).get('periods', [7, 14, 21])
            for period in periods:
                try:
                    # 使用ta库计算ATR
                    df[f'ATR_{period}'] = ta.volatility.average_true_range(
                        df['最高'],
                        df['最低'],
                        df['收盘'],
                        window=period,
                        fillna=True
                    )

                    # 处理可能的无穷大值
                    df[f'ATR_{period}'] = df[f'ATR_{period}'].replace([np.inf, -np.inf], np.nan)

                    # 计算ATR占收盘价的比例
                    try:
                        # 确保收盘价不为0
                        non_zero_close = df['收盘'].replace(0, np.nan)
                        df[f'ATR_{period}_Pct'] = df[f'ATR_{period}'] / non_zero_close
                        # 限制范围，避免极端值
                        df[f'ATR_{period}_Pct'] = df[f'ATR_{period}_Pct'].replace([np.inf, -np.inf], np.nan).clip(0, 0.2)
                    except Exception as e:
                        self.logger.warning(f"计算ATR_{period}_Pct失败: {str(e)}")
                        df[f'ATR_{period}_Pct'] = 0.01

                    # 计算标准化ATR
                    try:
                        # 计算ATR的滚动平均
                        atr_mean = df[f'ATR_{period}'].rolling(window=period*2, min_periods=1).mean()
                        # 确保分母不为0
                        non_zero_atr_mean = atr_mean.replace(0, np.nan)
                        df[f'ATR_{period}_标准化'] = df[f'ATR_{period}'] / non_zero_atr_mean
                        # 限制范围，避免极端值
                        df[f'ATR_{period}_标准化'] = df[f'ATR_{period}_标准化'].replace([np.inf, -np.inf], np.nan).clip(0, 3)
                    except Exception as e:
                        self.logger.warning(f"计算ATR_{period}_标准化失败: {str(e)}")
                        df[f'ATR_{period}_标准化'] = 1.0
                except Exception as e:
                    self.logger.warning(f"计算ATR_{period}失败: {str(e)}")
                    # 使用简单的波动率作为替代
                    df[f'ATR_{period}'] = df['最高'] - df['最低']
                    df[f'ATR_{period}_Pct'] = 0.01
                    df[f'ATR_{period}_标准化'] = 1.0

        # 随机指标 (Stochastic)
        if self.feature_config.get('stoch', {}).get('use', True):
            k_periods = self.feature_config.get('stoch', {}).get('k_periods', [9, 14])
            d_periods = self.feature_config.get('stoch', {}).get('d_periods', [3, 5])
            for k_period in k_periods:
                # 使用ta库计算随机指标
                df[f'Stoch_K_{k_period}'] = ta.momentum.stoch(df['最高'], df['最低'], df['收盘'], window=k_period, smooth_window=3, fillna=True)

                for d_period in d_periods:
                    df[f'Stoch_D_{d_period}'] = ta.momentum.stoch_signal(df['最高'], df['最低'], df['收盘'], window=k_period, smooth_window=3, fillna=True)

                    # K线与D线交叉信号
                    if f'Stoch_K_{k_period}' in df.columns and f'Stoch_D_{d_period}' in df.columns:
                        df[f'Stoch_KD_Cross_{k_period}_{d_period}'] = df[f'Stoch_K_{k_period}'] - df[f'Stoch_D_{d_period}']

        # 成交量指标
        if self.feature_config.get('volume', {}).get('use', True):
            # 成交量移动平均
            periods = self.feature_config.get('volume', {}).get('periods', [5, 10, 20, 60])
            for period in periods:
                df[f'Volume_SMA_{period}'] = ta.trend.sma_indicator(df['成交量'], window=period, fillna=True)
                # 计算成交量相对其移动平均的比例
                df[f'Volume/SMA_{period}'] = df['成交量'] / df[f'Volume_SMA_{period}']

            # 成交量变化率
            df['Volume_Change'] = df['成交量'].pct_change()
            # 成交量波动率
            df['Volume_Volatility'] = df['成交量'].rolling(window=20).std() / df['成交量'].rolling(window=20).mean()

            # 能量潮指标 (OBV)
            df['OBV'] = ta.volume.on_balance_volume(df['收盘'], df['成交量'], fillna=True)
            # OBV动量
            df['OBV_动量'] = df['OBV'].diff(5) / df['OBV'].rolling(window=20).mean()

            # 成交量加权平均价格 (VWAP)
            df['VWAP'] = (df['成交量'] * df['收盘']).rolling(window=20).sum() / df['成交量'].rolling(window=20).sum()
            # 价格相对VWAP的位置
            df['Price/VWAP'] = df['收盘'] / df['VWAP']

        return df

    def _calculate_statistical_features(self, df):
        """计算统计特征"""
        # 收益率的滚动统计特征
        if self.feature_config.get('rolling_stats', {}).get('use', True):
            windows = self.feature_config.get('rolling_stats', {}).get('windows', [20, 60])
            for window in windows:
                # 滚动波动率
                df[f'Rolling_Volatility_{window}'] = df['涨跌幅'].rolling(window=window).std()
                # 滚动偏度
                df[f'Rolling_Skew_{window}'] = df['涨跌幅'].rolling(window=window).skew()
                # 滚动峰度
                df[f'Rolling_Kurt_{window}'] = df['涨跌幅'].rolling(window=window).kurt()
                # Z-score
                df[f'Price_ZScore_{window}'] = (df['收盘'] - df['收盘'].rolling(window=window).mean()) / df['收盘'].rolling(window=window).std()
                # 收益率自相关性
                df[f'Return_Autocorr_{window}'] = df['涨跌幅'].rolling(window=window).apply(lambda x: x.autocorr(lag=1) if len(x) > 1 else np.nan, raw=False)
                # 收益率序列的Hurst指数（趋势强度）
                if window >= 40:  # Hurst指数需要足够长的序列
                    df[f'Hurst_{window}'] = df['涨跌幅'].rolling(window=window).apply(self._calculate_hurst, raw=False)

        return df

    def _calculate_hurst(self, returns):
        """计算Hurst指数，用于判断时间序列的持续性"""
        try:
            # 确保有足够的数据点
            if len(returns) < 20:
                return np.nan

            # 计算累积偏差序列
            mean = returns.mean()
            dev = returns - mean
            cum_dev = dev.cumsum()

            # 计算范围
            rolling_max = pd.Series(cum_dev).rolling(window=len(cum_dev)).max()
            rolling_min = pd.Series(cum_dev).rolling(window=len(cum_dev)).min()
            R = rolling_max - rolling_min

            # 计算标准差
            S = returns.std()

            # 计算R/S比率
            if S == 0:
                return np.nan

            RS = R.iloc[-1] / S

            # Hurst指数估计
            H = np.log(RS) / np.log(len(returns))

            return H
        except:
            return np.nan
    def _calculate_advanced_features(self, df):
        """
        计算高级特征

        包括：
        1. 市场微观结构特征
        2. 时间特征
        3. Alpha因子
        4. 自适应特征
        5. 市场情绪指标
        """
        # 计算市场微观结构特征
        if self.feature_config.get('market_microstructure', {}).get('use', True):
            # 价格波动率比率
            df['高低价比率'] = df['最高'] / df['最低']

            # 日内价格范围
            df['日内价格范围'] = (df['最高'] - df['最低']) / df['开盘']

            # 收盘价在日内范围中的位置
            df['收盘价位置'] = (df['收盘'] - df['最低']) / ((df['最高'] - df['最低']) + 1e-10)

            # 成交量加权平均价格 (VWAP)
            if '成交量' in df.columns and '成交额' in df.columns:
                df['VWAP'] = df['成交额'] / df['成交量']
            else:
                # 如果没有成交额，使用估计值
                df['VWAP'] = ((df['开盘'] + df['最高'] + df['最低'] + df['收盘']) / 4) * df['成交量']

            # 价格效率（价格变化与路径长度的比值）
            df['价格效率'] = df['收盘'].diff(5).abs() / (df['最高'] - df['最低']).rolling(5).sum()

            # 成交量冲击（价格变化与成交量的比值）
            df['成交量冲击'] = df['收盘'].pct_change().abs() / df['成交量']

        # 计算时间特征
        if self.feature_config.get('time_features', {}).get('use', True) and isinstance(df.index, pd.DatetimeIndex):
            # 星期几
            df['星期几'] = df.index.dayofweek
            # 月份
            df['月份'] = df.index.month
            # 是否月初
            df['月初'] = (df.index.day <= 5).astype(int)
            # 是否月末
            df['月末'] = (df.index.day >= 25).astype(int)
            # 季度
            df['季度'] = df.index.quarter
            # 是否季度末
            df['季度末'] = ((df.index.month % 3 == 0) & (df.index.day >= 20)).astype(int)

            # 周期性特征（使用正弦和余弦变换）
            # 周内周期
            df['周内周期_sin'] = np.sin(2 * np.pi * df.index.dayofweek / 5)
            df['周内周期_cos'] = np.cos(2 * np.pi * df.index.dayofweek / 5)

            # 月内周期
            df['月内周期_sin'] = np.sin(2 * np.pi * df.index.day / 30)
            df['月内周期_cos'] = np.cos(2 * np.pi * df.index.day / 30)

            # 年内周期
            df['年内周期_sin'] = np.sin(2 * np.pi * df.index.dayofyear / 365)
            df['年内周期_cos'] = np.cos(2 * np.pi * df.index.dayofyear / 365)

        # 计算Alpha因子
        if self.feature_config.get('alpha_factors', {}).get('use', True):
            try:
                # 确保数据足够
                if len(df) < 60:
                    self.logger.warning("数据长度不足，无法计算Alpha因子")
                else:
                    # 1. 动量类Alpha因子

                    # Alpha1: (收盘价-开盘价)/振幅
                    df['Alpha1'] = (df['收盘'] - df['开盘']) / ((df['最高'] - df['最低']) + 1e-10)

                    # Alpha2: 收盘价相对于过去N天的排名
                    window = 10
                    df['Alpha2'] = df['收盘'].rolling(window).apply(lambda x: pd.Series(x).rank().iloc[-1] / window, raw=False)

                    # Alpha3: 收盘价与开盘价的相关性
                    window = 20
                    df['Alpha3'] = df['收盘'].rolling(window).corr(df['开盘'])

                    # Alpha4: 成交量加权收益率
                    df['Alpha4'] = df['涨跌幅'] * df['成交量'] / df['成交量'].rolling(window=10).mean()

                    # Alpha5: 短期动量相对于长期动量
                    df['Alpha5'] = df['收盘'].pct_change(5) - df['收盘'].pct_change(20)

                    # Alpha6: 收盘价突破N日高点
                    for n in [20, 60]:
                        df[f'Alpha6_{n}'] = (df['收盘'] > df['收盘'].rolling(n).max().shift(1)).astype(int)

                    # Alpha7: 收盘价跌破N日低点
                    for n in [20, 60]:
                        df[f'Alpha7_{n}'] = (df['收盘'] < df['收盘'].rolling(n).min().shift(1)).astype(int)

                    # 2. 波动率类Alpha因子

                    # Alpha8: 收益率波动率比率（短期/长期）
                    short_vol = df['收盘'].pct_change().rolling(10).std()
                    long_vol = df['收盘'].pct_change().rolling(30).std()
                    df['Alpha8'] = short_vol / long_vol

                    # Alpha9: 波动率变化率
                    df['Alpha9'] = df['收盘'].pct_change().rolling(10).std().pct_change(5)

                    # Alpha10: 高低价差异相对于移动平均的比率
                    df['Alpha10'] = (df['最高'] - df['最低']) / df['收盘'].rolling(10).mean()

                    # 3. 反转类Alpha因子

                    # Alpha11: 短期超买超卖（基于RSI）
                    if 'RSI_14' in df.columns:
                        df['Alpha11'] = (df['RSI_14'] < 30).astype(int) - (df['RSI_14'] > 70).astype(int)
                    else:
                        rsi = ta.momentum.rsi(df['收盘'], window=14, fillna=True)
                        df['Alpha11'] = (rsi < 30).astype(int) - (rsi > 70).astype(int)

                    # 4. 成交量类Alpha因子

                    # Alpha12: 成交量相对强度
                    df['Alpha12'] = df['成交量'] / df['成交量'].rolling(20).mean()

                    # Alpha13: 成交量加权收益率
                    df['Alpha13'] = df['收盘'].pct_change() * (df['成交量'] / df['成交量'].rolling(20).mean())

                    # Alpha14: 价量相关性
                    try:
                        # 先创建Series对象，确保类型正确
                        price_change_series = df['收盘'].pct_change()
                        volume_change_series = df['成交量'].pct_change()

                        # 使用apply函数计算滚动相关性
                        df['Alpha14'] = price_change_series.rolling(20).apply(
                            lambda x: pd.Series(x).corr(pd.Series(volume_change_series[x.index]))
                            if len(x) > 1 and not pd.Series(volume_change_series[x.index]).isna().all()
                            else np.nan,
                            raw=False
                        )
                    except Exception as e:
                        self.logger.warning(f"计算价量相关性失败: {str(e)}")
                        df['Alpha14'] = 0

                    # 5. 组合Alpha因子

                    # Alpha15: 综合动量信号
                    df['Alpha15'] = (
                        df['Alpha2'] +
                        df['Alpha5'] +
                        df['Alpha6_20'] -
                        df['Alpha7_20']
                    ) / 4

                    # 处理可能的无穷大值和NaN值
                    alpha_columns = [col for col in df.columns if col.startswith('Alpha')]
                    for col in alpha_columns:
                        df[col] = df[col].replace([np.inf, -np.inf], np.nan)
                        df[col] = df[col].fillna(method='ffill').fillna(0)

                        # 限制极端值
                        mean = df[col].mean()
                        std = df[col].std()
                        if not np.isnan(mean) and not np.isnan(std) and std > 0:
                            df[col] = df[col].clip(mean - 5*std, mean + 5*std)
            except Exception as e:
                self.logger.error(f"计算Alpha因子时出错: {str(e)}")

        # 计算自适应特征
        if self.feature_config.get('adaptive_features', {}).get('use', True):
            try:
                # 确保数据足够
                if len(df) < 60:
                    self.logger.warning("数据长度不足，无法计算自适应特征")
                else:
                    # 1. 市场状态识别

                    # 计算短期和长期趋势
                    short_ma = df['收盘'].rolling(20).mean()
                    long_ma = df['收盘'].rolling(60).mean()

                    # 趋势状态: 1=上升趋势, -1=下降趋势, 0=盘整
                    df['趋势状态'] = np.where(short_ma > long_ma, 1, np.where(short_ma < long_ma, -1, 0))

                    # 波动率状态: 1=高波动, 0=正常波动, -1=低波动
                    vol = df['收盘'].pct_change().rolling(20).std() * np.sqrt(252)  # 年化波动率
                    vol_mean = vol.rolling(60).mean()
                    vol_std = vol.rolling(60).std()

                    df['波动率状态'] = np.where(vol > vol_mean + vol_std, 1,
                                    np.where(vol < vol_mean - vol_std, -1, 0))

                    # 2. 自适应特征

                    # 自适应动量：在不同趋势状态下调整动量权重
                    if 'Alpha5' in df.columns:
                        df['自适应动量'] = df['Alpha5'] * (1 + 0.5 * df['趋势状态'])

                    # 自适应波动率：在不同波动率环境下调整策略
                    if 'Alpha9' in df.columns:
                        df['自适应波动率'] = df['Alpha9'] * (1 - 0.5 * df['波动率状态'])

                    # 自适应反转：在高波动环境下增强反转信号
                    if 'Alpha11' in df.columns:
                        df['自适应反转'] = df['Alpha11'] * (1 + 0.5 * df['波动率状态'])

                    # 3. 市场状态组合特征

                    # 创建市场状态组合特征
                    df['市场状态'] = df['趋势状态'] + df['波动率状态'] * 3  # 创建9种市场状态组合

                    # 为每种市场状态创建指示变量
                    for state in range(-4, 5):
                        df[f'市场状态_{state}'] = (df['市场状态'] == state).astype(int)

                    # 处理可能的无穷大值和NaN值
                    adaptive_columns = ['自适应动量', '自适应波动率', '自适应反转', '趋势状态', '波动率状态', '市场状态']
                    adaptive_columns.extend([f'市场状态_{state}' for state in range(-4, 5)])

                    for col in adaptive_columns:
                        if col in df.columns:
                            df[col] = df[col].replace([np.inf, -np.inf], np.nan)
                            df[col] = df[col].fillna(method='ffill').fillna(0)
            except Exception as e:
                self.logger.error(f"计算自适应特征时出错: {str(e)}")

        # 计算市场情绪指标
        if self.feature_config.get('sentiment_indicators', {}).get('use', True):
            try:
                # 确保数据足够
                if len(df) < 60:
                    self.logger.warning("数据长度不足，无法计算市场情绪指标")
                else:
                    # 1. 波动率指标

                    # 历史波动率（HV）
                    df['历史波动率_10'] = df['收盘'].pct_change().rolling(10).std() * np.sqrt(252)
                    df['历史波动率_20'] = df['收盘'].pct_change().rolling(20).std() * np.sqrt(252)

                    # 波动率比率
                    df['波动率比率'] = df['历史波动率_10'] / df['历史波动率_20']

                    # 2. 情绪指标

                    # 恐慌指数（基于ATR的变种）
                    if 'ATR_14' in df.columns:
                        atr = df['ATR_14']
                    else:
                        atr = ta.volatility.average_true_range(df['最高'], df['最低'], df['收盘'], window=14, fillna=True)

                    df['恐慌指数'] = atr / df['收盘'] * 100

                    # 恐慌指数变化率
                    df['恐慌指数变化率'] = df['恐慌指数'].pct_change(5)

                    # 3. 市场强度指标

                    # 价格强度（基于RSI的变种）
                    if 'RSI_14' in df.columns:
                        rsi = df['RSI_14']
                    else:
                        rsi = ta.momentum.rsi(df['收盘'], window=14, fillna=True)

                    df['价格强度'] = (rsi - 50) / 50  # 归一化到[-1,1]

                    # 成交量强度
                    df['成交量强度'] = df['成交量'] / df['成交量'].rolling(20).mean() - 1

                    # 4. 综合情绪指标

                    # 市场情绪指标（综合价格强度和波动率）
                    df['市场情绪'] = df['价格强度'] * (1 - df['历史波动率_20'])

                    # 处理可能的无穷大值和NaN值
                    sentiment_columns = [
                        '历史波动率_10', '历史波动率_20', '波动率比率',
                        '恐慌指数', '恐慌指数变化率', '价格强度', '成交量强度', '市场情绪'
                    ]

                    for col in sentiment_columns:
                        if col in df.columns:
                            df[col] = df[col].replace([np.inf, -np.inf], np.nan)
                            df[col] = df[col].fillna(method='ffill').fillna(0)

                            # 限制极端值
                            mean = df[col].mean()
                            std = df[col].std()
                            if not np.isnan(mean) and not np.isnan(std) and std > 0:
                                df[col] = df[col].clip(mean - 5*std, mean + 5*std)
            except Exception as e:
                self.logger.error(f"计算市场情绪指标时出错: {str(e)}")

        return df

    def _calculate_cross_asset_features(self, df):
        """计算跨资产特征"""
        # 如果配置中提供了其他资产数据
        other_assets = self.feature_config.get('cross_asset', {}).get('assets', {})

        for asset_name, asset_data in other_assets.items():
            if isinstance(asset_data, pd.DataFrame) and '收盘' in asset_data.columns:
                # 确保索引一致
                asset_data = asset_data.reindex(df.index, method='ffill')

                # 计算相关性
                window = 20
                df[f'{asset_name}_相关性'] = df['收盘'].rolling(window).corr(asset_data['收盘'])

                # 计算相对强度
                df[f'{asset_name}_相对强度'] = (df['收盘'] / df['收盘'].shift(10)) / (asset_data['收盘'] / asset_data['收盘'].shift(10))

                # 计算价差
                df[f'{asset_name}_价差'] = df['收盘'] - asset_data['收盘']

                # 计算价差动量
                df[f'{asset_name}_价差动量'] = df[f'{asset_name}_价差'].diff(5)

        return df

    def _apply_feature_selection(self, df):
        """
        应用特征选择

        参数:
            df (pandas.DataFrame): 包含特征的数据框

        返回:
            pandas.DataFrame: 只包含选定特征的数据框
        """
        # 保留原始价格数据
        price_columns = ['开盘', '最高', '最低', '收盘', '成交量']
        if '成交额' in df.columns:
            price_columns.append('成交额')

        # 提取特征列
        feature_columns = [col for col in df.columns if col not in price_columns]

        if not feature_columns:
            return df

        # 准备特征数据
        X = df[feature_columns].copy()

        # 使用未来N天的收益率作为目标变量，但确保避免前视偏差
        target_days = self.feature_config.get('feature_selection', {}).get('target_days', 5)
        y = df['收盘'].pct_change(target_days).shift(-target_days)  # 使用未来N天的收益率，但在训练时会排除这些样本

        # 删除缺失值
        valid_idx = ~(X.isna().any(axis=1) | y.isna())
        X_valid = X[valid_idx]
        y_valid = y[valid_idx]

        if len(X_valid) < 100:  # 确保有足够的数据
            self.logger.warning("数据不足，无法进行特征选择")
            return df

        # 特征选择方法
        method = self.feature_selection_method
        # 如果配置中指定了方法，优先使用配置中的方法
        if 'feature_selection' in self.feature_config and 'method' in self.feature_config['feature_selection']:
            method = self.feature_config['feature_selection']['method']

        # 获取最大特征数量
        n_features = self.max_features
        # 如果配置中指定了数量，优先使用配置中的数量
        if 'feature_selection' in self.feature_config and 'n_features' in self.feature_config['feature_selection']:
            n_features = self.feature_config['feature_selection']['n_features']

        # 限制特征数量不超过有效特征数量
        n_features = min(n_features, len(feature_columns))

        try:
            # 初始化特征重要性字典
            feature_importance_dict = {}

            # 根据选择的方法应用特征选择
            if method == 'filter' or method == 'combined':
                # 过滤法：使用多种统计指标评估特征

                # 1. 使用互信息
                self.logger.info("使用互信息进行特征选择...")
                mi_selector = SelectKBest(mutual_info_regression, k=n_features)
                mi_selector.fit(X_valid, y_valid)
                mi_scores = mi_selector.scores_
                mi_importance = dict(zip(feature_columns, mi_scores))

                # 2. 使用F检验
                self.logger.info("使用F检验进行特征选择...")
                f_selector = SelectKBest(f_regression, k=n_features)
                f_selector.fit(X_valid, y_valid)
                f_scores = f_selector.scores_
                f_importance = dict(zip(feature_columns, f_scores))

                # 合并两种方法的结果
                for col in feature_columns:
                    # 归一化分数并取平均值
                    mi_norm = mi_importance[col] / (max(mi_scores) if max(mi_scores) > 0 else 1)
                    f_norm = f_importance[col] / (max(f_scores) if max(f_scores) > 0 else 1)
                    feature_importance_dict[col] = (mi_norm + f_norm) / 2

            if method == 'wrapper' or method == 'combined':
                # 包裹法：使用模型性能评估特征

                # 使用递归特征消除 (RFE)
                self.logger.info("使用递归特征消除进行特征选择...")
                estimator = RandomForestRegressor(n_estimators=100, random_state=42)
                rfe = RFE(estimator, n_features_to_select=n_features, step=1)
                rfe.fit(X_valid, y_valid)
                rfe_importance = dict(zip(feature_columns, rfe.ranking_))

                # 转换排名为重要性分数（排名越低越重要）
                max_rank = max(rfe_importance.values())
                for col in feature_columns:
                    # 归一化并反转排名
                    rfe_score = (max_rank - rfe_importance[col] + 1) / max_rank

                    if method == 'wrapper':
                        feature_importance_dict[col] = rfe_score
                    else:  # combined
                        # 如果已经有过滤法的分数，取平均值
                        if col in feature_importance_dict:
                            feature_importance_dict[col] = (feature_importance_dict[col] + rfe_score) / 2
                        else:
                            feature_importance_dict[col] = rfe_score

            if method == 'embedded' or method == 'combined':
                # 嵌入法：使用正则化模型的系数

                # 1. 使用Lasso回归
                self.logger.info("使用Lasso回归进行特征选择...")
                lasso = Lasso(alpha=0.01, random_state=42)
                lasso.fit(X_valid, y_valid)
                lasso_importance = dict(zip(feature_columns, np.abs(lasso.coef_)))

                # 2. 使用随机森林
                self.logger.info("使用随机森林进行特征选择...")
                rf = RandomForestRegressor(n_estimators=100, random_state=42)
                rf.fit(X_valid, y_valid)
                rf_importance = dict(zip(feature_columns, rf.feature_importances_))

                # 合并两种方法的结果
                for col in feature_columns:
                    # 归一化分数
                    lasso_norm = lasso_importance[col] / (max(np.abs(lasso.coef_)) if max(np.abs(lasso.coef_)) > 0 else 1)
                    rf_norm = rf_importance[col] / (max(rf.feature_importances_) if max(rf.feature_importances_) > 0 else 1)
                    embedded_score = (lasso_norm + rf_norm) / 2

                    if method == 'embedded':
                        feature_importance_dict[col] = embedded_score
                    else:  # combined
                        # 如果已经有其他方法的分数，取平均值
                        if col in feature_importance_dict:
                            feature_importance_dict[col] = (feature_importance_dict[col] + embedded_score) / 2
                        else:
                            feature_importance_dict[col] = embedded_score

            # 如果没有选择任何方法，使用默认的互信息方法
            if not feature_importance_dict:
                self.logger.info("使用默认的互信息方法进行特征选择...")
                selector = SelectKBest(mutual_info_regression, k=n_features)
                selector.fit(X_valid, y_valid)
                feature_importance_dict = dict(zip(feature_columns, selector.scores_))

            # 保存特征重要性
            self.feature_importance = feature_importance_dict

            # 按重要性排序特征
            sorted_features = sorted(self.feature_importance.items(), key=lambda x: x[1], reverse=True)

            # 选择前N个特征
            self.selected_features = [f[0] for f in sorted_features[:n_features]]

            # 记录每个特征组的选择情况
            selected_by_group = {group: [] for group in self.feature_groups.keys()}
            for feature in self.selected_features:
                for group, features in self.feature_groups.items():
                    if feature in features:
                        selected_by_group[group].append(feature)

            # 打印每个组的选择情况
            for group, selected in selected_by_group.items():
                if selected:
                    self.logger.info(f"从 {group} 组中选择了 {len(selected)} 个特征: {', '.join(selected)}")

            # 只保留选定的特征
            selected_columns = price_columns + self.selected_features
            df_selected = df[selected_columns].copy()

            self.logger.info(f"特征选择完成，从 {len(feature_columns)} 个特征中选择了 {len(self.selected_features)} 个特征")

            return df_selected

        except Exception as e:
            self.logger.error(f"特征选择失败: {str(e)}")
            return df

    def _check_stationarity(self, df):
        """
        检查特征的平稳性

        参数:
            df (pandas.DataFrame): 包含特征的数据框

        返回:
            pandas.DataFrame: 处理后的数据框
            dict: 特征平稳性结果
        """
        if not self.check_stationarity:
            return df, {}

        self.logger.info("开始检查特征平稳性...")

        # 保留原始价格数据
        price_columns = ['开盘', '最高', '最低', '收盘', '成交量']
        if '成交额' in df.columns:
            price_columns.append('成交额')

        # 只检查特征列的平稳性
        feature_columns = [col for col in df.columns if col not in price_columns]
        stationarity_results = {}

        # 对每个特征进行ADF检验
        for col in feature_columns:
            # 跳过非数值列和全是NaN的列
            if not pd.api.types.is_numeric_dtype(df[col]) or df[col].isna().all():
                continue

            # 填充NaN，以便进行检验
            series = df[col].fillna(method='ffill').fillna(method='bfill')

            # 如果数据不足，跳过
            if len(series.dropna()) < 20:
                stationarity_results[col] = {'stationary': False, 'method': None, 'p_value': None}
                continue

            try:
                # 检查序列是否为常数
                if series.nunique() <= 1:
                    self.logger.info(f"特征 {col} 是常数序列，自动视为平稳")
                    stationarity_results[col] = {'stationary': True, 'method': 'constant', 'p_value': 0.0}
                    continue

                # 进行ADF检验
                adf_result = adfuller(series.dropna(), regression='ct')
                p_value = adf_result[1]

                # 如果p值小于0.05，则拒绝原假设，认为序列是平稳的
                is_stationary = p_value < 0.05

                # 如果不平稳，尝试差分
                if not is_stationary:
                    # 一阶差分
                    diff1 = series.diff().dropna()
                    if len(diff1) > 20:
                        adf_result_diff1 = adfuller(diff1, regression='ct')
                        p_value_diff1 = adf_result_diff1[1]
                        is_stationary_diff1 = p_value_diff1 < 0.05

                        if is_stationary_diff1:
                            # 使用一阶差分替换原序列
                            df[f'{col}_diff1'] = df[col].diff()
                            # 记录结果
                            stationarity_results[col] = {
                                'stationary': False,
                                'diff1_stationary': True,
                                'method': 'diff1',
                                'p_value': p_value,
                                'diff1_p_value': p_value_diff1
                            }
                            # 将原特征添加到要删除的列表中
                            continue

                        # 如果一阶差分仍不平稳，尝试二阶差分
                        diff2 = diff1.diff().dropna()
                        if len(diff2) > 20:
                            adf_result_diff2 = adfuller(diff2, regression='ct')
                            p_value_diff2 = adf_result_diff2[1]
                            is_stationary_diff2 = p_value_diff2 < 0.05

                            if is_stationary_diff2:
                                # 使用二阶差分替换原序列
                                df[f'{col}_diff2'] = df[col].diff().diff()
                                # 记录结果
                                stationarity_results[col] = {
                                    'stationary': False,
                                    'diff1_stationary': False,
                                    'diff2_stationary': True,
                                    'method': 'diff2',
                                    'p_value': p_value,
                                    'diff1_p_value': p_value_diff1,
                                    'diff2_p_value': p_value_diff2
                                }
                                continue

                # 记录原始序列的平稳性结果
                stationarity_results[col] = {
                    'stationary': is_stationary,
                    'method': 'original' if is_stationary else None,
                    'p_value': p_value
                }

            except Exception as e:
                self.logger.warning(f"检查特征 {col} 的平稳性时出错: {str(e)}")
                stationarity_results[col] = {'stationary': False, 'method': None, 'error': str(e)}

        self.feature_stationarity = stationarity_results
        self.logger.info(f"特征平稳性检查完成，共检查了 {len(stationarity_results)} 个特征")

        return df, stationarity_results

    def _transform_features(self, df):
        """
        对特征进行变换，使其更符合正态分布

        参数:
            df (pandas.DataFrame): 包含特征的数据框

        返回:
            pandas.DataFrame: 变换后的数据框
        """
        # 保留原始价格数据
        price_columns = ['开盘', '最高', '最低', '收盘', '成交量']
        if '成交额' in df.columns:
            price_columns.append('成交额')

        # 只变换特征列
        feature_columns = [col for col in df.columns if col not in price_columns]

        # 检查是否需要进行变换
        if not self.feature_config.get('transform_features', {}).get('use', False):
            return df

        self.logger.info("开始特征变换...")

        # 获取变换方法
        transform_method = self.feature_config.get('transform_features', {}).get('method', 'yeo-johnson')

        # 对每个特征进行变换
        for col in feature_columns:
            # 跳过非数值列和全是NaN的列
            if not pd.api.types.is_numeric_dtype(df[col]) or df[col].isna().all():
                continue

            # 填充NaN，以便进行变换
            series = df[col].fillna(method='ffill').fillna(method='bfill')

            # 检查是否有足够的数据
            if len(series.dropna()) < 10:
                continue

            try:
                # 计算偏度
                skewness = series.skew()

                # 如果偏度绝对值大于1，进行变换
                if abs(skewness) > 1:
                    if transform_method == 'log':
                        # 对于正值，使用对数变换
                        if (series > 0).all():
                            df[f'{col}_log'] = np.log(series)
                    elif transform_method == 'sqrt':
                        # 对于正值，使用平方根变换
                        if (series > 0).all():
                            df[f'{col}_sqrt'] = np.sqrt(series)
                    elif transform_method == 'box-cox':
                        # 对于正值，使用Box-Cox变换
                        if (series > 0).all():
                            transformer = PowerTransformer(method='box-cox')
                            df[f'{col}_boxcox'] = transformer.fit_transform(series.values.reshape(-1, 1)).flatten()
                    elif transform_method == 'yeo-johnson':
                        # Yeo-Johnson变换适用于任何实数
                        transformer = PowerTransformer(method='yeo-johnson')
                        df[f'{col}_yeojohnson'] = transformer.fit_transform(series.values.reshape(-1, 1)).flatten()
            except Exception as e:
                self.logger.warning(f"变换特征 {col} 时出错: {str(e)}")

        self.logger.info("特征变换完成")
        return df

    def _normalize_features(self, df):
        """
        归一化特征

        参数:
            df (pandas.DataFrame): 包含特征的数据框

        返回:
            pandas.DataFrame: 归一化后的数据框
        """
        # 保留原始价格数据
        price_columns = ['开盘', '最高', '最低', '收盘', '成交量']
        if '成交额' in df.columns:
            price_columns.append('成交额')

        # 只保留存在的列
        existing_price_columns = [col for col in price_columns if col in df.columns]
        original_prices = df[existing_price_columns].copy()

        # 归一化其他特征列
        feature_columns = [col for col in df.columns if col not in price_columns]

        if feature_columns:
            # 选择归一化方法
            method = self.normalization_method

            # 如果配置中指定了方法，优先使用配置中的方法
            if 'normalization' in self.feature_config and 'method' in self.feature_config['normalization']:
                method = self.feature_config['normalization']['method']

            # 处理缺失值
            df_features = df[feature_columns].copy()
            df_features = df_features.fillna(method='ffill').fillna(method='bfill')

            # 应用归一化方法
            if method == 'standard':
                # 标准化 (Z-score)
                if self.scaler is None:
                    self.scaler = StandardScaler()
                    df[feature_columns] = self.scaler.fit_transform(df_features)
                else:
                    df[feature_columns] = self.scaler.transform(df_features)

            elif method == 'robust':
                # 稳健标准化 (对异常值不敏感)
                if self.scaler is None:
                    self.scaler = RobustScaler()
                    df[feature_columns] = self.scaler.fit_transform(df_features)
                else:
                    df[feature_columns] = self.scaler.transform(df_features)

            elif method == 'yeo-johnson':
                # Yeo-Johnson变换 (使数据更接近正态分布)
                if self.scaler is None:
                    self.scaler = PowerTransformer(method='yeo-johnson')
                    df[feature_columns] = self.scaler.fit_transform(df_features)
                else:
                    df[feature_columns] = self.scaler.transform(df_features)

            else:
                # 默认使用MinMax标准化
                if self.scaler is None:
                    self.scaler = MinMaxScaler()
                    df[feature_columns] = self.scaler.fit_transform(df_features)
                else:
                    df[feature_columns] = self.scaler.transform(df_features)

        # 将原始价格数据放回
        for col in existing_price_columns:
            df[col] = original_prices[col]

        return df