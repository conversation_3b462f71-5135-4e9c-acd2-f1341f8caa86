# 量化交易系统用户指南

## 1. 系统概述

本量化交易系统是一个基于深度强化学习的量化交易平台，支持数据获取、特征工程、模型训练、回测和性能分析等功能。系统采用模块化设计，各个模块可以独立使用，也可以组合使用。

## 2. 环境配置

### 2.1 安装依赖

系统依赖于以下Python库：

```bash
# 核心依赖
pandas>=1.3.0
numpy>=1.20.0
matplotlib>=3.4.0
scikit-learn>=1.0.0
gymnasium>=0.26.0
stable-baselines3>=1.6.0
torch>=1.10.0
streamlit>=1.10.0
pyyaml>=6.0

# 数据源
akshare>=1.0.0

# 技术指标
ta-lib>=0.4.0

# 可视化
plotly>=5.5.0
seaborn>=0.11.0

# 工具
tqdm>=4.62.0
joblib>=1.1.0
```

可以通过以下命令安装依赖：

```bash
pip install -r requirements.txt
```

### 2.2 GPU支持

系统支持GPU加速，可以通过以下命令安装GPU支持：

```bash
python install_gpu_support.py
```

该脚本会自动检测系统是否有GPU，并安装相应的GPU版本的PyTorch和TensorFlow。

## 3. 数据获取

### 3.1 支持的数据类型

系统支持以下类型的数据：

- 股票数据：以`sh`或`sz`开头的股票代码
- 指数数据：以`index_`开头的指数代码

注意：系统不支持加密货币数据和期货数据。

### 3.2 数据获取示例

```python
from quant_project.core_logic.data_handler import DataHandler

# 创建数据处理器实例
data_handler = DataHandler()

# 获取股票数据
stock_data = data_handler.get_stock_data(
    stock_code="sh000001",  # 上证指数
    start_date="2022-01-01",
    end_date="2022-12-31",
    frequency="日线"
)

# 获取指数数据
index_data = data_handler.get_stock_data(
    stock_code="index_000300",  # 沪深300指数
    start_date="2022-01-01",
    end_date="2022-12-31",
    frequency="日线"
)
```

### 3.3 数据缓存

系统会自动缓存获取的数据，以提高数据获取的效率。缓存的数据存储在`data_cache`目录下。

## 4. 特征工程

### 4.1 特征配置

特征工程模块支持两种配置格式：标准格式和扁平格式。

#### 4.1.1 标准格式

```python
feature_config = {
    'price_features': {'use': True},
    'volume': {'use': True, 'periods': [5, 10, 20]},
    'sma': {'use': True, 'periods': [5, 10, 20, 60]},
    'ema': {'use': True, 'periods': [5, 10, 20]},
    'rsi': {'use': True, 'periods': [6, 14, 21]},
    'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9},
    'bbands': {'use': True, 'period': 20, 'std': 2.0},
    'normalization': {'use': True, 'method': 'minmax'}
}
```

#### 4.1.2 扁平格式

```python
feature_config = {
    'use_price': True,
    'use_volume': True,
    'use_technical': True,
    'sma_periods': [5, 10, 20, 30, 60],
    'ema_periods': [5, 10, 20, 30, 60],
    'rsi_periods': [14],
    'macd_params': {'fast': 12, 'slow': 26, 'signal': 9},
    'bb_params': {'window': 20, 'num_std': 2},
    'atr_periods': [14],
    'normalization': 'zscore'
}
```

系统会自动将扁平格式转换为标准格式。

### 4.2 特征工程示例

```python
from quant_project.core_logic.feature_engineer import FeatureEngineer
from quant_project.core_logic.feature_engineering_adapter import FeatureEngineeringAdapter

# 创建特征工程配置
feature_config = {
    'use_price': True,
    'use_volume': True,
    'use_technical': True,
    'sma_periods': [5, 10, 20],
    'rsi_periods': [14],
    'normalization': 'zscore'
}

# 使用适配器转换配置格式
adapter = FeatureEngineeringAdapter()
standard_config = adapter.adapt_config(feature_config)

# 创建特征工程实例
feature_engineer = FeatureEngineer(standard_config)

# 生成特征
processed_data = feature_engineer.generate_features(data)
```

## 5. 交易环境

### 5.1 交易环境配置

```python
env_config = {
    'initial_capital': 100000,  # 初始资金
    'commission_rate': 0.0003,  # 手续费率
    'min_hold_days': 3,  # 最小持仓天数
    'allow_short': False,  # 是否允许做空
    'max_position': 1.0,  # 最大仓位比例
    'window_size': 20,  # 观测窗口大小
    'reward_config': {
        'portfolio_return': 1.0,  # 组合收益权重
        'volatility_penalty': 0.1,  # 波动率惩罚权重
        'drawdown_penalty': 0.2,  # 回撤惩罚权重
        'holding_penalty': 0.05,  # 持仓成本惩罚权重
        'trade_penalty': 0.01  # 交易成本惩罚权重
    }
}
```

### 5.2 交易环境示例

```python
from quant_project.core_logic.trading_environment import TradingEnvironment

# 创建交易环境
trading_env = TradingEnvironment(
    data=processed_data,
    **env_config
)
```

## 6. 模型训练

### 6.1 DRL智能体配置

```python
agent_config = {
    'algorithm': 'PPO',  # 算法，支持PPO、A2C、DQN等
    'policy': 'MlpPolicy',  # 策略，支持MlpPolicy、CnnPolicy等
    'verbose': 1  # 日志级别
}
```

### 6.2 模型训练示例

```python
from quant_project.core_logic.drl_agent import DRLAgent

# 创建DRL智能体
drl_agent = DRLAgent(
    env=trading_env,
    **agent_config
)

# 训练模型
drl_agent.train(total_timesteps=100000)

# 保存模型
drl_agent.save('saved_models/my_model')
```

## 7. 回测

### 7.1 回测示例

```python
# 加载模型
drl_agent.load('saved_models/my_model')

# 回测
trades, portfolio_values = drl_agent.backtest(processed_data)
```

## 8. 性能分析

### 8.1 性能分析示例

```python
from quant_project.core_logic.performance_analyzer import PerformanceAnalyzer

# 创建性能分析器
performance_analyzer = PerformanceAnalyzer()

# 分析性能
metrics = performance_analyzer.analyze(trades, portfolio_values)

# 输出性能指标
print(f"总收益率: {metrics['total_return']:.4f}")
print(f"年化收益率: {metrics['annualized_return']:.4f}")
print(f"最大回撤: {metrics['max_drawdown']:.4f}")
print(f"夏普比率: {metrics['sharpe_ratio']:.4f}")
```

## 9. 用户界面

系统提供了基于Streamlit的用户界面，可以通过以下命令启动：

```bash
streamlit run quant_project/main_app.py
```

用户界面包括以下功能：

- 数据获取
- 特征工程
- 模型训练
- 回测
- 性能分析

## 10. 常见问题

### 10.1 数据获取失败

如果数据获取失败，请检查以下几点：

1. 确保网络连接正常
2. 确保股票代码格式正确
3. 确保时间范围有效

### 10.2 模型训练失败

如果模型训练失败，请检查以下几点：

1. 确保特征工程配置正确
2. 确保交易环境配置正确
3. 确保有足够的内存和计算资源

### 10.3 GPU支持问题

如果GPU支持有问题，请检查以下几点：

1. 确保系统有NVIDIA GPU
2. 确保已安装NVIDIA驱动
3. 确保已安装CUDA
4. 运行`python install_gpu_support.py`安装GPU支持
