#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
模型训练与加载测试脚本
测试量化交易系统的模型训练与加载功能
"""

import os
import sys
import time
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import traceback
import json
import matplotlib.pyplot as plt
import gymnasium as gym
import shutil

# 设置日志
os.makedirs('logs', exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join('logs', 'model_training_test.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('model_training_test')

def get_trading_environment():
    """获取交易环境用于测试"""
    logger.info("获取交易环境")
    
    try:
        # 尝试导入必要的模块
        try:
            from quant_project.core_logic.data_handler import DataHandler
            from quant_project.core_logic.feature_engineer import FeatureEngineer
            from quant_project.core_logic.trading_environment import TradingEnvironment
            logger.info("成功导入所需模块")
        except ImportError:
            try:
                from quant_trading.data.data_handler import DataHandler
                from quant_trading.features.feature_engineer import FeatureEngineer
                from quant_trading.trading.trading_environment import TradingEnvironment
                logger.info("成功导入所需模块(从根目录)")
            except ImportError as e:
                logger.error(f"导入模块失败: {str(e)}")
                return None
        
        # 创建数据处理器实例
        data_handler = DataHandler()
        
        # 获取样本数据
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')
        
        sample_data = data_handler.get_stock_data(
            stock_code='sh000001',
            start_date=start_date,
            end_date=end_date,
            frequency='日线'
        )
        
        if sample_data is None or sample_data.empty:
            logger.warning("获取样本数据失败: 数据为空")
            return None
        
        # 创建特征工程器实例
        feature_engineer = FeatureEngineer()
        
        # 设置特征配置
        feature_config = {
            'moving_averages': {'windows': [5, 10, 20]},
            'rsi': {'window': 14},
            'macd': {'fast': 12, 'slow': 26, 'signal': 9}
        }
        
        feature_engineer.feature_config = feature_config
        
        # 生成特征
        processed_data = feature_engineer.generate_features(sample_data)
        
        if processed_data is None or processed_data.empty:
            logger.warning("生成特征失败: 数据为空")
            return None
        
        # 创建交易环境
        env_config = {
            'initial_capital': 100000,
            'transaction_fee_rate': 0.0003,
            'min_holding_days': 1,
            'slippage_rate': 0.0001
        }
        
        env = TradingEnvironment(
            data=processed_data,
            **env_config
        )
        
        logger.info("成功创建交易环境")
        return env
    
    except Exception as e:
        logger.error(f"获取交易环境时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return None

def test_model_training_and_loading():
    """测试模型训练与加载"""
    logger.info("测试模型训练与加载")
    
    env = get_trading_environment()
    
    if env is None:
        logger.error("无法测试模型训练与加载: 交易环境不可用")
        return {
            'success': False,
            'error': '交易环境不可用'
        }
    
    try:
        # 尝试导入DRL代理模块
        try:
            from quant_project.core_logic.drl_agent import DRLAgent
            logger.info("成功导入DRLAgent模块")
        except ImportError:
            try:
                from quant_trading.agents.drl_agent import DRLAgent
                logger.info("成功导入DRLAgent模块(从根目录)")
            except ImportError as e:
                logger.error(f"导入DRLAgent模块失败: {str(e)}")
                return {
                    'success': False,
                    'error': f"导入DRLAgent模块失败: {str(e)}"
                }
        
        # 测试不同算法
        algorithms = ['A2C', 'PPO']
        
        # 确保模型保存目录存在
        os.makedirs('saved_models', exist_ok=True)
        
        results = []
        
        for algorithm in algorithms:
            try:
                logger.info(f"测试算法: {algorithm}")
                
                # 创建DRL代理
                agent = DRLAgent(env=env)
                
                # 设置训练参数
                train_params = {
                    'total_timesteps': 10000,  # 减少训练步数以加快测试
                    'log_interval': 1000
                }
                
                # 训练模型
                logger.info(f"开始训练 {algorithm} 模型")
                model = agent.train_model(algorithm=algorithm, **train_params)
                
                if model is None:
                    logger.warning(f"训练 {algorithm} 模型失败")
                    results.append({
                        'algorithm': algorithm,
                        'training_success': False,
                        'error': '训练失败'
                    })
                    continue
                
                logger.info(f"成功训练 {algorithm} 模型")
                
                # 保存模型
                model_path = os.path.join('saved_models', f'test_{algorithm}_{datetime.now().strftime("%Y%m%d_%H%M%S")}')
                model.save(model_path)
                logger.info(f"模型已保存到: {model_path}")
                
                # 加载模型
                loaded_model = agent.load_model(algorithm=algorithm, model_path=model_path)
                
                if loaded_model is None:
                    logger.warning(f"加载 {algorithm} 模型失败")
                    results.append({
                        'algorithm': algorithm,
                        'training_success': True,
                        'loading_success': False,
                        'error': '加载失败'
                    })
                    continue
                
                logger.info(f"成功加载 {algorithm} 模型")
                
                # 测试模型预测
                observation, info = env.reset()
                action, _states = loaded_model.predict(observation)
                
                logger.info(f"模型预测成功，动作: {action}")
                
                results.append({
                    'algorithm': algorithm,
                    'training_success': True,
                    'loading_success': True,
                    'prediction_success': True,
                    'model_path': model_path
                })
                
                # 测试模型清理
                if os.path.exists(model_path):
                    shutil.rmtree(model_path)
                    logger.info(f"成功清理模型: {model_path}")
                
            except Exception as e:
                logger.error(f"测试算法 {algorithm} 时出错: {str(e)}")
                logger.error(traceback.format_exc())
                results.append({
                    'algorithm': algorithm,
                    'success': False,
                    'error': str(e)
                })
        
        # 保存测试结果
        os.makedirs('test_results', exist_ok=True)
        
        # 将结果转换为可序列化的格式
        serializable_results = []
        for result in results:
            serializable_result = {k: (str(v) if not isinstance(v, (bool, int, float, str)) else v) for k, v in result.items()}
            serializable_results.append(serializable_result)
        
        with open('test_results/model_training_test.json', 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=4)
        
        training_success_rate = sum(1 for r in results if r.get('training_success', False)) / len(results) * 100
        loading_success_rate = sum(1 for r in results if r.get('loading_success', False)) / len(results) * 100
        
        logger.info(f"模型训练与加载测试完成，训练成功率: {training_success_rate:.2f}%, 加载成功率: {loading_success_rate:.2f}%")
        
        return {
            'success': True,
            'training_success_rate': training_success_rate,
            'loading_success_rate': loading_success_rate,
            'algorithms_tested': len(algorithms),
            'results': results
        }
    
    except Exception as e:
        logger.error(f"测试模型训练与加载时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            'success': False,
            'error': str(e)
        }

def run_all_tests():
    """运行所有模型训练与加载测试"""
    logger.info("开始运行模型训练与加载测试")
    
    results = test_model_training_and_loading()
    
    logger.info(f"模型训练与加载测试完成，结果: {results}")
    
    return results

if __name__ == "__main__":
    run_all_tests()
