"""
配置文件加载测试脚本
用于测试配置文件的加载和应用
"""

import os
import sys
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core_logic.utils import load_config, setup_logger
from core_logic.trading_environment import TradingEnvironment
from core_logic.drl_agent import DRLAgent
import pandas as pd
import numpy as np

# 设置日志
logger = setup_logger(log_file='logs/test_config.log')

def test_config_loading():
    """测试配置文件加载功能"""
    logger.info("开始测试配置文件加载功能")
    
    # 加载环境配置
    env_config_path = "configs/env_config.yaml"
    env_config = load_config(env_config_path)
    
    # 加载智能体配置
    agent_config_path = "configs/drl_agent_config.yaml"
    agent_config = load_config(agent_config_path)
    
    # 验证配置是否成功加载
    if not env_config:
        logger.error(f"加载环境配置失败: {env_config_path}")
        return False
    
    if not agent_config:
        logger.error(f"加载智能体配置失败: {agent_config_path}")
        return False
    
    # 验证关键配置项是否存在
    env_keys = ['initial_capital', 'commission_rate', 'min_hold_days', 'reward_config']
    for key in env_keys:
        if key not in env_config:
            logger.error(f"环境配置缺少关键项: {key}")
            return False
    
    agent_keys = ['algorithm', 'learning_rate', 'gamma']
    for key in agent_keys:
        if key not in agent_config:
            logger.error(f"智能体配置缺少关键项: {key}")
            return False
    
    logger.info("配置文件加载成功")
    logger.info(f"环境配置: {env_config}")
    logger.info(f"智能体配置: {agent_config}")
    
    return True

def test_config_application():
    """测试配置文件应用功能"""
    logger.info("开始测试配置文件应用功能")
    
    # 加载环境配置
    env_config_path = "configs/env_config.yaml"
    env_config = load_config(env_config_path)
    
    # 加载智能体配置
    agent_config_path = "configs/drl_agent_config.yaml"
    agent_config = load_config(agent_config_path)
    
    # 创建模拟数据
    dates = pd.date_range(start='2023-01-01', end='2023-12-31', freq='D')
    data = pd.DataFrame({
        '开盘': np.random.rand(len(dates)) * 100 + 3000,
        '最高': np.random.rand(len(dates)) * 100 + 3050,
        '最低': np.random.rand(len(dates)) * 100 + 2950,
        '收盘': np.random.rand(len(dates)) * 100 + 3000,
        '成交量': np.random.rand(len(dates)) * 1000000,
        'SMA_5': np.random.rand(len(dates)) * 100 + 3000,
        'SMA_20': np.random.rand(len(dates)) * 100 + 3000,
        'RSI_14': np.random.rand(len(dates)) * 100
    }, index=dates)
    
    try:
        # 创建交易环境
        trading_env = TradingEnvironment(
            df_processed_data=data,
            initial_capital=env_config['initial_capital'],
            commission_rate=env_config['commission_rate'],
            min_hold_days=env_config['min_hold_days'],
            reward_config=env_config['reward_config'],
            window_size=env_config['window_size']
        )
        
        # 验证环境参数是否正确应用
        assert trading_env.initial_capital == env_config['initial_capital']
        assert trading_env.commission_rate == env_config['commission_rate']
        assert trading_env.min_hold_days == env_config['min_hold_days']
        
        logger.info("环境配置应用成功")
        
        # 创建DRL智能体配置
        drl_env_config = {
            'df_processed_data': data,
            'initial_capital': env_config['initial_capital'],
            'commission_rate': env_config['commission_rate'],
            'min_hold_days': env_config['min_hold_days'],
            'window_size': env_config['window_size']
        }
        
        # 创建DRL智能体
        drl_agent = DRLAgent(drl_env_config, agent_config)
        
        # 验证智能体参数是否正确应用
        assert drl_agent.agent_config['algorithm'] == agent_config['algorithm']
        assert drl_agent.agent_config['learning_rate'] == agent_config['learning_rate']
        assert drl_agent.agent_config['gamma'] == agent_config['gamma']
        
        logger.info("智能体配置应用成功")
        
        return True
    
    except Exception as e:
        logger.error(f"配置应用测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    # 运行测试
    print("开始测试配置文件功能...")
    
    # 测试配置文件加载
    loading_result = test_config_loading()
    print(f"配置文件加载测试: {'成功' if loading_result else '失败'}")
    
    # 测试配置文件应用
    application_result = test_config_application()
    print(f"配置文件应用测试: {'成功' if application_result else '失败'}")
    
    # 总结
    if loading_result and application_result:
        print("配置文件功能测试全部通过！")
    else:
        print("配置文件功能测试存在问题，请查看日志获取详细信息。")
