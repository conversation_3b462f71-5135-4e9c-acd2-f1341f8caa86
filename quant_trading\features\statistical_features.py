"""
统计特征模块
负责计算统计特征
"""

import pandas as pd
import numpy as np
import logging

class StatisticalFeatures:
    """
    统计特征类
    负责计算统计特征
    """

    def __init__(self, config=None, logger=None):
        """
        初始化统计特征

        参数:
            config (dict): 统计特征配置
            logger (logging.Logger): 日志记录器
        """
        self.logger = logger or logging.getLogger('drl_trading')
        self.calculator = StatisticalFeatureCalculator(config=config, logger=self.logger)

    def calculate(self, df):
        """
        计算统计特征

        参数:
            df (pandas.DataFrame): 原始数据，必须包含OHLCV列和涨跌幅列

        返回:
            tuple: (处理后的数据框, 生成的特征名称列表)
        """
        return self.calculator.calculate(df)

class StatisticalFeatureCalculator:
    """
    统计特征计算器
    负责计算统计特征
    """

    def __init__(self, config=None, logger=None):
        """
        初始化统计特征计算器

        参数:
            config (dict): 统计特征配置
            logger (logging.Logger): 日志记录器
        """
        self.logger = logger or logging.getLogger('drl_trading')

        # 默认配置
        default_config = {
            'windows': [5, 10, 20, 60]
        }

        # 更新配置
        self.config = default_config
        if config:
            self.config.update(config)

    def calculate(self, df):
        """
        计算统计特征

        参数:
            df (pandas.DataFrame): 原始数据，必须包含OHLCV列和涨跌幅列

        返回:
            tuple: (处理后的数据框, 生成的特征名称列表)
        """
        # 确保涨跌幅列存在
        if '涨跌幅' not in df.columns:
            self.logger.warning("涨跌幅列不存在，将计算涨跌幅")
            df['涨跌幅'] = df['收盘'].pct_change()

        # 创建一个空的DataFrame来存储所有特征，避免DataFrame碎片化
        all_features = {}
        feature_names = []

        # 获取滚动窗口大小
        windows = self.config.get('windows', [5, 10, 20, 60])

        for window in windows:
            # 滚动波动率 (标准差)
            all_features[f'Rolling_Volatility_{window}'] = df['涨跌幅'].rolling(window=window).std()
            feature_names.append(f'Rolling_Volatility_{window}')

            # 滚动偏度
            all_features[f'Rolling_Skew_{window}'] = df['涨跌幅'].rolling(window=window).skew()
            feature_names.append(f'Rolling_Skew_{window}')

            # 滚动峰度
            all_features[f'Rolling_Kurt_{window}'] = df['涨跌幅'].rolling(window=window).kurt()
            feature_names.append(f'Rolling_Kurt_{window}')

            # 滚动最大值和最小值
            all_features[f'Rolling_Max_{window}'] = df['收盘'].rolling(window=window).max()
            all_features[f'Rolling_Min_{window}'] = df['收盘'].rolling(window=window).min()
            feature_names.extend([f'Rolling_Max_{window}', f'Rolling_Min_{window}'])

            # 滚动平均绝对偏差
            all_features[f'Rolling_MAD_{window}'] = df['涨跌幅'].rolling(window=window).apply(
                lambda x: np.mean(np.abs(x - np.mean(x))), raw=True
            )
            feature_names.append(f'Rolling_MAD_{window}')

            # 滚动Z-score (当前价格相对于滚动窗口的标准化得分)
            rolling_mean = df['收盘'].rolling(window=window).mean()
            rolling_std = df['收盘'].rolling(window=window).std()
            all_features[f'Rolling_ZScore_{window}'] = (df['收盘'] - rolling_mean) / (rolling_std + 1e-10)
            feature_names.append(f'Rolling_ZScore_{window}')

            # 滚动自相关系数
            try:
                all_features[f'Rolling_Autocorr_{window}'] = df['涨跌幅'].rolling(window=window).apply(
                    lambda x: x.autocorr(1) if len(x) > 1 else np.nan, raw=False
                )
                feature_names.append(f'Rolling_Autocorr_{window}')
            except Exception as e:
                self.logger.warning(f"计算Rolling_Autocorr_{window}失败: {str(e)}")

            # 滚动收益率的正负比例
            all_features[f'Rolling_PosRatio_{window}'] = df['涨跌幅'].rolling(window=window).apply(
                lambda x: np.sum(x > 0) / len(x), raw=True
            )
            feature_names.append(f'Rolling_PosRatio_{window}')

            # 滚动收益率的上升/下降趋势
            try:
                all_features[f'Rolling_Trend_{window}'] = df['涨跌幅'].rolling(window=window).apply(
                    lambda x: np.corrcoef(x, np.arange(len(x)))[0, 1] if len(x) > 1 else np.nan, raw=False
                )
                feature_names.append(f'Rolling_Trend_{window}')
            except Exception as e:
                self.logger.warning(f"计算Rolling_Trend_{window}失败: {str(e)}")

            # 滚动夏普比率 (假设无风险利率为0)
            all_features[f'Rolling_Sharpe_{window}'] = df['涨跌幅'].rolling(window=window).mean() / (df['涨跌幅'].rolling(window=window).std() + 1e-10) * np.sqrt(252)
            feature_names.append(f'Rolling_Sharpe_{window}')

            # 滚动最大回撤
            try:
                all_features[f'Rolling_MaxDrawdown_{window}'] = df['收盘'].rolling(window=window).apply(
                    lambda x: (x / np.maximum.accumulate(x) - 1).min(), raw=True
                )
                feature_names.append(f'Rolling_MaxDrawdown_{window}')
            except Exception as e:
                self.logger.warning(f"计算Rolling_MaxDrawdown_{window}失败: {str(e)}")

            # 滚动收益率的分位数
            for q in [0.25, 0.75]:
                all_features[f'Rolling_Quantile_{window}_{int(q*100)}'] = df['涨跌幅'].rolling(window=window).quantile(q)
                feature_names.append(f'Rolling_Quantile_{window}_{int(q*100)}')

        # 一次性创建所有特征的DataFrame
        features_df = pd.DataFrame(all_features, index=df.index)

        # 计算IQR (需要先有分位数特征)
        for window in windows:
            features_df[f'Rolling_IQR_{window}'] = features_df[f'Rolling_Quantile_{window}_75'] - features_df[f'Rolling_Quantile_{window}_25']
            feature_names.append(f'Rolling_IQR_{window}')

        # 将所有特征合并到原始DataFrame
        df = pd.concat([df, features_df], axis=1)

        self.logger.info(f"计算了 {len(feature_names)} 个统计特征")

        return df, feature_names
