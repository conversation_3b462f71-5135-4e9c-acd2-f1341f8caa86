"""
交易环境适配器模块
提供与原始交易环境类兼容的接口
"""

import logging
import numpy as np
import pandas as pd
import gymnasium as gym
from gymnasium import spaces

from .trading_environment import TradingEnvironment

class TradingEnvironmentAdapter(gym.Env):
    """
    交易环境适配器类
    提供与原始交易环境类兼容的接口
    """

    metadata = {'render_modes': ['human', 'rgb_array']}

    def __init__(self,
                 df_processed_data,
                 initial_capital=100000,
                 commission_rate=0.0003,
                 min_hold_days=3,
                 allow_short=False,
                 max_position=1.0,
                 reward_config=None,
                 window_size=20,
                 render_mode=None):
        """
        初始化交易环境适配器

        参数:
            df_processed_data (pandas.DataFrame): 处理好的行情数据和特征
            initial_capital (float): 初始资金
            commission_rate (float): 手续费率（单边）
            min_hold_days (int): 最小持仓天数
            allow_short (bool): 是否允许做空
            max_position (float): 最大仓位比例，范围[0, 1]
            reward_config (dict): 奖励函数配置
            window_size (int): 观测窗口大小
            render_mode (str): 渲染模式，可选 'human', 'rgb_array'
        """
        super(TradingEnvironmentAdapter, self).__init__()

        self.logger = logging.getLogger('drl_trading')

        # 创建新的交易环境
        self.env = TradingEnvironment(
            df_processed_data=df_processed_data,
            initial_capital=initial_capital,
            commission_rate=commission_rate,
            min_hold_days=min_hold_days,
            allow_short=allow_short,
            max_position=max_position,
            reward_config=reward_config,
            window_size=window_size,
            render_mode=render_mode
        )

        # 设置动作空间和观察空间
        self.action_space = self.env.action_space
        self.observation_space = self.env.observation_space

        # 保存原始交易环境的属性
        self.df = df_processed_data
        self.initial_capital = initial_capital
        self.commission_rate = commission_rate
        self.min_hold_days = min_hold_days
        self.allow_short = allow_short
        self.max_position = max_position
        self.window_size = window_size
        self.render_mode = render_mode
        self.reward_config = reward_config or {
            'portfolio_return': 1.0,      # 组合收益权重
            'volatility_penalty': 0.1,    # 波动率惩罚权重
            'drawdown_penalty': 0.2,      # 回撤惩罚权重
            'holding_penalty': 0.05,      # 持仓成本惩罚权重
            'trade_penalty': 0.01         # 交易成本惩罚权重
        }

        # 保存原始交易环境的状态
        self.current_step = self.env.current_step
        self.cash = self.env.cash
        self.shares_held = self.env.shares_held
        self.current_price = self.env.current_price
        self.cost_basis = self.env.cost_basis
        self.total_commission = self.env.total_commission
        self.holding_days = self.env.holding_days
        self.portfolio_values = self.env.portfolio_values
        self.n_features = self.env.observation_handler.n_features
        self.original_obs_dim = self.env.observation_handler.obs_dim

    def reset(self, seed=None, options=None):
        """
        重置环境到初始状态

        参数:
            seed (int): 随机种子
            options (dict): 重置选项

        返回:
            tuple: (observation, info)
        """
        observation, info = self.env.reset(seed=seed, options=options)

        # 更新状态
        self.current_step = self.env.current_step
        self.cash = self.env.cash
        self.shares_held = self.env.shares_held
        self.current_price = self.env.current_price
        self.cost_basis = self.env.cost_basis
        self.total_commission = self.env.total_commission
        self.holding_days = self.env.holding_days
        self.portfolio_values = self.env.portfolio_values

        return observation, info

    def step(self, action):
        """
        执行一个动作并更新环境

        参数:
            action (int): 动作，0=保持, 1=买入, 2=卖出

        返回:
            tuple: (observation, reward, terminated, truncated, info)
        """
        observation, reward, terminated, truncated, info = self.env.step(action)

        # 更新状态
        self.current_step = self.env.current_step
        self.cash = self.env.cash
        self.shares_held = self.env.shares_held
        self.current_price = self.env.current_price
        self.cost_basis = self.env.cost_basis
        self.total_commission = self.env.total_commission
        self.holding_days = self.env.holding_days
        self.portfolio_values = self.env.portfolio_values

        return observation, reward, terminated, truncated, info

    def render(self, mode=None):
        """
        渲染环境状态

        参数:
            mode (str): 渲染模式，可选 'human', 'rgb_array'

        返回:
            numpy.ndarray: 如果mode='rgb_array'，返回RGB数组
        """
        return self.env.render(mode=mode)

    def close(self):
        """
        关闭环境
        """
        self.env.close()

    def _calculate_portfolio_value(self):
        """
        计算当前组合价值

        返回:
            float: 组合价值
        """
        return self.env._calculate_portfolio_value()

    def _execute_trade_action(self, action):
        """
        执行交易动作 - 为了向后兼容

        参数:
            action (int): 动作，0=保持, 1=买入, 2=卖出
        """
        # 获取当前价格
        self.current_price = self.df['收盘'].iloc[self.current_step]

        # 执行交易动作
        self.cash, self.shares_held, self.holding_days, trade_cost, is_trade, cost_basis = self.env.action_handler.execute_action(
            action=action,
            current_step=self.current_step,
            current_price=self.current_price,
            cash=self.cash,
            shares_held=self.shares_held,
            holding_days=self.holding_days,
            initial_capital=self.initial_capital
        )

        # 更新成本基础
        if cost_basis > 0:
            self.cost_basis = cost_basis

        # 更新总手续费
        self.total_commission += trade_cost

    def _get_observation(self):
        """
        获取当前观测 - 为了向后兼容

        返回:
            numpy.ndarray: 观测向量
        """
        return self.env.observation_handler.get_observation(
            current_step=self.current_step,
            cash=self.cash,
            shares_held=self.shares_held,
            current_price=self.current_price,
            cost_basis=self.cost_basis,
            holding_days=self.holding_days
        )

    def _calculate_reward(self, prev_portfolio_value, current_portfolio_value):
        """
        计算奖励 - 为了向后兼容

        参数:
            prev_portfolio_value (float): 上一步的组合价值
            current_portfolio_value (float): 当前组合价值

        返回:
            float: 奖励值
        """
        reward, _ = self.env.reward_calculator.calculate_reward(
            prev_portfolio_value=prev_portfolio_value,
            current_portfolio_value=current_portfolio_value,
            holding_days=self.holding_days,
            trade_cost=0,
            is_trade=False
        )
        return reward

    def get_trades_history(self):
        """
        获取交易历史

        返回:
            list: 交易记录列表
        """
        return self.env.get_trades()

    def get_portfolio_history(self):
        """
        获取组合价值历史

        返回:
            pandas.Series: 组合价值序列
        """
        portfolio_values = self.env.get_portfolio_values()
        return pd.Series(
            portfolio_values,
            index=self.df.index[self.window_size:self.window_size + len(portfolio_values)]
        )

    def _update_observation_space(self, new_n_features):
        """
        更新观察空间以适应新的特征数量 - 为了向后兼容

        参数:
            new_n_features (int): 新的特征数量
        """
        self.env.observation_handler.update_observation_space(new_n_features)
        self.observation_space = self.env.observation_handler.observation_space
        self.n_features = self.env.observation_handler.n_features
        self.original_obs_dim = self.env.observation_handler.obs_dim

    def force_observation_space(self, target_dim):
        """
        强制设置观察空间维度为指定值 - 为了向后兼容

        参数:
            target_dim (int): 目标观察空间维度
        """
        self.env.observation_handler.force_observation_space(target_dim)
        self.observation_space = self.env.observation_handler.observation_space
        self.n_features = self.env.observation_handler.n_features
        self.original_obs_dim = self.env.observation_handler.obs_dim

    def adjust_observation_space(self, target_dim):
        """
        调整观察空间维度以匹配目标维度 - 为了向后兼容

        参数:
            target_dim (int): 目标观察空间维度
        """
        self.env.observation_handler.adjust_observation_space(target_dim)
        self.observation_space = self.env.observation_handler.observation_space
        self.n_features = self.env.observation_handler.n_features
        self.original_obs_dim = self.env.observation_handler.obs_dim
