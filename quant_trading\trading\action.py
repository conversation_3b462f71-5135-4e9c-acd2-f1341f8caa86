"""
动作处理模块
负责处理交易环境的动作空间和动作执行
"""

import logging
import numpy as np
from gymnasium import spaces
from datetime import datetime
from enum import Enum

class Action(Enum):
    """交易动作枚举"""
    HOLD = 0    # 保持当前仓位
    BUY = 1     # 买入
    SELL = 2    # 卖出

class ActionHandler:
    """
    动作处理类
    负责处理交易环境的动作空间和动作执行
    """

    def __init__(self, commission_rate=0.0003, min_hold_days=3, allow_short=False, max_position=1.0, logger=None):
        """
        初始化动作处理器

        参数:
            commission_rate (float): 手续费率（单边）
            min_hold_days (int): 最小持仓天数
            allow_short (bool): 是否允许做空
            max_position (float): 最大仓位比例，范围[0, 1]
            logger (logging.Logger): 日志记录器
        """
        self.logger = logger or logging.getLogger('drl_trading')
        self.commission_rate = commission_rate
        self.min_hold_days = min_hold_days
        self.allow_short = allow_short
        self.max_position = max_position

        # 设置动作空间
        # 0: 空仓/保持当前仓位, 1: 全仓买入/做多, 2: 平掉所有多头仓位
        self.action_space = spaces.Discrete(3)

        # 交易记录
        self.trades = []

    def execute_action(self, action, current_step, current_price, cash, shares_held, holding_days, initial_capital):
        """
        执行交易动作

        参数:
            action (int): 动作，0=保持, 1=买入, 2=卖出
            current_step (int): 当前时间步
            current_price (float): 当前股票价格
            cash (float): 当前现金
            shares_held (int): 当前持有股票数量
            holding_days (int): 持仓天数
            initial_capital (float): 初始资金

        返回:
            tuple: (新的现金, 新的持有股票数量, 新的持仓天数, 交易成本, 是否进行了交易, 成本基础)
        """
        # 初始化返回值
        new_cash = cash
        new_shares_held = shares_held
        new_holding_days = holding_days
        trade_cost = 0
        is_trade = False
        cost_basis = 0

        # 执行动作
        if action == 0:  # 保持当前仓位
            # 如果持有股票，增加持仓天数
            if shares_held > 0:
                new_holding_days += 1
            return new_cash, new_shares_held, new_holding_days, trade_cost, is_trade, cost_basis

        elif action == 1:  # 买入
            # 如果已经持有股票，不执行任何操作
            if shares_held > 0:
                new_holding_days += 1
                return new_cash, new_shares_held, new_holding_days, trade_cost, is_trade, cost_basis

            # 计算可以购买的最大股数（考虑手续费）
            max_shares = int(cash / (current_price * (1 + self.commission_rate)))

            # 限制最大仓位
            target_shares = int(self.max_position * initial_capital / current_price)
            shares_to_buy = min(max_shares, target_shares)

            # 如果可以买入至少1股
            if shares_to_buy > 0:
                # 计算交易成本
                cost = shares_to_buy * current_price
                commission = cost * self.commission_rate
                total_cost = cost + commission

                # 执行交易
                new_cash = cash - total_cost
                new_shares_held = shares_held + shares_to_buy
                new_holding_days = 1  # 重置持仓天数
                trade_cost = commission
                is_trade = True
                cost_basis = current_price

                # 记录交易
                self.trades.append({
                    'step': current_step,
                    'action': 'buy',
                    'price': current_price,
                    'shares': shares_to_buy,
                    'cost': cost,
                    'commission': commission,
                    'total_cost': total_cost,
                    'date': datetime.now().strftime('%Y-%m-%d')  # 添加日期字段
                })

                self.logger.info(f"买入 {shares_to_buy} 股，价格: {current_price:.2f}，总成本: {total_cost:.2f}，手续费: {commission:.2f}")

        elif action == 2:  # 卖出
            # 如果没有持有股票，不执行任何操作
            if shares_held <= 0:
                return new_cash, new_shares_held, new_holding_days, trade_cost, is_trade, cost_basis

            # 检查最小持仓天数
            if holding_days < self.min_hold_days:
                self.logger.info(f"持仓天数 ({holding_days}) 小于最小持仓天数 ({self.min_hold_days})，不能卖出")
                new_holding_days += 1
                return new_cash, new_shares_held, new_holding_days, trade_cost, is_trade, cost_basis

            # 计算交易成本
            revenue = shares_held * current_price
            commission = revenue * self.commission_rate
            total_revenue = revenue - commission

            # 执行交易
            new_cash = cash + total_revenue
            new_shares_held = 0
            new_holding_days = 0
            trade_cost = commission
            is_trade = True

            # 记录交易
            self.trades.append({
                'step': current_step,
                'action': 'sell',
                'price': current_price,
                'shares': shares_held,
                'revenue': revenue,
                'commission': commission,
                'total_revenue': total_revenue,
                'date': datetime.now().strftime('%Y-%m-%d')  # 添加日期字段
            })

            self.logger.info(f"卖出 {shares_held} 股，价格: {current_price:.2f}，总收入: {total_revenue:.2f}，手续费: {commission:.2f}")

        return new_cash, new_shares_held, new_holding_days, trade_cost, is_trade, cost_basis

    def get_trades(self):
        """
        获取交易记录

        返回:
            list: 交易记录列表
        """
        return self.trades

    def reset(self):
        """
        重置动作处理器
        """
        self.trades = []
