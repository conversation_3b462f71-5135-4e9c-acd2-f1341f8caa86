"""
工具函数模块
提供项目中使用的通用工具函数
"""

import os
import logging
from datetime import datetime
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import yaml

def setup_logger(log_file='logs/app.log', level=logging.INFO):
    """
    设置日志记录器

    参数:
        log_file (str): 日志文件路径
        level (int): 日志级别

    返回:
        logging.Logger: 配置好的日志记录器
    """
    # 确保日志目录存在
    os.makedirs(os.path.dirname(log_file), exist_ok=True)

    # 创建日志记录器
    logger = logging.getLogger('drl_trading')
    logger.setLevel(level)

    # 创建文件处理器
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(level)

    # 创建控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)

    # 创建格式化器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # 添加处理器到日志记录器
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger

def load_config(config_file):
    """
    从YAML文件加载配置

    参数:
        config_file (str): 配置文件路径

    返回:
        dict: 配置字典
    """
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except Exception as e:
        logger = logging.getLogger('drl_trading')
        logger.error(f"加载配置文件失败: {str(e)}")
        return {}

def save_config(config, config_file):
    """
    保存配置到YAML文件

    参数:
        config (dict): 配置字典
        config_file (str): 配置文件路径
    """
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(config_file), exist_ok=True)

        with open(config_file, 'w', encoding='utf-8') as f:
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
    except Exception as e:
        logger = logging.getLogger('drl_trading')
        logger.error(f"保存配置文件失败: {str(e)}")

def normalize_data(data, method='minmax'):
    """
    归一化数据

    参数:
        data (numpy.ndarray or pandas.DataFrame): 要归一化的数据
        method (str): 归一化方法，可选 'minmax' 或 'zscore'

    返回:
        numpy.ndarray or pandas.DataFrame: 归一化后的数据
    """
    # 确保数据是数值类型
    if isinstance(data, pd.DataFrame):
        # 创建数据的副本，避免SettingWithCopyWarning
        data = data.copy()
        # 尝试将所有列转换为数值类型
        for col in data.columns:
            try:
                data.loc[:, col] = pd.to_numeric(data[col], errors='coerce')
            except:
                pass

    if method == 'minmax':
        if isinstance(data, pd.DataFrame):
            # 处理可能的NaN值
            min_vals = data.min()
            max_vals = data.max()
            return (data - min_vals) / (max_vals - min_vals + 1e-8)
        else:
            return (data - np.nanmin(data)) / (np.nanmax(data) - np.nanmin(data) + 1e-8)
    elif method == 'zscore':
        if isinstance(data, pd.DataFrame):
            return (data - data.mean()) / (data.std() + 1e-8)
        else:
            return (data - np.nanmean(data)) / (np.nanstd(data) + 1e-8)
    else:
        raise ValueError(f"不支持的归一化方法: {method}")

def calculate_sharpe_ratio(returns, risk_free_rate=0.0, periods_per_year=252):
    """
    计算夏普比率

    参数:
        returns (numpy.ndarray or pandas.Series): 收益率序列
        risk_free_rate (float): 无风险利率
        periods_per_year (int): 每年的周期数，日线=252，周线=52，月线=12

    返回:
        float: 夏普比率
    """
    excess_returns = returns - risk_free_rate / periods_per_year
    return np.sqrt(periods_per_year) * np.mean(excess_returns) / (np.std(excess_returns) + 1e-8)

def calculate_drawdown(equity_curve):
    """
    计算回撤序列和最大回撤

    参数:
        equity_curve (numpy.ndarray or pandas.Series): 净值曲线

    返回:
        tuple: (回撤序列, 最大回撤, 最大回撤开始点, 最大回撤结束点)
    """
    # 计算累计最大值
    running_max = np.maximum.accumulate(equity_curve)
    # 计算回撤序列
    drawdown = (equity_curve - running_max) / running_max
    # 找到最大回撤及其位置
    max_drawdown = np.min(drawdown)
    end_idx = np.argmin(drawdown)
    # 找到最大回撤的开始点
    start_idx = np.argmax(equity_curve[:end_idx])

    return drawdown, max_drawdown, start_idx, end_idx

def plot_equity_curve(equity_curve, benchmark=None, title="策略净值曲线"):
    """
    绘制净值曲线

    参数:
        equity_curve (pandas.Series): 策略净值曲线
        benchmark (pandas.Series, optional): 基准净值曲线
        title (str): 图表标题

    返回:
        matplotlib.figure.Figure: 图表对象
    """
    fig, ax = plt.subplots(figsize=(12, 6))

    # 绘制策略净值曲线
    ax.plot(equity_curve.index, equity_curve.values, label="策略", linewidth=2)

    # 如果有基准，也绘制基准净值曲线
    if benchmark is not None:
        ax.plot(benchmark.index, benchmark.values, label="基准", linewidth=2, alpha=0.7)

    # 添加图例和标题
    ax.legend()
    ax.set_title(title)
    ax.set_xlabel("日期")
    ax.set_ylabel("净值")
    ax.grid(True)

    return fig

def is_gpu_available():
    """
    检查是否有可用的GPU，使用多种方法进行检测

    返回:
        bool: 是否有可用的GPU
    """
    # 方法1: 使用PyTorch检测
    try:
        import torch
        if torch.cuda.is_available():
            return True
    except ImportError:
        pass

    # 方法2: 使用TensorFlow检测
    try:
        import tensorflow as tf
        if len(tf.config.list_physical_devices('GPU')) > 0:
            return True
    except ImportError:
        pass

    # 方法3: 直接检查NVIDIA-SMI
    try:
        import subprocess
        result = subprocess.run(['nvidia-smi'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, timeout=3)
        if result.returncode == 0 and 'NVIDIA-SMI' in result.stdout:
            return True
    except (subprocess.SubprocessError, FileNotFoundError):
        pass

    # 方法4: 检查Windows系统信息
    try:
        import subprocess
        import re
        result = subprocess.run(['wmic', 'path', 'win32_VideoController', 'get', 'name'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, timeout=3)
        if result.returncode == 0:
            gpu_info = result.stdout.lower()
            # 检查是否包含NVIDIA、AMD或其他常见GPU厂商名称
            if re.search(r'nvidia|geforce|quadro|rtx|gtx', gpu_info):
                return True
    except (subprocess.SubprocessError, FileNotFoundError):
        pass

    # 方法5: 检查DirectX诊断工具
    try:
        import subprocess
        import re
        result = subprocess.run(['dxdiag', '/t', 'temp_dxdiag.txt'], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, timeout=10)
        if result.returncode == 0:
            # 等待文件生成
            import time
            time.sleep(2)

            # 读取文件
            with open('temp_dxdiag.txt', 'r') as f:
                dxdiag_output = f.read().lower()

            # 删除临时文件
            import os
            try:
                os.remove('temp_dxdiag.txt')
            except:
                pass

            # 检查是否包含NVIDIA、AMD或其他常见GPU厂商名称
            if re.search(r'nvidia|geforce|quadro|rtx|gtx', dxdiag_output):
                return True
    except:
        pass

    return False

def get_gpu_info():
    """
    获取GPU详细信息，使用多种方法尝试获取

    返回:
        dict: GPU信息字典，包含GPU数量、名称、显存等信息
    """
    gpu_info = {
        'available': False,
        'count': 0,
        'devices': [],
        'framework': None,
        'memory': [],
        'detection_method': None
    }

    # 方法1: 尝试使用PyTorch获取GPU信息
    try:
        import torch
        if torch.cuda.is_available():
            gpu_info['available'] = True
            gpu_info['framework'] = 'PyTorch'
            gpu_info['count'] = torch.cuda.device_count()
            gpu_info['detection_method'] = 'PyTorch'

            for i in range(gpu_info['count']):
                device_name = torch.cuda.get_device_name(i)
                gpu_info['devices'].append(device_name)

                # 获取显存信息
                try:
                    total_memory = torch.cuda.get_device_properties(i).total_memory / (1024**3)  # GB
                    reserved_memory = torch.cuda.memory_reserved(i) / (1024**3)  # GB
                    allocated_memory = torch.cuda.memory_allocated(i) / (1024**3)  # GB
                    free_memory = total_memory - allocated_memory

                    gpu_info['memory'].append({
                        'total': round(total_memory, 2),
                        'reserved': round(reserved_memory, 2),
                        'allocated': round(allocated_memory, 2),
                        'free': round(free_memory, 2)
                    })
                except:
                    gpu_info['memory'].append({'total': 'Unknown', 'free': 'Unknown'})

            return gpu_info
    except ImportError:
        pass

    # 方法2: 如果PyTorch不可用，尝试使用TensorFlow
    try:
        import tensorflow as tf
        gpus = tf.config.list_physical_devices('GPU')
        if len(gpus) > 0:
            gpu_info['available'] = True
            gpu_info['framework'] = 'TensorFlow'
            gpu_info['count'] = len(gpus)
            gpu_info['detection_method'] = 'TensorFlow'

            for gpu in gpus:
                gpu_info['devices'].append(gpu.name)

                # TensorFlow不提供直接的显存查询API，尝试使用nvidia-smi
                try:
                    import subprocess
                    result = subprocess.check_output(['nvidia-smi', '--query-gpu=memory.total,memory.used,memory.free', '--format=csv,nounits,noheader'])
                    memory_info = result.decode('utf-8').strip().split('\n')
                    for i, line in enumerate(memory_info):
                        if i < len(gpu_info['devices']):
                            total, used, free = map(int, line.split(','))
                            gpu_info['memory'].append({
                                'total': round(total / 1024, 2),  # GB
                                'used': round(used / 1024, 2),    # GB
                                'free': round(free / 1024, 2)     # GB
                            })
                except:
                    for _ in range(gpu_info['count']):
                        gpu_info['memory'].append({'total': 'Unknown', 'free': 'Unknown'})

            return gpu_info
    except ImportError:
        pass

    # 方法3: 如果深度学习框架都不可用，尝试直接使用nvidia-smi
    try:
        import subprocess
        import re

        # 检查NVIDIA GPU
        result = subprocess.run(['nvidia-smi', '--query-gpu=name,memory.total,memory.used,memory.free', '--format=csv,noheader,nounits'],
                               stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, timeout=3)

        if result.returncode == 0 and result.stdout.strip():
            gpu_info['available'] = True
            gpu_info['framework'] = 'System'
            gpu_info['detection_method'] = 'nvidia-smi'

            lines = result.stdout.strip().split('\n')
            gpu_info['count'] = len(lines)

            for line in lines:
                parts = line.split(',')
                if len(parts) >= 4:
                    device_name = parts[0].strip()
                    gpu_info['devices'].append(device_name)

                    try:
                        total = float(parts[1].strip())
                        used = float(parts[2].strip())
                        free = float(parts[3].strip())

                        gpu_info['memory'].append({
                            'total': round(total / 1024, 2),  # GB
                            'used': round(used / 1024, 2),    # GB
                            'free': round(free / 1024, 2)     # GB
                        })
                    except:
                        gpu_info['memory'].append({'total': 'Unknown', 'free': 'Unknown'})

            return gpu_info
    except:
        pass

    # 方法4: 使用Windows系统信息
    try:
        import subprocess
        import re

        # 获取GPU名称
        result = subprocess.run(['wmic', 'path', 'win32_VideoController', 'get', 'name,AdapterRAM'],
                               stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, timeout=3)

        if result.returncode == 0 and len(result.stdout.strip().split('\n')) > 1:
            lines = result.stdout.strip().split('\n')
            # 跳过标题行
            gpu_lines = [line for line in lines[1:] if line.strip()]

            if gpu_lines:
                gpu_info['available'] = True
                gpu_info['framework'] = 'System'
                gpu_info['detection_method'] = 'wmic'
                gpu_info['count'] = len(gpu_lines)

                for line in gpu_lines:
                    # 尝试提取名称和内存
                    match = re.search(r'(.*?)\s+(\d+)$', line.strip())
                    if match:
                        device_name = match.group(1).strip()
                        ram_bytes = int(match.group(2))
                        gpu_info['devices'].append(device_name)

                        gpu_info['memory'].append({
                            'total': round(ram_bytes / (1024**3), 2),  # GB
                            'free': 'Unknown'
                        })
                    else:
                        device_name = line.strip()
                        gpu_info['devices'].append(device_name)
                        gpu_info['memory'].append({'total': 'Unknown', 'free': 'Unknown'})

                return gpu_info
    except:
        pass

    # 方法5: 使用DirectX诊断工具
    if is_gpu_available() and gpu_info['count'] == 0:
        gpu_info['available'] = True
        gpu_info['framework'] = 'System'
        gpu_info['detection_method'] = 'system_check'
        gpu_info['count'] = 1
        gpu_info['devices'].append('GPU Detected (Details Unavailable)')
        gpu_info['memory'].append({'total': 'Unknown', 'free': 'Unknown'})

    return gpu_info

def setup_gpu_environment(auto_install=False):
    """
    设置GPU环境，包括内存增长限制和可见设备配置
    支持多种GPU类型和深度学习框架

    参数:
        auto_install (bool): 是否自动安装GPU支持的深度学习框架

    返回:
        dict: 配置结果信息
    """
    result = {
        'success': False,
        'framework': None,
        'message': '未检测到可用的深度学习框架',
        'gpu_info': None,
        'cuda_version': None,
        'cudnn_version': None,
        'auto_install_attempted': False,
        'auto_install_result': None
    }

    # 获取GPU信息
    gpu_info = get_gpu_info()
    result['gpu_info'] = gpu_info

    # 如果系统检测到GPU但深度学习框架没有检测到，尝试安装或配置CUDA
    if gpu_info['available'] and gpu_info['detection_method'] in ['wmic', 'system_check', 'nvidia-smi']:
        # 尝试安装PyTorch CUDA版本
        try:
            import subprocess
            import sys

            # 检查CUDA版本
            try:
                cuda_version = None
                nvcc_output = subprocess.check_output(['nvcc', '--version'], stderr=subprocess.STDOUT, text=True)
                import re
                match = re.search(r'release (\d+\.\d+)', nvcc_output)
                if match:
                    cuda_version = match.group(1)
                    result['cuda_version'] = cuda_version
            except:
                pass

            # 如果启用了自动安装且检测到GPU但深度学习框架未能识别
            if auto_install:
                try:
                    import importlib.util
                    # 检查install_gpu_support.py是否存在
                    install_script_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'install_gpu_support.py')

                    if os.path.exists(install_script_path):
                        # 导入安装脚本
                        spec = importlib.util.spec_from_file_location("install_gpu_support", install_script_path)
                        install_module = importlib.util.module_from_spec(spec)
                        spec.loader.exec_module(install_module)

                        # 执行安装
                        result['auto_install_attempted'] = True
                        install_result = install_module.main(return_result=True)
                        result['auto_install_result'] = install_result

                        # 更新消息
                        if install_result.get('success', False):
                            result['message'] = '已自动安装GPU支持的深度学习框架\n'
                            if install_result.get('pytorch_success', False):
                                result['message'] += '- PyTorch GPU版本安装成功\n'
                            if install_result.get('tensorflow_success', False):
                                result['message'] += '- TensorFlow GPU版本安装成功\n'
                        else:
                            result['message'] = '自动安装GPU支持失败，请手动安装\n'
                except Exception as e:
                    result['message'] = f'自动安装GPU支持时出错: {str(e)}\n'
            else:
                result['message'] = '检测到GPU但深度学习框架未能识别，尝试以下步骤配置GPU环境:\n'
                result['message'] += '1. 确保已安装最新的NVIDIA驱动\n'
                result['message'] += '2. 安装与CUDA兼容的PyTorch版本: pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118\n'
                result['message'] += '3. 或安装GPU版本的TensorFlow: pip install tensorflow\n'
                result['message'] += '4. 或运行 python install_gpu_support.py 自动安装GPU支持\n'

                if cuda_version:
                    result['message'] += f'5. 当前CUDA版本: {cuda_version}，请确保深度学习框架与此版本兼容\n'
        except Exception as e:
            result['message'] = f'配置GPU环境时出错: {str(e)}\n'

    # 尝试配置PyTorch环境
    try:
        import torch
        if torch.cuda.is_available():
            # 获取CUDA和cuDNN版本信息
            cuda_version = torch.version.cuda
            cudnn_version = torch.backends.cudnn.version() if hasattr(torch.backends.cudnn, 'version') else None

            result['cuda_version'] = cuda_version
            result['cudnn_version'] = cudnn_version

            # 设置PyTorch使用确定性算法，提高可重复性
            torch.backends.cudnn.deterministic = True
            torch.backends.cudnn.benchmark = False

            # 清理缓存
            torch.cuda.empty_cache()

            # 设置自动混合精度
            try:
                # 检查是否支持自动混合精度
                if hasattr(torch.cuda, 'amp') and torch.cuda.is_available():
                    # PyTorch 1.6+支持自动混合精度
                    result['amp_supported'] = True
            except:
                result['amp_supported'] = False

            result['success'] = True
            result['framework'] = 'PyTorch'
            result['message'] = f'成功配置PyTorch GPU环境，检测到 {torch.cuda.device_count()} 个GPU设备'
            if cuda_version:
                result['message'] += f'，CUDA版本: {cuda_version}'
            if cudnn_version:
                result['message'] += f'，cuDNN版本: {cudnn_version}'

            return result
    except ImportError:
        pass
    except Exception as e:
        result['message'] = f'PyTorch GPU配置错误: {str(e)}'

    # 尝试配置TensorFlow环境
    try:
        import tensorflow as tf
        gpus = tf.config.list_physical_devices('GPU')
        if len(gpus) > 0:
            # 获取TensorFlow版本信息
            tf_version = tf.__version__
            result['tf_version'] = tf_version

            # 尝试获取CUDA版本
            try:
                cuda_version = tf.sysconfig.get_build_info()['cuda_version']
                result['cuda_version'] = cuda_version
            except:
                pass

            # 设置内存增长，避免一次性分配所有显存
            for gpu in gpus:
                try:
                    tf.config.experimental.set_memory_growth(gpu, True)
                except RuntimeError as e:
                    result['message'] = f'无法设置TensorFlow内存增长: {str(e)}'
                    return result

            # 设置混合精度
            try:
                if tf.__version__ >= '2.4.0':
                    policy = tf.keras.mixed_precision.Policy('mixed_float16')
                    tf.keras.mixed_precision.set_global_policy(policy)
                    result['amp_supported'] = True
                else:
                    # 旧版TensorFlow
                    from tensorflow.keras.mixed_precision import experimental as mixed_precision
                    policy = mixed_precision.Policy('mixed_float16')
                    mixed_precision.set_global_policy(policy)
                    result['amp_supported'] = True
            except:
                # 混合精度设置失败不影响主要功能
                result['amp_supported'] = False

            result['success'] = True
            result['framework'] = 'TensorFlow'
            result['message'] = f'成功配置TensorFlow GPU环境，检测到 {len(gpus)} 个GPU设备，TensorFlow版本: {tf_version}'
            if 'cuda_version' in result and result['cuda_version']:
                result['message'] += f'，CUDA版本: {result["cuda_version"]}'

            return result
    except ImportError:
        pass
    except Exception as e:
        result['message'] = f'TensorFlow GPU配置错误: {str(e)}'

    # 如果深度学习框架都不可用，但系统检测到GPU
    if gpu_info['available']:
        result['message'] = '检测到系统有GPU，但未能配置深度学习框架。请安装GPU版本的PyTorch或TensorFlow。'
        # 提供安装命令建议
        result['message'] += '\n\n推荐安装命令:\n'
        result['message'] += 'PyTorch: pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118\n'
        result['message'] += 'TensorFlow: pip install tensorflow'

        # 添加自动安装脚本信息
        result['install_script_available'] = True
        result['install_script_path'] = 'install_gpu_support.py'

    return result

def detect_gpu():
    """
    检测系统GPU并返回详细信息

    返回:
        dict: GPU信息字典
    """
    return get_gpu_info()

def diagnose_gpu_issues():
    """
    诊断GPU相关问题并提供解决方案建议

    返回:
        dict: 诊断结果和建议
    """
    diagnosis = {
        'issues_detected': False,
        'problems': [],
        'suggestions': [],
        'gpu_detected': False,
        'cuda_detected': False,
        'pytorch_gpu': False,
        'tensorflow_gpu': False,
        'system_info': {},
        'cuda_version': None,
        'driver_version': None
    }

    # 获取系统信息
    try:
        import platform
        diagnosis['system_info']['os'] = platform.system()
        diagnosis['system_info']['os_version'] = platform.version()
        diagnosis['system_info']['python_version'] = platform.python_version()
    except:
        pass

    # 检查系统是否有GPU硬件
    gpu_info = get_gpu_info()
    diagnosis['gpu_detected'] = gpu_info['available']
    diagnosis['gpu_info'] = gpu_info

    # 如果系统检测到GPU但深度学习框架没有检测到
    if gpu_info['available'] and gpu_info['detection_method'] in ['wmic', 'system_check', 'nvidia-smi']:
        diagnosis['issues_detected'] = True
        diagnosis['problems'].append('系统检测到GPU硬件，但深度学习框架无法使用GPU')

        # 检查NVIDIA驱动
        try:
            import subprocess
            try:
                nvidia_smi_output = subprocess.check_output(['nvidia-smi'], text=True)
                # 提取驱动版本
                import re
                driver_match = re.search(r'Driver Version: (\d+\.\d+\.\d+)', nvidia_smi_output)
                if driver_match:
                    driver_version = driver_match.group(1)
                    diagnosis['driver_version'] = driver_version
                    diagnosis['suggestions'].append(f'当前NVIDIA驱动版本: {driver_version}')

                # 检查CUDA版本
                try:
                    nvcc_output = subprocess.check_output(['nvcc', '--version'], stderr=subprocess.STDOUT, text=True)
                    cuda_match = re.search(r'release (\d+\.\d+)', nvcc_output)
                    if cuda_match:
                        cuda_version = cuda_match.group(1)
                        diagnosis['cuda_version'] = cuda_version
                        diagnosis['cuda_detected'] = True
                        diagnosis['suggestions'].append(f'当前CUDA版本: {cuda_version}')
                except:
                    diagnosis['problems'].append('未检测到CUDA工具包(nvcc)')
                    diagnosis['suggestions'].append('请安装CUDA工具包: https://developer.nvidia.com/cuda-downloads')
            except:
                diagnosis['problems'].append('无法执行nvidia-smi命令')
                diagnosis['suggestions'].append('请确保已安装NVIDIA驱动')
                diagnosis['suggestions'].append('访问 https://www.nvidia.com/Download/index.aspx 下载并安装最新驱动')
        except:
            pass

    # 检查PyTorch GPU支持
    try:
        import torch
        diagnosis['pytorch_installed'] = True
        diagnosis['pytorch_version'] = torch.__version__

        if torch.cuda.is_available():
            diagnosis['pytorch_gpu'] = True
            diagnosis['cuda_version'] = torch.version.cuda
        else:
            diagnosis['issues_detected'] = True
            diagnosis['problems'].append(f'PyTorch {torch.__version__} 无法检测到CUDA')

            # 检查是否安装了CPU版本的PyTorch
            if 'cpu' in torch.__version__:
                diagnosis['problems'].append('您安装的是CPU版本的PyTorch')
                diagnosis['suggestions'].append('请卸载当前PyTorch并安装GPU版本')

            # 根据CUDA版本推荐安装命令
            if diagnosis.get('cuda_version'):
                cuda_version = diagnosis['cuda_version']
                if cuda_version.startswith('11.'):
                    diagnosis['suggestions'].append(f'运行 "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118" 安装支持CUDA 11.8的PyTorch')
                elif cuda_version.startswith('12.'):
                    diagnosis['suggestions'].append(f'运行 "pip install torch torchvision torchaudio" 安装支持CUDA 12.x的PyTorch')
                else:
                    diagnosis['suggestions'].append(f'运行 "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118" 安装支持CUDA的PyTorch')
            else:
                diagnosis['suggestions'].append('运行 "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118" 安装支持CUDA 11.8的PyTorch')
    except ImportError:
        diagnosis['pytorch_installed'] = False
        diagnosis['suggestions'].append('PyTorch未安装，运行 "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118" 安装支持CUDA的PyTorch')

    # 检查TensorFlow GPU支持
    try:
        import tensorflow as tf
        diagnosis['tensorflow_installed'] = True
        diagnosis['tensorflow_version'] = tf.__version__

        if len(tf.config.list_physical_devices('GPU')) > 0:
            diagnosis['tensorflow_gpu'] = True
        else:
            diagnosis['issues_detected'] = True
            diagnosis['problems'].append(f'TensorFlow {tf.__version__} 无法检测到GPU')
            diagnosis['suggestions'].append('请确保已安装GPU版本的TensorFlow')
            diagnosis['suggestions'].append('运行 "pip install tensorflow" 安装最新版TensorFlow (包含GPU支持)')
    except ImportError:
        diagnosis['tensorflow_installed'] = False

    # 检查环境变量
    try:
        import os
        cuda_path = os.environ.get('CUDA_PATH')
        if cuda_path:
            diagnosis['cuda_path'] = cuda_path
        else:
            diagnosis['problems'].append('未设置CUDA_PATH环境变量')
            diagnosis['suggestions'].append('请设置CUDA_PATH环境变量指向CUDA安装目录')
    except:
        pass

    # 检查虚拟环境
    try:
        import sys
        if hasattr(sys, 'prefix'):
            diagnosis['virtual_env'] = sys.prefix

            # 检查是否在虚拟环境中
            import os
            if os.path.exists(os.path.join(sys.prefix, 'pyvenv.cfg')):
                diagnosis['in_virtual_env'] = True
            else:
                diagnosis['in_virtual_env'] = False
                diagnosis['suggestions'].append('建议在虚拟环境中安装和配置GPU支持，以避免系统级依赖冲突')
    except:
        pass

    # 如果没有检测到具体问题但GPU仍不可用
    if not diagnosis['issues_detected'] and not is_gpu_available():
        diagnosis['issues_detected'] = True
        diagnosis['problems'].append('未检测到具体问题，但GPU仍不可用')
        diagnosis['suggestions'].append('请检查系统是否有兼容的NVIDIA GPU')
        diagnosis['suggestions'].append('确保在虚拟环境中安装了正确的深度学习框架GPU版本')
        diagnosis['suggestions'].append('尝试重启计算机后再次检测')

    # 如果检测到GPU但深度学习框架都不支持
    if diagnosis['gpu_detected'] and not (diagnosis.get('pytorch_gpu', False) or diagnosis.get('tensorflow_gpu', False)):
        diagnosis['issues_detected'] = True
        if not diagnosis.get('pytorch_installed', False) and not diagnosis.get('tensorflow_installed', False):
            diagnosis['problems'].append('未安装任何深度学习框架')
            diagnosis['suggestions'].append('请安装PyTorch或TensorFlow')

        # 提供自动安装脚本建议
        diagnosis['suggestions'].append('可以创建以下安装脚本来配置GPU环境:')
        diagnosis['install_script'] = """
# 创建install_gpu.py文件，内容如下:
import subprocess
import sys

def install_gpu_support():
    print("正在安装GPU支持...")

    # 安装PyTorch GPU版本
    subprocess.check_call([sys.executable, '-m', 'pip', 'install',
                          'torch', 'torchvision', 'torchaudio',
                          '--index-url', 'https://download.pytorch.org/whl/cu118'])

    # 安装TensorFlow
    subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'tensorflow'])

    print("GPU支持安装完成，请重启应用程序")

if __name__ == "__main__":
    install_gpu_support()
"""
        diagnosis['suggestions'].append('运行 "python install_gpu.py" 来安装GPU支持')

    return diagnosis
