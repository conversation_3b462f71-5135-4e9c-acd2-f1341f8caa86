#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
UI会话状态测试脚本
测试UI会话状态管理和参数传递
"""

import os
import sys
import time
import logging
import subprocess
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import traceback
import json
import yaml
import re

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join('logs', 'ui_session_test.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('ui_session_test')

def monitor_log_file(log_file, patterns, timeout=60):
    """
    监控日志文件，查找匹配的模式
    
    参数:
        log_file (str): 日志文件路径
        patterns (dict): 要查找的模式字典，键为模式名称，值为正则表达式
        timeout (int): 超时时间（秒）
    
    返回:
        dict: 匹配结果字典，键为模式名称，值为是否匹配
    """
    logger.info(f"开始监控日志文件: {log_file}")
    
    # 确保日志文件存在
    if not os.path.exists(log_file):
        logger.warning(f"日志文件不存在: {log_file}")
        return {name: False for name in patterns}
    
    # 初始化结果
    results = {name: False for name in patterns}
    compiled_patterns = {name: re.compile(pattern) for name, pattern in patterns.items()}
    
    # 记录开始时间
    start_time = time.time()
    
    # 获取初始文件大小
    initial_size = os.path.getsize(log_file)
    
    while time.time() - start_time < timeout:
        # 检查文件是否有更新
        current_size = os.path.getsize(log_file)
        
        if current_size > initial_size:
            # 读取新内容
            with open(log_file, 'r', encoding='utf-8') as f:
                f.seek(initial_size)
                new_content = f.read()
                initial_size = current_size
            
            # 检查是否匹配模式
            for name, pattern in compiled_patterns.items():
                if not results[name] and pattern.search(new_content):
                    logger.info(f"找到匹配模式: {name}")
                    results[name] = True
            
            # 如果所有模式都匹配，提前退出
            if all(results.values()):
                logger.info("所有模式都已匹配")
                break
        
        # 等待一段时间
        time.sleep(1)
    
    # 检查超时
    if time.time() - start_time >= timeout:
        logger.warning(f"监控超时，未找到所有匹配模式")
    
    return results

def test_session_state():
    """测试会话状态管理"""
    logger.info("测试会话状态管理")
    
    try:
        # 使用subprocess启动应用
        process = subprocess.Popen(
            [sys.executable, "-m", "streamlit", "run", "main_app.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待应用启动
        time.sleep(5)
        
        # 检查进程是否仍在运行
        if process.poll() is not None:
            stdout, stderr = process.communicate()
            logger.error(f"应用启动失败: {stderr}")
            return False
        
        # 定义要监控的日志模式
        log_file = os.path.join('logs', 'app.log')
        patterns = {
            '数据加载': r'成功获取股票.*的数据',
            '特征生成': r'特征生成完成',
            '环境配置保存': r'环境配置已保存',
            '会话状态更新': r'会话状态已更新'
        }
        
        # 监控日志文件
        results = monitor_log_file(log_file, patterns, timeout=60)
        
        # 终止进程
        process.terminate()
        process.wait(timeout=5)
        
        # 分析结果
        success = all(results.values())
        logger.info(f"会话状态测试结果: {'成功' if success else '失败'}")
        logger.info(f"详细结果: {results}")
        
        return {
            'success': success,
            'patterns_matched': results
        }
    
    except Exception as e:
        logger.error(f"测试会话状态管理时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            'success': False,
            'error': str(e)
        }

def test_parameter_passing():
    """测试参数传递"""
    logger.info("测试参数传递")
    
    try:
        # 使用subprocess启动应用
        process = subprocess.Popen(
            [sys.executable, "-m", "streamlit", "run", "main_app.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待应用启动
        time.sleep(5)
        
        # 检查进程是否仍在运行
        if process.poll() is not None:
            stdout, stderr = process.communicate()
            logger.error(f"应用启动失败: {stderr}")
            return False
        
        # 定义要监控的日志模式
        log_file = os.path.join('logs', 'app.log')
        patterns = {
            '股票代码参数': r'股票代码: sh000001',
            '日期参数': r'开始日期: \d{4}-\d{2}-\d{2}, 结束日期: \d{4}-\d{2}-\d{2}',
            '频率参数': r'频率: 日线',
            '初始资金参数': r'初始资金: 100000',
            '手续费参数': r'手续费率: 0.0003',
            '算法参数': r'算法: (PPO|A2C|DQN)',
            '训练步数参数': r'训练步数: \d+'
        }
        
        # 监控日志文件
        results = monitor_log_file(log_file, patterns, timeout=60)
        
        # 终止进程
        process.terminate()
        process.wait(timeout=5)
        
        # 分析结果
        success_rate = sum(results.values()) / len(results)
        logger.info(f"参数传递测试结果: 成功率 {success_rate:.2f}")
        logger.info(f"详细结果: {results}")
        
        return {
            'success_rate': success_rate,
            'patterns_matched': results
        }
    
    except Exception as e:
        logger.error(f"测试参数传递时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            'success_rate': 0,
            'error': str(e)
        }

def test_ui_backend_consistency():
    """测试UI和后端一致性"""
    logger.info("测试UI和后端一致性")
    
    try:
        # 使用subprocess启动应用
        process = subprocess.Popen(
            [sys.executable, "-m", "streamlit", "run", "main_app.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待应用启动
        time.sleep(5)
        
        # 检查进程是否仍在运行
        if process.poll() is not None:
            stdout, stderr = process.communicate()
            logger.error(f"应用启动失败: {stderr}")
            return False
        
        # 定义要监控的日志模式
        log_file = os.path.join('logs', 'app.log')
        patterns = {
            'UI特征配置': r'UI特征配置: \{.*\}',
            '后端特征配置': r'后端特征配置: \{.*\}',
            'UI环境配置': r'UI环境配置: \{.*\}',
            '后端环境配置': r'后端环境配置: \{.*\}',
            'UI智能体配置': r'UI智能体配置: \{.*\}',
            '后端智能体配置': r'后端智能体配置: \{.*\}'
        }
        
        # 监控日志文件
        results = monitor_log_file(log_file, patterns, timeout=60)
        
        # 终止进程
        process.terminate()
        process.wait(timeout=5)
        
        # 分析结果
        success_rate = sum(results.values()) / len(results)
        logger.info(f"UI和后端一致性测试结果: 成功率 {success_rate:.2f}")
        logger.info(f"详细结果: {results}")
        
        return {
            'success_rate': success_rate,
            'patterns_matched': results
        }
    
    except Exception as e:
        logger.error(f"测试UI和后端一致性时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            'success_rate': 0,
            'error': str(e)
        }

def run_all_tests():
    """运行所有会话状态测试"""
    logger.info("开始运行所有UI会话状态测试")
    
    # 确保测试目录存在
    os.makedirs('test_results', exist_ok=True)
    os.makedirs('logs', exist_ok=True)
    
    # 测试会话状态管理
    session_state_results = test_session_state()
    
    # 测试参数传递
    parameter_passing_results = test_parameter_passing()
    
    # 测试UI和后端一致性
    ui_backend_consistency_results = test_ui_backend_consistency()
    
    # 汇总测试结果
    results = {
        '会话状态管理': session_state_results,
        '参数传递': parameter_passing_results,
        'UI和后端一致性': ui_backend_consistency_results
    }
    
    logger.info(f"所有会话状态测试完成，结果: {results}")
    
    # 保存测试结果
    os.makedirs('test_results', exist_ok=True)
    with open('test_results/ui_session_test_results.json', 'w') as f:
        json.dump(results, f, indent=4, default=str)
    
    return results

if __name__ == "__main__":
    run_all_tests()
