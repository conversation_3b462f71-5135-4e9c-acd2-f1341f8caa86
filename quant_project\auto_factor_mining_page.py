"""
自动因子挖掘页面
实现自动因子挖掘的UI界面
"""

import streamlit as st
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os
import logging
import time
from scipy.interpolate import interp1d
import sys

def apply_factors_to_dataframe(data, factors, add_prefix=True):
    """
    将挖掘的因子应用到数据帧
    
    参数:
        data (pd.DataFrame): 目标数据帧
        factors (dict): 因子字典 {name: pd.Series}
        add_prefix (bool): 是否添加"factor_"前缀
    
    返回:
        pd.DataFrame: 添加因子后的数据帧
        list: 成功添加的因子名称列表
    """
    if not factors:
        return data, []
    
    # 创建数据副本
    new_data = data.copy()
    factors_added = []
    
    # 应用每个因子
    for name, factor_series in factors.items():
        try:
            # 尝试重新索引对齐
            if isinstance(new_data.index, pd.DatetimeIndex) and isinstance(factor_series.index, pd.DatetimeIndex):
                # 日期索引对齐
                aligned_factor = factor_series.reindex(new_data.index)
                
                # 检查对齐后的有效值
                valid_pct = aligned_factor.notna().mean()
                if valid_pct >= 0.7:  # 至少70%有效值
                    # 填充缺失值
                    aligned_factor = aligned_factor.fillna(method='ffill').fillna(method='bfill')
                    factor_name = f"factor_{name}" if add_prefix else name
                    new_data[factor_name] = aligned_factor.values
                    factors_added.append(name)
                else:
                    # 尝试插值对齐
                    try:
                        # 使用位置插值
                        x_old = np.linspace(0, 1, len(factor_series))
                        x_new = np.linspace(0, 1, len(new_data))
                        
                        # 处理可能的NaN值
                        factor_values = factor_series.values
                        mask = np.isfinite(factor_values)
                        if np.sum(mask) > 10:  # 至少需要10个有效值
                            f = interp1d(
                                x_old[mask], factor_values[mask], 
                                bounds_error=False, fill_value="extrapolate"
                            )
                            new_values = f(x_new)
                            factor_name = f"factor_{name}" if add_prefix else name
                            new_data[factor_name] = new_values
                            factors_added.append(name)
                    except Exception:
                        # 插值失败，跳过此因子
                        pass
            else:
                # 不是日期索引，检查长度
                if len(factor_series) == len(new_data):
                    # 长度匹配，直接使用值
                    factor_name = f"factor_{name}" if add_prefix else name
                    new_data[factor_name] = factor_series.values
                    factors_added.append(name)
                else:
                    # 长度不匹配，尝试插值
                    try:
                        # 使用位置插值
                        x_old = np.linspace(0, 1, len(factor_series))
                        x_new = np.linspace(0, 1, len(new_data))
                        
                        # 处理可能的NaN值
                        factor_values = factor_series.values
                        mask = np.isfinite(factor_values)
                        if np.sum(mask) > 10:  # 至少需要10个有效值
                            f = interp1d(
                                x_old[mask], factor_values[mask], 
                                bounds_error=False, fill_value="extrapolate"
                            )
                            new_values = f(x_new)
                            factor_name = f"factor_{name}" if add_prefix else name
                            new_data[factor_name] = new_values
                            factors_added.append(name)
                    except Exception:
                        # 插值失败，跳过此因子
                        pass
        except Exception:
            # 处理任何其他错误，跳过此因子
            pass
    
    return new_data, factors_added


def get_best_factors():
    """
    获取当前已挖掘的最佳因子
    用于在主程序中访问因子数据
    
    返回:
        dict: 最佳因子字典
    """
    if 'best_factors' in st.session_state and st.session_state.best_factors is not None:
        return st.session_state.best_factors
    return {}

def display_auto_factor_mining_page():
    """自动因子挖掘页面"""
    st.header("自动因子挖掘")
    
    # 创建Tab布局
    tabs = st.tabs(["因子挖掘配置", "因子评估结果", "因子可视化", "因子应用", "挖掘日志"])
    
    # 初始化 session_state
    if 'factor_mining_results' not in st.session_state:
        st.session_state.factor_mining_results = None
    if 'best_factors' not in st.session_state:
        st.session_state.best_factors = None
    if 'evaluation_results' not in st.session_state:
        st.session_state.evaluation_results = None
    if 'mining_stats' not in st.session_state:
        st.session_state.mining_stats = None
    if 'factor_df' not in st.session_state:
        st.session_state.factor_df = None
    if 'mining_logs' not in st.session_state:
        st.session_state.mining_logs = []
    
    # 自定义日志处理器，将日志存储到session_state中
    class SessionLogHandler(logging.Handler):
        def emit(self, record):
            log_entry = self.format(record)
            if 'mining_logs' not in st.session_state:
                st.session_state.mining_logs = []
            st.session_state.mining_logs.append({
                'time': record.asctime if hasattr(record, 'asctime') else time.strftime('%Y-%m-%d %H:%M:%S'),
                'level': record.levelname,
                'message': record.getMessage(),
                'color': self.get_level_color(record.levelname)
            })
            
        def get_level_color(self, levelname):
            colors = {
                'DEBUG': 'gray',
                'INFO': 'blue',
                'WARNING': 'orange',
                'ERROR': 'red',
                'CRITICAL': 'darkred'
            }
            return colors.get(levelname, 'black')
    
    # 设置日志捕获器
    log_handler = SessionLogHandler()
    log_handler.setLevel(logging.INFO)
    log_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    log_handler.setFormatter(log_formatter)
    
    # 获取drl_trading日志记录器并添加处理器
    logger = logging.getLogger('drl_trading')
    logger.setLevel(logging.INFO)
    
    # 确保不重复添加处理器
    has_session_handler = False
    for handler in logger.handlers:
        if isinstance(handler, SessionLogHandler):
            has_session_handler = True
            break
    
    if not has_session_handler:
        logger.addHandler(log_handler)
    
    # Tab 1: 因子挖掘配置
    with tabs[0]:
        st.subheader("因子挖掘参数配置")
        
        # 检查主系统是否已经加载数据
        has_main_data = 'data' in st.session_state and st.session_state.data is not None
        
        if has_main_data:
            st.success("已检测到主系统数据，将使用系统中已加载的数据进行因子挖掘")
            # 展示已加载的数据信息
            show_data_info = st.expander("查看已加载数据信息", expanded=False)
            with show_data_info:
                data = st.session_state.data
                st.write(f"数据记录数: {len(data)} 条")
                st.write(f"数据时间范围: {data.index.min()} 至 {data.index.max()}")
                st.write(f"可用列: {', '.join(data.columns.tolist())}")
                st.write("数据预览:")
                st.dataframe(data.head(5))
        else:
            st.warning("未检测到主系统数据，将重新获取数据用于因子挖掘")
        
        col1, col2 = st.columns(2)
        
        with col1:
            if has_main_data:
                # 如果有主系统数据，显示当前数据信息但不允许修改
                stock_code = st.text_input("股票/指数代码", value=st.session_state.get('stock_code', 'sh000001'), disabled=True,
                                          help="使用系统中已加载的数据")
                # 显示日期范围但不能修改
                data_range = f"{data.index.min().strftime('%Y-%m-%d')} 至 {data.index.max().strftime('%Y-%m-%d')}"
                st.text_input("数据日期范围", value=data_range, disabled=True)
            else:
                # 如果没有主系统数据，提供完整的输入
                stock_code = st.text_input("股票/指数代码", value="sh000001", help="格式: sh000001(上证指数), sz399001(深证成指)")
                start_date = st.date_input("起始日期", value=datetime.now() - timedelta(days=365), help="回测起始日期")
                end_date = st.date_input("结束日期", value=datetime.now(), help="回测结束日期")
        
        with col2:
            min_ic_abs = st.slider("最小IC绝对值阈值", min_value=0.01, max_value=0.2, value=0.05, step=0.01, help="IC绝对值低于此值的因子将被过滤")
            corr_threshold = st.slider("相关性阈值", min_value=0.5, max_value=0.9, value=0.7, step=0.05, help="相关性高于此值的因子将被合并")
            top_n_factors = st.slider("选择因子数量", min_value=5, max_value=50, value=20, step=5, help="最终选择的因子数量")
            
        # 高级设置
        with st.expander("高级设置"):
            evaluation_periods = st.multiselect(
                "评估周期(天)",
                options=[1, 3, 5, 10, 20, 30],
                default=[1, 5, 10, 20],
                help="用于计算因子IC值的未来收益周期"
            )
            factor_types = st.multiselect(
                "因子类型",
                options=["技术指标", "交叉特征", "时序特征"],
                default=["技术指标", "交叉特征", "时序特征"],
                help="要生成的因子类型"
            )
        
        # 运行按钮
        run_col1, run_col2 = st.columns([1, 3])
        with run_col1:
            if st.button("开始挖掘因子", type="primary"):
                # 清除之前的日志
                st.session_state.mining_logs = []
                
                # 创建占位符用于显示进度
                progress_placeholder = st.empty()
                status_placeholder = st.empty()
                result_placeholder = st.empty()
                
                # 显示初始进度条
                progress_bar = progress_placeholder.progress(0)
                status_placeholder.info("正在初始化因子挖掘流程...")
                
                with st.spinner("正在挖掘因子..."):
                    try:
                        # 导入必要的模块
                        from core_logic.data_handler import DataHandler
                        from core_logic.auto_factor_mining import AutoFactorPipeline
                        
                        # 初始化数据处理器和流水线
                        data_handler = DataHandler(cache_dir='data_cache')
                        pipeline = AutoFactorPipeline(data_handler)
                        
                        # 更新进度
                        progress_bar.progress(10)
                        status_placeholder.info("初始化完成，开始准备数据...")
                        
                        # 确定使用的数据
                        if has_main_data:
                            # 使用已加载的系统数据
                            data = st.session_state.data
                            status_placeholder.success("使用主系统已加载数据进行因子挖掘")
                            
                            # 更新进度
                            progress_bar.progress(20)
                            status_placeholder.info("数据准备完成，开始生成基础因子...")
                            
                            # 手动更新进度，因为实际上没有使用回调函数
                            progress_bar.progress(30)
                            status_placeholder.info("生成基础技术指标因子...")
                            
                            progress_bar.progress(40)
                            status_placeholder.info("正在进行因子挖掘中...")
                            
                            # 运行因子挖掘流水线
                            best_factors, evaluation_results, stats = pipeline.run_with_data(
                                data=data,
                                min_ic_abs=min_ic_abs,
                                corr_threshold=corr_threshold,
                                top_n_factors=top_n_factors,
                                evaluation_periods=evaluation_periods
                            )
                            
                            # 完成进度
                            progress_bar.progress(100)
                        else:
                            # 重新获取数据
                            status_placeholder.info("正在获取新数据...")
                            progress_bar.progress(20)
                            
                            # 运行流水线
                            best_factors, evaluation_results, stats = pipeline.run(
                                stock_code=stock_code,
                                start_date=start_date.strftime('%Y-%m-%d'),
                                end_date=end_date.strftime('%Y-%m-%d'),
                                min_ic_abs=min_ic_abs,
                                corr_threshold=corr_threshold,
                                top_n_factors=top_n_factors,
                                evaluation_periods=evaluation_periods
                            )
                            
                            # 完成进度
                            progress_bar.progress(100)
                        
                        # 保存结果到session_state
                        st.session_state.best_factors = best_factors
                        st.session_state.evaluation_results = evaluation_results
                        st.session_state.mining_stats = stats
                        
                        # 获取数据用于展示和因子可视化
                        if best_factors:
                            status_placeholder.success(f"找到 {len(best_factors)} 个有效因子！正在准备数据可视化...")
                            
                            if has_main_data:
                                # 使用已有数据
                                factor_data = data
                            else:
                                # 获取数据
                                factor_data = data_handler.get_stock_data(
                                    stock_code=stock_code,
                                    start_date=start_date.strftime('%Y-%m-%d'),
                                    end_date=end_date.strftime('%Y-%m-%d')
                                )
                            
                            # 创建一个DataFrame用于展示
                            factor_df = pd.DataFrame(index=factor_data.index)
                            for name, factor in best_factors.items():
                                factor_df[name] = factor
                                
                            st.session_state.factor_df = factor_df
                        
                        # 显示结果摘要
                        if best_factors:
                            result_placeholder.success(f"因子挖掘完成! 找到 {len(best_factors)} 个有效因子。")
                        else:
                            result_placeholder.warning("因子挖掘完成，但没有找到满足条件的有效因子。请尝试降低IC阈值或检查数据质量。")
                            
                        # 自动切换到结果标签页
                        if len(best_factors) > 0:
                            status_placeholder.info("请查看'因子评估结果'标签页查看详细结果。")
                        
                    except Exception as e:
                        # 更新进度条为错误状态
                        progress_bar.progress(100)
                        status_placeholder.error(f"挖掘过程出错: {str(e)}")
                        result_placeholder.error(f"因子挖掘失败: {str(e)}")
                        st.exception(e)  # 显示完整的错误堆栈
    
    # Tab 2: 因子评估结果
    with tabs[1]:
        st.subheader("因子评估结果")
        
        if st.session_state.best_factors is not None and st.session_state.evaluation_results is not None:
            # 显示统计信息
            stats = st.session_state.mining_stats
            
            col1, col2, col3, col4 = st.columns(4)
            col1.metric("初始因子数量", stats.get('initial_factors', 0))
            col2.metric("有效因子数量", stats.get('valid_factors', 0))
            col3.metric("去相关后因子数量", stats.get('decorrelated_factors', 0))
            col4.metric("最终因子数量", stats.get('final_factors', 0))
            
            # 创建评估结果表格
            best_factor_names = list(st.session_state.best_factors.keys())
            
            if best_factor_names:
                eval_data = []
                for name in best_factor_names:
                    if name in st.session_state.evaluation_results:
                        result = st.session_state.evaluation_results[name]
                        eval_data.append({
                            "因子名称": name,
                            "IC均值": f"{result.get('ic_mean', 0):.4f}",
                            "IC标准差": f"{result.get('ic_std', 0):.4f}",
                            "IR值": f"{result.get('ir', 0):.4f}",
                            "IC(1日)": f"{result.get('ic_details', {}).get('ic_pearson_1d', 0):.4f}",
                            "IC(5日)": f"{result.get('ic_details', {}).get('ic_pearson_5d', 0):.4f}",
                            "IC(20日)": f"{result.get('ic_details', {}).get('ic_pearson_20d', 0):.4f}",
                        })
                
                if eval_data:
                    eval_df = pd.DataFrame(eval_data)
                    st.dataframe(
                        eval_df,
                        use_container_width=True,
                        hide_index=True
                    )
                else:
                    st.info("没有评估结果可显示")
            else:
                st.info("没有找到有效因子")
        else:
            st.info("请先在'因子挖掘配置'标签页运行因子挖掘")
    
    # Tab 3: 因子可视化
    with tabs[2]:
        st.subheader("因子可视化")
        
        if st.session_state.factor_df is not None and st.session_state.evaluation_results is not None:
            # 选择可视化因子
            factor_options = list(st.session_state.factor_df.columns)
            
            if factor_options:
                selected_factors = st.multiselect(
                    "选择要可视化的因子",
                    options=factor_options,
                    default=factor_options[:min(3, len(factor_options))]
                )
                
                if selected_factors:
                    # 创建因子与收益率对比图
                    factor_df = st.session_state.factor_df
                    
                    fig = plt.figure(figsize=(12, 8))
                    
                    # 绘制因子值
                    for factor in selected_factors:
                        # 标准化因子值以便于比较
                        factor_values = factor_df[factor]
                        normalized_factor = (factor_values - factor_values.mean()) / factor_values.std()
                        plt.plot(factor_df.index, normalized_factor, label=factor)
                    
                    plt.title("因子值变化趋势")
                    plt.xlabel("日期")
                    plt.ylabel("标准化因子值")
                    plt.legend()
                    plt.grid(True, alpha=0.3)
                    
                    st.pyplot(fig)
                    
                    # IC热力图
                    if st.checkbox("显示因子IC热力图"):
                        try:
                            # 预处理数据，提取IC值
                            ic_data = {}
                            for factor in selected_factors:
                                if factor in st.session_state.evaluation_results:
                                    result = st.session_state.evaluation_results[factor]
                                    ic_details = result.get('ic_details', {})
                                    
                                    # 提取不同周期的IC值
                                    factor_ic = {}
                                    for k, v in ic_details.items():
                                        if k.startswith('ic_pearson_'):
                                            period = k.split('_')[-1].replace('d', '天')
                                            factor_ic[period] = v
                                    
                                    ic_data[factor] = factor_ic
                            
                            # 创建热力图数据
                            if ic_data:
                                periods = sorted(list(set(period for factor_ic in ic_data.values() for period in factor_ic.keys())))
                                factors = list(ic_data.keys())
                                
                                heatmap_data = np.zeros((len(factors), len(periods)))
                                
                                for i, factor in enumerate(factors):
                                    for j, period in enumerate(periods):
                                        heatmap_data[i, j] = ic_data[factor].get(period, 0)
                                
                                fig, ax = plt.subplots(figsize=(12, max(6, len(factors) * 0.4)))
                                sns.heatmap(heatmap_data, annot=True, cmap="coolwarm", center=0, 
                                            fmt=".3f", linewidths=.5, xticklabels=periods, yticklabels=factors)
                                plt.title("因子IC值热力图")
                                plt.tight_layout()
                                
                                st.pyplot(fig)
                        except Exception as e:
                            st.warning(f"生成IC热力图时出错: {str(e)}")
                    
                    # 因子相关性矩阵
                    if st.checkbox("显示因子相关性矩阵") and len(selected_factors) > 1:
                        try:
                            corr_matrix = factor_df[selected_factors].corr()
                            
                            fig, ax = plt.subplots(figsize=(10, 8))
                            sns.heatmap(corr_matrix, annot=True, cmap="coolwarm", center=0, 
                                        fmt=".2f", linewidths=.5)
                            plt.title("因子相关性矩阵")
                            plt.tight_layout()
                            
                            st.pyplot(fig)
                        except Exception as e:
                            st.warning(f"生成相关性矩阵时出错: {str(e)}")
                    
                else:
                    st.info("请选择要可视化的因子")
            else:
                st.info("没有可用的因子数据")
        else:
            st.info("请先在'因子挖掘配置'标签页运行因子挖掘")
    
    # Tab 4: 因子应用
    with tabs[3]:
        st.subheader("因子应用到模型训练")
        
        if st.session_state.best_factors is None:
            st.info("请先在'因子挖掘配置'标签页运行因子挖掘")
        else:
            st.success(f"已挖掘 {len(st.session_state.best_factors)} 个有效因子，可应用于模型训练")
            
            # 因子特征列表
            st.write("挖掘出的因子列表:")
            factor_names = list(st.session_state.best_factors.keys())
            factor_cols = st.columns(3)
            for i, factor_name in enumerate(factor_names):
                col_idx = i % 3
                with factor_cols[col_idx]:
                    st.write(f"- {factor_name}")
            
            st.markdown("---")
            
            # 应用因子到主系统
            st.subheader("应用因子到主系统")
            
            # 检查是否已经将因子应用到主系统
            factors_applied = 'applied_factors' in st.session_state and st.session_state.applied_factors
            
            if factors_applied:
                st.success("已将挖掘的因子应用到主系统中")
                st.write(f"应用的因子数量: {len(st.session_state.applied_factors)}")
                st.write("应用的因子:")
                st.write(", ".join(st.session_state.applied_factors))
            else:
                if st.button("应用挖掘的因子到主系统", type="primary"):
                    try:
                        # 检查主系统中是否有处理后的数据
                        if 'processed_data' in st.session_state and st.session_state.processed_data is not None:
                            # 创建新的数据帧，结合原始处理数据和新因子
                            new_data, factors_added = apply_factors_to_dataframe(st.session_state.processed_data, st.session_state.best_factors)
                            
                            # 打印诊断信息
                            st.info(f"主系统数据: {len(new_data)}行, 索引类型: {type(new_data.index)}")
                            if len(st.session_state.best_factors) > 0:
                                first_factor = list(st.session_state.best_factors.values())[0]
                                st.info(f"因子数据: {len(first_factor)}行, 索引类型: {type(first_factor.index)}")
                            
                            # 检查索引类型是否匹配，转换为一致类型
                            try:
                                # 如果主系统数据索引不是DatetimeIndex，尝试转换
                                if not isinstance(new_data.index, pd.DatetimeIndex):
                                    st.warning("主系统数据索引不是日期类型，尝试转换...")
                                    # 尝试转换为日期类型
                                    new_data.index = pd.to_datetime(new_data.index)
                                    st.success("成功将主系统数据索引转换为日期类型")
                            except Exception as e:
                                st.warning(f"转换主系统数据索引失败: {str(e)}")
                            
                            # 更新处理后的数据
                            st.session_state.processed_data = new_data
                            # 记录应用的因子
                            st.session_state.applied_factors = factors_added
                            
                            # 如果有特征配置，更新特征配置
                            if 'feature_config' in st.session_state:
                                for name in factors_added:
                                    st.session_state.feature_config[f'factor_{name}'] = {
                                        'type': 'custom',
                                        'source': 'auto_mining',
                                        'params': {}
                                    }
                            
                            if factors_added:
                                st.success(f"成功应用 {len(factors_added)} 个挖掘的因子到主系统")
                                st.write("已应用的因子:")
                                st.write(", ".join(factors_added))
                                
                                # 添加一键训练功能
                                st.markdown("---")
                                st.subheader("一键训练模型")
                                
                                # 添加训练参数设置
                                col1, col2 = st.columns([1, 1])
                                with col1:
                                    train_epochs = st.number_input("训练轮数", min_value=10, max_value=1000, value=100, step=10)
                                    st.session_state.train_epochs = train_epochs
                                with col2:
                                    train_algorithm = st.selectbox(
                                        "算法选择", 
                                        ["PPO", "A2C", "DQN", "SAC"], 
                                        index=0
                                    )
                                    st.session_state.train_algorithm = train_algorithm
                                
                                # 额外训练选项
                                col1, col2 = st.columns([1, 1])
                                with col1:
                                    use_gpu = st.checkbox("使用GPU加速", value=True)
                                    st.session_state.use_gpu = use_gpu
                                with col2:
                                    use_ensemble = st.checkbox("使用集成学习", value=True)
                                    st.session_state.use_ensemble = use_ensemble
                                
                                # 一键训练模型
                                if st.button("一键训练模型"):
                                    # 创建执行标记，避免重复执行
                                    auto_train_key = f"auto_train_exec_{int(time.time())}"
                                    if auto_train_key not in st.session_state:
                                        st.session_state[auto_train_key] = True
                                        
                                        # 设置训练参数
                                        train_epochs = st.session_state.get('train_epochs', 100)
                                        train_algorithm = st.session_state.get('train_algorithm', 'PPO')
                                        use_gpu = st.session_state.get('use_gpu', True)
                                        use_ensemble = st.session_state.get('use_ensemble', True)
                                        
                                        # 保存自动训练参数
                                        st.session_state.auto_train_params = {
                                            'algorithm': train_algorithm,
                                            'epochs': train_epochs,
                                            'use_factors': factors_added,
                                            'use_gpu': use_gpu,
                                            'use_ensemble': use_ensemble
                                        }
                                        
                                        # 设置自动训练标志
                                        st.session_state.auto_train_with_factors = True
                                        st.session_state.auto_start_training = True
                                        
                                        # 确保清除任何可能存在的旧标志
                                        st.session_state.auto_train_action_executed = False
                                        
                                        # 提示用户
                                        st.success(f"将使用{len(factors_added)}个因子进行自动训练，算法：{train_algorithm}，训练轮数：{train_epochs}")
                                        st.info("正在跳转到DRL智能体训练页面...")
                                        st.switch_page("main_app.py")
                            else:
                                st.error("没有因子被成功应用，请检查数据索引兼容性")
                        else:
                            st.error("主系统中没有处理后的数据，无法应用因子")
                            st.info("请先在'数据中心与环境配置'页面获取并处理数据")
                    except Exception as e:
                        st.error(f"应用因子时出错: {str(e)}")
                        st.exception(e)
            
            # 导出因子
            st.markdown("---")
            st.subheader("导出/导入因子")
            
            export_col1, export_col2 = st.columns(2)
            
            with export_col1:
                export_path = st.text_input("导出路径", value="saved_factors/best_factors.json")
                
                if st.button("导出因子"):
                    try:
                        # 确保目录存在
                        os.makedirs(os.path.dirname(export_path), exist_ok=True)
                        
                        # 导出因子
                        import json
                        
                        # 准备导出的数据
                        export_data = {
                            "best_factors": {k: v.tolist() for k, v in st.session_state.best_factors.items()},
                            "evaluation_results": st.session_state.evaluation_results,
                            "mining_stats": st.session_state.mining_stats,
                            "export_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                            "export_version": "1.0"
                        }
                        
                        # 保存到文件
                        with open(export_path, 'w') as f:
                            json.dump(export_data, f, indent=2)
                            
                        st.success(f"成功导出因子到 {export_path}")
                    except Exception as e:
                        st.error(f"导出因子失败: {str(e)}")
            
            with export_col2:
                import_path = st.text_input("导入路径", value="saved_factors/best_factors.json")
                
                if st.button("导入因子"):
                    try:
                        # 检查文件是否存在
                        if os.path.exists(import_path):
                            # 导入因子
                            import json
                            
                            # 加载文件
                            with open(import_path, 'r') as f:
                                import_data = json.load(f)
                                
                            # 转换回pandas Series
                            import_factors = {}
                            for k, v in import_data["best_factors"].items():
                                import_factors[k] = pd.Series(v)
                                
                            # 更新session state
                            st.session_state.best_factors = import_factors
                            st.session_state.evaluation_results = import_data["evaluation_results"]
                            st.session_state.mining_stats = import_data["mining_stats"]
                            
                            st.success(f"成功从 {import_path} 导入因子")
                            st.info(f"导入 {len(import_factors)} 个因子")
                        else:
                            st.error(f"文件不存在: {import_path}")
                    except Exception as e:
                        st.error(f"导入因子失败: {str(e)}")
                        st.exception(e)
    
    # Tab 5: 挖掘日志
    with tabs[4]:
        st.subheader("挖掘日志")
        
        col1, col2 = st.columns([1, 6])
        with col1:
            if st.button("清除日志"):
                st.session_state.mining_logs = []
                st.success("日志已清除")
        
        with col2:
            log_level_filter = st.multiselect(
                "日志级别过滤",
                ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"],
                default=["INFO", "WARNING", "ERROR", "CRITICAL"]
            )
        
        if st.session_state.mining_logs:
            # 过滤日志
            filtered_logs = [log for log in st.session_state.mining_logs 
                           if log['level'] in log_level_filter]
            
            # 日志容器
            log_container = st.container()
            with log_container:
                # 使用markdown样式显示日志
                for log in filtered_logs:
                    if log['level'] == 'ERROR' or log['level'] == 'CRITICAL':
                        st.markdown(f"<div style='color:{log['color']};'><b>{log['time']} - {log['level']}:</b> {log['message']}</div>", unsafe_allow_html=True)
                    elif log['level'] == 'WARNING':
                        st.markdown(f"<div style='color:{log['color']};'>{log['time']} - {log['level']}: {log['message']}</div>", unsafe_allow_html=True)
                    else:
                        st.markdown(f"<div style='color:{log['color']};'>{log['time']} - {log['message']}</div>", unsafe_allow_html=True)
            
            # 显示日志统计
            level_counts = {}
            for log in st.session_state.mining_logs:
                level_counts[log['level']] = level_counts.get(log['level'], 0) + 1
                
            st.write("---")
            st.write("日志统计:")
            cols = st.columns(5)
            
            for i, (level, count) in enumerate(level_counts.items()):
                with cols[i % 5]:
                    st.metric(level, count)
        else:
            st.info("没有可用的挖掘日志")
            
            # 显示说明
            st.markdown("""
            日志将显示因子挖掘过程中的详细信息，包括:
            - 数据加载和处理状态
            - 因子生成过程和统计
            - 因子评估结果
            - 错误和警告信息
            
            运行挖掘过程后，这里会自动显示日志内容。
            """)
    
    # 底部显示注意事项
    st.markdown("---")
    st.markdown("""
    **注意事项：**
    - 因子挖掘过程可能需要一些时间，具体取决于数据量和因子数量
    - IC值是评估因子有效性的重要指标，绝对值越大表示因子预测能力越强
    - 去相关过程可以减少冗余因子，提高模型训练效率
    - 挖掘出的最佳因子直接影响模型的学习效果，建议仔细分析
    """) 