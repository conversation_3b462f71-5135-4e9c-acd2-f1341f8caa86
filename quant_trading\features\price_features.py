"""
价格特征模块
负责计算基本价格特征
"""

import pandas as pd
import numpy as np
import logging

class PriceFeatures:
    """
    价格特征类
    负责计算基本价格特征
    """

    def __init__(self, logger=None):
        """
        初始化价格特征

        参数:
            logger (logging.Logger): 日志记录器
        """
        self.logger = logger or logging.getLogger('drl_trading')
        self.calculator = PriceFeatureCalculator(logger=self.logger)

    def calculate(self, df):
        """
        计算基本价格特征

        参数:
            df (pandas.DataFrame): 原始数据，必须包含OHLCV列

        返回:
            tuple: (处理后的数据框, 生成的特征名称列表)
        """
        return self.calculator.calculate(df)

class PriceFeatureCalculator:
    """
    价格特征计算器
    负责计算基本价格特征
    """

    def __init__(self, logger=None):
        """
        初始化价格特征计算器

        参数:
            logger (logging.Logger): 日志记录器
        """
        self.logger = logger or logging.getLogger('drl_trading')

    def calculate(self, df):
        """
        计算基本价格特征

        参数:
            df (pandas.DataFrame): 原始数据，必须包含OHLCV列

        返回:
            tuple: (处理后的数据框, 生成的特征名称列表)
        """
        # 确保价格列是数值类型
        price_columns = ['开盘', '最高', '最低', '收盘', '成交量']
        for col in price_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # 创建一个列表来存储生成的特征名称
        feature_names = []

        # 计算涨跌幅
        if '涨跌幅' not in df.columns:
            try:
                df['涨跌幅'] = df['收盘'].pct_change()
                # 限制涨跌幅的范围，避免极端值
                df['涨跌幅'] = df['涨跌幅'].replace([np.inf, -np.inf], np.nan).clip(-0.5, 0.5)
                feature_names.append('涨跌幅')
            except Exception as e:
                self.logger.warning(f"计算涨跌幅失败: {str(e)}")
                df['涨跌幅'] = 0
                feature_names.append('涨跌幅')

        # 计算对数收益率
        try:
            # 确保收盘价为正值
            positive_close = df['收盘'].replace(0, np.nan)
            shifted_close = positive_close.shift(1).replace(0, np.nan)
            # 计算对数收益率，并处理可能的无穷大值
            log_returns = np.log(positive_close / shifted_close)
            df['对数收益率'] = log_returns.replace([np.inf, -np.inf], np.nan).clip(-0.5, 0.5)
            feature_names.append('对数收益率')
        except Exception as e:
            self.logger.warning(f"计算对数收益率失败: {str(e)}")
            df['对数收益率'] = 0
            feature_names.append('对数收益率')

        # 计算真实波动幅度 (True Range)
        try:
            high_low = df['最高'] - df['最低']
            high_close = (df['最高'] - df['收盘'].shift(1)).abs()
            low_close = (df['最低'] - df['收盘'].shift(1)).abs()
            df['TR'] = pd.concat([high_low, high_close, low_close], axis=1).max(axis=1)
            # 处理可能的无穷大值
            df['TR'] = df['TR'].replace([np.inf, -np.inf], np.nan)
            feature_names.append('TR')
        except Exception as e:
            self.logger.warning(f"计算TR失败: {str(e)}")
            df['TR'] = df['最高'] - df['最低']  # 最简单的替代方案
            feature_names.append('TR')

        # 计算收盘价相对开盘价的变化
        try:
            # 确保开盘价不为0
            non_zero_open = df['开盘'].replace(0, np.nan)
            df['日内涨跌幅'] = (df['收盘'] - df['开盘']) / non_zero_open
            # 限制范围，避免极端值
            df['日内涨跌幅'] = df['日内涨跌幅'].replace([np.inf, -np.inf], np.nan).clip(-0.5, 0.5)
            feature_names.append('日内涨跌幅')
        except Exception as e:
            self.logger.warning(f"计算日内涨跌幅失败: {str(e)}")
            df['日内涨跌幅'] = 0
            feature_names.append('日内涨跌幅')

        # 计算高低价差占开盘价的比例
        try:
            # 确保开盘价不为0
            non_zero_open = df['开盘'].replace(0, np.nan)
            df['日内波动率'] = (df['最高'] - df['最低']) / non_zero_open
            # 限制范围，避免极端值
            df['日内波动率'] = df['日内波动率'].replace([np.inf, -np.inf], np.nan).clip(0, 0.5)
            feature_names.append('日内波动率')
        except Exception as e:
            self.logger.warning(f"计算日内波动率失败: {str(e)}")
            df['日内波动率'] = 0
            feature_names.append('日内波动率')

        # 计算价格相对位置 (当前价格在当日最高最低价之间的位置)
        df['价格相对位置'] = (df['收盘'] - df['最低']) / (df['最高'] - df['最低'] + 1e-10)
        feature_names.append('价格相对位置')

        # 计算价格动量 (当前价格与N日前价格的差)
        for period in [1, 3, 5, 10, 20]:
            df[f'价格动量_{period}'] = df['收盘'] - df['收盘'].shift(period)
            feature_names.append(f'价格动量_{period}')

        # 计算价格加速度 (当前动量与前一日动量的差)
        for period in [1, 3, 5, 10]:
            momentum_col = f'价格动量_{period}'
            df[f'价格加速度_{period}'] = df[momentum_col] - df[momentum_col].shift(1)
            feature_names.append(f'价格加速度_{period}')

        # 计算价格波动率 (过去N日收益率的标准差)
        for period in [5, 10, 20, 60]:
            df[f'价格波动率_{period}'] = df['涨跌幅'].rolling(window=period).std()
            feature_names.append(f'价格波动率_{period}')

        # 计算价格趋势强度 (过去N日的价格方向一致性)
        for period in [5, 10, 20]:
            df[f'趋势强度_{period}'] = df['涨跌幅'].rolling(window=period).apply(
                lambda x: np.sum(np.sign(x)) / period, raw=True
            )
            feature_names.append(f'趋势强度_{period}')

        # 计算成交量特征
        df['成交量变化率'] = df['成交量'].pct_change()
        df['成交量相对强度'] = df['成交量'] / df['成交量'].rolling(window=20).mean()
        feature_names.extend(['成交量变化率', '成交量相对强度'])

        # 计算价量相关性
        for period in [5, 10, 20]:
            df[f'价量相关_{period}'] = df['涨跌幅'].rolling(window=period).corr(df['成交量变化率'])
            feature_names.append(f'价量相关_{period}')

        return df, feature_names
