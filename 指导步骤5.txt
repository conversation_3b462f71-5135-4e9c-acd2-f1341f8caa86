块 5: 阶段二 (续) (feature_engineer.py 与阶段二测试要求)

本块目标： 实现特征工程模块 feature_engineer.py，用于为DRL智能体的状态空间生成所需特征。同时，明确阶段二所有模块（特别是TradingEnvironment）的严格测试要求。

继续【阶段二】的剩余部分。

核心任务2：实现 core_logic/feature_engineer.py – 服务于DRL状态空间

此模块负责从原始行情数据计算技术指标和其他特征，这些特征将作为DRL智能体状态空间的一部分。

功能需求：

输入： Pandas DataFrame (包含OHLCV等基础数据，由 data_handler.py 提供)。
输出： Pandas DataFrame，在原始数据基础上增加新的特征列。
可配置性：

UI（在‘数据中心与环境配置’模块）后续应允许用户选择希望计算并包含在状态空间中的技术指标类别和具体指标（例如，从预定义的列表中勾选）。
对于参数化指标（如移动平均线的周期），UI应允许用户配置参数。这些配置最终会传递给 feature_engineer.py。
指标库使用：

优先使用成熟的第三方库如 pandas_ta 来计算标准技术指标，确保准确性和效率。
若某些特殊指标无法通过库实现，则需自行编码实现，并确保其正确性。
涵盖范围（用户可选）：
趋势指标：SMA, EMA, MACD, Bollinger Bands, ADX等。
动量指标：RSI, Stochastic Oscillator, CCI, ROC等。
波动率指标：ATR, Historical Volatility等。
成交量相关指标：OBV, Volume MA等。
（可选）统计特征：价格的滚动偏度、峰度等。
效率与无未来函数：

所有特征计算必须高效，并且绝对不能引入未来函数（即计算时刻 t 的特征时，只能使用时刻 t 及之前的数据）。
数据清洗与处理：

确保输入数据适合指标计算（例如，处理NaN值，确保有足够前置数据进行滚动计算）。
阶段二模块化测试与验证要求 (trading_environment.py 和 feature_engineer.py 完成时)：

强制性环境检查 (TradingEnvironment)：

必须使用 gymnasium.utils.env_checker.check_env(env) 对实现的 TradingEnvironment 实例进行严格的API兼容性检查，确保没有错误或警告。这是与DRL框架兼容的基础。
核心逻辑单元测试 (TradingEnvironment)：

(AI需在 tests/unit/test_trading_environment_logic.py 或类似文件中概念性完成，或实际编写)
针对奖励函数中的关键计算逻辑（例如，盈亏计算）、交易成本计算、最小持仓期规则的判断逻辑等编写独立的单元测试用例。
模拟交互集成测试 (TradingEnvironment)：

(AI需在 tests/integration/test_trading_environment.py 中概念性完成，或实际编写)
编写一个测试脚本，实例化 TradingEnvironment。
让一个执行预设动作序列（而非随机动作）的“脚本化智能体”与环境交互数个回合。这些动作序列应专门测试：
最小持仓期规则： 序列包含在持有期未满时尝试卖出的动作，验证环境是否正确阻止或惩罚了该动作，并保持了仓位。
无杠杆规则： 序列包含尝试用超出可用现金的金额进行买入的动作，验证环境是否按最大可用现金购买或拒绝了交易。
正常交易流程： 开仓 -> 持有（满足最短天数） -> 平仓。
在每个 step 中，详细记录并检查：observation 的符合性、reward 的预期性、terminated 和 truncated 的触发条件、info 字典内容的准确性、账户净值与资金变化的预期性。
特征工程单元测试 (feature_engineer.py)：

(AI需在 tests/unit/test_feature_engineer.py 中概念性完成，或实际编写)
对该模块中核心的、自定义实现的或参数配置较复杂的技术指标计算函数进行单元测试。使用小段已知数据和手动/其他工具计算的预期输出来验证。测试边界条件（如数据长度不足）。
特征工程集成测试 (feature_engineer.py 与 data_handler.py 对接)：

使用 data_handler.py 获取真实市场数据，传递给 feature_engineer.py 处理。
验证输出DataFrame是否包含了所选特征列，且无非预期NaN值。
文档同步：

确保 TradingEnvironment 和 feature_engineer.py 中的所有类、公共方法和关键逻辑都有完整、准确、详尽的文档字符串。
代码审查（AI自我审查）：

仔细审查环境逻辑的完备性、奖励函数设计的合理性、交易约束执行的严格性、特征计算的正确性以及与Gymnasium API的严格符合性。
确认： 只有当所有上述测试（特别是 check_env 和精心设计的交互测试）都成功通过，并且你确信环境能够准确模拟市场并严格执行所有规则后，此阶段才算完成。”

