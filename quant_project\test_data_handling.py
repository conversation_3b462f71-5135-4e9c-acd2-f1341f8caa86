"""
测试数据处理模块
"""

import os
import sys
import pandas as pd
import numpy as np
import logging
from datetime import datetime, timedelta

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('test_data_handling')

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入原始数据处理类和重构后的数据处理类
from core_logic.data_handler import DataHandler as OldDataHandler
from core_logic.data_handling.data_handler import DataHandler as NewDataHandler
from core_logic.data_handling.adapter import DataHandlerAdapter

def create_test_data(n_samples=100):
    """
    创建测试数据
    
    参数:
        n_samples (int): 样本数量
        
    返回:
        pandas.DataFrame: 测试数据
    """
    # 创建日期索引
    dates = pd.date_range(start='2020-01-01', periods=n_samples, freq='D')
    
    # 创建价格数据
    np.random.seed(42)
    close = np.random.normal(loc=100, scale=10, size=n_samples).cumsum()
    open_price = close * np.random.normal(loc=1, scale=0.01, size=n_samples)
    high = np.maximum(close, open_price) * np.random.normal(loc=1.02, scale=0.01, size=n_samples)
    low = np.minimum(close, open_price) * np.random.normal(loc=0.98, scale=0.01, size=n_samples)
    volume = np.random.normal(loc=1000000, scale=200000, size=n_samples)
    
    # 创建数据框
    df = pd.DataFrame({
        '开盘': open_price,
        '最高': high,
        '最低': low,
        '收盘': close,
        '成交量': volume
    }, index=dates)
    
    return df

def test_data_handling():
    """
    测试数据处理模块
    """
    logger.info("创建测试数据...")
    df = create_test_data(n_samples=100)
    
    # 测试原始数据处理类
    logger.info("测试原始数据处理类...")
    try:
        old_dh = OldDataHandler()
        # 使用缓存数据（如果有）
        cache_key = old_dh._generate_cache_key("sh000001", "2022-01-01", "2022-12-31", "日线")
        cache_file = os.path.join(old_dh.cache_dir, f"{cache_key}.csv")
        
        if os.path.exists(cache_file):
            logger.info(f"使用缓存数据: {cache_file}")
            df_old = pd.read_csv(cache_file, index_col=0, parse_dates=True)
        else:
            logger.info("缓存数据不存在，使用模拟数据")
            # 缓存测试数据
            old_dh._cache_data(df, "sh000001", "2022-01-01", "2022-12-31", "日线")
            df_old = df
        
        logger.info(f"原始数据处理类处理了 {len(df_old)} 条记录")
    except Exception as e:
        logger.error(f"测试原始数据处理类失败: {str(e)}")
        df_old = None
    
    # 测试新的数据处理类
    logger.info("测试新的数据处理类...")
    try:
        new_dh = NewDataHandler()
        # 使用缓存数据（如果有）
        cache_key = new_dh.cache._generate_cache_key("sh000001", "2022-01-01", "2022-12-31", "日线")
        cache_file = os.path.join(new_dh.cache_dir, f"{cache_key}.csv")
        
        if os.path.exists(cache_file):
            logger.info(f"使用缓存数据: {cache_file}")
            df_new = pd.read_csv(cache_file, index_col=0, parse_dates=True)
        else:
            logger.info("缓存数据不存在，使用模拟数据")
            # 缓存测试数据
            new_dh.cache.cache_data(df, "sh000001", "2022-01-01", "2022-12-31", "日线")
            df_new = df
        
        logger.info(f"新的数据处理类处理了 {len(df_new)} 条记录")
    except Exception as e:
        logger.error(f"测试新的数据处理类失败: {str(e)}")
        df_new = None
    
    # 测试适配器
    logger.info("测试数据处理适配器...")
    try:
        adapter_dh = DataHandlerAdapter()
        # 使用缓存数据（如果有）
        cache_key = adapter_dh._generate_cache_key("sh000001", "2022-01-01", "2022-12-31", "日线")
        cache_file = os.path.join(adapter_dh.cache_dir, f"{cache_key}.csv")
        
        if os.path.exists(cache_file):
            logger.info(f"使用缓存数据: {cache_file}")
            df_adapter = pd.read_csv(cache_file, index_col=0, parse_dates=True)
        else:
            logger.info("缓存数据不存在，使用模拟数据")
            # 缓存测试数据
            adapter_dh._cache_data(df, "sh000001", "2022-01-01", "2022-12-31", "日线")
            df_adapter = df
        
        logger.info(f"数据处理适配器处理了 {len(df_adapter)} 条记录")
    except Exception as e:
        logger.error(f"测试数据处理适配器失败: {str(e)}")
        df_adapter = None
    
    # 测试数据清洗功能
    logger.info("测试数据清洗功能...")
    try:
        # 创建带有缺失值和异常值的测试数据
        df_dirty = df.copy()
        # 添加缺失值
        df_dirty.iloc[10:15, 0] = np.nan
        # 添加异常值
        df_dirty.iloc[20, 1] = df_dirty.iloc[20, 1] * 10
        
        # 使用新的数据处理类清洗数据
        new_dh = NewDataHandler(outlier_detection=True)
        df_cleaned = new_dh.cleaner.clean_data(df_dirty)
        
        # 检查缺失值是否被处理
        missing_before = df_dirty.isnull().sum().sum()
        missing_after = df_cleaned.isnull().sum().sum()
        logger.info(f"清洗前缺失值数量: {missing_before}, 清洗后缺失值数量: {missing_after}")
        
        # 检查异常值是否被处理
        max_before = df_dirty.iloc[20, 1]
        max_after = df_cleaned.iloc[20, 1]
        logger.info(f"清洗前异常值: {max_before}, 清洗后异常值: {max_after}")
        
        logger.info("数据清洗功能测试完成")
    except Exception as e:
        logger.error(f"测试数据清洗功能失败: {str(e)}")
    
    # 测试数据验证功能
    logger.info("测试数据验证功能...")
    try:
        new_dh = NewDataHandler()
        is_valid, error_msg = new_dh.validator.validate_data(df)
        logger.info(f"数据验证结果: {is_valid}, 错误信息: {error_msg}")
        
        # 获取数据质量报告
        quality_report = new_dh.get_data_quality_report(df)
        logger.info(f"数据质量评分: {quality_report['quality_score']['overall']}")
        
        logger.info("数据验证功能测试完成")
    except Exception as e:
        logger.error(f"测试数据验证功能失败: {str(e)}")
    
    # 测试数据重采样功能
    logger.info("测试数据重采样功能...")
    try:
        new_dh = NewDataHandler()
        df_weekly = new_dh._resample_data(df, 'W')
        df_monthly = new_dh._resample_data(df, 'M')
        
        logger.info(f"原始数据: {len(df)} 条记录, 周线数据: {len(df_weekly)} 条记录, 月线数据: {len(df_monthly)} 条记录")
        logger.info("数据重采样功能测试完成")
    except Exception as e:
        logger.error(f"测试数据重采样功能失败: {str(e)}")
    
    # 比较结果
    logger.info("比较结果...")
    
    if df_old is not None and df_new is not None:
        # 比较数据形状
        shape_old = df_old.shape
        shape_new = df_new.shape
        logger.info(f"原始数据处理类: {shape_old}, 新的数据处理类: {shape_new}")
        
        # 比较列名
        cols_old = set(df_old.columns)
        cols_new = set(df_new.columns)
        common_cols = cols_old.intersection(cols_new)
        logger.info(f"共同列数量: {len(common_cols)}")
    
    # 输出结果摘要
    logger.info("测试结果摘要:")
    logger.info(f"原始数据处理类: {'成功' if df_old is not None else '失败'}")
    logger.info(f"新的数据处理类: {'成功' if df_new is not None else '失败'}")
    logger.info(f"数据处理适配器: {'成功' if df_adapter is not None else '失败'}")

if __name__ == "__main__":
    test_data_handling()
