#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
特征工程测试脚本
测试量化交易系统的特征工程模块
"""

import os
import sys
import time
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import traceback
import json
import matplotlib.pyplot as plt
import talib

# 设置日志
os.makedirs('logs', exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join('logs', 'feature_engineering_test.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('feature_engineering_test')

def get_sample_data():
    """获取样本数据用于测试"""
    logger.info("获取样本数据")

    try:
        # 尝试导入数据处理模块
        try:
            from quant_project.core_logic.data_handler import DataHandler
            logger.info("成功导入DataHandler模块")
        except ImportError:
            try:
                from core_logic.data_handler import DataHandler
                logger.info("成功导入DataHandler模块(从根目录)")
            except ImportError as e:
                logger.error(f"导入DataHandler模块失败: {str(e)}")
                return None

        # 创建数据处理器实例
        data_handler = DataHandler()

        # 获取样本数据
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')

        sample_data = data_handler.get_stock_data(
            stock_code='sh000001',
            start_date=start_date,
            end_date=end_date,
            frequency='日线'
        )

        if sample_data is not None and not sample_data.empty:
            logger.info(f"成功获取样本数据: {len(sample_data)} 条记录")
            return sample_data
        else:
            logger.warning("获取样本数据失败: 数据为空")
            return None

    except Exception as e:
        logger.error(f"获取样本数据时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return None

def test_feature_engineer(sample_data):
    """测试特征工程模块"""
    logger.info("测试特征工程模块")

    if sample_data is None or sample_data.empty:
        logger.error("无法测试特征工程模块: 样本数据不可用")
        return {
            'success': False,
            'error': '样本数据不可用'
        }

    try:
        # 尝试导入特征工程模块
        try:
            from quant_project.core_logic.feature_engineer import FeatureEngineer
            logger.info("成功导入FeatureEngineer模块")
        except ImportError:
            try:
                from core_logic.feature_engineer import FeatureEngineer
                logger.info("成功导入FeatureEngineer模块(从根目录)")
            except ImportError as e:
                logger.error(f"导入FeatureEngineer模块失败: {str(e)}")
                return {
                    'success': False,
                    'error': f"导入FeatureEngineer模块失败: {str(e)}"
                }

        # 创建特征工程器实例
        feature_engineer = FeatureEngineer()

        # 测试不同特征配置
        feature_configs = [
            # 基本配置
            {
                'moving_averages': {'windows': [5, 10, 20]},
                'rsi': {'window': 14},
                'macd': {'fast': 12, 'slow': 26, 'signal': 9}
            },
            # 扩展配置
            {
                'moving_averages': {'windows': [5, 10, 20, 30, 60]},
                'rsi': {'window': 14},
                'macd': {'fast': 12, 'slow': 26, 'signal': 9},
                'bollinger_bands': {'window': 20, 'num_std': 2},
                'stochastic': {'k_window': 14, 'd_window': 3}
            }
        ]

        results = []

        for i, config in enumerate(feature_configs):
            try:
                logger.info(f"测试特征配置 {i+1}")
                feature_engineer.feature_config = config

                processed_data = feature_engineer.generate_features(sample_data)

                if processed_data is not None and not processed_data.empty:
                    feature_count = len(processed_data.columns) - len(sample_data.columns)
                    logger.info(f"成功生成特征: {feature_count} 个新特征")

                    # 验证特征计算的准确性
                    validation_results = validate_features(sample_data, processed_data, config)

                    results.append({
                        'config_id': i+1,
                        'success': True,
                        'feature_count': feature_count,
                        'validation': validation_results
                    })
                else:
                    logger.warning(f"生成特征失败: 数据为空")
                    results.append({
                        'config_id': i+1,
                        'success': False,
                        'error': '数据为空'
                    })
            except Exception as e:
                logger.error(f"生成特征出错: {str(e)}")
                logger.error(traceback.format_exc())
                results.append({
                    'config_id': i+1,
                    'success': False,
                    'error': str(e)
                })

        # 保存测试结果
        os.makedirs('test_results', exist_ok=True)

        # 将结果转换为可序列化的格式
        serializable_results = []
        for result in results:
            serializable_result = {k: (v if not isinstance(v, dict) else {kk: str(vv) for kk, vv in v.items()})
                                 for k, v in result.items()}
            serializable_results.append(serializable_result)

        with open('test_results/feature_engineering_test.json', 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=4)

        success_rate = sum(1 for r in results if r.get('success', False)) / len(results) * 100
        logger.info(f"特征工程模块测试完成，成功率: {success_rate:.2f}%")

        return {
            'success': True,
            'success_rate': success_rate,
            'feature_configs_tested': len(feature_configs),
            'results': results
        }

    except Exception as e:
        logger.error(f"测试特征工程模块时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            'success': False,
            'error': str(e)
        }

def validate_features(original_data, processed_data, config):
    """验证特征计算的准确性"""
    logger.info("验证特征计算的准确性")

    validation_results = {}

    try:
        # 确定价格列名（中文或英文）
        close_col = '收盘' if '收盘' in original_data.columns else 'close'
        high_col = '最高' if '最高' in original_data.columns else 'high'
        low_col = '最低' if '最低' in original_data.columns else 'low'

        if close_col not in original_data.columns:
            logger.error(f"数据中缺少收盘价列，可用列: {original_data.columns.tolist()}")
            return {'error': f"数据中缺少收盘价列，可用列: {original_data.columns.tolist()}"}

        # 验证移动平均线
        if 'moving_averages' in config:
            windows = config['moving_averages'].get('windows', [])
            for window in windows:
                column_name = f'MA_{window}'
                if column_name in processed_data.columns:
                    # 使用talib计算参考值
                    reference = talib.SMA(original_data[close_col].values, timeperiod=window)
                    # 比较最后10个值
                    processed_values = processed_data[column_name].values[-10:]
                    reference_values = reference[-10:]
                    # 计算相对误差
                    errors = np.abs(processed_values - reference_values) / (np.abs(reference_values) + 1e-10)
                    mean_error = np.nanmean(errors)
                    validation_results[column_name] = {
                        'mean_error': mean_error,
                        'passed': mean_error < 0.01  # 误差小于1%
                    }

        # 验证RSI
        if 'rsi' in config:
            window = config['rsi'].get('window', 14)
            column_name = f'RSI_{window}'
            if column_name in processed_data.columns:
                # 使用talib计算参考值
                reference = talib.RSI(original_data[close_col].values, timeperiod=window)
                # 比较最后10个值
                processed_values = processed_data[column_name].values[-10:]
                reference_values = reference[-10:]
                # 计算相对误差
                errors = np.abs(processed_values - reference_values) / (np.abs(reference_values) + 1e-10)
                mean_error = np.nanmean(errors)
                validation_results[column_name] = {
                    'mean_error': mean_error,
                    'passed': mean_error < 0.01  # 误差小于1%
                }

        # 验证MACD
        if 'macd' in config:
            fast = config['macd'].get('fast', 12)
            slow = config['macd'].get('slow', 26)
            signal = config['macd'].get('signal', 9)
            column_names = [f'MACD_{fast}_{slow}', f'MACD_signal_{signal}', f'MACD_hist']

            if all(col in processed_data.columns for col in column_names):
                # 使用talib计算参考值
                macd, signal_line, hist = talib.MACD(
                    original_data[close_col].values,
                    fastperiod=fast,
                    slowperiod=slow,
                    signalperiod=signal
                )

                # 比较最后10个值
                for i, col in enumerate(column_names):
                    reference = [macd, signal_line, hist][i]
                    processed_values = processed_data[col].values[-10:]
                    reference_values = reference[-10:]
                    # 计算相对误差
                    errors = np.abs(processed_values - reference_values) / (np.abs(reference_values) + 1e-10)
                    mean_error = np.nanmean(errors)
                    validation_results[col] = {
                        'mean_error': mean_error,
                        'passed': mean_error < 0.01  # 误差小于1%
                    }

        return validation_results

    except Exception as e:
        logger.error(f"验证特征计算时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {'error': str(e)}

def run_all_tests():
    """运行所有特征工程测试"""
    logger.info("开始运行特征工程测试")

    # 获取样本数据
    sample_data = get_sample_data()

    # 测试特征工程模块
    if sample_data is not None and not sample_data.empty:
        results = test_feature_engineer(sample_data)
    else:
        results = {
            'success': False,
            'error': '无法获取样本数据'
        }

    logger.info(f"特征工程测试完成，结果: {results}")

    return results

if __name__ == "__main__":
    run_all_tests()
