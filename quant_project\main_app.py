"""
DRL量化交易系统 - 主应用程序
基于Streamlit的用户界面，模块化组织，简洁清晰
"""

import os
import sys
import logging
import streamlit as st
import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta
import matplotlib.pyplot as plt

# 配置日志
os.makedirs('logs', exist_ok=True)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/app.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('drl_trading')

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(current_dir)
sys.path.append(parent_dir)

# 尝试应用兼容性补丁
try:
    from fix_asyncio_torch import apply_all_patches
    apply_all_patches()
    logger.info("已应用 Python + PyTorch + Streamlit 兼容性补丁")
except Exception as e:
    logger.warning(f"应用兼容性补丁时出错: {str(e)}")

# 配置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'SimSun', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def check_dependencies():
    """检查并记录依赖项状态"""
    try:
        import torch
        if torch.cuda.is_available():
            logger.info(f"PyTorch已启用GPU支持: {torch.cuda.get_device_name(0)}")
        else:
            logger.info("PyTorch未检测到GPU")
    except Exception as e:
        logger.warning(f"检查PyTorch GPU支持时出错: {str(e)}")

    try:
        import streamlit
        logger.info(f"Streamlit版本: {streamlit.__version__}")
    except Exception as e:
        logger.warning(f"检查Streamlit版本时出错: {str(e)}")

def setup_page():
    """设置页面配置和全局样式"""
    # 页面配置
    st.set_page_config(
        page_title="DRL量化交易系统",
        page_icon="📈",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # 自定义CSS
    st.markdown("""
    <style>
        .main-header {
            color: #1E88E5;
            font-size: 2rem;
            font-weight: 600;
            margin-bottom: 1rem;
            text-align: center;
            padding: 0.5rem;
            border-bottom: 2px solid #f0f2f6;
        }
        .stTabs [data-baseweb="tab-list"] {
            gap: 12px;
        }
        .stTabs [data-baseweb="tab"] {
            padding: 8px 16px;
            border-radius: 4px;
        }
        .stTabs [aria-selected="true"] {
            background-color: rgba(30, 136, 229, 0.1);
        }
        /* 美化侧边栏 */
        .css-1d391kg {
            padding-top: 2rem;
        }
        /* 美化卡片 */
        div[data-testid="stMetric"] {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 6px;
            box-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }
        /* 美化表单 */
        div[data-testid="stForm"] {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
    """, unsafe_allow_html=True)
    
    # 标题
    st.markdown('<div class="main-header">DRL量化交易系统</div>', unsafe_allow_html=True)

def main():
    """主应用程序函数"""
    # 初始化检查和设置
    check_dependencies()
    setup_page()
    
    # 初始化会话状态
    if 'current_page' not in st.session_state:
        st.session_state.current_page = "数据中心"
    
    # 导入UI组件模块
    try:
        from quant_project.ui_components import (
            render_data_center,
            render_factor_management,
            render_model_training,
            render_backtest_analysis
        )
        logger.info("成功导入UI组件模块")
    except ImportError as e:
        logger.error(f"导入UI组件模块失败: {str(e)}")
        st.error("无法加载UI组件。请确保项目目录结构正确。")
        return
    
    # 侧边栏导航
    with st.sidebar:
        st.title("导航")
        
        # 导航按钮
        nav_options = {
            "数据中心": "📊 数据中心",
            "因子管理": "🧩 因子管理",
            "模型训练": "🤖 模型训练",
            "回测分析": "📈 回测分析"
        }
        
        for key, label in nav_options.items():
            if st.button(label, key=f"nav_{key}", use_container_width=True):
                st.session_state.current_page = key
        
        # 系统信息
        st.sidebar.markdown("---")
        st.sidebar.subheader("系统信息")
        
        # 显示数据状态
        if 'data' in st.session_state and st.session_state.data is not None:
            data_info = f"已加载数据: {len(st.session_state.data)}条记录"
            st.sidebar.success(data_info)
        else:
            st.sidebar.warning("未加载数据")
        
        # 显示模型状态
        if 'model' in st.session_state and st.session_state.model is not None:
            model_info = f"已加载模型: {st.session_state.model.get('name', 'Unknown')}"
            st.sidebar.success(model_info)
        else:
            st.sidebar.warning("未加载模型")
        
        # 显示系统日期
        st.sidebar.info(f"系统时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 渲染当前页面
    if st.session_state.current_page == "数据中心":
        render_data_center()
    elif st.session_state.current_page == "因子管理":
        render_factor_management()
    elif st.session_state.current_page == "模型训练":
        render_model_training()
    elif st.session_state.current_page == "回测分析":
        render_backtest_analysis()
    else:
        # 默认页面
        render_data_center()

if __name__ == "__main__":
    main()
