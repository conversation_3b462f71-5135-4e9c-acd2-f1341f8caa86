"""
性能指标模块
提供计算交易策略性能指标的功能
"""

import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Union, Tuple, Optional, Any
from scipy import stats

class PerformanceMetrics:
    """
    性能指标类
    提供计算交易策略性能指标的功能
    """

    def __init__(self, risk_free_rate: float = 0.0):
        """
        初始化性能指标计算器

        参数:
            risk_free_rate (float): 无风险利率，年化
        """
        self.logger = logging.getLogger('drl_trading')
        self.risk_free_rate = risk_free_rate

    def calculate_metrics(self, portfolio_values: pd.Series, 
                         trades: Optional[Union[pd.DataFrame, List[Dict]]] = None,
                         benchmark_values: Optional[pd.Series] = None) -> Dict[str, Any]:
        """
        计算交易策略的性能指标

        参数:
            portfolio_values (pandas.Series): 组合价值序列，索引为日期
            trades (list or pandas.DataFrame, optional): 交易记录
            benchmark_values (pandas.Series, optional): 基准价值序列，索引为日期

        返回:
            dict: 性能指标字典
        """
        # 验证输入
        if not isinstance(portfolio_values, pd.Series) or len(portfolio_values) < 2:
            return {"status": "error", "message": "组合价值序列无效或数据不足"}

        # 转换交易记录为DataFrame
        trades_df = None
        if trades is not None:
            if isinstance(trades, pd.DataFrame):
                trades_df = trades.copy()
            else:
                try:
                    trades_df = pd.DataFrame(trades)
                except Exception as e:
                    self.logger.warning(f"无法将交易记录转换为DataFrame: {str(e)}")

        # 计算收益率序列
        returns = portfolio_values.pct_change().dropna()

        # 如果有基准，计算基准收益率序列
        benchmark_returns = None
        if benchmark_values is not None and len(benchmark_values) >= 2:
            benchmark_returns = benchmark_values.pct_change().dropna()

        # 计算各项指标
        metrics = {}

        # 基础收益指标
        metrics.update(self._calculate_return_metrics(portfolio_values, benchmark_values))

        # 风险指标
        metrics.update(self._calculate_risk_metrics(returns, benchmark_returns))

        # 回撤指标
        metrics.update(self._calculate_drawdown_metrics(portfolio_values))

        # 交易统计
        if trades_df is not None and not trades_df.empty:
            metrics.update(self._calculate_trade_metrics(trades_df))

        # 分析收益率分布
        metrics.update(self._analyze_return_distribution(returns))

        # 计算滚动指标
        metrics.update(self._calculate_rolling_metrics(returns))

        # 计算风险调整后收益指标
        metrics.update(self._calculate_risk_adjusted_metrics(returns, benchmark_returns))

        # 按期间统计
        metrics.update(self._calculate_period_statistics(portfolio_values))

        return metrics

    def _calculate_return_metrics(self, portfolio_values: pd.Series, 
                                benchmark_values: Optional[pd.Series] = None) -> Dict[str, Any]:
        """
        计算收益相关指标

        参数:
            portfolio_values (pandas.Series): 组合价值序列
            benchmark_values (pandas.Series, optional): 基准价值序列

        返回:
            dict: 收益指标字典
        """
        metrics = {}

        # 总收益率
        metrics['total_return'] = (portfolio_values.iloc[-1] / portfolio_values.iloc[0]) - 1

        # 年化收益率
        start_date = portfolio_values.index[0]
        end_date = portfolio_values.index[-1]
        days = (end_date - start_date).days
        years = max(days / 365, 1e-10)  # 避免除以零
        metrics['annual_return'] = (1 + metrics['total_return']) ** (1 / years) - 1

        # 日度收益率统计
        returns = portfolio_values.pct_change().dropna()
        metrics['daily_return_mean'] = returns.mean()
        metrics['daily_return_std'] = returns.std()
        metrics['daily_return_min'] = returns.min()
        metrics['daily_return_max'] = returns.max()

        # 基准相关指标
        if benchmark_values is not None and len(benchmark_values) >= 2:
            metrics['benchmark_total_return'] = (benchmark_values.iloc[-1] / benchmark_values.iloc[0]) - 1
            metrics['benchmark_annual_return'] = (1 + metrics['benchmark_total_return']) ** (1 / years) - 1
            metrics['excess_return'] = metrics['total_return'] - metrics['benchmark_total_return']
            metrics['excess_annual_return'] = metrics['annual_return'] - metrics['benchmark_annual_return']

        return metrics

    def _calculate_risk_metrics(self, returns: pd.Series, 
                               benchmark_returns: Optional[pd.Series] = None) -> Dict[str, Any]:
        """
        计算风险相关指标

        参数:
            returns (pandas.Series): 收益率序列
            benchmark_returns (pandas.Series, optional): 基准收益率序列

        返回:
            dict: 风险指标字典
        """
        metrics = {}

        # 波动率 (年化)
        metrics['volatility'] = returns.std() * np.sqrt(252)

        # 下行波动率
        negative_returns = returns[returns < 0]
        metrics['downside_volatility'] = negative_returns.std() * np.sqrt(252) if len(negative_returns) > 0 else 0

        # 正收益率占比
        metrics['positive_days'] = (returns > 0).sum() / len(returns)

        # 负收益率占比
        metrics['negative_days'] = (returns < 0).sum() / len(returns)

        # 最大单日收益
        metrics['max_daily_gain'] = returns.max()

        # 最大单日亏损
        metrics['max_daily_loss'] = returns.min()

        # 计算VaR (95% 和 99%)
        metrics['var_95'] = np.percentile(returns, 5)
        metrics['var_99'] = np.percentile(returns, 1)

        # 如果有基准，计算相对风险指标
        if benchmark_returns is not None and len(benchmark_returns) > 0:
            # 确保长度一致
            common_index = returns.index.intersection(benchmark_returns.index)
            if len(common_index) > 0:
                aligned_returns = returns.loc[common_index]
                aligned_benchmark = benchmark_returns.loc[common_index]

                # 计算β (贝塔值)
                cov_matrix = np.cov(aligned_returns, aligned_benchmark)
                if cov_matrix.shape == (2, 2) and cov_matrix[1, 1] != 0:
                    metrics['beta'] = cov_matrix[0, 1] / cov_matrix[1, 1]
                else:
                    metrics['beta'] = np.nan

                # 计算α (阿尔法值)
                daily_risk_free = (1 + self.risk_free_rate) ** (1/252) - 1
                metrics['alpha'] = (aligned_returns.mean() - daily_risk_free) - metrics['beta'] * (aligned_benchmark.mean() - daily_risk_free)
                metrics['alpha'] *= 252  # 年化

                # 计算跟踪误差
                tracking_error = (aligned_returns - aligned_benchmark).std() * np.sqrt(252)
                metrics['tracking_error'] = tracking_error

                # 信息比率
                if tracking_error > 0:
                    metrics['information_ratio'] = (aligned_returns.mean() - aligned_benchmark.mean()) * 252 / tracking_error
                else:
                    metrics['information_ratio'] = np.nan

        return metrics

    def _calculate_drawdown_metrics(self, portfolio_values: pd.Series) -> Dict[str, Any]:
        """
        计算回撤相关指标

        参数:
            portfolio_values (pandas.Series): 组合价值序列

        返回:
            dict: 回撤指标字典
        """
        metrics = {}

        # 计算累计最大值
        running_max = np.maximum.accumulate(portfolio_values)
        
        # 计算回撤序列
        drawdown = (portfolio_values - running_max) / running_max
        
        # 最大回撤
        max_drawdown = drawdown.min()
        metrics['max_drawdown'] = max_drawdown
        
        # 回撤持续时间
        if max_drawdown < 0:
            # 找到最大回撤的结束点
            end_idx = drawdown.idxmin()
            
            # 找到最大回撤的开始点 (结束点之前的最高点)
            start_idx = portfolio_values.loc[:end_idx].idxmax()
            
            # 找到最大回撤的恢复点 (结束点之后首次达到开始点的值)
            recovery_idx = None
            if end_idx != portfolio_values.index[-1]:
                # 获取结束点之后的数据
                post_dd = portfolio_values.loc[end_idx:]
                
                # 找到首次超过开始点值的日期
                recovery_values = post_dd[post_dd >= portfolio_values.loc[start_idx]]
                if len(recovery_values) > 0:
                    recovery_idx = recovery_values.index[0]
            
            # 计算持续时间
            dd_duration = (end_idx - start_idx).days
            metrics['max_drawdown_duration'] = dd_duration
            
            # 记录日期
            metrics['max_drawdown_start'] = start_idx
            metrics['max_drawdown_end'] = end_idx
            metrics['max_drawdown_recovery'] = recovery_idx
            
            # 计算从底部到恢复的时间
            if recovery_idx is not None:
                recovery_duration = (recovery_idx - end_idx).days
                metrics['recovery_duration'] = recovery_duration
            else:
                metrics['recovery_duration'] = None
        else:
            # 没有回撤
            metrics['max_drawdown_duration'] = 0
            metrics['max_drawdown_start'] = None
            metrics['max_drawdown_end'] = None
            metrics['max_drawdown_recovery'] = None
            metrics['recovery_duration'] = None
        
        # 平均回撤
        metrics['avg_drawdown'] = drawdown.mean()
        
        # 回撤次数 (连续的负回撤算一次)
        drawdown_episodes = (drawdown < 0).astype(int).diff()
        drawdown_starts = drawdown_episodes[drawdown_episodes == 1].index
        metrics['drawdown_count'] = len(drawdown_starts)
        
        return metrics

    def _calculate_trade_metrics(self, trades_df: pd.DataFrame) -> Dict[str, Any]:
        """
        计算交易相关指标

        参数:
            trades_df (pandas.DataFrame): 交易记录

        返回:
            dict: 交易指标字典
        """
        metrics = {}

        # 交易总数
        metrics['total_trades'] = len(trades_df)

        # 分类统计
        if 'type' in trades_df.columns:
            buy_trades = trades_df[trades_df['type'] == 'buy']
            sell_trades = trades_df[trades_df['type'] == 'sell']
            metrics['buy_trades'] = len(buy_trades)
            metrics['sell_trades'] = len(sell_trades)

        # 胜率和盈亏比统计
        if 'profit' in trades_df.columns:
            winning_trades = trades_df[trades_df['profit'] > 0]
            losing_trades = trades_df[trades_df['profit'] < 0]

            metrics['winning_trades'] = len(winning_trades)
            metrics['losing_trades'] = len(losing_trades)

            if len(trades_df) > 0:
                metrics['win_rate'] = len(winning_trades) / len(trades_df)
            else:
                metrics['win_rate'] = 0

            # 平均收益和亏损
            if len(winning_trades) > 0:
                metrics['avg_profit'] = winning_trades['profit'].mean()
            else:
                metrics['avg_profit'] = 0

            if len(losing_trades) > 0:
                metrics['avg_loss'] = losing_trades['profit'].mean()
            else:
                metrics['avg_loss'] = 0

            # 盈亏比
            if metrics['avg_loss'] != 0:
                metrics['profit_loss_ratio'] = abs(metrics['avg_profit'] / metrics['avg_loss'])
            else:
                metrics['profit_loss_ratio'] = float('inf')

            # 总盈亏
            metrics['total_profit'] = trades_df['profit'].sum()
            metrics['gross_profit'] = winning_trades['profit'].sum() if len(winning_trades) > 0 else 0
            metrics['gross_loss'] = losing_trades['profit'].sum() if len(losing_trades) > 0 else 0

        # 交易频率
        if 'date' in trades_df.columns:
            # 交易周期 (天)
            first_trade = trades_df['date'].min()
            last_trade = trades_df['date'].max()
            trading_days = (last_trade - first_trade).days
            if trading_days > 0:
                metrics['avg_trades_per_day'] = len(trades_df) / trading_days
            else:
                metrics['avg_trades_per_day'] = len(trades_df)

        # 交易成本统计
        if 'commission' in trades_df.columns:
            metrics['total_commission'] = trades_df['commission'].sum()
            metrics['avg_commission'] = trades_df['commission'].mean()
            metrics['commission_ratio'] = metrics['total_commission'] / trades_df['amount'].sum() if 'amount' in trades_df.columns and trades_df['amount'].sum() != 0 else 0

        return metrics

    def _analyze_return_distribution(self, returns: pd.Series) -> Dict[str, Any]:
        """
        分析收益率分布

        参数:
            returns (pandas.Series): 收益率序列

        返回:
            dict: 分布指标字典
        """
        metrics = {}

        # 偏度 (Skewness)
        metrics['skewness'] = stats.skew(returns.dropna())

        # 峰度 (Kurtosis)
        metrics['kurtosis'] = stats.kurtosis(returns.dropna())

        # 正态性检验 (Jarque-Bera test)
        jb_stat, jb_pvalue = stats.jarque_bera(returns.dropna())
        metrics['jarque_bera_stat'] = jb_stat
        metrics['jarque_bera_pvalue'] = jb_pvalue
        metrics['is_normal'] = jb_pvalue > 0.05  # 5% 显著性水平

        # 收益率分位数
        metrics['return_quantiles'] = {
            'q1': returns.quantile(0.01),
            'q5': returns.quantile(0.05),
            'q25': returns.quantile(0.25),
            'q50': returns.quantile(0.50),
            'q75': returns.quantile(0.75),
            'q95': returns.quantile(0.95),
            'q99': returns.quantile(0.99)
        }

        return metrics

    def _calculate_rolling_metrics(self, returns: pd.Series, window: int = 21) -> Dict[str, Any]:
        """
        计算滚动性能指标

        参数:
            returns (pandas.Series): 收益率序列
            window (int): 滚动窗口大小，默认为21（约一个月）

        返回:
            dict: 滚动指标字典
        """
        metrics = {}

        # 确保有足够的数据
        if len(returns) < window:
            return metrics

        # 滚动波动率
        rolling_vol = returns.rolling(window=window).std() * np.sqrt(252)
        metrics['rolling_volatility'] = rolling_vol

        # 滚动夏普比率
        daily_risk_free = (1 + self.risk_free_rate) ** (1/252) - 1
        excess_returns = returns - daily_risk_free
        rolling_sharpe = returns.rolling(window=window).mean() / returns.rolling(window=window).std() * np.sqrt(252)
        metrics['rolling_sharpe_ratio'] = rolling_sharpe

        # 滚动下行波动率
        def downside_vol(x):
            neg_returns = x[x < 0]
            return neg_returns.std() * np.sqrt(252) if len(neg_returns) > 0 else 0

        rolling_downside_vol = returns.rolling(window=window).apply(downside_vol, raw=True)
        metrics['rolling_downside_volatility'] = rolling_downside_vol

        # 滚动索提诺比率
        rolling_sortino = returns.rolling(window=window).mean() / rolling_downside_vol * np.sqrt(252)
        metrics['rolling_sortino_ratio'] = rolling_sortino

        # 滚动最大回撤
        def max_drawdown(x):
            cumulative_returns = (1 + x).cumprod()
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdown = (cumulative_returns - running_max) / running_max
            return drawdown.min()

        rolling_max_dd = returns.rolling(window=window).apply(max_drawdown, raw=True)
        metrics['rolling_max_drawdown'] = rolling_max_dd

        # 滚动胜率
        rolling_win_rate = returns.rolling(window=window).apply(lambda x: (x > 0).mean(), raw=True)
        metrics['rolling_win_rate'] = rolling_win_rate

        return metrics

    def _calculate_risk_adjusted_metrics(self, returns: pd.Series, 
                                        benchmark_returns: Optional[pd.Series] = None) -> Dict[str, Any]:
        """
        计算风险调整后的收益指标

        参数:
            returns (pandas.Series): 收益率序列
            benchmark_returns (pandas.Series, optional): 基准收益率序列

        返回:
            dict: 风险调整后指标字典
        """
        metrics = {}

        # 计算年化收益和波动率
        annual_return = returns.mean() * 252
        annual_vol = returns.std() * np.sqrt(252)

        # 夏普比率
        daily_risk_free = (1 + self.risk_free_rate) ** (1/252) - 1
        if annual_vol > 0:
            metrics['sharpe_ratio'] = (annual_return - self.risk_free_rate) / annual_vol
        else:
            metrics['sharpe_ratio'] = float('inf') if annual_return > self.risk_free_rate else float('-inf')

        # 索提诺比率
        downside_returns = returns[returns < 0]
        downside_vol = downside_returns.std() * np.sqrt(252) if len(downside_returns) > 0 else 0
        if downside_vol > 0:
            metrics['sortino_ratio'] = (annual_return - self.risk_free_rate) / downside_vol
        else:
            metrics['sortino_ratio'] = float('inf') if annual_return > self.risk_free_rate else float('-inf')

        # 卡玛比率 (需要最大回撤数据)
        max_drawdown = self._calculate_drawdown_metrics(returns.cumsum())['max_drawdown']
        if max_drawdown != 0:
            metrics['calmar_ratio'] = annual_return / abs(max_drawdown)
        else:
            metrics['calmar_ratio'] = float('inf') if annual_return > 0 else 0

        # 欧米茄比率 (Omega Ratio)
        if len(returns) > 0:
            threshold = daily_risk_free  # 使用无风险利率作为阈值
            returns_above_threshold = returns[returns > threshold] - threshold
            returns_below_threshold = threshold - returns[returns < threshold]
            
            if len(returns_below_threshold) > 0 and returns_below_threshold.sum() > 0:
                metrics['omega_ratio'] = returns_above_threshold.sum() / returns_below_threshold.sum()
            else:
                metrics['omega_ratio'] = float('inf')
        else:
            metrics['omega_ratio'] = 0

        # 如果有基准，计算相对指标
        if benchmark_returns is not None and len(benchmark_returns) > 0:
            # 确保长度一致
            common_index = returns.index.intersection(benchmark_returns.index)
            if len(common_index) > 0:
                aligned_returns = returns.loc[common_index]
                aligned_benchmark = benchmark_returns.loc[common_index]
                
                # 特雷诺比率 (Treynor Ratio)
                beta = self._calculate_risk_metrics(aligned_returns, aligned_benchmark).get('beta', 0)
                if beta != 0:
                    metrics['treynor_ratio'] = (annual_return - self.risk_free_rate) / beta
                else:
                    metrics['treynor_ratio'] = float('inf') if annual_return > self.risk_free_rate else float('-inf')

        return metrics

    def _calculate_period_statistics(self, portfolio_values: pd.Series) -> Dict[str, Any]:
        """
        计算不同期间的统计信息

        参数:
            portfolio_values (pandas.Series): 组合价值序列

        返回:
            dict: 期间统计字典
        """
        metrics = {}

        # 确保索引是日期类型
        if not isinstance(portfolio_values.index, pd.DatetimeIndex):
            return metrics

        # 计算月度收益率
        monthly_returns = portfolio_values.resample('M').last().pct_change().dropna()
        metrics['monthly_returns'] = monthly_returns
        
        if len(monthly_returns) > 0:
            metrics['avg_monthly_return'] = monthly_returns.mean()
            metrics['std_monthly_return'] = monthly_returns.std()
            metrics['monthly_win_rate'] = (monthly_returns > 0).mean()
            
            # 最好和最差的月份
            metrics['best_month'] = monthly_returns.max()
            metrics['worst_month'] = monthly_returns.min()
            metrics['best_month_date'] = monthly_returns.idxmax()
            metrics['worst_month_date'] = monthly_returns.idxmin()

        # 计算季度收益率
        quarterly_returns = portfolio_values.resample('Q').last().pct_change().dropna()
        metrics['quarterly_returns'] = quarterly_returns
        
        if len(quarterly_returns) > 0:
            metrics['avg_quarterly_return'] = quarterly_returns.mean()
            metrics['std_quarterly_return'] = quarterly_returns.std()
            metrics['quarterly_win_rate'] = (quarterly_returns > 0).mean()

        # 计算年度收益率
        yearly_returns = portfolio_values.resample('Y').last().pct_change().dropna()
        metrics['yearly_returns'] = yearly_returns
        
        if len(yearly_returns) > 0:
            metrics['avg_yearly_return'] = yearly_returns.mean()
            metrics['std_yearly_return'] = yearly_returns.std()
            metrics['yearly_win_rate'] = (yearly_returns > 0).mean()
            
            # 最好和最差的年份
            metrics['best_year'] = yearly_returns.max()
            metrics['worst_year'] = yearly_returns.min()
            metrics['best_year_date'] = yearly_returns.idxmax()
            metrics['worst_year_date'] = yearly_returns.idxmin()

        return metrics 