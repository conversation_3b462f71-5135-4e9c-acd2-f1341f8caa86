import streamlit as st
import logging
import sys
import os

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("test_factor_mining")

# 添加测试信息
logger.info("开始测试自动因子挖掘页面...")
logger.info(f"当前工作目录: {os.getcwd()}")
logger.info(f"Python路径: {sys.path}")

try:
    # 尝试导入自动因子挖掘页面
    logger.info("尝试导入自动因子挖掘页面模块...")
    from auto_factor_mining_page import display_auto_factor_mining_page
    logger.info("成功导入自动因子挖掘页面模块")
    
    # 设置页面标题
    st.title("测试自动因子挖掘页面")
    st.write("如果下面的内容正确显示，则表示自动因子挖掘页面功能正常")
    
    # 调用显示函数
    logger.info("调用显示函数...")
    display_auto_factor_mining_page()
    
    # 测试完成
    logger.info("测试完成")
    
except Exception as e:
    # 捕获并显示错误
    logger.error(f"测试过程中出错: {str(e)}", exc_info=True)
    st.error(f"测试过程中出错: {str(e)}")
    
    # 显示错误详情
    st.error("错误详情:")
    import traceback
    st.code(traceback.format_exc()) 