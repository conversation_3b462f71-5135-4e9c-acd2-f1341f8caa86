import numpy as np
import pandas as pd
import gym
from gym import spaces
from typing import Dict, List, Union, Tuple, Callable, Optional, Any
import matplotlib.pyplot as plt
import logging
from collections import deque

# Configure logger
logger = logging.getLogger(__name__)

class TradingEnvironment(gym.Env):
    """
    OpenAI Gym compatible environment for reinforcement learning-based trading.
    Simulates a market environment for an agent to learn trading strategies.
    """
    
    metadata = {'render.modes': ['human', 'rgb_array']}
    
    def __init__(self, 
                df: pd.DataFrame, 
                features: List[str],
                initial_balance: float = 10000.0,
                commission_percent: float = 0.1,
                reward_function: str = 'returns',
                window_size: int = 20,
                max_shares: int = 1000,
                max_steps: int = None,
                scaling: bool = True,
                price_column: str = 'close'):
        """
        Initialize the TradingEnvironment.
        
        Args:
            df: DataFrame containing price and feature data
            features: List of feature column names to use as state
            initial_balance: Initial account balance
            commission_percent: Commission percentage for trades
            reward_function: Type of reward function ('returns', 'profit', 'sharpe')
            window_size: Size of observation window
            max_shares: Maximum number of shares to trade at once
            max_steps: Maximum number of steps per episode (default: len(df))
            scaling: Whether to normalize/scale the features
            price_column: Name of the column containing price data
        """
        super(TradingEnvironment, self).__init__()
        
        # Store parameters
        self.df = df.copy().reset_index(drop=True)
        self.features = features
        self.initial_balance = initial_balance
        self.commission_percent = commission_percent / 100.0  # Convert to decimal
        self.reward_function = reward_function
        self.window_size = window_size
        self.max_shares = max_shares
        self.price_column = price_column
        
        # Validate feature columns
        for feature in self.features:
            if feature not in self.df.columns:
                raise ValueError(f"Feature column '{feature}' not found in DataFrame")
                
        # Check if price column exists
        if self.price_column not in self.df.columns:
            raise ValueError(f"Price column '{self.price_column}' not found in DataFrame")
        
        # Set maximum steps
        self.max_steps = max_steps if max_steps is not None else len(self.df)
        
        # Scale features if requested
        if scaling:
            self._scale_features()
            
        # Define action and observation spaces
        # Action: buy/sell/hold (-1, 0, 1) and share amount (0 to 1)
        self.action_space = spaces.Box(
            low=np.array([-1, 0]), 
            high=np.array([1, 1]),
            dtype=np.float32
        )
        
        # Observation: market features (window_size x num_features) + account info
        num_features = len(self.features)
        self.observation_space = spaces.Dict({
            'market': spaces.Box(
                low=-np.inf, 
                high=np.inf, 
                shape=(self.window_size, num_features),
                dtype=np.float32
            ),
            'account': spaces.Box(
                low=-np.inf,
                high=np.inf,
                shape=(4,),  # [balance, shares_held, avg_buy_price, unrealized_pnl]
                dtype=np.float32
            )
        })
        
        # Initialize state variables
        self.current_step = 0
        self.balance = self.initial_balance
        self.shares_held = 0
        self.avg_buy_price = 0
        self.unrealized_pnl = 0
        self.total_pnl = 0
        
        # For tracking history
        self.account_history = []
        self.action_history = []
        self.reward_history = []
        
        # For Sharpe ratio calculation
        self.returns_window = deque(maxlen=window_size)
        
    def _scale_features(self) -> None:
        """
        Scale/normalize the features.
        """
        # Make a copy of the features for scaling
        scaled_features = self.df[self.features].copy()
        
        # Apply standard scaling
        for feature in self.features:
            mean = scaled_features[feature].mean()
            std = scaled_features[feature].std()
            if std != 0:
                scaled_features[feature] = (scaled_features[feature] - mean) / std
            else:
                logger.warning(f"Feature {feature} has zero standard deviation, skipping scaling")
                
        # Replace original features with scaled versions
        for feature in self.features:
            self.df[f"scaled_{feature}"] = scaled_features[feature]
            
        # Update feature list to use scaled versions
        self.features = [f"scaled_{feature}" for feature in self.features]
    
    def reset(self) -> Dict:
        """
        Reset the environment to initial state.
        
        Returns:
            Initial observation
        """
        # Reset state variables
        self.current_step = 0
        self.balance = self.initial_balance
        self.shares_held = 0
        self.avg_buy_price = 0
        self.unrealized_pnl = 0
        self.total_pnl = 0
        
        # Reset history
        self.account_history = []
        self.action_history = []
        self.reward_history = []
        self.returns_window = deque(maxlen=self.window_size)
        
        # Return initial observation
        return self._get_observation()
    
    def step(self, action: np.ndarray) -> Tuple[Dict, float, bool, Dict]:
        """
        Take a step in the environment.
        
        Args:
            action: Action to take [action_type (-1=sell, 0=hold, 1=buy), amount (0-1)]
            
        Returns:
            observation: New observation
            reward: Reward for the action
            done: Whether the episode is done
            info: Additional information
        """
        # Unpack action
        action_type = action[0]  # -1 (sell), 0 (hold), 1 (buy)
        action_amount = action[1]  # 0 to 1, representing percentage of max_shares
        
        # Calculate actual shares to trade
        shares_to_trade = int(action_amount * self.max_shares)
        
        # Get current price
        current_price = self.df.loc[self.current_step, self.price_column]
        
        # Initialize reward and info
        reward = 0
        info = {}
        
        # Execute the trade
        if action_type > 0.5:  # Buy
            # Calculate maximum shares that can be bought with current balance
            max_possible_shares = self.balance // (current_price * (1 + self.commission_percent))
            shares_to_buy = min(shares_to_trade, max_possible_shares)
            
            if shares_to_buy > 0:
                # Calculate cost including commission
                cost = shares_to_buy * current_price * (1 + self.commission_percent)
                
                # Update state
                self.balance -= cost
                
                # Update average buy price
                if self.shares_held == 0:
                    self.avg_buy_price = current_price
                else:
                    # Weighted average of existing and new shares
                    self.avg_buy_price = (self.avg_buy_price * self.shares_held + current_price * shares_to_buy) / (self.shares_held + shares_to_buy)
                
                self.shares_held += shares_to_buy
                info['action'] = f"Bought {shares_to_buy} shares at {current_price}"
                
        elif action_type < -0.5:  # Sell
            shares_to_sell = min(shares_to_trade, self.shares_held)
            
            if shares_to_sell > 0:
                # Calculate sale value after commission
                sale_value = shares_to_sell * current_price * (1 - self.commission_percent)
                
                # Calculate profit/loss
                profit = sale_value - (shares_to_sell * self.avg_buy_price)
                
                # Update state
                self.balance += sale_value
                self.shares_held -= shares_to_sell
                self.total_pnl += profit
                
                # Reset average buy price if all shares sold
                if self.shares_held == 0:
                    self.avg_buy_price = 0
                    
                info['action'] = f"Sold {shares_to_sell} shares at {current_price}, Profit: {profit:.2f}"
                
        else:  # Hold
            info['action'] = "Hold"
            
        # Calculate unrealized P&L
        if self.shares_held > 0:
            self.unrealized_pnl = self.shares_held * (current_price - self.avg_buy_price)
        else:
            self.unrealized_pnl = 0
            
        # Calculate net worth (balance + value of shares)
        net_worth = self.balance + (self.shares_held * current_price)
        
        # Calculate reward based on chosen reward function
        reward = self._calculate_reward(net_worth)
        
        # Store history
        self.account_history.append({
            'step': self.current_step,
            'price': current_price,
            'balance': self.balance,
            'shares_held': self.shares_held,
            'avg_buy_price': self.avg_buy_price,
            'unrealized_pnl': self.unrealized_pnl,
            'total_pnl': self.total_pnl,
            'net_worth': net_worth
        })
        
        self.action_history.append({
            'step': self.current_step,
            'action_type': action_type,
            'action_amount': action_amount,
            'shares_traded': shares_to_trade
        })
        
        self.reward_history.append(reward)
        
        # Increment step
        self.current_step += 1
        
        # Check if episode is done
        done = self.current_step >= self.max_steps
        
        # Get new observation
        obs = self._get_observation()
        
        # Add additional info
        info['current_step'] = self.current_step
        info['balance'] = self.balance
        info['shares_held'] = self.shares_held
        info['net_worth'] = net_worth
        info['total_pnl'] = self.total_pnl
        
        return obs, reward, done, info
    
    def _get_observation(self) -> Dict:
        """
        Get current observation of the environment.
        
        Returns:
            Dictionary with market and account information
        """
        # Get market features for the window
        start_idx = max(0, self.current_step - self.window_size + 1)
        end_idx = self.current_step + 1
        
        # Handle the case where we don't have enough history
        if end_idx - start_idx < self.window_size:
            # Pad with zeros at the beginning
            padding = self.window_size - (end_idx - start_idx)
            market_data = np.zeros((self.window_size, len(self.features)))
            market_data[padding:] = self.df.loc[start_idx:self.current_step, self.features].values
        else:
            market_data = self.df.loc[start_idx:self.current_step, self.features].values
            
        # Get account information
        current_price = self.df.loc[self.current_step, self.price_column]
        account_data = np.array([
            self.balance,
            self.shares_held,
            self.avg_buy_price,
            self.unrealized_pnl
        ], dtype=np.float32)
        
        # Return observation
        return {
            'market': market_data.astype(np.float32),
            'account': account_data
        }
    
    def _calculate_reward(self, net_worth: float) -> float:
        """
        Calculate reward based on selected reward function.
        
        Args:
            net_worth: Current net worth
            
        Returns:
            Reward value
        """
        if self.current_step == 0:
            return 0
            
        # Get previous net worth
        prev_net_worth = self.account_history[-1]['net_worth'] if self.account_history else self.initial_balance
        
        # Calculate returns
        returns = (net_worth - prev_net_worth) / prev_net_worth
        
        # Store returns for Sharpe ratio calculation
        self.returns_window.append(returns)
        
        if self.reward_function == 'returns':
            # Simple returns
            return returns
            
        elif self.reward_function == 'profit':
            # Absolute profit/loss
            return net_worth - prev_net_worth
            
        elif self.reward_function == 'sharpe':
            # Simplified Sharpe ratio
            if len(self.returns_window) < 2:
                return 0
                
            returns_mean = np.mean(self.returns_window)
            returns_std = np.std(self.returns_window) + 1e-9  # Avoid division by zero
            
            return returns_mean / returns_std
            
        else:
            logger.warning(f"Unknown reward function: {self.reward_function}, using returns")
            return returns
    
    def render(self, mode='human'):
        """
        Render the environment.
        
        Args:
            mode: Rendering mode ('human' or 'rgb_array')
        """
        if len(self.account_history) == 0:
            return
            
        # Convert history to DataFrame
        history_df = pd.DataFrame(self.account_history)
        
        # Create a figure
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(10, 12), sharex=True)
        
        # Plot price
        ax1.plot(history_df['step'], history_df['price'])
        ax1.set_title('Price')
        ax1.set_ylabel('Price')
        ax1.grid(True)
        
        # Plot net worth
        ax2.plot(history_df['step'], history_df['net_worth'])
        ax2.set_title('Net Worth')
        ax2.set_ylabel('Value')
        ax2.grid(True)
        
        # Plot shares held
        ax3.plot(history_df['step'], history_df['shares_held'])
        ax3.set_title('Shares Held')
        ax3.set_ylabel('Shares')
        ax3.set_xlabel('Step')
        ax3.grid(True)
        
        # Add buy/sell markers
        action_df = pd.DataFrame(self.action_history)
        
        # Buy markers
        buys = action_df[action_df['action_type'] > 0.5]
        if not buys.empty:
            ax1.scatter(buys['step'], history_df.loc[buys['step'].values, 'price'], 
                      marker='^', color='green', s=100, label='Buy')
            
        # Sell markers
        sells = action_df[action_df['action_type'] < -0.5]
        if not sells.empty:
            ax1.scatter(sells['step'], history_df.loc[sells['step'].values, 'price'], 
                      marker='v', color='red', s=100, label='Sell')
            
        ax1.legend()
        
        plt.tight_layout()
        
        if mode == 'human':
            plt.show()
        elif mode == 'rgb_array':
            fig.canvas.draw()
            image = np.array(fig.canvas.renderer.buffer_rgba())
            plt.close()
            return image
            
    def close(self):
        """
        Close the environment.
        """
        plt.close()

class CustomRewardWrapper(gym.Wrapper):
    """
    Wrapper for custom reward functions.
    """
    
    def __init__(self, env, reward_function: Callable):
        """
        Initialize the wrapper.
        
        Args:
            env: The environment to wrap
            reward_function: Custom reward function
        """
        super(CustomRewardWrapper, self).__init__(env)
        self.reward_function = reward_function
        
    def step(self, action):
        """
        Override step method to apply custom reward function.
        
        Args:
            action: Action to take
            
        Returns:
            observation, reward, done, info
        """
        obs, reward, done, info = self.env.step(action)
        
        # Apply custom reward function
        custom_reward = self.reward_function(self.env, obs, reward, done, info)
        
        return obs, custom_reward, done, info

class MultiObjectiveRewardWrapper(gym.Wrapper):
    """
    Wrapper for multi-objective rewards.
    """
    
    def __init__(self, 
                env, 
                reward_functions: Dict[str, Callable],
                weights: Dict[str, float] = None):
        """
        Initialize the wrapper.
        
        Args:
            env: The environment to wrap
            reward_functions: Dictionary of reward functions
            weights: Dictionary of weights for each reward function
        """
        super(MultiObjectiveRewardWrapper, self).__init__(env)
        self.reward_functions = reward_functions
        
        # Set default weights if not provided
        if weights is None:
            weights = {name: 1.0 / len(reward_functions) for name in reward_functions}
        else:
            # Normalize weights to sum to 1
            total = sum(weights.values())
            weights = {name: weight / total for name, weight in weights.items()}
            
        self.weights = weights
        
    def step(self, action):
        """
        Override step method to apply multi-objective rewards.
        
        Args:
            action: Action to take
            
        Returns:
            observation, reward, done, info
        """
        obs, reward, done, info = self.env.step(action)
        
        # Calculate rewards for each objective
        rewards = {}
        for name, func in self.reward_functions.items():
            rewards[name] = func(self.env, obs, reward, done, info)
            
        # Apply weights and sum
        weighted_reward = sum(rewards[name] * self.weights[name] for name in self.reward_functions)
        
        # Add individual rewards to info
        info['rewards'] = rewards
        
        return obs, weighted_reward, done, info

class ActionNormalizationWrapper(gym.ActionWrapper):
    """
    Wrapper for normalizing actions.
    """
    
    def __init__(self, env):
        """
        Initialize the wrapper.
        
        Args:
            env: The environment to wrap
        """
        super(ActionNormalizationWrapper, self).__init__(env)
        
    def action(self, action):
        """
        Normalize the action.
        
        Args:
            action: Action to normalize
            
        Returns:
            Normalized action
        """
        # Ensure action_type is -1, 0, or 1
        action_type = action[0]
        if action_type > 0.33:
            action_type = 1.0  # Buy
        elif action_type < -0.33:
            action_type = -1.0  # Sell
        else:
            action_type = 0.0  # Hold
            
        # Ensure action_amount is between 0 and 1
        action_amount = max(0.0, min(1.0, action[1]))
        
        return np.array([action_type, action_amount])

class StateNormalizationWrapper(gym.ObservationWrapper):
    """
    Wrapper for normalizing observations.
    """
    
    def __init__(self, env):
        """
        Initialize the wrapper.
        
        Args:
            env: The environment to wrap
        """
        super(StateNormalizationWrapper, self).__init__(env)
        
    def observation(self, observation):
        """
        Normalize the observation.
        
        Args:
            observation: Observation to normalize
            
        Returns:
            Normalized observation
        """
        # Normalize account data
        account = observation['account']
        
        # Normalize balance by initial balance
        account[0] = account[0] / self.env.initial_balance
        
        # Normalize shares held by max shares
        account[1] = account[1] / self.env.max_shares
        
        # Normalize avg buy price by current price
        current_price = self.env.df.loc[self.env.current_step, self.env.price_column]
        if current_price > 0:
            account[2] = account[2] / current_price
            
        # Normalize unrealized P&L by initial balance
        account[3] = account[3] / self.env.initial_balance
        
        # Return normalized observation
        return {
            'market': observation['market'],  # Market data is already normalized
            'account': account
        }

# Example reward functions for wrappers

def returns_reward(env, obs, reward, done, info):
    """Returns-based reward function."""
    current_net_worth = info['net_worth']
    
    if len(env.account_history) <= 1:
        return 0.0
        
    prev_net_worth = env.account_history[-2]['net_worth']
    return (current_net_worth - prev_net_worth) / prev_net_worth

def sharpe_reward(env, obs, reward, done, info):
    """Sharpe ratio based reward function."""
    if len(env.reward_history) < 2:
        return 0.0
        
    # Use recent returns for rolling Sharpe
    returns = np.array(env.reward_history[-env.window_size:])
    
    if len(returns) < 2:
        return 0.0
        
    returns_mean = np.mean(returns)
    returns_std = np.std(returns) + 1e-9  # Avoid division by zero
    
    # Annualized Sharpe (approximately)
    sharpe = returns_mean / returns_std * np.sqrt(252)
    
    return sharpe

def sortino_reward(env, obs, reward, done, info):
    """Sortino ratio based reward function."""
    if len(env.reward_history) < 2:
        return 0.0
        
    # Use recent returns for rolling Sortino
    returns = np.array(env.reward_history[-env.window_size:])
    
    if len(returns) < 2:
        return 0.0
        
    returns_mean = np.mean(returns)
    
    # Calculate downside deviation (only negative returns)
    negative_returns = returns[returns < 0]
    
    if len(negative_returns) == 0:
        return returns_mean * 10  # Reward for no negative returns
        
    downside_deviation = np.sqrt(np.mean(negative_returns**2)) + 1e-9
    
    # Annualized Sortino (approximately)
    sortino = returns_mean / downside_deviation * np.sqrt(252)
    
    return sortino

def max_drawdown_penalty(env, obs, reward, done, info):
    """Penalty based on maximum drawdown."""
    if len(env.account_history) < 2:
        return 0.0
        
    # Calculate current drawdown
    peak = max([h['net_worth'] for h in env.account_history])
    current = info['net_worth']
    
    drawdown = (peak - current) / peak if peak > 0 else 0
    
    # Return negative penalty
    return -drawdown * 10  # Scale to make it more significant

def trading_activity_reward(env, obs, reward, done, info):
    """Reward for trading activity (to encourage exploration)."""
    # Check if a trade was made in this step
    if 'action' in info and 'Hold' not in info['action']:
        return 0.01  # Small positive reward for taking action
    return 0.0  # No reward for holding

def position_correlation_reward(env, obs, reward, done, info):
    """Reward for position correlation with trend."""
    if len(env.account_history) < 3:
        return 0.0
        
    # Calculate recent price trend
    prices = [h['price'] for h in env.account_history[-3:]]
    trend = np.mean(np.diff(prices))
    
    # Get current position
    position = env.shares_held
    
    # Reward for being in the right direction
    if (trend > 0 and position > 0) or (trend < 0 and position == 0):
        return 0.02
    return -0.01 