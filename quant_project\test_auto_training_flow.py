import os
import sys
import logging
import time
import pandas as pd
import numpy as np
import streamlit as st

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('test_auto_training_flow')

def test_auto_training_flow():
    """测试从因子挖掘到智能体训练的完整流程"""
    logger.info("开始测试自动训练流程...")
    
    # 步骤1：模拟创建因子
    logger.info("步骤1：模拟创建因子")
    
    # 创建模拟数据
    dates = pd.date_range(start='2020-01-01', end='2022-01-01')
    data = pd.DataFrame({
        'close': np.random.rand(len(dates)) * 100,
        'open': np.random.rand(len(dates)) * 100,
        'high': np.random.rand(len(dates)) * 110,
        'low': np.random.rand(len(dates)) * 90,
        'volume': np.random.rand(len(dates)) * 1000000
    }, index=dates)
    
    # 创建模拟因子
    factors = {
        'factor_1': pd.Series(np.random.rand(len(dates)), index=dates),
        'factor_2': pd.Series(np.random.rand(len(dates)), index=dates),
        'factor_3': pd.Series(np.random.rand(len(dates)), index=dates),
    }
    
    # 设置session_state以模拟页面状态
    if 'data' not in st.session_state:
        st.session_state.data = data
    
    if 'processed_data' not in st.session_state:
        st.session_state.processed_data = data.copy()
        # 添加因子到处理后的数据
        for name, factor in factors.items():
            st.session_state.processed_data[name] = factor.values
    
    if 'best_factors' not in st.session_state:
        st.session_state.best_factors = factors
    
    # 步骤2：模拟一键训练功能
    logger.info("步骤2：模拟一键训练功能")
    
    # 模拟用户设置
    train_epochs = 100
    train_algorithm = 'PPO'
    use_gpu = True
    use_ensemble = True
    factors_added = list(factors.keys())
    
    # 保存自动训练参数
    st.session_state.auto_train_params = {
        'algorithm': train_algorithm,
        'epochs': train_epochs,
        'use_factors': factors_added,
        'use_gpu': use_gpu,
        'use_ensemble': use_ensemble
    }
    
    # 设置自动训练标志
    st.session_state.auto_train_with_factors = True
    st.session_state.auto_start_training = True
    
    # 确保清除任何可能存在的旧标志
    if 'auto_train_action_executed' in st.session_state:
        del st.session_state.auto_train_action_executed
    
    logger.info(f"模拟设置完成: 算法={train_algorithm}, 轮数={train_epochs}, 因子数量={len(factors_added)}")
    
    # 步骤3：模拟检查自动训练参数
    logger.info("步骤3：测试自动训练参数检查")
    
    try:
        # 导入因子训练工具
        from factor_training_utils import check_auto_train_factors
        
        # 检查自动训练参数
        auto_start = check_auto_train_factors()
        
        logger.info(f"自动训练检查结果: auto_start={auto_start}")
        logger.info(f"环境配置: {st.session_state.get('env_config', {})}")
        logger.info(f"智能体配置: {st.session_state.get('agent_config', {})}")
        
        # 检查是否正确设置了训练参数
        if auto_start:
            logger.info("✓ 成功设置自动训练标志")
            
            # 检查算法和总步数
            algorithm = st.session_state.get('algorithm')
            total_timesteps = st.session_state.get('total_timesteps')
            logger.info(f"设置的算法: {algorithm}, 总步数: {total_timesteps}")
            
            if algorithm == train_algorithm and total_timesteps == train_epochs * 1000:
                logger.info("✓ 算法和训练步数设置正确")
            else:
                logger.error(f"✗ 算法或训练步数设置错误: {algorithm} != {train_algorithm} 或 {total_timesteps} != {train_epochs * 1000}")
            
            # 检查环境配置中是否包含因子
            env_config = st.session_state.get('env_config', {})
            if 'feature_columns' in env_config:
                feature_columns = env_config['feature_columns']
                factors_included = all(f in feature_columns for f in factors_added)
                
                if factors_included:
                    logger.info("✓ 因子已正确添加到环境配置")
                else:
                    missing = [f for f in factors_added if f not in feature_columns]
                    logger.error(f"✗ 部分因子未添加到环境配置: {missing}")
            else:
                logger.error("✗ 环境配置中缺少feature_columns字段")
            
            # 检查智能体配置
            agent_config = st.session_state.get('agent_config', {})
            if 'use_gpu' in agent_config and agent_config['use_gpu'] == use_gpu:
                logger.info("✓ GPU设置正确")
            else:
                logger.error(f"✗ GPU设置错误: {agent_config.get('use_gpu')} != {use_gpu}")
            
            # 检查集成学习配置
            if use_ensemble:
                if 'ensemble' in agent_config and agent_config['ensemble'].get('use') is True:
                    logger.info("✓ 集成学习设置正确")
                else:
                    logger.error("✗ 集成学习设置错误")
        else:
            logger.error("✗ 未成功设置自动训练标志")
            
    except ImportError as e:
        logger.error(f"导入因子训练工具失败: {str(e)}")
    except Exception as e:
        logger.error(f"测试过程中出错: {str(e)}", exc_info=True)
    
    # 清理session_state
    logger.info("清理session_state...")
    for key in ['auto_train_with_factors', 'auto_start_training', 'auto_train_params', 'auto_train_action_executed']:
        if key in st.session_state:
            del st.session_state[key]
    
    logger.info("测试完成")

if __name__ == "__main__":
    test_auto_training_flow()
    
    print("\n测试结果:")
    print("==========================================================")
    print("如果您看到所有步骤都标记为"✓"，则测试通过")
    print("如果有任何标记为"✗"的步骤，表示存在需要修复的问题")
    print("==========================================================")
    
    # 如果是在Streamlit环境中
    if 'streamlit' in sys.modules:
        st.title("自动训练流程测试")
        st.info("请查看控制台输出以了解测试结果")
        
        # 显示会话状态
        st.subheader("当前会话状态")
        filtered_state = {k: v for k, v in st.session_state.items() if not callable(v) and str(type(v)) != "<class 'module'>"}
        st.json(filtered_state) 