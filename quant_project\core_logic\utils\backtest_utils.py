"""
回测工具模块
提供回测引擎和评估指标计算功能
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from typing import Dict, List, Union, Tuple, Optional, Callable
import logging
from datetime import datetime
import os
import json

logger = logging.getLogger('drl_trading')

class BacktestEngine:
    """回测引擎类，用于模拟交易策略"""
    
    def __init__(self, 
                data: pd.DataFrame,
                initial_capital: float = 1000000.0,
                commission_rate: float = 0.0003,
                slippage: float = 0.0001,
                position_size: float = 1.0,
                price_col: str = '收盘',
                date_col: Optional[str] = None,
                trade_log_path: Optional[str] = None):
        """
        初始化回测引擎
        
        参数:
            data: DataFrame 回测数据，包含价格和信号
            initial_capital: float 初始资金
            commission_rate: float 手续费率
            slippage: float 滑点
            position_size: float 仓位大小 (0.0-1.0)
            price_col: str 价格列名
            date_col: str 日期列名，如不指定则使用索引
            trade_log_path: str 交易日志保存路径
        """
        self.data = data.copy()
        self.initial_capital = initial_capital
        self.commission_rate = commission_rate
        self.slippage = slippage
        self.position_size = position_size
        self.price_col = price_col
        
        # 设置日期索引
        if date_col is not None and date_col in self.data.columns:
            self.data.set_index(date_col, inplace=True)
        
        # 确保索引是日期类型
        if not isinstance(self.data.index, pd.DatetimeIndex):
            logger.warning("数据索引不是DatetimeIndex类型，可能影响回测结果的展示")
        
        # 初始化交易日志
        self.trade_log = []
        self.trade_log_path = trade_log_path
        
        # 初始化绩效指标
        self.performance_metrics = {}
        
        # 初始化回测结果
        self.portfolio_value = None
        self.positions = None
        self.cash = None
        
    def run(self, 
           signals: Optional[pd.Series] = None,
           signal_col: Optional[str] = None,
           strategy_func: Optional[Callable] = None,
           strategy_params: Dict = {}) -> pd.DataFrame:
        """
        运行回测
        
        参数:
            signals: Series 交易信号序列 (1: 买入, -1: 卖出, 0: 持有)
            signal_col: str 信号列名，如signals为None则使用
            strategy_func: Callable 策略函数，接收data和参数，返回信号
            strategy_params: Dict 策略参数
            
        返回:
            回测结果DataFrame
        """
        # 获取交易信号
        if signals is not None:
            # 使用传入的信号序列
            self.data['signal'] = signals
        elif signal_col is not None and signal_col in self.data.columns:
            # 使用数据中的信号列
            self.data['signal'] = self.data[signal_col]
        elif strategy_func is not None:
            # 使用策略函数生成信号
            self.data['signal'] = strategy_func(self.data, **strategy_params)
        else:
            raise ValueError("必须提供交易信号: signals, signal_col 或 strategy_func")
        
        # 初始化回测变量
        n_periods = len(self.data)
        self.portfolio_value = np.zeros(n_periods)
        self.positions = np.zeros(n_periods)
        self.cash = np.zeros(n_periods)
        
        # 第一天的状态
        self.cash[0] = self.initial_capital
        self.portfolio_value[0] = self.initial_capital
        
        # 当前持仓状态
        current_position = 0
        
        # 遍历每个交易日
        for i in range(1, n_periods):
            # 获取前一天的信息
            prev_cash = self.cash[i-1]
            prev_position = self.positions[i-1]
            prev_portfolio = self.portfolio_value[i-1]
            
            # 获取当前价格和信号
            current_price = self.data[self.price_col].iloc[i]
            current_signal = self.data['signal'].iloc[i]
            
            # 处理交易信号
            if current_signal == 1 and current_position <= 0:  # 买入信号
                # 计算买入数量
                buy_value = prev_cash * self.position_size
                # 考虑滑点
                execution_price = current_price * (1 + self.slippage)
                # 计算可买入的股数
                shares_to_buy = buy_value / execution_price
                # 计算手续费
                commission = shares_to_buy * execution_price * self.commission_rate
                # 更新持仓和现金
                current_position = shares_to_buy
                current_cash = prev_cash - shares_to_buy * execution_price - commission
                
                # 记录交易
                self._log_trade('BUY', self.data.index[i], execution_price, 
                               shares_to_buy, commission, current_position, current_cash)
                
            elif current_signal == -1 and current_position >= 0:  # 卖出信号
                # 考虑滑点
                execution_price = current_price * (1 - self.slippage)
                # 计算手续费
                commission = current_position * execution_price * self.commission_rate
                # 更新现金和持仓
                current_cash = prev_cash + current_position * execution_price - commission
                
                # 记录交易
                self._log_trade('SELL', self.data.index[i], execution_price, 
                               current_position, commission, 0, current_cash)
                
                # 清空持仓
                current_position = 0
                
            else:  # 保持当前状态
                current_position = prev_position
                current_cash = prev_cash
            
            # 更新状态变量
            self.positions[i] = current_position
            self.cash[i] = current_cash
            
            # 计算当前组合价值
            self.portfolio_value[i] = current_cash + current_position * current_price
        
        # 创建回测结果DataFrame
        results = pd.DataFrame({
            'Price': self.data[self.price_col],
            'Signal': self.data['signal'],
            'Position': self.positions,
            'Cash': self.cash,
            'Portfolio': self.portfolio_value
        }, index=self.data.index)
        
        # 计算每日收益率
        results['Returns'] = results['Portfolio'].pct_change()
        
        # 计算绩效指标
        self._calculate_performance_metrics(results)
        
        # 保存交易日志
        if self.trade_log_path is not None:
            self._save_trade_log()
            
        return results
    
    def _log_trade(self, 
                  action: str, 
                  date: datetime,
                  price: float, 
                  shares: float, 
                  commission: float, 
                  new_position: float, 
                  new_cash: float) -> None:
        """记录交易"""
        trade = {
            'date': date,
            'action': action,
            'price': price,
            'shares': shares,
            'commission': commission,
            'position': new_position,
            'cash': new_cash,
            'value': new_position * price + new_cash
        }
        self.trade_log.append(trade)
    
    def _save_trade_log(self) -> None:
        """保存交易日志"""
        # 确保目录存在
        os.makedirs(os.path.dirname(self.trade_log_path), exist_ok=True)
        
        # 转换交易日志为DataFrame
        trade_df = pd.DataFrame(self.trade_log)
        
        # 保存为CSV
        trade_df.to_csv(self.trade_log_path, index=False)
        
        # 同时保存为JSON以便查看
        json_path = self.trade_log_path.replace('.csv', '.json')
        with open(json_path, 'w') as f:
            json.dump(self.trade_log, f, indent=4, default=str)
    
    def _calculate_performance_metrics(self, results: pd.DataFrame) -> None:
        """计算绩效指标"""
        # 基本回报指标
        total_return = (results['Portfolio'].iloc[-1] / self.initial_capital) - 1
        
        # 年化收益率
        n_years = len(results) / 252  # 假设252个交易日/年
        annual_return = (1 + total_return) ** (1 / n_years) - 1
        
        # 计算每日收益率和累积收益
        daily_returns = results['Returns'].fillna(0)
        cumulative_returns = (1 + daily_returns).cumprod()
        
        # 波动率（年化）
        volatility = daily_returns.std() * np.sqrt(252)
        
        # 夏普比率
        risk_free_rate = 0.02  # 假设无风险利率为2%
        sharpe_ratio = (annual_return - risk_free_rate) / volatility if volatility != 0 else 0
        
        # 最大回撤
        rolling_max = cumulative_returns.cummax()
        drawdown = (cumulative_returns / rolling_max) - 1
        max_drawdown = drawdown.min()
        
        # 计算胜率
        trades = results[results['Signal'] != 0].copy()
        if len(trades) > 1:
            trades['Next_Portfolio'] = trades['Portfolio'].shift(-1)
            trades['Trade_Return'] = trades['Next_Portfolio'] / trades['Portfolio'] - 1
            win_rate = (trades['Trade_Return'] > 0).mean()
        else:
            win_rate = 0
        
        # 存储所有指标
        self.performance_metrics = {
            'Initial Capital': self.initial_capital,
            'Final Value': results['Portfolio'].iloc[-1],
            'Total Return': total_return,
            'Annual Return': annual_return,
            'Volatility': volatility,
            'Sharpe Ratio': sharpe_ratio,
            'Max Drawdown': max_drawdown,
            'Win Rate': win_rate,
            'Trade Count': len(self.trade_log),
            'Commission Paid': sum(trade['commission'] for trade in self.trade_log)
        }
        
    def plot_results(self, 
                    figsize: Tuple[int, int] = (15, 10),
                    save_path: Optional[str] = None) -> None:
        """
        绘制回测结果
        
        参数:
            figsize: 图表大小
            save_path: 保存路径，如不指定则显示图表
        """
        if self.portfolio_value is None:
            logger.warning("必须先运行回测才能绘制结果")
            return
            
        fig, axes = plt.subplots(3, 1, figsize=figsize, sharex=True)
        
        # 价格和仓位
        ax1 = axes[0]
        ax1.set_title('Price and Positions')
        ax1.plot(self.data.index, self.data[self.price_col], 'b-', label='Price')
        ax1.legend(loc='upper left')
        
        # 创建双Y轴以显示仓位
        ax1b = ax1.twinx()
        ax1b.fill_between(self.data.index, 0, self.positions, alpha=0.3, color='g', label='Position')
        ax1b.legend(loc='upper right')
        
        # 投资组合价值
        ax2 = axes[1]
        ax2.set_title('Portfolio Value')
        ax2.plot(self.data.index, self.portfolio_value, 'g-')
        
        # 累积收益对比基准
        returns = pd.Series(self.portfolio_value) / self.initial_capital
        benchmark_returns = self.data[self.price_col] / self.data[self.price_col].iloc[0]
        
        ax3 = axes[2]
        ax3.set_title('Strategy vs. Buy & Hold')
        ax3.plot(self.data.index, returns, 'g-', label='Strategy')
        ax3.plot(self.data.index, benchmark_returns, 'b--', label='Buy & Hold')
        ax3.legend()
        
        # 设置格式
        plt.tight_layout()
        
        # 保存或显示
        if save_path:
            plt.savefig(save_path)
            plt.close()
        else:
            plt.show()
            
    def print_performance_summary(self) -> None:
        """打印绩效摘要"""
        if not self.performance_metrics:
            logger.warning("必须先运行回测才能显示绩效摘要")
            return
            
        print("\n===== 回测绩效摘要 =====")
        print(f"初始资金: {self.performance_metrics['Initial Capital']:,.2f}")
        print(f"最终价值: {self.performance_metrics['Final Value']:,.2f}")
        print(f"总收益率: {self.performance_metrics['Total Return']:.2%}")
        print(f"年化收益率: {self.performance_metrics['Annual Return']:.2%}")
        print(f"波动率(年化): {self.performance_metrics['Volatility']:.2%}")
        print(f"夏普比率: {self.performance_metrics['Sharpe Ratio']:.2f}")
        print(f"最大回撤: {self.performance_metrics['Max Drawdown']:.2%}")
        print(f"胜率: {self.performance_metrics['Win Rate']:.2%}")
        print(f"交易次数: {self.performance_metrics['Trade Count']}")
        print(f"总手续费: {self.performance_metrics['Commission Paid']:,.2f}")
        print("=======================\n")
    
    def get_performance_metrics(self) -> Dict:
        """获取绩效指标"""
        return self.performance_metrics


# 策略函数示例
def simple_ma_strategy(data: pd.DataFrame, 
                      short_window: int = 20, 
                      long_window: int = 50,
                      price_col: str = '收盘') -> pd.Series:
    """
    简单的移动平均交叉策略
    
    参数:
        data: DataFrame 价格数据
        short_window: int 短期均线周期
        long_window: int 长期均线周期
        price_col: str 价格列名
        
    返回:
        信号序列 (1: 买入, -1: 卖出, 0: 持有)
    """
    # 计算移动平均线
    data = data.copy()
    data['ma_short'] = data[price_col].rolling(short_window).mean()
    data['ma_long'] = data[price_col].rolling(long_window).mean()
    
    # 初始化信号
    signals = pd.Series(0, index=data.index)
    
    # 生成交叉信号
    signals[data['ma_short'] > data['ma_long']] = 1  # 金叉买入
    signals[data['ma_short'] < data['ma_long']] = -1  # 死叉卖出
    
    # 填充初始的NaN值
    signals.fillna(0, inplace=True)
    
    return signals


def calculate_drawdowns(returns: pd.Series) -> Tuple[float, pd.Series]:
    """
    计算最大回撤和回撤序列
    
    参数:
        returns: Series 收益率序列
        
    返回:
        (最大回撤, 回撤序列)
    """
    # 计算累积收益
    cumulative_returns = (1 + returns).cumprod()
    
    # 计算走高点
    running_max = cumulative_returns.cummax()
    
    # 计算回撤
    drawdowns = (cumulative_returns / running_max) - 1
    
    # 最大回撤
    max_drawdown = drawdowns.min()
    
    return max_drawdown, drawdowns


def calculate_rolling_statistics(returns: pd.Series, 
                                window: int = 252,
                                risk_free_rate: float = 0.0) -> pd.DataFrame:
    """
    计算滚动统计指标
    
    参数:
        returns: Series 收益率序列
        window: int 滚动窗口大小
        risk_free_rate: float 无风险利率
        
    返回:
        滚动统计指标DataFrame
    """
    # 计算滚动波动率 (年化)
    rolling_vol = returns.rolling(window).std() * np.sqrt(252)
    
    # 计算滚动年化收益率
    rolling_return = returns.rolling(window).mean() * 252
    
    # 计算滚动夏普比率
    rolling_sharpe = (rolling_return - risk_free_rate) / rolling_vol
    
    # 计算滚动最大回撤
    rolling_max_drawdown = returns.rolling(window).apply(
        lambda x: calculate_drawdowns(x)[0]
    )
    
    # 组合所有指标
    rolling_stats = pd.DataFrame({
        'Volatility': rolling_vol,
        'Return': rolling_return,
        'Sharpe': rolling_sharpe,
        'Max Drawdown': rolling_max_drawdown
    }, index=returns.index)
    
    return rolling_stats


def calculate_monthly_returns(portfolio_values: pd.Series) -> pd.DataFrame:
    """
    计算月度收益率
    
    参数:
        portfolio_values: Series 投资组合价值序列
        
    返回:
        月度收益率DataFrame
    """
    # 确保索引是日期类型
    if not isinstance(portfolio_values.index, pd.DatetimeIndex):
        raise ValueError("portfolio_values必须有DatetimeIndex索引")
        
    # 计算日收益率
    daily_returns = portfolio_values.pct_change().fillna(0)
    
    # 重采样为月度收益率
    monthly_returns = (1 + daily_returns).resample('M').prod() - 1
    
    # 创建月度收益表
    return_table = pd.DataFrame(monthly_returns)
    return_table.columns = ['Monthly Return']
    
    # 添加年份和月份列
    return_table['Year'] = return_table.index.year
    return_table['Month'] = return_table.index.month
    
    # 转换为透视表
    monthly_return_table = return_table.pivot_table(
        values='Monthly Return',
        index='Year',
        columns='Month',
        aggfunc='first'
    )
    
    # 重命名列为月份名称
    month_names = {
        1: 'Jan', 2: 'Feb', 3: 'Mar', 4: 'Apr', 5: 'May', 6: 'Jun',
        7: 'Jul', 8: 'Aug', 9: 'Sep', 10: 'Oct', 11: 'Nov', 12: 'Dec'
    }
    monthly_return_table.rename(columns=month_names, inplace=True)
    
    # 添加年度收益率
    monthly_return_table['Annual'] = (1 + monthly_return_table.fillna(0)).prod(axis=1) - 1
    
    return monthly_return_table 