@echo off
echo 正在启动量化交易系统 (带补丁版本)...
echo.

REM 检查是否有headless参数
SET HEADLESS=
IF "%1"=="headless" (
    SET HEADLESS=--headless
    echo 以无浏览器模式启动（不会自动打开浏览器）...
)

REM 激活虚拟环境 (如果存在)
if exist "test_env\Scripts\activate.bat" (
    echo 正在激活虚拟环境...
    call test_env\Scripts\activate.bat
)

REM 运行带补丁的应用
python run_with_patch.py %HEADLESS%

REM 如果出错，暂停以便查看错误信息
if %ERRORLEVEL% NEQ 0 (
    echo.
    echo 应用运行出错，请查看上面的错误信息
    pause
)

REM 如果虚拟环境已激活，则退出
if defined VIRTUAL_ENV (
    deactivate
)
