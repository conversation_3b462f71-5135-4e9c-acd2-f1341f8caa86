@echo off
echo 正在启动DRL量化交易系统...

REM 检查是否有headless参数
SET HEADLESS=
IF "%2"=="headless" (
    SET HEADLESS=--server.headless true
    echo 以无浏览器模式启动（不会自动打开浏览器）...
)

REM 检查是否有参数
IF "%1"=="factor" (
    echo 正在启动自动因子挖掘模块...
    python -m streamlit run run_auto_factor_mining.py %HEADLESS%
) ELSE IF "%1"=="debug" (
    echo 正在启动调试版本...
    python -m streamlit run fixed_main_app.py %HEADLESS%
) ELSE (
    echo 正在启动主程序...
    python -m streamlit run main_app.py %HEADLESS%
)

pause 