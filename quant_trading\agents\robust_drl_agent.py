"""
鲁棒DRL智能体模块
实现具有市场状态感知、风险管理和交叉验证的鲁棒DRL智能体
符合顶尖量化基金的最佳实践
"""

import os
import logging
import time
import datetime
import numpy as np
import pandas as pd
import gymnasium as gym
from gymnasium.utils.env_checker import check_env
from stable_baselines3 import PPO, A2C, DQN, SAC, TD3, DDPG
from stable_baselines3.common.callbacks import BaseCallback, EvalCallback, CallbackList, StopTrainingOnRewardThreshold
from stable_baselines3.common.monitor import Monitor
from stable_baselines3.common.vec_env import DummyVecEnv, VecNormalize, SubprocVecEnv
from stable_baselines3.common.noise import NormalActionNoise, OrnsteinUhlenbeckActionNoise
from stable_baselines3.common.evaluation import evaluate_policy
import torch
import optuna
from optuna.pruners import MedianPruner
from optuna.samplers import TPESampler
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Optional, Tuple, Union, Any, Callable
import warnings

# 忽略特定警告
warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", category=RuntimeWarning)

# 导入自定义模块
from quant_trading.trading import RobustTradingEnvironment
from quant_trading.market import MarketConditionDetector, MarketCondition
from quant_trading.risk import RiskManager, StopLossType, PositionSizingMethod
from quant_trading.validation import TimeSeriesCV, CVMethod, OverfittingDetector, MarketConditionCV
from quant_trading.evaluation import ModelEvaluator
from quant_trading.utils.common import is_gpu_available, get_gpu_info

try:
    from quant_trading.agents.ensemble_learning import EnsembleModel, EnsembleLearning
    ENSEMBLE_AVAILABLE = True
except ImportError:
    ENSEMBLE_AVAILABLE = False
    print("警告: 集成学习模块不可用，请确保ensemble_learning.py文件存在")

class RobustMetricsCallback(BaseCallback):
    """
    鲁棒指标回调类
    记录训练过程中的指标，包括市场状态和风险指标
    """

    def __init__(self, agent, verbose=0):
        super(RobustMetricsCallback, self).__init__(verbose)
        self.agent = agent
        self.training_stats = agent.training_stats
        self.market_detector = agent.market_detector
        self.overfitting_detector = agent.overfitting_detector

    def _on_step(self):
        # 记录当前步数
        self.training_stats['steps'].append(self.num_timesteps)

        # 记录当前奖励
        if len(self.model.ep_info_buffer) > 0:
            rewards = [ep_info['r'] for ep_info in self.model.ep_info_buffer]
            mean_reward = np.mean(rewards)
            self.training_stats['rewards'].append(mean_reward)
            self.training_stats['episodes'].append(len(self.training_stats['rewards']))

            # 检查是否有足够的数据进行过拟合检测
            if len(self.training_stats['rewards']) > 10:
                # 使用最近的10个奖励作为"训练集"和"测试集"
                train_rewards = self.training_stats['rewards'][-10:-5]
                test_rewards = self.training_stats['rewards'][-5:]

                # 检测过拟合
                is_overfitting, overfitting_degree, _ = self.overfitting_detector.detect_overfitting(
                    train_scores=train_rewards,
                    test_scores=test_rewards,
                    threshold=0.2
                )

                # 记录过拟合程度
                self.training_stats['overfitting_degrees'].append(overfitting_degree)

                # 如果检测到过拟合，记录警告
                if is_overfitting:
                    self.logger.warning(f"检测到过拟合，程度: {overfitting_degree:.4f}")

        # 记录当前学习率
        if hasattr(self.model, 'learning_rate'):
            if callable(self.model.learning_rate):
                lr = self.model.learning_rate(1.0)  # 假设进度为1.0
            else:
                lr = self.model.learning_rate
            self.training_stats['learning_rates'].append(lr)

        return True

class RobustDRLAgent:
    """
    鲁棒DRL智能体类
    实现具有市场状态感知、风险管理和交叉验证的鲁棒DRL智能体
    """

    def __init__(self, env_config, agent_config, hpo_config=None):
        """
        初始化鲁棒DRL智能体

        参数:
            env_config (dict): 交易环境配置
            agent_config (dict): 智能体配置
            hpo_config (dict, optional): 超参数优化配置
        """
        self.logger = logging.getLogger('drl_trading')
        self.env_config = env_config
        self.agent_config = agent_config
        self.hpo_config = hpo_config

        # 设置模型保存目录
        self.models_dir = 'saved_models'
        os.makedirs(self.models_dir, exist_ok=True)

        # 初始化最佳模型路径
        self.best_model_path = None

        # 初始化集成模型
        self.ensemble_model = None

        # 初始化市场状态检测器
        self.market_detector = MarketConditionDetector(logger=self.logger)

        # 初始化风险管理器
        self.risk_manager = RiskManager(
            stop_loss_config=self.agent_config.get('stop_loss_config'),
            position_sizing_config=self.agent_config.get('position_sizing_config'),
            market_condition_detector=self.market_detector,
            logger=self.logger
        )

        # 初始化过拟合检测器
        self.overfitting_detector = OverfittingDetector(logger=self.logger)

        # 初始化模型评估器
        self.model_evaluator = ModelEvaluator(logger=self.logger)

        # 创建训练环境
        self.env = self._create_environment()

        # 检查环境是否符合Gymnasium API
        try:
            check_env(self.env)
            self.logger.info("环境检查通过")
        except Exception as e:
            self.logger.error(f"环境检查失败: {str(e)}")
            raise

        # 创建DRL模型
        self.model = self._create_model()

        # 训练指标
        self.training_stats = {
            'rewards': [],
            'losses': [],
            'steps': [],
            'episodes': [],
            'learning_rates': [],
            'overfitting_degrees': [],
            'market_conditions': []
        }

    def _create_environment(self):
        """
        创建鲁棒交易环境

        返回:
            gym.Env: 交易环境实例
        """
        # 从环境配置中提取参数
        df_processed_data = self.env_config.get('df_processed_data')
        initial_capital = self.env_config.get('initial_capital', 100000)
        commission_rate = self.env_config.get('commission_rate', 0.0003)
        min_hold_days = self.env_config.get('min_hold_days', 3)
        allow_short = self.env_config.get('allow_short', False)
        max_position = self.env_config.get('max_position', 1.0)
        reward_config = self.env_config.get('reward_config', None)
        window_size = self.env_config.get('window_size', 20)

        # 创建鲁棒交易环境
        env = RobustTradingEnvironment(
            df_processed_data=df_processed_data,
            initial_capital=initial_capital,
            commission_rate=commission_rate,
            min_hold_days=min_hold_days,
            allow_short=allow_short,
            max_position=max_position,
            reward_config=reward_config,
            window_size=window_size,
            market_condition_detector=self.market_detector,
            risk_manager=self.risk_manager,
            adaptive_reward=self.agent_config.get('adaptive_reward', True),
            market_regime_aware=self.agent_config.get('market_regime_aware', True)
        )

        # 包装环境以记录指标
        env = Monitor(env)

        # 是否使用向量化环境
        if self.agent_config.get('use_vec_env', False):
            # 创建向量化环境
            env = DummyVecEnv([lambda: env])

            # 是否使用归一化环境
            if self.agent_config.get('use_vec_normalize', False):
                env = VecNormalize(
                    env,
                    norm_obs=True,
                    norm_reward=True,
                    clip_obs=10.0,
                    clip_reward=10.0,
                    gamma=self.agent_config.get('gamma', 0.99),
                    epsilon=1e-8
                )

        return env

    def _create_model(self):
        """
        创建DRL模型

        返回:
            stable_baselines3.BaseAlgorithm: DRL模型实例
        """
        # 从智能体配置中提取参数
        algorithm = self.agent_config.get('algorithm', 'PPO')
        policy = self.agent_config.get('policy_network', 'MlpPolicy')
        learning_rate = self.agent_config.get('learning_rate', 0.0003)
        gamma = self.agent_config.get('gamma', 0.99)

        # 检查是否有GPU可用
        device = 'cuda' if is_gpu_available() and self.agent_config.get('use_gpu', True) else 'cpu'
        self.logger.info(f"使用设备: {device}")

        # 记录当前算法
        self.logger.info(f"创建模型，算法: {algorithm}")

        # 创建策略关键字参数
        policy_kwargs = {}

        # 如果配置了网络结构
        if 'net_arch' in self.agent_config:
            # 确保网络结构格式与算法匹配
            if algorithm in ['PPO', 'A2C']:
                # 对于PPO和A2C，net_arch应该是字典格式 {'pi': [...], 'vf': [...]}
                if isinstance(self.agent_config['net_arch'], dict):
                    # 已经是字典格式，直接使用
                    policy_kwargs['net_arch'] = self.agent_config['net_arch']
                elif isinstance(self.agent_config['net_arch'], list):
                    # 如果是列表格式，转换为字典格式
                    policy_kwargs['net_arch'] = {'pi': self.agent_config['net_arch'], 'vf': self.agent_config['net_arch']}
                else:
                    # 默认网络结构
                    policy_kwargs['net_arch'] = {'pi': [64, 64], 'vf': [64, 64]}
            else:
                # 对于DQN、SAC等，net_arch应该是列表格式 [64, 64]
                if isinstance(self.agent_config['net_arch'], list):
                    # 已经是列表格式，直接使用
                    policy_kwargs['net_arch'] = self.agent_config['net_arch']
                elif isinstance(self.agent_config['net_arch'], dict) and 'pi' in self.agent_config['net_arch']:
                    # 如果是字典格式，转换为列表格式（使用策略网络的结构）
                    policy_kwargs['net_arch'] = self.agent_config['net_arch']['pi']
                else:
                    # 默认网络结构
                    policy_kwargs['net_arch'] = [64, 64]
        else:
            # 默认网络结构
            if algorithm in ['PPO', 'A2C']:
                policy_kwargs['net_arch'] = {'pi': [64, 64], 'vf': [64, 64]}
            else:
                policy_kwargs['net_arch'] = [64, 64]

        # 如果配置了激活函数
        if 'activation_fn' in self.agent_config:
            activation = self.agent_config['activation_fn']
            if activation == 'ReLU':
                policy_kwargs['activation_fn'] = torch.nn.ReLU
            elif activation == 'Tanh':
                policy_kwargs['activation_fn'] = torch.nn.Tanh
            elif activation == 'ELU':
                policy_kwargs['activation_fn'] = torch.nn.ELU

        # 记录最终的策略关键字参数
        self.logger.info(f"策略关键字参数: {policy_kwargs}")

        # 根据算法创建模型
        if algorithm == 'PPO':
            model = PPO(
                policy=policy,
                env=self.env,
                learning_rate=learning_rate,
                gamma=gamma,
                n_steps=self.agent_config.get('n_steps', 2048),
                batch_size=self.agent_config.get('batch_size', 64),
                n_epochs=self.agent_config.get('n_epochs', 10),
                gae_lambda=self.agent_config.get('gae_lambda', 0.95),
                clip_range=self.agent_config.get('clip_range', 0.2),
                clip_range_vf=self.agent_config.get('clip_range_vf', None),
                normalize_advantage=self.agent_config.get('normalize_advantage', True),
                ent_coef=self.agent_config.get('ent_coef', 0.01),
                vf_coef=self.agent_config.get('vf_coef', 0.5),
                max_grad_norm=self.agent_config.get('max_grad_norm', 0.5),
                use_sde=self.agent_config.get('use_sde', False),
                sde_sample_freq=self.agent_config.get('sde_sample_freq', -1),
                target_kl=self.agent_config.get('target_kl', None),
                tensorboard_log=self.agent_config.get('tensorboard_log', None),
                policy_kwargs=policy_kwargs,
                verbose=1,
                seed=self.agent_config.get('seed', None),
                device=device
            )
        elif algorithm == 'A2C':
            model = A2C(
                policy=policy,
                env=self.env,
                learning_rate=learning_rate,
                gamma=gamma,
                n_steps=self.agent_config.get('n_steps', 5),
                vf_coef=self.agent_config.get('vf_coef', 0.5),
                ent_coef=self.agent_config.get('ent_coef', 0.01),
                max_grad_norm=self.agent_config.get('max_grad_norm', 0.5),
                rms_prop_eps=self.agent_config.get('rms_prop_eps', 1e-5),
                use_rms_prop=self.agent_config.get('use_rms_prop', True),
                use_sde=self.agent_config.get('use_sde', False),
                sde_sample_freq=self.agent_config.get('sde_sample_freq', -1),
                normalize_advantage=self.agent_config.get('normalize_advantage', False),
                tensorboard_log=self.agent_config.get('tensorboard_log', None),
                policy_kwargs=policy_kwargs,
                verbose=1,
                seed=self.agent_config.get('seed', None),
                device=device
            )
        elif algorithm == 'DQN':
            model = DQN(
                policy=policy,
                env=self.env,
                learning_rate=learning_rate,
                gamma=gamma,
                buffer_size=self.agent_config.get('buffer_size', 10000),
                learning_starts=self.agent_config.get('learning_starts', 1000),
                batch_size=self.agent_config.get('batch_size', 32),
                tau=self.agent_config.get('tau', 1.0),
                train_freq=self.agent_config.get('train_freq', 4),
                gradient_steps=self.agent_config.get('gradient_steps', 1),
                target_update_interval=self.agent_config.get('target_update_interval', 1000),
                exploration_fraction=self.agent_config.get('exploration_fraction', 0.1),
                exploration_initial_eps=self.agent_config.get('exploration_initial_eps', 1.0),
                exploration_final_eps=self.agent_config.get('exploration_final_eps', 0.05),
                max_grad_norm=self.agent_config.get('max_grad_norm', 10),
                tensorboard_log=self.agent_config.get('tensorboard_log', None),
                policy_kwargs=policy_kwargs,
                verbose=1,
                seed=self.agent_config.get('seed', None),
                device=device
            )
        else:
            raise ValueError(f"不支持的算法: {algorithm}")

        return model

    def train(self, total_timesteps, callback_list=None, hpo_trial=None, progress_bar=True):
        """
        训练DRL智能体

        参数:
            total_timesteps (int): 训练总步数
            callback_list (list, optional): 回调函数列表
            hpo_trial (optuna.Trial, optional): Optuna试验对象
            progress_bar (bool): 是否显示进度条

        返回:
            dict: 训练结果统计信息
        """
        start_time = time.time()
        self.logger.info(f"开始训练，总步数: {total_timesteps}")

        # 检查是否使用集成学习
        ensemble_config = self.agent_config.get('ensemble', {})
        use_ensemble = ensemble_config.get('use', False)
        # 确保use_ensemble是布尔值
        use_ensemble = bool(use_ensemble)
        self.logger.info(f"集成学习状态: {'启用' if use_ensemble else '禁用'}")

        # 如果使用集成学习，但集成学习模块不可用，则发出警告并禁用集成学习
        if use_ensemble and not ENSEMBLE_AVAILABLE:
            self.logger.warning("集成学习模块不可用，将禁用集成学习")
            use_ensemble = False

        # 如果不使用集成学习，直接训练单个模型
        if not use_ensemble:
            # 创建自定义回调
            metrics_callback = RobustMetricsCallback(self)

            # 创建早停回调
            if self.agent_config.get('early_stopping', False):
                reward_threshold = self.agent_config.get('reward_threshold', 0)
                stop_training_callback = StopTrainingOnRewardThreshold(
                    reward_threshold=reward_threshold,
                    verbose=1
                )
                eval_callback = EvalCallback(
                    self.env,
                    callback_on_new_best=stop_training_callback,
                    eval_freq=self.agent_config.get('eval_freq', 10000),
                    n_eval_episodes=self.agent_config.get('n_eval_episodes', 5),
                    verbose=1
                )
                callbacks = [metrics_callback, eval_callback]
            else:
                callbacks = [metrics_callback]

            # 添加用户提供的回调
            if callback_list:
                callbacks.extend(callback_list)

            # 创建回调列表
            callback = CallbackList(callbacks)

            # 训练模型
            self.model.learn(
                total_timesteps=total_timesteps,
                callback=callback,
                progress_bar=progress_bar
            )

            # 保存最佳模型
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            algorithm = self.agent_config.get('algorithm', 'PPO')
            model_path = os.path.join(self.models_dir, f"{algorithm}_{timestamp}_best.zip")
            self.model.save(model_path)
            self.best_model_path = model_path
            self.logger.info(f"最佳模型已保存至: {model_path}")

            # 评估模型在不同市场状态下的表现
            self._evaluate_model_robustness()

        else:
            # 使用集成学习训练多个模型
            self._train_ensemble(total_timesteps, callback_list, progress_bar)

        # 计算训练时间
        training_time = time.time() - start_time
        self.logger.info(f"训练完成，耗时: {training_time:.2f}秒")

        # 返回训练结果
        return {
            'training_time': training_time,
            'best_model_path': self.best_model_path,
            'training_stats': self.training_stats
        }

    def _train_ensemble(self, total_timesteps, callback_list=None, progress_bar=True):
        """
        训练集成模型

        参数:
            total_timesteps (int): 训练总步数
            callback_list (list, optional): 回调函数列表
            progress_bar (bool): 是否显示进度条
        """
        ensemble_config = self.agent_config.get('ensemble', {})
        n_models = ensemble_config.get('n_models', 3)
        voting_method = ensemble_config.get('voting_method', 'majority')
        use_multi_algorithm = ensemble_config.get('use_multi_algorithm', False)

        self.logger.info(f"开始集成学习训练，模型数量: {n_models}, 投票方法: {voting_method}")

        # 定义要使用的算法列表
        if 'selected_algorithms' in ensemble_config and ensemble_config['selected_algorithms']:
            # 使用用户选择的算法列表
            algorithms = ensemble_config['selected_algorithms']
            self.logger.info(f"使用用户选择的算法列表: {algorithms}")
        else:
            # 如果使用多算法集成学习但没有指定算法列表或列表为空
            if use_multi_algorithm:
                # 使用默认的多种算法进行集成学习
                algorithms = ['PPO', 'A2C', 'DQN']
                self.logger.info(f"使用默认算法列表: {algorithms}")
            else:
                # 使用单一算法进行集成学习
                # 检查是否在集成学习配置中指定了算法
                ensemble_algorithm = ensemble_config.get('algorithm', self.agent_config.get('algorithm', 'PPO'))
                algorithms = [ensemble_algorithm] * n_models
                self.logger.info(f"使用单一算法 {ensemble_algorithm} 进行集成学习")

        # 确保算法数量不超过模型数量
        if len(algorithms) > n_models:
            algorithms = algorithms[:n_models]
            self.logger.info(f"算法数量超过模型数量，截取为: {algorithms}")

        # 确保算法列表不为空
        if not algorithms:
            self.logger.warning("算法列表为空，使用默认算法 'PPO'")
            algorithms = ['PPO']

        # 如果算法数量少于模型数量，重复使用算法
        if len(algorithms) < n_models:
            while len(algorithms) < n_models:
                algorithms.append(algorithms[len(algorithms) % len(algorithms)])

        # 保存原始模型和配置
        original_model = self.model
        original_algorithm = self.agent_config.get('algorithm', 'PPO')
        original_config = self.agent_config.copy()

        # 训练多个模型
        ensemble_models = []
        model_paths = []

        for i in range(n_models):
            # 更新算法
            algorithm = algorithms[i]
            self.agent_config['algorithm'] = algorithm

            # 创建新的模型
            self.model = self._create_model()

            # 创建自定义回调
            metrics_callback = RobustMetricsCallback(self)
            callbacks = [metrics_callback]

            # 添加用户提供的回调
            if callback_list:
                callbacks.extend(callback_list)

            # 创建回调列表
            callback = CallbackList(callbacks)

            # 训练模型
            self.logger.info(f"训练集成模型 {i+1}/{n_models}，算法: {algorithm}")
            self.model.learn(
                total_timesteps=total_timesteps // n_models,  # 平均分配训练步数
                callback=callback,
                progress_bar=progress_bar
            )

            # 保存模型
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            model_path = os.path.join(self.models_dir, f"{algorithm}_{timestamp}_ensemble_{i+1}.zip")
            self.model.save(model_path)
            model_paths.append(model_path)
            ensemble_models.append(self.model)

            self.logger.info(f"集成模型 {i+1}/{n_models} 已保存至: {model_path}")

        # 创建集成模型
        self.ensemble_model = EnsembleModel(
            models=ensemble_models,
            model_paths=model_paths,
            voting_method=voting_method
        )

        # 保存集成模型
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        ensemble_path = os.path.join(self.models_dir, f"ensemble_{timestamp}_best.pkl")
        self.ensemble_model.save(ensemble_path)
        self.best_model_path = ensemble_path

        # 恢复原始配置
        self.model = original_model
        self.agent_config = original_config

        self.logger.info(f"集成模型已保存至: {ensemble_path}")
        self.logger.info(f"集成学习训练完成，共 {len(ensemble_models)}/{n_models} 个模型")

        # 评估集成模型在不同市场状态下的表现
        self._evaluate_ensemble_robustness()

    def _evaluate_model_robustness(self):
        """评估模型在不同市场状态下的鲁棒性"""
        # 获取训练数据
        df_processed_data = self.env_config.get('df_processed_data')

        # 评估模型在不同市场状态下的表现
        results = self.model_evaluator.evaluate_model_in_market_conditions(
            model=self.model,
            data=df_processed_data,
            initial_capital=self.env_config.get('initial_capital', 100000),
            commission_rate=self.env_config.get('commission_rate', 0.0003),
            window_size=self.env_config.get('window_size', 20)
        )

        # 记录评估结果
        self.logger.info(f"模型鲁棒性评估完成，鲁棒性得分: {results['robustness_score']:.4f}")

        # 生成鲁棒性报告
        report = self.model_evaluator.get_robustness_report()

        # 记录报告
        self.logger.info("模型鲁棒性报告:")
        self.logger.info(f"鲁棒性得分: {report['robustness_score']:.4f}")
        self.logger.info(f"优势: {report['strengths']}")
        self.logger.info(f"劣势: {report['weaknesses']}")
        self.logger.info(f"建议: {report['recommendations']}")

        # 绘制性能对比图
        fig = self.model_evaluator.plot_performance_by_market_condition()
        if fig:
            # 保存图表
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            fig_path = os.path.join('plots', f"robustness_{timestamp}.png")
            os.makedirs('plots', exist_ok=True)
            fig.savefig(fig_path)
            plt.close(fig)
            self.logger.info(f"性能对比图已保存至: {fig_path}")

    def _evaluate_ensemble_robustness(self):
        """评估集成模型在不同市场状态下的鲁棒性"""
        if not self.ensemble_model:
            self.logger.warning("集成模型不存在，无法评估鲁棒性")
            return

        # 获取训练数据
        df_processed_data = self.env_config.get('df_processed_data')

        # 评估集成模型在不同市场状态下的表现
        results = self.model_evaluator.evaluate_model_in_market_conditions(
            model=self.ensemble_model,
            data=df_processed_data,
            initial_capital=self.env_config.get('initial_capital', 100000),
            commission_rate=self.env_config.get('commission_rate', 0.0003),
            window_size=self.env_config.get('window_size', 20)
        )

        # 记录评估结果
        self.logger.info(f"集成模型鲁棒性评估完成，鲁棒性得分: {results['robustness_score']:.4f}")

        # 生成鲁棒性报告
        report = self.model_evaluator.get_robustness_report()

        # 记录报告
        self.logger.info("集成模型鲁棒性报告:")
        self.logger.info(f"鲁棒性得分: {report['robustness_score']:.4f}")
        self.logger.info(f"优势: {report['strengths']}")
        self.logger.info(f"劣势: {report['weaknesses']}")
        self.logger.info(f"建议: {report['recommendations']}")

        # 绘制性能对比图
        fig = self.model_evaluator.plot_performance_by_market_condition()
        if fig:
            # 保存图表
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            fig_path = os.path.join('plots', f"ensemble_robustness_{timestamp}.png")
            os.makedirs('plots', exist_ok=True)
            fig.savefig(fig_path)
            plt.close(fig)
            self.logger.info(f"集成模型性能对比图已保存至: {fig_path}")

    def predict(self, observation, state=None, deterministic=True):
        """
        使用模型进行预测

        参数:
            observation: 观测
            state: 状态（用于循环策略）
            deterministic: 是否使用确定性动作

        返回:
            tuple: (action, state)
        """
        if self.ensemble_model:
            return self.ensemble_model.predict(observation, state, deterministic)
        else:
            return self.model.predict(observation, state, deterministic)

    def save(self, path):
        """
        保存模型

        参数:
            path (str): 保存路径
        """
        if self.ensemble_model:
            self.ensemble_model.save(path)
        else:
            self.model.save(path)

        self.logger.info(f"模型已保存至: {path}")

    def load(self, path):
        """
        加载模型

        参数:
            path (str): 加载路径
        """
        if path.endswith('.pkl'):
            # 加载集成模型
            self.ensemble_model = EnsembleModel.load(path)
            self.best_model_path = path
            self.logger.info(f"集成模型已从 {path} 加载")
        else:
            # 加载单个模型
            algorithm = self.agent_config.get('algorithm', 'PPO')

            if algorithm == 'PPO':
                self.model = PPO.load(path, env=self.env)
            elif algorithm == 'A2C':
                self.model = A2C.load(path, env=self.env)
            elif algorithm == 'DQN':
                self.model = DQN.load(path, env=self.env)
            else:
                raise ValueError(f"不支持的算法: {algorithm}")

            self.best_model_path = path
            self.logger.info(f"模型已从 {path} 加载")

    def optimize_hyperparameters(self, n_trials=10, n_startup_trials=5, n_evaluations=2, n_timesteps=5000):
        """
        优化超参数

        参数:
            n_trials (int): 试验次数
            n_startup_trials (int): 启动试验次数
            n_evaluations (int): 每次试验的评估次数
            n_timesteps (int): 每次评估的时间步数

        返回:
            dict: 优化结果
        """
        if self.hpo_config is None:
            self.logger.error("未提供超参数优化配置")
            return None

        self.logger.info(f"开始超参数优化，试验次数: {n_trials}")

        # 创建Optuna研究
        study_name = f"drl_hpo_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
        sampler = TPESampler(n_startup_trials=n_startup_trials)
        pruner = MedianPruner(n_startup_trials=n_startup_trials, n_warmup_steps=n_evaluations // 3)

        study = optuna.create_study(
            study_name=study_name,
            sampler=sampler,
            pruner=pruner,
            direction="maximize"
        )

        # 定义目标函数
        def objective(trial):
            # 从超参数配置中获取参数范围
            param_ranges = self.hpo_config.get('param_ranges', {})

            # 创建超参数字典
            hyperparams = {}

            # 算法选择
            if 'algorithm' in param_ranges:
                hyperparams['algorithm'] = trial.suggest_categorical('algorithm', param_ranges['algorithm'])
            else:
                hyperparams['algorithm'] = self.agent_config.get('algorithm', 'PPO')

            # 学习率
            if 'learning_rate' in param_ranges:
                lr_min, lr_max = param_ranges['learning_rate']
                hyperparams['learning_rate'] = trial.suggest_float('learning_rate', lr_min, lr_max, log=True)
            else:
                hyperparams['learning_rate'] = self.agent_config.get('learning_rate', 0.0003)

            # Gamma
            if 'gamma' in param_ranges:
                gamma_min, gamma_max = param_ranges['gamma']
                hyperparams['gamma'] = trial.suggest_float('gamma', gamma_min, gamma_max)
            else:
                hyperparams['gamma'] = self.agent_config.get('gamma', 0.99)

            # 根据算法添加特定参数
            algorithm = hyperparams['algorithm']

            if algorithm == 'PPO':
                # PPO特定参数
                if 'n_steps' in param_ranges:
                    n_steps_min, n_steps_max = param_ranges['n_steps']
                    hyperparams['n_steps'] = trial.suggest_int('n_steps', n_steps_min, n_steps_max, log=True)
                else:
                    hyperparams['n_steps'] = self.agent_config.get('n_steps', 2048)

                if 'batch_size' in param_ranges:
                    batch_size_min, batch_size_max = param_ranges['batch_size']
                    hyperparams['batch_size'] = trial.suggest_int('batch_size', batch_size_min, batch_size_max, log=True)
                else:
                    hyperparams['batch_size'] = self.agent_config.get('batch_size', 64)

                if 'n_epochs' in param_ranges:
                    n_epochs_min, n_epochs_max = param_ranges['n_epochs']
                    hyperparams['n_epochs'] = trial.suggest_int('n_epochs', n_epochs_min, n_epochs_max)
                else:
                    hyperparams['n_epochs'] = self.agent_config.get('n_epochs', 10)

                if 'gae_lambda' in param_ranges:
                    gae_lambda_min, gae_lambda_max = param_ranges['gae_lambda']
                    hyperparams['gae_lambda'] = trial.suggest_float('gae_lambda', gae_lambda_min, gae_lambda_max)
                else:
                    hyperparams['gae_lambda'] = self.agent_config.get('gae_lambda', 0.95)

                if 'clip_range' in param_ranges:
                    clip_range_min, clip_range_max = param_ranges['clip_range']
                    hyperparams['clip_range'] = trial.suggest_float('clip_range', clip_range_min, clip_range_max)
                else:
                    hyperparams['clip_range'] = self.agent_config.get('clip_range', 0.2)

                if 'ent_coef' in param_ranges:
                    ent_coef_min, ent_coef_max = param_ranges['ent_coef']
                    hyperparams['ent_coef'] = trial.suggest_float('ent_coef', ent_coef_min, ent_coef_max, log=True)
                else:
                    hyperparams['ent_coef'] = self.agent_config.get('ent_coef', 0.01)

                if 'vf_coef' in param_ranges:
                    vf_coef_min, vf_coef_max = param_ranges['vf_coef']
                    hyperparams['vf_coef'] = trial.suggest_float('vf_coef', vf_coef_min, vf_coef_max)
                else:
                    hyperparams['vf_coef'] = self.agent_config.get('vf_coef', 0.5)

            elif algorithm == 'A2C':
                # A2C特定参数
                if 'n_steps' in param_ranges:
                    n_steps_min, n_steps_max = param_ranges['n_steps']
                    hyperparams['n_steps'] = trial.suggest_int('n_steps', n_steps_min, n_steps_max)
                else:
                    hyperparams['n_steps'] = self.agent_config.get('n_steps', 5)

                if 'ent_coef' in param_ranges:
                    ent_coef_min, ent_coef_max = param_ranges['ent_coef']
                    hyperparams['ent_coef'] = trial.suggest_float('ent_coef', ent_coef_min, ent_coef_max, log=True)
                else:
                    hyperparams['ent_coef'] = self.agent_config.get('ent_coef', 0.01)

                if 'vf_coef' in param_ranges:
                    vf_coef_min, vf_coef_max = param_ranges['vf_coef']
                    hyperparams['vf_coef'] = trial.suggest_float('vf_coef', vf_coef_min, vf_coef_max)
                else:
                    hyperparams['vf_coef'] = self.agent_config.get('vf_coef', 0.5)

            elif algorithm == 'DQN':
                # DQN特定参数
                if 'buffer_size' in param_ranges:
                    buffer_size_min, buffer_size_max = param_ranges['buffer_size']
                    hyperparams['buffer_size'] = trial.suggest_int('buffer_size', buffer_size_min, buffer_size_max, log=True)
                else:
                    hyperparams['buffer_size'] = self.agent_config.get('buffer_size', 10000)

                if 'learning_starts' in param_ranges:
                    learning_starts_min, learning_starts_max = param_ranges['learning_starts']
                    hyperparams['learning_starts'] = trial.suggest_int('learning_starts', learning_starts_min, learning_starts_max, log=True)
                else:
                    hyperparams['learning_starts'] = self.agent_config.get('learning_starts', 1000)

                if 'batch_size' in param_ranges:
                    batch_size_min, batch_size_max = param_ranges['batch_size']
                    hyperparams['batch_size'] = trial.suggest_int('batch_size', batch_size_min, batch_size_max, log=True)
                else:
                    hyperparams['batch_size'] = self.agent_config.get('batch_size', 32)

                if 'target_update_interval' in param_ranges:
                    target_update_interval_min, target_update_interval_max = param_ranges['target_update_interval']
                    hyperparams['target_update_interval'] = trial.suggest_int('target_update_interval', target_update_interval_min, target_update_interval_max, log=True)
                else:
                    hyperparams['target_update_interval'] = self.agent_config.get('target_update_interval', 1000)

                if 'exploration_fraction' in param_ranges:
                    exploration_fraction_min, exploration_fraction_max = param_ranges['exploration_fraction']
                    hyperparams['exploration_fraction'] = trial.suggest_float('exploration_fraction', exploration_fraction_min, exploration_fraction_max)
                else:
                    hyperparams['exploration_fraction'] = self.agent_config.get('exploration_fraction', 0.1)

            # 更新智能体配置
            agent_config = self.agent_config.copy()
            agent_config.update(hyperparams)

            # 创建新的环境和模型
            env_config = self.env_config.copy()
            agent = RobustDRLAgent(env_config=env_config, agent_config=agent_config)

            # 训练模型
            mean_rewards = []

            for _ in range(n_evaluations):
                # 训练模型
                agent.train(total_timesteps=n_timesteps, progress_bar=False)

                # 评估模型
                df_processed_data = env_config.get('df_processed_data')
                results = agent.model_evaluator.evaluate_model_in_market_conditions(
                    model=agent.model,
                    data=df_processed_data,
                    initial_capital=env_config.get('initial_capital', 100000),
                    commission_rate=env_config.get('commission_rate', 0.0003),
                    window_size=env_config.get('window_size', 20)
                )

                # 获取整体表现
                overall_return = results['overall'].get('annualized_return', 0)
                overall_sharpe = results['overall'].get('sharpe_ratio', 0)
                robustness_score = results['robustness_score']

                # 计算综合评分
                score = overall_return * 0.4 + overall_sharpe * 0.3 + robustness_score * 0.3
                mean_rewards.append(score)

                # 报告中间值
                trial.report(score, _)

                # 处理修剪
                if trial.should_prune():
                    raise optuna.TrialPruned()

            # 返回平均评分
            return np.mean(mean_rewards)

        # 运行优化
        study.optimize(objective, n_trials=n_trials)

        # 获取最佳参数
        best_params = study.best_params
        best_value = study.best_value

        self.logger.info(f"超参数优化完成，最佳评分: {best_value:.4f}")
        self.logger.info(f"最佳参数: {best_params}")

        # 更新智能体配置
        self.agent_config.update(best_params)

        # 使用最佳参数重新创建模型
        self.model = self._create_model()

        # 返回优化结果
        return {
            'best_params': best_params,
            'best_value': best_value,
            'study': study
        }

    def plot_optimization_results(self, study):
        """
        绘制优化结果

        参数:
            study (optuna.Study): Optuna研究对象

        返回:
            dict: 图表对象字典
        """
        # 创建图表目录
        plots_dir = 'plots/optimization'
        os.makedirs(plots_dir, exist_ok=True)

        # 绘制优化历史
        fig1 = optuna.visualization.plot_optimization_history(study)
        fig1_path = os.path.join(plots_dir, 'optimization_history.png')
        fig1.write_image(fig1_path)

        # 绘制参数重要性
        fig2 = optuna.visualization.plot_param_importances(study)
        fig2_path = os.path.join(plots_dir, 'param_importances.png')
        fig2.write_image(fig2_path)

        # 绘制参数关系
        fig3 = optuna.visualization.plot_parallel_coordinate(study)
        fig3_path = os.path.join(plots_dir, 'parallel_coordinate.png')
        fig3.write_image(fig3_path)

        # 绘制切片图
        fig4 = optuna.visualization.plot_slice(study)
        fig4_path = os.path.join(plots_dir, 'slice.png')
        fig4.write_image(fig4_path)

        self.logger.info(f"优化结果图表已保存至: {plots_dir}")

        return {
            'optimization_history': fig1,
            'param_importances': fig2,
            'parallel_coordinate': fig3,
            'slice': fig4
        }
