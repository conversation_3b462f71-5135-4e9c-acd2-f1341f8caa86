"""
测试BestModelCallback修复
"""

import os
import logging
import numpy as np
import pandas as pd
import gymnasium as gym
from stable_baselines3 import PPO
from stable_baselines3.common.monitor import Monitor
from stable_baselines3.common.vec_env import DummyVecEnv

from core_logic.drl_agent import DRLAgent, BestModelCallback
from core_logic.trading_environment import TradingEnvironment
from core_logic.utils import is_gpu_available

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_callback')

def create_test_data():
    """创建测试数据"""
    # 创建一个简单的价格序列
    dates = pd.date_range(start='2020-01-01', periods=100, freq='D')
    prices = np.random.normal(100, 10, 100).cumsum() + 1000
    volumes = np.random.randint(1000, 10000, 100)

    # 创建DataFrame
    df = pd.DataFrame({
        '开盘': prices * 0.99,
        '最高': prices * 1.02,
        '最低': prices * 0.98,
        '收盘': prices,
        '成交量': volumes
    }, index=dates)

    # 添加一些技术指标作为特征
    df['ma5'] = df['收盘'].rolling(5).mean()
    df['ma10'] = df['收盘'].rolling(10).mean()
    df['rsi'] = 50 + np.random.normal(0, 10, 100)  # 简化的RSI

    # 填充NaN值
    df = df.fillna(method='bfill')

    return df

def main():
    """主函数"""
    logger.info("开始测试BestModelCallback修复")

    try:
        # 创建测试数据
        df = create_test_data()
        logger.info(f"创建了测试数据，形状: {df.shape}")

        # 环境配置
        env_config = {
            'df_processed_data': df,
            'initial_capital': 10000,
            'commission_rate': 0.001,
            'window_size': 10
        }

        # 智能体配置
        agent_config = {
            'algorithm': 'PPO',
            'policy_network': 'MlpPolicy',
            'learning_rate': 0.0003,
            'gamma': 0.99,
            'n_steps': 64,
            'batch_size': 32,
            'n_epochs': 5,
            'use_gpu': is_gpu_available()
        }

        # 创建DRL智能体
        drl_agent = DRLAgent(env_config, agent_config)
        logger.info("创建了DRL智能体")

        # 创建评估环境
        eval_env = drl_agent._create_environment()

        # 创建最佳模型回调
        best_model_callback = BestModelCallback(
            eval_env=eval_env,
            eval_freq=200,
            n_eval_episodes=2,
            save_path="saved_models/test_best_model.zip",
            verbose=1
        )

        # 训练模型
        logger.info("开始训练模型...")
        total_timesteps = 1000  # 短时间训练

        drl_agent.train(
            total_timesteps=total_timesteps,
            callback_list=[best_model_callback],
            progress_bar=True,
            save_best_model=False  # 不使用内置的最佳模型保存，只使用我们的回调
        )

        logger.info("训练完成，没有发生错误")
        return True

    except Exception as e:
        logger.error(f"测试过程中发生错误: {str(e)}", exc_info=True)
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("测试成功：BestModelCallback修复有效")
    else:
        print("测试失败：BestModelCallback修复无效")
