"""
UI测试运行脚本

该脚本运行UI测试并生成测试报告。
"""

import os
import sys
import unittest
import HtmlTestRunner
import logging
from datetime import datetime

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(parent_dir, 'logs', 'ui_test_runner.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('ui_test_runner')

def run_tests():
    """运行UI测试并生成报告"""
    logger.info("开始运行UI测试")
    
    # 确保报告目录存在
    reports_dir = os.path.join(parent_dir, 'test_reports', 'ui_tests')
    os.makedirs(reports_dir, exist_ok=True)
    
    # 导入测试模块
    from ui_test_automation import StreamlitUITest
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_suite.addTest(unittest.makeSuite(StreamlitUITest))
    
    # 运行测试并生成报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    runner = HtmlTestRunner.HTMLTestRunner(
        output=reports_dir,
        report_name=f"ui_test_report_{timestamp}",
        combine_reports=True,
        report_title="DRL量化交易系统UI测试报告"
    )
    
    result = runner.run(test_suite)
    
    # 输出测试结果摘要
    logger.info(f"测试完成: 运行 {result.testsRun} 个测试")
    logger.info(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    logger.info(f"失败: {len(result.failures)}")
    logger.info(f"错误: {len(result.errors)}")
    
    # 返回测试结果
    return result

def main():
    """主函数"""
    logger.info("=" * 80)
    logger.info("UI测试运行脚本")
    logger.info("=" * 80)
    
    # 运行测试
    result = run_tests()
    
    # 根据测试结果设置退出代码
    if result.wasSuccessful():
        logger.info("所有测试通过")
        sys.exit(0)
    else:
        logger.error("测试失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
