"""
深度强化学习交易智能体模块
提供PPO和SAC智能体实现
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from torch.distributions import Categorical, Normal
import gym
from typing import Dict, List, Union, Tuple, Optional, Any
import logging
import os
from datetime import datetime
import json
import matplotlib.pyplot as plt

logger = logging.getLogger('drl_trading')

class DRLBase:
    """DRL智能体基类"""
    
    def __init__(self, 
                env: gym.Env,
                model_name: str = 'drl_agent',
                checkpoint_dir: str = './checkpoints',
                device: torch.device = None,
                gamma: float = 0.99,
                seed: int = 42):
        """
        初始化DRL基类
        
        参数:
            env: gym.Env 交易环境
            model_name: str 模型名称
            checkpoint_dir: str 检查点保存目录
            device: torch.device 训练设备
            gamma: float 折扣因子
            seed: int 随机种子
        """
        self.env = env
        self.model_name = model_name
        self.checkpoint_dir = checkpoint_dir
        self.gamma = gamma
        
        # 设置随机种子
        np.random.seed(seed)
        torch.manual_seed(seed)
        
        # 设置设备
        if device is None:
            self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        else:
            self.device = device
            
        # 确保检查点目录存在
        if not os.path.exists(checkpoint_dir):
            os.makedirs(checkpoint_dir)
            
        # 初始化训练记录
        self.train_rewards = []
        self.train_lengths = []
        self.eval_rewards = []
        self.train_steps = 0
        self.best_reward = -float('inf')
            
    def preprocess_observation(self, observation: Dict[str, np.ndarray]) -> torch.Tensor:
        """
        预处理观察，转换为神经网络输入
        
        参数:
            observation: Dict 观察字典
            
        返回:
            处理后的tensor
        """
        # 市场特征
        market = torch.FloatTensor(observation['market']).to(self.device)
        # 账户信息
        account = torch.FloatTensor(observation['account']).to(self.device)
        
        # 展平市场特征
        batch_size = market.shape[0] if market.dim() > 2 else 1
        market_flat = market.view(batch_size, -1) if market.dim() > 2 else market.view(1, -1)
        
        # 展平账户信息
        account_flat = account.view(batch_size, -1) if account.dim() > 1 else account.view(1, -1)
        
        # 连接所有特征
        features = torch.cat([market_flat, account_flat], dim=1)
        
        return features
        
    def save_checkpoint(self, is_best: bool = False) -> str:
        """
        保存模型检查点，需要子类实现
        
        参数:
            is_best: bool 是否为最佳模型
            
        返回:
            保存路径
        """
        raise NotImplementedError("需要在子类中实现")
        
    def load_checkpoint(self, checkpoint_path: str) -> None:
        """
        加载模型检查点，需要子类实现
        
        参数:
            checkpoint_path: str 检查点路径
        """
        raise NotImplementedError("需要在子类中实现")
        
    def train(self, n_episodes: int, max_steps: int = 1000) -> None:
        """
        训练智能体，需要子类实现
        
        参数:
            n_episodes: int 训练轮数
            max_steps: int 每轮最大步数
        """
        raise NotImplementedError("需要在子类中实现")
        
    def evaluate(self, n_episodes: int = 10, render: bool = False) -> Tuple[float, float]:
        """
        评估智能体性能
        
        参数:
            n_episodes: int 评估轮数
            render: bool 是否渲染
            
        返回:
            (平均奖励, 奖励标准差)
        """
        rewards = []
        
        for episode in range(n_episodes):
            obs = self.env.reset()
            done = False
            episode_reward = 0
            
            while not done:
                action = self.select_action(obs, eval=True)
                obs, reward, done, _ = self.env.step(action)
                episode_reward += reward
                
                if render:
                    self.env.render()
                    
            rewards.append(episode_reward)
            
        # 计算统计信息
        mean_reward = np.mean(rewards)
        std_reward = np.std(rewards)
        
        # 记录评估结果
        self.eval_rewards.append(mean_reward)
        
        # 打印结果
        logger.info(f"Evaluation over {n_episodes} episodes: Mean Reward: {mean_reward:.2f}, Std: {std_reward:.2f}")
        
        return mean_reward, std_reward
        
    def select_action(self, observation: Dict[str, np.ndarray], eval: bool = False) -> int:
        """
        选择动作，需要子类实现
        
        参数:
            observation: Dict 观察
            eval: bool 是否在评估模式
            
        返回:
            选择的动作
        """
        raise NotImplementedError("需要在子类中实现")
        
    def plot_training_history(self, save_path: Optional[str] = None) -> None:
        """
        绘制训练历史
        
        参数:
            save_path: str 保存路径
        """
        plt.figure(figsize=(12, 8))
        
        # 绘制训练奖励
        plt.subplot(2, 1, 1)
        plt.plot(self.train_rewards, label='Train Rewards')
        if self.eval_rewards:
            plt.plot(np.linspace(0, len(self.train_rewards), len(self.eval_rewards)), 
                    self.eval_rewards, 'r-', label='Eval Rewards')
        plt.xlabel('Episode')
        plt.ylabel('Reward')
        plt.legend()
        plt.title('Training Rewards')
        
        # 绘制回合长度
        plt.subplot(2, 1, 2)
        plt.plot(self.train_lengths)
        plt.xlabel('Episode')
        plt.ylabel('Episode Length')
        plt.title('Episode Lengths')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path)
            plt.close()
        else:
            plt.show()


class PPONetwork(nn.Module):
    """PPO网络，包含策略网络和价值网络"""
    
    def __init__(self, input_dim: int, action_dim: int, hidden_dims: List[int] = [256, 128]):
        """
        初始化PPO网络
        
        参数:
            input_dim: int 输入维度
            action_dim: int 动作维度
            hidden_dims: List[int] 隐藏层维度列表
        """
        super(PPONetwork, self).__init__()
        
        # 特征提取层
        layers = []
        prev_dim = input_dim
        
        for dim in hidden_dims:
            layers.append(nn.Linear(prev_dim, dim))
            layers.append(nn.ReLU())
            prev_dim = dim
            
        self.feature_extractor = nn.Sequential(*layers)
        
        # 策略网络 (输出动作概率)
        self.policy_head = nn.Sequential(
            nn.Linear(hidden_dims[-1], action_dim),
            nn.Softmax(dim=-1)
        )
        
        # 价值网络 (估计状态价值)
        self.value_head = nn.Linear(hidden_dims[-1], 1)
        
    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        前向传播
        
        参数:
            x: Tensor 输入特征
            
        返回:
            (动作概率, 状态价值)
        """
        features = self.feature_extractor(x)
        action_probs = self.policy_head(features)
        state_value = self.value_head(features)
        
        return action_probs, state_value


class PPOAgent(DRLBase):
    """PPO算法智能体"""
    
    def __init__(self, 
                env: gym.Env,
                model_name: str = 'ppo_agent',
                checkpoint_dir: str = './checkpoints',
                device: torch.device = None,
                gamma: float = 0.99,
                clip_ratio: float = 0.2,
                policy_lr: float = 3e-4,
                value_lr: float = 1e-3,
                gae_lambda: float = 0.95,
                entropy_coef: float = 0.01,
                seed: int = 42):
        """
        初始化PPO智能体
        
        参数:
            env: gym.Env 交易环境
            model_name: str 模型名称
            checkpoint_dir: str 检查点保存目录
            device: torch.device 训练设备
            gamma: float 折扣因子
            clip_ratio: float PPO剪切比率
            policy_lr: float 策略学习率
            value_lr: float 价值学习率
            gae_lambda: float GAE lambda参数
            entropy_coef: float 熵正则化系数
            seed: int 随机种子
        """
        super(PPOAgent, self).__init__(env, model_name, checkpoint_dir, device, gamma, seed)
        
        # PPO超参数
        self.clip_ratio = clip_ratio
        self.policy_lr = policy_lr
        self.value_lr = value_lr
        self.gae_lambda = gae_lambda
        self.entropy_coef = entropy_coef
        
        # 获取观察维度
        sample_obs = env.reset()
        processed_obs = self.preprocess_observation(sample_obs)
        input_dim = processed_obs.shape[1]
        
        # 动作维度
        action_dim = env.action_space.n
        
        # 创建网络
        self.network = PPONetwork(input_dim, action_dim).to(self.device)
        
        # 创建优化器
        self.optimizer = optim.Adam(self.network.parameters(), lr=policy_lr)
        
        # 训练缓冲区
        self.buffer = {
            'states': [],
            'actions': [],
            'rewards': [],
            'values': [],
            'log_probs': [],
            'dones': []
        }
        
    def select_action(self, observation: Dict[str, np.ndarray], eval: bool = False) -> int:
        """
        选择动作
        
        参数:
            observation: Dict 观察
            eval: bool 是否在评估模式
            
        返回:
            选择的动作
        """
        state = self.preprocess_observation(observation)
        
        with torch.no_grad():
            action_probs, state_value = self.network(state)
            
        # 创建分类分布
        dist = Categorical(action_probs)
        
        if eval:
            # 评估时选择最高概率的动作
            action = torch.argmax(action_probs, dim=1).item()
        else:
            # 训练时根据概率采样
            action = dist.sample().item()
            
        # 如果在训练模式，保存到缓冲区
        if not eval:
            self.buffer['states'].append(state)
            self.buffer['actions'].append(action)
            self.buffer['values'].append(state_value.item())
            self.buffer['log_probs'].append(dist.log_prob(torch.tensor(action)).item())
            self.buffer['dones'].append(False)  # 将在step中更新
            
        return action
        
    def train_on_batch(self, 
                     states: torch.Tensor, 
                     actions: torch.Tensor,
                     returns: torch.Tensor, 
                     advantages: torch.Tensor,
                     old_log_probs: torch.Tensor) -> Tuple[float, float, float]:
        """
        在批次数据上训练
        
        参数:
            states: Tensor 状态批次
            actions: Tensor 动作批次
            returns: Tensor 回报批次
            advantages: Tensor 优势批次
            old_log_probs: Tensor 旧策略的动作对数概率
            
        返回:
            (策略损失, 价值损失, 熵)
        """
        # 前向传播
        action_probs, values = self.network(states)
        
        # 创建分类分布
        dist = Categorical(action_probs)
        
        # 计算新的动作对数概率
        new_log_probs = dist.log_prob(actions)
        
        # 计算比率 r(θ) = π_θ(a|s) / π_θ_old(a|s)
        ratio = torch.exp(new_log_probs - old_log_probs)
        
        # 计算未剪切的替代目标
        surr1 = ratio * advantages
        
        # 计算剪切的替代目标
        surr2 = torch.clamp(ratio, 1.0 - self.clip_ratio, 1.0 + self.clip_ratio) * advantages
        
        # 计算策略损失 (负号是因为我们要最大化，而优化器是最小化)
        policy_loss = -torch.min(surr1, surr2).mean()
        
        # 计算值损失 (MSE)
        value_loss = nn.MSELoss()(values, returns)
        
        # 计算熵 (用于鼓励探索)
        entropy = dist.entropy().mean()
        
        # 总损失 = 策略损失 + 值损失系数 * 值损失 - 熵系数 * 熵
        loss = policy_loss + 0.5 * value_loss - self.entropy_coef * entropy
        
        # 梯度下降
        self.optimizer.zero_grad()
        loss.backward()
        
        # 梯度裁剪
        nn.utils.clip_grad_norm_(self.network.parameters(), max_norm=0.5)
        
        self.optimizer.step()
        
        return policy_loss.item(), value_loss.item(), entropy.item()
        
    def compute_gae(self, 
                   rewards: List[float], 
                   values: List[float], 
                   dones: List[bool],
                   next_value: float = 0.0) -> Tuple[List[float], List[float]]:
        """
        计算广义优势估计 (GAE)
        
        参数:
            rewards: List[float] 奖励列表
            values: List[float] 价值列表
            dones: List[bool] 结束标志列表
            next_value: float 下一个状态的价值
            
        返回:
            (回报列表, 优势列表)
        """
        advantages = []
        returns = []
        gae = 0
        
        # 逆序计算
        for t in reversed(range(len(rewards))):
            if t == len(rewards) - 1:
                next_value = next_value
                next_non_terminal = 1.0 - dones[t]
            else:
                next_value = values[t + 1]
                next_non_terminal = 1.0 - dones[t + 1]
                
            # 计算TD误差
            delta = rewards[t] + self.gamma * next_value * next_non_terminal - values[t]
            
            # 计算GAE
            gae = delta + self.gamma * self.gae_lambda * next_non_terminal * gae
            
            # 添加到列表
            advantages.insert(0, gae)
            returns.insert(0, gae + values[t])
            
        return returns, advantages
        
    def train(self, n_episodes: int, epoch_per_update: int = 4, batch_size: int = 64, 
             evaluate_interval: int = 10, max_steps: int = 1000) -> None:
        """
        训练智能体
        
        参数:
            n_episodes: int 训练轮数
            epoch_per_update: int 每次更新的epoch数
            batch_size: int 批次大小
            evaluate_interval: int 评估间隔
            max_steps: int 每轮最大步数
        """
        for episode in range(n_episodes):
            # 重置环境和缓冲区
            obs = self.env.reset()
            self.buffer = {
                'states': [],
                'actions': [],
                'rewards': [],
                'values': [],
                'log_probs': [],
                'dones': []
            }
            
            done = False
            episode_reward = 0
            episode_length = 0
            
            # 收集轨迹
            while not done and episode_length < max_steps:
                # 选择动作
                action = self.select_action(obs)
                
                # 执行动作
                next_obs, reward, done, _ = self.env.step(action)
                
                # 保存到缓冲区
                self.buffer['rewards'].append(reward)
                self.buffer['dones'][-1] = done
                
                # 更新状态
                obs = next_obs
                episode_reward += reward
                episode_length += 1
                self.train_steps += 1
                
            # 如果回合结束，获取最后状态的价值
            if done:
                next_value = 0.0
            else:
                state = self.preprocess_observation(obs)
                with torch.no_grad():
                    _, next_value = self.network(state)
                    next_value = next_value.item()
            
            # 计算GAE
            returns, advantages = self.compute_gae(
                self.buffer['rewards'], 
                self.buffer['values'], 
                self.buffer['dones'],
                next_value
            )
            
            # 转换为张量
            states = torch.cat(self.buffer['states'])
            actions = torch.tensor(self.buffer['actions'], device=self.device)
            returns = torch.tensor(returns, device=self.device).unsqueeze(1)
            advantages = torch.tensor(advantages, device=self.device)
            old_log_probs = torch.tensor(self.buffer['log_probs'], device=self.device)
            
            # 标准化优势
            advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)
            
            # 计算批次数量
            n_samples = len(self.buffer['states'])
            n_batches = max(n_samples // batch_size, 1)
            
            # 多个epoch的更新
            for epoch in range(epoch_per_update):
                # 生成随机索引
                indices = np.random.permutation(n_samples)
                
                # 按批次训练
                for start_idx in range(0, n_samples, batch_size):
                    end_idx = min(start_idx + batch_size, n_samples)
                    batch_indices = indices[start_idx:end_idx]
                    
                    # 获取批次数据
                    batch_states = states[batch_indices]
                    batch_actions = actions[batch_indices]
                    batch_returns = returns[batch_indices]
                    batch_advantages = advantages[batch_indices]
                    batch_old_log_probs = old_log_probs[batch_indices]
                    
                    # 训练
                    policy_loss, value_loss, entropy = self.train_on_batch(
                        batch_states, batch_actions, batch_returns, 
                        batch_advantages, batch_old_log_probs
                    )
            
            # 记录训练结果
            self.train_rewards.append(episode_reward)
            self.train_lengths.append(episode_length)
            
            # 打印进度
            if (episode + 1) % 10 == 0:
                avg_reward = np.mean(self.train_rewards[-10:])
                logger.info(f"Episode {episode+1}/{n_episodes}, Avg Reward: {avg_reward:.2f}, Steps: {self.train_steps}")
            
            # 评估和保存模型
            if (episode + 1) % evaluate_interval == 0:
                eval_reward, _ = self.evaluate(n_episodes=5)
                
                # 保存最佳模型
                if eval_reward > self.best_reward:
                    self.best_reward = eval_reward
                    self.save_checkpoint(is_best=True)
                    logger.info(f"New best model with reward: {eval_reward:.2f}")
                    
                # 保存常规检查点
                if (episode + 1) % (evaluate_interval * 5) == 0:
                    self.save_checkpoint()
        
        # 训练结束后评估
        self.evaluate(n_episodes=10)
        
        # 保存最终模型
        self.save_checkpoint()
        
    def save_checkpoint(self, is_best: bool = False) -> str:
        """
        保存模型检查点
        
        参数:
            is_best: bool 是否为最佳模型
            
        返回:
            保存路径
        """
        # 创建检查点字典
        checkpoint = {
            'model_state_dict': self.network.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'train_rewards': self.train_rewards,
            'train_lengths': self.train_lengths,
            'eval_rewards': self.eval_rewards,
            'train_steps': self.train_steps,
            'best_reward': self.best_reward,
            'hyperparams': {
                'gamma': self.gamma,
                'clip_ratio': self.clip_ratio,
                'policy_lr': self.policy_lr,
                'value_lr': self.value_lr,
                'gae_lambda': self.gae_lambda,
                'entropy_coef': self.entropy_coef
            }
        }
        
        # 创建文件名
        if is_best:
            filename = os.path.join(self.checkpoint_dir, f"{self.model_name}_best.pth")
        else:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os.path.join(self.checkpoint_dir, f"{self.model_name}_{timestamp}.pth")
        
        # 保存检查点
        torch.save(checkpoint, filename)
        logger.info(f"Model saved to {filename}")
        
        return filename
        
    def load_checkpoint(self, checkpoint_path: str) -> None:
        """
        加载模型检查点
        
        参数:
            checkpoint_path: str 检查点路径
        """
        # 检查文件是否存在
        if not os.path.exists(checkpoint_path):
            logger.error(f"Checkpoint file not found: {checkpoint_path}")
            return
            
        # 加载检查点
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        
        # 加载模型参数
        self.network.load_state_dict(checkpoint['model_state_dict'])
        
        # 加载优化器参数
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        
        # 加载训练记录
        if 'train_rewards' in checkpoint:
            self.train_rewards = checkpoint['train_rewards']
        if 'train_lengths' in checkpoint:
            self.train_lengths = checkpoint['train_lengths']
        if 'eval_rewards' in checkpoint:
            self.eval_rewards = checkpoint['eval_rewards']
        if 'train_steps' in checkpoint:
            self.train_steps = checkpoint['train_steps']
        if 'best_reward' in checkpoint:
            self.best_reward = checkpoint['best_reward']
            
        logger.info(f"Model loaded from {checkpoint_path}")
        
        # 打印超参数
        if 'hyperparams' in checkpoint:
            params = checkpoint['hyperparams']
            logger.info(f"Loaded model hyperparameters: {params}")
            
            # 可选：更新当前实例的超参数
            if 'gamma' in params:
                self.gamma = params['gamma']
            if 'clip_ratio' in params:
                self.clip_ratio = params['clip_ratio']
            if 'policy_lr' in params:
                self.policy_lr = params['policy_lr']
            if 'value_lr' in params:
                self.value_lr = params['value_lr']
            if 'gae_lambda' in params:
                self.gae_lambda = params['gae_lambda']
            if 'entropy_coef' in params:
                self.entropy_coef = params['entropy_coef']