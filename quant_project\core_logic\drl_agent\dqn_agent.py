"""
DQN智能体模块
实现基于深度Q网络(DQN)算法的强化学习智能体
"""

import os
import logging
import numpy as np
from stable_baselines3 import DQN
import torch

from quant_project.core_logic.drl_agent.agent_base import DRLAgentBase
from quant_project.core_logic.utils import is_gpu_available

class DQNAgent(DRLAgentBase):
    """
    DQN智能体类
    基于深度Q网络(Deep Q-Network)算法
    """

    def __init__(self, env_config, agent_config, hpo_config=None):
        """
        初始化DQN智能体

        参数:
            env_config (dict): 交易环境配置
            agent_config (dict): 智能体配置
            hpo_config (dict, optional): 超参数优化配置
        """
        # 确保算法设置为DQN
        if agent_config is None:
            agent_config = {}
        agent_config['algorithm'] = 'DQN'
        
        super().__init__(env_config, agent_config, hpo_config)
        
        # 创建DQN模型
        self.model = self._create_model()
        
    def _create_model(self):
        """
        创建DQN模型

        返回:
            stable_baselines3.DQN: DQN模型实例
        """
        # 从智能体配置中提取参数
        policy = self.agent_config.get('policy_network', 'MlpPolicy')
        learning_rate = self.agent_config.get('learning_rate', 0.0001)
        gamma = self.agent_config.get('gamma', 0.99)
        
        # 检查是否有GPU可用
        device = 'cuda' if is_gpu_available() and self.agent_config.get('use_gpu', True) else 'cpu'
        self.logger.info(f"使用设备: {device}")
        
        # 创建DQN模型
        model = DQN(
            policy=policy,
            env=self.env,
            learning_rate=learning_rate,
            gamma=gamma,
            buffer_size=self.agent_config.get('buffer_size', 10000),
            learning_starts=self.agent_config.get('learning_starts', 1000),
            batch_size=self.agent_config.get('batch_size', 32),
            tau=self.agent_config.get('tau', 1.0),
            train_freq=self.agent_config.get('train_freq', 4),
            gradient_steps=self.agent_config.get('gradient_steps', 1),
            target_update_interval=self.agent_config.get('target_update_interval', 1000),
            exploration_fraction=self.agent_config.get('exploration_fraction', 0.1),
            exploration_initial_eps=self.agent_config.get('exploration_initial_eps', 1.0),
            exploration_final_eps=self.agent_config.get('exploration_final_eps', 0.05),
            max_grad_norm=self.agent_config.get('max_grad_norm', 10),
            verbose=1,
            device=device
        )
        
        return model
    
    @classmethod
    def load_model(cls, model_path, eval_env_config=None):
        """
        加载DQN模型

        参数:
            model_path (str): 模型路径
            eval_env_config (dict, optional): 评估环境配置

        返回:
            DQNAgent: 加载的智能体实例
        """
        # 检查模型文件是否存在
        if not os.path.exists(model_path):
            logging.error(f"模型文件不存在: {model_path}")
            return None
            
        try:
            # 创建评估环境配置
            if eval_env_config is None:
                eval_env_config = {}
                
            # 提取算法信息，默认为DQN
            algorithm = 'DQN'
            
            # 创建智能体配置
            agent_config = {
                'algorithm': algorithm,
                'use_gpu': is_gpu_available()
            }
            
            # 创建智能体实例
            agent = cls(eval_env_config, agent_config)
            
            # 加载模型
            agent.model = DQN.load(model_path, env=agent.env)
            logging.info(f"成功加载模型: {model_path}")
            
            return agent
            
        except Exception as e:
            logging.error(f"加载模型时出错: {str(e)}")
            return None
            
    def optimize_hyperparameters(self, n_trials=50, n_startup_trials=10, n_evaluations=2, n_timesteps=50000):
        """
        优化超参数

        参数:
            n_trials (int): 试验次数
            n_startup_trials (int): 启动试验次数
            n_evaluations (int): 每次试验的评估次数
            n_timesteps (int): 每次评估的时间步数

        返回:
            dict: 优化结果
        """
        try:
            import optuna
            from optuna.pruners import MedianPruner
            from optuna.samplers import TPESampler
        except ImportError:
            self.logger.error("无法导入optuna，请安装: pip install optuna")
            return {"status": "error", "message": "未安装optuna库"}
            
        self.logger.info(f"开始DQN超参数优化，试验次数: {n_trials}")
        
        # 创建optuna研究对象
        study = optuna.create_study(
            sampler=TPESampler(n_startup_trials=n_startup_trials),
            pruner=MedianPruner(n_startup_trials=n_startup_trials // 2),
            direction="maximize"
        )
        
        def objective(trial):
            # 采样超参数
            params = {
                'learning_rate': trial.suggest_float('learning_rate', 1e-5, 1e-3, log=True),
                'gamma': trial.suggest_float('gamma', 0.9, 0.9999),
                'batch_size': trial.suggest_int('batch_size', 16, 128, log=True),
                'buffer_size': trial.suggest_int('buffer_size', 1000, 100000, log=True),
                'learning_starts': trial.suggest_int('learning_starts', 100, 10000, log=True),
                'tau': trial.suggest_float('tau', 0.001, 1.0, log=True),
                'train_freq': trial.suggest_int('train_freq', 1, 10),
                'gradient_steps': trial.suggest_int('gradient_steps', 1, 10),
                'target_update_interval': trial.suggest_int('target_update_interval', 100, 10000, log=True),
                'exploration_fraction': trial.suggest_float('exploration_fraction', 0.01, 0.5),
                'exploration_final_eps': trial.suggest_float('exploration_final_eps', 0.01, 0.2),
                'max_grad_norm': trial.suggest_float('max_grad_norm', 0.1, 10.0, log=True)
            }
            
            # 更新智能体配置
            for key, value in params.items():
                self.agent_config[key] = value
                
            # 重新创建环境和模型
            self.env = self._create_environment()
            self.model = self._create_model()
            
            # 执行多次评估
            mean_rewards = []
            
            for _ in range(n_evaluations):
                # 训练模型
                self.model.learn(total_timesteps=n_timesteps)
                
                # 评估模型
                mean_reward, _ = self.model.evaluate(self.env, n_eval_episodes=10)
                mean_rewards.append(mean_reward)
                
                # 报告进度
                trial.report(mean_reward, _)
                
                # 如果需要修剪
                if trial.should_prune():
                    raise optuna.TrialPruned()
                    
            return np.mean(mean_rewards)
            
        # 开始超参数优化
        try:
            study.optimize(objective, n_trials=n_trials)
            
            # 获取最佳参数
            best_params = study.best_params
            best_value = study.best_value
            
            self.logger.info(f"超参数优化完成，最佳奖励: {best_value:.4f}")
            self.logger.info(f"最佳参数: {best_params}")
            
            # 使用最佳参数更新智能体配置
            for key, value in best_params.items():
                self.agent_config[key] = value
                
            # 重新创建环境和模型
            self.env = self._create_environment()
            self.model = self._create_model()
            
            return {
                "status": "success",
                "best_params": best_params,
                "best_value": best_value
            }
            
        except Exception as e:
            self.logger.error(f"超参数优化过程中出错: {str(e)}")
            return {"status": "error", "message": str(e)} 