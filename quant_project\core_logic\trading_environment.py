"""
交易环境模块
实现与Gymnasium API兼容的自定义交易环境
"""

import logging
import numpy as np
import pandas as pd
import gymnasium as gym
from gymnasium import spaces
import matplotlib.pyplot as plt

class TradingEnvironment(gym.Env):
    """
    交易环境类
    实现与Gymnasium API兼容的自定义交易环境，用于DRL智能体的训练和评估
    """

    metadata = {'render_modes': ['human']}

    def __init__(self,
                 df_processed_data,
                 initial_capital=100000,
                 commission_rate=0.0003,
                 min_hold_days=3,
                 allow_short=False,
                 max_position=1.0,
                 reward_config=None,
                 window_size=20,
                 render_mode=None):
        """
        初始化交易环境

        参数:
            df_processed_data (pandas.DataFrame): 处理好的行情数据和特征
            initial_capital (float): 初始资金
            commission_rate (float): 手续费率（单边）
            min_hold_days (int): 最小持仓天数
            allow_short (bool): 是否允许做空
            max_position (float): 最大仓位比例，范围[0, 1]
            reward_config (dict): 奖励函数配置
            window_size (int): 观测窗口大小
            render_mode (str): 渲染模式，可选 'human'
        """
        super(TradingEnvironment, self).__init__()

        self.logger = logging.getLogger('drl_trading')
        self.df = df_processed_data
        self.initial_capital = initial_capital
        self.commission_rate = commission_rate
        self.min_hold_days = min_hold_days
        self.allow_short = allow_short
        self.max_position = max_position
        self.window_size = window_size
        self.render_mode = render_mode

        # 奖励函数配置
        self.reward_config = reward_config or {
            'portfolio_return': 1.0,      # 组合收益权重
            'volatility_penalty': 0.1,    # 波动率惩罚权重
            'drawdown_penalty': 0.2,      # 回撤惩罚权重
            'holding_penalty': 0.05,      # 持仓成本惩罚权重
            'trade_penalty': 0.01         # 交易成本惩罚权重
        }

        # 设置动作空间
        # 0: 空仓/保持当前仓位, 1: 全仓买入/做多, 2: 平掉所有多头仓位
        self.action_space = spaces.Discrete(3)

        # 设置观测空间
        # 包括市场数据特征和账户状态
        # 假设特征数量为 n_features
        self.n_features = len(self.df.columns) - 5  # 减去OHLCV列
        self.logger.info(f"初始化环境，特征数量: {self.n_features}")

        # 观测空间包括:
        # 1. 窗口大小的特征数据 (window_size * n_features)
        # 2. 账户状态 (现金比例, 持仓市值比例, 持仓天数, 未实现盈亏比例)
        obs_dim = self.window_size * self.n_features + 4

        # 保存原始观测空间维度，用于后续检查
        self.original_obs_dim = obs_dim
        self.logger.info(f"观测空间维度: {obs_dim}")

        self.observation_space = spaces.Box(
            low=-10.0,  # 使用一个足够大的负数代替-np.inf
            high=10.0,  # 使用一个足够大的正数代替np.inf
            shape=(obs_dim,),
            dtype=np.float32
        )

        # 重置环境
        self.reset()

    def reset(self, seed=None, options=None):
        """
        重置环境到初始状态

        参数:
            seed (int): 随机种子
            options (dict): 重置选项

        返回:
            tuple: (observation, info)
        """
        # 调用父类的reset方法设置随机种子
        super().reset(seed=seed)

        # 检查特征数量是否发生变化，如果变化则更新观测空间
        feature_columns = [col for col in self.df.columns if col not in ['开盘', '最高', '最低', '收盘', '成交量']]
        current_n_features = len(feature_columns)

        if current_n_features != self.n_features:
            self.logger.warning(f"特征数量从 {self.n_features} 变为 {current_n_features}，更新观测空间")
            self._update_observation_space(current_n_features)

        # 设置当前时间步
        self.current_step = self.window_size

        # 初始化账户状态
        self.cash = self.initial_capital
        self.shares_held = 0
        self.current_price = self.df['收盘'].iloc[self.current_step]
        self.cost_basis = 0
        self.total_commission = 0
        self.holding_days = 0

        # 交易记录
        self.trades = []

        # 账户价值历史
        self.portfolio_values = [self.initial_capital]

        # 奖励历史
        self.rewards = []

        # 获取初始观测
        observation = self._get_observation()

        # 信息字典
        info = {
            'step': self.current_step,
            'portfolio_value': self._calculate_portfolio_value(),
            'cash': self.cash,
            'shares_held': self.shares_held,
            'current_price': self.current_price,
            'holding_days': self.holding_days
        }

        return observation, info

    def _update_observation_space(self, new_n_features):
        """
        更新观测空间以适应新的特征数量

        参数:
            new_n_features (int): 新的特征数量
        """
        # 保存原始特征数量
        old_n_features = self.n_features

        # 更新特征数量
        self.n_features = new_n_features

        # 计算新的观测空间维度
        new_obs_dim = self.window_size * self.n_features + 4

        # 记录变化
        self.logger.info(f"观测空间维度从 {self.original_obs_dim} 变为 {new_obs_dim}")

        # 更新观测空间
        self.observation_space = spaces.Box(
            low=-10.0,
            high=10.0,
            shape=(new_obs_dim,),
            dtype=np.float32
        )

        # 更新原始观测空间维度
        self.original_obs_dim = new_obs_dim

    def force_observation_space(self, target_dim):
        """
        强制设置观测空间维度为指定值

        参数:
            target_dim (int): 目标观测空间维度
        """
        # 记录变化
        self.logger.warning(f"强制设置观测空间维度: {self.original_obs_dim} -> {target_dim}")

        # 计算所需的特征数量
        required_features = (target_dim - 4) // self.window_size

        # 更新特征数量
        self.n_features = required_features

        # 更新观测空间
        self.observation_space = spaces.Box(
            low=-10.0,
            high=10.0,
            shape=(target_dim,),
            dtype=np.float32
        )

        # 更新原始观测空间维度
        self.original_obs_dim = target_dim

        self.logger.info(f"观测空间已强制设置为 {target_dim}，对应特征数量: {required_features}")

    def adjust_observation_space(self, target_dim):
        """
        调整观测空间维度以匹配目标维度

        这个方法用于解决模型加载时观测空间维度不匹配的问题。
        它会调整环境的观测空间维度，使其与加载的模型匹配。

        参数:
            target_dim (int): 目标观测空间维度
        """
        # 记录当前维度和目标维度
        current_dim = self.observation_space.shape[0]
        self.logger.warning(f"调整观测空间维度: {current_dim} -> {target_dim}")

        if current_dim == target_dim:
            self.logger.info("观测空间维度已经匹配，无需调整")
            return

        # 使用force_observation_space方法强制设置观测空间维度
        self.force_observation_space(target_dim)

        # 记录调整后的信息
        self.logger.info(f"观测空间已调整为 {target_dim} 维度")
        self.logger.info(f"特征数量: {self.n_features}, 窗口大小: {self.window_size}, 账户状态: 4")

    def step(self, action):
        """
        执行一个动作并更新环境

        参数:
            action (int): 动作，0=保持, 1=买入, 2=卖出

        返回:
            tuple: (observation, reward, terminated, truncated, info)
        """
        # 保存上一步的组合价值用于计算奖励
        prev_portfolio_value = self._calculate_portfolio_value()

        # 获取当前价格
        self.current_price = self.df['收盘'].iloc[self.current_step]

        # 执行交易动作
        self._execute_trade_action(action)

        # 更新持仓天数
        if self.shares_held > 0:
            self.holding_days += 1
        else:
            self.holding_days = 0

        # 计算当前组合价值
        current_portfolio_value = self._calculate_portfolio_value()

        # 记录组合价值历史
        self.portfolio_values.append(current_portfolio_value)

        # 计算奖励
        reward = self._calculate_reward(prev_portfolio_value, current_portfolio_value)
        self.rewards.append(reward)

        # 移动到下一个时间步
        self.current_step += 1

        # 检查是否结束
        terminated = self.current_step >= len(self.df) - 1
        truncated = False  # 在金融环境中通常不使用truncated

        # 获取新的观测
        observation = self._get_observation()

        # 信息字典
        info = {
            'step': self.current_step,
            'portfolio_value': current_portfolio_value,
            'cash': self.cash,
            'shares_held': self.shares_held,
            'current_price': self.current_price,
            'holding_days': self.holding_days,
            'total_commission': self.total_commission,
            'return': (current_portfolio_value / prev_portfolio_value) - 1
        }

        return observation, reward, terminated, truncated, info

    def render(self, mode='human'):
        """
        渲染环境状态

        参数:
            mode (str): 渲染模式
        """
        if mode != 'human':
            raise ValueError(f"不支持的渲染模式: {mode}")

        # 创建图表
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8), sharex=True)

        # 绘制价格和交易点
        ax1.set_title('价格和交易点')
        ax1.set_ylabel('价格')

        # 获取可见的数据范围
        start_idx = max(0, self.current_step - 100)
        end_idx = self.current_step + 1

        # 绘制价格
        ax1.plot(self.df.index[start_idx:end_idx], self.df['收盘'].iloc[start_idx:end_idx], label='收盘价')

        # 标记交易点
        for trade in self.trades:
            if start_idx <= trade['step'] <= end_idx:
                if trade['action'] == 'buy':
                    ax1.scatter(self.df.index[trade['step']], trade['price'], color='green', marker='^', s=100)
                elif trade['action'] == 'sell':
                    ax1.scatter(self.df.index[trade['step']], trade['price'], color='red', marker='v', s=100)

        # 绘制组合价值
        ax2.set_title('组合价值')
        ax2.set_ylabel('价值')
        ax2.set_xlabel('日期')
        ax2.plot(self.df.index[self.window_size:end_idx], self.portfolio_values, label='组合价值')

        # 添加图例
        ax1.legend()
        ax2.legend()

        # 显示图表
        plt.tight_layout()
        plt.show()

    def _get_observation(self):
        """
        获取当前观测

        返回:
            numpy.ndarray: 观测向量
        """
        # 获取窗口内的特征数据
        start_idx = self.current_step - self.window_size + 1
        end_idx = self.current_step + 1

        # 提取特征列（排除OHLCV列）
        feature_columns = [col for col in self.df.columns if col not in ['开盘', '最高', '最低', '收盘', '成交量']]

        # 检查特征数量是否与初始化时一致
        if len(feature_columns) != self.n_features:
            self.logger.warning(f"特征列数量 ({len(feature_columns)}) 与初始化时 ({self.n_features}) 不一致，将使用初始化时的特征数量")
            # 如果特征数量不一致，可能是因为数据结构变化，我们需要确保观测空间维度一致
            if len(feature_columns) > self.n_features:
                # 如果当前特征更多，只使用前n_features个
                feature_columns = feature_columns[:self.n_features]
            else:
                # 如果当前特征更少，用0填充缺失的特征
                padding_size = self.n_features - len(feature_columns)
                self.logger.warning(f"特征数量不足，将添加 {padding_size} 个零填充特征")

        # 获取特征窗口
        try:
            # 尝试获取特征窗口
            features_window = self.df[feature_columns].iloc[start_idx:end_idx].values

            # 检查特征窗口的形状
            if features_window.shape[0] != self.window_size:
                self.logger.warning(f"特征窗口大小 ({features_window.shape[0]}) 与预期 ({self.window_size}) 不一致，将调整")
                # 如果窗口大小不一致，调整为正确的大小
                if features_window.shape[0] < self.window_size:
                    # 如果窗口太小，用0填充
                    padding = np.zeros((self.window_size - features_window.shape[0], features_window.shape[1]))
                    features_window = np.vstack([padding, features_window])
                else:
                    # 如果窗口太大，截取最近的window_size个
                    features_window = features_window[-self.window_size:]

            # 展平特征窗口
            features_window = features_window.flatten()

            # 检查特征窗口的维度
            expected_dim = self.window_size * len(feature_columns)
            if len(features_window) != expected_dim:
                self.logger.warning(f"特征窗口维度 ({len(features_window)}) 与预期 ({expected_dim}) 不一致，将调整")
                # 如果维度不一致，调整为正确的维度
                if len(features_window) < expected_dim:
                    # 如果维度太小，用0填充
                    features_window = np.pad(features_window, (0, expected_dim - len(features_window)))
                else:
                    # 如果维度太大，截取前expected_dim个
                    features_window = features_window[:expected_dim]
        except Exception as e:
            self.logger.error(f"获取特征窗口时出错: {str(e)}")
            # 如果出错，创建一个全零的特征窗口
            features_window = np.zeros(self.window_size * self.n_features)

        # 计算账户状态特征
        portfolio_value = self._calculate_portfolio_value()
        cash_ratio = self.cash / portfolio_value
        position_ratio = (self.shares_held * self.current_price) / portfolio_value
        unrealized_pnl_ratio = 0
        if self.shares_held > 0 and self.cost_basis > 0:
            unrealized_pnl_ratio = ((self.current_price - self.cost_basis) / self.cost_basis)

        # 归一化持仓天数
        normalized_holding_days = self.holding_days / 30  # 假设最长持仓30天

        # 组合观测向量
        account_state = np.array([
            cash_ratio,
            position_ratio,
            normalized_holding_days,
            unrealized_pnl_ratio
        ], dtype=np.float32)

        # 确保特征窗口和账户状态的数据类型一致
        features_window = features_window.astype(np.float32)

        # 组合观测向量
        observation = np.concatenate([features_window, account_state])

        # 确保观测值在观测空间的范围内
        observation = np.clip(observation, -10.0, 10.0)

        # 确保观测向量的维度与观测空间一致
        expected_obs_dim = self.observation_space.shape[0]

        # 检查观测向量维度是否与观测空间一致
        if len(observation) != expected_obs_dim:
            # 记录详细的维度信息，帮助调试
            self.logger.warning(
                f"观测向量维度 ({len(observation)}) 与观测空间 ({expected_obs_dim}) 不一致，将调整\n"
                f"特征窗口维度: {len(features_window)}, 账户状态维度: {len(account_state)}\n"
                f"特征列数量: {len(feature_columns)}, 窗口大小: {self.window_size}\n"
                f"原始观测空间维度: {self.original_obs_dim}"
            )

            # 如果维度不一致，尝试调整为正确的维度
            if len(observation) < expected_obs_dim:
                # 如果维度太小，用0填充
                padding_size = expected_obs_dim - len(observation)
                self.logger.info(f"观测向量维度太小，添加 {padding_size} 个零填充")
                observation = np.pad(observation, (0, padding_size))
            else:
                # 如果维度太大，截取前expected_obs_dim个
                self.logger.info(f"观测向量维度太大，截取前 {expected_obs_dim} 个元素")
                observation = observation[:expected_obs_dim]

            # 再次检查维度是否正确
            if len(observation) != expected_obs_dim:
                self.logger.error(f"调整后观测向量维度 ({len(observation)}) 仍与观测空间 ({expected_obs_dim}) 不一致")
                # 如果仍然不一致，创建一个全零的观测向量
                observation = np.zeros(expected_obs_dim, dtype=np.float32)

        return observation.astype(np.float32)

    def _execute_trade_action(self, action):
        """
        执行交易动作

        参数:
            action (int): 动作，0=保持, 1=买入, 2=卖出
        """
        if action == 0:  # 保持当前仓位
            return

        elif action == 1:  # 买入
            # 如果已经持有股票，不执行任何操作
            if self.shares_held > 0:
                return

            # 计算可以购买的最大股数（考虑手续费）
            max_shares = int(self.cash / (self.current_price * (1 + self.commission_rate)))

            # 限制最大仓位
            target_shares = int(self.max_position * self.initial_capital / self.current_price)
            shares_to_buy = min(max_shares, target_shares)

            # 如果可以买入至少1股
            if shares_to_buy > 0:
                # 计算交易成本
                cost = shares_to_buy * self.current_price
                commission = cost * self.commission_rate
                total_cost = cost + commission

                # 执行交易
                self.cash -= total_cost
                self.shares_held += shares_to_buy
                self.cost_basis = self.current_price
                self.total_commission += commission
                self.holding_days = 1  # 重置持仓天数

                # 记录交易
                self.trades.append({
                    'step': self.current_step,
                    'date': self.df.index[self.current_step],
                    'action': 'buy',
                    'price': self.current_price,
                    'shares': shares_to_buy,
                    'cost': cost,
                    'commission': commission
                })

                self.logger.info(f"买入 {shares_to_buy} 股，价格: {self.current_price:.2f}，成本: {total_cost:.2f}")

        elif action == 2:  # 卖出
            # 如果没有持有股票，不执行任何操作
            if self.shares_held <= 0:
                return

            # 检查最小持仓天数
            if self.holding_days < self.min_hold_days:
                self.logger.info(f"尝试卖出但未满足最小持仓天数 ({self.holding_days}/{self.min_hold_days})，操作被拒绝")
                return

            # 计算交易收益
            revenue = self.shares_held * self.current_price
            commission = revenue * self.commission_rate
            total_revenue = revenue - commission

            # 执行交易
            self.cash += total_revenue
            self.shares_held = 0
            self.total_commission += commission

            # 记录交易
            self.trades.append({
                'step': self.current_step,
                'date': self.df.index[self.current_step],
                'action': 'sell',
                'price': self.current_price,
                'shares': self.shares_held,
                'revenue': revenue,
                'commission': commission
            })

            self.logger.info(f"卖出所有股票，价格: {self.current_price:.2f}，收入: {total_revenue:.2f}")

    def _calculate_portfolio_value(self):
        """
        计算当前组合价值

        返回:
            float: 组合价值
        """
        return self.cash + (self.shares_held * self.current_price)

    def _calculate_reward(self, prev_portfolio_value, current_portfolio_value):
        """
        计算奖励

        参数:
            prev_portfolio_value (float): 上一步的组合价值
            current_portfolio_value (float): 当前组合价值

        返回:
            float: 奖励值
        """
        # 计算组合收益率
        portfolio_return = (current_portfolio_value / prev_portfolio_value) - 1

        # 基础奖励：组合收益
        reward = portfolio_return * self.reward_config['portfolio_return']

        # 波动率惩罚
        if len(self.portfolio_values) > 10:
            # 确保数组长度匹配
            values = np.array(self.portfolio_values[-11:])
            returns = (values[1:] - values[:-1]) / values[:-1]
            volatility = np.std(returns)
            reward -= volatility * self.reward_config['volatility_penalty']

        # 回撤惩罚
        if len(self.portfolio_values) > 1:
            peak = max(self.portfolio_values)
            drawdown = (peak - current_portfolio_value) / peak
            if drawdown > 0:
                reward -= drawdown * self.reward_config['drawdown_penalty']

        # 持仓成本惩罚（鼓励有效利用资金）
        cash_ratio = self.cash / current_portfolio_value
        if cash_ratio > 0.5:  # 如果超过50%的资金闲置
            reward -= (cash_ratio - 0.5) * self.reward_config['holding_penalty']

        # 交易成本惩罚
        if self.trades and self.trades[-1]['step'] == self.current_step:
            reward -= self.reward_config['trade_penalty']

        return reward

    def get_trades_history(self):
        """
        获取交易历史

        返回:
            list: 交易记录列表
        """
        return self.trades

    def get_portfolio_history(self):
        """
        获取组合价值历史

        返回:
            pandas.Series: 组合价值序列
        """
        return pd.Series(
            self.portfolio_values,
            index=self.df.index[self.window_size:self.window_size + len(self.portfolio_values)]
        )
