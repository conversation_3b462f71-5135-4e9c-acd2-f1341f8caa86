#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试 Streamlit 应用程序
"""

import os
import sys
import streamlit as st

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
st.write(f"Current directory: {current_dir}")
st.write(f"Python path: {sys.path}")

# 检查文件是否存在
data_handler_path = os.path.join(current_dir, "core_logic", "data_handler.py")
st.write(f"data_handler.py 文件是否存在: {os.path.exists(data_handler_path)}")

# 列出 core_logic 目录中的文件
core_logic_dir = os.path.join(current_dir, "core_logic")
if os.path.exists(core_logic_dir):
    st.write("core_logic 目录中的文件:")
    for file in os.listdir(core_logic_dir):
        st.write(f"  - {file}")
else:
    st.write("core_logic 目录不存在")

# 尝试导入模块
try:
    from core_logic.data_handler import DataHandler
    st.success("成功导入 DataHandler 模块")
except ImportError as e:
    st.error(f"导入失败: {str(e)}")

# 显示简单的UI
st.title("测试 Streamlit 应用程序")
st.header("这是一个测试应用程序")
st.write("如果你能看到这个页面，说明 Streamlit 正常工作。")
