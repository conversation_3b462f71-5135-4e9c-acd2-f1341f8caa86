import os
import numpy as np
import pandas as pd
import torch
import matplotlib.pyplot as plt
from datetime import datetime

# Import custom modules
from data_processors import DataProcessor, calculate_technical_indicators
from model_utils import ModelTrainer, create_mlp, prepare_data_for_training
from backtest_utils import BacktestEngine, sma_crossover_strategy, bollinger_band_strategy
from trading_env import TradingEnvironment, ActionNormalizationWrapper, StateNormalizationWrapper, MultiObjectiveRewardWrapper
from drl_agent import PPOAgent, PPOTrainer, calculate_state_dim, calculate_action_dim, flatten_dict_observation
from auto_trainer import AutoTrainer, configure_email_notifications

# Set random seeds for reproducibility
np.random.seed(42)
torch.manual_seed(42)
if torch.cuda.is_available():
    torch.cuda.manual_seed(42)

# Create directories for outputs
os.makedirs('data', exist_ok=True)
os.makedirs('models', exist_ok=True)
os.makedirs('results', exist_ok=True)
os.makedirs('logs', exist_ok=True)

def load_sample_data():
    """
    Load sample financial data for demonstration.
    If no data file exists, create synthetic data.
    """
    data_file = 'data/sample_data.csv'
    
    if os.path.exists(data_file):
        print(f"Loading data from {data_file}")
        return pd.read_csv(data_file, parse_dates=['date'])
    
    print("Creating synthetic data for demonstration")
    # Create date range
    date_range = pd.date_range(start='2020-01-01', end='2022-12-31', freq='B')
    
    # Initialize random walk for price
    np.random.seed(42)  # For reproducibility
    price = 100.0
    prices = [price]
    
    # Generate prices using random walk with drift
    for _ in range(1, len(date_range)):
        # Random price change with drift
        change_percent = np.random.normal(0.0003, 0.012)  # Mean slightly positive
        price *= (1 + change_percent)
        prices.append(price)
    
    # Create DataFrame
    df = pd.DataFrame({
        'date': date_range,
        'open': prices,
        'high': prices,
        'low': prices,
        'close': prices,
        'volume': np.random.randint(100000, 10000000, size=len(date_range))
    })
    
    # Adjust open, high, low based on close
    for i in range(len(df)):
        daily_volatility = df['close'][i] * 0.015  # 1.5% volatility
        df.loc[i, 'open'] = df['close'][i] * (1 + np.random.normal(0, 0.005))
        df.loc[i, 'high'] = max(df['open'][i], df['close'][i]) + abs(np.random.normal(0, daily_volatility))
        df.loc[i, 'low'] = min(df['open'][i], df['close'][i]) - abs(np.random.normal(0, daily_volatility))
    
    # Save to file
    df.to_csv(data_file, index=False)
    print(f"Synthetic data saved to {data_file}")
    
    return df

def demonstrate_data_processing():
    """
    Demonstrate data processing capabilities.
    """
    print("\n" + "="*50)
    print("DATA PROCESSING DEMONSTRATION")
    print("="*50)
    
    # Load sample data
    data = load_sample_data()
    print(f"Loaded data with shape: {data.shape}")
    
    # Initialize data processor
    processor = DataProcessor()
    
    # Process data
    print("Applying data processing steps...")
    
    # Add technical indicators
    data = calculate_technical_indicators(data)
    print(f"Added technical indicators. New shape: {data.shape}")
    
    # Handle outliers
    data = processor.remove_outliers(data, method='zscore')
    print("Removed outliers using z-score method")
    
    # Generate time features
    data = processor.generate_time_features(data, date_column='date')
    print("Generated time-based features")
    
    # Calculate returns
    data = processor.calculate_returns(data, price_column='close')
    print("Calculated returns at different horizons")
    
    # Normalize features
    features_to_normalize = ['rsi_14', 'macd', 'ema_20', 'sma_20', 'return_5d']
    data = processor.normalize_features(data, columns=features_to_normalize)
    print(f"Normalized features: {features_to_normalize}")
    
    # Impute missing values
    data = processor.impute_missing_values(data, method='ffill')
    print("Imputed missing values using forward fill")
    
    # Show summary of processed data
    print("\nProcessed data summary:")
    print(f"Shape: {data.shape}")
    print(f"Columns: {data.columns.tolist()[:10]}... (and {len(data.columns)-10} more)")
    print(f"Date range: {data['date'].min()} to {data['date'].max()}")
    print(f"Missing values: {data.isna().sum().sum()}")
    
    # Save processed data
    processed_file = 'data/processed_data.csv'
    data.to_csv(processed_file, index=False)
    print(f"Processed data saved to {processed_file}")
    
    return data

def demonstrate_model_training(processed_data):
    """
    Demonstrate model training capabilities.
    """
    print("\n" + "="*50)
    print("MODEL TRAINING DEMONSTRATION")
    print("="*50)
    
    # Prepare features and target
    print("Preparing features and target...")
    
    # Select features
    features = [
        'open', 'high', 'low', 'close', 'volume',
        'rsi_14', 'macd', 'macd_signal', 'macd_hist',
        'bb_upper', 'bb_middle', 'bb_lower',
        'sma_5', 'sma_10', 'sma_20', 'ema_5', 'ema_10', 'ema_20',
        'return_1d', 'return_5d'
    ]
    
    # Remove rows with NaN (from indicators calculation)
    data = processed_data.dropna(subset=features)
    
    # Target: next day return
    target = data['return_1d'].shift(-1)
    data = data[:-1]  # Remove last row (no target)
    target = target[:-1]
    
    # Prepare data loaders
    print("Creating train/validation/test splits...")
    train_loader, val_loader, test_loader = prepare_data_for_training(
        data[features], target, batch_size=64
    )
    
    # Create model
    input_dim = len(features)
    model = create_mlp(
        input_dim=input_dim,
        output_dim=1,
        hidden_dims=[128, 64, 32],
        batch_norm=True
    )
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Initialize trainer
    trainer = ModelTrainer(
        model=model,
        device=device,
        config={'learning_rate': 0.001}
    )
    
    # Train model
    print("Training model...")
    epochs = 20
    history = trainer.fit(
        train_loader=train_loader,
        val_loader=val_loader,
        epochs=epochs,
        verbose=True
    )
    
    # Plot learning curves
    trainer.plot_learning_curves()
    
    # Evaluate model
    print("\nEvaluating model on test data...")
    test_metrics = trainer.evaluate(test_loader)
    
    # Save model
    model_path = 'models/return_prediction_model.pt'
    trainer.save_checkpoint(model_path)
    print(f"Model saved to {model_path}")
    
    return model, history, test_metrics

def demonstrate_backtesting(processed_data, model):
    """
    Demonstrate backtesting capabilities.
    """
    print("\n" + "="*50)
    print("BACKTESTING DEMONSTRATION")
    print("="*50)
    
    # Prepare data for backtesting
    backtest_data = processed_data.set_index('date')
    
    # Initialize backtest engine
    engine = BacktestEngine(
        price_data=backtest_data,
        price_column='close',
        initial_capital=100000.0,
        commission_rate=0.001
    )
    
    # Run backtest with SMA crossover strategy
    print("Running backtest with SMA crossover strategy...")
    sma_params = {
        'fast_period': 20,
        'slow_period': 50,
        'price_column': 'close'
    }
    sma_results = engine.run_backtest(
        strategy=sma_crossover_strategy,
        strategy_params=sma_params
    )
    
    # Calculate performance metrics
    sma_metrics = engine.calculate_performance_metrics()
    
    # Print performance summary
    print("\nSMA Crossover Strategy Performance:")
    engine.print_performance_summary()
    
    # Plot performance
    engine.plot_performance()
    
    # Run backtest with Bollinger Band strategy
    print("\nRunning backtest with Bollinger Band strategy...")
    bb_params = {
        'period': 20,
        'std_dev': 2.0,
        'price_column': 'close'
    }
    bb_results = engine.run_backtest(
        strategy=bollinger_band_strategy,
        strategy_params=bb_params
    )
    
    # Calculate performance metrics
    bb_metrics = engine.calculate_performance_metrics()
    
    # Print performance summary
    print("\nBollinger Band Strategy Performance:")
    engine.print_performance_summary()
    
    # Plot performance
    engine.plot_performance()
    
    # Compare strategies
    print("\nStrategy Comparison:")
    print(f"SMA Crossover - Total Return: {sma_metrics['total_return']*100:.2f}%, Sharpe: {sma_metrics['sharpe_ratio']:.2f}")
    print(f"Bollinger Band - Total Return: {bb_metrics['total_return']*100:.2f}%, Sharpe: {bb_metrics['sharpe_ratio']:.2f}")
    
    return {
        'sma_results': sma_results,
        'sma_metrics': sma_metrics,
        'bb_results': bb_results,
        'bb_metrics': bb_metrics
    }

def demonstrate_reinforcement_learning(processed_data):
    """
    Demonstrate reinforcement learning for trading.
    """
    print("\n" + "="*50)
    print("REINFORCEMENT LEARNING DEMONSTRATION")
    print("="*50)
    
    # Prepare data for RL
    print("Preparing data for reinforcement learning...")
    
    # Select features for state representation
    features = [
        'rsi_14', 'macd', 'macd_signal',
        'bb_upper', 'bb_middle', 'bb_lower',
        'ema_5', 'ema_20', 'ema_50',
        'return_1d', 'return_5d'
    ]
    
    # Remove rows with NaN
    rl_data = processed_data.dropna(subset=features + ['close'])
    
    # Create trading environment
    print("Creating trading environment...")
    env = TradingEnvironment(
        df=rl_data,
        features=features,
        initial_balance=100000.0,
        commission_percent=0.1,
        reward_function='returns',
        window_size=20,
        max_shares=1000
    )
    
    # Apply wrappers
    env = ActionNormalizationWrapper(env)
    env = StateNormalizationWrapper(env)
    
    # Calculate state and action dimensions
    state_dim = calculate_state_dim(env)
    action_dim = calculate_action_dim(env)
    print(f"State dimension: {state_dim}")
    print(f"Action dimension: {action_dim}")
    
    # Create PPO agent
    print("Creating PPO agent...")
    agent = PPOAgent(
        state_dim=state_dim,
        action_dim=action_dim,
        hidden_dims=[128, 64],
        lr_policy=3e-4,
        lr_value=1e-3,
        gamma=0.99,
        clip_ratio=0.2
    )
    
    # Create PPO trainer
    trainer = PPOTrainer(
        env=env,
        agent=agent,
        num_steps=1000,
        max_episodes=10,  # Small number for demonstration
        checkpoint_dir='models/rl_checkpoints',
        verbose=True
    )
    
    # Train agent (for a small number of episodes for demonstration)
    print("Training PPO agent (demonstration with few episodes)...")
    training_stats = trainer.train()
    
    # Evaluate agent
    print("\nEvaluating trained agent...")
    eval_stats = trainer.evaluate(num_episodes=3, render=True)
    
    # Save final plots
    plt.figure(figsize=(12, 6))
    plt.plot(training_stats['episode_rewards'])
    plt.title('Episode Rewards During Training')
    plt.xlabel('Episode')
    plt.ylabel('Total Reward')
    plt.grid(True)
    plt.savefig('results/rl_training_rewards.png')
    
    return {
        'env': env,
        'agent': agent,
        'training_stats': training_stats,
        'eval_stats': eval_stats
    }

def demonstrate_auto_training(processed_data, model):
    """
    Demonstrate automatic training capabilities.
    """
    print("\n" + "="*50)
    print("AUTOMATIC TRAINING DEMONSTRATION")
    print("="*50)
    
    # Define model training function
    def train_model():
        # Prepare features and target
        features = [
            'open', 'high', 'low', 'close', 'volume',
            'rsi_14', 'macd', 'macd_signal', 'macd_hist',
            'sma_5', 'sma_20', 'ema_5', 'ema_20',
            'return_1d', 'return_5d'
        ]
        
        # Use only part of the data for quick demonstration
        data = processed_data.dropna(subset=features).iloc[:500]
        
        # Target: next day return
        target = data['return_1d'].shift(-1)
        data = data[:-1]
        target = target[:-1]
        
        # Prepare data loaders
        train_loader, val_loader, test_loader = prepare_data_for_training(
            data[features], target, batch_size=64
        )
        
        # Initialize model
        input_dim = len(features)
        model = create_mlp(input_dim=input_dim, output_dim=1, hidden_dims=[64, 32])
        
        # Initialize trainer
        trainer = ModelTrainer(model=model)
        
        # Train for a short time for demonstration
        trainer.fit(train_loader, val_loader, epochs=5, verbose=True)
        
        return trainer
    
    # Set up email configuration (dummy for demonstration)
    email_config = configure_email_notifications(
        smtp_server='smtp.example.com',
        smtp_port=587,
        username='your_username',
        password='your_password',
        sender='<EMAIL>',
        recipient='<EMAIL>'
    )
    
    # Initialize auto trainer
    auto_trainer = AutoTrainer(
        model_trainer=train_model(),
        data_loader=lambda: processed_data,
        checkpoint_dir='models/auto_train',
        log_dir='logs/auto_train',
        email_config=None,  # Set to email_config to enable notifications
        recovery_mode=True,
        resource_monitoring=True,
        max_training_time=60  # 60 seconds max for demonstration
    )
    
    # Run training for a short time for demonstration
    print("Running automated training (demonstration mode with short duration)...")
    try:
        training_stats = auto_trainer.train_with_recovery()
        print(f"Training completed in {training_stats['duration']:.2f} seconds")
    except Exception as e:
        print(f"Error during demonstration: {str(e)}")
    
    print("\nAuto-training features demonstrated. In a real scenario, you would:")
    print("1. Configure email notifications for completion/errors")
    print("2. Set longer training durations")
    print("3. Use more data and more complex models")
    print("4. Schedule training at specific times or recurring intervals")
    
def main():
    """
    Main function to demonstrate all capabilities.
    """
    print("QUANTITATIVE TRADING PROJECT DEMONSTRATION")
    print("="*50)
    
    # Data processing
    processed_data = demonstrate_data_processing()
    
    # Model training
    model, history, test_metrics = demonstrate_model_training(processed_data)
    
    # Backtesting
    backtest_results = demonstrate_backtesting(processed_data, model)
    
    # Reinforcement learning
    rl_results = demonstrate_reinforcement_learning(processed_data)
    
    # Automatic training
    demonstrate_auto_training(processed_data, model)
    
    print("\n" + "="*50)
    print("DEMONSTRATION COMPLETED")
    print("="*50)
    print("All modules have been demonstrated:")
    print("1. Data processing (data_processors.py)")
    print("2. Model training (model_utils.py)")
    print("3. Backtesting (backtest_utils.py)")
    print("4. Reinforcement learning (trading_env.py, drl_agent.py)")
    print("5. Automatic training (auto_trainer.py)")
    print("\nResults are saved in the following directories:")
    print("- data/: Processed data files")
    print("- models/: Trained models")
    print("- results/: Plots and metrics")
    print("- logs/: Training logs")

if __name__ == "__main__":
    main() 