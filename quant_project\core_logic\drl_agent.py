"""
DRL智能体模块 (导入文件)
此文件现在从重构后的模块中导入相应的类，以保持向后兼容性
"""

# 导入重构后的类
from quant_project.core_logic.drl_agent.agent_base import DRLAgentBase
from quant_project.core_logic.drl_agent.ppo_agent import PPOAgent
from quant_project.core_logic.drl_agent.a2c_agent import A2CAgent
from quant_project.core_logic.drl_agent.dqn_agent import DQNAgent
from quant_project.core_logic.drl_agent.dummy_agent import DummyAgent
from quant_project.core_logic.drl_agent.callbacks import (
    MetricsCallback, 
    BestModelCallback,
    RobustMetricsCallback
)

# 直接从__init__.py中导入DRLAgent类
from quant_project.core_logic.drl_agent import DRLAgent

# 向后兼容导出
__all__ = [
    'DRLAgent',           # 原始类名保持不变
    'DRLAgentBase',       # 新的基类
    'PPOAgent',           # PPO特定实现
    'A2CAgent',           # A2C特定实现
    'DQNAgent',           # DQN特定实现
    'DummyAgent',         # 空智能体实现
    'MetricsCallback',    # 指标回调
    'BestModelCallback',  # 最佳模型回调
    'RobustMetricsCallback'  # 鲁棒指标回调
]
