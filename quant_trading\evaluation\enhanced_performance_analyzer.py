"""
增强性能分析模块
实现高级回测和性能评估，包括多种风险指标、绩效归因和统计分析
符合顶尖量化基金的最佳实践
"""

import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import empyrical as ep
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score
import statsmodels.api as sm
from statsmodels.tsa.stattools import adfuller
from statsmodels.stats.diagnostic import acorr_ljungbox
# from arch import arch_model  # 暂时注释掉，需要安装arch包

class EnhancedPerformanceAnalyzer:
    """
    增强性能分析类
    实现高级回测和性能评估，包括多种风险指标、绩效归因和统计分析
    """

    def __init__(self, benchmark_data=None):
        """
        初始化性能分析器

        参数:
            benchmark_data (pandas.DataFrame, optional): 基准数据，必须包含日期索引和收盘价列
        """
        self.logger = logging.getLogger('drl_trading')
        self.benchmark_data = benchmark_data
        self.benchmark_returns = None

        # 如果提供了基准数据，计算基准收益率
        if benchmark_data is not None and '收盘' in benchmark_data.columns:
            self.benchmark_returns = benchmark_data['收盘'].pct_change().dropna()

    def analyze_performance(self, portfolio_history, risk_free_rate=0.0):
        """
        分析投资组合性能

        参数:
            portfolio_history (pandas.DataFrame): 投资组合历史数据，必须包含日期索引和portfolio_value列
            risk_free_rate (float): 无风险利率，年化

        返回:
            dict: 性能指标
        """
        # 确保投资组合历史数据包含必要的列
        if 'portfolio_value' not in portfolio_history.columns:
            self.logger.error("投资组合历史数据缺少portfolio_value列")
            raise ValueError("投资组合历史数据缺少portfolio_value列")

        # 计算每日收益率
        portfolio_history['daily_returns'] = portfolio_history['portfolio_value'].pct_change()

        # 删除缺失值
        portfolio_returns = portfolio_history['daily_returns'].dropna()

        # 将无风险利率转换为日利率
        daily_risk_free_rate = (1 + risk_free_rate) ** (1/252) - 1

        # 计算基本性能指标
        total_return = (portfolio_history['portfolio_value'].iloc[-1] / portfolio_history['portfolio_value'].iloc[0]) - 1
        annual_return = self._calculate_annual_return(portfolio_returns)
        annual_volatility = self._calculate_annual_volatility(portfolio_returns)
        sharpe_ratio = self._calculate_sharpe_ratio(portfolio_returns, daily_risk_free_rate)
        max_drawdown = self._calculate_max_drawdown(portfolio_history['portfolio_value'])
        calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown != 0 else np.nan

        # 计算高级性能指标
        sortino_ratio = self._calculate_sortino_ratio(portfolio_returns, daily_risk_free_rate)
        omega_ratio = self._calculate_omega_ratio(portfolio_returns, daily_risk_free_rate)
        var_95 = self._calculate_var(portfolio_returns, 0.95)
        cvar_95 = self._calculate_cvar(portfolio_returns, 0.95)

        # 计算交易统计
        if 'position' in portfolio_history.columns and 'action' in portfolio_history.columns:
            trade_stats = self._calculate_trade_statistics(portfolio_history)
        else:
            trade_stats = {}

        # 计算绩效归因（如果提供了基准数据）
        if self.benchmark_returns is not None:
            # 确保基准收益率和投资组合收益率具有相同的索引
            common_index = portfolio_returns.index.intersection(self.benchmark_returns.index)
            if len(common_index) > 0:
                portfolio_returns_aligned = portfolio_returns.loc[common_index]
                benchmark_returns_aligned = self.benchmark_returns.loc[common_index]

                # 计算绩效归因
                alpha, beta, r_squared = self._calculate_performance_attribution(
                    portfolio_returns_aligned,
                    benchmark_returns_aligned
                )

                # 计算信息比率
                tracking_error = self._calculate_tracking_error(
                    portfolio_returns_aligned,
                    benchmark_returns_aligned
                )
                information_ratio = (annual_return - benchmark_returns_aligned.mean() * 252) / tracking_error if tracking_error != 0 else np.nan

                # 计算捕获比率
                up_capture, down_capture = self._calculate_capture_ratios(
                    portfolio_returns_aligned,
                    benchmark_returns_aligned
                )
            else:
                alpha, beta, r_squared = np.nan, np.nan, np.nan
                tracking_error, information_ratio = np.nan, np.nan
                up_capture, down_capture = np.nan, np.nan
        else:
            alpha, beta, r_squared = np.nan, np.nan, np.nan
            tracking_error, information_ratio = np.nan, np.nan
            up_capture, down_capture = np.nan, np.nan

        # 汇总性能指标
        performance_metrics = {
            # 基本性能指标
            'total_return': total_return,
            'annual_return': annual_return,
            'annual_volatility': annual_volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'calmar_ratio': calmar_ratio,

            # 高级性能指标
            'sortino_ratio': sortino_ratio,
            'omega_ratio': omega_ratio,
            'var_95': var_95,
            'cvar_95': cvar_95,

            # 绩效归因
            'alpha': alpha,
            'beta': beta,
            'r_squared': r_squared,
            'tracking_error': tracking_error,
            'information_ratio': information_ratio,
            'up_capture': up_capture,
            'down_capture': down_capture,

            # 交易统计
            **trade_stats
        }

        return performance_metrics

    def _calculate_annual_return(self, returns):
        """计算年化收益率"""
        return (1 + returns.mean()) ** 252 - 1

    def _calculate_annual_volatility(self, returns):
        """计算年化波动率"""
        return returns.std() * np.sqrt(252)

    def _calculate_sharpe_ratio(self, returns, risk_free_rate):
        """计算夏普比率"""
        excess_returns = returns - risk_free_rate
        return excess_returns.mean() / excess_returns.std() * np.sqrt(252) if excess_returns.std() != 0 else np.nan

    def _calculate_max_drawdown(self, portfolio_values):
        """计算最大回撤"""
        # 计算累积最大值
        running_max = np.maximum.accumulate(portfolio_values)
        # 计算回撤
        drawdown = (portfolio_values - running_max) / running_max
        # 返回最大回撤
        return drawdown.min()

    def _calculate_sortino_ratio(self, returns, risk_free_rate):
        """计算索提诺比率"""
        excess_returns = returns - risk_free_rate
        downside_returns = excess_returns[excess_returns < 0]
        downside_deviation = downside_returns.std() * np.sqrt(252) if len(downside_returns) > 0 else 0
        return excess_returns.mean() * 252 / downside_deviation if downside_deviation != 0 else np.nan

    def _calculate_omega_ratio(self, returns, risk_free_rate, threshold=0.0):
        """计算欧米茄比率"""
        excess_returns = returns - risk_free_rate
        positive_returns = excess_returns[excess_returns > threshold]
        negative_returns = excess_returns[excess_returns <= threshold]

        if len(negative_returns) == 0 or negative_returns.abs().sum() == 0:
            return np.inf

        return positive_returns.sum() / negative_returns.abs().sum()

    def _calculate_var(self, returns, confidence_level=0.95):
        """计算风险价值(VaR)"""
        return np.percentile(returns, 100 * (1 - confidence_level))

    def _calculate_cvar(self, returns, confidence_level=0.95):
        """计算条件风险价值(CVaR)"""
        var = self._calculate_var(returns, confidence_level)
        return returns[returns <= var].mean()

    def _calculate_performance_attribution(self, portfolio_returns, benchmark_returns):
        """计算绩效归因（CAPM模型）"""
        # 创建线性回归模型
        X = sm.add_constant(benchmark_returns)
        model = sm.OLS(portfolio_returns, X).fit()

        # 提取alpha和beta
        alpha = model.params[0] * 252  # 年化alpha
        beta = model.params[1]
        r_squared = model.rsquared

        return alpha, beta, r_squared

    def _calculate_tracking_error(self, portfolio_returns, benchmark_returns):
        """计算跟踪误差"""
        tracking_diff = portfolio_returns - benchmark_returns
        return tracking_diff.std() * np.sqrt(252)

    def _calculate_capture_ratios(self, portfolio_returns, benchmark_returns):
        """计算上行/下行捕获比率"""
        # 上行市场
        up_market = benchmark_returns > 0
        if up_market.sum() > 0:
            up_capture = (portfolio_returns[up_market].mean() / benchmark_returns[up_market].mean()) if benchmark_returns[up_market].mean() != 0 else np.nan
        else:
            up_capture = np.nan

        # 下行市场
        down_market = benchmark_returns < 0
        if down_market.sum() > 0:
            down_capture = (portfolio_returns[down_market].mean() / benchmark_returns[down_market].mean()) if benchmark_returns[down_market].mean() != 0 else np.nan
        else:
            down_capture = np.nan

        return up_capture, down_capture

    def _calculate_trade_statistics(self, portfolio_history):
        """计算交易统计"""
        # 提取交易信号
        trades = portfolio_history[portfolio_history['action'] != 0].copy()

        if len(trades) == 0:
            return {
                'total_trades': 0,
                'win_rate': np.nan,
                'avg_profit': np.nan,
                'avg_loss': np.nan,
                'profit_factor': np.nan,
                'avg_trade_duration': np.nan
            }

        # 标记买入和卖出点
        buy_signals = trades[trades['action'] == 1]
        sell_signals = trades[trades['action'] == -1]

        # 计算交易次数
        total_trades = min(len(buy_signals), len(sell_signals))

        # 如果没有完整的交易（买入后卖出），返回默认值
        if total_trades == 0:
            return {
                'total_trades': 0,
                'win_rate': np.nan,
                'avg_profit': np.nan,
                'avg_loss': np.nan,
                'profit_factor': np.nan,
                'avg_trade_duration': np.nan
            }

        # 计算每笔交易的收益
        trade_returns = []
        trade_durations = []

        for i in range(total_trades):
            # 获取买入和卖出价格
            buy_price = portfolio_history.loc[buy_signals.index[i], 'price']
            sell_price = portfolio_history.loc[sell_signals.index[i], 'price']

            # 计算收益率
            trade_return = (sell_price / buy_price) - 1
            trade_returns.append(trade_return)

            # 计算交易持续时间（天数）
            trade_duration = (sell_signals.index[i] - buy_signals.index[i]).days
            trade_durations.append(trade_duration)

        # 转换为numpy数组
        trade_returns = np.array(trade_returns)
        trade_durations = np.array(trade_durations)

        # 计算胜率
        winning_trades = trade_returns > 0
        win_rate = winning_trades.mean()

        # 计算平均盈利和平均亏损
        avg_profit = trade_returns[winning_trades].mean() if winning_trades.sum() > 0 else np.nan
        avg_loss = trade_returns[~winning_trades].mean() if (~winning_trades).sum() > 0 else np.nan

        # 计算盈亏比
        profit_factor = (trade_returns[winning_trades].sum() / abs(trade_returns[~winning_trades].sum())) if (~winning_trades).sum() > 0 and trade_returns[~winning_trades].sum() != 0 else np.nan

        # 计算平均交易持续时间
        avg_trade_duration = trade_durations.mean()

        return {
            'total_trades': total_trades,
            'win_rate': win_rate,
            'avg_profit': avg_profit,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'avg_trade_duration': avg_trade_duration
        }

    def generate_performance_report(self, portfolio_history, risk_free_rate=0.0, output_path=None):
        """
        生成性能报告

        参数:
            portfolio_history (pandas.DataFrame): 投资组合历史数据
            risk_free_rate (float): 无风险利率，年化
            output_path (str, optional): 报告输出路径

        返回:
            dict: 性能指标和图表
        """
        # 分析性能
        metrics = self.analyze_performance(portfolio_history, risk_free_rate)

        # 创建报告字典
        report = {
            'metrics': metrics,
            'charts': {}
        }

        # 生成图表
        try:
            # 设置图表风格
            plt.style.use('dark_background')

            # 投资组合价值图
            fig_value, ax_value = plt.subplots(figsize=(12, 6))
            ax_value.plot(portfolio_history.index, portfolio_history['portfolio_value'], label='Portfolio Value')

            # 如果有基准数据，添加到图表中
            if self.benchmark_data is not None and '收盘' in self.benchmark_data.columns:
                # 将基准数据标准化为与投资组合起始值相同
                benchmark_normalized = self.benchmark_data['收盘'] * (portfolio_history['portfolio_value'].iloc[0] / self.benchmark_data['收盘'].iloc[0])
                ax_value.plot(self.benchmark_data.index, benchmark_normalized, label='Benchmark', alpha=0.7)

            ax_value.set_title('Portfolio Value Over Time')
            ax_value.set_xlabel('Date')
            ax_value.set_ylabel('Value')
            ax_value.legend()
            ax_value.grid(True)

            # 保存图表
            if output_path:
                fig_value.savefig(f"{output_path}/portfolio_value.png")

            # 添加到报告
            report['charts']['portfolio_value'] = fig_value

            # 收益率分布图
            fig_dist, ax_dist = plt.subplots(figsize=(12, 6))
            sns.histplot(portfolio_history['daily_returns'].dropna(), kde=True, ax=ax_dist)
            ax_dist.axvline(0, color='r', linestyle='--')
            ax_dist.set_title('Daily Returns Distribution')
            ax_dist.set_xlabel('Daily Return')
            ax_dist.set_ylabel('Frequency')

            # 保存图表
            if output_path:
                fig_dist.savefig(f"{output_path}/returns_distribution.png")

            # 添加到报告
            report['charts']['returns_distribution'] = fig_dist

            # 回撤图
            fig_dd, ax_dd = plt.subplots(figsize=(12, 6))

            # 计算回撤
            portfolio_values = portfolio_history['portfolio_value']
            running_max = np.maximum.accumulate(portfolio_values)
            drawdown = (portfolio_values - running_max) / running_max

            ax_dd.fill_between(portfolio_history.index, 0, drawdown, color='red', alpha=0.3)
            ax_dd.set_title('Portfolio Drawdown')
            ax_dd.set_xlabel('Date')
            ax_dd.set_ylabel('Drawdown')
            ax_dd.set_ylim(drawdown.min() * 1.1, 0.01)
            ax_dd.grid(True)

            # 保存图表
            if output_path:
                fig_dd.savefig(f"{output_path}/drawdown.png")

            # 添加到报告
            report['charts']['drawdown'] = fig_dd

            # 如果有基准数据，生成相对性能图
            if self.benchmark_returns is not None:
                # 确保基准收益率和投资组合收益率具有相同的索引
                common_index = portfolio_history['daily_returns'].dropna().index.intersection(self.benchmark_returns.index)

                if len(common_index) > 0:
                    portfolio_returns_aligned = portfolio_history.loc[common_index, 'daily_returns']
                    benchmark_returns_aligned = self.benchmark_returns.loc[common_index]

                    # 计算累积收益率
                    portfolio_cum_returns = (1 + portfolio_returns_aligned).cumprod() - 1
                    benchmark_cum_returns = (1 + benchmark_returns_aligned).cumprod() - 1

                    # 相对性能图
                    fig_rel, ax_rel = plt.subplots(figsize=(12, 6))
                    ax_rel.plot(common_index, portfolio_cum_returns, label='Portfolio')
                    ax_rel.plot(common_index, benchmark_cum_returns, label='Benchmark', alpha=0.7)
                    ax_rel.set_title('Cumulative Returns Comparison')
                    ax_rel.set_xlabel('Date')
                    ax_rel.set_ylabel('Cumulative Return')
                    ax_rel.legend()
                    ax_rel.grid(True)

                    # 保存图表
                    if output_path:
                        fig_rel.savefig(f"{output_path}/relative_performance.png")

                    # 添加到报告
                    report['charts']['relative_performance'] = fig_rel

                    # 滚动相关性图
                    fig_corr, ax_corr = plt.subplots(figsize=(12, 6))
                    rolling_corr = portfolio_returns_aligned.rolling(window=60).corr(benchmark_returns_aligned)
                    ax_corr.plot(common_index, rolling_corr)
                    ax_corr.set_title('60-Day Rolling Correlation with Benchmark')
                    ax_corr.set_xlabel('Date')
                    ax_corr.set_ylabel('Correlation')
                    ax_corr.set_ylim(-1.1, 1.1)
                    ax_corr.grid(True)

                    # 保存图表
                    if output_path:
                        fig_corr.savefig(f"{output_path}/rolling_correlation.png")

                    # 添加到报告
                    report['charts']['rolling_correlation'] = fig_corr

            # 关闭所有图表
            plt.close('all')

        except Exception as e:
            self.logger.error(f"生成图表时发生错误: {str(e)}")

        return report

    def analyze_risk(self, portfolio_history):
        """
        分析投资组合风险

        参数:
            portfolio_history (pandas.DataFrame): 投资组合历史数据

        返回:
            dict: 风险分析结果
        """
        # 确保投资组合历史数据包含必要的列
        if 'portfolio_value' not in portfolio_history.columns:
            self.logger.error("投资组合历史数据缺少portfolio_value列")
            raise ValueError("投资组合历史数据缺少portfolio_value列")

        # 计算每日收益率
        if 'daily_returns' not in portfolio_history.columns:
            portfolio_history['daily_returns'] = portfolio_history['portfolio_value'].pct_change()

        # 删除缺失值
        returns = portfolio_history['daily_returns'].dropna()

        if len(returns) < 30:
            self.logger.warning("数据点不足，无法进行可靠的风险分析")
            return {
                'var_historical': np.nan,
                'cvar_historical': np.nan,
                'var_parametric': np.nan,
                'var_cornish_fisher': np.nan,
                'volatility_clustering': np.nan,
                'tail_risk': np.nan,
                'stationarity': np.nan,
                'autocorrelation': np.nan
            }

        # 计算历史VaR和CVaR
        var_95 = self._calculate_var(returns, 0.95)
        cvar_95 = self._calculate_cvar(returns, 0.95)

        # 计算参数化VaR（假设正态分布）
        mean = returns.mean()
        std = returns.std()
        var_parametric = mean - 1.645 * std  # 95% 置信度

        # 计算Cornish-Fisher VaR（考虑偏度和峰度）
        skew = returns.skew()
        kurt = returns.kurt()
        z = 1.645  # 95% 置信度的Z值
        cf_z = z + (z**2 - 1) * skew / 6 + (z**3 - 3*z) * kurt / 24 - (2*z**3 - 5*z) * skew**2 / 36
        var_cornish_fisher = mean - cf_z * std

        # 检测波动率聚集（GARCH效应）
        try:
            # 使用ARCH模型检测波动率聚集
            model = arch_model(returns, vol='GARCH', p=1, q=1)
            result = model.fit(disp='off')
            volatility_clustering = result.params['omega'] > 0 and result.params['alpha[1]'] > 0 and result.params['beta[1]'] > 0
        except:
            volatility_clustering = np.nan

        # 计算尾部风险（超额峰度）
        tail_risk = kurt

        # 检测平稳性（ADF检验）
        try:
            adf_result = adfuller(returns)
            stationarity = adf_result[1] < 0.05  # p值小于0.05表示序列是平稳的
        except:
            stationarity = np.nan

        # 检测自相关性（Ljung-Box检验）
        try:
            lb_result = acorr_ljungbox(returns, lags=[10], return_df=True)
            autocorrelation = lb_result['lb_pvalue'].iloc[0] < 0.05  # p值小于0.05表示存在自相关性
        except:
            autocorrelation = np.nan

        # 汇总风险分析结果
        risk_metrics = {
            'var_historical': var_95,
            'cvar_historical': cvar_95,
            'var_parametric': var_parametric,
            'var_cornish_fisher': var_cornish_fisher,
            'volatility_clustering': volatility_clustering,
            'tail_risk': tail_risk,
            'stationarity': stationarity,
            'autocorrelation': autocorrelation
        }

        return risk_metrics
