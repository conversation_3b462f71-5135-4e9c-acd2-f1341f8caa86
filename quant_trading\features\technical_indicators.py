"""
技术指标模块
负责计算各种技术指标
"""

import pandas as pd
import numpy as np
import logging
import ta

class TechnicalIndicators:
    """
    技术指标类
    负责计算各种技术指标
    """

    def __init__(self, config=None, logger=None):
        """
        初始化技术指标

        参数:
            config (dict): 技术指标配置
            logger (logging.Logger): 日志记录器
        """
        self.logger = logger or logging.getLogger('drl_trading')
        self.calculator = TechnicalIndicatorCalculator(config=config, logger=self.logger)

    def calculate(self, df):
        """
        计算技术指标

        参数:
            df (pandas.DataFrame): 原始数据，必须包含OHLCV列

        返回:
            tuple: (处理后的数据框, 生成的特征名称列表)
        """
        return self.calculator.calculate(df)

class TechnicalIndicatorCalculator:
    """
    技术指标计算器
    负责计算各种技术指标
    """

    def __init__(self, config=None, logger=None):
        """
        初始化技术指标计算器

        参数:
            config (dict): 技术指标配置
            logger (logging.Logger): 日志记录器
        """
        self.logger = logger or logging.getLogger('drl_trading')

        # 默认配置
        default_config = {
            'sma': {'use': True, 'periods': [5, 10, 20, 60, 120]},
            'ema': {'use': True, 'periods': [5, 10, 20, 60]},
            'rsi': {'use': True, 'periods': [6, 14, 21]},
            'macd': {'use': True, 'fast': 12, 'slow': 26, 'signal': 9},
            'bbands': {'use': True, 'period': 20, 'std': 2.0},
            'atr': {'use': True, 'periods': [7, 14, 21]},
            'volume': {'use': True, 'periods': [5, 10, 20]}
        }

        # 更新配置
        self.config = default_config
        if config:
            for key, value in config.items():
                if key in self.config:
                    self.config[key].update(value)

    def calculate(self, df):
        """
        计算技术指标

        参数:
            df (pandas.DataFrame): 原始数据，必须包含OHLCV列

        返回:
            tuple: (处理后的数据框, 生成的特征名称列表)
        """
        # 创建一个列表来存储生成的特征名称
        feature_names = []

        # 计算简单移动平均线 (SMA)
        if self.config.get('sma', {}).get('use', True):
            df, sma_features = self._calculate_sma(df)
            feature_names.extend(sma_features)

        # 计算指数移动平均线 (EMA)
        if self.config.get('ema', {}).get('use', True):
            df, ema_features = self._calculate_ema(df)
            feature_names.extend(ema_features)

        # 计算相对强弱指标 (RSI)
        if self.config.get('rsi', {}).get('use', True):
            df, rsi_features = self._calculate_rsi(df)
            feature_names.extend(rsi_features)

        # 计算MACD
        if self.config.get('macd', {}).get('use', True):
            df, macd_features = self._calculate_macd(df)
            feature_names.extend(macd_features)

        # 计算布林带
        if self.config.get('bbands', {}).get('use', True):
            df, bbands_features = self._calculate_bbands(df)
            feature_names.extend(bbands_features)

        # 计算平均真实波动幅度 (ATR)
        if self.config.get('atr', {}).get('use', True):
            df, atr_features = self._calculate_atr(df)
            feature_names.extend(atr_features)

        # 计算成交量指标
        if self.config.get('volume', {}).get('use', True):
            df, volume_features = self._calculate_volume(df)
            feature_names.extend(volume_features)

        return df, feature_names

    def _calculate_sma(self, df):
        """计算简单移动平均线"""
        feature_names = []
        periods = self.config.get('sma', {}).get('periods', [5, 10, 20, 60, 120])

        for period in periods:
            try:
                df[f'SMA_{period}'] = ta.trend.sma_indicator(df['收盘'], window=period, fillna=True)
                feature_names.append(f'SMA_{period}')

                # 计算价格相对SMA的位置
                df[f'收盘/SMA_{period}'] = df['收盘'] / df[f'SMA_{period}']
                feature_names.append(f'收盘/SMA_{period}')
            except Exception as e:
                self.logger.warning(f"计算SMA_{period}失败: {str(e)}")

        return df, feature_names

    def _calculate_ema(self, df):
        """计算指数移动平均线"""
        feature_names = []
        periods = self.config.get('ema', {}).get('periods', [5, 10, 20, 60])

        for period in periods:
            try:
                df[f'EMA_{period}'] = ta.trend.ema_indicator(df['收盘'], window=period, fillna=True)
                feature_names.append(f'EMA_{period}')
            except Exception as e:
                self.logger.warning(f"计算EMA_{period}失败: {str(e)}")

        return df, feature_names

    def _calculate_rsi(self, df):
        """计算相对强弱指标"""
        feature_names = []
        periods = self.config.get('rsi', {}).get('periods', [6, 14, 21])

        for period in periods:
            try:
                df[f'RSI_{period}'] = ta.momentum.rsi(df['收盘'], window=period, fillna=True)
                feature_names.append(f'RSI_{period}')
            except Exception as e:
                self.logger.warning(f"计算RSI_{period}失败: {str(e)}")

        return df, feature_names

    def _calculate_macd(self, df):
        """计算MACD"""
        feature_names = []
        fast = self.config.get('macd', {}).get('fast', 12)
        slow = self.config.get('macd', {}).get('slow', 26)
        signal = self.config.get('macd', {}).get('signal', 9)

        try:
            df[f'MACD_{fast}_{slow}'] = ta.trend.macd(df['收盘'], window_slow=slow, window_fast=fast, fillna=True)
            df[f'MACD_Signal_{signal}'] = ta.trend.macd_signal(df['收盘'], window_slow=slow, window_fast=fast, window_sign=signal, fillna=True)
            df[f'MACD_Hist'] = ta.trend.macd_diff(df['收盘'], window_slow=slow, window_fast=fast, window_sign=signal, fillna=True)

            feature_names.extend([f'MACD_{fast}_{slow}', f'MACD_Signal_{signal}', 'MACD_Hist'])
        except Exception as e:
            self.logger.warning(f"计算MACD失败: {str(e)}")

        return df, feature_names

    def _calculate_bbands(self, df):
        """计算布林带"""
        feature_names = []
        period = self.config.get('bbands', {}).get('period', 20)
        std = self.config.get('bbands', {}).get('std', 2.0)

        try:
            df[f'BBands_Upper_{period}'] = ta.volatility.bollinger_hband(df['收盘'], window=period, window_dev=std, fillna=True)
            df[f'BBands_Middle_{period}'] = ta.volatility.bollinger_mavg(df['收盘'], window=period, fillna=True)
            df[f'BBands_Lower_{period}'] = ta.volatility.bollinger_lband(df['收盘'], window=period, window_dev=std, fillna=True)

            # 计算价格在布林带中的位置 (0-1)
            upper = df[f'BBands_Upper_{period}']
            lower = df[f'BBands_Lower_{period}']
            df[f'BBands_Position_{period}'] = (df['收盘'] - lower) / (upper - lower + 1e-10)

            feature_names.extend([f'BBands_Upper_{period}', f'BBands_Middle_{period}',
                                 f'BBands_Lower_{period}', f'BBands_Position_{period}'])
        except Exception as e:
            self.logger.warning(f"计算布林带失败: {str(e)}")

        return df, feature_names

    def _calculate_atr(self, df):
        """计算平均真实波动幅度"""
        feature_names = []
        periods = self.config.get('atr', {}).get('periods', [7, 14, 21])

        for period in periods:
            try:
                df[f'ATR_{period}'] = ta.volatility.average_true_range(
                    df['最高'],
                    df['最低'],
                    df['收盘'],
                    window=period,
                    fillna=True
                )

                # 计算ATR占收盘价的比例
                df[f'ATR_{period}_Pct'] = df[f'ATR_{period}'] / df['收盘']

                feature_names.extend([f'ATR_{period}', f'ATR_{period}_Pct'])
            except Exception as e:
                self.logger.warning(f"计算ATR_{period}失败: {str(e)}")

        return df, feature_names

    def _calculate_volume(self, df):
        """计算成交量指标"""
        feature_names = []
        periods = self.config.get('volume', {}).get('periods', [5, 10, 20])

        try:
            # 成交量移动平均
            for period in periods:
                df[f'Volume_SMA_{period}'] = ta.trend.sma_indicator(df['成交量'], window=period, fillna=True)
                # 计算成交量相对其移动平均的比例
                df[f'Volume/SMA_{period}'] = df['成交量'] / df[f'Volume_SMA_{period}']

                feature_names.extend([f'Volume_SMA_{period}', f'Volume/SMA_{period}'])

            # 成交量变化率
            df['Volume_Change'] = df['成交量'].pct_change()
            feature_names.append('Volume_Change')

            # 能量潮指标(OBV)
            df['OBV'] = ta.volume.on_balance_volume(df['收盘'], df['成交量'], fillna=True)
            feature_names.append('OBV')
        except Exception as e:
            self.logger.warning(f"计算成交量指标失败: {str(e)}")

        return df, feature_names
