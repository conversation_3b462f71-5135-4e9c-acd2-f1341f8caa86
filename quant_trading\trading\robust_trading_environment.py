"""
鲁棒交易环境模块
实现具有市场状态感知和风险管理的鲁棒交易环境
"""

import logging
import numpy as np
import pandas as pd
import gymnasium as gym
from gymnasium import spaces
from typing import Dict, List, Optional, Tuple, Union, Any

from quant_trading.trading.trading_environment import TradingEnvironment
from quant_trading.trading.observation import ObservationHandler
from quant_trading.trading.reward import RewardCalculator
from quant_trading.trading.action import ActionHandler
from quant_trading.trading.rendering import Renderer
from quant_trading.market.market_condition_detector import MarketConditionDetector, MarketCondition
from quant_trading.risk.risk_manager import RiskManager, StopLossType, PositionSizingMethod

class RobustTradingEnvironment(TradingEnvironment):
    """
    鲁棒交易环境类
    实现具有市场状态感知和风险管理的鲁棒交易环境
    """

    def __init__(self,
                 df_processed_data,
                 initial_capital=100000,
                 commission_rate=0.0003,
                 min_hold_days=3,
                 allow_short=False,
                 max_position=1.0,
                 reward_config=None,
                 window_size=20,
                 render_mode=None,
                 market_condition_detector=None,
                 risk_manager=None,
                 stop_loss_config=None,
                 position_sizing_config=None,
                 adaptive_reward=True,
                 market_regime_aware=True):
        """
        初始化鲁棒交易环境

        参数:
            df_processed_data (pandas.DataFrame): 处理好的行情数据和特征
            initial_capital (float): 初始资金
            commission_rate (float): 手续费率（单边）
            min_hold_days (int): 最小持仓天数
            allow_short (bool): 是否允许做空
            max_position (float): 最大仓位比例，范围[0, 1]
            reward_config (dict): 奖励函数配置
            window_size (int): 观测窗口大小
            render_mode (str): 渲染模式，可选 'human', 'rgb_array'
            market_condition_detector (MarketConditionDetector): 市场状态检测器
            risk_manager (RiskManager): 风险管理器
            stop_loss_config (dict): 止损配置
            position_sizing_config (dict): 仓位大小配置
            adaptive_reward (bool): 是否使用自适应奖励函数
            market_regime_aware (bool): 是否感知市场状态
        """
        # 调用父类初始化方法
        super(RobustTradingEnvironment, self).__init__(
            df_processed_data=df_processed_data,
            initial_capital=initial_capital,
            commission_rate=commission_rate,
            min_hold_days=min_hold_days,
            allow_short=allow_short,
            max_position=max_position,
            reward_config=reward_config,
            window_size=window_size,
            render_mode=render_mode
        )

        # 市场状态检测器
        self.market_detector = market_condition_detector or MarketConditionDetector(logger=self.logger)

        # 风险管理器
        self.risk_manager = risk_manager or RiskManager(
            stop_loss_config=stop_loss_config,
            position_sizing_config=position_sizing_config,
            market_condition_detector=self.market_detector,
            logger=self.logger
        )

        # 自适应奖励和市场状态感知
        self.adaptive_reward = adaptive_reward
        self.market_regime_aware = market_regime_aware

        # 市场状态相关属性
        self.current_market_condition = None
        self.market_condition_history = []

        # 风险管理相关属性
        self.stop_loss_triggered = False
        self.take_profit_triggered = False
        self.position_sizing_applied = False

        # 扩展信息字典
        self.extended_info = {}

    def reset(self, seed=None, options=None):
        """
        重置环境到初始状态

        参数:
            seed (int): 随机种子
            options (dict): 重置选项

        返回:
            tuple: (observation, info)
        """
        # 调用父类的reset方法
        observation, info = super().reset(seed=options)

        # 重置市场状态
        self.current_market_condition = None
        self.market_condition_history = []

        # 重置风险管理器
        self.risk_manager.reset()
        self.stop_loss_triggered = False
        self.take_profit_triggered = False
        self.position_sizing_applied = False

        # 重置扩展信息
        self.extended_info = {}

        # 如果启用市场状态感知，检测初始市场状态
        if self.market_regime_aware and self.current_step >= self.window_size:
            self._update_market_condition()

        return observation, info

    def step(self, action):
        """
        执行一个动作并更新环境

        参数:
            action (int): 动作，0=保持, 1=买入, 2=卖出

        返回:
            tuple: (observation, reward, terminated, truncated, info)
        """
        # 保存上一步的组合价值用于计算奖励
        prev_portfolio_value = self._calculate_portfolio_value()

        # 获取当前价格
        self.current_price = self.df['收盘'].iloc[self.current_step]

        # 获取当前日期
        current_date = self.df.index[self.current_step]

        # 如果启用市场状态感知，更新市场状态
        if self.market_regime_aware:
            self._update_market_condition()

        # 检查是否触发止损或止盈
        if self.shares_held != 0 and self.cost_basis > 0:
            # 计算ATR（如果可用）
            atr = self._calculate_atr() if '真实波动幅度' in self.df.columns else None

            # 检查止损
            stop_loss_triggered = self.risk_manager.check_stop_loss(
                current_price=self.current_price,
                position=self.shares_held,
                entry_price=self.cost_basis,
                atr=atr
            )

            # 检查止盈
            take_profit_triggered = self.risk_manager.check_take_profit(
                current_price=self.current_price,
                position=self.shares_held,
                entry_price=self.cost_basis
            )

            # 如果触发止损或止盈，强制卖出
            if stop_loss_triggered or take_profit_triggered:
                self.stop_loss_triggered = stop_loss_triggered
                self.take_profit_triggered = take_profit_triggered
                action = 2  # 卖出
                self.logger.info(f"触发{'止损' if stop_loss_triggered else '止盈'}，强制卖出")

        # 如果是买入动作，应用仓位大小计算
        if action == 1 and self.shares_held == 0:
            # 计算波动率
            volatility = self._calculate_volatility()

            # 计算胜率和平均盈亏比（如果有历史交易）
            win_rate, avg_win, avg_loss = self._calculate_trading_stats()

            # 计算仓位大小
            position_size = self.risk_manager.calculate_position_size(
                price=self.current_price,
                volatility=volatility,
                capital=self.cash,
                win_rate=win_rate,
                avg_win=avg_win,
                avg_loss=avg_loss
            )

            # 记录原始动作和调整后的仓位大小
            self.position_sizing_applied = True
            self.extended_info['original_action'] = action
            self.extended_info['position_size'] = position_size

            # 修改动作处理逻辑，使用计算的仓位大小
            self.action_handler.position_size = position_size

        # 执行交易动作
        self.cash, self.shares_held, self.holding_days, trade_cost, is_trade, cost_basis = self.action_handler.execute_action(
            action=action,
            current_step=self.current_step,
            current_price=self.current_price,
            cash=self.cash,
            shares_held=self.shares_held,
            holding_days=self.holding_days,
            initial_capital=self.initial_capital
        )

        # 更新交易记录中的日期
        if is_trade and len(self.action_handler.trades) > 0:
            # 更新最新交易记录的日期
            latest_trade = self.action_handler.trades[-1]
            latest_trade['date'] = current_date

            # 添加市场状态信息
            if self.current_market_condition:
                latest_trade['market_condition'] = self.current_market_condition.name

        # 更新成本基础
        if cost_basis > 0:
            self.cost_basis = cost_basis

        # 更新总手续费
        self.total_commission += trade_cost

        # 计算当前组合价值
        current_portfolio_value = self._calculate_portfolio_value()

        # 记录组合价值历史
        self.portfolio_values.append(current_portfolio_value)

        # 计算奖励
        if self.adaptive_reward and self.current_market_condition:
            # 使用自适应奖励函数
            reward, reward_components = self._calculate_adaptive_reward(
                prev_portfolio_value=prev_portfolio_value,
                current_portfolio_value=current_portfolio_value,
                holding_days=self.holding_days,
                trade_cost=trade_cost,
                is_trade=is_trade
            )
        else:
            # 使用标准奖励函数
            reward, reward_components = self.reward_calculator.calculate_reward(
                prev_portfolio_value=prev_portfolio_value,
                current_portfolio_value=current_portfolio_value,
                holding_days=self.holding_days,
                trade_cost=trade_cost,
                is_trade=is_trade
            )

        # 移动到下一个时间步
        self.current_step += 1

        # 检查是否结束
        terminated = self.current_step >= len(self.df) - 1
        truncated = False  # 在金融环境中通常不使用truncated

        # 获取新的观测
        observation = self.observation_handler.get_observation(
            current_step=self.current_step,
            cash=self.cash,
            shares_held=self.shares_held,
            current_price=self.current_price,
            cost_basis=self.cost_basis,
            holding_days=self.holding_days
        )

        # 更新渲染器数据
        self.renderer.update_data(
            portfolio_values=self.portfolio_values,
            trades=self.action_handler.get_trades()
        )

        # 扩展信息字典
        info = {
            'step': self.current_step,
            'portfolio_value': current_portfolio_value,
            'cash': self.cash,
            'shares_held': self.shares_held,
            'current_price': self.current_price,
            'holding_days': self.holding_days,
            'total_commission': self.total_commission,
            'return': (current_portfolio_value / prev_portfolio_value) - 1,
            'reward_components': reward_components,
            'market_condition': self.current_market_condition.name if self.current_market_condition else None,
            'stop_loss_triggered': self.stop_loss_triggered,
            'take_profit_triggered': self.take_profit_triggered,
            'position_sizing_applied': self.position_sizing_applied
        }

        # 添加扩展信息
        info.update(self.extended_info)

        # 重置单步状态
        self.stop_loss_triggered = False
        self.take_profit_triggered = False
        self.position_sizing_applied = False
        self.extended_info = {}

        return observation, reward, terminated, truncated, info

    def _update_market_condition(self):
        """更新市场状态"""
        # 获取当前窗口的数据
        start_idx = max(0, self.current_step - self.window_size)
        window_data = self.df.iloc[start_idx:self.current_step + 1]

        # 检测市场状态
        result = self.market_detector.detect_market_condition(window_data)
        self.current_market_condition = result['primary_condition']

        # 记录市场状态
        self.market_condition_history.append(self.current_market_condition)

        # 更新风险管理器的市场状态
        self.risk_manager.update_market_condition(window_data)

    def _calculate_adaptive_reward(self, prev_portfolio_value, current_portfolio_value, holding_days, trade_cost, is_trade):
        """
        计算自适应奖励

        参数:
            prev_portfolio_value (float): 上一步的组合价值
            current_portfolio_value (float): 当前组合价值
            holding_days (int): 持仓天数
            trade_cost (float): 交易成本
            is_trade (bool): 是否进行了交易

        返回:
            tuple: (reward, reward_components)
        """
        # 计算基础奖励
        reward, reward_components = self.reward_calculator.calculate_reward(
            prev_portfolio_value=prev_portfolio_value,
            current_portfolio_value=current_portfolio_value,
            holding_days=holding_days,
            trade_cost=trade_cost,
            is_trade=is_trade
        )

        # 根据市场状态调整奖励
        if self.current_market_condition == MarketCondition.BULL:
            # 牛市：更重视收益，减轻波动率惩罚
            reward_components['portfolio_return'] *= 1.2
            reward_components['volatility_penalty'] *= 0.8

        elif self.current_market_condition == MarketCondition.BEAR:
            # 熊市：更重视风险控制，减轻收益压力
            reward_components['portfolio_return'] *= 0.8
            reward_components['drawdown_penalty'] *= 1.2

        elif self.current_market_condition == MarketCondition.VOLATILE:
            # 高波动市场：更重视波动率控制
            reward_components['volatility_penalty'] *= 1.5

        elif self.current_market_condition == MarketCondition.SIDEWAYS:
            # 震荡市场：减轻交易成本惩罚，鼓励更多交易
            reward_components['trade_penalty'] *= 0.7

        elif self.current_market_condition == MarketCondition.CRISIS:
            # 危机市场：大幅增加风险惩罚，保护资金
            reward_components['drawdown_penalty'] *= 2.0
            reward_components['volatility_penalty'] *= 1.5

        # 重新计算总奖励
        reward = (
            reward_components['portfolio_return'] -
            reward_components['volatility_penalty'] -
            reward_components['drawdown_penalty'] -
            reward_components['holding_penalty'] -
            reward_components['trade_penalty']
        )

        # 更新总奖励
        reward_components['total_reward'] = reward

        return reward, reward_components

    def _calculate_atr(self, window=14):
        """
        计算ATR（平均真实波动幅度）

        参数:
            window (int): 窗口大小

        返回:
            float: ATR值
        """
        if '真实波动幅度' in self.df.columns:
            # 如果数据中已经有ATR列，直接使用
            return self.df['真实波动幅度'].iloc[self.current_step]

        # 计算真实波动幅度
        start_idx = max(0, self.current_step - window)
        window_data = self.df.iloc[start_idx:self.current_step + 1]

        # 计算真实波动幅度
        high = window_data['最高'].values
        low = window_data['最低'].values
        close = window_data['收盘'].values

        tr1 = np.abs(high[1:] - low[1:])
        tr2 = np.abs(high[1:] - close[:-1])
        tr3 = np.abs(low[1:] - close[:-1])

        tr = np.maximum(np.maximum(tr1, tr2), tr3)
        atr = np.mean(tr)

        return atr

    def _calculate_volatility(self, window=20):
        """
        计算波动率

        参数:
            window (int): 窗口大小

        返回:
            float: 波动率
        """
        start_idx = max(0, self.current_step - window)
        window_data = self.df.iloc[start_idx:self.current_step + 1]

        # 计算收益率
        returns = window_data['收盘'].pct_change().dropna()

        # 计算波动率
        volatility = np.std(returns) * np.sqrt(252)  # 年化波动率

        return volatility

    def _calculate_trading_stats(self):
        """
        计算交易统计数据

        返回:
            tuple: (胜率, 平均盈利, 平均亏损)
        """
        trades = self.action_handler.get_trades()

        if len(trades) < 2:
            return None, None, None

        # 计算每笔交易的盈亏
        profits = []
        for i in range(0, len(trades), 2):
            if i + 1 < len(trades):
                buy_trade = trades[i] if trades[i]['action'] == 'buy' else trades[i + 1]
                sell_trade = trades[i + 1] if trades[i + 1]['action'] == 'sell' else trades[i]

                buy_cost = buy_trade['cost'] + buy_trade['commission']
                sell_revenue = sell_trade['revenue'] - sell_trade['commission']

                profit = sell_revenue - buy_cost
                profits.append(profit)

        if not profits:
            return None, None, None

        # 计算胜率
        win_count = sum(1 for p in profits if p > 0)
        win_rate = win_count / len(profits) if len(profits) > 0 else 0.5

        # 计算平均盈利和平均亏损
        win_profits = [p for p in profits if p > 0]
        loss_profits = [p for p in profits if p <= 0]

        avg_win = np.mean(win_profits) if win_profits else 0
        avg_loss = abs(np.mean(loss_profits)) if loss_profits else 0

        return win_rate, avg_win, avg_loss
