"""
修复 Python 3.13 + PyTorch + Streamlit 兼容性问题

此脚本解决以下错误:
1. RuntimeError: no running event loop
2. RuntimeError: Tried to instantiate class '__path__._path', but it does not exist!

适用于 Python 3.13 + PyTorch 2.7.0 + Streamlit 1.45.0
"""

import os
import sys
import asyncio
import importlib
import logging
from functools import wraps
import warnings

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('fix_asyncio_torch')

def apply_asyncio_patch():
    """
    修复 Python 3.13 中的 asyncio 事件循环问题
    """
    logger.info("应用 asyncio 事件循环补丁...")

    # 检查 Python 版本
    if sys.version_info >= (3, 13):
        logger.info("检测到 Python 3.13+，应用 asyncio 补丁")

        # 保存原始函数
        original_get_running_loop = asyncio.get_running_loop

        @wraps(original_get_running_loop)
        def patched_get_running_loop():
            """
            修补版的 get_running_loop 函数
            如果没有运行中的事件循环，则创建一个新的
            """
            try:
                return original_get_running_loop()
            except RuntimeError:
                # 如果没有运行中的事件循环，创建一个新的
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                return loop

        # 替换原始函数
        asyncio.get_running_loop = patched_get_running_loop
        logger.info("asyncio.get_running_loop 已被修补")

        # 修补 asyncio.run 函数，确保它能在 Python 3.13 中正常工作
        original_run = asyncio.run

        @wraps(original_run)
        def patched_run(coro, *, debug=None, loop_factory=None):
            """
            修补版的 asyncio.run 函数
            确保在 Python 3.13 中能正常工作
            """
            try:
                return original_run(coro, debug=debug, loop_factory=loop_factory)
            except RuntimeError as e:
                if "no running event loop" in str(e):
                    logger.info("捕获到 'no running event loop' 错误，创建新的事件循环")
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    try:
                        return loop.run_until_complete(coro)
                    finally:
                        loop.close()
                else:
                    raise

        # 替换原始函数
        asyncio.run = patched_run
        logger.info("asyncio.run 已被修补")

        return True
    else:
        logger.info(f"当前 Python 版本 {sys.version} 不需要 asyncio 补丁")
        return False

def apply_torch_patch():
    """
    修复 PyTorch 自定义类注册问题
    """
    logger.info("应用 PyTorch 自定义类补丁...")

    try:
        import torch

        # 检查 PyTorch 版本
        logger.info(f"检测到 PyTorch 版本: {torch.__version__}")

        # 修补 torch._dynamo 模块以避免某些兼容性问题
        if hasattr(torch, '_dynamo'):
            try:
                # 禁用 torch._dynamo 以避免某些兼容性问题
                if hasattr(torch._dynamo, 'config') and hasattr(torch._dynamo.config, 'suppress_errors'):
                    torch._dynamo.config.suppress_errors = True
                    logger.info("已设置 torch._dynamo.config.suppress_errors = True")

                # 如果存在 torch._dynamo.allow_in_graph 函数，修补它以避免某些错误
                if hasattr(torch._dynamo, 'allow_in_graph'):
                    original_allow_in_graph = torch._dynamo.allow_in_graph

                    @wraps(original_allow_in_graph)
                    def patched_allow_in_graph(fn):
                        try:
                            return original_allow_in_graph(fn)
                        except Exception as e:
                            logger.warning(f"torch._dynamo.allow_in_graph 出错: {str(e)}")
                            return fn

                    torch._dynamo.allow_in_graph = patched_allow_in_graph
                    logger.info("torch._dynamo.allow_in_graph 已被修补")
            except Exception as e:
                logger.warning(f"修补 torch._dynamo 时出错: {str(e)}")

        # 修补 torch._classes 模块
        if hasattr(torch, '_classes'):
            # 使用猴子补丁方法修补 _classes 模块
            import types

            # 创建一个自定义的 __getattr__ 函数
            def custom_getattr(name):
                """
                自定义的 __getattr__ 函数
                处理 '__path__._path' 特殊情况
                """
                if name == '__path__' or name == '_path':
                    # 返回一个空列表而不是引发错误
                    logger.warning(f"捕获到对 '{name}' 的访问，返回空列表")
                    return []

                # 对于其他属性，尝试使用原始方法
                try:
                    # 使用原始的 _get_custom_class_python_wrapper 方法
                    if hasattr(torch._C, '_get_custom_class_python_wrapper'):
                        return torch._C._get_custom_class_python_wrapper(torch._classes.name, name)
                    else:
                        # 如果找不到原始方法，抛出 AttributeError
                        raise AttributeError(f"module 'torch._classes' has no attribute '{name}'")
                except RuntimeError as e:
                    if '__path__._path' in str(e):
                        # 处理特定错误
                        logger.warning("捕获到 '__path__._path' 错误，返回空列表")
                        return []
                    # 重新抛出其他错误
                    raise

            # 修补 streamlit.watcher.local_sources_watcher 模块中的 extract_paths 函数
            # 这是一个更直接的方法来解决问题
            try:
                import streamlit.watcher.local_sources_watcher

                original_get_module_paths = streamlit.watcher.local_sources_watcher.get_module_paths

                def patched_get_module_paths(module):
                    """修补版的 get_module_paths 函数"""
                    if hasattr(module, '__name__') and ('torch' in module.__name__ or 'pytorch' in module.__name__.lower()):
                        logger.info(f"跳过 PyTorch 模块的路径提取: {module.__name__}")
                        return []
                    return original_get_module_paths(module)

                streamlit.watcher.local_sources_watcher.get_module_paths = patched_get_module_paths
                logger.info("streamlit.watcher.local_sources_watcher.get_module_paths 已被修补")

                return True
            except (ImportError, AttributeError) as e:
                logger.warning(f"无法修补 Streamlit watcher 模块: {str(e)}")

                # 尝试直接修补 torch._classes 模块
                try:
                    # 保存原始的 __class__ 属性
                    original_class = torch._classes.__class__

                    # 创建一个新的模块对象
                    patched_module = types.ModuleType('torch._classes')

                    # 复制所有属性
                    for attr in dir(torch._classes):
                        if not attr.startswith('__'):
                            setattr(patched_module, attr, getattr(torch._classes, attr))

                    # 添加自定义的 __getattr__ 方法
                    patched_module.__getattr__ = custom_getattr

                    # 替换原始模块
                    torch._classes = patched_module

                    logger.info("torch._classes 模块已被替换为带有自定义 __getattr__ 的版本")
                    return True
                except Exception as e:
                    logger.warning(f"无法修补 torch._classes 模块: {str(e)}")
                    return False
        else:
            logger.warning("未找到 torch._classes 模块，无法应用补丁")
            return False

    except ImportError:
        logger.warning("未安装 PyTorch，无法应用补丁")
        return False

def patch_streamlit_watcher():
    """
    修补 Streamlit 的 local_sources_watcher 模块
    """
    logger.info("应用 Streamlit watcher 补丁...")

    try:
        import streamlit

        # 尝试多种方式修补 Streamlit 的文件监视器
        patched = False

        # 修补 Streamlit 的 server.server_util 模块，避免某些兼容性问题
        try:
            from streamlit.web import server
            if hasattr(server, 'server_util'):
                # 修补 server_util 模块中可能导致问题的函数
                if hasattr(server.server_util, 'make_url_path_regex'):
                    original_make_url_path_regex = server.server_util.make_url_path_regex

                    @wraps(original_make_url_path_regex)
                    def patched_make_url_path_regex(*args, **kwargs):
                        try:
                            return original_make_url_path_regex(*args, **kwargs)
                        except Exception as e:
                            logger.warning(f"make_url_path_regex 出错: {str(e)}")
                            # 返回一个简单的正则表达式作为后备
                            import re
                            return re.compile(r".*")

                    server.server_util.make_url_path_regex = patched_make_url_path_regex
                    logger.info("streamlit.web.server.server_util.make_url_path_regex 已被修补")
                    patched = True
        except (ImportError, AttributeError) as e:
            logger.warning(f"无法修补 Streamlit server_util: {str(e)}")

        # 方法 1: 修补 extract_paths 函数
        try:
            from streamlit.watcher import local_sources_watcher

            if hasattr(local_sources_watcher, 'extract_paths'):
                # 保存原始函数
                original_extract_paths = local_sources_watcher.extract_paths

                @wraps(original_extract_paths)
                def patched_extract_paths(module):
                    """
                    修补版的 extract_paths 函数
                    处理 PyTorch 模块的特殊情况
                    """
                    try:
                        if hasattr(module, '__name__') and ('torch' in module.__name__ or
                                                           'pytorch' in module.__name__.lower() or
                                                           'tensorboard' in module.__name__.lower() or
                                                           'tensorflow' in module.__name__.lower()):
                            # 对于 PyTorch/TensorFlow 模块，直接返回空列表
                            logger.info(f"跳过模块的路径提取: {module.__name__}")
                            return []
                        return original_extract_paths(module)
                    except Exception as e:
                        if hasattr(module, '__name__') and ('torch' in module.__name__ or
                                                           'pytorch' in module.__name__.lower() or
                                                           'tensorboard' in module.__name__.lower() or
                                                           'tensorflow' in module.__name__.lower()):
                            # 对于 PyTorch/TensorFlow 模块，返回空列表
                            logger.warning(f"处理模块 {module.__name__} 时出错，跳过路径提取: {str(e)}")
                            return []
                        # 重新抛出其他错误
                        raise

                # 替换原始函数
                local_sources_watcher.extract_paths = patched_extract_paths
                logger.info("streamlit.watcher.local_sources_watcher.extract_paths 已被修补")
                patched = True
        except (ImportError, AttributeError) as e:
            logger.warning(f"无法修补 extract_paths: {str(e)}")

        # 方法 2: 修补 get_module_paths 函数
        try:
            from streamlit.watcher import local_sources_watcher

            if hasattr(local_sources_watcher, 'get_module_paths'):
                # 保存原始函数
                original_get_module_paths = local_sources_watcher.get_module_paths

                @wraps(original_get_module_paths)
                def patched_get_module_paths(module):
                    """
                    修补版的 get_module_paths 函数
                    处理 PyTorch 模块的特殊情况
                    """
                    try:
                        if hasattr(module, '__name__') and ('torch' in module.__name__ or
                                                           'pytorch' in module.__name__.lower() or
                                                           'tensorboard' in module.__name__.lower() or
                                                           'tensorflow' in module.__name__.lower()):
                            # 对于 PyTorch/TensorFlow 模块，直接返回空列表
                            logger.info(f"跳过模块的路径提取: {module.__name__}")
                            return []
                        return original_get_module_paths(module)
                    except Exception as e:
                        if hasattr(module, '__name__') and ('torch' in module.__name__ or
                                                           'pytorch' in module.__name__.lower() or
                                                           'tensorboard' in module.__name__.lower() or
                                                           'tensorflow' in module.__name__.lower()):
                            # 对于 PyTorch/TensorFlow 模块，返回空列表
                            logger.warning(f"处理模块 {module.__name__} 时出错，跳过路径提取: {str(e)}")
                            return []
                        # 重新抛出其他错误
                        raise

                # 替换原始函数
                local_sources_watcher.get_module_paths = patched_get_module_paths
                logger.info("streamlit.watcher.local_sources_watcher.get_module_paths 已被修补")
                patched = True
        except (ImportError, AttributeError) as e:
            logger.warning(f"无法修补 get_module_paths: {str(e)}")

        # 方法 3: 修补 Streamlit 的 runtime 模块
        try:
            if hasattr(streamlit, 'runtime'):
                # 修补 Runtime 类的 _get_async_objs 方法
                from streamlit.runtime.runtime import Runtime
                if hasattr(Runtime, '_get_async_objs'):
                    original_get_async_objs = Runtime._get_async_objs

                    @wraps(original_get_async_objs)
                    def patched_get_async_objs(self):
                        try:
                            return original_get_async_objs(self)
                        except RuntimeError as e:
                            if "no running event loop" in str(e):
                                logger.warning("捕获到 'no running event loop' 错误，创建新的事件循环")
                                import asyncio
                                loop = asyncio.new_event_loop()
                                asyncio.set_event_loop(loop)
                                return original_get_async_objs(self)
                            raise

                    Runtime._get_async_objs = patched_get_async_objs
                    logger.info("streamlit.runtime.runtime.Runtime._get_async_objs 已被修补")
                    patched = True
        except (ImportError, AttributeError) as e:
            logger.warning(f"无法修补 Streamlit runtime: {str(e)}")

        # 方法 4: 修补 _path 属性访问
        try:
            # 修补 Python 的模块系统，拦截对 __path__._path 的访问
            import types

            original_getattr = types.ModuleType.__getattribute__

            def patched_module_getattr(self, name):
                """修补版的 __getattribute__ 函数"""
                try:
                    return original_getattr(self, name)
                except Exception as e:
                    if name == '__path__' and hasattr(self, '__name__') and (
                        'torch' in self.__name__ or
                        'pytorch' in self.__name__.lower() or
                        'tensorboard' in self.__name__.lower() or
                        'tensorflow' in self.__name__.lower()):
                        # 为特定模块返回一个带有 _path 属性的对象
                        class PathWrapper:
                            _path = []
                        return PathWrapper()
                    # 重新抛出其他错误
                    raise

            # 替换原始函数
            # 注意: 这是一个危险的操作，可能会影响其他模块
            # 只在其他方法都失败时尝试
            if not patched:
                types.ModuleType.__getattribute__ = patched_module_getattr
                logger.info("types.ModuleType.__getattribute__ 已被修补")
                patched = True
        except Exception as e:
            logger.warning(f"无法修补模块系统: {str(e)}")

        return patched

    except ImportError:
        logger.warning("未安装 Streamlit，无法应用补丁")
        return False

def apply_all_patches():
    """
    应用所有补丁
    """
    logger.info("开始应用所有兼容性补丁...")

    asyncio_patched = apply_asyncio_patch()
    torch_patched = apply_torch_patch()
    streamlit_patched = patch_streamlit_watcher()

    if asyncio_patched or torch_patched or streamlit_patched:
        logger.info("补丁应用成功")
        return True
    else:
        logger.warning("没有应用任何补丁")
        return False

if __name__ == "__main__":
    print("应用 Python 3.13 + PyTorch + Streamlit 兼容性补丁...")
    result = apply_all_patches()
    print(f"补丁应用{'成功' if result else '失败'}")
