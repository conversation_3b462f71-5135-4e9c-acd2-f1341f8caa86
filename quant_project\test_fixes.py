"""
测试修复脚本
用于测试修复后的性能分析器和交易环境
"""

import logging
import pandas as pd
import numpy as np
from core_logic.performance_analyzer import PerformanceAnalyzer
from core_logic.trading_environment import TradingEnvironment
from core_logic.data_handler import DataHandler
from core_logic.feature_engineer import FeatureEngineer

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_fixes')

def create_test_data(n_rows=100, n_features=10):
    """创建测试数据"""
    # 创建日期索引
    dates = pd.date_range(start='2020-01-01', periods=n_rows, freq='D')
    
    # 创建OHLCV数据
    ohlcv_data = {
        '开盘': np.random.normal(100, 10, n_rows),
        '最高': np.random.normal(105, 10, n_rows),
        '最低': np.random.normal(95, 10, n_rows),
        '收盘': np.random.normal(100, 10, n_rows),
        '成交量': np.random.normal(1000000, 200000, n_rows)
    }
    
    # 创建特征数据
    feature_data = {}
    for i in range(n_features):
        feature_data[f'feature_{i}'] = np.random.normal(0, 1, n_rows)
    
    # 合并数据
    data = {**ohlcv_data, **feature_data}
    df = pd.DataFrame(data, index=dates)
    
    # 确保最高价 >= 开盘价、收盘价、最低价
    df['最高'] = df[['开盘', '收盘', '最高']].max(axis=1)
    
    # 确保最低价 <= 开盘价、收盘价
    df['最低'] = df[['开盘', '收盘', '最低']].min(axis=1)
    
    return df

def test_performance_analyzer():
    """测试性能分析器修复"""
    logger.info("测试性能分析器修复")
    
    # 创建测试数据
    portfolio_values = pd.Series(
        np.linspace(100000, 120000, 100),
        index=pd.date_range(start='2020-01-01', periods=100, freq='D')
    )
    
    # 创建交易记录，但不包含profit列
    trades = []
    for i in range(10):
        buy_date = pd.Timestamp('2020-01-01') + pd.Timedelta(days=i*5)
        sell_date = buy_date + pd.Timedelta(days=3)
        trades.append({
            'symbol': 'TEST',
            'buy_date': buy_date,
            'sell_date': sell_date,
            'buy_price': 100 + i,
            'sell_price': 105 + i,
            'quantity': 100,
            'commission': 10
        })
    
    # 创建性能分析器
    analyzer = PerformanceAnalyzer()
    
    try:
        # 分析性能
        metrics = analyzer.analyze(trades, portfolio_values)
        logger.info(f"性能分析成功，指标: {metrics}")
        return True
    except Exception as e:
        logger.error(f"性能分析失败: {str(e)}")
        return False

def test_trading_environment():
    """测试交易环境修复"""
    logger.info("测试交易环境修复")
    
    # 创建测试数据
    df1 = create_test_data(n_rows=200, n_features=160)  # 160个特征
    
    # 创建交易环境
    env = TradingEnvironment(
        df_processed_data=df1,
        initial_capital=100000,
        commission_rate=0.0003,
        min_hold_days=3,
        window_size=20
    )
    
    # 重置环境并获取初始观测
    observation1, _ = env.reset()
    logger.info(f"初始观测形状: {observation1.shape}")
    
    # 创建特征数量不同的新测试数据
    df2 = create_test_data(n_rows=200, n_features=180)  # 180个特征
    
    # 更新环境的数据
    env.df = df2
    
    try:
        # 重置环境
        observation2, _ = env.reset()
        logger.info(f"特征数量变化后的观测形状: {observation2.shape}")
        
        # 执行一步
        action = 0  # 保持当前仓位
        observation3, reward, terminated, truncated, info = env.step(action)
        logger.info(f"执行一步后的观测形状: {observation3.shape}")
        
        return True
    except Exception as e:
        logger.error(f"交易环境测试失败: {str(e)}")
        return False

def test_real_data():
    """使用真实数据测试"""
    logger.info("使用真实数据测试")
    
    try:
        # 获取数据
        data_handler = DataHandler()
        data = data_handler.get_stock_data(
            stock_code="000001",  # 平安银行
            start_date="2020-01-01",
            end_date="2020-12-31",
            frequency="daily"
        )
        
        # 特征工程
        feature_engineer = FeatureEngineer()
        processed_data = feature_engineer.generate_features(data)
        logger.info(f"处理后的数据形状: {processed_data.shape}, 特征数量: {len(processed_data.columns)}")
        
        # 创建交易环境
        env = TradingEnvironment(
            df_processed_data=processed_data,
            initial_capital=100000,
            commission_rate=0.0003,
            min_hold_days=3,
            window_size=20
        )
        
        # 重置环境
        observation, _ = env.reset()
        logger.info(f"观测形状: {observation.shape}")
        
        # 执行几步交易
        for _ in range(5):
            action = np.random.randint(0, 3)  # 随机动作
            observation, reward, terminated, truncated, info = env.step(action)
            logger.info(f"执行动作 {action}, 奖励: {reward:.4f}, 组合价值: {info['portfolio_value']:.2f}")
            
            if terminated or truncated:
                break
        
        return True
    except Exception as e:
        logger.error(f"真实数据测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    logger.info("开始测试修复")
    
    # 测试性能分析器
    pa_result = test_performance_analyzer()
    logger.info(f"性能分析器测试结果: {'成功' if pa_result else '失败'}")
    
    # 测试交易环境
    te_result = test_trading_environment()
    logger.info(f"交易环境测试结果: {'成功' if te_result else '失败'}")
    
    # 使用真实数据测试
    rd_result = test_real_data()
    logger.info(f"真实数据测试结果: {'成功' if rd_result else '失败'}")
    
    logger.info("测试完成")
