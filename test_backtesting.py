#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
回测系统测试脚本
测试回测准确性、性能指标计算、交易成本和滑点
"""

import os
import sys
import pandas as pd
import numpy as np
import logging
import traceback
from datetime import datetime, timedelta
import matplotlib.pyplot as plt

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f'logs/backtesting_test_{datetime.now().strftime("%Y-%m-%d")}.log')
    ]
)

# 确保logs目录存在
os.makedirs('logs', exist_ok=True)

# 添加项目路径到系统路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入相关模块
from quant_trading.data.data_handler import DataHandler
from quant_trading.features.feature_engineer import FeatureEngineer
from quant_trading.trading.trading_environment import TradingEnvironment
from quant_trading.agents.drl_agent import DRLAgent
from quant_trading.evaluation.enhanced_performance_analyzer import EnhancedPerformanceAnalyzer
from quant_trading.utils.common import load_config

def test_backtesting_accuracy():
    """测试回测准确性"""
    print("\n===== 测试回测准确性 =====")

    # 获取测试数据
    handler = DataHandler(cache_dir='data_cache')
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=120)).strftime('%Y-%m-%d')
    stock = 'sh000001'

    try:
        # 获取原始数据
        data = handler.get_stock_data(stock, start_date, end_date)

        if data is None or data.empty:
            print("获取测试数据失败，无法进行回测测试")
            return

        # 加载配置
        env_config = load_config("quant_project/configs/env_config.yaml")
        agent_config = load_config("quant_project/configs/drl_agent_config.yaml")

        # 生成特征
        feature_engineer = FeatureEngineer(env_config.get('feature_config', {}))
        processed_data = feature_engineer.generate_features(data)

        # 数据分割
        split_config = env_config.get('data_split', {'train_ratio': 0.7, 'val_ratio': 0.15, 'test_ratio': 0.15})
        train_size = int(len(processed_data) * split_config['train_ratio'])
        val_size = int(len(processed_data) * split_config['val_ratio'])

        train_data = processed_data.iloc[:train_size]
        val_data = processed_data.iloc[train_size:train_size+val_size]
        test_data = processed_data.iloc[train_size+val_size:]

        print(f"数据分割: 训练集={len(train_data)}, 验证集={len(val_data)}, 测试集={len(test_data)}")

        # 创建环境配置
        env_params = {
            'df_processed_data': train_data,
            'initial_capital': env_config.get('initial_capital', 100000),
            'commission_rate': env_config.get('commission_rate', 0.0003),
            'min_hold_days': env_config.get('min_hold_days', 3),
            'allow_short': env_config.get('allow_short', False),
            'max_position': env_config.get('max_position', 1.0),
            'window_size': min(20, len(train_data) - 1),  # 确保窗口大小不超过数据长度
            'reward_config': env_config.get('reward_config', {})
        }

        # 创建DRL智能体
        agent_config['algorithm'] = 'PPO'  # 使用PPO算法
        drl_agent = DRLAgent(env_params, agent_config)

        # 训练模型（使用较小的步数）
        print("训练简单模型...")
        drl_agent.train(total_timesteps=2000, progress_bar=True)

        # 保存模型
        print("保存模型...")
        model_path = drl_agent.save_model(stock_code=stock)
        print(f"模型已保存到: {model_path}")

        # 在测试集上进行回测
        print("\n在测试集上进行回测...")

        # 创建测试环境
        test_env_params = {
            'df_processed_data': test_data,
            'initial_capital': env_config.get('initial_capital', 100000),
            'commission_rate': env_config.get('commission_rate', 0.0003),
            'min_hold_days': env_config.get('min_hold_days', 3),
            'allow_short': env_config.get('allow_short', False),
            'max_position': env_config.get('max_position', 1.0),
            'window_size': min(20, len(test_data) - 1)  # 确保窗口大小不超过数据长度
        }

        test_env = TradingEnvironment(**test_env_params)

        # 加载模型
        loaded_agent = DRLAgent.load_model(model_path)

        if loaded_agent is None:
            print("模型加载失败，无法进行回测")
            return

        # 执行回测
        observation, info = test_env.reset()
        done = False
        portfolio_history = []

        while not done:
            action = loaded_agent.predict_action(observation)
            observation, reward, terminated, truncated, info = test_env.step(action)

            # 记录每一步的信息
            portfolio_history.append({
                'date': test_data.index[test_env.current_step - 1],
                'portfolio_value': info['portfolio_value'],
                'position': info['position'],
                'action': action,
                'price': test_data.iloc[test_env.current_step - 1]['收盘'] if '收盘' in test_data.columns else 0
            })

            done = terminated or truncated

        # 转换为DataFrame
        portfolio_df = pd.DataFrame(portfolio_history)
        portfolio_df.set_index('date', inplace=True)

        # 创建性能分析器
        performance_analyzer = EnhancedPerformanceAnalyzer()

        # 分析性能
        backtest_results = performance_analyzer.analyze_performance(portfolio_df)

        # 打印回测结果
        print("\n回测结果:")
        print(f"最终资产: {portfolio_df['portfolio_value'].iloc[-1]:.2f}")
        print(f"总收益率: {backtest_results['total_return']:.2%}")
        print(f"年化收益率: {backtest_results['annual_return']:.2%}")
        print(f"夏普比率: {backtest_results['sharpe_ratio']:.2f}")
        print(f"最大回撤: {backtest_results['max_drawdown']:.2%}")
        print(f"索提诺比率: {backtest_results['sortino_ratio']:.2f}")
        print(f"卡玛比率: {backtest_results['calmar_ratio']:.2f}")

        if 'total_trades' in backtest_results:
            print(f"交易次数: {backtest_results['total_trades']}")
            print(f"胜率: {backtest_results['win_rate']:.2%}")

        # 生成性能报告
        plots_dir = "plots"
        if not os.path.exists(plots_dir):
            os.makedirs(plots_dir)

        report = performance_analyzer.generate_performance_report(portfolio_df, output_path=plots_dir)

        # 保存图表
        for chart_name, fig in report['charts'].items():
            fig_path = os.path.join(plots_dir, f"{chart_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
            fig.savefig(fig_path)
            print(f"图表已保存到: {fig_path}")

        # 分析风险
        risk_analysis = performance_analyzer.analyze_risk(portfolio_df)

        print("\n风险分析:")
        print(f"历史VaR (95%): {risk_analysis['var_historical']:.2%}")
        print(f"历史CVaR (95%): {risk_analysis['cvar_historical']:.2%}")
        print(f"参数化VaR (95%): {risk_analysis['var_parametric']:.2%}")

        # 关闭所有图表
        plt.close('all')

    except Exception as e:
        print(f"测试回测准确性时出错: {str(e)}")
        print(traceback.format_exc())

def test_performance_metrics():
    """测试性能指标计算"""
    print("\n===== 测试性能指标计算 =====")

    try:
        # 创建性能分析器
        performance_analyzer = EnhancedPerformanceAnalyzer()

        # 创建模拟的回测数据
        dates = pd.date_range(start='2023-01-01', periods=100, freq='D')

        # 情景1: 稳定上涨的资产曲线
        portfolio_values1 = np.linspace(100000, 120000, 100)  # 线性增长20%

        # 创建投资组合历史数据
        portfolio_history1 = pd.DataFrame({
            'portfolio_value': portfolio_values1,
            'daily_returns': np.concatenate([[0], np.diff(portfolio_values1) / portfolio_values1[:-1]])
        }, index=dates)

        # 计算性能指标
        print("\n情景1: 稳定上涨的资产曲线")
        metrics1 = performance_analyzer.analyze_performance(portfolio_history1)

        print(f"总收益率: {metrics1['total_return']:.2%}")
        print(f"年化收益率: {metrics1['annual_return']:.2%}")
        print(f"夏普比率: {metrics1['sharpe_ratio']:.2f}")
        print(f"最大回撤: {metrics1['max_drawdown']:.2%}")
        print(f"索提诺比率: {metrics1['sortino_ratio']:.2f}")
        print(f"卡玛比率: {metrics1['calmar_ratio']:.2f}")

        # 情景2: 波动上涨的资产曲线
        x = np.linspace(0, 4*np.pi, 100)
        portfolio_values2 = 100000 + 10000 * np.sin(x) + 100 * x  # 波动上涨

        # 创建投资组合历史数据
        portfolio_history2 = pd.DataFrame({
            'portfolio_value': portfolio_values2,
            'daily_returns': np.concatenate([[0], np.diff(portfolio_values2) / portfolio_values2[:-1]])
        }, index=dates)

        # 计算性能指标
        print("\n情景2: 波动上涨的资产曲线")
        metrics2 = performance_analyzer.analyze_performance(portfolio_history2)

        print(f"总收益率: {metrics2['total_return']:.2%}")
        print(f"年化收益率: {metrics2['annual_return']:.2%}")
        print(f"夏普比率: {metrics2['sharpe_ratio']:.2f}")
        print(f"最大回撤: {metrics2['max_drawdown']:.2%}")
        print(f"索提诺比率: {metrics2['sortino_ratio']:.2f}")
        print(f"卡玛比率: {metrics2['calmar_ratio']:.2f}")

        # 情景3: 先上涨后下跌的资产曲线
        portfolio_values3 = np.concatenate([
            np.linspace(100000, 130000, 50),  # 前半段上涨30%
            np.linspace(130000, 110000, 50)   # 后半段下跌约15.4%
        ])

        # 创建投资组合历史数据
        portfolio_history3 = pd.DataFrame({
            'portfolio_value': portfolio_values3,
            'daily_returns': np.concatenate([[0], np.diff(portfolio_values3) / portfolio_values3[:-1]])
        }, index=dates)

        # 计算性能指标
        print("\n情景3: 先上涨后下跌的资产曲线")
        metrics3 = performance_analyzer.analyze_performance(portfolio_history3)

        print(f"总收益率: {metrics3['total_return']:.2%}")
        print(f"年化收益率: {metrics3['annual_return']:.2%}")
        print(f"夏普比率: {metrics3['sharpe_ratio']:.2f}")
        print(f"最大回撤: {metrics3['max_drawdown']:.2%}")
        print(f"索提诺比率: {metrics3['sortino_ratio']:.2f}")
        print(f"卡玛比率: {metrics3['calmar_ratio']:.2f}")

        # 风险分析
        print("\n风险分析:")
        risk_analysis = performance_analyzer.analyze_risk(portfolio_history3)
        print(f"历史VaR (95%): {risk_analysis['var_historical']:.2%}")
        print(f"历史CVaR (95%): {risk_analysis['cvar_historical']:.2%}")
        print(f"参数化VaR (95%): {risk_analysis['var_parametric']:.2%}")

        # 生成性能报告
        plots_dir = "plots"
        if not os.path.exists(plots_dir):
            os.makedirs(plots_dir)

        report = performance_analyzer.generate_performance_report(portfolio_history3, output_path=plots_dir)

        # 保存图表
        for chart_name, fig in report['charts'].items():
            fig_path = os.path.join(plots_dir, f"metrics_{chart_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
            fig.savefig(fig_path)
            print(f"图表已保存到: {fig_path}")

        # 关闭所有图表
        plt.close('all')

    except Exception as e:
        print(f"测试性能指标计算时出错: {str(e)}")
        print(traceback.format_exc())

def test_transaction_costs():
    """测试交易成本和滑点"""
    print("\n===== 测试交易成本和滑点 =====")

    # 获取测试数据
    handler = DataHandler(cache_dir='data_cache')
    end_date = datetime.now().strftime('%Y-%m-%d')
    start_date = (datetime.now() - timedelta(days=120)).strftime('%Y-%m-%d')
    stock = 'sh000001'

    try:
        # 获取原始数据
        data = handler.get_stock_data(stock, start_date, end_date)

        if data is None or data.empty:
            print("获取测试数据失败，无法进行交易成本测试")
            return

        # 加载配置
        env_config = load_config("quant_project/configs/env_config.yaml")
        agent_config = load_config("quant_project/configs/drl_agent_config.yaml")

        # 生成特征
        feature_engineer = FeatureEngineer(env_config.get('feature_config', {}))
        processed_data = feature_engineer.generate_features(data)

        # 创建性能分析器
        performance_analyzer = EnhancedPerformanceAnalyzer()

        # 训练一个简单模型用于测试
        print("\n训练简单模型用于测试...")

        # 创建环境配置
        env_params = {
            'df_processed_data': processed_data,
            'initial_capital': env_config.get('initial_capital', 100000),
            'commission_rate': env_config.get('commission_rate', 0.0003),
            'min_hold_days': env_config.get('min_hold_days', 3),
            'allow_short': env_config.get('allow_short', False),
            'max_position': env_config.get('max_position', 1.0),
            'window_size': min(20, len(processed_data) - 1)  # 确保窗口大小不超过数据长度
        }

        # 创建DRL智能体
        agent_config['algorithm'] = 'PPO'  # 使用PPO算法
        drl_agent = DRLAgent(env_params, agent_config)

        # 训练模型（使用较小的步数）
        drl_agent.train(total_timesteps=2000, progress_bar=True)

        # 保存模型
        model_path = drl_agent.save_model(stock_code=stock)
        print(f"模型已保存到: {model_path}")

        # 测试不同交易成本设置
        commission_rates = [0.0, 0.0003, 0.001, 0.003]

        results = []

        for commission_rate in commission_rates:
            print(f"\n测试手续费率: {commission_rate}")

            # 创建测试环境
            test_env_params = env_params.copy()
            test_env_params['commission_rate'] = commission_rate
            test_env = TradingEnvironment(**test_env_params)

            # 加载模型
            loaded_agent = DRLAgent.load_model(model_path)

            if loaded_agent is None:
                print("模型加载失败，无法进行测试")
                continue

            # 执行回测
            observation, info = test_env.reset()
            done = False
            portfolio_history = []

            while not done:
                action = loaded_agent.predict_action(observation)
                observation, reward, terminated, truncated, info = test_env.step(action)

                # 记录每一步的信息
                portfolio_history.append({
                    'date': processed_data.index[test_env.current_step - 1],
                    'portfolio_value': info['portfolio_value'],
                    'position': info.get('position', 0),
                    'action': action,
                    'price': processed_data.iloc[test_env.current_step - 1]['收盘'] if '收盘' in processed_data.columns else 0,
                    'commission': info.get('commission', 0)
                })

                done = terminated or truncated

            # 转换为DataFrame
            portfolio_df = pd.DataFrame(portfolio_history)
            portfolio_df.set_index('date', inplace=True)

            # 分析性能
            metrics = performance_analyzer.analyze_performance(portfolio_df)

            # 计算总手续费
            total_commission = portfolio_df['commission'].sum() if 'commission' in portfolio_df.columns else 0

            # 记录结果
            results.append({
                'commission_rate': commission_rate,
                'final_value': portfolio_df['portfolio_value'].iloc[-1],
                'total_return': metrics['total_return'],
                'annual_return': metrics['annual_return'],
                'sharpe_ratio': metrics['sharpe_ratio'],
                'max_drawdown': metrics['max_drawdown'],
                'total_commission': total_commission,
                'total_trades': metrics.get('total_trades', 0)
            })

            print(f"最终资产: {portfolio_df['portfolio_value'].iloc[-1]:.2f}")
            print(f"总收益率: {metrics['total_return']:.2%}")
            print(f"年化收益率: {metrics['annual_return']:.2%}")
            print(f"夏普比率: {metrics['sharpe_ratio']:.2f}")
            print(f"最大回撤: {metrics['max_drawdown']:.2%}")
            print(f"总手续费: {total_commission:.2f}")
            if 'total_trades' in metrics:
                print(f"交易次数: {metrics['total_trades']}")

        # 创建结果数据框
        results_df = pd.DataFrame(results)

        # 绘制交易成本影响
        plt.figure(figsize=(12, 8))

        # 绘制手续费率对总收益的影响
        plt.subplot(2, 2, 1)
        plt.plot(results_df['commission_rate'], results_df['total_return'], marker='o')
        plt.title('手续费率对总收益的影响')
        plt.xlabel('手续费率')
        plt.ylabel('总收益率')
        plt.grid(True)

        # 绘制手续费率对年化收益的影响
        plt.subplot(2, 2, 2)
        plt.plot(results_df['commission_rate'], results_df['annual_return'], marker='o')
        plt.title('手续费率对年化收益的影响')
        plt.xlabel('手续费率')
        plt.ylabel('年化收益率')
        plt.grid(True)

        # 绘制手续费率对夏普比率的影响
        plt.subplot(2, 2, 3)
        plt.plot(results_df['commission_rate'], results_df['sharpe_ratio'], marker='o')
        plt.title('手续费率对夏普比率的影响')
        plt.xlabel('手续费率')
        plt.ylabel('夏普比率')
        plt.grid(True)

        # 绘制总手续费与总收益的关系
        plt.subplot(2, 2, 4)
        plt.scatter(results_df['total_commission'], results_df['total_return'], c=results_df['commission_rate'], cmap='viridis')
        plt.colorbar(label='手续费率')
        plt.title('总手续费与总收益的关系')
        plt.xlabel('总手续费')
        plt.ylabel('总收益率')
        plt.grid(True)

        plt.tight_layout()

        plots_dir = "plots"
        if not os.path.exists(plots_dir):
            os.makedirs(plots_dir)

        plt.savefig(os.path.join(plots_dir, f"transaction_costs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"))
        print(f"交易成本分析可视化已保存到 plots/transaction_costs_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")

        # 关闭所有图表
        plt.close('all')

    except Exception as e:
        print(f"测试交易成本和滑点时出错: {str(e)}")
        print(traceback.format_exc())

def run_all_tests():
    """运行所有回测系统测试"""
    try:
        test_backtesting_accuracy()
        test_performance_metrics()
        test_transaction_costs()
        print("\n===== 所有回测系统测试完成 =====")
        return True
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        print(traceback.format_exc())
        return False

if __name__ == '__main__':
    run_all_tests()
