#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
UI测试脚本
根据UI测试方案测试量化交易系统的UI功能
"""

import os
import sys
import time
import logging
import subprocess
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import traceback

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join('logs', 'ui_test.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('ui_test')

# 导入项目模块
try:
    from quant_trading.data.data_handler import DataHandler
    from quant_trading.features.feature_engineer import FeatureEngineer
    from quant_trading.trading.trading_environment import TradingEnvironment
    from quant_trading.agents.drl_agent import DRLAgent
    from quant_trading.evaluation.performance_analyzer import PerformanceAnalyzer
    from quant_trading.utils.common import setup_logger, is_gpu_available
    logger.info("成功导入所有核心模块")
except ImportError as e:
    logger.error(f"导入模块失败: {str(e)}")
    sys.exit(1)

def test_app_startup():
    """测试应用启动"""
    logger.info("测试应用启动")
    
    try:
        # 使用subprocess启动应用，不阻塞当前进程
        process = subprocess.Popen(
            [sys.executable, "-m", "streamlit", "run", "main_app.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待几秒钟让应用启动
        time.sleep(5)
        
        # 检查进程是否仍在运行
        if process.poll() is None:
            logger.info("应用成功启动")
            
            # 终止进程
            process.terminate()
            process.wait(timeout=5)
            
            return True
        else:
            # 获取错误输出
            stdout, stderr = process.communicate()
            logger.error(f"应用启动失败: {stderr}")
            return False
    except Exception as e:
        logger.error(f"测试应用启动时出错: {str(e)}")
        return False

def test_module_imports():
    """测试模块导入"""
    logger.info("测试模块导入")
    
    modules = {
        "DataHandler": DataHandler,
        "FeatureEngineer": FeatureEngineer,
        "TradingEnvironment": TradingEnvironment,
        "DRLAgent": DRLAgent,
        "PerformanceAnalyzer": PerformanceAnalyzer
    }
    
    all_imported = True
    for name, module in modules.items():
        if module is not None:
            logger.info(f"成功导入 {name}")
        else:
            logger.error(f"导入 {name} 失败")
            all_imported = False
    
    return all_imported

def test_data_handler():
    """测试数据处理模块"""
    logger.info("测试数据处理模块")
    
    try:
        # 创建数据处理器实例
        data_handler = DataHandler()
        
        # 测试不同股票代码
        stock_codes = ['sh000001', 'sz399001', 'index_000300']
        
        # 测试不同日期范围
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_dates = [
            (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'),
            (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d'),
            (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')
        ]
        
        # 测试不同频率
        frequencies = ['日线', '周线', '月线']
        
        results = []
        
        # 测试组合
        for stock_code in stock_codes:
            for start_date in start_dates:
                for frequency in frequencies:
                    try:
                        logger.info(f"获取数据: {stock_code}, {start_date} 到 {end_date}, {frequency}")
                        data = data_handler.get_stock_data(
                            stock_code=stock_code,
                            start_date=start_date,
                            end_date=end_date,
                            frequency=frequency
                        )
                        
                        if data is not None and not data.empty:
                            logger.info(f"成功获取数据: {len(data)} 条记录")
                            results.append({
                                'stock_code': stock_code,
                                'start_date': start_date,
                                'end_date': end_date,
                                'frequency': frequency,
                                'success': True,
                                'records': len(data)
                            })
                        else:
                            logger.warning(f"获取数据失败: 数据为空")
                            results.append({
                                'stock_code': stock_code,
                                'start_date': start_date,
                                'end_date': end_date,
                                'frequency': frequency,
                                'success': False,
                                'error': '数据为空'
                            })
                    except Exception as e:
                        logger.error(f"获取数据出错: {str(e)}")
                        results.append({
                            'stock_code': stock_code,
                            'start_date': start_date,
                            'end_date': end_date,
                            'frequency': frequency,
                            'success': False,
                            'error': str(e)
                        })
        
        # 测试数据缓存
        logger.info("测试数据缓存")
        cache_test_code = 'sh000001'
        cache_test_start = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
        
        # 第一次获取数据（可能从API获取）
        start_time = time.time()
        data1 = data_handler.get_stock_data(
            stock_code=cache_test_code,
            start_date=cache_test_start,
            end_date=end_date,
            frequency='日线'
        )
        first_fetch_time = time.time() - start_time
        
        # 第二次获取数据（应该从缓存获取）
        start_time = time.time()
        data2 = data_handler.get_stock_data(
            stock_code=cache_test_code,
            start_date=cache_test_start,
            end_date=end_date,
            frequency='日线'
        )
        second_fetch_time = time.time() - start_time
        
        cache_working = second_fetch_time < first_fetch_time
        logger.info(f"缓存测试: 第一次获取时间 {first_fetch_time:.2f}秒, 第二次获取时间 {second_fetch_time:.2f}秒")
        logger.info(f"缓存是否工作: {cache_working}")
        
        # 测试异常处理
        logger.info("测试异常处理")
        try:
            invalid_data = data_handler.get_stock_data(
                stock_code='invalid_code',
                start_date=start_dates[0],
                end_date=end_date,
                frequency='日线'
            )
            logger.warning("无效股票代码未引发异常")
            exception_handled = False
        except Exception as e:
            logger.info(f"正确处理了无效股票代码: {str(e)}")
            exception_handled = True
        
        # 保存测试结果
        results_df = pd.DataFrame(results)
        os.makedirs('test_results', exist_ok=True)
        results_df.to_csv('test_results/data_handler_test.csv', index=False)
        
        success_rate = results_df['success'].mean() * 100
        logger.info(f"数据处理模块测试完成，成功率: {success_rate:.2f}%")
        
        return {
            'success_rate': success_rate,
            'cache_working': cache_working,
            'exception_handled': exception_handled
        }
    
    except Exception as e:
        logger.error(f"测试数据处理模块时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            'success_rate': 0,
            'cache_working': False,
            'exception_handled': False
        }

def test_feature_engineer(sample_data):
    """测试特征工程模块"""
    logger.info("测试特征工程模块")
    
    try:
        # 创建特征工程器实例
        feature_engineer = FeatureEngineer()
        
        # 测试不同特征配置
        feature_configs = [
            # 基本配置
            {
                'moving_averages': {'windows': [5, 10, 20]},
                'rsi': {'window': 14},
                'macd': {'fast': 12, 'slow': 26, 'signal': 9}
            },
            # 扩展配置
            {
                'moving_averages': {'windows': [5, 10, 20, 30, 60]},
                'rsi': {'window': 14},
                'macd': {'fast': 12, 'slow': 26, 'signal': 9},
                'bollinger_bands': {'window': 20, 'num_std': 2},
                'stochastic': {'k_window': 14, 'd_window': 3}
            }
        ]
        
        results = []
        
        for i, config in enumerate(feature_configs):
            try:
                logger.info(f"测试特征配置 {i+1}")
                feature_engineer.feature_config = config
                
                processed_data = feature_engineer.generate_features(sample_data)
                
                if processed_data is not None and not processed_data.empty:
                    feature_count = len(processed_data.columns) - len(sample_data.columns)
                    logger.info(f"成功生成特征: {feature_count} 个新特征")
                    results.append({
                        'config_id': i+1,
                        'success': True,
                        'feature_count': feature_count
                    })
                else:
                    logger.warning(f"生成特征失败: 数据为空")
                    results.append({
                        'config_id': i+1,
                        'success': False,
                        'error': '数据为空'
                    })
            except Exception as e:
                logger.error(f"生成特征出错: {str(e)}")
                results.append({
                    'config_id': i+1,
                    'success': False,
                    'error': str(e)
                })
        
        # 保存测试结果
        results_df = pd.DataFrame(results)
        os.makedirs('test_results', exist_ok=True)
        results_df.to_csv('test_results/feature_engineer_test.csv', index=False)
        
        success_rate = results_df['success'].mean() * 100
        logger.info(f"特征工程模块测试完成，成功率: {success_rate:.2f}%")
        
        return {
            'success_rate': success_rate,
            'feature_configs_tested': len(feature_configs)
        }
    
    except Exception as e:
        logger.error(f"测试特征工程模块时出错: {str(e)}")
        logger.error(traceback.format_exc())
        return {
            'success_rate': 0,
            'feature_configs_tested': 0
        }

def run_all_tests():
    """运行所有测试"""
    logger.info("开始运行所有UI测试")
    
    # 确保测试目录存在
    os.makedirs('test_results', exist_ok=True)
    os.makedirs('logs', exist_ok=True)
    
    # 测试应用启动
    app_startup = test_app_startup()
    
    # 测试模块导入
    module_imports = test_module_imports()
    
    # 获取样本数据用于后续测试
    try:
        data_handler = DataHandler()
        sample_data = data_handler.get_stock_data(
            stock_code='sh000001',
            start_date='2023-01-01',
            end_date='2023-12-31',
            frequency='日线'
        )
        logger.info(f"获取样本数据成功: {len(sample_data)} 条记录")
    except Exception as e:
        logger.error(f"获取样本数据失败: {str(e)}")
        sample_data = None
    
    # 测试数据处理模块
    data_handler_results = test_data_handler()
    
    # 如果有样本数据，测试特征工程模块
    feature_engineer_results = None
    if sample_data is not None and not sample_data.empty:
        feature_engineer_results = test_feature_engineer(sample_data)
    else:
        logger.warning("无法测试特征工程模块: 样本数据不可用")
    
    # 汇总测试结果
    results = {
        '应用启动': app_startup,
        '模块导入': module_imports,
        '数据处理模块': data_handler_results,
        '特征工程模块': feature_engineer_results
    }
    
    logger.info(f"所有测试完成，结果: {results}")
    
    return results

if __name__ == "__main__":
    run_all_tests()
