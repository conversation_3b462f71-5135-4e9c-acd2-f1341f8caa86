"""
性能分析模块
负责计算交易策略的性能指标
"""

import logging
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime

class PerformanceAnalyzer:
    """
    性能分析类
    负责计算交易策略的性能指标
    """

    def __init__(self, risk_free_rate=0.0):
        """
        初始化性能分析器

        参数:
            risk_free_rate (float): 无风险利率，年化
        """
        self.logger = logging.getLogger('drl_trading')
        self.risk_free_rate = risk_free_rate

    def analyze(self, trades, portfolio_values, benchmark_values=None):
        """
        分析交易策略性能

        参数:
            trades (list): 交易记录列表
            portfolio_values (pandas.Series): 组合价值序列，索引为日期
            benchmark_values (pandas.Series, optional): 基准价值序列，索引为日期

        返回:
            dict: 性能指标字典
        """
        # 转换交易记录为DataFrame
        trades_df = pd.DataFrame(trades) if trades else pd.DataFrame()

        # 计算收益率序列
        returns = portfolio_values.pct_change().dropna()

        # 如果有基准，计算基准收益率序列
        benchmark_returns = None
        if benchmark_values is not None:
            benchmark_returns = benchmark_values.pct_change().dropna()

        # 计算各项指标
        metrics = {}

        # 总收益率
        metrics['total_return'] = self.calculate_total_return(portfolio_values)

        # 年化收益率
        metrics['annualized_return'] = self.calculate_annualized_return(portfolio_values)

        # 最大回撤
        drawdown_info = self.calculate_max_drawdown(portfolio_values)
        metrics['max_drawdown'] = drawdown_info['max_drawdown']
        metrics['max_drawdown_duration'] = drawdown_info['duration']
        metrics['max_drawdown_start'] = drawdown_info['start_date']
        metrics['max_drawdown_end'] = drawdown_info['end_date']

        # 夏普比率
        metrics['sharpe_ratio'] = self.calculate_sharpe_ratio(returns)

        # 索提诺比率
        metrics['sortino_ratio'] = self.calculate_sortino_ratio(returns)

        # 卡玛比率
        metrics['calmar_ratio'] = self.calculate_calmar_ratio(metrics['annualized_return'], metrics['max_drawdown'])

        # 交易统计
        if not trades_df.empty:
            trade_stats = self.calculate_trade_statistics(trades_df)
            metrics.update(trade_stats)

        # 月度/年度收益率统计
        monthly_returns = self.calculate_period_returns(portfolio_values, period='M')
        yearly_returns = self.calculate_period_returns(portfolio_values, period='Y')

        metrics['monthly_returns'] = monthly_returns
        metrics['yearly_returns'] = yearly_returns
        metrics['avg_monthly_return'] = monthly_returns.mean()
        metrics['std_monthly_return'] = monthly_returns.std()

        # 与基准对比
        if benchmark_returns is not None:
            metrics['benchmark_total_return'] = self.calculate_total_return(benchmark_values)
            metrics['benchmark_annualized_return'] = self.calculate_annualized_return(benchmark_values)
            metrics['benchmark_max_drawdown'] = self.calculate_max_drawdown(benchmark_values)['max_drawdown']
            metrics['benchmark_sharpe_ratio'] = self.calculate_sharpe_ratio(benchmark_returns)

            # 计算阿尔法、贝塔
            alpha_beta = self.calculate_alpha_beta(returns, benchmark_returns)
            metrics['alpha'] = alpha_beta['alpha']
            metrics['beta'] = alpha_beta['beta']

            # 信息比率
            metrics['information_ratio'] = self.calculate_information_ratio(returns, benchmark_returns)

        return metrics

    def calculate_total_return(self, portfolio_values):
        """
        计算总收益率

        参数:
            portfolio_values (pandas.Series): 组合价值序列

        返回:
            float: 总收益率
        """
        if len(portfolio_values) < 2:
            return 0.0

        return (portfolio_values.iloc[-1] / portfolio_values.iloc[0]) - 1

    def calculate_annualized_return(self, portfolio_values):
        """
        计算年化收益率

        参数:
            portfolio_values (pandas.Series): 组合价值序列

        返回:
            float: 年化收益率
        """
        if len(portfolio_values) < 2:
            return 0.0

        total_return = self.calculate_total_return(portfolio_values)

        # 计算交易天数
        start_date = portfolio_values.index[0]
        end_date = portfolio_values.index[-1]
        days = (end_date - start_date).days

        # 避免除以零
        if days <= 0:
            return 0.0

        # 计算年化收益率
        years = days / 365
        return (1 + total_return) ** (1 / years) - 1

    def calculate_max_drawdown(self, portfolio_values):
        """
        计算最大回撤

        参数:
            portfolio_values (pandas.Series): 组合价值序列

        返回:
            dict: 最大回撤信息
        """
        if len(portfolio_values) < 2:
            return {
                'max_drawdown': 0.0,
                'duration': 0,
                'start_date': None,
                'end_date': None
            }

        # 计算累计最大值
        running_max = np.maximum.accumulate(portfolio_values)
        # 计算回撤序列
        drawdown = (portfolio_values - running_max) / running_max
        # 找到最大回撤及其位置
        max_drawdown = drawdown.min()
        end_idx = drawdown.idxmin()

        # 找到最大回撤的开始点
        start_idx = portfolio_values.loc[:end_idx].idxmax()

        # 计算回撤持续时间
        duration = (end_idx - start_idx).days

        return {
            'max_drawdown': max_drawdown,
            'duration': duration,
            'start_date': start_idx,
            'end_date': end_idx
        }

    def calculate_sharpe_ratio(self, returns, periods_per_year=252):
        """
        计算夏普比率

        参数:
            returns (pandas.Series): 收益率序列
            periods_per_year (int): 每年的周期数，日线=252，周线=52，月线=12

        返回:
            float: 夏普比率
        """
        if len(returns) < 2:
            return 0.0

        # 计算超额收益
        excess_returns = returns - self.risk_free_rate / periods_per_year

        # 计算夏普比率
        sharpe_ratio = np.sqrt(periods_per_year) * excess_returns.mean() / (excess_returns.std() + 1e-10)

        return sharpe_ratio

    def calculate_sortino_ratio(self, returns, periods_per_year=252):
        """
        计算索提诺比率

        参数:
            returns (pandas.Series): 收益率序列
            periods_per_year (int): 每年的周期数，日线=252，周线=52，月线=12

        返回:
            float: 索提诺比率
        """
        if len(returns) < 2:
            return 0.0

        # 计算超额收益
        excess_returns = returns - self.risk_free_rate / periods_per_year

        # 计算下行标准差
        negative_returns = excess_returns[excess_returns < 0]
        downside_std = negative_returns.std() if len(negative_returns) > 0 else 0

        # 计算索提诺比率
        sortino_ratio = np.sqrt(periods_per_year) * excess_returns.mean() / (downside_std + 1e-10)

        return sortino_ratio

    def calculate_calmar_ratio(self, annualized_return, max_drawdown):
        """
        计算卡玛比率

        参数:
            annualized_return (float): 年化收益率
            max_drawdown (float): 最大回撤

        返回:
            float: 卡玛比率
        """
        if max_drawdown >= 0:
            return 0.0

        return -annualized_return / max_drawdown

    def calculate_trade_statistics(self, trades_df):
        """
        计算交易统计指标

        参数:
            trades_df (pandas.DataFrame): 交易记录DataFrame

        返回:
            dict: 交易统计指标
        """
        if trades_df.empty:
            return {
                'total_trades': 0,
                'win_rate': 0.0,
                'avg_profit': 0.0,
                'avg_loss': 0.0,
                'profit_loss_ratio': 0.0,
                'profit_factor': 0.0,
                'avg_holding_period': 0
            }

        # 计算总交易次数
        total_trades = len(trades_df)

        # 确保日期列是datetime类型
        if 'buy_date' in trades_df.columns and not pd.api.types.is_datetime64_any_dtype(trades_df['buy_date']):
            trades_df['buy_date'] = pd.to_datetime(trades_df['buy_date'])
        if 'sell_date' in trades_df.columns and not pd.api.types.is_datetime64_any_dtype(trades_df['sell_date']):
            trades_df['sell_date'] = pd.to_datetime(trades_df['sell_date'])

        # 检查交易记录的格式
        if 'action' in trades_df.columns:
            # 如果有action列，按action分类
            buy_trades = trades_df[trades_df['action'] == 'buy']
            sell_trades = trades_df[trades_df['action'] == 'sell']

            # 如果没有完整的交易（买入和卖出），返回默认值
            if len(buy_trades) == 0 or len(sell_trades) == 0:
                return {
                    'total_trades': total_trades,
                    'win_rate': 0.0,
                    'avg_profit': 0.0,
                    'avg_loss': 0.0,
                    'profit_loss_ratio': 0.0,
                    'profit_factor': 0.0,
                    'avg_holding_period': 0
                }
        elif 'direction' in trades_df.columns:
            # 如果有direction列，按direction分类
            long_trades = trades_df[trades_df['direction'] == 'long']
            short_trades = trades_df[trades_df['direction'] == 'short']

            # 如果没有完整的交易，返回默认值
            if len(long_trades) == 0 and len(short_trades) == 0:
                return {
                    'total_trades': total_trades,
                    'win_rate': 0.0,
                    'avg_profit': 0.0,
                    'avg_loss': 0.0,
                    'profit_loss_ratio': 0.0,
                    'profit_factor': 0.0,
                    'avg_holding_period': 0
                }
        else:
            # 如果既没有action列也没有direction列，直接使用profit列进行分析
            if 'profit' not in trades_df.columns:
                return {
                    'total_trades': total_trades,
                    'win_rate': 0.0,
                    'avg_profit': 0.0,
                    'avg_loss': 0.0,
                    'profit_loss_ratio': 0.0,
                    'profit_factor': 0.0,
                    'avg_holding_period': 0
                }

        # 计算每笔交易的盈亏
        # 注意：这里假设交易记录中有盈亏信息，如果没有，需要计算
        # 这部分逻辑已移至下方，确保trades_df有profit列的代码块中

        # 确保trades_df有profit列
        if 'profit' not in trades_df.columns:
            self.logger.info("交易记录中没有profit列，尝试从交易记录计算")

            # 检查是否有action列，这表示是TradingEnvironment生成的交易记录
            if 'action' in trades_df.columns:
                # 分离买入和卖出交易
                buy_trades = trades_df[trades_df['action'] == 'buy'].copy()
                sell_trades = trades_df[trades_df['action'] == 'sell'].copy()

                # 如果买入和卖出交易数量不匹配，可能是因为最后一笔买入没有卖出
                if len(buy_trades) > len(sell_trades):
                    self.logger.warning(f"买入交易数量({len(buy_trades)})大于卖出交易数量({len(sell_trades)})，可能有未平仓的交易")
                    # 只保留有对应卖出的买入交易
                    buy_trades = buy_trades.iloc[:len(sell_trades)]

                # 创建配对交易记录
                paired_trades = []

                # 遍历所有卖出交易
                for i, sell_trade in sell_trades.iterrows():
                    # 找到对应的买入交易（假设买入和卖出是按顺序配对的）
                    buy_idx = buy_trades.index[len(paired_trades)]
                    buy_trade = buy_trades.loc[buy_idx]

                    # 计算利润
                    buy_price = buy_trade['price']
                    sell_price = sell_trade['price']
                    shares = buy_trade['shares']  # 假设买入和卖出的股数相同
                    buy_commission = buy_trade['commission']
                    sell_commission = sell_trade['commission']

                    # 计算总利润 = 卖出收入 - 买入成本 - 总手续费
                    profit = (sell_price * shares) - (buy_price * shares) - buy_commission - sell_commission

                    # 创建配对交易记录
                    paired_trade = {
                        'buy_date': buy_trade['date'],
                        'sell_date': sell_trade['date'],
                        'buy_price': buy_price,
                        'sell_price': sell_price,
                        'shares': shares,
                        'buy_commission': buy_commission,
                        'sell_commission': sell_commission,
                        'profit': profit
                    }
                    paired_trades.append(paired_trade)

                # 转换为DataFrame
                if paired_trades:
                    trades_df = pd.DataFrame(paired_trades)
                    self.logger.info(f"成功配对并计算了{len(trades_df)}笔完整交易的利润")
                else:
                    self.logger.warning("没有找到可配对的交易")
                    return {
                        'total_trades': total_trades,
                        'win_rate': 0.0,
                        'avg_profit': 0.0,
                        'avg_loss': 0.0,
                        'profit_loss_ratio': 0.0,
                        'profit_factor': 0.0,
                        'avg_holding_period': 0
                    }
            # 尝试从价格和数量计算profit（旧方法，保留兼容性）
            elif all(col in trades_df.columns for col in ['buy_price', 'sell_price']):
                # 检查是否有quantity或shares列
                quantity_col = None
                for col in ['quantity', 'shares', 'volume']:
                    if col in trades_df.columns:
                        quantity_col = col
                        break

                if quantity_col:
                    trades_df['profit'] = (trades_df['sell_price'] - trades_df['buy_price']) * trades_df[quantity_col]
                else:
                    # 如果没有数量列，假设每次交易1单位
                    trades_df['profit'] = trades_df['sell_price'] - trades_df['buy_price']

                # 如果有commission列，减去手续费
                if 'commission' in trades_df.columns:
                    trades_df['profit'] = trades_df['profit'] - trades_df['commission']

                self.logger.info(f"已计算profit列，共{len(trades_df)}条交易记录")
            else:
                self.logger.warning("无法计算profit，缺少必要的交易信息")
                return {
                    'total_trades': total_trades,
                    'win_rate': 0.0,
                    'avg_profit': 0.0,
                    'avg_loss': 0.0,
                    'profit_loss_ratio': 0.0,
                    'profit_factor': 0.0,
                    'avg_holding_period': 0
                }

        # 计算盈利和亏损交易
        profitable_trades = trades_df[trades_df['profit'] > 0]
        loss_trades = trades_df[trades_df['profit'] <= 0]

        # 计算胜率
        win_rate = len(profitable_trades) / total_trades if total_trades > 0 else 0.0

        # 计算平均盈利和平均亏损
        avg_profit = profitable_trades['profit'].mean() if len(profitable_trades) > 0 else 0.0
        avg_loss = loss_trades['profit'].mean() if len(loss_trades) > 0 else 0.0

        # 计算盈亏比
        profit_loss_ratio = abs(avg_profit / avg_loss) if avg_loss != 0 else 0.0

        # 计算盈利因子
        total_profit = profitable_trades['profit'].sum() if len(profitable_trades) > 0 else 0.0
        total_loss = abs(loss_trades['profit'].sum()) if len(loss_trades) > 0 else 0.0
        profit_factor = total_profit / total_loss if total_loss > 0 else 0.0

        # 计算平均持仓周期
        if 'buy_date' in trades_df.columns and 'sell_date' in trades_df.columns:
            trades_df['holding_period'] = (trades_df['sell_date'] - trades_df['buy_date']).dt.days
            avg_holding_period = trades_df['holding_period'].mean()
        else:
            avg_holding_period = 0

        return {
            'total_trades': total_trades,
            'win_rate': win_rate,
            'avg_profit': avg_profit,
            'avg_loss': avg_loss,
            'profit_loss_ratio': profit_loss_ratio,
            'profit_factor': profit_factor,
            'avg_holding_period': avg_holding_period
        }

    def calculate_period_returns(self, portfolio_values, period='M'):
        """
        计算周期收益率

        参数:
            portfolio_values (pandas.Series): 组合价值序列
            period (str): 周期，'M'表示月，'Y'表示年

        返回:
            pandas.Series: 周期收益率
        """
        if len(portfolio_values) < 2:
            return pd.Series()

        # 确保索引是日期类型
        if not isinstance(portfolio_values.index, pd.DatetimeIndex):
            portfolio_values.index = pd.to_datetime(portfolio_values.index)

        # 重采样到指定周期的最后一天
        period_end_values = portfolio_values.resample(period).last()

        # 计算周期收益率
        period_returns = period_end_values.pct_change().dropna()

        return period_returns

    def calculate_alpha_beta(self, returns, benchmark_returns):
        """
        计算阿尔法和贝塔

        参数:
            returns (pandas.Series): 策略收益率序列
            benchmark_returns (pandas.Series): 基准收益率序列

        返回:
            dict: 阿尔法和贝塔
        """
        if len(returns) < 2 or len(benchmark_returns) < 2:
            return {'alpha': 0.0, 'beta': 0.0}

        # 对齐数据
        aligned_returns = pd.concat([returns, benchmark_returns], axis=1).dropna()
        if len(aligned_returns) < 2:
            return {'alpha': 0.0, 'beta': 0.0}

        strategy_returns = aligned_returns.iloc[:, 0]
        benchmark_returns = aligned_returns.iloc[:, 1]

        # 计算贝塔
        covariance = strategy_returns.cov(benchmark_returns)
        benchmark_variance = benchmark_returns.var()
        beta = covariance / benchmark_variance if benchmark_variance != 0 else 0.0

        # 计算阿尔法 (年化)
        alpha = (strategy_returns.mean() - self.risk_free_rate / 252 - beta * (benchmark_returns.mean() - self.risk_free_rate / 252)) * 252

        return {'alpha': alpha, 'beta': beta}

    def calculate_information_ratio(self, returns, benchmark_returns):
        """
        计算信息比率

        参数:
            returns (pandas.Series): 策略收益率序列
            benchmark_returns (pandas.Series): 基准收益率序列

        返回:
            float: 信息比率
        """
        if len(returns) < 2 or len(benchmark_returns) < 2:
            return 0.0

        # 对齐数据
        aligned_returns = pd.concat([returns, benchmark_returns], axis=1).dropna()
        if len(aligned_returns) < 2:
            return 0.0

        strategy_returns = aligned_returns.iloc[:, 0]
        benchmark_returns = aligned_returns.iloc[:, 1]

        # 计算超额收益
        excess_returns = strategy_returns - benchmark_returns

        # 计算信息比率
        information_ratio = excess_returns.mean() / (excess_returns.std() + 1e-10) * np.sqrt(252)

        return information_ratio
