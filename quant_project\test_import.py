#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试导入模块
"""

import os
import sys

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
print(f"Current directory: {current_dir}")
print(f"Python path: {sys.path}")

try:
    # 尝试导入模块
    from core_logic.data_handler import DataHandler
    print("成功导入 DataHandler 模块")
except ImportError as e:
    print(f"导入失败: {str(e)}")

# 检查文件是否存在
data_handler_path = os.path.join(current_dir, "core_logic", "data_handler.py")
print(f"data_handler.py 文件是否存在: {os.path.exists(data_handler_path)}")

# 列出 core_logic 目录中的文件
core_logic_dir = os.path.join(current_dir, "core_logic")
if os.path.exists(core_logic_dir):
    print(f"core_logic 目录中的文件:")
    for file in os.listdir(core_logic_dir):
        print(f"  - {file}")
else:
    print("core_logic 目录不存在")
