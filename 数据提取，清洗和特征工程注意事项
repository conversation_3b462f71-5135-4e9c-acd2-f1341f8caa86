一、数据提取和清洗 (Data Extraction and Cleaning)

此阶段的目标是获取最高质量、最完整、最准确的数据，并确保其在时间序列上的一致性和可复现性。

数据源选择与管理 (Data Sourcing & Management)：

数据字典与文档： 对所有数据源、字段、更新频率、获取逻辑等进行详尽记录，建立清晰的数据字典。
历史数据完整性： 关注数据开始和结束日期，以及中间是否有缺失。
数据清洗与预处理核心实践：

处理缺失值 (NaNs)：

识别： 检测所有形式的缺失（NaN, None, 0，特定占位符等）。
分析： 理解缺失的原因（例如，交易不活跃、节假日、数据提供商问题）。
处理策略（需审慎）：
前向/后向填充 (Forward/Backward Fill): 适用于某些缓慢变化的序列，但需注意引入look-ahead偏误的风险。
插值 (Interpolation): 线性、样条等，但可能平滑掉真实波动。
均值/中位数/众数填充： 可能会扭曲分布，降低方差。
基于模型的填充： 使用其他特征预测缺失值，但模型本身可能引入误差。
直接删除： 如果缺失比例过高或特定行/列不重要，但可能损失信息。
最佳实践： 没有万能方法。选择需基于数据特性、模型敏感度，并进行敏感性分析。记录所有填充方法和理由。对于DRL环境，通常更倾向于保留时间序列的结构，前向填充是较常见的简单选择，但需确保不泄露未来信息给当前状态。
处理异常值/离群点 (Outliers)：

识别： 统计方法（如Z-score, IQR），可视化（箱线图、散点图），或领域知识（例如，价格日内涨跌幅限制）。
分析： 判断是真实极端事件还是数据错误。错误数据需要修正或剔除。
处理策略：
盖帽/缩尾 (Winsorization/Clipping): 将超出阈值的值替换为阈值本身。
剔除： 如果确定为错误且不可修复。
不处理： 如果是真实市场行为，模型应有能力处理。
最佳实践： 记录所有处理，避免过度平滑真实市场波动。
时间戳处理与时区统一 (Timestamping & Timezones)：

高精度时间戳： 确保时间戳的准确性和精度（尤其对于高频数据）。
时区统一： 所有数据转换为统一时区（如UTC）进行存储和处理，避免混淆。
价格和成交量的调整 (Adjustments for Corporate Actions & Splits)：

前复权/后复权： 通常使用**后复权 (Backward Adjusted)**数据进行模型训练，以保证价格序列的连续性，避免因公司行为导致的价格跳空被模型误解为真实市场波动。akshare通常能提供复权因子或直接提供复权后的数据。
数据对齐 (Data Alignment)：

当使用不同频率或不同来源的数据时（例如，日线行情、分钟线行情、宏观数据、财报数据），需要将它们在时间轴上精确对齐。
数据校验规则 (Validation Rules)：

例如：high >= open, high >= low, high >= close, low <= open, low <= close, volume >= 0。
不同数据源间的交叉校验。
与已知市场事实的核对。
数据存储与版本控制 (Data Storage & Versioning)：

标准化存储： 清洗后的数据应以高效、标准化的格式存储（如Parquet, HDF5, 数据库）。
数据版本控制： 对原始数据、清洗脚本和清洗后的数据进行版本控制，确保研究和回测的可复现性。每次数据更新或清洗逻辑变更都应有记录。
二、特征工程 (Feature Engineering)

此阶段的目标是创造出对未来市场行为有预测能力 (Alpha Signal)、稳健且**可解释性（尽可能）**的特征。

特征构建的指导原则：

经济学/金融学直觉： 特征应有合理的经济或行为金融学解释，而不仅仅是数据的随机拟合。为什么这个特征可能预测市场？
避免前视偏差 (Look-ahead Bias)： 构建特征时，确保在时间点 t 使用的所有信息都是在 t 或 t 之前可获得的。例如，计算移动平均线时，窗口应包含 t 时刻及之前的数据，用于 t+1 的预测。
避免目标泄露 (Target Leakage)： 特征的计算不能直接或间接包含未来目标变量的信息。
鲁棒性与稳定性： 特征不应过度敏感于微小的输入数据变化，其预测能力应随时间相对稳定。
考虑交易成本： 隐含交易成本的特征（如基于高换手率指标构建的特征）在实际应用中可能表现不佳。
常用特征类别与技术：

价格与收益率相关特征 (Price & Return-based)：
动量 (Momentum)： 过去N期收益率、价格与移动平均线的偏离度。
波动率 (Volatility)： 已实现波动率（如标准差、ATR）、GARCH模型预测波动率。
均值回归 (Mean Reversion)： 价格与长期均值的偏离、布林带指标。
收益率变换： 对数收益率（log(P_t / P_{t-1})）通常比简单收益率有更好的统计特性。
成交量与资金流相关特征 (Volume & Flow-based)：
价量关系：OBV (On-Balance Volume), MFI (Money Flow Index), 成交量加权平均价 (VWAP) 及其偏离。
成交量突变、成交量加速度。
技术指标 (Technical Indicators)：
谨慎使用，避免过度复杂化。选择有一定逻辑支撑且被广泛研究的指标。
对参数进行敏感性分析，避免参数过拟合。

特征变换与预处理：

归一化/标准化 (Normalization/Standardization)：
将特征缩放到相似范围（如0-1或均值为0标准差为1）。对于神经网络（如DRL中的策略网络）非常重要。常用方法：Min-Max Scaling, Z-score Standardization。
注意： 在时间序列上进行滚动标准化时，要避免使用未来数据。应使用截至当前时间点 t 的滚动窗口数据来计算均值和标准差，用于标准化 t 时刻的特征。
平稳性处理 (Stationarity)：
许多时序模型（包括DRL的某些理解方式）假设输入特征是平稳的。使用统计检验（如ADF, KPSS）判断平稳性。
若非平稳，可尝试差分（X_t - X_{t-1}）、对数变换、去除趋势/季节性等方法。
处理偏度与峰度 (Skewness & Kurtosis)：
对数变换、Box-Cox变换等可以使特征分布更接近正态。
离散化/分箱 (Discretization/Binning)：
将连续特征转换为分类特征，有时能捕获非线性关系或降低噪声影响。
特征交互 (Interaction Features)：
创建现有特征的乘积、比率等，可能捕获更复杂的关系。DRL模型理论上可以学习这些，但显式提供有时能加速学习。
特征选择与降维 (Feature Selection & Dimensionality Reduction)：

目标： 减少特征数量，去除冗余或不相关的特征，降低过拟合风险，提高模型训练效率和泛化能力。
过滤法 (Filter Methods)： 基于统计特性（如相关系数、互信息、方差阈值）独立评估特征。
包裹法 (Wrapper Methods)： 使用特定学习算法的性能来评估特征子集（如递归特征消除 RFE）。计算成本高。
嵌入法 (Embedded Methods)： 在模型训练过程中进行特征选择（如L1正则化 (Lasso) 会使不重要特征的系数变为0，决策树的特征重要性排序）。
降维： PCA（主成分分析）等，将高维特征映射到低维空间，但可能损失可解释性。
DRL特定的特征工程考量：

观测空间 (Observation Space) 设计： 特征工程的输出直接构成DRL智能体的观测空间。这个空间需要包含足够的信息，让智能体能够理解当前市场状态和自身状态（如持仓、未实现盈亏、已持仓天数），以便做出有效决策。
奖励函数相关的特征： 如果奖励函数依赖某些特定指标（如夏普率、回撤），那么这些指标或其组成部分也应该作为特征或状态的一部分提供给智能体。
动作空间反馈： 智能体执行动作后的状态变化应能通过特征清晰地反映出来。
迭代与验证：

特征工程是一个高度迭代的过程。提出假设 -> 构建特征 -> 在验证集上测试其对模型性能的边际贡献 -> 分析结果 -> 改进或放弃。
使用严格的回测框架（包括样本外测试）来评估特征的有效性，避免过拟合。
所有特征的定义、构建逻辑、参数选择和验证结果都应详细记录和版本控制。