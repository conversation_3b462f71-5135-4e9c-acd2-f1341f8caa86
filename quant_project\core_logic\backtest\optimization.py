"""
策略优化模块
提供参数优化和策略验证功能
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Union, Tuple, Optional, Any, Callable
import itertools
from concurrent.futures import ProcessPoolExecutor, as_completed
import time

class StrategyOptimizer:
    """
    策略优化类
    提供参数优化和策略验证功能
    """

    def __init__(self, 
                 strategy_class: Any, 
                 data: pd.DataFrame,
                 initial_capital: float = 100000.0,
                 benchmark_symbol: Optional[str] = None,
                 eval_metric: str = 'sharpe_ratio',
                 maximize: bool = True,
                 use_multiprocessing: bool = True,
                 max_workers: Optional[int] = None):
        """
        初始化策略优化器

        参数:
            strategy_class: 策略类，必须提供支持参数初始化和predict_action方法
            data (pandas.DataFrame): 回测数据
            initial_capital (float): 初始资金
            benchmark_symbol (str, optional): 基准标的代码
            eval_metric (str): 评估指标，可选 'sharpe_ratio', 'total_return', 'max_drawdown' 等
            maximize (bool): 是否最大化评估指标，True表示最大化，False表示最小化
            use_multiprocessing (bool): 是否使用多进程并行优化
            max_workers (int, optional): 最大工作进程数，None表示使用CPU核心数
        """
        self.logger = logging.getLogger('drl_trading')
        
        self.strategy_class = strategy_class
        self.data = data
        self.initial_capital = initial_capital
        self.benchmark_symbol = benchmark_symbol
        self.eval_metric = eval_metric
        self.maximize = maximize
        self.use_multiprocessing = use_multiprocessing
        self.max_workers = max_workers
        
        # 导入依赖模块，确保在运行优化时可用
        from quant_project.core_logic.backtest.backtest_engine import BacktestEngine
        from quant_project.core_logic.backtest.performance_metrics import PerformanceMetrics
        
        self.backtest_engine = BacktestEngine(initial_capital=initial_capital)
        self.performance_metrics = PerformanceMetrics()
        
        self.logger.info(f"初始化策略优化器: 评估指标={eval_metric}, 最大化={maximize}")

    def grid_search(self, param_grid: Dict[str, List[Any]], 
                   fixed_params: Optional[Dict[str, Any]] = None) -> pd.DataFrame:
        """
        网格搜索优化参数

        参数:
            param_grid (dict): 参数网格，格式为 {参数名: [参数值列表]}
            fixed_params (dict, optional): 固定参数，不参与网格搜索的参数

        返回:
            pandas.DataFrame: 优化结果，包含参数组合和对应的评估指标值
        """
        # 构建参数组合
        param_names = list(param_grid.keys())
        param_values = list(param_grid.values())
        param_combinations = list(itertools.product(*param_values))
        
        total_combinations = len(param_combinations)
        self.logger.info(f"开始网格搜索: {total_combinations}个参数组合")
        
        if fixed_params is None:
            fixed_params = {}
        
        results = []
        start_time = time.time()
        
        if self.use_multiprocessing and total_combinations > 1:
            # 使用多进程并行优化
            with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
                futures = []
                
                for i, values in enumerate(param_combinations):
                    params = fixed_params.copy()
                    params.update(dict(zip(param_names, values)))
                    
                    futures.append(executor.submit(self._evaluate_params, params, i, total_combinations))
                
                for future in as_completed(futures):
                    try:
                        result = future.result()
                        if result is not None:
                            results.append(result)
                    except Exception as e:
                        self.logger.error(f"参数评估失败: {str(e)}")
        else:
            # 使用单进程顺序优化
            for i, values in enumerate(param_combinations):
                params = fixed_params.copy()
                params.update(dict(zip(param_names, values)))
                
                result = self._evaluate_params(params, i, total_combinations)
                if result is not None:
                    results.append(result)
        
        # 构建结果DataFrame
        results_df = pd.DataFrame(results)
        
        # 根据评估指标排序
        if self.maximize:
            results_df = results_df.sort_values(self.eval_metric, ascending=False)
        else:
            results_df = results_df.sort_values(self.eval_metric, ascending=True)
        
        elapsed_time = time.time() - start_time
        self.logger.info(f"网格搜索完成: 耗时 {elapsed_time:.2f}秒, 最佳{self.eval_metric}={results_df.iloc[0][self.eval_metric]:.4f}")
        
        return results_df

    def random_search(self, param_space: Dict[str, Callable], 
                     fixed_params: Optional[Dict[str, Any]] = None,
                     n_samples: int = 100,
                     random_seed: Optional[int] = None) -> pd.DataFrame:
        """
        随机搜索优化参数

        参数:
            param_space (dict): 参数空间，格式为 {参数名: 采样函数}
            fixed_params (dict, optional): 固定参数，不参与随机搜索的参数
            n_samples (int): 采样数量
            random_seed (int, optional): 随机种子

        返回:
            pandas.DataFrame: 优化结果，包含参数组合和对应的评估指标值
        """
        if random_seed is not None:
            np.random.seed(random_seed)
        
        self.logger.info(f"开始随机搜索: {n_samples}个参数组合")
        
        if fixed_params is None:
            fixed_params = {}
        
        results = []
        start_time = time.time()
        
        param_combinations = []
        for _ in range(n_samples):
            params = fixed_params.copy()
            for param_name, sampler in param_space.items():
                params[param_name] = sampler()
            param_combinations.append(params)
        
        if self.use_multiprocessing and n_samples > 1:
            # 使用多进程并行优化
            with ProcessPoolExecutor(max_workers=self.max_workers) as executor:
                futures = []
                
                for i, params in enumerate(param_combinations):
                    futures.append(executor.submit(self._evaluate_params, params, i, n_samples))
                
                for future in as_completed(futures):
                    try:
                        result = future.result()
                        if result is not None:
                            results.append(result)
                    except Exception as e:
                        self.logger.error(f"参数评估失败: {str(e)}")
        else:
            # 使用单进程顺序优化
            for i, params in enumerate(param_combinations):
                result = self._evaluate_params(params, i, n_samples)
                if result is not None:
                    results.append(result)
        
        # 构建结果DataFrame
        results_df = pd.DataFrame(results)
        
        # 根据评估指标排序
        if self.maximize:
            results_df = results_df.sort_values(self.eval_metric, ascending=False)
        else:
            results_df = results_df.sort_values(self.eval_metric, ascending=True)
        
        elapsed_time = time.time() - start_time
        self.logger.info(f"随机搜索完成: 耗时 {elapsed_time:.2f}秒, 最佳{self.eval_metric}={results_df.iloc[0][self.eval_metric]:.4f}")
        
        return results_df

    def walk_forward_validation(self, params: Dict[str, Any], 
                              window_size: int, 
                              step_size: int) -> pd.DataFrame:
        """
        执行滚动窗口验证

        参数:
            params (dict): 策略参数
            window_size (int): 窗口大小，单位为数据点数量
            step_size (int): 步长，单位为数据点数量

        返回:
            pandas.DataFrame: 验证结果，包含每个窗口的评估指标
        """
        if not isinstance(self.data.index, pd.DatetimeIndex):
            self.logger.warning("数据索引不是日期类型，尝试转换...")
            try:
                self.data.index = pd.to_datetime(self.data.index)
            except Exception as e:
                self.logger.error(f"无法转换索引为日期类型: {str(e)}")
                return pd.DataFrame()
        
        # 确保数据已按日期排序
        data = self.data.sort_index()
        
        total_points = len(data)
        if window_size >= total_points:
            self.logger.error(f"窗口大小 ({window_size}) 必须小于数据点总数 ({total_points})")
            return pd.DataFrame()
        
        self.logger.info(f"开始滚动窗口验证: 窗口大小={window_size}, 步长={step_size}")
        
        results = []
        
        # 计算窗口起始位置
        start_indices = range(0, total_points - window_size, step_size)
        
        for i, start_idx in enumerate(start_indices):
            end_idx = start_idx + window_size
            window_data = data.iloc[start_idx:end_idx]
            
            window_start_date = window_data.index[0]
            window_end_date = window_data.index[-1]
            
            self.logger.info(f"评估窗口 {i+1}/{len(start_indices)}: {window_start_date} 至 {window_end_date}")
            
            try:
                # 初始化策略
                strategy = self.strategy_class(**params)
                
                # 运行回测
                backtest_result = self.backtest_engine.run_backtest(
                    window_data, 
                    strategy, 
                    benchmark_symbol=self.benchmark_symbol
                )
                
                if backtest_result["status"] == "success":
                    # 计算性能指标
                    metrics = self.performance_metrics.calculate_metrics(
                        backtest_result["portfolio_values"],
                        backtest_result["trades"],
                        backtest_result["benchmark_values"]
                    )
                    
                    # 保存结果
                    result = {
                        "window": i + 1,
                        "start_date": window_start_date,
                        "end_date": window_end_date,
                        "data_points": len(window_data)
                    }
                    
                    # 添加主要性能指标
                    for metric in ['total_return', 'annual_return', 'max_drawdown', 'sharpe_ratio', 'sortino_ratio']:
                        if metric in metrics:
                            result[metric] = metrics[metric]
                    
                    # 添加交易统计
                    for metric in ['total_trades', 'win_rate']:
                        if metric in metrics:
                            result[metric] = metrics[metric]
                    
                    results.append(result)
                else:
                    self.logger.warning(f"窗口 {i+1} 回测失败: {backtest_result.get('message', '未知错误')}")
            
            except Exception as e:
                self.logger.error(f"窗口 {i+1} 评估失败: {str(e)}")
        
        # 构建结果DataFrame
        results_df = pd.DataFrame(results)
        
        if not results_df.empty:
            # 计算总体统计信息
            avg_metrics = results_df.mean()
            std_metrics = results_df.std()
            
            self.logger.info(f"滚动窗口验证完成: {len(results)} 个有效窗口")
            self.logger.info(f"平均年化收益率: {avg_metrics.get('annual_return', 0):.2%}, 标准差: {std_metrics.get('annual_return', 0):.2%}")
            self.logger.info(f"平均夏普比率: {avg_metrics.get('sharpe_ratio', 0):.2f}, 标准差: {std_metrics.get('sharpe_ratio', 0):.2f}")
        else:
            self.logger.warning("滚动窗口验证未产生有效结果")
        
        return results_df

    def _evaluate_params(self, params: Dict[str, Any], index: int, total: int) -> Optional[Dict[str, Any]]:
        """
        评估参数组合

        参数:
            params (dict): 参数字典
            index (int): 当前参数组合的索引
            total (int): 总参数组合数

        返回:
            dict: 评估结果，包括参数和评估指标值
        """
        try:
            # 初始化策略
            strategy = self.strategy_class(**params)
            
            # 运行回测
            backtest_result = self.backtest_engine.run_backtest(
                self.data, 
                strategy, 
                benchmark_symbol=self.benchmark_symbol
            )
            
            if backtest_result["status"] == "success":
                # 计算性能指标
                metrics = self.performance_metrics.calculate_metrics(
                    backtest_result["portfolio_values"],
                    backtest_result["trades"],
                    backtest_result["benchmark_values"]
                )
                
                # 构建结果字典
                result = params.copy()
                
                # 添加主要性能指标
                for metric in ['total_return', 'annual_return', 'max_drawdown', 'sharpe_ratio', 'sortino_ratio']:
                    if metric in metrics:
                        result[metric] = metrics[metric]
                
                # 添加交易统计
                for metric in ['total_trades', 'win_rate']:
                    if metric in metrics:
                        result[metric] = metrics[metric]
                
                # 记录进度
                if index % max(1, total // 10) == 0 or index == total - 1:
                    self.logger.info(f"评估进度: {index+1}/{total} ({(index+1)/total*100:.1f}%), "
                                   f"当前{self.eval_metric}={result.get(self.eval_metric, 0):.4f}")
                
                return result
            else:
                self.logger.warning(f"参数组合 {index+1}/{total} 回测失败: {backtest_result.get('message', '未知错误')}")
                return None
        
        except Exception as e:
            self.logger.error(f"参数组合 {index+1}/{total} 评估失败: {str(e)}")
            return None 