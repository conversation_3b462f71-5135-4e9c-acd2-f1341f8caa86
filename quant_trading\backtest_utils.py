import numpy as np
import pandas as pd
from typing import Dict, List, Union, Tuple, Callable, Optional, Any
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import logging
from scipy import stats
import matplotlib.dates as mdates
from matplotlib.ticker import FuncFormatter

# Configure logger
logger = logging.getLogger(__name__)

class BacktestEngine:
    """
    Engine for backtesting trading strategies.
    Provides utilities for simulating trades, calculating performance metrics,
    and visualizing results.
    """
    
    def __init__(self, 
                price_data: pd.DataFrame,
                date_column: str = 'date',
                price_column: str = 'close',
                initial_capital: float = 100000.0,
                commission_rate: float = 0.001,
                slippage: float = 0.0,
                position_size: float = 1.0,
                max_position_size: float = 1.0,
                risk_free_rate: float = 0.0):
        """
        Initialize the BacktestEngine.
        
        Args:
            price_data: DataFrame containing price data
            date_column: Name of the column containing dates
            price_column: Name of the column containing price data
            initial_capital: Initial capital for backtesting
            commission_rate: Commission rate per trade (as a fraction)
            slippage: Slippage per trade (as a fraction)
            position_size: Default position size as a fraction of capital
            max_position_size: Maximum position size as a fraction of capital
            risk_free_rate: Annual risk-free rate for performance metrics
        """
        self.price_data = price_data.copy()
        
        # Validate required columns
        if date_column not in self.price_data.columns:
            raise ValueError(f"Date column '{date_column}' not found in price data")
        if price_column not in self.price_data.columns:
            raise ValueError(f"Price column '{price_column}' not found in price data")
            
        self.date_column = date_column
        self.price_column = price_column
        
        # Ensure date column is datetime type
        if not pd.api.types.is_datetime64_any_dtype(self.price_data[date_column]):
            self.price_data[date_column] = pd.to_datetime(self.price_data[date_column])
            
        # Sort by date
        self.price_data = self.price_data.sort_values(by=date_column)
        
        # Set date as index
        self.price_data = self.price_data.set_index(date_column)
        
        # Settings
        self.initial_capital = initial_capital
        self.commission_rate = commission_rate
        self.slippage = slippage
        self.position_size = position_size
        self.max_position_size = max_position_size
        self.risk_free_rate = risk_free_rate
        
        # Results storage
        self.results = None
        
    def run_backtest(self, 
                    strategy: Callable,
                    strategy_params: Dict = None,
                    start_date: Union[str, datetime] = None,
                    end_date: Union[str, datetime] = None) -> pd.DataFrame:
        """
        Run backtest using the provided strategy.
        
        Args:
            strategy: Function that generates signals
                     Function signature: strategy(data, params) -> DataFrame with 'signal' column
            strategy_params: Parameters to pass to the strategy function
            start_date: Start date for backtesting
            end_date: End date for backtesting
            
        Returns:
            DataFrame with backtest results
        """
        strategy_params = strategy_params or {}
        
        # Filter data by date range if provided
        data = self.price_data.copy()
        if start_date is not None:
            data = data[data.index >= pd.to_datetime(start_date)]
        if end_date is not None:
            data = data[data.index <= pd.to_datetime(end_date)]
            
        # Get signals from strategy
        signals = strategy(data, strategy_params)
        
        if not isinstance(signals, pd.DataFrame):
            raise ValueError("Strategy function must return a DataFrame")
            
        if 'signal' not in signals.columns:
            raise ValueError("Strategy must generate a 'signal' column")
            
        # Merge signals with price data
        backtest_data = data.join(signals[['signal']], how='left')
        
        # Replace NaN signals with 0 (no position)
        backtest_data['signal'] = backtest_data['signal'].fillna(0)
        
        # Calculate position changes
        backtest_data['position'] = backtest_data['signal']
        backtest_data['position_change'] = backtest_data['position'].diff().fillna(0)
        
        # Calculate trade details
        backtest_data['price'] = backtest_data[self.price_column]
        
        # Apply slippage to trade prices
        backtest_data['buy_price'] = backtest_data['price'] * (1 + self.slippage)
        backtest_data['sell_price'] = backtest_data['price'] * (1 - self.slippage)
        
        # Calculate trade values
        backtest_data['buy_value'] = np.where(
            backtest_data['position_change'] > 0, 
            backtest_data['position_change'] * backtest_data['buy_price'], 
            0
        )
        backtest_data['sell_value'] = np.where(
            backtest_data['position_change'] < 0, 
            -backtest_data['position_change'] * backtest_data['sell_price'], 
            0
        )
        
        # Calculate commissions
        backtest_data['commission'] = (backtest_data['buy_value'] + backtest_data['sell_value']) * self.commission_rate
        
        # Calculate cash flow
        backtest_data['cash_flow'] = backtest_data['sell_value'] - backtest_data['buy_value'] - backtest_data['commission']
        
        # Calculate portfolio value
        backtest_data['cash'] = self.initial_capital + backtest_data['cash_flow'].cumsum()
        backtest_data['position_value'] = backtest_data['position'] * backtest_data['price']
        backtest_data['portfolio_value'] = backtest_data['cash'] + backtest_data['position_value']
        
        # Calculate returns
        backtest_data['daily_return'] = backtest_data['portfolio_value'].pct_change().fillna(0)
        backtest_data['cumulative_return'] = (1 + backtest_data['daily_return']).cumprod() - 1
        
        # Calculate drawdowns
        backtest_data['peak_value'] = backtest_data['portfolio_value'].cummax()
        backtest_data['drawdown'] = (backtest_data['portfolio_value'] - backtest_data['peak_value']) / backtest_data['peak_value']
        
        # Store results
        self.results = backtest_data
        
        return backtest_data
    
    def calculate_performance_metrics(self) -> Dict[str, float]:
        """
        Calculate performance metrics for the backtest.
        
        Returns:
            Dictionary with performance metrics
        """
        if self.results is None:
            logger.warning("No backtest results available. Run backtest first.")
            return {}
            
        # Extract daily returns
        daily_returns = self.results['daily_return'].values
        
        # Calculate metrics
        total_days = len(daily_returns)
        trading_days_per_year = 252  # Standard assumption
        
        # Total return
        total_return = self.results['portfolio_value'].iloc[-1] / self.initial_capital - 1
        
        # Annualized return
        years = total_days / trading_days_per_year
        annualized_return = (1 + total_return) ** (1 / years) - 1
        
        # Volatility (annualized)
        volatility = np.std(daily_returns) * np.sqrt(trading_days_per_year)
        
        # Sharpe ratio
        excess_returns = daily_returns - self.risk_free_rate / trading_days_per_year
        sharpe_ratio = np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(trading_days_per_year)
        
        # Sortino ratio (downside risk only)
        negative_returns = daily_returns[daily_returns < 0]
        sortino_ratio = np.mean(excess_returns) / np.std(negative_returns) * np.sqrt(trading_days_per_year) if len(negative_returns) > 0 else np.inf
        
        # Maximum drawdown
        max_drawdown = self.results['drawdown'].min()
        
        # Calmar ratio
        calmar_ratio = annualized_return / abs(max_drawdown) if max_drawdown != 0 else np.inf
        
        # Win rate
        daily_win_rate = np.sum(daily_returns > 0) / total_days
        
        # Average win/loss
        avg_win = np.mean(daily_returns[daily_returns > 0]) if np.any(daily_returns > 0) else 0
        avg_loss = np.mean(daily_returns[daily_returns < 0]) if np.any(daily_returns < 0) else 0
        
        # Profit factor
        gross_profits = np.sum(daily_returns[daily_returns > 0])
        gross_losses = np.abs(np.sum(daily_returns[daily_returns < 0]))
        profit_factor = gross_profits / gross_losses if gross_losses != 0 else np.inf
        
        # Risk-return ratio
        risk_return_ratio = annualized_return / volatility if volatility != 0 else np.inf
        
        # Value at Risk (VaR) at 95% confidence
        var_95 = np.percentile(daily_returns, 5)
        
        # Conditional Value at Risk (CVaR) / Expected Shortfall
        cvar_95 = np.mean(daily_returns[daily_returns <= var_95])
        
        # Kurtosis (tailedness of return distribution)
        kurtosis = stats.kurtosis(daily_returns)
        
        # Skewness of return distribution
        skewness = stats.skew(daily_returns)
        
        # Alpha and Beta (if benchmark data available)
        alpha, beta = 0.0, 1.0  # Default values
        
        # Compile metrics
        metrics = {
            'total_return': total_return,
            'annualized_return': annualized_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'sortino_ratio': sortino_ratio,
            'max_drawdown': max_drawdown,
            'calmar_ratio': calmar_ratio,
            'win_rate': daily_win_rate,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'risk_return_ratio': risk_return_ratio,
            'var_95': var_95,
            'cvar_95': cvar_95,
            'kurtosis': kurtosis,
            'skewness': skewness,
            'alpha': alpha,
            'beta': beta
        }
        
        return metrics
    
    def plot_performance(self, figsize: Tuple[int, int] = (14, 8)) -> None:
        """
        Plot performance of the backtest.
        
        Args:
            figsize: Figure size (width, height)
        """
        if self.results is None:
            logger.warning("No backtest results available. Run backtest first.")
            return
            
        fig, axs = plt.subplots(3, 1, figsize=figsize, sharex=True, gridspec_kw={'height_ratios': [2, 1, 1]})
        
        # Reset index for plotting
        plot_data = self.results.reset_index()
        
        # Format date axis
        date_format = mdates.DateFormatter('%Y-%m-%d')
        
        # Portfolio value
        axs[0].plot(plot_data[self.date_column], plot_data['portfolio_value'], label='Portfolio Value')
        axs[0].set_title('Portfolio Value Over Time')
        axs[0].set_ylabel('Value ($)')
        axs[0].grid(True)
        axs[0].legend()
        axs[0].xaxis.set_major_formatter(date_format)
        
        # Format y-axis as currency
        axs[0].yaxis.set_major_formatter(FuncFormatter(lambda x, p: f'${x:,.0f}'))
        
        # Cumulative returns
        axs[1].plot(plot_data[self.date_column], plot_data['cumulative_return'] * 100)
        axs[1].set_title('Cumulative Returns')
        axs[1].set_ylabel('Return (%)')
        axs[1].grid(True)
        axs[1].xaxis.set_major_formatter(date_format)
        
        # Format y-axis as percentage
        axs[1].yaxis.set_major_formatter(FuncFormatter(lambda x, p: f'{x:.1f}%'))
        
        # Drawdowns
        axs[2].fill_between(plot_data[self.date_column], plot_data['drawdown'] * 100, 0, color='red', alpha=0.3)
        axs[2].set_title('Drawdowns')
        axs[2].set_ylabel('Drawdown (%)')
        axs[2].set_xlabel('Date')
        axs[2].grid(True)
        axs[2].xaxis.set_major_formatter(date_format)
        
        # Format y-axis as percentage
        axs[2].yaxis.set_major_formatter(FuncFormatter(lambda x, p: f'{x:.1f}%'))
        
        plt.tight_layout()
        plt.show()
        
    def plot_returns_distribution(self, figsize: Tuple[int, int] = (14, 6)) -> None:
        """
        Plot distribution of returns.
        
        Args:
            figsize: Figure size (width, height)
        """
        if self.results is None:
            logger.warning("No backtest results available. Run backtest first.")
            return
            
        fig, axs = plt.subplots(1, 2, figsize=figsize)
        
        daily_returns = self.results['daily_return'].values * 100  # Convert to percentage
        
        # Histogram
        sns.histplot(daily_returns, kde=True, ax=axs[0])
        axs[0].set_title('Distribution of Daily Returns')
        axs[0].set_xlabel('Daily Return (%)')
        axs[0].set_ylabel('Frequency')
        
        # Add normal distribution for comparison
        x = np.linspace(min(daily_returns), max(daily_returns), 100)
        y = stats.norm.pdf(x, np.mean(daily_returns), np.std(daily_returns))
        axs[0].plot(x, y * len(daily_returns) * (max(daily_returns) - min(daily_returns)) / 10, 
                   'r--', linewidth=2, label='Normal Distribution')
        axs[0].legend()
        
        # Q-Q plot
        stats.probplot(daily_returns, plot=axs[1])
        axs[1].set_title('Q-Q Plot of Daily Returns')
        
        plt.tight_layout()
        plt.show()
        
    def plot_monthly_returns(self, figsize: Tuple[int, int] = (14, 8)) -> None:
        """
        Plot heatmap of monthly returns.
        
        Args:
            figsize: Figure size (width, height)
        """
        if self.results is None:
            logger.warning("No backtest results available. Run backtest first.")
            return
            
        # Reset index to access date column
        monthly_returns = self.results.reset_index()
        
        # Extract year and month
        monthly_returns['year'] = monthly_returns[self.date_column].dt.year
        monthly_returns['month'] = monthly_returns[self.date_column].dt.month
        
        # Calculate monthly returns
        monthly_returns = monthly_returns.groupby(['year', 'month'])['daily_return'].apply(
            lambda x: (1 + x).prod() - 1
        ).reset_index()
        
        # Create pivot table
        pivot_table = monthly_returns.pivot(index='year', columns='month', values='daily_return')
        
        # Plot heatmap
        plt.figure(figsize=figsize)
        sns.heatmap(pivot_table * 100, annot=True, fmt='.2f', cmap='RdYlGn',
                   linewidths=1, center=0, cbar_kws={'label': 'Monthly Return (%)'})
        
        # Customize plot
        plt.title('Monthly Returns (%)')
        plt.xlabel('Month')
        plt.ylabel('Year')
        
        # Set month names as column labels
        month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 
                      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        plt.xticks(np.arange(12) + 0.5, month_names)
        
        plt.tight_layout()
        plt.show()
        
    def plot_rolling_statistics(self, window: int = 60, figsize: Tuple[int, int] = (14, 10)) -> None:
        """
        Plot rolling statistics.
        
        Args:
            window: Window size for rolling statistics (in days)
            figsize: Figure size (width, height)
        """
        if self.results is None:
            logger.warning("No backtest results available. Run backtest first.")
            return
            
        # Calculate rolling statistics
        rolling_stats = self.results.copy()
        rolling_stats['rolling_return'] = rolling_stats['daily_return'].rolling(window=window).mean() * 252 * 100  # Annualized, in percentage
        rolling_stats['rolling_vol'] = rolling_stats['daily_return'].rolling(window=window).std() * np.sqrt(252) * 100  # Annualized, in percentage
        rolling_stats['rolling_sharpe'] = rolling_stats['rolling_return'] / rolling_stats['rolling_vol']
        
        # Reset index for plotting
        plot_data = rolling_stats.reset_index()
        
        # Create plot
        fig, axs = plt.subplots(3, 1, figsize=figsize, sharex=True)
        
        # Format date axis
        date_format = mdates.DateFormatter('%Y-%m-%d')
        
        # Rolling returns
        axs[0].plot(plot_data[self.date_column], plot_data['rolling_return'])
        axs[0].set_title(f'Rolling {window}-day Annualized Return')
        axs[0].set_ylabel('Return (%)')
        axs[0].grid(True)
        axs[0].axhline(y=0, color='r', linestyle='--')
        axs[0].xaxis.set_major_formatter(date_format)
        
        # Rolling volatility
        axs[1].plot(plot_data[self.date_column], plot_data['rolling_vol'])
        axs[1].set_title(f'Rolling {window}-day Annualized Volatility')
        axs[1].set_ylabel('Volatility (%)')
        axs[1].grid(True)
        axs[1].xaxis.set_major_formatter(date_format)
        
        # Rolling Sharpe ratio
        axs[2].plot(plot_data[self.date_column], plot_data['rolling_sharpe'])
        axs[2].set_title(f'Rolling {window}-day Sharpe Ratio')
        axs[2].set_ylabel('Sharpe Ratio')
        axs[2].set_xlabel('Date')
        axs[2].grid(True)
        axs[2].axhline(y=0, color='r', linestyle='--')
        axs[2].axhline(y=1, color='g', linestyle='--')
        axs[2].xaxis.set_major_formatter(date_format)
        
        plt.tight_layout()
        plt.show()
        
    def print_performance_summary(self) -> None:
        """
        Print summary of backtest performance.
        """
        if self.results is None:
            logger.warning("No backtest results available. Run backtest first.")
            return
            
        metrics = self.calculate_performance_metrics()
        
        print("=" * 50)
        print("BACKTEST PERFORMANCE SUMMARY")
        print("=" * 50)
        
        print(f"Initial Capital: ${self.initial_capital:,.2f}")
        print(f"Final Portfolio Value: ${self.results['portfolio_value'].iloc[-1]:,.2f}")
        
        print("\nRETURNS:")
        print(f"Total Return: {metrics['total_return']*100:.2f}%")
        print(f"Annualized Return: {metrics['annualized_return']*100:.2f}%")
        
        print("\nRISK METRICS:")
        print(f"Volatility (Annualized): {metrics['volatility']*100:.2f}%")
        print(f"Sharpe Ratio: {metrics['sharpe_ratio']:.2f}")
        print(f"Sortino Ratio: {metrics['sortino_ratio']:.2f}")
        print(f"Maximum Drawdown: {metrics['max_drawdown']*100:.2f}%")
        print(f"Calmar Ratio: {metrics['calmar_ratio']:.2f}")
        print(f"VaR (95%): {metrics['var_95']*100:.2f}%")
        print(f"CVaR (95%): {metrics['cvar_95']*100:.2f}%")
        
        print("\nTRADING STATISTICS:")
        print(f"Win Rate: {metrics['win_rate']*100:.2f}%")
        print(f"Average Win: {metrics['avg_win']*100:.4f}%")
        print(f"Average Loss: {metrics['avg_loss']*100:.4f}%")
        print(f"Profit Factor: {metrics['profit_factor']:.2f}")
        
        print("\nDISTRIBUTION METRICS:")
        print(f"Skewness: {metrics['skewness']:.2f}")
        print(f"Kurtosis: {metrics['kurtosis']:.2f}")
        
        print("=" * 50)

# Example trading strategies

def sma_crossover_strategy(data: pd.DataFrame, params: Dict) -> pd.DataFrame:
    """
    Simple Moving Average crossover strategy.
    
    Args:
        data: DataFrame containing price data
        params: Strategy parameters
            - fast_period: Period for fast moving average
            - slow_period: Period for slow moving average
            - price_column: Column name for price data
            
    Returns:
        DataFrame with 'signal' column
    """
    # Default parameters
    fast_period = params.get('fast_period', 20)
    slow_period = params.get('slow_period', 50)
    price_column = params.get('price_column', 'close')
    
    # Create copy of data
    result = data.copy()
    
    # Calculate moving averages
    result['fast_ma'] = result[price_column].rolling(window=fast_period).mean()
    result['slow_ma'] = result[price_column].rolling(window=slow_period).mean()
    
    # Generate signals
    result['signal'] = 0.0
    
    # Buy signal: fast MA crosses above slow MA
    result.loc[result['fast_ma'] > result['slow_ma'], 'signal'] = 1.0
    
    # Sell signal: fast MA crosses below slow MA
    result.loc[result['fast_ma'] <= result['slow_ma'], 'signal'] = 0.0
    
    return result

def bollinger_band_strategy(data: pd.DataFrame, params: Dict) -> pd.DataFrame:
    """
    Bollinger Band strategy.
    
    Args:
        data: DataFrame containing price data
        params: Strategy parameters
            - period: Period for moving average
            - std_dev: Number of standard deviations for bands
            - price_column: Column name for price data
            
    Returns:
        DataFrame with 'signal' column
    """
    # Default parameters
    period = params.get('period', 20)
    std_dev = params.get('std_dev', 2.0)
    price_column = params.get('price_column', 'close')
    
    # Create copy of data
    result = data.copy()
    
    # Calculate Bollinger Bands
    result['middle_band'] = result[price_column].rolling(window=period).mean()
    result['std'] = result[price_column].rolling(window=period).std()
    result['upper_band'] = result['middle_band'] + std_dev * result['std']
    result['lower_band'] = result['middle_band'] - std_dev * result['std']
    
    # Generate signals
    result['signal'] = 0.0
    
    # Buy signal: price crosses below lower band
    result.loc[result[price_column] <= result['lower_band'], 'signal'] = 1.0
    
    # Sell signal: price crosses above upper band
    result.loc[result[price_column] >= result['upper_band'], 'signal'] = 0.0
    
    # Exit signals can also be based on mean reversion to middle band
    # result.loc[result[price_column] >= result['middle_band'], 'signal'] = 0.0
    
    return result

def rsi_strategy(data: pd.DataFrame, params: Dict) -> pd.DataFrame:
    """
    Relative Strength Index (RSI) strategy.
    
    Args:
        data: DataFrame containing price data
        params: Strategy parameters
            - period: Period for RSI calculation
            - oversold: RSI level considered oversold
            - overbought: RSI level considered overbought
            - price_column: Column name for price data
            
    Returns:
        DataFrame with 'signal' column
    """
    # Default parameters
    period = params.get('period', 14)
    oversold = params.get('oversold', 30)
    overbought = params.get('overbought', 70)
    price_column = params.get('price_column', 'close')
    
    # Create copy of data
    result = data.copy()
    
    # Calculate price changes
    delta = result[price_column].diff()
    
    # Separate gains and losses
    gain = delta.copy()
    loss = delta.copy()
    gain[gain < 0] = 0
    loss[loss > 0] = 0
    loss = abs(loss)
    
    # Calculate average gain and loss
    avg_gain = gain.rolling(window=period).mean()
    avg_loss = loss.rolling(window=period).mean()
    
    # Calculate RS and RSI
    rs = avg_gain / avg_loss
    result['rsi'] = 100 - (100 / (1 + rs))
    
    # Generate signals
    result['signal'] = 0.0
    
    # Buy signal: RSI crosses below oversold level
    result.loc[result['rsi'] <= oversold, 'signal'] = 1.0
    
    # Sell signal: RSI crosses above overbought level
    result.loc[result['rsi'] >= overbought, 'signal'] = 0.0
    
    return result

def macd_strategy(data: pd.DataFrame, params: Dict) -> pd.DataFrame:
    """
    Moving Average Convergence Divergence (MACD) strategy.
    
    Args:
        data: DataFrame containing price data
        params: Strategy parameters
            - fast_period: Period for fast EMA
            - slow_period: Period for slow EMA
            - signal_period: Period for signal line
            - price_column: Column name for price data
            
    Returns:
        DataFrame with 'signal' column
    """
    # Default parameters
    fast_period = params.get('fast_period', 12)
    slow_period = params.get('slow_period', 26)
    signal_period = params.get('signal_period', 9)
    price_column = params.get('price_column', 'close')
    
    # Create copy of data
    result = data.copy()
    
    # Calculate MACD
    result['ema_fast'] = result[price_column].ewm(span=fast_period, adjust=False).mean()
    result['ema_slow'] = result[price_column].ewm(span=slow_period, adjust=False).mean()
    result['macd'] = result['ema_fast'] - result['ema_slow']
    result['signal_line'] = result['macd'].ewm(span=signal_period, adjust=False).mean()
    result['histogram'] = result['macd'] - result['signal_line']
    
    # Generate signals
    result['signal'] = 0.0
    
    # Buy signal: MACD crosses above signal line
    result.loc[result['macd'] > result['signal_line'], 'signal'] = 1.0
    
    # Sell signal: MACD crosses below signal line
    result.loc[result['macd'] <= result['signal_line'], 'signal'] = 0.0
    
    return result 