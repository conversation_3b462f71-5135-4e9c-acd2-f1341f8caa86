#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
项目重构脚本
用于重构项目目录结构，统一导入方式，避免循环导入
"""

import os
import sys
import shutil
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('restructure_project.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('restructure_project')

# 项目根目录
PROJECT_ROOT = Path(os.getcwd())
# 新项目目录
NEW_PROJECT_DIR = PROJECT_ROOT / 'quant_trading'
# 旧项目目录
OLD_PROJECT_DIR = PROJECT_ROOT / 'quant_project'

# 目录映射关系
DIR_MAPPING = {
    'data': [
        (OLD_PROJECT_DIR / 'core_logic' / 'data_handler.py', NEW_PROJECT_DIR / 'data' / 'data_handler.py'),
        (OLD_PROJECT_DIR / 'core_logic' / 'data_handling' / 'data_fetcher.py', NEW_PROJECT_DIR / 'data' / 'data_fetcher.py'),
        (OLD_PROJECT_DIR / 'core_logic' / 'data_handling' / 'data_cleaner.py', NEW_PROJECT_DIR / 'data' / 'data_cleaner.py'),
        (OLD_PROJECT_DIR / 'core_logic' / 'data_handling' / 'data_validator.py', NEW_PROJECT_DIR / 'data' / 'data_validator.py'),
        (OLD_PROJECT_DIR / 'core_logic' / 'data_handling' / 'data_cache.py', NEW_PROJECT_DIR / 'data' / 'data_cache.py'),
        (OLD_PROJECT_DIR / 'core_logic' / 'data_handling' / 'enhanced_data_validator.py', NEW_PROJECT_DIR / 'data' / 'enhanced_data_validator.py'),
        (OLD_PROJECT_DIR / 'core_logic' / 'optimized_data_handler.py', NEW_PROJECT_DIR / 'data' / 'optimized_data_handler.py'),
        (OLD_PROJECT_DIR / 'core_logic' / 'optimized_data_handler_adapter.py', NEW_PROJECT_DIR / 'data' / 'optimized_data_handler_adapter.py'),
    ],
    'features': [
        (OLD_PROJECT_DIR / 'core_logic' / 'feature_engineer.py', NEW_PROJECT_DIR / 'features' / 'feature_engineer.py'),
        (OLD_PROJECT_DIR / 'core_logic' / 'enhanced_feature_engineer.py', NEW_PROJECT_DIR / 'features' / 'enhanced_feature_engineer.py'),
        (OLD_PROJECT_DIR / 'core_logic' / 'feature_engineering' / 'price_features.py', NEW_PROJECT_DIR / 'features' / 'price_features.py'),
        (OLD_PROJECT_DIR / 'core_logic' / 'feature_engineering' / 'technical_indicators.py', NEW_PROJECT_DIR / 'features' / 'technical_indicators.py'),
        (OLD_PROJECT_DIR / 'core_logic' / 'feature_engineering' / 'statistical_features.py', NEW_PROJECT_DIR / 'features' / 'statistical_features.py'),
        (OLD_PROJECT_DIR / 'core_logic' / 'feature_engineering' / 'time_features.py', NEW_PROJECT_DIR / 'features' / 'time_features.py'),
        (OLD_PROJECT_DIR / 'core_logic' / 'optimized_feature_engineering.py', NEW_PROJECT_DIR / 'features' / 'optimized_feature_engineering.py'),
        (OLD_PROJECT_DIR / 'core_logic' / 'optimized_feature_engineering_adapter.py', NEW_PROJECT_DIR / 'features' / 'optimized_feature_engineering_adapter.py'),
    ],
    'trading': [
        (OLD_PROJECT_DIR / 'core_logic' / 'trading_environment.py', NEW_PROJECT_DIR / 'trading' / 'trading_environment.py'),
        (OLD_PROJECT_DIR / 'core_logic' / 'enhanced_trading_environment.py', NEW_PROJECT_DIR / 'trading' / 'enhanced_trading_environment.py'),
        (OLD_PROJECT_DIR / 'core_logic' / 'trading_env' / 'action.py', NEW_PROJECT_DIR / 'trading' / 'action.py'),
        (OLD_PROJECT_DIR / 'core_logic' / 'trading_env' / 'observation.py', NEW_PROJECT_DIR / 'trading' / 'observation.py'),
        (OLD_PROJECT_DIR / 'core_logic' / 'trading_env' / 'reward.py', NEW_PROJECT_DIR / 'trading' / 'reward.py'),
        (OLD_PROJECT_DIR / 'core_logic' / 'trading_env' / 'rendering.py', NEW_PROJECT_DIR / 'trading' / 'rendering.py'),
        (OLD_PROJECT_DIR / 'core_logic' / 'trading_env' / 'robust_trading_environment.py', NEW_PROJECT_DIR / 'trading' / 'robust_trading_environment.py'),
    ],
    'agents': [
        (OLD_PROJECT_DIR / 'core_logic' / 'drl_agent.py', NEW_PROJECT_DIR / 'agents' / 'drl_agent.py'),
        (OLD_PROJECT_DIR / 'core_logic' / 'robust_drl_agent.py', NEW_PROJECT_DIR / 'agents' / 'robust_drl_agent.py'),
        (OLD_PROJECT_DIR / 'core_logic' / 'enhanced_drl_agent.py', NEW_PROJECT_DIR / 'agents' / 'enhanced_drl_agent.py'),
        (OLD_PROJECT_DIR / 'core_logic' / 'ensemble_learning.py', NEW_PROJECT_DIR / 'agents' / 'ensemble_learning.py'),
    ],
    'evaluation': [
        (OLD_PROJECT_DIR / 'core_logic' / 'performance_analyzer.py', NEW_PROJECT_DIR / 'evaluation' / 'performance_analyzer.py'),
        (OLD_PROJECT_DIR / 'core_logic' / 'enhanced_performance_analyzer.py', NEW_PROJECT_DIR / 'evaluation' / 'enhanced_performance_analyzer.py'),
        (OLD_PROJECT_DIR / 'core_logic' / 'evaluation' / 'model_evaluator.py', NEW_PROJECT_DIR / 'evaluation' / 'model_evaluator.py'),
    ],
    'risk': [
        (OLD_PROJECT_DIR / 'core_logic' / 'risk_management' / 'risk_manager.py', NEW_PROJECT_DIR / 'risk' / 'risk_manager.py'),
    ],
    'validation': [
        (OLD_PROJECT_DIR / 'core_logic' / 'validation' / 'time_series_cv.py', NEW_PROJECT_DIR / 'validation' / 'time_series_cv.py'),
        (OLD_PROJECT_DIR / 'core_logic' / 'validation' / 'market_condition_cv.py', NEW_PROJECT_DIR / 'validation' / 'market_condition_cv.py'),
        (OLD_PROJECT_DIR / 'core_logic' / 'validation' / 'overfitting_detector.py', NEW_PROJECT_DIR / 'validation' / 'overfitting_detector.py'),
    ],
    'market': [
        (OLD_PROJECT_DIR / 'core_logic' / 'market_analysis' / 'market_condition_detector.py', NEW_PROJECT_DIR / 'market' / 'market_condition_detector.py'),
    ],
    'utils': [
        (OLD_PROJECT_DIR / 'core_logic' / 'utils.py', NEW_PROJECT_DIR / 'utils' / 'common.py'),
        (OLD_PROJECT_DIR / 'install_gpu_support.py', NEW_PROJECT_DIR / 'utils' / 'gpu_support.py'),
    ],
    'root': [
        (OLD_PROJECT_DIR / 'main_app.py', NEW_PROJECT_DIR / 'main.py'),
        (OLD_PROJECT_DIR / 'run_project.py', NEW_PROJECT_DIR / 'run.py'),
    ]
}

# 测试文件映射
TEST_MAPPING = {
    'data': [
        (PROJECT_ROOT / 'test_data_handler.py', NEW_PROJECT_DIR / 'tests' / 'test_data.py'),
    ],
    'features': [
        (PROJECT_ROOT / 'test_feature_engineering.py', NEW_PROJECT_DIR / 'tests' / 'test_features.py'),
    ],
    'trading': [
        (PROJECT_ROOT / 'test_trading_environment.py', NEW_PROJECT_DIR / 'tests' / 'test_trading.py'),
    ],
    'agents': [
        (PROJECT_ROOT / 'test_model_training.py', NEW_PROJECT_DIR / 'tests' / 'test_agents.py'),
        (PROJECT_ROOT / 'test_model_loading.py', NEW_PROJECT_DIR / 'tests' / 'test_agents_loading.py'),
        (PROJECT_ROOT / 'test_model_cleanup.py', NEW_PROJECT_DIR / 'tests' / 'test_agents_cleanup.py'),
    ],
    'ui': [
        (PROJECT_ROOT / 'test_ui.py', NEW_PROJECT_DIR / 'tests' / 'test_ui.py'),
        (PROJECT_ROOT / 'test_ui_advanced.py', NEW_PROJECT_DIR / 'tests' / 'test_ui_advanced.py'),
        (PROJECT_ROOT / 'test_ui_components.py', NEW_PROJECT_DIR / 'tests' / 'test_ui_components.py'),
        (PROJECT_ROOT / 'test_ui_session.py', NEW_PROJECT_DIR / 'tests' / 'test_ui_session.py'),
    ],
    'core': [
        (PROJECT_ROOT / 'test_core_modules.py', NEW_PROJECT_DIR / 'tests' / 'test_core.py'),
        (PROJECT_ROOT / 'test_robust_modules.py', NEW_PROJECT_DIR / 'tests' / 'test_robust.py'),
    ]
}

# __init__.py 文件内容模板
INIT_TEMPLATES = {
    'root': """#!/usr/bin/env python
# -*- coding: utf-8 -*-

\"\"\"
量化交易系统
\"\"\"

__version__ = '2.0.0'
""",
    'data': """#!/usr/bin/env python
# -*- coding: utf-8 -*-

\"\"\"
数据处理模块
提供数据获取、清洗、验证和缓存功能
\"\"\"

from quant_trading.data.data_handler import DataHandler
from quant_trading.data.data_fetcher import DataFetcher
from quant_trading.data.data_cleaner import DataCleaner
from quant_trading.data.data_validator import DataValidator
from quant_trading.data.data_cache import DataCache
from quant_trading.data.enhanced_data_validator import EnhancedDataValidator
from quant_trading.data.optimized_data_handler import OptimizedDataHandler
from quant_trading.data.optimized_data_handler_adapter import OptimizedDataHandlerAdapter

__all__ = [
    'DataHandler',
    'DataFetcher',
    'DataCleaner',
    'DataValidator',
    'DataCache',
    'EnhancedDataValidator',
    'OptimizedDataHandler',
    'OptimizedDataHandlerAdapter'
]
""",
    'features': """#!/usr/bin/env python
# -*- coding: utf-8 -*-

\"\"\"
特征工程模块
提供特征生成和处理功能
\"\"\"

from quant_trading.features.feature_engineer import FeatureEngineer
from quant_trading.features.enhanced_feature_engineer import EnhancedFeatureEngineer
from quant_trading.features.price_features import PriceFeatures
from quant_trading.features.technical_indicators import TechnicalIndicators
from quant_trading.features.statistical_features import StatisticalFeatures
from quant_trading.features.time_features import TimeFeatures
from quant_trading.features.optimized_feature_engineering import OptimizedFeatureEngineering
from quant_trading.features.optimized_feature_engineering_adapter import OptimizedFeatureEngineeringAdapter

__all__ = [
    'FeatureEngineer',
    'EnhancedFeatureEngineer',
    'PriceFeatures',
    'TechnicalIndicators',
    'StatisticalFeatures',
    'TimeFeatures',
    'OptimizedFeatureEngineering',
    'OptimizedFeatureEngineeringAdapter'
]
""",
    'trading': """#!/usr/bin/env python
# -*- coding: utf-8 -*-

\"\"\"
交易环境模块
提供交易环境和相关功能
\"\"\"

from quant_trading.trading.trading_environment import TradingEnvironment
from quant_trading.trading.enhanced_trading_environment import EnhancedTradingEnvironment
from quant_trading.trading.robust_trading_environment import RobustTradingEnvironment
from quant_trading.trading.action import Action
from quant_trading.trading.observation import Observation
from quant_trading.trading.reward import Reward
from quant_trading.trading.rendering import Rendering

__all__ = [
    'TradingEnvironment',
    'EnhancedTradingEnvironment',
    'RobustTradingEnvironment',
    'Action',
    'Observation',
    'Reward',
    'Rendering'
]
""",
    'agents': """#!/usr/bin/env python
# -*- coding: utf-8 -*-

\"\"\"
智能体模块
提供强化学习智能体和相关功能
\"\"\"

from quant_trading.agents.drl_agent import DRLAgent
from quant_trading.agents.robust_drl_agent import RobustDRLAgent
from quant_trading.agents.enhanced_drl_agent import EnhancedDRLAgent
from quant_trading.agents.ensemble_learning import EnsembleLearning

__all__ = [
    'DRLAgent',
    'RobustDRLAgent',
    'EnhancedDRLAgent',
    'EnsembleLearning'
]
""",
    'evaluation': """#!/usr/bin/env python
# -*- coding: utf-8 -*-

\"\"\"
评估模块
提供性能评估和模型评估功能
\"\"\"

from quant_trading.evaluation.performance_analyzer import PerformanceAnalyzer
from quant_trading.evaluation.enhanced_performance_analyzer import EnhancedPerformanceAnalyzer
from quant_trading.evaluation.model_evaluator import ModelEvaluator

__all__ = [
    'PerformanceAnalyzer',
    'EnhancedPerformanceAnalyzer',
    'ModelEvaluator'
]
""",
    'risk': """#!/usr/bin/env python
# -*- coding: utf-8 -*-

\"\"\"
风险管理模块
提供风险管理和控制功能
\"\"\"

from quant_trading.risk.risk_manager import RiskManager, StopLossType, PositionSizingMethod

__all__ = [
    'RiskManager',
    'StopLossType',
    'PositionSizingMethod'
]
""",
    'validation': """#!/usr/bin/env python
# -*- coding: utf-8 -*-

\"\"\"
验证模块
提供交叉验证和过拟合检测功能
\"\"\"

from quant_trading.validation.time_series_cv import TimeSeriesCV, CVMethod
from quant_trading.validation.market_condition_cv import MarketConditionCV
from quant_trading.validation.overfitting_detector import OverfittingDetector

__all__ = [
    'TimeSeriesCV',
    'CVMethod',
    'MarketConditionCV',
    'OverfittingDetector'
]
""",
    'market': """#!/usr/bin/env python
# -*- coding: utf-8 -*-

\"\"\"
市场分析模块
提供市场状态检测和分析功能
\"\"\"

from quant_trading.market.market_condition_detector import MarketConditionDetector, MarketCondition

__all__ = [
    'MarketConditionDetector',
    'MarketCondition'
]
""",
    'utils': """#!/usr/bin/env python
# -*- coding: utf-8 -*-

\"\"\"
工具模块
提供通用工具和辅助功能
\"\"\"

from quant_trading.utils.common import setup_logger, is_gpu_available
from quant_trading.utils.gpu_support import install_gpu_support

__all__ = [
    'setup_logger',
    'is_gpu_available',
    'install_gpu_support'
]
""",
    'tests': """#!/usr/bin/env python
# -*- coding: utf-8 -*-

\"\"\"
测试模块
提供单元测试和集成测试
\"\"\"
"""
}

def create_directory_structure():
    """创建新的目录结构"""
    logger.info("创建新的目录结构")
    
    # 创建主目录
    if NEW_PROJECT_DIR.exists():
        logger.warning(f"目录 {NEW_PROJECT_DIR} 已存在，将被清空")
        shutil.rmtree(NEW_PROJECT_DIR)
    
    NEW_PROJECT_DIR.mkdir(exist_ok=True)
    logger.info(f"创建目录: {NEW_PROJECT_DIR}")
    
    # 创建子目录
    subdirs = ['data', 'features', 'trading', 'agents', 'evaluation', 'risk', 'validation', 'market', 'utils', 'tests', 'ui']
    for subdir in subdirs:
        (NEW_PROJECT_DIR / subdir).mkdir(exist_ok=True)
        logger.info(f"创建子目录: {NEW_PROJECT_DIR / subdir}")
    
    # 创建__init__.py文件
    for subdir in [''] + subdirs:
        init_file = NEW_PROJECT_DIR / subdir / '__init__.py'
        template_key = 'root' if subdir == '' else subdir
        template = INIT_TEMPLATES.get(template_key, "# 模块初始化文件")
        
        with open(init_file, 'w', encoding='utf-8') as f:
            f.write(template)
        
        logger.info(f"创建__init__.py文件: {init_file}")
    
    return True

def copy_files():
    """复制文件到新的目录结构"""
    logger.info("复制文件到新的目录结构")
    
    # 复制核心文件
    for category, mappings in DIR_MAPPING.items():
        for src, dst in mappings:
            if src.exists():
                # 确保目标目录存在
                dst.parent.mkdir(exist_ok=True)
                
                # 复制文件
                shutil.copy2(src, dst)
                logger.info(f"复制文件: {src} -> {dst}")
            else:
                logger.warning(f"源文件不存在: {src}")
    
    # 复制测试文件
    for category, mappings in TEST_MAPPING.items():
        for src, dst in mappings:
            if src.exists():
                # 确保目标目录存在
                dst.parent.mkdir(exist_ok=True)
                
                # 复制文件
                shutil.copy2(src, dst)
                logger.info(f"复制测试文件: {src} -> {dst}")
            else:
                logger.warning(f"源测试文件不存在: {src}")
    
    return True

def run():
    """运行重构脚本"""
    logger.info("开始重构项目")
    
    # 创建目录结构
    create_directory_structure()
    
    # 复制文件
    copy_files()
    
    logger.info("项目重构完成")
    
    return True

if __name__ == "__main__":
    run()
