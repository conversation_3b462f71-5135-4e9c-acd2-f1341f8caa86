# 无浏览器模式运行指南

本文档说明如何在服务器环境或不需要自动打开浏览器的情况下运行量化交易系统。

## 为什么需要无浏览器模式？

在以下情况下，无浏览器模式特别有用：

1. 在没有图形界面的服务器上运行
2. 通过远程SSH连接运行系统
3. 系统自动启动时（如通过计划任务）
4. 只需要API服务而不需要UI界面
5. 多用户环境下避免过多浏览器窗口

## 启动方式

### 1. 使用启动脚本

```bash
# 标准模式启动（无浏览器）
python quant_project/start_system.py --mode standard --headless

# 因子挖掘模式启动（无浏览器）
python quant_project/start_system.py --mode factor_mining --headless

# 交互式选择模式（无浏览器）
python quant_project/start_system.py --headless
```

### 2. 使用批处理文件（Windows）

```bash
# 标准模式启动（无浏览器）
cd quant_project
run.bat normal headless

# 因子挖掘模式启动（无浏览器）
cd quant_project
run.bat factor headless

# 带补丁版本启动（无浏览器）
cd quant_project
run_app.bat headless
```

### 3. 直接使用Streamlit命令

```bash
# 标准模式启动（无浏览器）
cd quant_project
python -m streamlit run main_app.py --server.headless true

# 因子挖掘模式启动（无浏览器）
cd quant_project
python -m streamlit run main_app_with_factor_mining.py --server.headless true
```

### 4. 使用补丁运行脚本

```bash
# 标准模式启动（无浏览器）
cd quant_project
python run_with_patch.py --headless
```

## 访问应用

在无浏览器模式下启动后，应用将在本地运行，但不会自动打开浏览器。

您可以通过以下方式访问应用：

1. 在浏览器中手动输入地址：`http://localhost:8501`
2. 如果在远程服务器上运行，请确保端口已开放，然后访问：`http://<服务器IP>:8501`

## 端口配置

默认情况下，Streamlit使用`8501`端口。如果需要更改端口，可以使用以下参数：

```bash
python -m streamlit run main_app.py --server.port 9000 --server.headless true
```

## 常见问题

### Q: 启动后如何知道系统是否正常运行？

A: 检查控制台输出，当看到"You can now view your Streamlit app in your browser"消息时，表示系统已正常启动。

### Q: 如何在计划任务中使用无浏览器模式？

A: 在计划任务中，始终使用`--headless`或`--server.headless true`参数启动系统。

### Q: 如何停止运行中的应用？

A: 在控制台中按`Ctrl+C`可以停止运行中的应用。

---

如有更多问题，请参考[Streamlit官方文档](https://docs.streamlit.io/develop/api-reference/cli)。 